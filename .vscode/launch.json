{
    "version": "0.2.0",
    "configurations": [

        {
            "name": "Python: Test Action Gen",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": [
                "${workspaceFolder}/server/api/unit_tests/playground/test_nodes.py::test_reflection_node_successful_reflection",
                "-v"
            ],
            "cwd": "${workspaceFolder}/server",
            "django": true,
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "Python Debugger: Django",
            "type": "debugpy",
            "request": "launch",
            "args": [
                "runserver"
            ],
            "django": true,
            "autoStartBrowser": false,
            "program": "${workspaceFolder}/server/manage.py",
            "justMyCode": false
        },
        {
            "name": "Celery",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/venv/bin/celery",
            "args": ["-A", "server", "worker", "--loglevel=DEBUG", "--beat"],
            "console": "integratedTerminal",
            "env": {
                "DJANGO_SETTINGS_MODULE": "server.settings",
                "OBJC_DISABLE_INITIALIZE_FORK_SAFETY": "YES"
            },
            "justMyCode": false,
            "cwd": "${workspaceFolder}/server" 
        },
        {
            "name": "Debug Release Test Script",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/server/api/release_tests/release_test_script.py",
            "cwd": "${workspaceFolder}/server",  // Changed to point to the server directory
            "env": {
                "PYTHONPATH": "${workspaceFolder}/server",
                "DJANGO_SETTINGS_MODULE": "server.settings",
                // "TOFU_ENV": "local",  // Required by your settings.py
                // "DEBUG": "True",
                // "LOCAL_DB": "False"
            },
            "args": ["--env", "local"],
            "django": true,
            "justMyCode": false,
            "console": "integratedTerminal"
        },
        {
            "name": "Debug Backfill Suggested Connected Sources",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/server/manage.py",
            "args": [
                "backfill_suggested_connected_sources",
                "--file",
                "/Users/<USER>/Workspace/tofu/backend/tofu/server/api/management/commands/domains.txt",
                "--dry-run"
            ],
            "cwd": "${workspaceFolder}/server",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/server",
                "DJANGO_SETTINGS_MODULE": "server.settings"
            },
            "django": true,
            "justMyCode": false,
            "console": "integratedTerminal"
        }
    ],
    "compounds": [
        {
            "name": "Django + Celery",
            "configurations": ["Python Debugger: Django", "Celery"]
        }
    ]
}