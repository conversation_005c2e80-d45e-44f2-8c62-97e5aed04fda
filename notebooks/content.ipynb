{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Python-dotenv could not parse statement starting at line 11\n", "Python-dotenv could not parse statement starting at line 28\n", "Python-dotenv could not parse statement starting at line 33\n", "Python-dotenv could not parse statement starting at line 46\n", "/Users/<USER>/Documents/GitHub/tofu/.venv/lib/python3.11/site-packages/pinecone/index.py:4: TqdmExperimentalWarning: Using `tqdm.autonotebook.tqdm` in notebook mode. Use `tqdm.tqdm` instead to force console mode (e.g. in jupyter console)\n", "  from tqdm.autonotebook import tqdm\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-07-13 12:54:54,813 INFO: Note: NumExpr detected 10 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "2023-07-13 12:54:54,814 INFO: NumExpr defaulting to 8 threads.\n", "2023-07-13 12:54:54,976 INFO: found organization id: tofu-ykka\n", "2023-07-13 12:54:54,988 WARNING: Python-dotenv could not parse statement starting at line 11\n", "2023-07-13 12:54:55,057 WARNING: Python-dotenv could not parse statement starting at line 28\n", "2023-07-13 12:54:55,058 WARNING: Python-dotenv could not parse statement starting at line 33\n", "2023-07-13 12:54:55,058 WARNING: Python-dotenv could not parse statement starting at line 46\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# Add the following block to import and setup the django project\n", "import os\n", "import sys\n", "import django\n", "\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# setup playbook\n", "from api.playbook import PlaybookHandler\n", "from api.content import ContentGenerator\n", "\n", "playbook_handler = PlaybookHandler.load_from_db(playbook_id=74)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Rewrite the following marketing text from UiPath to a new one.\\n\\nWhether that's claims management, underwriting, billing and payments, customer service, or IT, you're able to discover insights by extracting your data from all relevant sources in your backend (SAP, Salesforce, Oracle, etc.),\\n\\nFor context, the preceding and succeeding paragraphs of this text are\\n\\nYou can then understand your data by visualizing your end-to-end processes, analyzing bottlenecks/deviations/rework, and simulating the impact of potential actions with AI-driven analysis.\\n\\n your frontend (i.e., your team's desktops) or your organization's communication channels (email, CRM, etc.).End-to-end visibility and understanding of processes in the organization\\n\\nNow, generate a 32 words new text but tailored for UiPath's targeting customers with the following combined characteristics\\n\\n\\n\\nMake sure your response follows these rules:\\n1. Only output the new message, nothing else\\n2. Your output should try to match the punctuations of the original message\\n3. Make sure to still focus on what UiPath does and what services they provide when you replace the message.\\n\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["playbook_handler = PlaybookHandler.load_from_db(playbook_id=74)\n", "content_generation = ContentGenerator(playbook_handler)\n", "content_type = \"Text\"\n", "target_params = {}\n", "prompt_params = {}\n", "example_content = {\n", "    \"meta\": {\n", "        \"precedingContent\": \"You can then understand your data by visualizing your end-to-end processes, analyzing bottlenecks/deviations/rework, and simulating the impact of potential actions with AI-driven analysis.\",\n", "        \"succeedingContent\": \" your frontend (i.e., your team's desktops) or your organization's communication channels (email, CRM, etc.).End-to-end visibility and understanding of processes in the organization\",\n", "    },\n", "    \"text\": \"Whether that's claims management, underwriting, billing and payments, customer service, or IT, you're able to discover insights by extracting your data from all relevant sources in your backend (SAP, Salesforce, Oracle, etc.),\",\n", "}\n", "prompt_template_params = {\n", "    \"static_context\": \"\"\"\n", "key differentiators\n", "value propositions\n", "brand guidelines\n", "\"\"\",\n", "    \"ref_content\": \"\"\"I want you to generate a document based on the following content.\n", "\n", "---start: reference content---\n", "{asset_context}\n", "---end: reference content---\n", "\n", "In order to generate the whole document, we have to generate the messages in the document one by one. So, I will give you an example message from a similar document and ask you to replace it. Remember, the document we want to generate is about the above reference content. The example message is only for you to understand the layout of the document and structure of the message (such as length and narrative).\n", "\"\"\",\n", "    \"custom\": \"\"\"Here are some instructions you also need to follow when generating response\n", "\n", "- {custom_prompts}\n", "\n", "\"\"\",\n", "}\n", "content_generation.build_context(\"text\", target_params, {})\n", "content_generation.create_instructions(\n", "    content_type, target_params, prompt_params, example_content, prompt_template_params\n", ")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}