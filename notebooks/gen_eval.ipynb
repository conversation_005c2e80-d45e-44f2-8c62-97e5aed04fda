{"cells": [{"cell_type": "code", "execution_count": null, "id": "66fcf52d-3cd2-4591-8813-4314a7d739b6", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "# Run Personalization Eval Gen\n", "\n", "from datetime import datetime\n", "from api.eval import (\n", "    Benchmark,\n", "    benchmark_review_personalization_campaigns,\n", ")\n", "import logging\n", "\n", "campaign_ids = benchmark_review_personalization_campaigns\n", "content_ids = []\n", "prefix = f'eval_{datetime.now().date().strftime(\"%Y-%m-%d\")}'\n", "model_name = \"gpt-4-0125-preview\"\n", "num_of_variations = 1\n", "enable_custom = True\n", "generate_all_targets = [4480, 6599]\n", "joint_generation = True\n", "test_only = False\n", "\n", "logging.info(\n", "    f\"Generating eval doc for campaigns {campaign_ids} with {model_name}\"\n", ")\n", "\n", "benchmark = Benchmark.get_benchmark_instance(test_only=test_only)\n", "result_data = benchmark.gen_review(\n", "    prefix,\n", "    campaign_ids,\n", "    content_ids,\n", "    model_name,\n", "    num_of_variations,\n", "    enable_custom,\n", "    generate_all_targets,\n", "    joint_generation=joint_generation,\n", ")\n", "print(result_data)"]}, {"cell_type": "code", "execution_count": null, "id": "77bd1a3f", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "# Run Repurposing Eval Gen\n", "\n", "from datetime import datetime\n", "from api.eval import (\n", "    Benchmark,\n", "    benchmark_review_repurpose_campaigns,\n", ")\n", "import logging\n", "\n", "campaign_ids = benchmark_review_repurpose_campaigns\n", "content_ids = []\n", "prefix = f'eval_{datetime.now().date().strftime(\"%Y-%m-%d\")}'\n", "model_name = \"gpt-4-0125-preview\"\n", "num_of_variations = 1\n", "enable_custom = True\n", "generate_all_targets = []\n", "joint_generation = True\n", "test_only = False\n", "\n", "logging.info(f\"Generating eval doc for campaigns {campaign_ids} with {model_name}\")\n", "\n", "benchmark = Benchmark.get_benchmark_instance(test_only=test_only)\n", "result_data = benchmark.gen_review_repurpose(\n", "    prefix,\n", "    campaign_ids,\n", "    content_ids,\n", "    model_name,\n", "    num_of_variations,\n", "    enable_custom,\n", "    generate_all_targets,\n", "    joint_generation,\n", ")\n", "print(result_data)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}