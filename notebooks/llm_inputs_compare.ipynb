{"cells": [{"cell_type": "code", "execution_count": null, "id": "193c64a7-914a-41e6-9da9-a7addea47e53", "metadata": {}, "outputs": [], "source": ["# 2023-10-10\n", "# migrate custom_instructions\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from django.core.cache import cache\n", "\n", "\n", "def get_llm_inputs(content_id, component_id):\n", "    cache_key = f\"{content_id}-{component_id}\"\n", "    llm_inputs = cache.get(cache_key, {}).get(\"llm_inputs\", None)\n", "    if not llm_inputs:\n", "        print(\n", "            f\"error: You have to generate the component first before being able to query the log\"\n", "        )\n", "        return \"\"\n", "    else:\n", "        return llm_inputs\n", "\n", "\n", "import pandas as pd\n", "\n", "# Load the CSV file into a DataFrame\n", "file_path = \"~/Downloads/benchmark.csv\"\n", "df = pd.read_csv(file_path)\n", "\n", "# Display the first few rows of the DataFrame\n", "\n", "content_ids = df[\"content_id\"].tolist()\n", "component_ids = df[\"component_id\"].tolist()\n", "\n", "all_data = []\n", "\n", "# Iterating over both content_ids and component_ids\n", "for content_id, component_id in zip(content_ids, component_ids):\n", "    list_messages = get_llm_inputs(content_id, component_id)\n", "\n", "    # Assuming list_messages returns a list of dictionaries\n", "    for message in list_messages:\n", "        all_data.append(\n", "            {\n", "                \"content_id\": content_id,\n", "                \"component_id\": component_id,\n", "                \"label\": message[\"label\"],\n", "                \"content\": message[\"content\"],\n", "            }\n", "        )\n", "\n", "# Convert the list of dictionaries to a DataFrame\n", "df = pd.DataFrame(all_data)\n", "\n", "# Write the DataFrame to a CSV file\n", "df.to_csv(\"output.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}