{"cells": [{"cell_type": "code", "execution_count": 2, "id": "c142dc2b-4e46-49e1-a2e8-f26b63bd4cca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-06-13 12:10:00,320 INFO: checking: perotsystems.com\n", "2024-06-13 12:10:00,321 INFO: checking: sachealthsystem.org\n", "failed to crawl: perotsystems.com with <Response [400]> {\"error\":\"Error 500 when crawling https://perotsystems.com\"}\n", "failed to crawl: sachealthsystem.org with <Response [400]> {\"error\":\"Error 500 when crawling https://sachealthsystem.org\"}\n", "2024-06-13 12:10:26,547 INFO: checking: bpexpressinc.com\n", "2024-06-13 12:10:27,501 INFO: checking: lambertvetsupply.com\n", "2024-06-13 12:10:31,593 INFO: bpexpressinc.com is good\n", "2024-06-13 12:10:31,594 INFO: checking: theledges.org\n", "failed to crawl: lambertvetsupply.com with <Response [400]> {\"error\":\"Error 500 when crawling https://lambertvetsupply.com\"}\n", "failed to crawl: theledges.org with <Response [400]> {\"error\":\"Error 500 when crawling https://theledges.org\"}\n", "2024-06-13 12:10:54,491 INFO: checking: signaturehealthinc.com\n", "2024-06-13 12:11:01,328 INFO: checking: wintercorp.com\n", "2024-06-13 12:11:02,011 INFO: wintercorp.com is good\n", "2024-06-13 12:11:02,012 INFO: checking: tlfoundation.org\n", "failed to crawl: tlfoundation.org with <Response [400]> {\"error\":\"Error 500 when crawling https://tlfoundation.org\"}\n", "2024-06-13 12:11:27,949 INFO: checking: opco.com\n", "failed to crawl: opco.com with <Response [400]> {\"error\":\"No content found in url https://opco.com\"}\n", "2024-06-13 12:11:48,674 INFO: checking: federal-mogul.com\n", "failed to crawl: signaturehealthinc.com with <Response [400]> {\"error\":\"Error 500 when crawling https://signaturehealthinc.com\"}\n", "2024-06-13 12:12:09,263 INFO: checking: davisinsurancegroup.com\n", "failed to crawl: davisinsurancegroup.com with <Response [400]> {\"error\":\"Error 500 when crawling https://davisinsurancegroup.com\"}\n", "2024-06-13 12:12:32,090 INFO: checking: strykercorp.com\n", "50.0000% finished\n", "failed to crawl: federal-mogul.com with <Response [400]> {\"error\":\"Error 500 when crawling https://federal-mogul.com\"}\n", "2024-06-13 12:14:39,798 INFO: checking: txi.com\n", "failed to crawl: strykercorp.com with <Response [400]> {\"error\":\"Error 500 when crawling https://strykercorp.com\"}\n", "2024-06-13 12:15:33,182 INFO: checking: rocktenn.com\n", "failed to crawl: rocktenn.com with <Response [400]> {\"error\":\"Error 500 when crawling https://rocktenn.com\"}\n", "2024-06-13 12:16:06,587 INFO: checking: paalp.com\n", "failed to crawl: paalp.com with <Response [400]> {\"error\":\"Error 500 when crawling https://paalp.com\"}\n", "2024-06-13 12:16:32,946 INFO: checking: oxfordfinance.com\n", "2024-06-13 12:17:25,828 INFO: oxfordfinance.com is good\n", "2024-06-13 12:17:25,828 INFO: checking: br-inc.com\n", "failed to crawl: br-inc.com with <Response [400]> {\"error\":\"Error 500 when crawling https://br-inc.com\"}\n", "2024-06-13 12:17:56,104 INFO: checking: custinoenterprises.com\n", "failed to crawl: txi.com with <Response [400]> {\"error\":\"Error 500 when crawling https://txi.com\"}\n", "failed to crawl: custinoenterprises.com with <Response [400]> {\"error\":\"Error 404 when crawling https://custinoenterprises.com\"}\n", "2024-06-13 12:18:21,530 INFO: checking: averydennison.com\n", "2024-06-13 12:18:21,609 INFO: checking: portallenpolice.org\n", "2024-06-13 12:18:30,799 INFO: portallenpolice.org is good\n", "failed to crawl: averydennison.com with <Response [400]> {\"error\":\"Error 500 when crawling https://averydennison.com\"}\n", "100.0000% finished\n"]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.data_loaders.url_handler import UrlHandler\n", "from api.data_loaders.web_page_loader import TofuWebPageLoader\n", "from api.models import UrlRecord\n", "\n", "import logging\n", "import requests\n", "from langchain_core.documents import Document\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "import time\n", "\n", "def get_all_object_urls():\n", "    objects = []\n", "    all_assets = AssetInfo.objects.all()\n", "    all_targets = TargetInfo.objects.all()\n", "    all_companys = CompanyInfo.objects.all()\n", "    \n", "    objects.extend(all_assets)\n", "    objects.extend(all_targets)\n", "    objects.extend(all_companys)\n", "    \n", "    all_urls = set()\n", "    for aobject in objects:\n", "        docs = aobject.docs\n", "        for doc in docs.values():\n", "            if doc.get(\"type\") == \"url\":\n", "                value = doc.get(\"value\")\n", "                if value:\n", "                    all_urls.add(value)\n", "    \n", "    all_urls = list(all_urls)\n", "\n", "    return all_urls\n", "\n", "def local_crawl(url):\n", "    try:\n", "        loader = TofuWebPageLoader(url, deep_crawl=False)\n", "        docs = loader.load()\n", "        docs = [doc for doc in docs if doc.page_content.strip()]\n", "        logging.error(f\"{url} result having docs: {bool(docs)}\")\n", "        return bool(docs)\n", "    except Exception as e:\n", "        logging.error(f\"failed to crawl {url} due to {e}\")\n", "        return False\n", "    return False\n", "\n", "redirect_url_maps = {\n", "    \"johannafoods.com\": \"http://johannafoods.com/\",\n", "    \"christianhomes.org\": \"https://christianhomes.com/\",\n", "    \"tjfinancial.com\": \"https://www.tjfinancial.com\",\n", "    \"up.com\": \"www.up.com\",\n", "    \"www.rmsmortgage.com\": \"http://www.rmsmortgage.com\",\n", "    \"kickmaker.net\": \"http://www.kickmaker.net\",\n", "    \"vbhcs.org\": \"www.vbhcs.org\",\n", "    \"exeloncorp.com\": \"www.exeloncorp.com\",\n", "    \"eppsaviation.com\": \"http://www.eppsaviation.com\",\n", "    \"valleyforgegroup.com\": \"http://www.valleyforgegroup.com\",\n", "    \"afa-cpa.com\": \"http://www.afa-cpa.com\",\n", "    \"carsoncomm.net\": \"http://carsoncomm.net\",\n", "    \"proteja.net\": \"http://www.proteja.net\",\n", "    \"humana.com\": \"www.humana.com\",\n", "    \"imcglobal.com\": \"http://www.imcglobal.com\",\n", "    \"perseny.com\": \"http://www.perseny.com\",\n", "    \"firebirdsinternational.com\": \"http://www.firebirdsinternational.com\",\n", "    \"deanfoods.com\": \"http://www.deanfoods.com\",\n", "    \"tourissimo.travel\": \"www.tourissimo.travel\",\n", "    \"chrislaneinsurance.com\": \"http://chrislaneinsurance.com\",\n", "    \"nrucfc.org\": \"http://www.nrucfc.org\",\n", "    \"atlantamedicaldayspa.com\": \"http://www.atlantamedicaldayspa.com\",\n", "    \"skinjectablestucson.com\": \"http://skinjectablestucson.com\",\n", "    \"stage.com\": \"http://www.stage.com\",\n", "    \"peterswealth.com\": \"www.peterswealth.com\",\n", "    \"littletonregionalhealthcare.org\": \"http://www.littletonregionalhealthcare.org\",\n", "    \"whp-global.com\": \"www.whp-global.com\",\n", "    \"f-trans.com\": \"http://www.f-trans.com\",\n", "    \"www.kb-concrete.net\": \"http://www.kb-concrete.net\",\n", "    \"abhi.com\": \"http://www.abhi.com\",\n", "    \"stjohns.edu\": \"www.stjohns.edu\",\n", "    \"kintegra.org\": \"www.kintegra.org\",\n", "    \"brookesystems.com\": \"www.brookesystems.com\",\n", "    \"pmma.org\": \"http://www.pmma.org\",\n", "    \"cicfinancial.com\": \"http://www.cicfinancial.com\",\n", "    \"thecaissoncompany.com\": \"http://www.thecaissoncompany.com\",\n", "    \"nwa.com\": \"http://www.nwa.com\",\n", "    \"groffmurphy.com\": \"www.groffmurphy.com\",\n", "    \"jbsunited.com\": \"http://www.jbsunited.com\",\n", "    \"springisd.org\": \"www.springisd.org\",\n", "    \"thriva.com\": \"http://www.thriva.com\",\n", "    \"facture.co\": \"www.facture.co\",\n", "    \"easterseals.com\": \"www.easterseals.com\",\n", "    \"michaelplynchlaw.com\": \"www.michaelplynchlaw.com\",\n", "    \"leitchfieldpolice.org\": \"http://leitchfieldpolice.org\",\n", "    \"frontierinfiniti.com\": \"http://frontierinfiniti.com\",\n", "    \"ferragamo.com\": \"www.ferragamo.com\",\n", "    \"zaha-hadid.com\": \"www.zaha-hadid.com\",\n", "    \"gentek-global.com\": \"http://www.gentek-global.com\",\n", "    \"sk.ibm.com\": \"http://www.sk.ibm.com\",\n", "    \"questwsfc.com\": \"http://www.questwsfc.com\",\n", "    \"ciatrucking.com\": \"http://www.ciatrucking.com\",\n", "    \"callenderinsurance.com\": \"http://www.callenderinsurance.com\",\n", "    \"millenniumchem.com\": \"http://www.millenniumchem.com\",\n", "    \"fleet.com\": \"http://www.fleet.com\",\n", "    \"ottercreekadvisor.com\": \"http://www.ottercreekadvisor.com\",\n", "    \"vermis.si\": \"www.vermis.si\",\n", "    \"bench.com\": \"www.bench.com\",\n", "    \"www.progressive.com\": \"http://www.progressive.com\",\n", "    \"kroger.com\": \"www.kroger.com\",\n", "    \"bostonproperties.com\": \"http://www.bostonproperties.com\",\n", "    \"southeastpetro.com\": \"www.southeastpetro.com\",\n", "    \"saberpermit.com\": \"http://www.saberpermit.com\",\n", "    \"southernco.com\": \"www.southernco.com\",\n", "    \"bluefruit.co.uk\": \"www.bluefruit.co.uk\",\n", "    \"pinnaclewest.com\": \"www.pinnaclewest.com\",\n", "    \"jfwmail.com\": \"www.jfwmail.com\",\n", "    \"www.bankofny.com\": \"http://www.bankofny.com\",\n", "    \"mysportsrehab.com\": \"http://www.mysportsrehab.com\",\n", "    \"www.urbanoutfitters.com\": \"http://www.urbanoutfitters.com\",\n", "    \"yablonovichlaw.com\": \"www.yablonovichlaw.com\",\n", "    \"corfufoods.com\": \"www.corfufoods.com\",\n", "    \"romarwealth.com\": \"www.romarwealth.com\",\n", "    \"pristineimagemedspa.com\": \"www.pristineimagemedspa.com\",\n", "    \"uk.ibm.com\": \"http://www.uk.ibm.com\",\n", "    \"texmed.org\": \"www.texmed.org\",\n", "    \"globalpayments.com\": \"http://www.globalpayments.com\",\n", "    \"brooksyt.com\": \"http://www.brooksyt.com\",\n", "    \"cablevision.com\": \"http://www.cablevision.com\",\n", "    \"northerntrust.com\": \"www.northerntrust.com\",\n", "    \"goodwillsew.com\": \"www.goodwillsew.com\",\n", "    \"adelphia.net\": \"www.adelphia.net\",\n", "    \"wecprotects.org\": \"http://www.wecprotects.org\",\n", "    \"gp.com\": \"www.gp.com\",\n", "    \"traversecitymi.gov\": \"www.traversecitymi.gov\",\n", "    \"eagle-elsner.com\": \"www.eagle-elsner.com\",\n", "    \"fainsleylaw.com\": \"http://www.fainsleylaw.com\",\n", "    \"redbarninc.com\": \"http://www.redbarninc.com\",\n", "    \"arnl.org\": \"www.arnl.org\",\n", "    \"pssworldmedical.com\": \"http://www.pssworldmedical.com\",\n", "    \"socomahoa.com\": \"http://www.socomahoa.com\",\n", "    \"calvinklein.us\": \"www.calvinklein.us\",\n", "    \"wealthpro.org\": \"http://www.wealthpro.org\",\n", "    \"berggroupnd.com\": \"www.berggroupnd.com\",\n", "    \"hersheys.com\": \"www.hersheys.com\",\n", "    \"superatv.com\": \"www.superatv.com\",\n", "    \"asdasda.com\": \"http://www.asdasda.com\",\n", "    \"brunton1.com\": \"http://www.brunton1.com\",\n", "    \"mgh.harvard.edu\": \"www.mgh.harvard.edu\",\n", "    \"resource-mn.org\": \"http://www.resource-mn.org\",\n", "    \"perotsystems.com\": \"http://www.perotsystems.com\",\n", "    \"sachealthsystem.org\": \"http://www.sachealthsystem.org\",\n", "    \"bpexpressinc.com\": \"www.bpexpressinc.com\",\n", "    \"lambertvetsupply.com\": \"www.lambertvetsupply.com\",\n", "    \"theledges.org\": \"http://www.theledges.org\",\n", "    \"signaturehealthinc.com\": \"www.signaturehealthinc.com\",\n", "    \"wintercorp.com\": \"http://www.wintercorp.com\",\n", "    \"tlfoundation.org\": \"www.tlfoundation.org\",\n", "    \"opco.com\": \"www.opco.com\",\n", "    \"federal-mogul.com\": \"http://www.federal-mogul.com\",\n", "    \"davisinsurancegroup.com\": \"http://www.davisinsurancegroup.com\",\n", "    \"strykercorp.com\": \"http://www.strykercorp.com\",\n", "    \"txi.com\": \"http://www.txi.com\",\n", "    \"rocktenn.com\": \"http://www.rocktenn.com\",\n", "    \"paalp.com\": \"www.paalp.com\",\n", "    \"oxfordfinance.com\": \"http://oxfordfinance.com\",\n", "    \"br-inc.com\": \"http://www.br-inc.com\",\n", "    \"custinoenterprises.com\": \"http://www.custinoenterprises.com\",\n", "    \"averydennison.com\": \"www.averydennison.com\",\n", "    \"portallenpolice.org\": \"www.portallenpolice.org\",\n", "    \"wamu.com\": \"www.wamu.com\",\n", "    \"gettheskin.com\": \"http://www.gettheskin.com\",\n", "    \"stpaul.com\": \"http://www.stpaul.com\",\n", "    \"anl.gov\": \"www.anl.gov\",\n", "    \"hess.com\": \"www.hess.com\",\n", "    \"totalwine.com\": \"www.totalwine.com\",\n", "    \"cedars-sinai.edu\": \"www.cedars-sinai.edu\",\n", "    \"admworld.com\": \"http://www.admworld.com\",\n", "    \"centerforpersonalizedmedicine.com\": \"http://www.centerforpersonalizedmedicine.com\",\n", "    \"paloaltojcc.org\": \"www.paloaltojcc.org\",\n", "    \"kgzmlaw.com\": \"www.kgzmlaw.com\",\n", "    \"emifusion.com\": \"www.emifusion.com\",\n", "    \"myamtel.com\": \"http://www.myamtel.com\",\n", "    \"www.aspentransportation.com\": \"http://www.aspentransportation.com\",\n", "    \"lunacapm.com\": \"http://www.lunacapm.com\",\n", "    \"pacificafoods.net\": \"http://www.pacificafoods.net\",\n", "    \"possibleproject.org\": \"http://www.possibleproject.org\",\n", "    \"mmm.com\": \"www.mmm.com\",\n", "    \"oceanmist.com\": \"www.oceanmist.com\",\n", "    \"64loft.com\": \"http://www.64loft.com\",\n", "    \"mail-well.com\": \"http://www.mail-well.com\",\n", "    \"bernhardtwealth.com\": \"http://www.bernhardtwealth.com\",\n", "    \"maxim-ic.com\": \"http://www.maxim-ic.com\",\n", "    \"meadwestvaco.com\": \"www.meadwestvaco.com\",\n", "    \"insuretn.co\": \"http://www.insuretn.co\",\n", "    \"carolinagrouponline.com\": \"http://carolinagrouponline.com\",\n", "    \"apdmh.org\": \"http://www.apdmh.org\",\n", "    \"jscpafirm.com\": \"http://www.jscpafirm.com\",\n", "    \"mgmmirage.com\": \"http://www.mgmmirage.com\",\n", "    \"flammlaw.com\": \"http://www.flammlaw.com\",\n", "    \"solutia.com\": \"www.solutia.com\",\n", "    \"petsmart.com\": \"www.petsmart.com\",\n", "    \"www.ignou.ac.in\": \"http://www.ignou.ac.in\",\n", "    \"campbellsoup.com\": \"www.campbellsoup.com\",\n", "    \"bellsouth.com\": \"www.bellsouth.com\",\n", "    \"intuitive.com\": \"http://intuitive.com\",\n", "    \"abcma.org\": \"www.abcma.org\",\n", "    \"alliedwaste.com\": \"www.alliedwaste.com\",\n", "    \"temple-inland.com\": \"http://www.temple-inland.com\",\n", "    \"microsoftnewengland.com\": \"www.microsoftnewengland.com\",\n", "    \"highered.texas.gov\": \"www.highered.texas.gov\",\n", "    \"takealot.com\": \"http://takealot.com\",\n", "    \"stdreg.com\": \"http://www.stdreg.com\",\n", "    \"roots.com\": \"www.roots.com\",\n", "    \"regenmedicalspa.com\": \"http://www.regenmedicalspa.com\",\n", "    \"spectracf.com\": \"www.spectracf.com\",\n", "    \"philips.com\": \"www.philips.com\",\n", "    \"restorationhardware.com\": \"http://www.restorationhardware.com\",\n", "    \"botoxrnandmedspa.com\": \"http://www.botoxrnandmedspa.com\",\n", "    \"nulo.com\": \"www.nulo.com\",\n", "    \"www.amexglobalbusinesstravel.com\": \"http://www.amexglobalbusinesstravel.com\",\n", "    \"bowater.com\": \"http://bowater.com\",\n", "    \"babyswiss.com\": \"www.babyswiss.com\",\n", "    \"abinsuranceagency.net\": \"http://www.abinsuranceagency.net\",\n", "    \"philipmorris.com\": \"http://www.philipmorris.com\",\n", "    \"collabmedspascottsdaleaz.com\": \"http://www.collabmedspascottsdaleaz.com\",\n", "    \"sky-cap.com\": \"http://www.sky-cap.com\",\n", "    \"alleghenyenergy.com\": \"http://www.alleghenyenergy.com\",\n", "    \"uniprofoodservice.com\": \"www.uniprofoodservice.com\",\n", "    \"foamex.com\": \"http://www.foamex.com\",\n", "    \"dietzglobal.com\": \"www.dietzglobal.com\",\n", "    \"saude.ce.gov.br\": \"www.saude.ce.gov.br\",\n", "    \"weismarkets.com\": \"http://www.weismarkets.com\",\n", "    \"centilgroup.com\": \"http://www.centilgroup.com\",\n", "    \"sgi.com\": \"www.sgi.com\",\n", "    \"rda-insurance.com\": \"http://www.rda-insurance.com\",\n", "    \"agribeef.com\": \"www.agribeef.com\",\n", "    \"southhadleypolice.org\": \"http://www.southhadleypolice.org\",\n", "    \"attsinc.com\": \"http://www.attsinc.com\",\n", "    \"yumacountysheriff.net\": \"http://www.yumacountysheriff.net\",\n", "    \"fullspectrumim.com\": \"http://www.fullspectrumim.com\",\n", "    \"altria.com\": \"http://altria.com\",\n", "    \"dcinsllc.com\": \"www.dcinsllc.com\",\n", "    \"lebleu.com\": \"www.lebleu.com\",\n", "    \"nhfc.com\": \"http://nhfc.com\",\n", "    \"buildabear.com\": \"www.buildabear.com\",\n", "    \"tlccompanies.net\": \"http://www.tlccompanies.net\",\n", "    \"popdental.com\": \"http://www.popdental.com\",\n", "    \"familyfareconveniencestores.com\": \"http://www.familyfareconveniencestores.com\",\n", "    \"deloittece.com\": \"http://www.deloittece.com\",\n", "    \"www.heb.com\": \"http://www.heb.com\",\n", "    \"amsexotic.com\": \"http://www.amsexotic.com\",\n", "    \"beckleypd.com\": \"http://www.beckleypd.com\",\n", "    \"trinseo.com\": \"www.trinseo.com\",\n", "    \"eddypacking.com\": \"http://www.eddypacking.com\",\n", "    \"montgomeryinn.com\": \"www.montgomeryinn.com\",\n", "    \"ge.com\": \"www.ge.com\",\n", "    \"macneilcapital.com\": \"http://www.macneilcapital.com\",\n", "    \"bethlehemfarms.net\": \"http://www.bethlehemfarms.net\",\n", "    \"BYLAND.COM\": \"http://www.byland.com\",\n", "    \"bigwheelconnection.com\": \"http://www.bigwheelconnection.com\",\n", "    \"ecofarmsusa.com\": \"http://www.ecofarmsusa.com\",\n", "    \"macadoodles.com\": \"www.macadoodles.com\",\n", "    \"scenichills-realty.com\": \"http://www.scenichills-realty.com\",\n", "    \"cargoconnections.net\": \"www.cargoconnections.net\",\n", "    \"3LLINC.COM\": \"http://www.3llinc.com\",\n", "    \"firstgeneralinsurance.com\": \"http://firstgeneralinsurance.com\",\n", "    \"kbhome.com\": \"www.kbhome.com\",\n", "    \"sandhilllegacyplanning.com\": \"www.sandhilllegacyplanning.com\",\n", "    \"chsco-ops.com\": \"http://www.chsco-ops.com\",\n", "    \"www.weismarkets.com\": \"http://www.weismarkets.com\",\n", "    \"allprotransportation.com\": \"http://www.allprotransportation.com\",\n", "    \"tricountypoa.com\": \"www.tricountypoa.com\",\n", "    \"santaclaritahomehealth.com\": \"http://www.santaclaritahomehealth.com\",\n", "    \"alternativecourier.com\": \"http://www.alternativecourier.com\",\n", "    \"enterohealthcare.com\": \"www.enterohealthcare.com\",\n", "    \"scienceandbeautynyc.com\": \"http://www.scienceandbeautynyc.com\",\n", "    \"twingate.com\": \"www.twingate.com\",\n", "    \"footlocker.com\": \"http://www.footlocker.com\",\n", "    \"leroyhaynes.org\": \"www.leroyhaynes.org\",\n", "    \"tiffany.com\": \"www.tiffany.com\",\n", "    \"alpsalpine.com\": \"www.alpsalpine.com\",\n", "    \"richmondamerican.com\": \"www.richmondamerican.com\",\n", "    \"generalpet.com\": \"http://www.generalpet.com\",\n", "    \"siimage.com\": \"http://www.siimage.com\",\n", "    \"bigspringpd.net\": \"http://www.bigspringpd.net\",\n", "    \"delta.com\": \"www.delta.com\",\n", "    \"bellinibeauty.com\": \"www.bellinibeauty.com\",\n", "    \"mchoices.com\": \"http://mchoices.com\",\n", "    \"daleymohan.com\": \"http://www.daleymohan.com\",\n", "    \"openconnect.com.br\": \"www.openconnect.com.br\",\n", "    \"mcenroefinancialplanning.com\": \"http://mcenroefinancialplanning.com\",\n", "    \"town.sturbridge.ma.us\": \"www.town.sturbridge.ma.us\",\n", "    \"roundys.com\": \"www.roundys.com\",\n", "    \"wescodist.com\": \"http://www.wescodist.com\",\n", "    \"corning.com\": \"www.corning.com\",\n", "    \"dougvaroneanddancers.org\": \"http://www.dougvaroneanddancers.org\",\n", "    \"pacificsunwear.com\": \"www.pacificsunwear.com\",\n", "    \"swymconsulting.com\": \"http://swymconsulting.com\",\n", "    \"gebutlerlaw.com\": \"www.gebutlerlaw.com\",\n", "    \"agcomlogistics.com\": \"http://www.agcomlogistics.com\",\n", "    \"blkoutllc.com\": \"http://www.blkoutllc.com\",\n", "    \"reside.health\": \"http://www.reside.health\",\n", "    \"wrbc.com\": \"http://www.wrbc.com\",\n", "    \"personifysearch.com\": \"http://www.personifysearch.com\",\n", "    \"bcbst.com\": \"www.bcbst.com\",\n", "    \"graypines.com\": \"www.graypines.com\",\n", "    \"cellspark.com\": \"http://www.cellspark.com\",\n", "    \"centrumvalley.com\": \"http://www.centrumvalley.com\",\n", "    \"cedars-sinai.org\": \"www.cedars-sinai.org\",\n", "    \"hfddel.com\": \"http://www.hfddel.com\",\n", "    \"conversepd.com\": \"http://www.conversepd.com\",\n", "    \"ryersontull.com\": \"http://ryersontull.com\",\n", "    \"marinerhealth.com\": \"http://marinerhealth.com\",\n", "    \"gv.com\": \"www.gv.com\",\n", "    \"pccasegear.com\": \"www.pccasegear.com\",\n", "    \"suntrust.com\": \"www.suntrust.com\",\n", "    \"hibbett.com\": \"http://hibbett.com\",\n", "    \"stlcardio.com\": \"http://www.stlcardio.com\",\n", "    \"getstored.com\": \"http://www.getstored.com\",\n", "    \"dhitechnologies.org\": \"http://www.dhitechnologies.org\",\n", "    \"ddsccolorado.com\": \"http://ddsccolorado.com\",\n", "    \"ccbctaunton.org\": \"http://www.ccbctaunton.org\",\n", "    \"elegantimagedayspa.com\": \"http://www.elegantimagedayspa.com\",\n", "    \"itc.in\": \"http://www.itc.in\",\n", "    \"njid.org\": \"www.njid.org\",\n", "    \"vnhh.org\": \"http://www.vnhh.org\",\n", "    \"gpenergy.com\": \"http://www.gpenergy.com\",\n", "    \"pilgrimspride.com\": \"http://pilgrimspride.com\",\n", "    \"mprc.umaryland.edu\": \"www.mprc.umaryland.edu\",\n", "    \"grantcountywa.gov\": \"www.grantcountywa.gov\",\n", "    \"jmfpllc.com\": \"http://www.jmfpllc.com\",\n", "    \"alohatransllc.com\": \"http://www.alohatransllc.com\",\n", "    \"cornproducts.com\": \"www.cornproducts.com\",\n", "    \"unionbank.com\": \"www.unionbank.com\",\n", "    \"vracpa.com\": \"http://www.vracpa.com\",\n", "    \"edftrading.com\": \"www.edftrading.com\",\n", "    \"fortunebrands.com\": \"www.fortunebrands.com\",\n", "    \"ned.org\": \"www.ned.org\",\n", "    \"www.cloudpay.net\": \"http://www.cloudpay.net\",\n", "    \"novacklaw.com\": \"http://www.novacklaw.com\",\n", "    \"marvinandmarvin.com\": \"www.marvinandmarvin.com\",\n", "    \"fisglobal.com\": \"www.fisglobal.com\",\n", "    \"prudential.com\": \"www.prudential.com\",\n", "    \"frenchcasey.com\": \"http://www.frenchcasey.com\",\n", "    \"giregional.org\": \"http://www.giregional.org\",\n", "    \"worldcom.com\": \"www.worldcom.com\",\n", "    \"bankofny.com\": \"http://www.bankofny.com\",\n", "    \"elmercandy.com\": \"http://www.elmercandy.com\",\n", "    \"jgmclaw.com\": \"www.jgmclaw.com\",\n", "    \"vasaricc.com\": \"http://www.vasaricc.com\",\n", "    \"evicore.com\": \"www.evicore.com\",\n", "    \"circonenviro.com\": \"http://www.circonenviro.com\",\n", "    \"morinandbarkley.com\": \"http://www.morinandbarkley.com\",\n", "    \"nextel.com\": \"http://www.nextel.com\",\n", "    \"dciinc.org\": \"www.dciinc.org\",\n", "    \"eqt.com\": \"www.eqt.com\",\n", "    \"summahealth.org\": \"www.summahealth.org\",\n", "    \"elliscountyhomeless.com\": \"http://www.elliscountyhomeless.com\",\n", "    \"themine.com\": \"http://www.themine.com\",\n", "    \"bcbsa.com\": \"http://www.bcbsa.com\",\n", "    \"thechautauquacenter.org\": \"http://www.thechautauquacenter.org\",\n", "    \"cusa.canon.com\": \"www.cusa.canon.com\",\n", "    \"jellysmack.com\": \"www.jellysmack.com\",\n", "    \"tririvershc.com\": \"www.tririvershc.com\",\n", "    \"milkeninstitute.org\": \"www.milkeninstitute.org\",\n", "    \"alcmodesto.com\": \"http://alcmodesto.com\",\n", "    \"mdlz.com\": \"http://www.mdlz.com\",\n", "    \"co.guadalupe.tx.us\": \"www.co.guadalupe.tx.us\",\n", "    \"aul.com\": \"http://www.aul.com\",\n", "    \"brunsonandcompany.com\": \"http://www.brunsonandcompany.com\",\n", "    \"pewtrusts.org\": \"www.pewtrusts.org\",\n", "    \"mosssavings.com\": \"http://www.mosssavings.com\",\n", "    \"columbusadvisorygroup.com\": \"www.columbusadvisorygroup.com\",\n", "    \"tyson.com\": \"www.tyson.com\",\n", "    \"glcac.org\": \"www.glcac.org\",\n", "    \"arkbest.com\": \"http://www.arkbest.com\",\n", "    \"pfcil.com\": \"http://www.pfcil.com\",\n", "    \"dts.edu\": \"www.dts.edu\",\n", "    \"carnivalcorp.com\": \"www.carnivalcorp.com\",\n", "    \"bakerlogistics.com\": \"http://www.bakerlogistics.com\",\n", "    \"aspirehp.org\": \"http://www.aspirehp.org\",\n", "    \"notjustbeancounters.com\": \"http://www.notjustbeancounters.com\",\n", "    \"dqe.com\": \"www.dqe.com\",\n", "    \"mhealthconnect.com\": \"http://www.mhealthconnect.com\",\n", "    \"footlocker-inc.com\": \"http://www.footlocker-inc.com\",\n", "    \"avnet.eu\": \"www.avnet.eu\",\n", "    \"tenderhearthh.com\": \"http://www.tenderhearthh.com\",\n", "    \"mackenzie-childs.com\": \"www.mackenzie-childs.com\",\n", "    \"longs.com\": \"http://www.longs.com\",\n", "    \"fstpt.com\": \"www.fstpt.com\",\n", "    \"nixcavating.com\": \"http://www.nixcavating.com\",\n", "    \"stonebrew.com\": \"http://www.stonebrew.com\",\n", "    \"sterlingcheck.com\": \"www.sterlingcheck.com\",\n", "    \"wilmingtonps.org\": \"www.wilmingtonps.org\",\n", "    \"firstnational.com\": \"www.firstnational.com\",\n", "    \"relx.com\": \"www.relx.com\",\n", "    \"chathampolice.com\": \"http://www.chathampolice.com\",\n", "    \"gentiva.com\": \"http://www.gentiva.com\",\n", "    \"vanceandrorrer.com\": \"http://vanceandrorrer.com\",\n", "    \"consumerscu.org\": \"www.consumerscu.org\",\n", "    \"aldogroup.com\": \"www.aldogroup.com\",\n", "    \"sungard.com\": \"http://www.sungard.com\",\n", "    \"phronimoscap.com\": \"www.phronimoscap.com\",\n", "    \"stamatininternalmedicine.com\": \"http://www.stamatininternalmedicine.com\",\n", "    \"unova.com\": \"http://www.unova.com\",\n", "    \"wrapmanager.com\": \"www.wrapmanager.com\",\n", "    \"harrahs.com\": \"www.harrahs.com\",\n", "    \"allmerica.com\": \"www.allmerica.com\",\n", "    \"bamfunds.com\": \"www.bamfunds.com\",\n", "    \"legacyhousingcorp.com\": \"http://www.legacyhousingcorp.com\",\n", "    \"goclientfocus.com\": \"http://www.goclientfocus.com\",\n", "    \"talosenergy.com\": \"www.talosenergy.com\",\n", "    \"alberto.com\": \"http://www.alberto.com\",\n", "    \"hcmsgroup.com\": \"www.hcmsgroup.com\",\n", "    \"www.aracoconcrete.com\": \"http://www.aracoconcrete.com\",\n", "    \"pacreditcardlaws.com\": \"http://www.pacreditcardlaws.com\",\n", "    \"ldcapp.com\": \"http://www.ldcapp.com\",\n", "    \"sofinn.it\": \"http://www.sofinn.it\",\n", "    \"medline.com\": \"http://www.medline.com\",\n", "    \"alleghenytechnologies.com\": \"http://www.alleghenytechnologies.com\",\n", "    \"gs.com\": \"www.gs.com\",\n", "    \"hibners.com\": \"http://www.hibners.com\",\n", "    \"dsw-llc.com\": \"http://www.dsw-llc.com\",\n", "    \"gainesfinancialservices.com\": \"www.gainesfinancialservices.com\",\n", "    \"gis.com.mx\": \"www.gis.com.mx\",\n", "    \"johnvarvatos.com\": \"www.johnvarvatos.com\",\n", "    \"ellisoninsuranceinc.com\": \"http://www.ellisoninsuranceinc.com\",\n", "    \"melfawm.com\": \"http://www.melfawm.com\",\n", "    \"allgoodlogistics.com\": \"www.allgoodlogistics.com\",\n", "    \"fredmeyerjewelers.com\": \"http://fredmeyerjewelers.com\",\n", "    \"flatworldsolutions.com.ph\": \"http://www.flatworldsolutions.com.ph\",\n", "    \"teldta.com\": \"http://teldta.com\",\n", "    \"certinia.com\": \"www.certinia.com\",\n", "    \"amexglobalbusinesstravel.com\": \"http://www.amexglobalbusinesstravel.com\",\n", "    \"healthsourceofohio.com\": \"http://www.healthsourceofohio.com\",\n", "    \"worthingtonpolice.com\": \"www.worthingtonpolice.com\",\n", "    \"bbbsc.org\": \"http://www.bbbsc.org\",\n", "    \"seafrigo.com\": \"www.seafrigo.com\",\n", "    \"americanstandardexpress.com\": \"http://www.americanstandardexpress.com\",\n", "    \"rraonline.com\": \"http://www.rraonline.com\",\n", "    \"haggar.com\": \"www.haggar.com\",\n", "    \"mandalayresortgroup.com\": \"http://www.mandalayresortgroup.com\",\n", "    \"lvh.com\": \"www.lvh.com\",\n", "    \"wealthmgt.com\": \"http://www.wealthmgt.com\",\n", "    \"plushmedicalaesthetics.com\": \"http://www.plushmedicalaesthetics.com\",\n", "    \"genzyme.com\": \"www.genzyme.com\",\n", "    \"bwauto.com\": \"http://www.bwauto.com\",\n", "    \"catskill.net\": \"http://www.catskill.net\",\n", "    \"sfpe.org\": \"www.sfpe.org\",\n", "    \"cdnlogistics.us\": \"http://www.cdnlogistics.us\",\n", "    \"mattapoisettpolice.com\": \"http://www.mattapoisettpolice.com\",\n", "    \"houndapps.com\": \"http://houndapps.com\",\n", "    \"grantparkpacking.com\": \"http://www.grantparkpacking.com\",\n", "    \"cromptoncorp.com\": \"http://www.cromptoncorp.com\",\n", "    \"co.tom-green.tx.us\": \"http://www.co.tom-green.tx.us\",\n", "    \"northpark.edu\": \"www.northpark.edu\",\n", "    \"natcofoodservice.com\": \"http://www.natcofoodservice.com\",\n", "    \"www.basspro.com\": \"http://www.basspro.com\",\n", "    \"tenneco-automotive.com\": \"http://www.tenneco-automotive.com\",\n", "    \"montanapbs.org\": \"www.montanapbs.org\",\n", "    \"cerberus.com\": \"www.cerberus.com\",\n", "    \"house-foods.com\": \"www.house-foods.com\",\n", "    \"triconglobal.com\": \"http://www.triconglobal.com\",\n", "    \"adambros.com\": \"http://www.adambros.com\",\n", "    \"mizarcpa.com\": \"http://mizarcpa.com\",\n", "    \"symbol.com\": \"www.symbol.com\",\n", "    \"sartoricompany.com\": \"http://www.sartoricompany.com\",\n", "    \"bbandt.com\": \"www.bbandt.com\",\n", "    \"lifeguardwealth.com\": \"http://www.lifeguardwealth.com\",\n", "    \"equityoffice.com\": \"http://www.equityoffice.com\",\n", "    \"duluthtrading.com\": \"www.duluthtrading.com\",\n", "    \"bartushfoods.com\": \"http://www.bartushfoods.com\",\n", "    \"countrywide.com\": \"http://www.countrywide.com\",\n", "    \"cotrancorp.com\": \"http://www.cotrancorp.com\",\n", "    \"rbc.com\": \"http://www.rbc.com\",\n", "    \"hanoveradvisorsinc.com\": \"www.hanoveradvisorsinc.com\",\n", "    \"sbc.com\": \"www.sbc.com\",\n", "    \"goheenotoole.com\": \"http://www.goheenotoole.com\",\n", "    \"nikken.co.jp\": \"www.nikken.co.jp\",\n", "    \"lizclaiborne.com\": \"www.lizclaiborne.com\",\n", "    \"atoztb.com\": \"www.atoztb.com\",\n", "    \"aal.org\": \"http://www.aal.org\",\n", "    \"partners.org\": \"www.partners.org\",\n", "    \"pureengineering.com\": \"www.pureengineering.com\",\n", "    \"rmlspecialtyhospital.org\": \"www.rmlspecialtyhospital.org\",\n", "    \"biovisageclinic.com\": \"http://www.biovisageclinic.com\",\n", "    \"hollywoodvideo.com\": \"http://www.hollywoodvideo.com\",\n", "    \"alltel.com\": \"http://www.alltel.com\",\n", "    \"bppr.com\": \"www.bppr.com\",\n", "    \"conseco.com\": \"http://www.conseco.com\",\n", "    \"mariannaind.com\": \"http://www.mariannaind.com\",\n", "    \"edifecs.com\": \"www.edifecs.com\",\n", "    \"mdc-partners.com\": \"http://www.mdc-partners.com\",\n", "    \"fclegal.com\": \"http://www.fclegal.com\",\n", "    \"wisemen.com\": \"http://www.wisemen.com\",\n", "    \"kns.com\": \"www.kns.com\",\n", "    \"jcpenney.net\": \"www.jcpenney.net\",\n", "    \"halliburton.com\": \"www.halliburton.com\",\n", "    \"bankofamerica.com\": \"www.bankofamerica.com\",\n", "    \"tomwoodintegrity.com\": \"www.tomwoodintegrity.com\",\n", "    \"newellco.com\": \"http://www.newellco.com\",\n", "    \"trinidadcoffee.us\": \"www.trinidadcoffee.us\",\n", "    \"wglholdings.com\": \"www.wglholdings.com\",\n", "    \"reilyfoods.com\": \"http://www.reilyfoods.com\",\n", "    \"oldworldprovisions.com\": \"http://www.oldworldprovisions.com\",\n", "    \"donohuefeiman.com\": \"http://donohuefeiman.com\",\n", "    \"co.brooks.tx.us\": \"www.co.brooks.tx.us\",\n", "    \"rosemintzllp.com\": \"www.rosemintzllp.com\",\n", "    \"olo.com\": \"www.olo.com\",\n", "    \"darlingrisbrough.com\": \"http://www.darlingrisbrough.com\",\n", "    \"gmh.edu\": \"http://www.gmh.edu\",\n", "    \"spearmania.com\": \"http://www.spearmania.com\",\n", "    \"eileenfisher.com\": \"www.eileenfisher.com\",\n", "    \"indonornetwork.org\": \"http://indonornetwork.org\",\n", "    \"dbg.com.cn\": \"www.dbg.com.cn\",\n", "    \"amica.com\": \"www.amica.com\",\n", "    \"andreajordanskincare.com\": \"http://andreajordanskincare.com\",\n", "    \"assetcofreight.com\": \"www.assetcofreight.com\",\n", "    \"www.fromsoftware.com\": \"http://www.fromsoftware.com\",\n", "    \"universalstudios.com\": \"www.universalstudios.com\",\n", "    \"lakecapital.com\": \"www.lakecapital.com\",\n", "    \"unitekgs.com\": \"http://unitekgs.com\",\n", "    \"hfahawaii.com\": \"www.hfahawaii.com\",\n", "    \"go-bfs.com\": \"http://www.go-bfs.com\",\n", "    \"landon.net\": \"www.landon.net\",\n", "    \"state.gov\": \"www.state.gov\",\n", "    \"mawccpas.com\": \"http://mawccpas.com\",\n", "    \"reliantenergy.com\": \"http://www.reliantenergy.com\",\n", "    \"pccmarkets.com\": \"www.pccmarkets.com\",\n", "    \"abcinfo.org\": \"www.abcinfo.org\",\n", "    \"cvph.org\": \"www.cvph.org\",\n", "    \"ifoodgroup.com\": \"http://www.ifoodgroup.com\",\n", "    \"bravelogisticsinc.com\": \"http://www.bravelogisticsinc.com\",\n", "    \"2010allamericanfreightlogistics.com\": \"http://www.2010allamericanfreightlogistics.com\",\n", "    \"jackinthebox.com\": \"http://jackinthebox.com\",\n", "    \"gammaseafood.com\": \"http://www.gammaseafood.com\",\n", "    \"queens.edu\": \"www.queens.edu\",\n", "    \"emc.com\": \"www.emc.com\",\n", "    \"northwestcosmeticlabs.com\": \"http://www.northwestcosmeticlabs.com\",\n", "    \"bates-electric.com\": \"www.bates-electric.com\",\n", "    \"pamf.org\": \"http://www.pamf.org\",\n", "    \"vaughn-law.com\": \"http://www.vaughn-law.com\",\n", "    \"incoco.com\": \"http://www.incoco.com\",\n", "    \"bcbsma.com\": \"www.bcbsma.com\",\n", "    \"streetwisecapital.net\": \"www.streetwisecapital.net\",\n", "    \"eagleinvsys.com\": \"www.eagleinvsys.com\",\n", "    \"dimon.com\": \"http://www.dimon.com\",\n", "    \"aikencosmeticsurgery.com\": \"http://www.aikencosmeticsurgery.com\",\n", "    \"aircointernational.com\": \"http://www.aircointernational.com\",\n", "    \"columbia.edu\": \"www.columbia.edu\",\n", "    \"whistlepigrye.com\": \"http://www.whistlepigrye.com\",\n", "    \"www.qca.qualcomm.com\": \"http://www.qca.qualcomm.com\",\n", "    \"administaff.com\": \"www.administaff.com\",\n", "    \"praxair.com\": \"www.praxair.com\",\n", "    \"bshsi.org\": \"http://www.bshsi.org\",\n", "    \"cfpfire.com\": \"www.cfpfire.com\",\n", "    \"pixelworks.com\": \"www.pixelworks.com\",\n", "    \"townofluray.com\": \"www.townofluray.com\",\n", "    \"sonomacutrer.com\": \"www.sonomacutrer.com\",\n", "    \"germainmichaelr.com\": \"www.germainmichaelr.com\",\n", "    \"cooperindustries.com\": \"http://www.cooperindustries.com\",\n", "    \"usa.philips.com\": \"www.usa.philips.com\",\n", "    \"mobiquityinc.com\": \"www.mobiquityinc.com\",\n", "    \"srgfinancialadvisors.com\": \"www.srgfinancialadvisors.com\",\n", "    \"edgefieldcountysheriff.org\": \"http://www.edgefieldcountysheriff.org\",\n", "    \"aaamerica.com.mx\": \"www.aaamerica.com.mx\",\n", "    \"c-techindustrial.com\": \"http://www.c-techindustrial.com\",\n", "    \"pwg-inc.com\": \"http://www.pwg-inc.com\",\n", "    \"msasecurity.net\": \"www.msasecurity.net\",\n", "    \"forestinkclothing.com\": \"www.forestinkclothing.com\",\n", "    \"localeclectic.com\": \"http://www.localeclectic.com\",\n", "    \"nobleaff.com\": \"http://www.nobleaff.com\",\n", "    \"countyoflee.org\": \"http://countyoflee.org\",\n", "    \"cchmc.org\": \"www.cchmc.org\",\n", "    \"wka-law.com\": \"http://wka-law.com\",\n", "    \"globalharmonyexcellence.org\": \"http://www.globalharmonyexcellence.org\",\n", "    \"wregional.com\": \"www.wregional.com\",\n", "    \"ielectric.com\": \"http://www.ielectric.com\",\n", "    \"cmtransport.com\": \"http://cmtransport.com\",\n", "    \"its-laredo.com\": \"www.its-laredo.com\",\n", "    \"dstsystems.com\": \"www.dstsystems.com\",\n", "    \"mercyships.org\": \"www.mercyships.org\",\n", "    \"mbna.com\": \"http://www.mbna.com\",\n", "    \"cmsenergy.com\": \"www.cmsenergy.com\",\n", "    \"kikkoman.com\": \"www.kikkoman.com\",\n", "    \"hairgrowthcenters.com\": \"www.hairgrowthcenters.com\",\n", "    \"dupont.com\": \"www.dupont.com\",\n", "    \"integrawealthadvisors.com\": \"http://www.integrawealthadvisors.com\",\n", "    \"amstransportation.com\": \"http://www.amstransportation.com\",\n", "    \"theohioacademy.com\": \"http://www.theohioacademy.com\",\n", "    \"agsystems.com\": \"www.agsystems.com\",\n", "    \"hpspartners.com\": \"www.hpspartners.com\",\n", "    \"us.ibm.com\": \"http://www.us.ibm.com\",\n", "    \"caribouinternational.com\": \"http://www.caribouinternational.com\",\n", "    \"academicanv.com\": \"http://www.academicanv.com\",\n", "    \"nwpacking.com\": \"http://www.nwpacking.com\",\n", "    \"josephspasta.com\": \"www.josephspasta.com\",\n", "    \"faa.gov\": \"www.faa.gov\",\n", "    \"pacs-ky.org\": \"http://www.pacs-ky.org\",\n", "    \"sals.info\": \"http://www.sals.info\",\n", "    \"sbcms.org\": \"www.sbcms.org\",\n", "    \"foundfin.com\": \"www.foundfin.com\",\n", "    \"charco3.com\": \"http://www.charco3.com\",\n", "    \"s-k.dev\": \"http://www.s-k.dev\",\n", "    \"ahayatrucking.com\": \"http://www.ahayatrucking.com\",\n", "    \"miasafety.com\": \"http://www.miasafety.com\",\n", "    \"jei-cs.com\": \"http://www.jei-cs.com\",\n", "    \"aprile.it\": \"www.aprile.it\",\n", "    \"absolutefrt.com\": \"http://www.absolutefrt.com\",\n", "    \"reliablene.com\": \"http://www.reliablene.com\",\n", "    \"northandcobb.com\": \"http://www.northandcobb.com\",\n", "    \"cakeworthystore.com\": \"www.cakeworthystore.com\",\n", "    \"molex.com\": \"www.molex.com\",\n", "    \"www.sutterdelta.com\": \"http://www.sutterdelta.com\",\n", "    \"bordersgroupinc.com\": \"www.bordersgroupinc.com\",\n", "    \"stegenevieve.org\": \"http://www.stegenevieve.org\",\n", "    \"tiftregional.com\": \"www.tiftregional.com\",\n", "    \"rhodesbread.com\": \"http://www.rhodesbread.com\",\n", "    \"worthingtonattorneys.com\": \"http://www.worthingtonattorneys.com\",\n", "    \"yvcog.org\": \"http://www.yvcog.org\",\n", "    \"prestonwoodchristian.org\": \"www.prestonwoodchristian.org\",\n", "    \"iomico.us\": \"http://www.iomico.us\",\n", "    \"atmel.com\": \"http://www.atmel.com\",\n", "    \"hanes.com\": \"www.hanes.com\",\n", "    \"permobil.com\": \"www.permobil.com\",\n", "    \"petfoodexperts.com\": \"www.petfoodexperts.com\",\n", "    \"sportsauthority.com\": \"http://www.sportsauthority.com\",\n", "    \"everettlawgroup.com\": \"www.everettlawgroup.com\",\n", "    \"aholdusa.com\": \"www.aholdusa.com\",\n", "    \"cskauto.com\": \"http://www.cskauto.com\",\n", "    \"townofbelleair.net\": \"http://www.townofbelleair.net\",\n", "    \"bolles.org\": \"www.bolles.org\",\n", "    \"sterling-advisors.com\": \"www.sterling-advisors.com\",\n", "    \"preferredregistry.com\": \"http://www.preferredregistry.com\",\n", "    \"nfa.futures.org\": \"www.nfa.futures.org\",\n", "    \"ajftransport.com\": \"http://www.ajftransport.com\",\n", "    \"austinvoices.org\": \"www.austinvoices.org\",\n", "    \"www.uniteddrugs.com\": \"http://www.uniteddrugs.com\",\n", "    \"famhealth.com\": \"http://www.famhealth.com\",\n", "    \"urscorp.com\": \"http://urscorp.com\",\n", "    \"ecsinsure.com\": \"http://www.ecsinsure.com\",\n", "    \"buckleytrans.com\": \"http://www.buckleytrans.com\",\n", "    \"www.bestmaidproducts.com\": \"http://www.bestmaidproducts.com\",\n", "    \"zrgpartners.com\": \"www.zrgpartners.com\",\n", "    \"stage2.capital\": \"www.stage2.capital\",\n", "    \"jjill.com\": \"www.jjill.com\",\n", "    \"westfieldegg.com\": \"http://www.westfieldegg.com\",\n", "    \"mandtbank.com\": \"http://www.mandtbank.com\",\n", "    \"clntx.com\": \"http://www.clntx.com\",\n", "    \"adenasolutions.com\": \"http://www.adenasolutions.com\",\n", "    \"parkplace.com\": \"www.parkplace.com\",\n", "    \"bea.com\": \"www.bea.com\",\n", "    \"genpt.com\": \"www.genpt.com\",\n", "    \"brownshoe.com\": \"http://www.brownshoe.com\",\n", "    \"technipfmc.com\": \"www.technipfmc.com\",\n", "    \"eafit.edu.co\": \"https://www.eafit.edu.co/\",\n", "    \"cargosalesnetwork.com\": \"http://www.cargosalesnetwork.com\",\n", "    \"ryanair.com\": \"www.ryanair.com\",\n", "    \"fhlbatl.com\": \"www.fhlbatl.com\",\n", "    \"capecodinsuranceadvisers.com\": \"www.capecodinsuranceadvisers.com\",\n", "    \"myarklogistics.com\": \"http://www.myarklogistics.com\",\n", "    \"ppfa.org\": \"www.ppfa.org\",\n", "    \"godiva.com\": \"www.godiva.com\",\n", "    \"californiaveterinaryspecialists.com\": \"http://www.californiaveterinaryspecialists.com\",\n", "    \"autotransportkings.com\": \"http://www.autotransportkings.com\",\n", "    \"4sightpm.com\": \"www.4sightpm.com\",\n", "    \"libertygroup.co.uk\": \"http://www.libertygroup.co.uk\",\n", "    \"www.classichotels.com\": \"http://www.classichotels.com\",\n", "    \"goodysonline.com\": \"http://www.goodysonline.com\",\n", "    \"champssports.com\": \"www.champssports.com\",\n", "    \"smarthotbuys.com\": \"http://www.smarthotbuys.com\",\n", "    \"dellemc.com\": \"www.dellemc.com\",\n", "    \"pfxne.com\": \"http://www.pfxne.com\",\n", "    \"bryanlogistics.com\": \"http://www.bryanlogistics.com\",\n", "    \"mountrainierpd.org\": \"http://www.mountrainierpd.org\",\n", "    \"healthsouth.com\": \"http://www.healthsouth.com\",\n", "    \"bmc.com\": \"www.bmc.com\",\n", "    \"unitedstationers.com\": \"www.unitedstationers.com\",\n", "    \"neillsvillepd.org\": \"www.neillsvillepd.org\",\n", "    \"townofhawriver.com\": \"http://www.townofhawriver.com\",\n", "    \"supplies-for-success.org\": \"http://supplies-for-success.org\",\n", "    \"usg.com\": \"www.usg.com\",\n", "    \"stukeyfinancialplanning.com\": \"http://stukeyfinancialplanning.com\",\n", "    \"assertiotx.com\": \"www.assertiotx.com\",\n", "    \"wartburg.edu\": \"www.wartburg.edu\",\n", "    \"flagshipfood.com\": \"http://www.flagshipfood.com\",\n", "    \"bestyetexpress.com\": \"http://www.bestyetexpress.com\",\n", "    \"rickfletcherlaw.com\": \"http://www.rickfletcherlaw.com\",\n", "    \"dfci.harvard.edu\": \"http://www.dfci.harvard.edu\",\n", "    \"appcino.com\": \"http://www.appcino.com\",\n", "    \"compaq.com\": \"www.compaq.com\",\n", "    \"mytraffic.io\": \"www.mytraffic.io\",\n", "    \"mileendlogistics.com\": \"www.mileendlogistics.com\",\n", "    \"lsnyc.org\": \"http://www.lsnyc.org\",\n", "    \"regencyfamily.com\": \"http://regencyfamily.com\",\n", "    \"comcast.com\": \"www.comcast.com\",\n", "    \"ttrf.org\": \"www.ttrf.org\",\n", "    \"cctc.net\": \"http://www.cctc.net\",\n", "    \"co.anson.nc.us\": \"www.co.anson.nc.us\",\n", "    \"cornerstone-excavating.com\": \"http://cornerstone-excavating.com\",\n", "    \"powerco.org\": \"http://www.powerco.org\",\n", "    \"adastralogistics.org\": \"www.adastralogistics.org\",\n", "    \"spartanstores.com\": \"http://www.spartanstores.com\",\n", "    \"arplogistics.com\": \"http://www.arplogistics.com\",\n", "    \"tcr2.com\": \"http://www.tcr2.com\",\n", "    \"valero.com\": \"www.valero.com\",\n", "    \"shopsimon.com\": \"http://www.shopsimon.com\",\n", "    \"johnhermanins.com\": \"http://www.johnhermanins.com\",\n", "    \"swansonhealth.com\": \"www.swansonhealth.com\",\n", "    \"medicalspaastoria.com\": \"http://www.medicalspaastoria.com\",\n", "    \"anchorintlgrp.com\": \"http://anchorintlgrp.com\",\n", "    \"eccreamery.net\": \"http://www.eccreamery.net\",\n", "    \"calmarketing.com\": \"http://www.calmarketing.com\",\n", "    \"agrilinkfoods.com\": \"http://www.agrilinkfoods.com\",\n", "    \"alcoa.com\": \"www.alcoa.com\",\n", "    \"gayleafoods.com\": \"http://www.gayleafoods.com\",\n", "    \"aljfreightllc.com\": \"http://www.aljfreightllc.com\",\n", "    \"adp.com\": \"www.adp.com\",\n", "    \"sppirx.com\": \"www.sppirx.com\",\n", "    \"markelcorp.com\": \"www.markelcorp.com\",\n", "    \"celonis.de\": \"http://celonis.de\",\n", "    \"mwk-cpa.com\": \"http://www.mwk-cpa.com\",\n", "    \"jpms.com\": \"http://www.jpms.com\",\n", "    \"rockwell.com\": \"http://rockwell.com\",\n", "    \"probablymonsters.com\": \"www.probablymonsters.com\",\n", "    \"rorschachconsulting.com\": \"http://www.rorschachconsulting.com\",\n", "    \"superiorwalkincenter.com\": \"http://www.superiorwalkincenter.com\",\n", "    \"covanta.com\": \"www.covanta.com\",\n", "    \"sanyofoods.us\": \"http://www.sanyofoods.us\",\n", "    \"astreotech.com\": \"www.astreotech.com\",\n", "    \"thaxtonbarabe.com\": \"http://www.thaxtonbarabe.com\",\n", "    \"lajollaeconomics.com\": \"www.lajollaeconomics.com\",\n", "    \"sherwinwilliamscompany.com\": \"http://www.sherwinwilliamscompany.com\",\n", "    \"kelapo.com\": \"http://kelapo.com\",\n", "    \"keybank.com\": \"www.keybank.com\",\n", "    \"tcfbank.com\": \"www.tcfbank.com\",\n", "    \"shawgrp.com\": \"http://www.shawgrp.com\",\n", "    \"lisacaskey.com\": \"http://www.lisacaskey.com\",\n", "    \"martinfallon.com\": \"http://www.martinfallon.com\",\n", "    \"polarisindustries.com\": \"http://www.polarisindustries.com\",\n", "    \"trscpas.com\": \"http://www.trscpas.com\",\n", "    \"campbellfoley.com\": \"http://www.campbellfoley.com\",\n", "    \"dickenson-consulting.com\": \"http://www.dickenson-consulting.com\",\n", "    \"usouthal.edu\": \"www.usouthal.edu\",\n", "    \"youthranch.org\": \"www.youthranch.org\",\n", "    \"lifeschools.net\": \"http://www.lifeschools.net\",\n", "    \"phoenixbusinessinsurance.com\": \"http://www.phoenixbusinessinsurance.com\",\n", "    \"armstrong.com\": \"www.armstrong.com\",\n", "    \"www.pos.toasttab.com\": \"http://www.pos.toasttab.com\",\n", "    \"bnydc.org\": \"http://www.bnydc.org\",\n", "    \"ymcachicago.org\": \"www.ymcachicago.org\",\n", "    \"pgecorp.com\": \"www.pgecorp.com\",\n", "    \"nemcsa.org\": \"www.nemcsa.org\",\n", "    \"sbch.org\": \"www.sbch.org\",\n", "    \"allpointsassistedtrans.com\": \"http://www.allpointsassistedtrans.com\",\n", "    \"ycs.org\": \"www.ycs.org\",\n", "    \"lumettaproduce.com\": \"www.lumettaproduce.com\",\n", "    \"www.footlocker.com\": \"http://www.footlocker.com\",\n", "    \"stanleyworks.com\": \"www.stanleyworks.com\",\n", "    \"lithia.com\": \"www.lithia.com\",\n", "    \"freeport.tx.us\": \"www.freeport.tx.us\",\n", "    \"stimpertlaw.com\": \"www.stimpertlaw.com\",\n", "    \"aon.it\": \"www.aon.it\",\n", "    \"anadarko.com\": \"www.anadarko.com\",\n", "    \"acclaromortgage.com\": \"http://www.acclaromortgage.com\",\n", "    \"newbelgium.com\": \"www.newbelgium.com\",\n", "    \"mathisbrothers.com\": \"www.mathisbrothers.com\",\n", "    \"namesinthenews.com\": \"www.namesinthenews.com\",\n", "    \"soprabankingsoftware.com\": \"http://www.soprabankingsoftware.com\",\n", "    \"solectron.com\": \"www.solectron.com\",\n", "    \"aero.org\": \"http://www.aero.org\",\n", "    \"msdgames.com\": \"http://www.msdgames.com\",\n", "    \"jafra.com\": \"www.jafra.com\",\n", "    \"shinc.org\": \"http://www.shinc.org\",\n", "    \"ipmfoods.com\": \"www.ipmfoods.com\",\n", "    \"lewistrucklines.com\": \"http://www.lewistrucklines.com\",\n", "    \"co.kerr.tx.us\": \"http://co.kerr.tx.us\",\n", "    \"blueangel.com\": \"http://www.blueangel.com\",\n", "    \"tyler.net\": \"http://www.tyler.net\",\n", "    \"wardcountytx.org\": \"http://www.wardcountytx.org\",\n", "    \"beclinical.com\": \"www.beclinical.com\",\n", "    \"disruptmgt.com\": \"http://www.disruptmgt.com\",\n", "    \"tmf-group.com\": \"www.tmf-group.com\",\n", "    \"shopbic.com\": \"http://www.shopbic.com\",\n", "    \"jpisi.com\": \"www.jpisi.com\",\n", "    \"skyboxasset.com\": \"http://www.skyboxasset.com\",\n", "    \"goldmansachs.com\": \"www.goldmansachs.com\",\n", "    \"abellavitaoc.com\": \"http://www.abellavitaoc.com\",\n", "    \"disabilityrightsca.org\": \"www.disabilityrightsca.org\",\n", "    \"chcinj.org\": \"http://www.chcinj.org\",\n", "    \"tribune.com\": \"http://www.tribune.com\",\n", "    \"kingsasian.com\": \"http://www.kingsasian.com\",\n", "    \"alpha-omega-inc.com\": \"www.alpha-omega-inc.com\",\n", "    \"westdale.com\": \"www.westdale.com\",\n", "    \"smartruckingservices.com\": \"http://www.smartruckingservices.com\",\n", "    \"parkwaywealth.com\": \"http://www.parkwaywealth.com\",\n", "    \"co.upton.tx.us\": \"www.co.upton.tx.us\",\n", "    \"kerry.com\": \"www.kerry.com\",\n", "    \"macleancapitalmanagement.com\": \"http://www.macleancapitalmanagement.com\",\n", "    \"villamedicalarts.com\": \"http://www.villamedicalarts.com\",\n", "    \"ridgetowncapital.com\": \"www.ridgetowncapital.com\",\n", "    \"bkfreight.com\": \"www.bkfreight.com\",\n", "    \"guion.com\": \"www.guion.com\",\n", "    \"endcap.com\": \"www.endcap.com\",\n", "    \"norwest.com\": \"http://www.norwest.com\",\n", "    \"newmanschimel.com\": \"http://www.newmanschimel.com\",\n", "    \"dh.org\": \"www.dh.org\",\n", "    \"bottom-linelogistics.com\": \"http://www.bottom-linelogistics.com\",\n", "    \"rooslaw.com\": \"http://www.rooslaw.com\",\n", "    \"adc.com\": \"http://www.adc.com\",\n", "    \"peoplesoft.com\": \"www.peoplesoft.com\",\n", "    \"gevityhr.com\": \"http://www.gevityhr.com\",\n", "    \"encompassadvising.net\": \"http://www.encompassadvising.net\",\n", "    \"aryamedicalspa.com\": \"http://www.aryamedicalspa.com\",\n", "    \"kd-aesthetics.com\": \"www.kd-aesthetics.com\",\n", "    \"starwood.com\": \"www.starwood.com\",\n", "    \"ch.ibm.com\": \"http://www.ch.ibm.com\",\n", "    \"herrmannfinancial.com\": \"http://www.herrmannfinancial.com\",\n", "    \"geappliances.com\": \"www.geappliances.com\",\n", "    \"energytechsystems.com\": \"www.energytechsystems.com\",\n", "    \"tenethealth.com\": \"www.tenethealth.com\",\n", "    \"mcgraw-hill.com\": \"http://www.mcgraw-hill.com\",\n", "    \"c-advantage.com\": \"http://www.c-advantage.com\",\n", "    \"retirementfundingadvisors.com\": \"http://retirementfundingadvisors.com\",\n", "    \"genesistoday.com\": \"http://www.genesistoday.com\",\n", "    \"3com.com\": \"http://www.3com.com\",\n", "    \"http://dance.uoregon.edu\": \"https://musicanddance.uoregon.edu/dance\",\n", "    \"conectiv.com\": \"https://www.conectiv.co/\",\n", "    \"johnsoncontrols.com\": \"https://www.johnsoncontrols.com/about-us/our-company\",\n", "    \"https://www.imperialoaks.com\": \"http://imperialoakshoa.com/\",\n", "    \"grandriver.cc\": \"https://www.grandriver.org/\",\n", "}\n", "\n", "\n", "def adjust_urls(url):\n", "    cands = []\n", "    if not url.startswith(\"http\") and not url.startswith(\"www\"):\n", "        cands.append(f\"www.{url}\")\n", "        cands.append(f\"http://www.{url}\")\n", "\n", "    if not url.startswith(\"http\"):\n", "        cands.append(f\"http://{url}\")\n", "\n", "    return cands\n", "\n", "def crawl_about(url):\n", "    from api.playbook_build.external_info_builder import ExternalInfoBuilder\n", "    \n", "    builder = ExternalInfoBuilder()\n", "    results = builder.build(\n", "        f\"{url} about\",\n", "        num_links=1,\n", "        include_sitelinks=False,\n", "    )\n", "    redirect_url = results[0]['url']\n", "    logging.error(f\"keywords: {url} about => {redirect_url}\")\n", "    return redirect_url\n", "    \n", "def get_possible_urls(url):\n", "    candidates = []\n", "    if url in redirect_url_maps:\n", "        cand_url = redirect_url_maps[url]\n", "        if not cand_url.startswith(\"http\"):\n", "            cand_url = \"https://\" + cand_url\n", "        candidates.append(cand_url)\n", "    # for cand_url in adjust_urls(url):\n", "    #     if cand_url not in candidates:\n", "    #         candidates.append(cand_url)\n", "    # candidates.append(crawl_about(url))\n", "    return candidates\n", "\n", "\n", "def process_url(url):\n", "    logging.info(f\"checking: {url}\")\n", "\n", "    result = core_crawl(url)\n", "    if result:\n", "        logging.info(f\"{url} is good\")\n", "        return\n", "    time.sleep(20)\n", "    return\n", "    \n", "    wrapper = UrlHandler(url)\n", "    if wrapper._url_handler.crawling_status == UrlRecord.ExtractResult.NO_RECORD:\n", "        logging.error(f\"the status is wrong for {url}\")\n", "    return\n", "\n", "    logging.error(f\"failed to crawl {url}\")\n", "    candidate_urls = get_possible_urls(url)\n", "    for candidate_url in candidate_urls:\n", "        try:\n", "            if local_crawl(candidate_url):\n", "                wrapper = UrlHandler(url)\n", "                if wrapper._url_handler.crawling_status != UrlRecord.ExtractResult.ALWAYS_FAILS:\n", "                    logging.error(f\"it's not consistently fail for {url}\")\n", "                    continue\n", "                    \n", "                wrapper.set_redirect_urls([candidate_url])\n", "                break\n", "        except Exception as e:\n", "            logging.error(f\"exception for {candidate_url} due to {e}\")\n", "\n", "def main(url_list):\n", "    total_urls = len(url_list)\n", "    completed = 0\n", "    \n", "    with ThreadPoolExecutor(max_workers=2) as executor:\n", "        futures = {executor.submit(process_url, url): url for url in url_list}\n", "        \n", "        for future in as_completed(futures):\n", "            url = futures[future]\n", "            try:\n", "                future.result()\n", "            except Exception as e:\n", "                logging.error(f\"exception for {url} due to {e}\")\n", "            finally:\n", "                # Update and print progress\n", "                completed += 1\n", "                if completed % 10 == 0:\n", "                    progress = (completed / total_urls) * 100\n", "                    print(f\"{progress:.4f}% finished\")\n", "\n", "url_list = list(redirect_url_maps.keys())\n", "main(url_list[100:120])\n"]}, {"cell_type": "code", "execution_count": null, "id": "159fe3a4-bd5c-4f6a-9626-f4a99b315fe7", "metadata": {}, "outputs": [], "source": ["orig = \"https://www.fromsoftware.com\"\n", "dest = \"https://www.fromsoftware.jp/ww/company_about.html\""]}, {"cell_type": "code", "execution_count": null, "id": "235c711e-4824-4384-b5f5-642312e06f3b", "metadata": {}, "outputs": [], "source": ["\n", "wrapper = UrlHandler(orig)\n", "wrapper.set_redirect_urls([dest])"]}, {"cell_type": "code", "execution_count": null, "id": "9ade307a-33ab-45fe-bea5-6196873a3908", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}