{"cells": [{"cell_type": "code", "execution_count": null, "id": "018ea6dc-0b4d-4081-bb05-983fe0dc21ce", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "\n", "# Start of django setup block\n", "import django\n", "cur_dir = os.getcwd()\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "os.chdir(cur_dir)\n", "# End of django setup block\n", "\n", "import requests\n", "import logging\n", "from youtube_transcript_api._transcripts import TranscriptListFetcher\n", "from langchain_community.document_loaders import YoutubeLoader\n", "\n", "video_id = \"g83xobPCAn4\"\n", "\n", "SCRAPING_BEE_API_KEY = os.getenv(\"SCRAPING_BEE_API_KEY\")\n", "proxies = {\n", "    \"http\": f\"http://{SCRAPING_BEE_API_KEY}:render_js=False&premium_proxy=<EMAIL>:8886\",\n", "    \"https\": f\"https://{SCRAPING_BEE_API_KEY}:render_js=False&premium_proxy=<EMAIL>:8887\",\n", "    \"socks5\": f\"socks5://{SCRAPING_BEE_API_KEY}:render_js=False&premium_proxy=<EMAIL>:8888\",\n", "}\n", "\n", "\n", "with requests.Session() as http_client:\n", "    # [Tofu customized code] disable SSL verification,\n", "    # otherwise, we will have to install the SSL certificate of the proxy server which is a lot of trouble\n", "    http_client.verify = False\n", "    http_client.proxies = proxies if proxies else {}\n", "    transcript_list = TranscriptListFetcher(http_client).fetch(video_id)\n", "    print(transcript_list)\n", "    print(type(transcript_list))\n", "                    \n", "    transcript = transcript_list.find_transcript([\"en\"])\n", "    print(transcript)"]}, {"cell_type": "code", "execution_count": null, "id": "3cff912a-be16-4943-9c06-461ab3c5901c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}