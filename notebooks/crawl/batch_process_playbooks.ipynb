{"cells": [{"cell_type": "code", "execution_count": 1, "id": "394be5ee-26aa-45b9-944d-18bad34386b5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-06-24 01:32:59,967 INFO: found organization id: tofu-ykka\n", "2024-06-24 01:33:01,085 ERROR: counts: 826 26 1\n", "637\n", "1.5699% finished\n", "3.1397% finished\n", "4.7096% finished\n", "6.2794% finished\n", "7.8493% finished\n", "9.4192% finished\n", "10.9890% finished\n", "12.5589% finished\n", "14.1287% finished\n", "failed to crawl: deltafaucet.com with <Response [400]> {\"error\":\"No content found in url https://deltafaucet.com\"}\n", "15.6986% finished\n", "17.2684% finished\n", "18.8383% finished\n", "failed to crawl: hawaiianair.com with <Response [400]> {\"error\":\"Error 500 when crawling https://hawaiianair.com\"}\n", "20.4082% finished\n", "21.9780% finished\n", "failed to crawl: http://www.g2gmg.com with <Response [400]> {\"error\":\"Error 500 when crawling https://www.g2gmg.com\"}\n", "23.5479% finished\n", "failed to crawl: purple.com with <Response [400]> {\"error\":\"No content found in url https://purple.com\"}\n", "25.1177% finished\n", "26.6876% finished\n", "28.2575% finished\n", "29.8273% finished\n", "31.3972% finished\n", "32.9670% finished\n", "failed to crawl: crocs.com with <Response [400]> {\"error\":\"Error 500 when crawling https://crocs.com\"}\n", "failed to crawl: bcbsa.com with <Response [400]> {\"error\":\"Error 500 when crawling https://bcbsa.com\"}\n", "34.5369% finished\n", "failed to crawl: permobil.com with <Response [400]> {\"error\":\"Error 500 when crawling https://permobil.com\"}\n", "36.1068% finished\n", "37.6766% finished\n", "39.2465% finished\n", "40.8163% finished\n", "failed to crawl: city-furniture.com with <Response [400]> {\"error\":\"Error 500 when crawling https://city-furniture.com\"}\n", "failed to crawl: www.chewy.com with <Response [400]> {\"error\":\"Error 500 when crawling https://www.chewy.com\"}\n", "42.3862% finished\n", "failed to crawl: fossil.com with <Response [400]> {\"error\":\"Error 500 when crawling https://fossil.com\"}\n", "failed to crawl: www.blinkhealth.com with <Response [400]> {\"error\":\"No content found in url https://www.blinkhealth.com\"}\n", "failed to crawl: www.noom.com with <Response [400]> {\"error\":\"No content found in url https://www.noom.com\"}\n", "43.9560% finished\n", "45.5259% finished\n", "failed to crawl: usa.philips.com with <Response [400]> {\"error\":\"Error 500 when crawling https://usa.philips.com\"}\n", "47.0958% finished\n", "failed to crawl: aaanortheast.com with <Response [400]> {\"error\":\"Error 500 when crawling https://aaanortheast.com\"}\n", "48.6656% finished\n", "failed to crawl: pacificsunwear.com with <Response [400]> {\"error\":\"Error 500 when crawling https://pacificsunwear.com\"}\n", "failed to crawl: indeed.com with <Response [400]> {\"error\":\"Error 500 when crawling https://indeed.com\"}\n", "50.2355% finished\n", "51.8053% finished\n", "53.3752% finished\n", "54.9451% finished\n", "failed to crawl: bge.com with <Response [400]> {\"error\":\"No content found in url https://bge.com\"}\n", "56.5149% finished\n", "58.0848% finished\n", "failed to crawl: heartland.us with <Response [400]> {\"error\":\"Error 500 when crawling https://heartland.us\"}\n", "59.6546% finished\n", "failed to crawl: prudential.com with <Response [400]> {\"error\":\"Error 500 when crawling https://prudential.com\"}\n", "61.2245% finished\n", "62.7943% finished\n", "64.3642% finished\n", "65.9341% finished\n", "failed to crawl: pgecorp.com with <Response [400]> {\"error\":\"Error 500 when crawling https://pgecorp.com\"}\n", "67.5039% finished\n", "69.0738% finished\n", "failed to crawl: flatworldsolutions.com.ph with <Response [400]> {\"error\":\"Error 500 when crawling https://flatworldsolutions.com.ph\"}\n", "70.6436% finished\n", "72.2135% finished\n", "failed to crawl: groupon.com with <Response [400]> {\"error\":\"No content found in url https://groupon.com\"}\n", "73.7834% finished\n", "failed to crawl: us.411locals.com with <Response [400]> {\"error\":\"Error 500 when crawling https://us.411locals.com\"}\n", "75.3532% finished\n", "76.9231% finished\n", "78.4929% finished\n", "80.0628% finished\n", "failed to crawl: coxinc.com with <Response [400]> {\"error\":\"Error 404 when crawling https://coxinc.com\"}\n", "81.6327% finished\n", "83.2025% finished\n", "failed to crawl: first-american.net with <Response [400]> {\"error\":\"No content found in url https://first-american.net\"}\n", "84.7724% finished\n", "86.3422% finished\n", "87.9121% finished\n", "failed to crawl: jacksonhewitt.com with <Response [400]> {\"error\":\"No content found in url https://jacksonhewitt.com\"}\n", "89.4819% finished\n", "failed to crawl: estee.com with <Response [400]> {\"error\":\"Error 500 when crawling https://estee.com\"}\n", "failed to crawl: delta.com with <Response [400]> {\"error\":\"Error 500 when crawling https://delta.com\"}\n", "failed to crawl: ryanair.com with <Response [400]> {\"error\":\"Error 500 when crawling https://ryanair.com\"}\n", "91.0518% finished\n", "failed to crawl: evicore.com with <Response [400]> {\"error\":\"Error 500 when crawling https://evicore.com\"}\n", "failed to crawl: nordstrom.com with <Response [400]> {\"error\":\"No content found in url https://nordstrom.com\"}\n", "92.6217% finished\n", "failed to crawl: petsmart.com with <Response [400]> {\"error\":\"Error 500 when crawling https://petsmart.com\"}\n", "94.1915% finished\n", "95.7614% finished\n", "97.3312% finished\n", "98.9011% finished\n", "failed to crawl: comcast.com with <Response [400]> {\"error\":\"Error 500 when crawling https://comcast.com\"}\n", "failed to crawl: usg.com with <Response [400]> {\"error\":\"Error 500 when crawling https://usg.com\"}\n", "['deltafaucet.com', 'hawaiianair.com', 'http://www.g2gmg.com', 'purple.com', 'crocs.com', 'bcbsa.com', 'permobil.com', 'city-furniture.com', 'www.chewy.com', 'fossil.com', 'www.blinkhealth.com', 'www.noom.com', 'usa.philips.com', 'aaanortheast.com', 'pacificsunwear.com', 'indeed.com', 'bge.com', 'heartland.us', 'prudential.com', 'pgecorp.com', 'flatworldsolutions.com.ph', 'groupon.com', 'us.411locals.com', 'coxinc.com', 'first-american.net', 'jacksonhewitt.com', 'estee.com', 'delta.com', 'ryanair.com', 'evicore.com', 'nordstrom.com', 'petsmart.com', 'comcast.com', 'usg.com']\n"]}], "source": ["import os\n", "import sys\n", "\n", "\n", "# Start of django setup block\n", "import django\n", "cur_dir = os.getcwd()\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "os.chdir(cur_dir)\n", "# End of django setup block\n", "\n", "from crawl_utils import rebuild_objects_for_urls, get_all_urls_given_playbook\n", "from crawl_methods import dry_run_urls, try_crawl\n", "from api.data_loaders.url_handler import UrlHandler\n", "\n", "playbook_ids = [\n", "    # 642, # <PERSON><PERSON><PERSON>\n", "    # 569, # Sked Social\n", "    619, # Level AI\n", "    # 442, # <PERSON><PERSON><PERSON>\n", "    # 512, # <PERSON><PERSON>\n", "    # 565, # Adaptiva\n", "    # 597, # Reprise\n", "    # 511, # Coder\n", "    # 291\n", "]\n", "\n", "all_urls = get_all_urls_given_playbook(playbook_ids)\n", "print(len(all_urls))\n", "\n", "failed_urls = dry_run_urls(all_urls)\n", "print(failed_urls)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "eb88b086-d0e1-4c1d-90ba-41cd7499ce61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-06-24 01:55:23,978 INFO: checking: deltafaucet.com\n", "2024-06-24 01:55:23,980 INFO: checking: hawaiianair.com\n", "2024-06-24 01:55:23,981 INFO: checking: http://www.g2gmg.com\n", "failed to crawl: deltafaucet.com with <Response [400]> {\"error\":\"No content found in url https://deltafaucet.com\"}\n", "2024-06-24 01:55:25,651 ERROR: https://www.deltafaucet.com result having docs: False\n", "2024-06-24 01:55:25,924 ERROR: http://www.deltafaucet.com result having docs: False\n", "2024-06-24 01:55:26,347 ERROR: http://deltafaucet.com result having docs: False\n", "2024-06-24 01:55:26,355 INFO: building external info: keyword deltafaucet.com about\n", "failed to crawl: hawaiianair.com with <Response [400]> {\"error\":\"Error 500 when crawling https://hawaiianair.com\"}\n", "2024-06-24 01:55:34,628 INFO: Exception raised when crawling https://www.hawaiianair.com: HTTPSConnectionPool(host='www.hawaiianair.com', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x32aab9610>: Failed to resolve 'www.hawaiianair.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "failed to crawl: http://www.g2gmg.com with <Response [400]> {\"error\":\"Error 500 when crawling https://www.g2gmg.com\"}\n", "2024-06-24 01:55:39,049 INFO: building external info: keyword http://www.g2gmg.com about\n", "2024-06-24 01:55:49,495 ERROR: failed to crawl https://www.hawaiianair.com due to Error 500 when crawling https://www.hawaiianair.com\n", "2024-06-24 01:55:49,514 INFO: Exception raised when crawling http://www.hawaiianair.com: HTTPConnectionPool(host='www.hawaiianair.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x32ade9e50>: Failed to resolve 'www.hawaiianair.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 01:55:51,858 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 01:55:51,872 ERROR: keywords: deltafaucet.com about => https://www.deltafaucetcompany.com/\n", "2024-06-24 01:55:52,811 ERROR: https://www.deltafaucetcompany.com/ result having docs: True\n", "2024-06-24 01:55:52,820 INFO: https://www.deltafaucetcompany.com/ is good\n", "2024-06-24 01:55:52,821 INFO: checking: purple.com\n", "failed to crawl: purple.com with <Response [400]> {\"error\":\"No content found in url https://purple.com\"}\n", "2024-06-24 01:55:53,566 ERROR: https://www.purple.com result having docs: False\n", "2024-06-24 01:55:53,820 ERROR: http://www.purple.com result having docs: False\n", "2024-06-24 01:55:54,122 ERROR: http://purple.com result having docs: False\n", "2024-06-24 01:55:54,133 INFO: building external info: keyword purple.com about\n", "2024-06-24 01:55:57,789 ERROR: failed to crawl http://www.hawaiianair.com due to Error 500 when crawling http://www.hawaiianair.com\n", "2024-06-24 01:55:57,804 INFO: Exception raised when crawling http://hawaiianair.com: HTTPConnectionPool(host='hawaiianair.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x32adc2990>: Failed to resolve 'hawaiianair.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 01:56:07,267 ERROR: failed to crawl http://hawaiianair.com due to Error 500 when crawling http://hawaiianair.com\n", "2024-06-24 01:56:07,270 INFO: building external info: keyword hawaiianair.com about\n", "2024-06-24 01:56:15,975 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 01:56:15,983 ERROR: keywords: http://www.g2gmg.com about => https://g2gmg.com/about-us\n", "2024-06-24 01:56:28,827 ERROR: failed to crawl https://g2gmg.com/about-us due to Error 500 when crawling https://g2gmg.com/about-us\n", "2024-06-24 01:56:28,837 INFO: checking: crocs.com\n", "2024-06-24 01:56:35,377 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 01:56:35,389 ERROR: keywords: purple.com about => https://purple.com/about-us\n", "2024-06-24 01:56:35,608 ERROR: https://purple.com/about-us result having docs: False\n", "2024-06-24 01:56:35,619 INFO: checking: bcbsa.com\n", "failed to crawl: crocs.com with <Response [400]> {\"error\":\"Error 500 when crawling https://crocs.com\"}\n", "2024-06-24 01:56:49,416 ERROR: failed to crawl https://www.crocs.com due to Error 500 when crawling https://www.crocs.com\n", "2024-06-24 01:56:52,964 INFO: scraping_bee_search: <Response [500]>\n", "2024-06-24 01:56:52,964 ERROR: Error while searching by scraping bee: {\"error\": \"Please try again (you will not be charged for this request)\"}\n", "2024-06-24 01:56:52,969 INFO: checking: permobil.com\n", "2024-06-24 01:56:54,867 ERROR: failed to crawl http://www.crocs.com due to Error 500 when crawling http://www.crocs.com\n", "2024-06-24 01:57:06,088 ERROR: failed to crawl http://crocs.com due to Error 500 when crawling http://crocs.com\n", "2024-06-24 01:57:06,103 INFO: building external info: keyword crocs.com about\n", "2024-06-24 01:57:25,454 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 01:57:25,463 ERROR: keywords: crocs.com about => https://www.crocs.com.sg/company/about-crocs.html\n", "2024-06-24 01:57:37,334 ERROR: failed to crawl https://www.crocs.com.sg/company/about-crocs.html due to Error 500 when crawling https://www.crocs.com.sg/company/about-crocs.html\n", "2024-06-24 01:57:37,348 INFO: checking: city-furniture.com\n", "failed to crawl: permobil.com with <Response [400]> {\"error\":\"Error 500 when crawling https://permobil.com\"}\n", "2024-06-24 01:59:35,591 ERROR: https://www.permobil.com result having docs: True\n", "2024-06-24 01:59:35,596 INFO: https://www.permobil.com is good\n", "2024-06-24 01:59:35,596 INFO: checking: www.chewy.com\n", "failed to crawl: www.chewy.com with <Response [400]> {\"error\":\"Error 500 when crawling https://www.chewy.com\"}\n", "failed to crawl: bcbsa.com with <Response [400]> {\"error\":\"Error 500 when crawling https://bcbsa.com\"}\n", "2024-06-24 01:59:52,277 ERROR: failed to crawl http://www.chewy.com due to Error 500 when crawling http://www.chewy.com\n", "2024-06-24 01:59:52,289 INFO: building external info: keyword www.chewy.com about\n", "2024-06-24 01:59:57,109 INFO: Exception raised when crawling https://www.bcbsa.com: HTTPSConnectionPool(host='www.bcbsa.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x32dfddc90>, 'Connection to www.bcbsa.com timed out. (connect timeout=10)')). Will do a retry with scraping bee.\n", "2024-06-24 02:00:24,121 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:00:24,127 ERROR: keywords: www.chewy.com about => https://www.chewy.com/app/content/about-us\n", "2024-06-24 02:00:26,199 ERROR: https://www.chewy.com/app/content/about-us result having docs: True\n", "2024-06-24 02:00:26,205 INFO: https://www.chewy.com/app/content/about-us is good\n", "2024-06-24 02:00:26,206 INFO: checking: fossil.com\n", "2024-06-24 02:00:27,987 INFO: fossil.com is good\n", "2024-06-24 02:00:27,988 INFO: checking: www.blinkhealth.com\n", "failed to crawl: www.blinkhealth.com with <Response [400]> {\"error\":\"No content found in url https://www.blinkhealth.com\"}\n", "2024-06-24 02:00:30,019 ERROR: http://www.blinkhealth.com result having docs: False\n", "2024-06-24 02:00:30,032 INFO: building external info: keyword www.blinkhealth.com about\n", "failed to crawl: city-furniture.com with <Response [400]> {\"error\":\"Error 500 when crawling https://city-furniture.com\"}\n", "2024-06-24 02:00:52,602 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:00:52,607 ERROR: keywords: www.blinkhealth.com about => https://www.blinkhealth.com/about-us\n", "2024-06-24 02:00:53,222 ERROR: https://www.blinkhealth.com/about-us result having docs: False\n", "2024-06-24 02:00:53,227 INFO: checking: www.noom.com\n", "2024-06-24 02:00:58,850 INFO: Exception raised when crawling https://www.city-furniture.com: HTTPSConnectionPool(host='www.city-furniture.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x32adc2c10>, 'Connection to www.city-furniture.com timed out. (connect timeout=10)')). Will do a retry with scraping bee.\n", "2024-06-24 02:00:59,186 INFO: www.noom.com is good\n", "2024-06-24 02:00:59,187 INFO: checking: usa.philips.com\n", "29.4118% finished\n", "2024-06-24 02:02:58,760 ERROR: failed to crawl https://www.bcbsa.com due to Error 500 when crawling https://www.bcbsa.com\n", "2024-06-24 02:02:59,234 ERROR: http://www.bcbsa.com result having docs: True\n", "2024-06-24 02:02:59,246 INFO: http://www.bcbsa.com is good\n", "2024-06-24 02:02:59,249 INFO: checking: aaanortheast.com\n", "failed to crawl: aaanortheast.com with <Response [400]> {\"error\":\"Error 500 when crawling https://aaanortheast.com\"}\n", "2024-06-24 02:03:11,046 INFO: Exception raised when crawling https://www.aaanortheast.com: HTTPSConnectionPool(host='www.aaanortheast.com', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x32df89810>: Failed to resolve 'www.aaanortheast.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 02:03:24,318 ERROR: failed to crawl https://www.aaanortheast.com due to Error 500 when crawling https://www.aaanortheast.com\n", "2024-06-24 02:03:24,331 INFO: Exception raised when crawling http://www.aaanortheast.com: HTTPConnectionPool(host='www.aaanortheast.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x32e03fdd0>: Failed to resolve 'www.aaanortheast.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 02:03:39,003 ERROR: failed to crawl http://www.aaanortheast.com due to Error 500 when crawling http://www.aaanortheast.com\n", "2024-06-24 02:03:39,025 INFO: Exception raised when crawling http://aaanortheast.com: HTTPConnectionPool(host='aaanortheast.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x32e020490>: Failed to resolve 'aaanortheast.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 02:03:45,814 ERROR: failed to crawl http://aaanortheast.com due to Error 500 when crawling http://aaanortheast.com\n", "2024-06-24 02:03:45,823 INFO: building external info: keyword aaanortheast.com about\n", "2024-06-24 02:04:00,264 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:04:00,272 ERROR: keywords: aaanortheast.com about => https://northeast.aaa.com/\n", "2024-06-24 02:04:00,909 ERROR: https://northeast.aaa.com/ result having docs: True\n", "2024-06-24 02:04:00,914 INFO: https://northeast.aaa.com/ is good\n", "2024-06-24 02:04:00,914 INFO: checking: pacificsunwear.com\n", "2024-06-24 02:04:01,179 ERROR: failed to crawl https://www.city-furniture.com due to Error 500 when crawling https://www.city-furniture.com\n", "failed to crawl: pacificsunwear.com with <Response [400]> {\"error\":\"Error 500 when crawling https://pacificsunwear.com\"}\n", "2024-06-24 02:04:07,712 ERROR: https://www.pacificsunwear.com result having docs: True\n", "2024-06-24 02:04:07,716 INFO: https://www.pacificsunwear.com is good\n", "2024-06-24 02:04:07,717 INFO: checking: indeed.com\n", "failed to crawl: usa.philips.com with <Response [400]> {\"error\":\"Error 500 when crawling https://usa.philips.com\"}\n", "2024-06-24 02:04:11,218 INFO: Exception raised when crawling http://www.city-furniture.com: HTTPConnectionPool(host='www.city-furniture.com', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x32df8bd50>, 'Connection to www.city-furniture.com timed out. (connect timeout=10)')). Will do a retry with scraping bee.\n", "2024-06-24 02:04:11,380 ERROR: https://www.usa.philips.com result having docs: True\n", "2024-06-24 02:04:11,386 INFO: https://www.usa.philips.com is good\n", "2024-06-24 02:04:11,386 INFO: checking: bge.com\n", "failed to crawl: indeed.com with <Response [400]> {\"error\":\"Error 500 when crawling https://indeed.com\"}\n", "failed to crawl: bge.com with <Response [400]> {\"error\":\"No content found in url https://bge.com\"}\n", "2024-06-24 02:04:12,651 ERROR: https://www.bge.com result having docs: False\n", "2024-06-24 02:04:12,873 ERROR: http://www.bge.com result having docs: False\n", "2024-06-24 02:04:13,816 ERROR: http://bge.com result having docs: False\n", "2024-06-24 02:04:13,831 INFO: building external info: keyword bge.com about\n", "2024-06-24 02:04:16,509 ERROR: failed to crawl https://www.indeed.com due to Error 500 when crawling https://www.indeed.com\n", "2024-06-24 02:04:17,138 ERROR: http://www.indeed.com result having docs: True\n", "2024-06-24 02:04:17,144 INFO: http://www.indeed.com is good\n", "2024-06-24 02:04:17,144 INFO: checking: heartland.us\n", "2024-06-24 02:04:29,066 INFO: heartland.us is good\n", "2024-06-24 02:04:29,068 INFO: checking: prudential.com\n", "failed to crawl: prudential.com with <Response [400]> {\"error\":\"Error 500 when crawling https://prudential.com\"}\n", "2024-06-24 02:04:39,172 ERROR: https://www.prudential.com result having docs: True\n", "2024-06-24 02:04:39,177 INFO: https://www.prudential.com is good\n", "2024-06-24 02:04:39,178 INFO: checking: pgecorp.com\n", "2024-06-24 02:04:41,930 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:04:41,938 ERROR: keywords: bge.com about => https://www.bge.com/AboutUs/Pages/default.aspx\n", "2024-06-24 02:04:42,211 ERROR: https://www.bge.com/AboutUs/Pages/default.aspx result having docs: False\n", "2024-06-24 02:04:42,226 INFO: checking: flatworldsolutions.com.ph\n", "2024-06-24 02:07:12,388 ERROR: failed to crawl http://www.city-furniture.com due to Error 500 when crawling http://www.city-furniture.com\n", "2024-06-24 02:07:22,420 INFO: Exception raised when crawling http://city-furniture.com: HTTPConnectionPool(host='city-furniture.com', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x32f43d250>, 'Connection to city-furniture.com timed out. (connect timeout=10)')). Will do a retry with scraping bee.\n", "failed to crawl: flatworldsolutions.com.ph with <Response [400]> {\"error\":\"Error 500 when crawling https://flatworldsolutions.com.ph\"}\n", "2024-06-24 02:07:43,821 INFO: Exception raised when crawling https://www.flatworldsolutions.com.ph: HTTPSConnectionPool(host='www.flatworldsolutions.com.ph', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x32df88a90>, 'Connection to www.flatworldsolutions.com.ph timed out. (connect timeout=10)')). Will do a retry with scraping bee.\n", "failed to crawl: pgecorp.com with <Response [400]> {\"error\":\"Error 500 when crawling https://pgecorp.com\"}\n", "2024-06-24 02:08:03,090 ERROR: https://www.pgecorp.com result having docs: True\n", "2024-06-24 02:08:03,096 INFO: https://www.pgecorp.com is good\n", "2024-06-24 02:08:03,097 INFO: checking: groupon.com\n", "failed to crawl: groupon.com with <Response [400]> {\"error\":\"No content found in url https://groupon.com\"}\n", "2024-06-24 02:08:05,609 ERROR: https://www.groupon.com result having docs: False\n", "2024-06-24 02:08:06,175 ERROR: http://www.groupon.com result having docs: False\n", "2024-06-24 02:08:09,306 ERROR: http://groupon.com result having docs: False\n", "2024-06-24 02:08:09,318 INFO: building external info: keyword groupon.com about\n", "2024-06-24 02:08:28,064 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:08:28,074 ERROR: keywords: groupon.com about => https://www.groupon.com/articles/about\n", "2024-06-24 02:08:29,179 ERROR: https://www.groupon.com/articles/about result having docs: True\n", "2024-06-24 02:08:29,184 INFO: https://www.groupon.com/articles/about is good\n", "2024-06-24 02:08:29,185 INFO: checking: us.411locals.com\n", "58.8235% finished\n", "failed to crawl: us.411locals.com with <Response [400]> {\"error\":\"Error 500 when crawling https://us.411locals.com\"}\n", "2024-06-24 02:08:40,614 INFO: Exception raised when crawling https://www.us.411locals.com: HTTPSConnectionPool(host='www.us.411locals.com', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x32aaa5910>: Failed to resolve 'www.us.411locals.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 02:08:55,458 ERROR: failed to crawl https://www.us.411locals.com due to Error 500 when crawling https://www.us.411locals.com\n", "2024-06-24 02:08:55,473 INFO: Exception raised when crawling http://www.us.411locals.com: HTTPConnectionPool(host='www.us.411locals.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x32f43ca50>: Failed to resolve 'www.us.411locals.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 02:09:06,004 ERROR: failed to crawl http://www.us.411locals.com due to Error 500 when crawling http://www.us.411locals.com\n", "2024-06-24 02:09:06,023 INFO: Exception raised when crawling http://us.411locals.com: HTTPConnectionPool(host='us.411locals.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x32f618d50>: Failed to resolve 'us.411locals.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 02:09:15,581 ERROR: failed to crawl http://us.411locals.com due to Error 500 when crawling http://us.411locals.com\n", "2024-06-24 02:09:15,591 INFO: building external info: keyword us.411locals.com about\n", "2024-06-24 02:09:34,585 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:09:34,597 ERROR: keywords: us.411locals.com about => https://411locals.com/company/\n", "2024-06-24 02:09:35,602 ERROR: https://411locals.com/company/ result having docs: True\n", "2024-06-24 02:09:35,607 INFO: https://411locals.com/company/ is good\n", "2024-06-24 02:09:35,608 INFO: checking: coxinc.com\n", "failed to crawl: coxinc.com with <Response [400]> {\"error\":\"Error 500 when crawling https://coxinc.com\"}\n", "2024-06-24 02:09:57,659 INFO: Exception raised when crawling https://www.coxinc.com: HTTPSConnectionPool(host='www.coxinc.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1002)'))). Will do a retry with scraping bee.\n", "2024-06-24 02:10:12,084 ERROR: failed to crawl https://www.coxinc.com due to Error 500 when crawling https://www.coxinc.com\n", "2024-06-24 02:10:23,491 ERROR: failed to crawl http://city-furniture.com due to Error 500 when crawling http://city-furniture.com\n", "2024-06-24 02:10:23,501 INFO: building external info: keyword city-furniture.com about\n", "2024-06-24 02:10:34,629 ERROR: failed to crawl http://www.coxinc.com due to Error 500 when crawling http://www.coxinc.com\n", "2024-06-24 02:10:40,137 ERROR: failed to crawl http://coxinc.com due to Error 500 when crawling http://coxinc.com\n", "2024-06-24 02:10:40,156 INFO: building external info: keyword coxinc.com about\n", "2024-06-24 02:10:40,682 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:10:40,687 ERROR: keywords: city-furniture.com about => https://www.cityfurniture.com/company/about-us\n", "2024-06-24 02:10:41,657 ERROR: https://www.cityfurniture.com/company/about-us result having docs: True\n", "2024-06-24 02:10:41,661 INFO: https://www.cityfurniture.com/company/about-us is good\n", "2024-06-24 02:10:41,661 INFO: checking: first-american.net\n", "failed to crawl: first-american.net with <Response [400]> {\"error\":\"No content found in url https://first-american.net\"}\n", "2024-06-24 02:10:42,170 ERROR: https://www.first-american.net result having docs: False\n", "2024-06-24 02:10:42,320 ERROR: http://www.first-american.net result having docs: False\n", "2024-06-24 02:10:42,461 ERROR: http://first-american.net result having docs: False\n", "2024-06-24 02:10:42,469 INFO: building external info: keyword first-american.net about\n", "2024-06-24 02:10:50,114 ERROR: failed to crawl https://www.flatworldsolutions.com.ph due to Error 500 when crawling https://www.flatworldsolutions.com.ph\n", "2024-06-24 02:10:51,882 ERROR: http://www.flatworldsolutions.com.ph result having docs: True\n", "2024-06-24 02:10:51,887 INFO: http://www.flatworldsolutions.com.ph is good\n", "2024-06-24 02:10:51,888 INFO: checking: jacksonhewitt.com\n", "failed to crawl: jacksonhewitt.com with <Response [400]> {\"error\":\"No content found in url https://jacksonhewitt.com\"}\n", "2024-06-24 02:10:52,595 ERROR: https://www.jacksonhewitt.com result having docs: False\n", "2024-06-24 02:10:52,832 ERROR: http://www.jacksonhewitt.com result having docs: False\n", "2024-06-24 02:10:53,189 ERROR: http://jacksonhewitt.com result having docs: False\n", "2024-06-24 02:10:53,205 INFO: building external info: keyword jacksonhewitt.com about\n", "2024-06-24 02:11:08,509 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:11:08,524 ERROR: keywords: coxinc.com about => https://www.coxenterprises.com/about-us\n", "2024-06-24 02:11:09,219 ERROR: https://www.coxenterprises.com/about-us result having docs: True\n", "2024-06-24 02:11:09,226 INFO: https://www.coxenterprises.com/about-us is good\n", "2024-06-24 02:11:09,227 INFO: checking: estee.com\n", "2024-06-24 02:11:18,895 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:11:18,900 ERROR: keywords: jacksonhewitt.com about => https://www.jacksonhewitt.com/\n", "2024-06-24 02:11:19,111 ERROR: https://www.jacksonhewitt.com/ result having docs: True\n", "2024-06-24 02:11:19,126 INFO: https://www.jacksonhewitt.com/ is good\n", "2024-06-24 02:11:19,137 INFO: checking: delta.com\n", "2024-06-24 02:11:28,107 INFO: scraping_bee_search: <Response [500]>\n", "2024-06-24 02:11:28,108 ERROR: Error while searching by scraping bee: {\"error\": \"Please try again (you will not be charged for this request)\"}\n", "2024-06-24 02:11:28,121 INFO: checking: ryanair.com\n", "failed to crawl: delta.com with <Response [400]> {\"error\":\"Error 500 when crawling https://delta.com\"}\n", "2024-06-24 02:13:55,925 ERROR: https://www.delta.com result having docs: True\n", "2024-06-24 02:13:55,939 INFO: https://www.delta.com is good\n", "2024-06-24 02:13:55,940 INFO: checking: evicore.com\n", "failed to crawl: evicore.com with <Response [400]> {\"error\":\"Error 500 when crawling https://evicore.com\"}\n", "2024-06-24 02:14:07,162 ERROR: https://www.evicore.com result having docs: True\n", "2024-06-24 02:14:07,168 INFO: https://www.evicore.com is good\n", "2024-06-24 02:14:07,169 INFO: checking: nordstrom.com\n", "failed to crawl: nordstrom.com with <Response [400]> {\"error\":\"No content found in url https://nordstrom.com\"}\n", "2024-06-24 02:14:08,262 ERROR: https://www.nordstrom.com result having docs: False\n", "2024-06-24 02:14:08,662 ERROR: http://www.nordstrom.com result having docs: False\n", "2024-06-24 02:14:09,202 ERROR: http://nordstrom.com result having docs: False\n", "2024-06-24 02:14:09,211 INFO: building external info: keyword nordstrom.com about\n", "failed to crawl: estee.com with <Response [400]> {\"error\":\"Error 500 when crawling https://estee.com\"}\n", "2024-06-24 02:14:31,837 INFO: Exception raised when crawling https://www.estee.com: HTTPSConnectionPool(host='www.estee.com', port=443): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x3002b8110>: Failed to resolve 'www.estee.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "failed to crawl: ryanair.com with <Response [400]> {\"error\":\"Error 500 when crawling https://ryanair.com\"}\n", "2024-06-24 02:14:40,098 ERROR: failed to crawl https://www.estee.com due to Error 500 when crawling https://www.estee.com\n", "2024-06-24 02:14:40,125 INFO: Exception raised when crawling http://www.estee.com: HTTPConnectionPool(host='www.estee.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x30021bb10>: Failed to resolve 'www.estee.com' ([Errno 8] nodename nor servname provided, or not known)\")). Will do a retry with scraping bee.\n", "2024-06-24 02:14:40,384 ERROR: https://www.ryanair.com result having docs: True\n", "2024-06-24 02:14:40,398 INFO: https://www.ryanair.com is good\n", "2024-06-24 02:14:40,399 INFO: checking: petsmart.com\n", "2024-06-24 02:14:46,459 ERROR: failed to crawl http://www.estee.com due to Error 500 when crawling http://www.estee.com\n", "2024-06-24 02:14:53,943 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:14:53,957 ERROR: keywords: nordstrom.com about => https://www.nordstrom.com/browse/about\n", "2024-06-24 02:14:54,357 ERROR: https://www.nordstrom.com/browse/about result having docs: False\n", "2024-06-24 02:14:54,367 INFO: checking: comcast.com\n", "88.2353% finished\n", "failed to crawl: comcast.com with <Response [400]> {\"error\":\"Error 500 when crawling https://comcast.com\"}\n", "2024-06-24 02:15:04,870 ERROR: https://www.comcast.com result having docs: True\n", "2024-06-24 02:15:04,875 INFO: https://www.comcast.com is good\n", "2024-06-24 02:15:04,876 INFO: checking: usg.com\n", "2024-06-24 02:15:06,590 INFO: Exception raised when crawling http://estee.com: HTTPConnectionPool(host='estee.com', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x300200510>, 'Connection to estee.com timed out. (connect timeout=10)')). Will do a retry with scraping bee.\n", "failed to crawl: usg.com with <Response [400]> {\"error\":\"Error 500 when crawling https://usg.com\"}\n", "2024-06-24 02:17:37,377 ERROR: https://www.usg.com result having docs: True\n", "2024-06-24 02:17:37,385 INFO: https://www.usg.com is good\n", "failed to crawl: petsmart.com with <Response [400]> {\"error\":\"Error 500 when crawling https://petsmart.com\"}\n", "2024-06-24 02:18:01,423 ERROR: https://www.petsmart.com result having docs: True\n", "2024-06-24 02:18:01,428 INFO: https://www.petsmart.com is good\n", "2024-06-24 02:18:07,679 ERROR: failed to crawl http://estee.com due to Error 500 when crawling http://estee.com\n", "2024-06-24 02:18:07,698 INFO: building external info: keyword estee.com about\n", "2024-06-24 02:18:35,020 INFO: scraping_bee_search: <Response [200]>\n", "2024-06-24 02:18:35,039 ERROR: keywords: estee.com about => https://www.elcompanies.com/en\n", "2024-06-24 02:18:35,877 ERROR: https://www.elcompanies.com/en result having docs: True\n", "2024-06-24 02:18:35,883 INFO: https://www.elcompanies.com/en is good\n", "['http://www.g2gmg.com', 'purple.com', 'hawaiianair.com', 'crocs.com', 'www.blinkhealth.com', 'bge.com', 'first-american.net', 'nordstrom.com']\n", "{'deltafaucet.com': 'https://www.deltafaucetcompany.com/', 'permobil.com': 'https://www.permobil.com', 'www.chewy.com': 'https://www.chewy.com/app/content/about-us', 'bcbsa.com': 'http://www.bcbsa.com', 'aaanortheast.com': 'https://northeast.aaa.com/', 'pacificsunwear.com': 'https://www.pacificsunwear.com', 'usa.philips.com': 'https://www.usa.philips.com', 'indeed.com': 'http://www.indeed.com', 'prudential.com': 'https://www.prudential.com', 'pgecorp.com': 'https://www.pgecorp.com', 'groupon.com': 'https://www.groupon.com/articles/about', 'us.411locals.com': 'https://411locals.com/company/', 'city-furniture.com': 'https://www.cityfurniture.com/company/about-us', 'flatworldsolutions.com.ph': 'http://www.flatworldsolutions.com.ph', 'coxinc.com': 'https://www.coxenterprises.com/about-us', 'jacksonhewitt.com': 'https://www.jacksonhewitt.com/', 'delta.com': 'https://www.delta.com', 'evicore.com': 'https://www.evicore.com', 'ryanair.com': 'https://www.ryanair.com', 'comcast.com': 'https://www.comcast.com', 'usg.com': 'https://www.usg.com', 'petsmart.com': 'https://www.petsmart.com', 'estee.com': 'https://www.elcompanies.com/en'}\n"]}], "source": ["import os\n", "import sys\n", "\n", "\n", "# Start of django setup block\n", "import django\n", "cur_dir = os.getcwd()\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "os.chdir(cur_dir)\n", "# End of django setup block\n", "\n", "from crawl_utils import rebuild_objects_for_urls, get_all_urls_given_playbook\n", "from crawl_methods import dry_run_urls, try_crawl\n", "from api.data_loaders.url_handler import UrlHandler\n", "\n", "\n", "# failed_urls = ['pamf.org', 'http://summahealth.org', 'servbhs.org', 'http://pamf.org', 'pmma.org', 'shrinenet.org', 'youthranch.org', 'arcmontmd.org', 'http://wvmedical.com', 'pewtrusts.org', 'glcac.org', 'nerc.net', 'brazoselectric.com', 'sachealthsystem.org', 'som.umaryland.edu', 'lifelinkfound.org', 'easternmichiganbbb.org', 'kns.com', 'arshtcenter.org', 'bnydc.org', 'glbhealth.org', 'wartburg.edu', 'hmc.psu.edu', 'nfib.org', 'phci.org', 'conehealth.com', 'landon.net', 'cato.org', 'resource-mn.org', 'http://leroyhaynes.org', 'janelia.hhmi.org', 'apdmh.org', 'inpo.org', 'summahealth.org', 'grahamhospital.org', 'famhealth.com', 'sfish.org', 'chcinj.org', 'alsac.stjude.org', 'bolles.org', 'http://healthdelivery.org', 'nemcsa.org', 'http://dciinc.org', 'http://sutterphysicianservices.com', 'leroyhaynes.org', 'mprc.umaryland.edu', 'cfamhc.org', 'bbbsc.org', 'dciinc.org', 'rmlspecialtyhospital.org', 'http://cookchp.com', 'bhcpns.org', 'lifeschools.net', 'abcinfo.org', 'pacs-ky.org', 'lsnyc.org', 'mmch.org', 'partners.org', 'shinc.org', 'dts.edu', 'amh.org', 'http://pixelworks.com', 'christianhomes.org', 'cumminsbhs.org', 'queens.edu', 'oclc.org', 'ccconcern.org', 'vnhh.org', 'goodwillsew.com', 'http://chcpinellas.org', 'http://arshtcenter.org', 'http://lifeschools.net', 'signaturehealthinc.com', 'indonornetwork.org', 'cheshire-med.com', 'disabilityrightsca.org', 'ycs.org', 'brennan.law.nyu.edu', 'sbch.org', 'vbhcs.org', 'njid.org', 'aero.org', 'http://academicanv.com', 'lvh.com', 'easterseals.com', 'dfci.harvard.edu', 'cusa.canon.com', 'mhc.net', 'cofmc.org', 'stjohns.edu', 'uww.unitedway.org', 'nfa.futures.org', 'texmed.org', 'healthsourceofohio.com', 'cvph.org', 'uscrimail.org', 'aspirehp.org', 'bshsi.org', 'ct.bbb.org', 'saintpetersuh.com', 'dh.org', 'ymcachicago.org', 'http://goodwillsew.com', 'ned.org', 'mgh.harvard.edu', 'http://dts.edu', 'giregional.org', 'littletonregionalhealthcare.org', 'academicanv.com', 'northpark.edu', 'gmh.edu', 'pixelworks.com']\n", "failed_urls = ['deltafaucet.com', 'hawaiianair.com', 'http://www.g2gmg.com', 'purple.com', 'crocs.com', 'bcbsa.com', 'permobil.com', 'city-furniture.com', 'www.chewy.com', 'fossil.com', 'www.blinkhealth.com', 'www.noom.com', 'usa.philips.com', 'aaanortheast.com', 'pacificsunwear.com', 'indeed.com', 'bge.com', 'heartland.us', 'prudential.com', 'pgecorp.com', 'flatworldsolutions.com.ph', 'groupon.com', 'us.411locals.com', 'coxinc.com', 'first-american.net', 'jacksonhewitt.com', 'estee.com', 'delta.com', 'ryanair.com', 'evicore.com', 'nordstrom.com', 'petsmart.com', 'comcast.com', 'usg.com']\n", "failed_urls, redirect_url_maps = try_crawl(failed_urls)\n", "print(failed_urls)\n", "print(redirect_url_maps)"]}, {"cell_type": "code", "execution_count": null, "id": "27661725-8582-446f-a31b-e73f68ad5f29", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}