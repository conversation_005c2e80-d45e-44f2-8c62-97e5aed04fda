import logging
import os
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

import requests
from api.data_loaders.url_handler import Url<PERSON>andler
from api.data_loaders.web_page_loader import TofuWebPageLoader
from api.models import UrlRecord
from api.playbook_build.external_info_builder import ExternalInfoBuilder
from langchain_core.documents import Document


def core_crawl(url_to_crawl):
    # url = "https://dev.api.tofuhq.com/api/playbook/extract_doc/"
    url = "http://0.0.0.0:8000/api/playbook/extract_doc/"

    # Headers including Basic Auth, Content-Type, CSRF token, and Accept
    headers = {
        "Accept": "application/json",
        "Authorization": f"Bearer {os.environ.get('TOFU_ADMIN_TOKEN')}",
        "Content-Type": "application/json",
    }

    # Data payload for the POST request
    payload = {
        "doc_type": "url",
        "doc_value": url_to_crawl,
        "disable_cache": True,
        "update_cache": False,
    }

    # Make the POST request
    response = requests.post(url, headers=headers, json=payload)
    if response.status_code == 200:
        # Parse the response data
        response_data = response.json()
        documents = [
            Document(
                page_content=response_data.get("content", ""),
                metadata=response_data.get("metadata", {}),
            )
        ]
        return True
    elif response.status_code // 100 == 5:
        logging.error(
            f"crawl: {url_to_crawl} skipped with returns {response.status_code} {response.text}"
        )
        return True
    else:
        if response.text.startswith("Error 429"):
            logging.error(
                f"crawl: {url_to_crawl} skipped with returns {response.status_code} {response.text}"
            )
            return True
        print(f"failed to crawl: {url_to_crawl} with {response} {response.text}")
        return False


def dry_run_urls(urls):
    completed = 0
    total_urls = len(urls)

    failed_urls = []

    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = {executor.submit(core_crawl, url): url for url in urls}

        for future in as_completed(futures):
            url = futures[future]
            try:
                result = future.result()
                if not result:
                    failed_urls.append(url)
            except Exception as e:
                logging.error(f"exception for {url} due to {e}")
            finally:
                completed += 1
                if completed % 10 == 0:
                    progress = (completed / total_urls) * 100
                    print(f"{progress:.4f}% finished")

    return failed_urls


def adjust_urls(url):
    cands = []
    if not url.startswith("http") and not url.startswith("www"):
        cands.append(f"https://www.{url}")
        cands.append(f"http://www.{url}")

    if not url.startswith("http"):
        cands.append(f"http://{url}")

    return cands


def crawl_about(url):
    builder = ExternalInfoBuilder()
    results = builder.build(
        f"{url} about",
        num_links=1,
        include_sitelinks=False,
    )
    if not results:
        return []
    redirect_url = results[0]["url"]
    logging.error(f"keywords: {url} about => {redirect_url}")
    return [redirect_url]


def get_possible_urls(url):
    for cand_url in adjust_urls(url):
        yield cand_url
    for cand_url in crawl_about(url):
        yield cand_url


def skip_url(url):
    if url.find("google") != -1:
        return True
    if url.find("facebook") != -1:
        return True
    if url.find("linkedin") != -1:
        return True
    if url.find("notion") != -1:
        return True
    if url.endswith(".pdf"):
        return True
    return False


def local_crawl(url):
    try:
        loader = TofuWebPageLoader(url, deep_crawl=False)
        docs = loader.load()
        docs = [doc for doc in docs if doc.page_content.strip()]
        logging.error(f"{url} result having docs: {bool(docs)}")
        return bool(docs)
    except Exception as e:
        logging.error(f"failed to crawl {url} due to {e}")
        return False
    return False


def process_url(url):
    logging.info(f"checking: {url}")

    if skip_url(url):
        logging.error(f"skipping: {url}")
        return True, None

    result = core_crawl(url)
    if result:
        logging.info(f"{url} is good")
        return True, None

    for candidate_url in get_possible_urls(url):
        result = local_crawl(candidate_url)
        if result:
            logging.info(f"{candidate_url} is good")
            return True, candidate_url

    return False, None


def try_crawl(url_list):
    total_urls = len(url_list)
    completed = 0

    redirect_url_maps = {}
    failed_urls = []

    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = {executor.submit(process_url, url): url for url in url_list}

        for future in as_completed(futures):
            url = futures[future]
            try:
                result, redirect_url = future.result()
                if not result:
                    failed_urls.append(url)
                elif redirect_url:
                    redirect_url_maps[url] = redirect_url
            except Exception as e:
                logging.error(f"exception for {url} due to {e}")
            finally:
                completed += 1
                if completed % 10 == 0:
                    progress = (completed / total_urls) * 100
                    print(f"{progress:.4f}% finished")

    return failed_urls, redirect_url_maps
