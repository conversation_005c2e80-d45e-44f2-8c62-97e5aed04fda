{"cells": [{"cell_type": "code", "execution_count": null, "id": "00b44d99-b699-4491-a8de-d5fb7194b1cd", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "\n", "# Start of django setup block\n", "import django\n", "cur_dir = os.getcwd()\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "os.chdir(cur_dir)\n", "# End of django setup block\n", "\n", "from crawl_utils import rebuild_objects_for_url\n", "from api.data_loaders.url_handler import UrlHandler\n", "\n", "\n", "\n", "orig = \"https://www.fromsoftware.com\"\n", "dest = \"https://www.fromsoftware.jp/ww/company_about.html\"\n", "\n", "wrapper = UrlHandler(orig)\n", "wrapper.set_redirect_urls([dest])\n", "\n", "rebuild_objects_for_url(orig, mock=True)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "db96fabc-6760-4a0d-b5aa-5daf5d6cad39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-06-24 02:24:18,413 ERROR: Rebuilding object: TargetInfo object (47360)\n", "2024-06-24 02:24:18,590 INFO: Loading data from url: https://petsmart.com\n", "2024-06-24 02:24:18,594 INFO: Cached data found for: https://petsmart.com\n", "2024-06-24 02:24:18,922 ERROR: Failed to load data from url: https://petsmart.com due to error: \n", "Error 500 when crawling https://petsmart.com\n", "2024-06-24 02:24:19,258 ERROR: 404\n", "2024-06-24 02:24:19,267 ERROR: {'error': 'value not found for key playbook_413_target_status'}\n", "2024-06-24 02:24:20,381 ERROR: Rebuilding object: TargetInfo object (4337)\n", "2024-06-24 02:24:20,683 INFO: Loading data from url: https://usg.com\n", "2024-06-24 02:24:20,687 INFO: Cached data found for: https://usg.com\n", "2024-06-24 02:24:20,770 ERROR: Failed to load data from url: https://usg.com due to error: \n", "Error 500 when crawling https://usg.com\n", "2024-06-24 02:24:20,943 ERROR: 404\n", "2024-06-24 02:24:20,953 ERROR: {'error': 'value not found for key playbook_183_target_status'}\n", "2024-06-24 02:24:21,736 ERROR: Rebuilding object: TargetInfo object (4063)\n", "2024-06-24 02:24:22,011 INFO: Loading data from url: https://comcast.com\n", "2024-06-24 02:24:22,015 INFO: Cached data found for: https://comcast.com\n", "2024-06-24 02:24:22,097 ERROR: Failed to load data from url: https://comcast.com due to error: \n", "Error 500 when crawling https://comcast.com\n", "2024-06-24 02:24:22,239 ERROR: 404\n", "2024-06-24 02:24:22,248 ERROR: {'error': 'value not found for key playbook_183_target_status'}\n", "2024-06-24 02:24:22,477 INFO: build value_prop for target 4063: Accounts-Comcast\n", "2024-06-24 02:24:22,538 INFO: debug: the prompt we return here is: input_variables=['company_name', 'company_summary', 'single_target_context', 'single_target_key1', 'single_target_key2'] template='\\n{company_summary}\\nOne of the targets {company_name} has in {single_target_key1} is {single_target_key2}.\\nContext about {single_target_key2}:\\n{single_target_context}\\nCreate bullet points on pain points that {single_target_key2} faces that they\\ncan help solve and also value propositions that {company_name} would provide to {single_target_key2}\\nRegardless of the pain points that {single_target_key2} faces, include only information that is factually correct based off the products and services that {company_name} provides\\n'\n", "2024-06-24 02:24:30,583 INFO: HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2024-06-24 02:24:30,592 INFO: build value prop for Accounts-Comcast\n", "2024-06-24 02:24:30,629 ERROR: Rebuilding object: TargetInfo object (4013)\n", "2024-06-24 02:24:30,920 INFO: Loading data from url: https://delta.com\n", "2024-06-24 02:24:30,923 INFO: Cached data found for: https://delta.com\n", "2024-06-24 02:24:31,044 ERROR: Failed to load data from url: https://delta.com due to error: \n", "Error 500 when crawling https://delta.com\n", "2024-06-24 02:24:31,205 ERROR: 404\n", "2024-06-24 02:24:31,213 ERROR: {'error': 'value not found for key playbook_183_target_status'}\n", "2024-06-24 02:24:31,440 INFO: build value_prop for target 4013: Accounts-Delta Air Lines\n", "2024-06-24 02:24:31,495 INFO: debug: the prompt we return here is: input_variables=['company_name', 'company_summary', 'single_target_context', 'single_target_key1', 'single_target_key2'] template='\\n{company_summary}\\nOne of the targets {company_name} has in {single_target_key1} is {single_target_key2}.\\nContext about {single_target_key2}:\\n{single_target_context}\\nCreate bullet points on pain points that {single_target_key2} faces that they\\ncan help solve and also value propositions that {company_name} would provide to {single_target_key2}\\nRegardless of the pain points that {single_target_key2} faces, include only information that is factually correct based off the products and services that {company_name} provides\\n'\n", "2024-06-24 02:24:40,226 INFO: HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2024-06-24 02:24:40,229 INFO: build value prop for Accounts-Delta Air Lines\n", "2024-06-24 02:24:40,272 ERROR: Rebuilding object: TargetInfo object (47720)\n", "2024-06-24 02:24:40,624 INFO: Loading data from url: https://comcast.com\n", "2024-06-24 02:24:40,627 INFO: Cached data found for: https://comcast.com\n", "2024-06-24 02:24:40,709 ERROR: Failed to load data from url: https://comcast.com due to error: \n", "Error 500 when crawling https://comcast.com\n", "2024-06-24 02:24:40,871 ERROR: 404\n", "2024-06-24 02:24:40,881 ERROR: {'error': 'value not found for key playbook_444_target_status'}\n", "2024-06-24 02:24:41,867 ERROR: Rebuilding object: TargetInfo object (3927)\n", "2024-06-24 02:24:42,134 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:24:42,137 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:24:42,226 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:24:42,383 ERROR: 404\n", "2024-06-24 02:24:42,388 ERROR: {'error': 'value not found for key playbook_183_target_status'}\n", "2024-06-24 02:24:42,632 ERROR: Rebuilding object: TargetInfo object (3952)\n", "2024-06-24 02:24:42,848 INFO: Loading data from url: https://pgecorp.com\n", "2024-06-24 02:24:42,850 INFO: Cached data found for: https://pgecorp.com\n", "2024-06-24 02:24:42,934 ERROR: Failed to load data from url: https://pgecorp.com due to error: \n", "Error 500 when crawling https://pgecorp.com\n", "2024-06-24 02:24:43,089 ERROR: 404\n", "2024-06-24 02:24:43,107 ERROR: {'error': 'value not found for key playbook_183_target_status'}\n", "2024-06-24 02:24:43,393 ERROR: Rebuilding object: TargetInfo object (4447)\n", "2024-06-24 02:24:43,892 INFO: Loading data from url: https://petsmart.com\n", "2024-06-24 02:24:43,893 INFO: Cached data found for: https://petsmart.com\n", "2024-06-24 02:24:43,974 ERROR: Failed to load data from url: https://petsmart.com due to error: \n", "Error 500 when crawling https://petsmart.com\n", "2024-06-24 02:24:44,158 ERROR: 404\n", "2024-06-24 02:24:44,168 ERROR: {'error': 'value not found for key playbook_183_target_status'}\n", "2024-06-24 02:24:44,452 ERROR: Rebuilding object: TargetInfo object (62709)\n", "2024-06-24 02:24:44,736 INFO: Loading data from url: https://comcast.com\n", "2024-06-24 02:24:44,738 INFO: Cached data found for: https://comcast.com\n", "2024-06-24 02:24:44,818 ERROR: Failed to load data from url: https://comcast.com due to error: \n", "Error 500 when crawling https://comcast.com\n", "2024-06-24 02:24:44,978 ERROR: 404\n", "2024-06-24 02:24:44,991 ERROR: {'error': 'value not found for key playbook_444_target_status'}\n", "2024-06-24 02:24:45,285 ERROR: Rebuilding object: TargetInfo object (45264)\n", "2024-06-24 02:24:45,610 INFO: Loading data from url: https://www.chewy.com\n", "2024-06-24 02:24:45,613 INFO: Cached data found for: https://www.chewy.com\n", "2024-06-24 02:24:45,704 ERROR: Failed to load data from url: https://www.chewy.com due to error: \n", "Error 500 when crawling https://www.chewy.com\n", "2024-06-24 02:24:45,870 ERROR: 404\n", "2024-06-24 02:24:45,881 ERROR: {'error': 'value not found for key playbook_423_target_status'}\n", "2024-06-24 02:24:47,161 INFO: build value_prop for target 45264: Fortune 500-Chewy\n", "2024-06-24 02:24:47,220 INFO: debug: the prompt we return here is: input_variables=['company_name', 'company_summary', 'single_target_context', 'single_target_key1', 'single_target_key2'] template='\\n{company_summary}\\nOne of the targets {company_name} has in {single_target_key1} is {single_target_key2}.\\nContext about {single_target_key2}:\\n{single_target_context}\\nCreate bullet points on pain points that {single_target_key2} faces that they\\ncan help solve and also value propositions that {company_name} would provide to {single_target_key2}\\nRegardless of the pain points that {single_target_key2} faces, include only information that is factually correct based off the products and services that {company_name} provides\\n'\n", "2024-06-24 02:24:56,696 INFO: HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2024-06-24 02:24:56,704 INFO: build value prop for Fortune 500-Chewy\n", "2024-06-24 02:24:56,745 ERROR: Rebuilding object: TargetInfo object (59491)\n", "2024-06-24 02:24:57,021 INFO: Loading data from url: https://comcast.com\n", "2024-06-24 02:24:57,024 INFO: Cached data found for: https://comcast.com\n", "2024-06-24 02:24:57,106 ERROR: Failed to load data from url: https://comcast.com due to error: \n", "Error 500 when crawling https://comcast.com\n", "2024-06-24 02:24:57,265 ERROR: 200\n", "2024-06-24 02:24:57,268 ERROR: {}\n", "2024-06-24 02:24:58,278 ERROR: Rebuilding object: TargetInfo object (89136)\n", "2024-06-24 02:24:58,472 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:24:58,475 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:24:58,564 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:24:58,741 ERROR: 200\n", "2024-06-24 02:24:58,751 ERROR: {}\n", "2024-06-24 02:24:59,233 ERROR: Rebuilding object: TargetInfo object (60541)\n", "2024-06-24 02:24:59,634 INFO: Loading data from url: https://petsmart.com\n", "2024-06-24 02:24:59,637 INFO: Cached data found for: https://petsmart.com\n", "2024-06-24 02:24:59,721 ERROR: Failed to load data from url: https://petsmart.com due to error: \n", "Error 500 when crawling https://petsmart.com\n", "2024-06-24 02:24:59,881 ERROR: 200\n", "2024-06-24 02:24:59,895 ERROR: {}\n", "2024-06-24 02:25:00,527 ERROR: Rebuilding object: TargetInfo object (58552)\n", "2024-06-24 02:25:00,851 INFO: Loading data from url: https://petsmart.com\n", "2024-06-24 02:25:00,860 INFO: Cached data found for: https://petsmart.com\n", "2024-06-24 02:25:00,950 ERROR: Failed to load data from url: https://petsmart.com due to error: \n", "Error 500 when crawling https://petsmart.com\n", "2024-06-24 02:25:01,107 ERROR: 404\n", "2024-06-24 02:25:01,117 ERROR: {'error': 'value not found for key playbook_238_target_status'}\n", "2024-06-24 02:25:01,390 ERROR: Rebuilding object: TargetInfo object (69741)\n", "2024-06-24 02:25:01,652 INFO: Loading data from url: https://delta.com\n", "2024-06-24 02:25:01,655 INFO: Cached data found for: https://delta.com\n", "2024-06-24 02:25:01,738 ERROR: Failed to load data from url: https://delta.com due to error: \n", "Error 500 when crawling https://delta.com\n", "2024-06-24 02:25:01,897 ERROR: 200\n", "2024-06-24 02:25:01,909 ERROR: {}\n", "2024-06-24 02:25:02,358 ERROR: Rebuilding object: TargetInfo object (69849)\n", "2024-06-24 02:25:02,635 INFO: Loading data from url: https://petsmart.com\n", "2024-06-24 02:25:02,637 INFO: Cached data found for: https://petsmart.com\n", "2024-06-24 02:25:02,718 ERROR: Failed to load data from url: https://petsmart.com due to error: \n", "Error 500 when crawling https://petsmart.com\n", "2024-06-24 02:25:02,866 ERROR: 404\n", "2024-06-24 02:25:02,876 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:03,152 ERROR: Rebuilding object: TargetInfo object (64150)\n", "2024-06-24 02:25:03,429 INFO: Loading data from url: https://indeed.com\n", "2024-06-24 02:25:03,431 INFO: Cached data found for: https://indeed.com\n", "2024-06-24 02:25:03,513 ERROR: Failed to load data from url: https://indeed.com due to error: \n", "Error 500 when crawling https://indeed.com\n", "2024-06-24 02:25:03,671 ERROR: 404\n", "2024-06-24 02:25:03,678 ERROR: {'error': 'value not found for key playbook_597_target_status'}\n", "2024-06-24 02:25:04,231 ERROR: Rebuilding object: TargetInfo object (69903)\n", "2024-06-24 02:25:04,519 INFO: Loading data from url: https://aaanortheast.com\n", "2024-06-24 02:25:04,521 INFO: Cached data found for: https://aaanortheast.com\n", "2024-06-24 02:25:04,604 ERROR: Failed to load data from url: https://aaanortheast.com due to error: \n", "Error 500 when crawling https://aaanortheast.com\n", "2024-06-24 02:25:04,757 ERROR: 404\n", "2024-06-24 02:25:04,767 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:05,034 ERROR: Rebuilding object: TargetInfo object (69930)\n", "2024-06-24 02:25:05,294 INFO: Loading data from url: https://coxinc.com\n", "2024-06-24 02:25:05,300 INFO: Cached data found for: https://coxinc.com\n", "2024-06-24 02:25:05,380 ERROR: Failed to load data from url: https://coxinc.com due to error: \n", "Error 500 when crawling https://coxinc.com\n", "2024-06-24 02:25:05,552 ERROR: 404\n", "2024-06-24 02:25:05,568 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:05,830 ERROR: Rebuilding object: TargetInfo object (69925)\n", "2024-06-24 02:25:06,069 INFO: Loading data from url: https://usa.philips.com\n", "2024-06-24 02:25:06,070 INFO: Cached data found for: https://usa.philips.com\n", "2024-06-24 02:25:06,148 ERROR: Failed to load data from url: https://usa.philips.com due to error: \n", "Error 500 when crawling https://usa.philips.com\n", "2024-06-24 02:25:06,293 ERROR: 404\n", "2024-06-24 02:25:06,304 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:06,563 ERROR: Rebuilding object: TargetInfo object (68151)\n", "2024-06-24 02:25:06,735 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:25:06,737 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:25:06,818 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:25:06,966 ERROR: 404\n", "2024-06-24 02:25:06,980 ERROR: {'error': 'value not found for key playbook_658_target_status'}\n", "2024-06-24 02:25:07,417 ERROR: Rebuilding object: TargetInfo object (69657)\n", "2024-06-24 02:25:07,744 INFO: Loading data from url: https://groupon.com\n", "2024-06-24 02:25:07,747 INFO: Cached data found for: https://groupon.com\n", "2024-06-24 02:25:07,831 ERROR: Failed to load data from url: https://groupon.com due to error: \n", "No content found in url https://groupon.com\n", "2024-06-24 02:25:07,972 ERROR: 404\n", "2024-06-24 02:25:07,984 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:08,270 ERROR: Rebuilding object: TargetInfo object (79722)\n", "2024-06-24 02:25:08,580 INFO: Loading data from url: https://ryanair.com\n", "2024-06-24 02:25:08,586 INFO: Cached data found for: https://ryanair.com\n", "2024-06-24 02:25:08,671 ERROR: Failed to load data from url: https://ryanair.com due to error: \n", "Error 500 when crawling https://ryanair.com\n", "2024-06-24 02:25:08,821 ERROR: 404\n", "2024-06-24 02:25:08,830 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:09,115 ERROR: Rebuilding object: TargetInfo object (63870)\n", "2024-06-24 02:25:09,387 INFO: Loading data from url: https://comcast.com\n", "2024-06-24 02:25:09,396 INFO: Cached data found for: https://comcast.com\n", "2024-06-24 02:25:09,479 ERROR: Failed to load data from url: https://comcast.com due to error: \n", "Error 500 when crawling https://comcast.com\n", "2024-06-24 02:25:09,628 ERROR: 404\n", "2024-06-24 02:25:09,639 ERROR: {'error': 'value not found for key playbook_597_target_status'}\n", "2024-06-24 02:25:09,901 ERROR: Rebuilding object: TargetInfo object (64061)\n", "2024-06-24 02:25:10,181 INFO: Loading data from url: https://groupon.com\n", "2024-06-24 02:25:10,186 INFO: Cached data found for: https://groupon.com\n", "2024-06-24 02:25:10,281 ERROR: Failed to load data from url: https://groupon.com due to error: \n", "No content found in url https://groupon.com\n", "2024-06-24 02:25:10,428 ERROR: 404\n", "2024-06-24 02:25:10,440 ERROR: {'error': 'value not found for key playbook_597_target_status'}\n", "2024-06-24 02:25:10,714 ERROR: Rebuilding object: TargetInfo object (66191)\n", "2024-06-24 02:25:10,986 INFO: Loading data from url: https://permobil.com\n", "2024-06-24 02:25:10,990 INFO: Cached data found for: https://permobil.com\n", "2024-06-24 02:25:11,074 ERROR: Failed to load data from url: https://permobil.com due to error: \n", "Error 500 when crawling https://permobil.com\n", "2024-06-24 02:25:11,222 ERROR: 404\n", "2024-06-24 02:25:11,232 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:11,501 ERROR: Rebuilding object: TargetInfo object (66222)\n", "2024-06-24 02:25:11,769 INFO: Loading data from url: https://us.411locals.com\n", "2024-06-24 02:25:11,772 INFO: Cached data found for: https://us.411locals.com\n", "2024-06-24 02:25:11,854 ERROR: Failed to load data from url: https://us.411locals.com due to error: \n", "Error 500 when crawling https://us.411locals.com\n", "2024-06-24 02:25:12,006 ERROR: 404\n", "2024-06-24 02:25:12,016 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:12,276 ERROR: Rebuilding object: TargetInfo object (66226)\n", "2024-06-24 02:25:12,564 INFO: Loading data from url: https://pacificsunwear.com\n", "2024-06-24 02:25:12,567 INFO: Cached data found for: https://pacificsunwear.com\n", "2024-06-24 02:25:12,687 ERROR: Failed to load data from url: https://pacificsunwear.com due to error: \n", "Error 500 when crawling https://pacificsunwear.com\n", "2024-06-24 02:25:12,840 ERROR: 404\n", "2024-06-24 02:25:12,848 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:13,433 ERROR: Rebuilding object: TargetInfo object (66239)\n", "2024-06-24 02:25:13,771 INFO: Loading data from url: https://flatworldsolutions.com.ph\n", "2024-06-24 02:25:13,774 INFO: Cached data found for: https://flatworldsolutions.com.ph\n", "2024-06-24 02:25:13,859 ERROR: Failed to load data from url: https://flatworldsolutions.com.ph due to error: \n", "Error 500 when crawling https://flatworldsolutions.com.ph\n", "2024-06-24 02:25:14,013 ERROR: 404\n", "2024-06-24 02:25:14,027 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:14,279 ERROR: Rebuilding object: TargetInfo object (69719)\n", "2024-06-24 02:25:14,544 INFO: Loading data from url: https://indeed.com\n", "2024-06-24 02:25:14,547 INFO: Cached data found for: https://indeed.com\n", "2024-06-24 02:25:14,637 ERROR: Failed to load data from url: https://indeed.com due to error: \n", "Error 500 when crawling https://indeed.com\n", "2024-06-24 02:25:14,790 ERROR: 404\n", "2024-06-24 02:25:14,800 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:15,060 ERROR: Rebuilding object: TargetInfo object (69717)\n", "2024-06-24 02:25:15,323 INFO: Loading data from url: https://pgecorp.com\n", "2024-06-24 02:25:15,325 INFO: Cached data found for: https://pgecorp.com\n", "2024-06-24 02:25:15,406 ERROR: Failed to load data from url: https://pgecorp.com due to error: \n", "Error 500 when crawling https://pgecorp.com\n", "2024-06-24 02:25:15,584 ERROR: 404\n", "2024-06-24 02:25:15,598 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:15,857 ERROR: Rebuilding object: TargetInfo object (69768)\n", "2024-06-24 02:25:16,133 INFO: Loading data from url: https://city-furniture.com\n", "2024-06-24 02:25:16,138 INFO: Cached data found for: https://city-furniture.com\n", "2024-06-24 02:25:16,221 ERROR: Failed to load data from url: https://city-furniture.com due to error: \n", "Error 500 when crawling https://city-furniture.com\n", "2024-06-24 02:25:16,380 ERROR: 404\n", "2024-06-24 02:25:16,386 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:16,634 ERROR: Rebuilding object: TargetInfo object (69911)\n", "2024-06-24 02:25:16,907 INFO: Loading data from url: https://comcast.com\n", "2024-06-24 02:25:16,912 INFO: Cached data found for: https://comcast.com\n", "2024-06-24 02:25:16,993 ERROR: Failed to load data from url: https://comcast.com due to error: \n", "Error 500 when crawling https://comcast.com\n", "2024-06-24 02:25:17,143 ERROR: 404\n", "2024-06-24 02:25:17,154 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:17,415 ERROR: Rebuilding object: TargetInfo object (69927)\n", "2024-06-24 02:25:17,680 INFO: Loading data from url: https://jacksonhewitt.com\n", "2024-06-24 02:25:17,682 INFO: Cached data found for: https://jacksonhewitt.com\n", "2024-06-24 02:25:17,800 ERROR: Failed to load data from url: https://jacksonhewitt.com due to error: \n", "No content found in url https://jacksonhewitt.com\n", "2024-06-24 02:25:17,968 ERROR: 404\n", "2024-06-24 02:25:17,978 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:18,269 ERROR: Rebuilding object: TargetInfo object (69928)\n", "2024-06-24 02:25:18,552 INFO: Loading data from url: https://bcbsa.com\n", "2024-06-24 02:25:18,556 INFO: Cached data found for: https://bcbsa.com\n", "2024-06-24 02:25:18,641 ERROR: Failed to load data from url: https://bcbsa.com due to error: \n", "Error 500 when crawling https://bcbsa.com\n", "2024-06-24 02:25:18,834 ERROR: 404\n", "2024-06-24 02:25:18,844 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:19,155 ERROR: Rebuilding object: TargetInfo object (69934)\n", "2024-06-24 02:25:19,434 INFO: Loading data from url: https://usg.com\n", "2024-06-24 02:25:19,437 INFO: Cached data found for: https://usg.com\n", "2024-06-24 02:25:19,517 ERROR: Failed to load data from url: https://usg.com due to error: \n", "Error 500 when crawling https://usg.com\n", "2024-06-24 02:25:19,658 ERROR: 404\n", "2024-06-24 02:25:19,669 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:19,942 ERROR: Rebuilding object: TargetInfo object (69631)\n", "2024-06-24 02:25:20,217 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:25:20,220 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:25:20,302 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:25:20,448 ERROR: 404\n", "2024-06-24 02:25:20,459 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:20,720 ERROR: Rebuilding object: TargetInfo object (72462)\n", "2024-06-24 02:25:20,890 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:25:20,894 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:25:20,976 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:25:21,130 ERROR: 404\n", "2024-06-24 02:25:21,139 ERROR: {'error': 'value not found for key playbook_708_target_status'}\n", "2024-06-24 02:25:21,719 ERROR: Rebuilding object: TargetInfo object (72499)\n", "2024-06-24 02:25:21,894 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:25:21,896 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:25:21,977 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:25:22,131 ERROR: 404\n", "2024-06-24 02:25:22,140 ERROR: {'error': 'value not found for key playbook_709_target_status'}\n", "2024-06-24 02:25:22,852 ERROR: Rebuilding object: TargetInfo object (86896)\n", "2024-06-24 02:25:23,319 INFO: Loading data from url: https://groupon.com\n", "2024-06-24 02:25:23,322 INFO: Cached data found for: https://groupon.com\n", "2024-06-24 02:25:23,438 ERROR: Failed to load data from url: https://groupon.com due to error: \n", "No content found in url https://groupon.com\n", "2024-06-24 02:25:23,617 ERROR: 200\n", "2024-06-24 02:25:23,628 ERROR: {}\n", "2024-06-24 02:25:24,530 ERROR: Rebuilding object: TargetInfo object (69697)\n", "2024-06-24 02:25:24,868 INFO: Loading data from url: https://evicore.com\n", "2024-06-24 02:25:24,870 INFO: Cached data found for: https://evicore.com\n", "2024-06-24 02:25:24,952 ERROR: Failed to load data from url: https://evicore.com due to error: \n", "Error 500 when crawling https://evicore.com\n", "2024-06-24 02:25:25,116 ERROR: 404\n", "2024-06-24 02:25:25,126 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:25,388 ERROR: Rebuilding object: TargetInfo object (69618)\n", "2024-06-24 02:25:25,651 INFO: Loading data from url: https://estee.com\n", "2024-06-24 02:25:25,654 INFO: Cached data found for: https://estee.com\n", "2024-06-24 02:25:25,736 ERROR: Failed to load data from url: https://estee.com due to error: \n", "Error 500 when crawling https://estee.com\n", "2024-06-24 02:25:25,891 ERROR: 404\n", "2024-06-24 02:25:25,897 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:26,150 ERROR: Rebuilding object: TargetInfo object (70293)\n", "2024-06-24 02:25:26,427 INFO: Loading data from url: https://delta.com\n", "2024-06-24 02:25:26,433 INFO: Cached data found for: https://delta.com\n", "2024-06-24 02:25:26,517 ERROR: Failed to load data from url: https://delta.com due to error: \n", "Error 500 when crawling https://delta.com\n", "2024-06-24 02:25:26,692 ERROR: 404\n", "2024-06-24 02:25:26,701 ERROR: {'error': 'value not found for key playbook_565_target_status'}\n", "2024-06-24 02:25:26,984 INFO: build value_prop for target 70293: Segment_Accounts_20240510_CRWD Strike Tech Minus Customers Showing Patch Intent_6SENSEID.csv-Delta Air Lines\n", "2024-06-24 02:25:27,042 INFO: debug: the prompt we return here is: input_variables=['company_name', 'company_summary', 'single_target_context', 'single_target_key1', 'single_target_key2'] template='\\n{company_summary}\\nOne of the targets {company_name} has in {single_target_key1} is {single_target_key2}.\\nContext about {single_target_key2}:\\n{single_target_context}\\nCreate bullet points on pain points that {single_target_key2} faces that they\\ncan help solve and also value propositions that {company_name} would provide to {single_target_key2}\\nRegardless of the pain points that {single_target_key2} faces, include only information that is factually correct based off the products and services that {company_name} provides\\n'\n", "2024-06-24 02:25:34,790 INFO: HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2024-06-24 02:25:34,797 INFO: build value prop for Segment_Accounts_20240510_CRWD Strike Tech Minus Customers Showing Patch Intent_6SENSEID.csv-Delta Air Lines\n", "2024-06-24 02:25:34,834 ERROR: Rebuilding object: TargetInfo object (70414)\n", "2024-06-24 02:25:35,247 INFO: Loading data from url: https://petsmart.com\n", "2024-06-24 02:25:35,253 INFO: Cached data found for: https://petsmart.com\n", "2024-06-24 02:25:35,432 ERROR: Failed to load data from url: https://petsmart.com due to error: \n", "Error 500 when crawling https://petsmart.com\n", "2024-06-24 02:25:35,615 ERROR: 404\n", "2024-06-24 02:25:35,631 ERROR: {'error': 'value not found for key playbook_565_target_status'}\n", "2024-06-24 02:25:35,863 INFO: build value_prop for target 70414: Segment_Accounts_20240510_CRWD Strike Tech Minus Customers Showing Patch Intent_6SENSEID.csv-PetSmart, Inc.\n", "2024-06-24 02:25:35,927 INFO: debug: the prompt we return here is: input_variables=['company_name', 'company_summary', 'single_target_context', 'single_target_key1', 'single_target_key2'] template='\\n{company_summary}\\nOne of the targets {company_name} has in {single_target_key1} is {single_target_key2}.\\nContext about {single_target_key2}:\\n{single_target_context}\\nCreate bullet points on pain points that {single_target_key2} faces that they\\ncan help solve and also value propositions that {company_name} would provide to {single_target_key2}\\nRegardless of the pain points that {single_target_key2} faces, include only information that is factually correct based off the products and services that {company_name} provides\\n'\n", "2024-06-24 02:25:44,331 INFO: HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2024-06-24 02:25:44,336 INFO: build value prop for Segment_Accounts_20240510_CRWD Strike Tech Minus Customers Showing Patch Intent_6SENSEID.csv-PetSmart, Inc.\n", "2024-06-24 02:25:44,491 ERROR: Rebuilding object: TargetInfo object (75083)\n", "2024-06-24 02:25:45,859 INFO: Loading data from url: https://deltafaucet.com\n", "2024-06-24 02:25:45,868 INFO: Cached data found for: https://deltafaucet.com\n", "2024-06-24 02:25:45,955 ERROR: Failed to load data from url: https://deltafaucet.com due to error: \n", "No content found in url https://deltafaucet.com\n", "2024-06-24 02:25:46,112 ERROR: 404\n", "2024-06-24 02:25:46,118 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:46,371 ERROR: Rebuilding object: TargetInfo object (79691)\n", "2024-06-24 02:25:46,743 INFO: Loading data from url: https://www.chewy.com\n", "2024-06-24 02:25:46,746 INFO: Cached data found for: https://www.chewy.com\n", "2024-06-24 02:25:46,834 ERROR: Failed to load data from url: https://www.chewy.com due to error: \n", "Error 500 when crawling https://www.chewy.com\n", "2024-06-24 02:25:46,986 ERROR: 404\n", "2024-06-24 02:25:46,997 ERROR: {'error': 'value not found for key playbook_619_target_status'}\n", "2024-06-24 02:25:47,262 ERROR: Rebuilding object: TargetInfo object (90327)\n", "2024-06-24 02:25:47,440 INFO: Loading data from url: https://www.chewy.com\n", "2024-06-24 02:25:47,444 INFO: Cached data found for: https://www.chewy.com\n", "2024-06-24 02:25:47,525 ERROR: Failed to load data from url: https://www.chewy.com due to error: \n", "Error 500 when crawling https://www.chewy.com\n", "2024-06-24 02:25:47,675 ERROR: 200\n", "2024-06-24 02:25:47,685 ERROR: {}\n", "2024-06-24 02:25:48,453 ERROR: Rebuilding object: TargetInfo object (80894)\n", "2024-06-24 02:25:48,672 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:25:48,675 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:25:48,757 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:25:48,941 ERROR: 200\n", "2024-06-24 02:25:48,955 ERROR: {}\n", "2024-06-24 02:25:49,481 ERROR: Rebuilding object: TargetInfo object (87029)\n", "2024-06-24 02:25:49,978 INFO: Loading data from url: https://prudential.com\n", "2024-06-24 02:25:49,981 INFO: Cached data found for: https://prudential.com\n", "2024-06-24 02:25:50,074 ERROR: Failed to load data from url: https://prudential.com due to error: \n", "Error 500 when crawling https://prudential.com\n", "2024-06-24 02:25:50,238 ERROR: 404\n", "2024-06-24 02:25:50,246 ERROR: {'error': 'value not found for key playbook_565_target_status'}\n"]}], "source": ["import os\n", "import sys\n", "\n", "\n", "# Start of django setup block\n", "import django\n", "cur_dir = os.getcwd()\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "os.chdir(cur_dir)\n", "# End of django setup block\n", "\n", "from crawl_utils import rebuild_objects_for_urls\n", "from api.data_loaders.url_handler import UrlHandler\n", "\n", "\n", "redirect_map = {'deltafaucet.com': 'https://www.deltafaucetcompany.com/', 'permobil.com': 'https://www.permobil.com', 'www.chewy.com': 'https://www.chewy.com/app/content/about-us', 'bcbsa.com': 'http://www.bcbsa.com', 'aaanortheast.com': 'https://northeast.aaa.com/', 'pacificsunwear.com': 'https://www.pacificsunwear.com', 'usa.philips.com': 'https://www.usa.philips.com', 'indeed.com': 'http://www.indeed.com', 'prudential.com': 'https://www.prudential.com', 'pgecorp.com': 'https://www.pgecorp.com', 'groupon.com': 'https://www.groupon.com/articles/about', 'us.411locals.com': 'https://411locals.com/company/', 'city-furniture.com': 'https://www.cityfurniture.com/company/about-us', 'flatworldsolutions.com.ph': 'http://www.flatworldsolutions.com.ph', 'coxinc.com': 'https://www.coxenterprises.com/about-us', 'jacksonhewitt.com': 'https://www.jacksonhewitt.com/', 'delta.com': 'https://www.delta.com', 'evicore.com': 'https://www.evicore.com', 'ryanair.com': 'https://www.ryanair.com', 'comcast.com': 'https://www.comcast.com', 'usg.com': 'https://www.usg.com', 'petsmart.com': 'https://www.petsmart.com', 'estee.com': 'https://www.elcompanies.com/en'}\n", "\n", "\n", "for orig, dest in redirect_map.items():\n", "    wrapper = UrlHandler(orig)\n", "    wrapper.set_redirect_urls([dest])\n", "\n", "rebuild_objects_for_urls(list(redirect_map.keys()), mock=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "25b00a80-4df9-4592-95f5-4def02a1359b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}