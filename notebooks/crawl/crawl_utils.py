import logging

from api.models import AssetInfo, CompanyInfo, TargetInfo, UrlRecord
from api.playbook_build.object_builder import ObjectBuilder


def get_all_object():
    objects = []
    all_assets = AssetInfo.objects.all()
    all_targets = TargetInfo.objects.all()
    all_companys = CompanyInfo.objects.all()

    objects.extend(all_assets)
    objects.extend(all_targets)
    objects.extend(all_companys)

    return objects


def get_all_object_urls():
    objects = get_all_object()

    all_urls = set()
    for aobject in objects:
        docs = aobject.docs
        for doc in docs.values():
            if doc.get("type") == "url":
                value = doc.get("value")
                if value:
                    all_urls.add(value)

    all_urls = list(all_urls)

    return all_urls


def get_all_objects_given_playbook(playbook_ids):
    objects = []

    target_infos = TargetInfo.objects.filter(
        target_info_group__playbook_id__in=playbook_ids
    )
    asset_infos = AssetInfo.objects.filter(
        asset_info_group__playbook_id__in=playbook_ids
    )
    company_infos = CompanyInfo.objects.filter(playbook__id__in=playbook_ids)

    logging.error(
        f"counts: {target_infos.count()} {asset_infos.count()} {company_infos.count()}"
    )

    objects.extend(target_infos)
    objects.extend(asset_infos)
    objects.extend(company_infos)

    return objects


def get_all_urls_given_playbook(playbook_ids):
    objects = get_all_objects_given_playbook(playbook_ids)

    all_urls = set()
    for aobject in objects:
        docs = aobject.docs
        for doc in docs.values():
            if doc.get("type") == "url":
                value = doc.get("value")
                if value:
                    all_urls.add(value)

    all_urls = list(all_urls)

    return all_urls


def get_objects_for_url(url):
    objects = get_all_object()
    objects_for_url = []
    for aobject in objects:
        docs = aobject.docs
        for doc in docs.values():
            if doc.get("type") == "url":
                value = doc.get("value")
                if value == url:
                    objects_for_url.append(aobject)
                    break
    return objects_for_url


def rebuild_objects_for_urls(urls, mock=True):
    if mock:
        logging.error(f"This is mocking flow for rebuild objects for urls: {urls}")

    all_objects = get_all_object()
    for object in all_objects:
        docs = object.docs
        for doc in docs.values():
            if doc.get("type") == "url":
                value = doc.get("value")
                if value in urls:
                    logging.error(f"Rebuilding object: {object}")
                    if not mock:
                        builder = ObjectBuilder.get_builder(object)
                        builder.build_docs(rebuild=True)


def rebuild_objects_for_url(url, mock=True):
    rebuild_objects_for_urls([url], mock=mock)
