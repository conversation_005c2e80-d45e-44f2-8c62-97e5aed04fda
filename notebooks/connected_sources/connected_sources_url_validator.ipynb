{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading secrets from AWS Secrets Manager: tofu-backend-local-secrets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "/Users/<USER>/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_arn\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-04-24 23:44:44,514 INFO: found organization id: tofu-ykka\n", "2025-04-24 23:44:44,706 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}], "source": ["import os\n", "import sys\n", "\n", "\n", "# Start of django setup block\n", "import django\n", "cur_dir = os.getcwd()\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "os.chdir(cur_dir)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-24 23:45:04,220 INFO: Deep crawling for website https://dragonboat.io/events\n", "2025-04-24 23:45:06,323 INFO: Adding https://dragonboat.io/events/ to the queue\n", "2025-04-24 23:45:06,324 INFO: Adding https://dragonboat.io/events/demystifying-the-world-of-chief-product-officer/ to the queue\n", "2025-04-24 23:45:06,324 INFO: Adding https://dragonboat.io/events/?sf_paged=6 to the queue\n", "2025-04-24 23:45:06,325 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,326 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,327 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,327 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,327 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,328 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,328 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,328 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,328 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,328 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,329 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,329 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,329 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,329 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,329 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,329 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,330 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,330 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,330 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,330 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,330 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:06,331 WARNING: Max page limit reached for https://dragonboat.io/events: 5\n", "2025-04-24 23:45:08,573 INFO: 4 are parsed for input url: https://dragonboat.io/events\n", "2025-04-24 23:45:11,143 INFO: HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-04-24 23:45:11,176 INFO: Generation complete: {'token_usage': {'completion_tokens': 59, 'prompt_tokens': 8570, 'total_tokens': 8629, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_129a36352a'}\n", "URL: https://dragonboat.io/events, Result: url='https://dragonboat.io/events' error_message=\"Looks like might be a webinar page—we can't download the videos. If you'd still like to use it in Tofu, we recommend manually uploading it in an assets list.\" status=<ValidationStatus.WARNING: 'warning'>\n"]}], "source": ["from api.connected_assets.connected_assets_url_validator import ConnectedAssetsUrlValidator\n", "\n", "urls = [\n", "    # \"www.google.com\",\n", "    # \"https://www.gong.io/blog/\",\n", "    # \"https://www.gong.io/podcast/\",\n", "    # \"https://www.tofuhq.com/blog/\",\n", "    # \"https://www.tofuhq.com/\",\n", "    # \"https://napta.io/webinar\",\n", "    \"https://dragonboat.io/events\",\n", "    # \"https://www.napta.io/en/guide\",\n", "\n", "]\n", "\n", "for url in urls:\n", "    url_validator = ConnectedAssetsUrlValidator(url, cache_enabled=False)\n", "    result = url_validator.validate()\n", "    print(f\"URL: {url}, Result: {result}\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}