{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading secrets from AWS Secrets Manager: tofu-backend-local-secrets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "/Users/<USER>/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_arn\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-05-01 19:57:25,875 INFO: found organization id: tofu-ykka\n", "2025-05-01 19:57:33,499 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}], "source": ["import os\n", "import sys\n", "\n", "\n", "# Start of django setup block\n", "import django\n", "cur_dir = os.getcwd()\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "os.chdir(cur_dir)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from typing import Dict, List, Tuple\n", "from api.models import AssetInfoGroup, Playbook, AssetInfo\n", "from api.connected_assets.connected_assets_utils import normalize_url"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def get_url_from_asset_info_group(asset_info_group) -> str:\n", "    \"\"\"Extract and normalize URL from asset info group metadata.\"\"\"\n", "    meta = getattr(asset_info_group, 'meta', {})\n", "    if not meta:\n", "        return None\n", "        \n", "    connected_assets_metadata = meta.get('tofu_connected_assets_metadata', {})\n", "    if not connected_assets_metadata:\n", "        return None\n", "        \n", "    type_info = connected_assets_metadata.get('tofu_connected_source_type_info', {})\n", "    if not type_info:\n", "        return None\n", "        \n", "    urls = type_info.get('urls', [])\n", "    if not urls:\n", "        return None\n", "        \n", "    # Take the first URL and normalize it\n", "    return normalize_url(urls[0])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def is_connected_sources(asset_info_group) -> bool:\n", "    \"\"\"Check if an asset info group is a connected source.\"\"\"\n", "    asset_infos = AssetInfo.objects.filter(asset_info_group=asset_info_group)\n", "    if not asset_infos:\n", "        return False\n", "\n", "    meta = getattr(asset_info_group, 'meta', {})\n", "    if not meta:\n", "        return False\n", "        \n", "    connected_assets_metadata = meta.get('tofu_connected_assets_metadata', {})\n", "    if not connected_assets_metadata:\n", "        return False\n", "        \n", "    return (\n", "        connected_assets_metadata.get('tofu_suggested_connected_source', False) is False\n", "        and connected_assets_metadata.get('tofu_connected_source', False) is True\n", "        and connected_assets_metadata.get('last_synced_at') is not None\n", "    )\n", "\n", "def is_suggested_connected_sources(asset_info_group) -> bool:\n", "    \"\"\"Check if an asset info group is a suggested connected source.\"\"\"\n", "    asset_infos = AssetInfo.objects.filter(asset_info_group=asset_info_group)\n", "    if not asset_infos:\n", "        return False\n", "\n", "    meta = getattr(asset_info_group, 'meta', {})\n", "    if not meta:\n", "        return False\n", "        \n", "    connected_assets_metadata = meta.get('tofu_connected_assets_metadata', {})\n", "    if not connected_assets_metadata:\n", "        return False\n", "        \n", "    return (\n", "        connected_assets_metadata.get('tofu_suggested_connected_source', False) is True\n", "        and connected_assets_metadata.get('tofu_connected_source', False) is True\n", "        and connected_assets_metadata.get('last_synced_at') is None\n", "    )\n", "\n", "def find_duplicate_connected_sources(playbook_id: int) -> List[Tuple[AssetInfoGroup, AssetInfoGroup]]:\n", "    \"\"\"Find pairs of asset info groups that share the same normalized URL where one is connected\n", "    and one is suggested.\n", "    \n", "    Args:\n", "        playbook_id: The ID of the playbook to check\n", "        \n", "    Returns:\n", "        List of tuples containing (connected_group, suggested_group) pairs\n", "    \"\"\"\n", "    # Get all asset info groups for the playbook\n", "    asset_info_groups = AssetInfoGroup.objects.filter(playbook_id=playbook_id)\n", "    \n", "    # Create dictionaries to store groups by normalized URL\n", "    connected_sources = {}\n", "    suggested_sources = {}\n", "    \n", "    # Categorize groups by their normalized URLs\n", "    for group in asset_info_groups:\n", "        normalized_url = get_url_from_asset_info_group(group)\n", "        if not normalized_url:\n", "            continue\n", "            \n", "        if is_connected_sources(group):\n", "            connected_sources[normalized_url] = group\n", "        elif is_suggested_connected_sources(group):\n", "            suggested_sources[normalized_url] = group\n", "    \n", "    # Find duplicates where URL exists in both dictionaries\n", "    duplicates = []\n", "    for url in connected_sources.keys():\n", "        if url in suggested_sources:\n", "            duplicates.append((connected_sources[url], suggested_sources[url]))\n", "    \n", "    return duplicates"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 0 duplicate pairs:\n"]}], "source": ["playbook_id = 13902\n", "duplicates = find_duplicate_connected_sources(playbook_id)\n", "\n", "print(f\"Found {len(duplicates)} duplicate pairs:\")\n", "for connected, suggested in duplicates:\n", "    print(f\"\\nConnected source: {connected.id}\")\n", "    print(f\"Suggested source: {suggested.id}\")\n", "    print(f\"Normalized URL: {get_url_from_asset_info_group(connected)}\")\n", "    duplicated_suggested_source = AssetInfoGroup.objects.filter(\n", "        id=suggested.id,\n", "        playbook_id=playbook_id,\n", "    ).first()\n", "    # duplicated_suggested_source.delete()\n", "    \n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}