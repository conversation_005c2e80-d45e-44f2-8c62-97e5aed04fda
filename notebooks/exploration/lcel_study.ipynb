{"cells": [{"cell_type": "code", "execution_count": 2, "id": "88411a93-6783-42a7-9998-9cc2e54696ad", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-02-27 21:07:52,319 INFO: found organization id: tofu-ykka\n"]}], "source": ["## data comparison\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a helpful assistant. Please respond to the user's request only based on the given context.\"),\n", "    (\"user\", \"Question: {question}\\nContext: {context}\")\n", "])\n", "model = ChatOpenAI(model=\"gpt-3.5-turbo\")\n", "output_parser = StrOutputParser()\n", "\n", "chain = prompt | model | output_parser\n"]}, {"cell_type": "code", "execution_count": 3, "id": "32e27d2e-7ff5-4712-985a-789a40c4ba40", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['context', 'question'], messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], template=\"You are a helpful assistant. Please respond to the user's request only based on the given context.\")), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], template='Question: {question}\\nContext: {context}'))])\n", "| ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x2b5787310>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x2b63078d0>, openai_api_key='***************************************************', openai_proxy='')\n", "| StrOutputParser()"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["chain"]}, {"cell_type": "code", "execution_count": 4, "id": "b3a7a35c-2caf-425b-8064-54dc5340cb52", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.runnables.base.RunnableSequence"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["type(chain)"]}, {"cell_type": "code", "execution_count": 8, "id": "6c67707c-3df7-45aa-b638-a848ec3ef6eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["StrOutputParser()"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.last"]}, {"cell_type": "code", "execution_count": 9, "id": "7f5d0a58-b355-4905-b0a3-0751e8711783", "metadata": {}, "outputs": [], "source": ["chain2 = prompt | model"]}, {"cell_type": "code", "execution_count": 11, "id": "685438a7-1b56-4699-8904-7e65b8d723f8", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.runnables.base.RunnableSequence"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["type(chain2)"]}, {"cell_type": "code", "execution_count": 14, "id": "cc866d00-c853-4e2c-9521-5540d859f7a9", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x2b5787310>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x2b63078d0>, openai_api_key='***************************************************', openai_proxy='')"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["chain2.last"]}, {"cell_type": "code", "execution_count": 15, "id": "7af89a35-956b-42ba-b553-88aec0bac31e", "metadata": {}, "outputs": [], "source": ["chain3 = prompt | model | output_parser | output_parser\n"]}, {"cell_type": "code", "execution_count": 16, "id": "04d1dd38-0689-4cef-a6f2-a175a5a1fb7b", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.runnables.base.RunnableSequence"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["type(chain3)"]}, {"cell_type": "code", "execution_count": 19, "id": "4999d869-2fc6-4ba8-9413-9ad42e6a9a08", "metadata": {}, "outputs": [{"data": {"text/plain": ["StrOutputParser()"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["chain3.last"]}, {"cell_type": "code", "execution_count": 23, "id": "40fa8b17-03b6-40bc-8492-7114551c1ea3", "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "def add_one(x: int) -> int:\n", "    return x + 1\n", "\n", "def mul_two(x: int) -> int:\n", "    return x * 2\n", "\n", "runnable_1 = RunnableLambda(add_one)\n", "runnable_2 = RunnableLambda(mul_two)\n", "sequence = runnable_1 | runnable_2\n", "# Or equivalently:\n", "# sequence = RunnableSequence(first=runnable_1, last=runnable_2)\n", "sequence.invoke(2)\n", "# await sequence.ainvoke(1)"]}, {"cell_type": "code", "execution_count": null, "id": "66fcea67-66b6-4b6d-ad73-1e13b8bc239f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}