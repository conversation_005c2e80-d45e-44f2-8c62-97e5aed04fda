{"cells": [{"cell_type": "code", "execution_count": null, "id": "57551e01-88ac-4bfd-a4ee-90d0d7e21d95", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "from langsmith.schemas import Example, Run\n", "from langsmith.evaluation import evaluate\n", "\n", "from langsmith import Client\n", "from langsmith.evaluation import LangChainStringEvaluator, evaluate\n", "\n", "\n", "# from api.evaluator.llm_prompt_evaluator import LLMEvaluatorRunner\n", "from api.evaluator.evaluators.dataset_eval import evaluate_against_dataset\n", "\n", "evaluate_against_dataset()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}