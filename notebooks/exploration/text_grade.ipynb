{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b0446db2-1e69-4c23-9cfa-6fa113906b6f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(43.02, 12.2)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import textstat\n", "\n", "email_text = \"\"\"\n", "<PERSON>,\n", "\n", "I hope this email finds you well. We specialize in providing comprehensive services designed to accelerate your startup's growth and efficiency, including:\n", "\n", "Prospect Investor Lists\n", "B2B Lead Generation\n", "Data Entry\n", "Data Enrichment\n", "Prospect Email List Building\n", "Custom Targeted Contact List Building\n", "\n", "We understand the unique challenges that startups face in building valuable connections and maintaining high-quality data. Our expert team is dedicated to delivering tailored solutions that meet your specific needs, helping you to focus on what matters most—growing your business.\n", "\n", "Here are some ways our services can benefit your startup:\n", "\n", "Enhanced Investor Outreach: Access targeted prospect investor lists to secure funding and partnerships.\n", "Efficient Lead Generation: Identify and connect with potential clients and partners to drive sales and collaboration.\n", "Accurate Data Management: Ensure your data is precise and up-to-date, enabling informed decision-making.\n", "Customized Contact Lists: Receive highly targeted contact lists that align with your strategic goals.\n", "\n", "We pride ourselves on delivering results-driven services with a commitment to quality and client satisfaction. I would love the opportunity to discuss how Pro Lead Maker can support your startup's objectives and contribute to your success.\n", "\n", "Please let me know a convenient time for you to have a quick meeting. I look forward to the possibility of working together and helping your startup reach new heights.\n", "\n", "Best regards,\n", "\"\"\"\n", "\n", "# Calculate Flesch Reading Ease\n", "flesch_reading_ease = textstat.flesch_reading_ease(email_text)\n", "# Calculate Flesch-Kincaid Grade Level\n", "flesch_kincaid_grade = textstat.flesch_kincaid_grade(email_text)\n", "\n", "flesch_reading_ease, flesch_kincaid_grade\n"]}, {"cell_type": "code", "execution_count": 3, "id": "4d08dabe-122b-487b-bbe5-9b74e8530a19", "metadata": {}, "outputs": [{"data": {"text/plain": ["(32.631797945205506, '13')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from readability import Readability\n", "\n", "email_text = \"\"\"\n", "<PERSON>,\n", "\n", "I hope this email finds you well. We specialize in providing comprehensive services designed to accelerate your startup's growth and efficiency, including:\n", "\n", "Prospect Investor Lists\n", "B2B Lead Generation\n", "Data Entry\n", "Data Enrichment\n", "Prospect Email List Building\n", "Custom Targeted Contact List Building\n", "\n", "We understand the unique challenges that startups face in building valuable connections and maintaining high-quality data. Our expert team is dedicated to delivering tailored solutions that meet your specific needs, helping you to focus on what matters most—growing your business.\n", "\n", "Here are some ways our services can benefit your startup:\n", "\n", "Enhanced Investor Outreach: Access targeted prospect investor lists to secure funding and partnerships.\n", "Efficient Lead Generation: Identify and connect with potential clients and partners to drive sales and collaboration.\n", "Accurate Data Management: Ensure your data is precise and up-to-date, enabling informed decision-making.\n", "Customized Contact Lists: Receive highly targeted contact lists that align with your strategic goals.\n", "\n", "We pride ourselves on delivering results-driven services with a commitment to quality and client satisfaction. I would love the opportunity to discuss how Pro Lead Maker can support your startup's objectives and contribute to your success.\n", "\n", "Please let me know a convenient time for you to have a quick meeting. I look forward to the possibility of working together and helping your startup reach new heights.\n", "\n", "Best regards,\n", "\"\"\"\n", "\n", "r = Readability(email_text)\n", "\n", "# Calculate Flesch Reading Ease\n", "flesch_reading_ease = r.flesch().score\n", "# Calculate Flesch-Kincaid Grade Level\n", "flesch_kincaid_grade = r.flesch_kincaid().grade_level\n", "\n", "flesch_reading_ease, flesch_kincaid_grade\n"]}, {"cell_type": "code", "execution_count": null, "id": "6c331266-89dd-4e1c-911a-7efcf2bd65fc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}