{"cells": [{"cell_type": "code", "execution_count": null, "id": "193c64a7-914a-41e6-9da9-a7addea47e53", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.evaluator.llm_evaluator_runner import LLMEvaluatorRunner\n", "\n", "runner = (\n", "    LLMEvaluator<PERSON><PERSON>ner()\n", "    .set_content_groups(content_groups=[13725, 85552])\n", "    .set_settings(num_of_variations=1, foundation_model='gpt-4o')\n", "    .evaluate_rules(predefined_rules=[\"personalization\", \"personalization-info\"])\n", "    .set_add_to_dataset(True)\n", ")\n", "report = runner.evaluate()\n", "print(report)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}