{"cells": [{"cell_type": "code", "execution_count": null, "id": "193c64a7-914a-41e6-9da9-a7addea47e53", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "\n", "# instruction\n", "# Instructions:\n", "# This script is used to measure the probability of JSON parse failure.\n", "# Run this script and check the reports for the following metrics:\n", "#   - Total Content generated: 8\n", "#   - Success Cases: 6\n", "#   - Failure Cases: 2\n", "# This indicates a success rate of 6/8.\n", "# You can adjust `num_of_variations` and `num_of_rounds` to manipulate the robustness of measurements.\n", "\n", "from api.evaluator.llm_evaluator_runner import LLMEvaluatorRunner, list_content_ids_json_test\n", "\n", "runner = (\n", "    LLMEvaluator<PERSON><PERSON>ner()\n", "    .set_contents(contents=list_content_ids_json_test)\n", "    .set_settings(num_of_variations=2, num_of_rounds=1, foundation_model='gpt-4-0125-preview')\n", "    .set_no_evaluation()\n", ")\n", "report = runner.evaluate()\n", "print(report)"]}, {"cell_type": "code", "execution_count": null, "id": "d647ad59-6010-49aa-b4f1-7160de17c333", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}