{"cells": [{"cell_type": "code", "execution_count": null, "id": "6bf1ec8a-b5d3-47df-b74a-767be56d8138", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "# Instructions:\n", "# This script is used to evaluate the quality of generation.\n", "# Further instructions will be provided later.\n", "\n", "\n", "from api.evaluator.llm_evaluator_runner import LLMEvaluatorRunner, list_content_ids_json_test\n", "\n", "runner = (\n", "    LLMEvaluator<PERSON><PERSON>ner()\n", "    .set_content_groups(content_groups=[14451, 774])\n", "    # .set_contents(contents=[])\n", "    .set_settings(num_of_variations=1, num_of_rounds=1, foundation_model='anthropic.claude-3-5-sonnet-20240620-v1:0')\n", "    # by default it would run all predefined rules\n", "    # .evaluate_rules(predefined_rules=[\"personalization\"])\n", ")\n", "report = runner.evaluate()\n", "print(report)"]}, {"cell_type": "code", "execution_count": null, "id": "628c4879-b73f-4740-877b-1f0e63ed6f1c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}