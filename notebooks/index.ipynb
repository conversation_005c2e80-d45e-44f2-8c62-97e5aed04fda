{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test data loading\n", "\n", "# Add the following block to import and setup the django project\n", "import os\n", "import sys\n", "import django\n", "\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "from api.data_loaders.url_loader import TofuURLLoader\n", "\n", "loader = TofuURLLoader(\"https://probation.lacounty.gov/\")\n", "docs = loader.load()\n", "# print(docs[0].page_content[12400*5 : 12400*6])\n", "\n", "from api.playbook import PlaybookHandler\n", "\n", "handler = PlaybookHandler.load_from_db(playbook_id=2)\n", "summary = handler.create_summary(docs)\n", "\n", "# print(handler.playbook_instance.company_info)\n", "# print(handler.playbook_instance.company_info_expanded)\n", "# handler.update_context(['target_info'])\n", "# # handler.force_context_full_refresh(['company_info'])\n", "\n", "# handler = PlaybookHandler.load_from_db(playbook_id=2)\n", "# print(handler.playbook_instance.company_info)\n", "# print(handler.playbook_instance.company_info_expanded)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# extract deltas of two json objects\n", "\n", "import json\n", "\n", "\n", "def extract_deltas(original, new, path=[]):\n", "    deltas = {\n", "        \"data deleted\": [],\n", "        \"data added\": [],\n", "        \"data modified\": [],\n", "    }\n", "    for key, value in original.items():\n", "        if key == \"meta\":\n", "            continue\n", "        if isinstance(value, dict):\n", "            if \"data\" in value:\n", "                new_value = new.get(key, {}).get(\"data\", [])\n", "                check_data_changes(value[\"data\"], new_value, deltas, path + [key])\n", "            else:\n", "                if key in new:\n", "                    deltas = merge_deltas(\n", "                        deltas, extract_deltas(value, new[key], path + [key])\n", "                    )\n", "                else:\n", "                    for sub_key in value.keys():\n", "                        if sub_key != \"meta\":\n", "                            deltas[\"data deleted\"].append(path + [key, sub_key])\n", "        else:\n", "            if key not in new or new[key] != value:\n", "                deltas[\"data modified\"].append(path + [key])\n", "    for key, value in new.items():\n", "        if key not in original and key != \"meta\":\n", "            if isinstance(value, dict):\n", "                if \"data\" in value:\n", "                    check_data_changes([], value[\"data\"], deltas, path + [key])\n", "                else:\n", "                    for sub_key in value.keys():\n", "                        if sub_key != \"meta\":\n", "                            deltas[\"data added\"].append(path + [key, sub_key])\n", "    return deltas\n", "\n", "\n", "def check_data_changes(old_data_list, new_data_list, deltas, path):\n", "    old_data = {item[\"id\"]: item for item in old_data_list}\n", "    new_data = {item[\"id\"]: item for item in new_data_list}\n", "\n", "    for id_key, old_item in old_data.items():\n", "        if id_key not in new_data:\n", "            deltas[\"data deleted\"].append(path + [id_key])\n", "        elif old_item[\"value\"] != new_data[id_key][\"value\"]:\n", "            deltas[\"data modified\"].append(path + [id_key])\n", "\n", "    for id_key, new_item in new_data.items():\n", "        if id_key not in old_data:\n", "            deltas[\"data added\"].append(path + [id_key])\n", "\n", "\n", "def merge_deltas(d1, d2):\n", "    for key, value in d2.items():\n", "        d1[key].extend(value)\n", "    return d1\n", "\n", "\n", "original_json = {\n", "    \"meta\": {\"position\": 2},\n", "    \"Customer Segments\": {\n", "        \"Digital Health\": {\n", "            \"data\": [\n", "                {\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"},\n", "                {\"id\": \"uuid2\", \"type\": \"url\", \"value\": \"url\"},\n", "            ],\n", "            \"meta\": {\"position\": 1},\n", "        },\n", "        \"Labs\": {\n", "            \"data\": [\n", "                {\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"},\n", "                {\"id\": \"uuid2\", \"type\": \"url\", \"value\": \"url\"},\n", "            ],\n", "            \"meta\": {\"position\": 2},\n", "        },\n", "        \"meta\": {\"position\": 1},\n", "    },\n", "    \"Companies\": {\n", "        \"Nuvo\": {\n", "            \"data\": [\n", "                {\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"},\n", "                {\"id\": \"uuid2\", \"type\": \"url\", \"value\": \"url\"},\n", "            ],\n", "            \"meta\": {\"position\": 1},\n", "        },\n", "        \"Equip\": {\n", "            \"data\": [\n", "                {\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"},\n", "                {\"id\": \"uuid2\", \"type\": \"url\", \"value\": \"url\"},\n", "            ],\n", "            \"meta\": {\"position\": 2},\n", "        },\n", "    },\n", "}\n", "\n", "new_json = {\n", "    \"meta\": {\"position\": 2},\n", "    \"Customer Segments\": {\n", "        \"Digital Health\": {\n", "            \"data\": [{\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"}],\n", "            \"meta\": {\"position\": 1},\n", "        },\n", "        \"Labs1\": {\n", "            \"data\": [\n", "                {\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"},\n", "                {\"id\": \"uuid2\", \"type\": \"url\", \"value\": \"url\"},\n", "            ],\n", "            \"meta\": {\"position\": 2},\n", "        },\n", "        \"meta\": {\"position\": 1},\n", "    },\n", "    \"Companies\": {\n", "        \"Nuvo\": {\n", "            \"data\": [\n", "                {\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"},\n", "                {\"id\": \"uuid2\", \"type\": \"url\", \"value\": \"new url\"},\n", "            ],\n", "            \"meta\": {\"position\": 1},\n", "        },\n", "        \"Equip\": {\n", "            \"data\": [\n", "                {\"id\": \"uuid1\", \"type\": \"text\", \"value\": \"text description\"},\n", "                {\"id\": \"uuid2\", \"type\": \"url\", \"value\": \"url\"},\n", "                {\"id\": \"uuid3\", \"type\": \"url\", \"value\": \"url\"},\n", "            ],\n", "            \"meta\": {\"position\": 2},\n", "        },\n", "    },\n", "}\n", "\n", "deltas = extract_deltas(original_json, new_json)\n", "print(json.dumps(deltas, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# manage index\n", "\n", "import os\n", "import pinecone\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "# initialize pinecone\n", "pinecone.init(\n", "    api_key=os.environ.get(\"PINECONE_API_KEY\"),  # find at app.pinecone.io\n", "    environment=os.environ.get(\"PINECONE_ENV\"),  # next to api key in console\n", ")\n", "index_name = \"playbook\"\n", "\n", "# # create index\n", "# if index_name not in pinecone.list_indexes():\n", "#     # we create a new index\n", "#     pinecone.create_index(\n", "#         name=index_name,\n", "#         metric='dotproduct',\n", "#         dimension=1536, # dim of text-embedding-ada-002\n", "#         metadata_config = {\n", "#             \"indexed\": [\"playbook_id\", \"column_id\", \"key_ids\", \"data_id\"]\n", "#         }\n", "#     )\n", "\n", "# delete vectors based on namespace\n", "index = pinecone.Index(index_name)\n", "index.delete(\n", "    namespace=\"2\",\n", "    delete_all=True,\n", ")\n", "\n", "# # delete vectors based on metadata\n", "# index = pinecone.Index(index_name)\n", "# index.delete(\n", "#     namespace='2',\n", "#     filter={'$and': [{'column_id': {'$eq': 'company_info'}}, {'$and': [{'key_ids': {'$eq': 'Product Overview'}}]}, {'data_id': {'$eq': '913675e5-e65e-0610-ed7a-79ae0edfdc85'}}]},\n", "#     delete_all=False,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# build and update index for playbook\n", "\n", "# Add the following block to import and setup the django project\n", "import os\n", "import sys\n", "import django\n", "\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Playbook\n", "from langchain.text_splitter import (\n", "    RecursiveCharacterTextSplitter,\n", "    CharacterTextSplitter,\n", ")\n", "from langchain.docstore.document import Document\n", "from langchain.embeddings.openai import OpenAIEmbeddings\n", "from langchain.vectorstores import Pinecone\n", "from api.data_loaders.url_loader import TofuURLLoader\n", "import pinecone\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "playbook = Playbook.objects.get(id=13)\n", "documents = []\n", "for key, value in playbook.company_info.items():\n", "    if key in (\"meta\", \"Company Name\"):\n", "        continue\n", "\n", "    for data_entry in value.get(\"data\", []):\n", "        type = data_entry.get(\"type\", \"\")\n", "        metadata = {\n", "            \"playbook_id\": str(playbook.id),\n", "            \"column_id\": \"company_info\",\n", "            \"key_ids\": [key],\n", "            \"data_id\": data_entry.get(\"id\", \"\"),\n", "            \"data_type\": type,\n", "        }\n", "        if type == \"text\":\n", "            text_content = data_entry.get(\"value\", None)\n", "            if text_content and len(text_content) > 0:\n", "                documents.append(\n", "                    Document(\n", "                        page_content=text_content,\n", "                        metadata=metadata,\n", "                    )\n", "                )\n", "        elif type == \"url\":\n", "            url = data_entry.get(\"value\", None)\n", "            if url:\n", "                loader = TofuURLLoader(url)\n", "                loaded_docs = loader.load()\n", "                for doc in loaded_docs:\n", "                    # add the above meta to existing doc metadata\n", "                    doc.metadata.update(metadata)\n", "                    # remove all the None values from doc metadata (otherwise <PERSON><PERSON>e will complain)\n", "                    doc.metadata = {\n", "                        k: v for k, v in doc.metadata.items() if v is not None\n", "                    }\n", "                # combine documents and loaded_docs\n", "                documents.extend(loaded_docs)\n", "\n", "text_splitter = CharacterTextSplitter(\n", "    separator=\"\",  # setting this as empty separator to split text at character level\n", "    chunk_size=1000,  # 1k characters which roughly equal to 250 tokens\n", "    chunk_overlap=20,\n", ")\n", "chunks = text_splitter.split_documents(documents)\n", "# print(documents)\n", "print(chunks)\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-ada-002\")\n", "# initialize pinecone\n", "pinecone.init(\n", "    api_key=os.environ.get(\"PINECONE_API_KEY\"),  # find at app.pinecone.io\n", "    environment=os.environ.get(\"PINECONE_ENV\"),  # next to api key in console\n", ")\n", "index_name = \"playbook\"\n", "namespace = str(playbook.id)\n", "pinecone_index = Pinecone.from_documents(\n", "    documents=chunks,\n", "    embedding=embeddings,\n", "    text_key=\"text\",\n", "    index_name=index_name,\n", "    namespace=namespace,\n", ")\n", "print(\"index updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query index\n", "import os\n", "from langchain.vectorstores import Pinecone\n", "from langchain.embeddings.openai import OpenAIEmbeddings\n", "import pinecone\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "# initialize pinecone\n", "pinecone.init(\n", "    api_key=os.environ.get(\"PINECONE_API_KEY\"),  # find at app.pinecone.io\n", "    environment=os.environ.get(\"PINECONE_ENV\"),  # next to api key in console\n", ")\n", "index = pinecone.Index(\"playbook\")\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-ada-002\")\n", "pinecone_index = Pinecone(index, embeddings.embed_query, \"text\", namespace=\"2\")\n", "\n", "query = \"\"\"\n", "What are the key differientiators of the company?\n", "What are the value propositions of the company?\n", "What are the key products of the company?\n", "What are the key services of the company?\n", "What is the brand guideline of the company?\n", "\"\"\"\n", "docs = pinecone_index.similarity_search(\n", "    query=query,\n", "    k=2,\n", "    filter={\n", "        \"$and\": [\n", "            {\"column_id\": {\"$eq\": \"company_info\"}},\n", "            {\"$and\": []},\n", "        ]\n", "    },\n", ")\n", "print(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test context building\n", "\n", "# Add the following block to import and setup the django project\n", "import os\n", "import sys\n", "import django\n", "\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "from api.playbook import PlaybookHandler\n", "\n", "handler = PlaybookHandler.load_from_db(playbook_id=2)\n", "context = handler.get_target_context(\"<PERSON><PERSON>\", \"CEO\")\n", "\n", "print(context)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# delete index which are not in use\n", "\n", "# Add the following block to import and setup the django project\n", "import os\n", "import sys\n", "import django\n", "\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "from api.playbook import PlaybookHandler\n", "import pinecone\n", "\n", "index_name = \"playbook\"\n", "index = pinecone.Index(index_name)\n", "\n", "def delete_namespace(namespace):\n", "    try:\n", "        res = index.delete(\n", "            namespace=namespace,\n", "            delete_all=True,\n", "        )\n", "        print(f\"delete result: {res}\")\n", "    except Exception as e:\n", "        print(f\"Error: fail to delete namespace {namespace} due to {e}\")\n", "\n", "delete_namespace(\"286\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pinecone-client in /Users/<USER>/Documents/Projects/tofu/backend/tofu/venv/lib/python3.11/site-packages (3.0.1)\n", "Requirement already satisfied: certifi>=2019.11.17 in /Users/<USER>/Documents/Projects/tofu/backend/tofu/venv/lib/python3.11/site-packages (from pinecone-client) (2023.7.22)\n", "Requirement already satisfied: tqdm>=4.64.1 in /Users/<USER>/Documents/Projects/tofu/backend/tofu/venv/lib/python3.11/site-packages (from pinecone-client) (4.65.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4 in /Users/<USER>/Documents/Projects/tofu/backend/tofu/venv/lib/python3.11/site-packages (from pinecone-client) (4.7.1)\n", "Requirement already satisfied: urllib3>=1.26.0 in /Users/<USER>/Documents/Projects/tofu/backend/tofu/venv/lib/python3.11/site-packages (from pinecone-client) (1.26.16)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m23.1.2\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m23.3.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip install --upgrade pinecone-client"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n", "delete result: {}\n"]}], "source": ["api_key=\"\"\n", "\n", "\n", "from pinecone import Pinecone\n", "pc = Pinecone(api_key=api_key)\n", "\n", "index_name = \"playbook\"\n", "index = pc.Index(index_name)\n", "\n", "def delete_namespace(namespace):\n", "    try:\n", "        index.delete(\n", "            namespace=namespace,\n", "            delete_all=True,\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error: fail to delete namespace {namespace} due to {e}\")\n", "\n", "for i in range(1, 400):\n", "    # delete_namespace(f\"{i}\")\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}