{"cells": [{"cell_type": "code", "execution_count": null, "id": "3c6d8cad-cf26-4ecb-8a62-6bd0bb4d1981", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "\n", "# Create a null handler to disable error logs\n", "null_handler = logging.NullHandler()\n", "# Get the root logger and add the null handler to it\n", "logging.getLogger().addHandler(null_handler)\n", "\n", "\n", "\n", "import random\n", "from api.models import Campaign, ContentGroup, Content\n", "from api.actions.legacy_converter.legacy_repurpose_template_converter import (\n", "    convert_repurpose_template_v2_to_v3,\n", "    convert_repurpose_template_v3_to_v2,\n", "    RepurposeTemplateConvertComparisor,\n", ")\n", "\n", "all_content_groups = ContentGroup.objects.filter(\n", "    campaign__campaign_params__campaign_goal=\"Repurpose Content\",\n", "    campaign__id__gt=80000\n", ")\n", "all_content_groups = list(all_content_groups)\n", "# Randomly select 30 content groups\n", "selected_content_groups = random.sample(all_content_groups, min(30, len(all_content_groups)))\n", "\n", "cnt_succ = 0\n", "cnt_fail = 0\n", "\n", "# Process each selected content group\n", "for selected_content_group in selected_content_groups:\n", "    playbook_id = selected_content_group.campaign.playbook.id\n", "    print(f\"Selected Content Group: {selected_content_group.content_group_name}\")\n", "\n", "    # Get the content group parameters\n", "    content_group_params = selected_content_group.content_group_params\n", "\n", "    # Convert templates from v2 to v3 and back, then compare\n", "    try:\n", "        template_v3 = convert_repurpose_template_v2_to_v3(selected_content_group.campaign.playbook.id, content_group_params)\n", "        content_group_params_converted = convert_repurpose_template_v3_to_v2(template_v3, content_group_params)\n", "        comparisor = RepurposeTemplateConvertComparisor()\n", "        comparison_result = comparisor.compare(content_group_params, content_group_params_converted)\n", "    except Exception as e:\n", "        logging.exception(f\"failure as for {selected_content_group.id}: {str(e)}\")\n", "        comparison_result = False\n", "\n", "    # Output the comparison result\n", "    if comparison_result:\n", "        print(\"  Conversion and comparison successful.\")\n", "        cnt_succ += 1\n", "    else:\n", "        print(f\"  Differences found during comparison for content_group {selected_content_group.id}.\")\n", "        print(content_group_params)\n", "        cnt_fail += 1\n", "print(f\"final: {cnt_succ} / {cnt_succ+cnt_fail}\")"]}, {"cell_type": "code", "execution_count": null, "id": "a5e133f9-3701-4544-a11e-aa22b7e8d946", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}