{"cells": [{"cell_type": "code", "execution_count": null, "id": "307798f1-a4b8-434a-8905-677ce659cdca", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Playbook, Campaign, AssetInfo, AssetInfoGroup, TargetInfo, TargetInfoGroup\n", "from api.validator.content_validator import validate_content\n", "from api.validator.content_variation_validator import validate_content_variation\n", "from api.validator.content_group_validator import validate_content_group\n", "from api.validator.campaign_validator import validate_campaign\n", "from api.validator.playbook_validator import validate_playbook\n", "from api.validator.asset_info_validator import validate_asset_info_group, validate_asset_info\n", "from api.validator.target_info_validator import validate_target_info_group, validate_target_info\n", "\n", "\n", "def validate_model_objects(ModelClass, validate_func):\n", "    print(f\"Checking {ModelClass.__name__}...\")\n", "    # Fetch all objects\n", "    objects = ModelClass.objects.all()\n", "\n", "    cnt_failures = 0\n", "    # Iterate over all objects\n", "    for obj in objects:\n", "        try:\n", "            error = validate_func(obj)\n", "        except Exception as e:\n", "            error = f\"Error in validation for object ID {obj.id} due to {e}\"\n", "            raise e\n", "            \n", "        if error:\n", "            print(f\"Validation failed for object ID {obj.id} due to {error}\")\n", "            cnt_failures += 1\n", "            print(\"-\" * 80)\n", "\n", "    print(\n", "        f\"Results: Validation for {ModelClass.__name__} failed for {cnt_failures} of {len(objects)} cases\"\n", "    )\n", "    print(\"=\" * 80)\n"]}, {"cell_type": "code", "execution_count": null, "id": "643fa013-cf00-4029-afe9-13f97ea721ad", "metadata": {}, "outputs": [], "source": ["validate_model_objects(Playbook, validate_playbook)"]}, {"cell_type": "code", "execution_count": null, "id": "369c8d32-4172-4daa-be28-66659fe3c731", "metadata": {}, "outputs": [], "source": ["validate_model_objects(Campaign, validate_campaign)"]}, {"cell_type": "code", "execution_count": null, "id": "73277adb-79f2-40e6-8602-1baba18d5f68", "metadata": {}, "outputs": [], "source": ["validate_model_objects(ContentGroup, validate_content_group)"]}, {"cell_type": "code", "execution_count": null, "id": "65794ec8-be98-450f-979e-e70b1f6f48f6", "metadata": {}, "outputs": [], "source": ["validate_model_objects(Content, validate_content)"]}, {"cell_type": "code", "execution_count": null, "id": "3873df03-1d00-47cc-ae81-119dde1f6b50", "metadata": {}, "outputs": [], "source": ["validate_model_objects(ContentVariation, validate_content_variation)"]}, {"cell_type": "code", "execution_count": null, "id": "c3a75b42-0f7f-4506-9695-df2d6ac892fe", "metadata": {}, "outputs": [], "source": ["validate_model_objects(AssetInfoGroup, validate_asset_info_group)"]}, {"cell_type": "code", "execution_count": null, "id": "26a0ef03-2a5f-4573-9383-fb716318f23a", "metadata": {}, "outputs": [], "source": ["validate_model_objects(AssetInfo, validate_asset_info)"]}, {"cell_type": "code", "execution_count": null, "id": "915d83b7-7e65-4c64-911c-d2c77023e370", "metadata": {}, "outputs": [], "source": ["validate_model_objects(TargetInfoGroup, validate_target_info_group)"]}, {"cell_type": "code", "execution_count": null, "id": "9ecbbc9e-206b-4706-8ec4-cb0e0f0c3e30", "metadata": {}, "outputs": [], "source": ["validate_model_objects(TargetInfo, validate_target_info)"]}, {"cell_type": "code", "execution_count": null, "id": "bd06b148-cd56-466b-a9fe-9776ac194548", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}