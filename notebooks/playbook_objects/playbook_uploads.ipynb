{"cells": [{"cell_type": "code", "execution_count": null, "id": "4bd900df-e91f-4084-9de6-68a01a1b251a", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Playbook, AssetInfoGroup, AssetInfo\n", "import csv\n", "import uuid\n", "from django.db import transaction\n", "\n", "playbook = Playbook.objects.get(id=801)\n", "\n", "with transaction.atomic():\n", "    group = AssetInfoGroup.objects.create(\n", "        playbook=playbook,\n", "        asset_info_group_key=\"AirTable\",\n", "        meta={\"position\":5}\n", "    )\n", "    \n", "    print(os.getcwd())\n", "    file_path = \"playbook_objects/napta.csv\"\n", "    position=1\n", "    prev_doc = {}\n", "    assets = []\n", "    with open(file_path, 'r') as file:\n", "        csv_reader = csv.DictReader(file)\n", "        for row in csv_reader:\n", "            print(row)\n", "            if row['Title'] in prev_doc:\n", "                if prev_doc[row['Title']] != row['Accès']:\n", "                    raise Exception(f\"conflict data for {row['Title']}\")\n", "                else:\n", "                    continue\n", "                \n", "            doc_uuid = str(uuid.uuid4())\n", "            asset_info = AssetInfo.objects.create(\n", "                asset_info_group=group,\n", "                asset_key = row['Title'],\n", "                docs = {\n", "                    doc_uuid: {\n", "                        \"id\": doc_u<PERSON>,\n", "                        \"type\":\"url\",\n", "                        \"value\":row['Accès'],\n", "                        \"field_name\":row['Type of content'],\n", "                        \"position\":0,\n", "                    }\n", "                },\n", "                meta = {\n", "                    \"position\": position\n", "                }\n", "            )\n", "            assets.append(asset_info)\n", "            position += 1\n", "            prev_doc[row['Title']] = row['Accès']\n", "\n", "for asset in assets:\n", "    builder = AssetObjectBuilder(asset)\n", "    builder.build_docs(rebuild=True, check_and_rebuild=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a4796404-42f6-4d28-86c6-0c9602f38557", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}