{"cells": [{"cell_type": "code", "execution_count": null, "id": "a4a3c5ab-2f2d-4a04-941f-cbd184120598", "metadata": {}, "outputs": [], "source": ["# copy one campaign from one account to another\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.eval import Benchmark\n", "from api.models import Playbook, Content, Campaign, ContentVariation, ContentGroup\n", "\n", "# orig_campaign_id = 337\n", "benchmark = Benchmark(creator_id=145, playbook_id=33)\n", "orig_campaign = Campaign.objects.get(pk=orig_campaign_id)\n", "benchmark.copy_campaign(orig_campaign, is_copy_for_review=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3af0694a-4384-470f-83c5-4f1bfd360b79", "metadata": {}, "outputs": [], "source": ["# copy one campaign from one account to another\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import re\n", "from api.models import Playbook, Content, Campaign, ContentVariation, ContentGroup\n", "\n", "eval_content_pattern = r\".*-copy\\(\\d+\\)-.*\"\n", "\n", "for content in Content.objects.all():\n", "    campaign_creator = content.content_group.campaign.creator\n", "    if campaign_creator.id != 129:  # 129 is tofu-review\n", "        if bool(re.match(eval_content_pattern, content.content_name)):\n", "            print(\n", "                f\"content {content.id} {content.content_name} to delete from campaign owner {campaign_creator.id}\"\n", "            )\n", "            # content.delete()"]}, {"cell_type": "code", "execution_count": null, "id": "3db1bb6c-89b0-4b5b-95a3-6b0cba75d4bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}