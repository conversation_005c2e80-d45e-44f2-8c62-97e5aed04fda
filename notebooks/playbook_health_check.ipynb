{"cells": [{"cell_type": "code", "execution_count": null, "id": "fb038bb3-efb0-4edf-94b3-4088d441fe78", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook\n", "from api.playbook import PlaybookHandler\n", "\n", "all_playbooks = Playbook.objects.all()\n", "\n", "cnt_error_playbook = 0\n", "for playbook in all_playbooks:\n", "    creator = playbook.users.first()\n", "    if not creator or not creator.username.startswith(\"tofuadmin\"):\n", "        # print(f\"playbook {playbook.id} not the user we want to handle {creator.username if creator else 'no_creator'}\")\n", "        continue\n", "\n", "    playbook_handler = PlaybookHandler.load_from_db(playbook.id)\n", "    healthiness, column_ids_to_rebuild, errors = playbook_handler.check_healthiness()\n", "    if not healthiness:\n", "        print(\n", "            f\"[ERROR]: Playbook {playbook.id} under {creator.username} is not healthy for columns {column_ids_to_rebuild} with errors: {errors}\"\n", "        )\n", "        # playbook_handler.force_context_full_refresh(column_ids_to_rebuild)\n", "        cnt_error_playbook += 1\n", "print(f\"Total playbook with error: {cnt_error_playbook}\")"]}, {"cell_type": "code", "execution_count": null, "id": "eafc6379-07bb-4f37-b533-0b680dd9daf8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}