{"cells": [{"cell_type": "code", "execution_count": null, "id": "bd359c7b-7b83-4e46-b4f7-41d0a4ececad", "metadata": {}, "outputs": [], "source": ["from langchain_anthropic.chat_models import ChatAnthropicMessages\n", "from langchain.schema import HumanMessage\n", "import boto3\n", "from langchain_community.chat_models.bedrock import BedrockChat\n", "\n", "def call_claude_api(prompt):\n", "    # chat = ChatAnthropicMessages(\n", "    #     model_name=\"claude-3-opus-20240229\",\n", "    #     # temperature=0.7,\n", "    #     # max_tokens=1000,\n", "    #     # top_p=1,\n", "    #     # frequency_penalty=0,\n", "    #     # presence_penalty=0\n", "    # )\n", "    model_name = \"anthropic.claude-3-opus-20240229-v1:0\"\n", "    bedrock_client = boto3.client(\n", "        service_name=\"bedrock-runtime\", region_name=\"us-west-2\"\n", "    )\n", "    kwargs = {}\n", "    kwargs.update(\n", "    {\n", "        \"max_tokens\": 4096,\n", "    }\n", ")\n", "    chat = BedrockChat(\n", "                client=bedrock_client, model_id=model_name, model_kwargs=kwargs)\n", "\n", "    response = chat([HumanMessage(content=prompt)])\n", "\n", "    return response.content\n", "import re\n", "\n", "def extract_schema_from_response(response):\n", "    # Regular expression pattern to match Python code snippets\n", "    pattern = r\"```python\\n(.*?)```\"\n", "\n", "    # Find all code snippets in the response\n", "    code_snippets = re.findall(pattern, response, re.DOTALL)\n", "\n", "    if code_snippets:\n", "        # Assume the last code snippet is the updated schema\n", "        updated_schema = code_snippets[-1].strip()\n", "        return updated_schema\n", "    else:\n", "        raise ValueError(\"No Python code snippet found in the response.\")\n", "        \n", "def update_schema(schema, violations):\n", "    claude_prompt = f\"\"\"\n", "Please help me update the Pydantic validation model based on the provided schema and violations.\n", "\n", "Current schema:\n", "{schema}\n", "\n", "Violations:\n", "{violations}\n", "\n", "To update the schema, please follow these guidelines:\n", "1. Analyze the violations and identify the necessary changes to the schema.\n", "2. Update the field types, constraints, or add new fields to accommodate the violated data.\n", "3. Ensure the updated schema is valid and compatible with Pydantic.\n", "4. Provide the updated schema as a complete Python code snippet, including all the necessary Pydantic models and field definitions.\n", "5. Don't try to remove extra=forbid.\n", "6. Please keep field as enum if it's defined so.\n", "\n", "Please provide the updated schema in your response.\n", "\"\"\"\n", "    print(claude_prompt)\n", "    \n", "    # Make the API call to <PERSON> with the prompt\n", "    response = call_claude_api(claude_prompt)\n", "    print(f\"response: {response}\")\n", "\n", "    # Extract the updated schema from <PERSON>'s response\n", "    updated_schema = extract_schema_from_response(response)\n", "\n", "    # Return the updated schema as a string\n", "    return updated_schema\n", "\n", "    # Placeholder return statement\n", "    \n", "\n", "\n", "# Step 1: <PERSON><PERSON>'s model database for the data\n", "\n", "def get_params_data(object_model, param_field):\n", "    objects = object_model.objects.all()\n", "    params = [(getattr(object, param_field), object.id) for object in objects]\n", "    return params\n", "\n", "\n", "# Step 2-4: Validate data and collect violations\n", "def validate_data(schema, data):\n", "    try:\n", "        schema.parse_obj(data)\n", "        return None\n", "    except ValidationError as e:\n", "        return e\n", "\n", "\n", "def collect_violations(schema, data_list):\n", "    violations = []\n", "    failed_object_ids = []\n", "    for data_pair in data_list:\n", "        data, object_id = data_pair\n", "        if not data:\n", "            continue\n", "        violation = validate_data(schema, data)\n", "        if violation:\n", "            print(f\"violation - {object_id}: {violation}\")\n", "            # print(f\"data: {object_id} - {data}\")\n", "            violations.append(f\"violation: {violation}\\ndata: {data}\\n\")\n", "            failed_object_ids.append(object_id)\n", "            if len(violations) >= 50:\n", "                break\n", "    return violations, failed_object_ids\n", "\n", "\n", "# Step 5: Update the schema using <PERSON>\n", "def learn_schema(object_model, schema, data_list):\n", "    violations, failed_object_ids = collect_violations(schema, data_list)\n", "    if not violations:\n", "        print(f\"no violations\")\n", "        return\n", "\n", "    id_result = []\n", "    for id in failed_object_ids:\n", "        obj = object_model.objects.get(id=id)\n", "        id_result.append(f\"{id}, #{obj.creator}\")\n", "    id_result = '\\n'.join(id_result)\n", "        \n", "    print(f\"[\\n{id_result}\\n]\")\n", "\n", "    updated_schema_str = update_schema(schema.schema_json(), violations)\n", "    schema = BaseModel.parse_raw(updated_schema_str)\n", "    return schema\n", "\n", "\n", "\n", "def run(object_model, param_field, validator):\n", "    # Get the content_params data from the Content model\n", "    params_data = get_params_data(object_model, param_field)\n", "\n", "    # Learn the schema using <PERSON>\n", "    learned_schema = learn_schema(object_model, validator, params_data)\n", "\n", "    # Print the updated schema\n", "    # print(\"Updated schema:\")\n", "    # print(learned_schema.schema_json(indent=2))"]}, {"cell_type": "code", "execution_count": null, "id": "76f006f8-c94d-4778-9508-1584ed2d1a74", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from pydantic import BaseModel, Field, ValidationError\n", "from django.db.models import Model\n", "from api.models import Content, ContentGroup, Campaign\n", "from api.validator.content_validator import ContentParamsValidation\n", "from api.validator.content_group_validator import ContentGroupParamsValidation\n", "from api.validator.campaign_validator import CampaignParamsValidation\n", "\n", "\n", "# run(Content, \"content_params\", ContentParamsValidation)\n", "# run(ContentGroup, \"content_group_params\", ContentGroupParamsValidation)\n", "run(Campaign, \"campaign_params\", CampaignParamsValidation)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9de31a09-9207-438d-b67d-5991f6bec15c", "metadata": {}, "outputs": [], "source": ["from api.models import Content, ContentGroup, Campaign\n", "\n", "ids_to_delete = [\n", "26, #<EMAIL>\n", "28278, #tofuadmin-jian3\n", "28064, #tofuadmin-isaque\n", "]\n", "\n", "# objects_to_delete = Campaign.objects.filter(id__in=ids_to_delete)\n", "# deleted_count, _ = objects_to_delete.delete()"]}, {"cell_type": "code", "execution_count": null, "id": "d2b9c839-9b3e-4bf2-8709-a7d76de6cb43", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}