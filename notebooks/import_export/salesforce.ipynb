{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-10-16 14:11:51,185 INFO: found organization id: tofu-ykka\n", "2024-10-16 14:11:51,545 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n", "2024-10-16 14:11:52,323 ERROR: Request URL: https://api.useparagon.com/projects/a673743b-3f80-49a1-9d95-6cc2c96512d9/sdk/proxy/salesforce/sobjects/Contact/listviews/00BHp00000G0uZSMAZ/results?limit=2000&offset=0\n", "2024-10-16 14:11:52,614 INFO: Found credentials in environment variables.\n", "2024-10-16 14:11:53,904 ERROR: Final records: 22\n", "2024-10-16 14:11:53,909 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Dickenson plc', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Operations', 'Owner.Alias': 'HL<PERSON>', 'Id': '003Hp00002oCMtvIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykcIAA', 'AccountId': '001Hp00002f3ykcIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,913 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'CEO', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMu2IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,918 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'United Oil & Gas, UK', 'Phone': '+44 191 4956203', 'Email': '<EMAIL>', 'Title': 'VP, Finance', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMu5IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykhIAA', 'AccountId': '001Hp00002f3ykhIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,921 ERROR: flattened: {'Name': '<PERSON><PERSON>', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'CFO', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu3IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,925 ERROR: flattened: {'Name': '<PERSON><PERSON>', 'Account.Name': 'Express Logistics and Transport', 'Phone': '(*************', 'Email': 'b.levy@expressl&t.net', 'Title': 'SVP, Operations', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu4IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykfIAA', 'AccountId': '001Hp00002f3ykfIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,931 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'GenePoint', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Technology', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMu8IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:53 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:53 GMT 2024', 'Account.Id': '001Hp00002f3ykjIAA', 'AccountId': '001Hp00002f3ykjIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,933 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Burlington Textiles Corp of America', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Facilities', 'Owner.Alias': 'HL<PERSON>', 'Id': '003Hp00002oCMttIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykaIAA', 'AccountId': '001Hp00002f3ykaIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,934 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 's<PERSON><PERSON><PERSON>', 'Phone': None, 'Email': None, 'Title': None, 'Owner.Alias': '<PERSON><PERSON><PERSON>', 'Id': '003Hp00002oCMuAIAW', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykkIAA', 'AccountId': '001Hp00002f3ykkIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,935 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'University of Arizona', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'Dean of Administration', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMu1IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykgIAA', 'AccountId': '001Hp00002f3ykgIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,937 ERROR: flattened: {'Name': '<PERSON><PERSON>1 <PERSON>', 'Account.Name': None, 'Phone': None, 'Email': '<EMAIL>', 'Title': None, 'Owner.Alias': '<PERSON><PERSON><PERSON>', 'Id': '003Hp0000375tlvIAA', 'CreatedDate': 'Wed Oct 16 18:41:17 GMT 2024', 'LastModifiedDate': 'Wed Oct 16 18:41:17 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:41:17 GMT 2024', 'Account.Id': None, 'AccountId': None, 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,939 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Grand Hotels & Resorts Ltd', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Facilities', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtxIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykdIAA', 'AccountId': '001Hp00002f3ykdIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,940 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Express Logistics and Transport', 'Phone': '(*************', 'Email': 'j.davis@expressl&t.net', 'Title': 'Director, Warehouse Mgmt', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMu0IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:57 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:57 GMT 2024', 'Account.Id': '001Hp00002f3ykfIAA', 'AccountId': '001Hp00002f3ykfIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,942 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Technology', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtzIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,943 ERROR: flattened: {'Name': \"<PERSON>\", 'Account.Name': 'United Oil & Gas, Singapore', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Production', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMu7IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:53 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:53 GMT 2024', 'Account.Id': '001Hp00002f3ykiIAA', 'AccountId': '001Hp00002f3ykiIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,945 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Pyramid Construction Inc.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Administration and Finance', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMtuIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykbIAA', 'AccountId': '001Hp00002f3ykbIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,946 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Edge Communications', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Procurement', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtrIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykZIAQ', 'AccountId': '001Hp00002f3ykZIAQ', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,948 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Edge Communications', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'CFO', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMtsIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykZIAQ', 'AccountId': '001Hp00002f3ykZIAQ', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,949 ERROR: flattened: {'Name': '<PERSON><PERSON><PERSON>', 'Account.Name': 's<PERSON><PERSON><PERSON>', 'Phone': None, 'Email': None, 'Title': None, 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu9IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykkIAA', 'AccountId': '001Hp00002f3ykkIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,951 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Production', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtyIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,953 ERROR: flattened: {'Name': 'test test', 'Account.Name': 'Test1', 'Phone': None, 'Email': '<EMAIL>', 'Title': None, 'Owner.Alias': 'HLiu', 'Id': '003Hp00002sUD57IAG', 'CreatedDate': 'Wed Jan 24 22:25:14 GMT 2024', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002m3EOxIAM', 'AccountId': '001Hp00002m3EOxIAM', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,954 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'Grand Hotels & Resorts Ltd', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Administration and Finance', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtwIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykdIAA', 'AccountId': '001Hp00002f3ykdIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "2024-10-16 14:11:53,956 ERROR: flattened: {'Name': '<PERSON>', 'Account.Name': 'United Oil & Gas, Singapore', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'Regional General Manager', 'Owner.Alias': 'H<PERSON><PERSON>', 'Id': '003Hp00002oCMu6IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:53 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:53 GMT 2024', 'Account.Id': '001Hp00002f3ykiIAA', 'AccountId': '001Hp00002f3ykiIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}\n", "[{'Name': '<PERSON>', 'Account.Name': 'Dickenson plc', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Operations', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtvIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykcIAA', 'AccountId': '001Hp00002f3ykcIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Arthur Song', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'CEO', 'Owner.<PERSON><PERSON>': 'HLiu', 'Id': '003Hp00002oCMu2IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Ashley James', 'Account.Name': 'United Oil & Gas, UK', 'Phone': '+44 191 4956203', 'Email': '<EMAIL>', 'Title': 'VP, Finance', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu5IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykhIAA', 'AccountId': '001Hp00002f3ykhIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Avi Green', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'CFO', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu3IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Babara Levy', 'Account.Name': 'Express Logistics and Transport', 'Phone': '(*************', 'Email': 'b.levy@expressl&t.net', 'Title': 'SVP, Operations', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu4IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykfIAA', 'AccountId': '001Hp00002f3ykfIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Edna Frank', 'Account.Name': 'GenePoint', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Technology', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu8IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:53 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:53 GMT 2024', 'Account.Id': '001Hp00002f3ykjIAA', 'AccountId': '001Hp00002f3ykjIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Jack Rogers', 'Account.Name': 'Burlington Textiles Corp of America', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Facilities', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMttIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykaIAA', 'AccountId': '001Hp00002f3ykaIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Jake Llorrac', 'Account.Name': 'sForce', 'Phone': None, 'Email': None, 'Title': None, 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMuAIAW', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykkIAA', 'AccountId': '001Hp00002f3ykkIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Jane Grey', 'Account.Name': 'University of Arizona', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'Dean of Administration', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu1IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykgIAA', 'AccountId': '001Hp00002f3ykgIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Jian1 Yang', 'Account.Name': None, 'Phone': None, 'Email': '<EMAIL>', 'Title': None, 'Owner.Alias': 'HLiu', 'Id': '003Hp0000375tlvIAA', 'CreatedDate': 'Wed Oct 16 18:41:17 GMT 2024', 'LastModifiedDate': 'Wed Oct 16 18:41:17 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:41:17 GMT 2024', 'Account.Id': None, 'AccountId': None, 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'John Bond', 'Account.Name': 'Grand Hotels & Resorts Ltd', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Facilities', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtxIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykdIAA', 'AccountId': '001Hp00002f3ykdIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Josh Davis', 'Account.Name': 'Express Logistics and Transport', 'Phone': '(*************', 'Email': 'j.davis@expressl&t.net', 'Title': 'Director, Warehouse Mgmt', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu0IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:57 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:57 GMT 2024', 'Account.Id': '001Hp00002f3ykfIAA', 'AccountId': '001Hp00002f3ykfIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Lauren Boyle', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Technology', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtzIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': \"Liz D'Cruz\", 'Account.Name': 'United Oil & Gas, Singapore', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'VP, Production', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu7IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:53 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:53 GMT 2024', 'Account.Id': '001Hp00002f3ykiIAA', 'AccountId': '001Hp00002f3ykiIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Pat Stumuller', 'Account.Name': 'Pyramid Construction Inc.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Administration and Finance', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtuIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykbIAA', 'AccountId': '001Hp00002f3ykbIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Rose Gonzalez', 'Account.Name': 'Edge Communications', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Procurement', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtrIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykZIAQ', 'AccountId': '001Hp00002f3ykZIAQ', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Sean Forbes', 'Account.Name': 'Edge Communications', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'CFO', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtsIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykZIAQ', 'AccountId': '001Hp00002f3ykZIAQ', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Siddartha Nedaerk', 'Account.Name': 'sForce', 'Phone': None, 'Email': None, 'Title': None, 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu9IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002f3ykkIAA', 'AccountId': '001Hp00002f3ykkIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Stella Pavlova', 'Account.Name': 'United Oil & Gas Corp.', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Production', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtyIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:55 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:55 GMT 2024', 'Account.Id': '001Hp00002f3ykeIAA', 'AccountId': '001Hp00002f3ykeIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'test test', 'Account.Name': 'Test1', 'Phone': None, 'Email': '<EMAIL>', 'Title': None, 'Owner.Alias': 'HLiu', 'Id': '003Hp00002sUD57IAG', 'CreatedDate': 'Wed Jan 24 22:25:14 GMT 2024', 'LastModifiedDate': 'Wed Oct 16 18:50:54 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:54 GMT 2024', 'Account.Id': '001Hp00002m3EOxIAM', 'AccountId': '001Hp00002m3EOxIAM', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Tim Barr', 'Account.Name': 'Grand Hotels & Resorts Ltd', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'SVP, Administration and Finance', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMtwIAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:52 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:52 GMT 2024', 'Account.Id': '001Hp00002f3ykdIAA', 'AccountId': '001Hp00002f3ykdIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}, {'Name': 'Tom Ripley', 'Account.Name': 'United Oil & Gas, Singapore', 'Phone': '(*************', 'Email': '<EMAIL>', 'Title': 'Regional General Manager', 'Owner.Alias': 'HLiu', 'Id': '003Hp00002oCMu6IAG', 'CreatedDate': 'Thu Oct 12 21:08:58 GMT 2023', 'LastModifiedDate': 'Wed Oct 16 18:50:53 GMT 2024', 'SystemModstamp': 'Wed Oct 16 18:50:53 GMT 2024', 'Account.Id': '001Hp00002f3ykiIAA', 'AccountId': '001Hp00002f3ykiIAA', 'Owner.Id': '005Hp00000fiqP4IAI', 'OwnerId': '005Hp00000fiqP4IAI'}]\n"]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.paragon_wrapper import ParagonWrapper\n", "from api.models import Playbook\n", "playbook = Playbook.objects.filter(id=291).first()\n", "\n", "paragon = ParagonWrapper(playbook)\n", "\n", "list_data = {\n", "    \"listType\": \"List\",\n", "    \"id\": \"00BHp00000G0uZSMAZ\",\n", "    \"type\": \"Contact\"\n", "}\n", "\n", "properties = [\"Id\", \"Name\"]\n", "\n", "agent = paragon.get_salesforce_agent()\n", "records = agent.import_list(list_data, properties)\n", "print(records)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}