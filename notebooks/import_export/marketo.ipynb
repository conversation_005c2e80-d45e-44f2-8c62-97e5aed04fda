{"cells": [{"cell_type": "code", "execution_count": 1, "id": "53793e7d-f4bf-4579-9d44-782ea7b6e6d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading secrets from AWS Secrets Manager: tofu-backend-local-secrets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "/Users/<USER>/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_arn\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-06-02 23:18:23,549 INFO: found organization id: tofu-ykka\n", "2025-06-02 23:18:23,656 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}, {"ename": "DoesNotExist", "evalue": "Campaign matching query does not exist.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mDoesNotExist\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 23\u001b[39m\n\u001b[32m     20\u001b[39m tig_id = \u001b[32m18503\u001b[39m\n\u001b[32m     22\u001b[39m playbook = Playbook.objects.filter(\u001b[38;5;28mid\u001b[39m=\u001b[32m291\u001b[39m).first()\n\u001b[32m---> \u001b[39m\u001b[32m23\u001b[39m campaign = \u001b[43mCampaign\u001b[49m\u001b[43m.\u001b[49m\u001b[43mobjects\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mid\u001b[39;49m\u001b[43m=\u001b[49m\u001b[43mcampaign_id\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     24\u001b[39m tig = TargetInfoGroup.objects.get(\u001b[38;5;28mid\u001b[39m=tig_id)\n\u001b[32m     25\u001b[39m list_id = \u001b[32m1012\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/manager.py:87\u001b[39m, in \u001b[36mBaseManager._get_queryset_methods.<locals>.create_method.<locals>.manager_method\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m     85\u001b[39m \u001b[38;5;129m@wraps\u001b[39m(method)\n\u001b[32m     86\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mmanager_method\u001b[39m(\u001b[38;5;28mself\u001b[39m, *args, **kwargs):\n\u001b[32m---> \u001b[39m\u001b[32m87\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mget_queryset\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/query.py:637\u001b[39m, in \u001b[36mQuerySet.get\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    635\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m clone._result_cache[\u001b[32m0\u001b[39m]\n\u001b[32m    636\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m num:\n\u001b[32m--> \u001b[39m\u001b[32m637\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m.model.DoesNotExist(\n\u001b[32m    638\u001b[39m         \u001b[33m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m matching query does not exist.\u001b[39m\u001b[33m\"\u001b[39m % \u001b[38;5;28mself\u001b[39m.model._meta.object_name\n\u001b[32m    639\u001b[39m     )\n\u001b[32m    640\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m.model.MultipleObjectsReturned(\n\u001b[32m    641\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mget() returned more than one \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m -- it returned \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m!\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    642\u001b[39m     % (\n\u001b[32m   (...)\u001b[39m\u001b[32m    645\u001b[39m     )\n\u001b[32m    646\u001b[39m )\n", "\u001b[31mDoesNotExist\u001b[39m: Campaign matching query does not exist."]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.paragon_wrapper import ParagonWrapper\n", "from api.models import Playbook, Campaign, TargetInfoGroup\n", "from api.sync.e2e.marketo_sync import PlaybookMarketoSyncer\n", "\n", "campaign_id = 131309\n", "tig_id = 18503\n", "\n", "playbook = Playbook.objects.filter(id=291).first()\n", "campaign = Campaign.objects.get(id=campaign_id)\n", "tig = TargetInfoGroup.objects.get(id=tig_id)\n", "list_id = 1012\n", "\n", "syncer = PlaybookMarketoSyncer(playbook, tig, [campaign], list_id)\n", "\n", "playbook_records = syncer._get_playbook_records()\n", "platform_records = syncer._get_platform_records()\n", "\n", "print(\"playbook_records\")\n", "print(playbook_records)\n", "print(\"platform_records\")\n", "print(platform_records)"]}, {"cell_type": "code", "execution_count": null, "id": "0709c3d0-e012-4cba-bcfb-64596c89237b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}