{"cells": [{"cell_type": "markdown", "id": "c0de7e02-00af-46b5-9496-f074a88634a9", "metadata": {}, "source": ["#list records given a list"]}, {"cell_type": "code", "execution_count": 1, "id": "57f27e23-8f77-491c-8d83-7ccc2e573ba9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading secrets from AWS Secrets Manager: tofu-backend-local-secrets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "/Users/<USER>/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_arn\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-05-29 22:44:14,168 INFO: found organization id: tofu-ykka\n", "2025-05-29 22:44:14,377 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n", "2025-05-29 22:44:42,304 INFO: Found credentials in environment variables.\n", "Error getting HubSpot list by id 86. Response: 404 {\"message\":\"Request failed with status code 404\",\"name\":\"<PERSON>rro<PERSON>\",\"statusText\":\"Not Found\",\"output\":{\"status\":\"error\",\"message\":\"List does not exist with ID 86.\",\"correlationId\":\"************************************\",\"context\":{\"listId\":[\"86\"]},\"category\":\"OBJECT_NOT_FOUND\",\"subCategory\":\"ListError.LIST_ID_DOES_NOT_EXIST\"},\"status\":404,\"headers\":{\"date\":\"Fri, 30 May 2025 05:45:08 GMT\",\"content-type\":\"application/json;charset=utf-8\",\"cf-ray\":\"947bec715b3b9388-IAD\",\"cf-cache-status\":\"DYNAMIC\",\"strict-transport-security\":\"max-age=31536000; includeSubDomains; preload\",\"vary\":\"origin\",\"access-control-allow-credentials\":\"false\",\"x-content-type-options\":\"nosniff\",\"x-hubspot-correlation-id\":\"************************************\",\"x-hubspot-notfound\":\"true\",\"x-hubspot-ratelimit-interval-milliseconds\":\"10000\",\"x-hubspot-ratelimit-max\":\"110\",\"x-hubspot-ratelimit-remaining\":\"109\",\"x-hubspot-ratelimit-secondly\":\"11\",\"x-hubspot-ratelimit-secondly-remaining\":\"10\",\"set-cookie\":[\"__cf_bm=ZX5JIRBLRYNdggVvjVRDPRlwHS44Jx9cN8sYVzBT2Sg-1748583908-1.0.1.1-XmNPJ237P3NCzSfOUu3VVMsX1.v47QTN9MTz2bPBcDpxNovpb2QD.zF922OwFCx2X4y5GZcqj5Focxtlw.78jEWQeFmLWAjglclQ.8wwlHo; path=/; expires=Fri, 30-May-25 06:15:08 GMT; domain=.hubapi.com; HttpOnly; Secure; SameSite=None\"],\"report-to\":\"{\\\"endpoints\\\":[{\\\"url\\\":\\\"https:\\\\/\\\\/a.nel.cloudflare.com\\\\/report\\\\/v4?s=4JF9leFmhPJF5vpW2VwvLyd7iIApayWCoSSfAAE9OW2Ox6hw%2B7HTNTZYEFbxtdete2ZJD0eXVfUyn7VXqBRLkpcBBirAzT6oJPNt%2FzY4YbfTrt%2BaaBzXIrLyVDBOwuMO\\\"}],\\\"group\\\":\\\"cf-nel\\\",\\\"max_age\\\":604800}\",\"nel\":\"{\\\"success_fraction\\\":0.01,\\\"report_to\\\":\\\"cf-nel\\\",\\\"max_age\\\":604800}\",\"server\":\"cloudflare\"}}\n"]}, {"ename": "Exception", "evalue": "Error getting Hubspot list records with list_id 86. Response: 404", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mException\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 43\u001b[39m\n\u001b[32m     41\u001b[39m workflow = CRMListSyncWorkflow(playbook_id, sync)\n\u001b[32m     42\u001b[39m record_fetcher = workflow._create_record_fetcher(integration)\n\u001b[32m---> \u001b[39m\u001b[32m43\u001b[39m new_records_data = \u001b[43mrecord_fetcher\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_new_records_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     44\u001b[39m \u001b[38;5;28mprint\u001b[39m(new_records_data)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/server/api/sync/worker/record_fetcher.py:24\u001b[39m, in \u001b[36mSyncListRecordFetcher.get_new_records_data\u001b[39m\u001b[34m(self, skip_func)\u001b[39m\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mget_new_records_data\u001b[39m(\u001b[38;5;28mself\u001b[39m, skip_func=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m     23\u001b[39m     \u001b[38;5;66;03m# step 1: get new records ids\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m     new_record_ids = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mget_new_record_ids\u001b[49m\u001b[43m(\u001b[49m\u001b[43mskip_func\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     25\u001b[39m     logging.info(\n\u001b[32m     26\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mNew records discovered for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.integration.target_info_group.target_info_group_key\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(new_record_ids)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m     27\u001b[39m     )\n\u001b[32m     28\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m new_record_ids:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/server/api/sync/worker/record_fetcher.py:54\u001b[39m, in \u001b[36mSyncListRecordFetcher.get_new_record_ids\u001b[39m\u001b[34m(self, skip_func)\u001b[39m\n\u001b[32m     45\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mget_new_record_ids\u001b[39m(\u001b[38;5;28mself\u001b[39m, skip_func=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m     46\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     47\u001b[39m \u001b[33;03m    Returns a list of new records to be imported, excluding those already present or skipped.\u001b[39;00m\n\u001b[32m     48\u001b[39m \u001b[33;03m    Args:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     52\u001b[39m \u001b[33;03m        list: New records to be imported.\u001b[39;00m\n\u001b[32m     53\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m54\u001b[39m     latest_records = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mintegration\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_get_platform_records\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     55\u001b[39m     current_records = \u001b[38;5;28mself\u001b[39m.integration._get_playbook_records()\n\u001b[32m     56\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.integration.target_info_group.meta:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/server/api/sync/integration_sync/hubspot_integration.py:34\u001b[39m, in \u001b[36mHubspotIntegration._get_platform_records\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_get_platform_records\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m---> \u001b[39m\u001b[32m34\u001b[39m     hubsport_records = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43magent\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_list_records\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mlist_id\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     35\u001b[39m     hubsport_records = [\u001b[38;5;28mstr\u001b[39m(x[\u001b[33m\"\u001b[39m\u001b[33mrecordId\u001b[39m\u001b[33m\"\u001b[39m]) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m hubsport_records]\n\u001b[32m     36\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m hubsport_records\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/server/api/paragon_wrapper.py:269\u001b[39m, in \u001b[36mHubspotAgent.get_list_records\u001b[39m\u001b[34m(self, list_id)\u001b[39m\n\u001b[32m    267\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m response.status_code != \u001b[32m200\u001b[39m:\n\u001b[32m    268\u001b[39m     message = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError getting Hubspot list records with list_id \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mlist_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m. Response: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresponse.status_code\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m269\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(message)\n\u001b[32m    271\u001b[39m response_data = response.json()\n\u001b[32m    272\u001b[39m output = response_data.get(\u001b[33m\"\u001b[39m\u001b[33moutput\u001b[39m\u001b[33m\"\u001b[39m, {})\n", "\u001b[31mException\u001b[39m: <PERSON><PERSON><PERSON> getting Hubspot list records with list_id 86. Response: 404"]}], "source": ["import os\n", "import sys\n", "import django\n", "import time\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Playbook, Campaign, TargetInfoGroup\n", "from api.sync.crm_list_sync.crm_list_sync_hubspot import CRMListSyncHubspot\n", "from api.sync.crm_list_sync.crm_list_sync_workflow import CRMListSyncWorkflow\n", "playbook_id = 17422\n", "# playbook_id = 7247\n", "# campaign_id = 202008\n", "# contact\n", "target_info_group_id = 60573\n", "# company\n", "target_info_group_id = 60498\n", "playbook = Playbook.objects.get(id=playbook_id)\n", "\n", "target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)\n", "# campaign = Campaign.objects.get(id=campaign_id)\n", "list_id = 90\n", "\n", "sync = CRMListSyncHubspot(playbook_id)\n", "try:\n", "    list_detail = sync.paragon_wrapper.get_hubspot_agent().get_list_by_id(list_id)\n", "    print(list_detail)\n", "except Exception as e:\n", "    print(f\"{e}\")\n", "\n", "integration = sync._create_integration(target_info_group)\n", "integration.list_id = list_id\n", "workflow = CRMListSyncWorkflow(playbook_id, sync)\n", "record_fetcher = workflow._create_record_fetcher(integration)\n", "new_records_data = record_fetcher.get_new_records_data()\n", "print(new_records_data)\n"]}, {"cell_type": "code", "execution_count": null, "id": "663b77d8-837d-4fce-8bcb-fbac316af8ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}