{"cells": [{"cell_type": "code", "execution_count": 1, "id": "40e51bda-4766-4871-9600-653c8e97b996", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading secrets from AWS Secrets Manager: tofu-backend-local-secrets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "/Users/<USER>/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_arn\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-06-02 23:17:29,759 INFO: found organization id: tofu-ykka\n", "2025-06-02 23:17:29,875 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n", "2025-06-02 23:17:32,662 INFO: Checking syncs for playbook Playbook object (13902)\n"]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "from api.models import Playbook\n", "from api.playbook_sync import PlaybookSyncerManager\n", "\n", "pl = Playbook.objects.get(id=13902)\n", "manager = PlaybookSyncerManager(pl)\n", "manager.sync()"]}, {"cell_type": "code", "execution_count": null, "id": "aeaf8198-6bf4-47fe-bb67-d8dde8e29206", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}