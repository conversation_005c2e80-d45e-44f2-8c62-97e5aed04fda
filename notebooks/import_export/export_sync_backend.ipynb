{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "OperationalError", "evalue": "server closed the connection unexpectedly\n\tThis probably means the server terminated abnormally\n\tbefore or while processing the request.\nserver closed the connection unexpectedly\n\tThis probably means the server terminated abnormally\n\tbefore or while processing the request.\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mOperationalError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/backends/utils.py:89\u001b[39m, in \u001b[36mCursorWrapper._execute\u001b[39m\u001b[34m(self, sql, params, *ignored_wrapper_args)\u001b[39m\n\u001b[32m     88\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m89\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcursor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mOperationalError\u001b[39m: server closed the connection unexpectedly\n\tThis probably means the server terminated abnormally\n\tbefore or while processing the request.\nserver closed the connection unexpectedly\n\tThis probably means the server terminated abnormally\n\tbefore or while processing the request.\n", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mOperationalError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 21\u001b[39m\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mapi\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Playbook, Campaign, TargetInfoGroup, ContentGroup\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mapi\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mshared_definitions\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mprotobuf\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mgen\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01maction_define_pb2\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m PlatformType\n\u001b[32m---> \u001b[39m\u001b[32m21\u001b[39m content_group = \u001b[43mContentGroup\u001b[49m\u001b[43m.\u001b[49m\u001b[43mobjects\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mid\u001b[39;49m\u001b[43m=\u001b[49m\u001b[32;43m322307\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     22\u001b[39m handler = get_export_handler(PlatformType.PLATFORM_TYPE_HUBSPOT, content_group)\n\u001b[32m     23\u001b[39m \u001b[38;5;66;03m# handler.export_new(content_group, EmailTemplateInput(html_content=\"\", email_file_name=\"\", email_subject=\"\", email_type=\"\"))\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/manager.py:87\u001b[39m, in \u001b[36mBaseManager._get_queryset_methods.<locals>.create_method.<locals>.manager_method\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m     85\u001b[39m \u001b[38;5;129m@wraps\u001b[39m(method)\n\u001b[32m     86\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mmanager_method\u001b[39m(\u001b[38;5;28mself\u001b[39m, *args, **kwargs):\n\u001b[32m---> \u001b[39m\u001b[32m87\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mget_queryset\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/query.py:633\u001b[39m, in \u001b[36mQuerySet.get\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    631\u001b[39m     limit = MAX_GET_RESULTS\n\u001b[32m    632\u001b[39m     clone.query.set_limits(high=limit)\n\u001b[32m--> \u001b[39m\u001b[32m633\u001b[39m num = \u001b[38;5;28mlen\u001b[39m(clone)\n\u001b[32m    634\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m num == \u001b[32m1\u001b[39m:\n\u001b[32m    635\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m clone._result_cache[\u001b[32m0\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/query.py:380\u001b[39m, in \u001b[36mQuerySet.__len__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    379\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__len__\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m380\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_fetch_all\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    381\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m._result_cache)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/query.py:1881\u001b[39m, in \u001b[36mQuerySet._fetch_all\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1879\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_fetch_all\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m   1880\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result_cache \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1881\u001b[39m         \u001b[38;5;28mself\u001b[39m._result_cache = \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mself\u001b[39m._iterable_class(\u001b[38;5;28mself\u001b[39m))\n\u001b[32m   1882\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._prefetch_related_lookups \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._prefetch_done:\n\u001b[32m   1883\u001b[39m         \u001b[38;5;28mself\u001b[39m._prefetch_related_objects()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/query.py:91\u001b[39m, in \u001b[36mModelIterable.__iter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     88\u001b[39m compiler = queryset.query.get_compiler(using=db)\n\u001b[32m     89\u001b[39m \u001b[38;5;66;03m# Execute the query. This will also fill compiler.select, klass_info,\u001b[39;00m\n\u001b[32m     90\u001b[39m \u001b[38;5;66;03m# and annotations.\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m91\u001b[39m results = \u001b[43mcompiler\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute_sql\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     92\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunked_fetch\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mchunked_fetch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk_size\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mchunk_size\u001b[49m\n\u001b[32m     93\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     94\u001b[39m select, klass_info, annotation_col_map = (\n\u001b[32m     95\u001b[39m     compiler.select,\n\u001b[32m     96\u001b[39m     compiler.klass_info,\n\u001b[32m     97\u001b[39m     compiler.annotation_col_map,\n\u001b[32m     98\u001b[39m )\n\u001b[32m     99\u001b[39m model_cls = klass_info[\u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/models/sql/compiler.py:1560\u001b[39m, in \u001b[36mSQLCompiler.execute_sql\u001b[39m\u001b[34m(self, result_type, chunked_fetch, chunk_size)\u001b[39m\n\u001b[32m   1558\u001b[39m     cursor = \u001b[38;5;28mself\u001b[39m.connection.cursor()\n\u001b[32m   1559\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1560\u001b[39m     \u001b[43mcursor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1561\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[32m   1562\u001b[39m     \u001b[38;5;66;03m# Might fail for server-side cursors (e.g. connection closed)\u001b[39;00m\n\u001b[32m   1563\u001b[39m     cursor.close()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/backends/utils.py:102\u001b[39m, in \u001b[36mCursorDebugWrapper.execute\u001b[39m\u001b[34m(self, sql, params)\u001b[39m\n\u001b[32m    100\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mexecute\u001b[39m(\u001b[38;5;28mself\u001b[39m, sql, params=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m    101\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m.debug_sql(sql, params, use_last_executed_query=\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[32m--> \u001b[39m\u001b[32m102\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/sentry_sdk/integrations/django/__init__.py:641\u001b[39m, in \u001b[36minstall_sql_hook.<locals>.execute\u001b[39m\u001b[34m(self, sql, params)\u001b[39m\n\u001b[32m    632\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m options \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    633\u001b[39m             attach_explain_plan_to_span(\n\u001b[32m    634\u001b[39m                 span,\n\u001b[32m    635\u001b[39m                 \u001b[38;5;28mself\u001b[39m.cursor.connection,\n\u001b[32m   (...)\u001b[39m\u001b[32m    639\u001b[39m                 options,\n\u001b[32m    640\u001b[39m             )\n\u001b[32m--> \u001b[39m\u001b[32m641\u001b[39m     result = \u001b[43mreal_execute\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    643\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m capture_internal_exceptions():\n\u001b[32m    644\u001b[39m     add_query_source(hub, span)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/backends/utils.py:67\u001b[39m, in \u001b[36mCursorWrapper.execute\u001b[39m\u001b[34m(self, sql, params)\u001b[39m\n\u001b[32m     66\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mexecute\u001b[39m(\u001b[38;5;28mself\u001b[39m, sql, params=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m---> \u001b[39m\u001b[32m67\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_execute_with_wrappers\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     68\u001b[39m \u001b[43m        \u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmany\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexecutor\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_execute\u001b[49m\n\u001b[32m     69\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/backends/utils.py:80\u001b[39m, in \u001b[36mCursorWrapper._execute_with_wrappers\u001b[39m\u001b[34m(self, sql, params, many, executor)\u001b[39m\n\u001b[32m     78\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m wrapper \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mreversed\u001b[39m(\u001b[38;5;28mself\u001b[39m.db.execute_wrappers):\n\u001b[32m     79\u001b[39m     executor = functools.partial(wrapper, executor)\n\u001b[32m---> \u001b[39m\u001b[32m80\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mexecutor\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmany\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/backends/utils.py:84\u001b[39m, in \u001b[36mCursorWrapper._execute\u001b[39m\u001b[34m(self, sql, params, *ignored_wrapper_args)\u001b[39m\n\u001b[32m     82\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_execute\u001b[39m(\u001b[38;5;28mself\u001b[39m, sql, params, *ignored_wrapper_args):\n\u001b[32m     83\u001b[39m     \u001b[38;5;28mself\u001b[39m.db.validate_no_broken_transaction()\n\u001b[32m---> \u001b[39m\u001b[32m84\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mdb\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrap_database_errors\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m     85\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m:\u001b[49m\n\u001b[32m     86\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;66;43;03m# params default might be backend specific.\u001b[39;49;00m\n\u001b[32m     87\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mreturn\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcursor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/utils.py:91\u001b[39m, in \u001b[36mDatabaseErrorWrapper.__exit__\u001b[39m\u001b[34m(self, exc_type, exc_value, traceback)\u001b[39m\n\u001b[32m     89\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m dj_exc_type \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m (DataError, IntegrityError):\n\u001b[32m     90\u001b[39m     \u001b[38;5;28mself\u001b[39m.wrapper.errors_occurred = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m91\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m dj_exc_value.with_traceback(traceback) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc_value\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Workspace/tofu/backend/tofu/venv/lib/python3.11/site-packages/django/db/backends/utils.py:89\u001b[39m, in \u001b[36mCursorWrapper._execute\u001b[39m\u001b[34m(self, sql, params, *ignored_wrapper_args)\u001b[39m\n\u001b[32m     87\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.cursor.execute(sql)\n\u001b[32m     88\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m89\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcursor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mOperationalError\u001b[39m: server closed the connection unexpectedly\n\tThis probably means the server terminated abnormally\n\tbefore or while processing the request.\nserver closed the connection unexpectedly\n\tThis probably means the server terminated abnormally\n\tbefore or while processing the request.\n"]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.sync.crm_export.export_factory import get_export_handler\n", "from api.sync.crm_export.export_handler import EmailTemplateInput\n", "from api.models import Playbook, Campaign, TargetInfoGroup, ContentGroup\n", "from api.shared_definitions.protobuf.gen.action_define_pb2 import PlatformType\n", "\n", "\n", "content_group = ContentGroup.objects.get(id=322307)\n", "handler = get_export_handler(PlatformType.PLATFORM_TYPE_HUBSPOT, content_group)\n", "# handler.export_new(content_group, EmailTemplateInput(html_content=\"\", email_file_name=\"\", email_subject=\"\", email_type=\"\"))\n", "handler.export_new(content_group)\n", "handler.export_existing(content_group)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}