{"cells": [{"cell_type": "code", "execution_count": null, "id": "ad654e41-28d5-44ef-ac05-85b067ad12bc", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import os\n", "import logging\n", "\n", "import pinecone\n", "\n", "from api.llms import get_llm_for_embeddings\n", "from api.constants.industry_list import INBOUND_INDUSTRY_LIST\n", "# from langchain_pinecone import Pinecone\n", "from pinecone import Pinecone, ServerlessSpec\n", "from pinecone.grpc import PineconeGRPC\n", "import time\n", "\n", "pinecone_key = os.environ.get(\"PINECONE_API_KEY\")\n", "pinecone_host=os.environ.get(\"PINECONE_INDEX_HOST\")\n", "os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION']='python'\n", "\n", "index_name=\"inbound\"\n", "namespace=\"inbound-serverless\"\n", "\n", "pc = Pinecone(api_key=pinecone_key)\n", "index = pc.Index(index_name)\n", "\n", "def rebuild_index():\n", "    index.delete(namespace=\"INBOUND_INDUSTRY_LIST\", delete_all=True)\n", "    \n", "    # Creates an index using the API key stored in the client 'pc'. \n", "    # Generate embeddings using the OpenAIEmbeddings object\n", "    MODEL_BUDGET, llm = get_llm_for_embeddings()\n", "    embeddings = llm.embed_documents(INBOUND_INDUSTRY_LIST)\n", "\n", "    vector_data = [{\n", "        \"id\":str(i),\n", "        \"values\":embeddings[i],\n", "        \"metadata\": {\"label\":INBOUND_INDUSTRY_LIST[i]},\n", "    } for i in range(len(INBOUND_INDUSTRY_LIST))]\n", "    upsert_response = index.upsert(\n", "        vectors=vector_data,\n", "        namespace=\"INBOUND_INDUSTRY_LIST\"\n", "    )\n", "    \n", "\n", "def query_index():\n", "    MODEL_BUDGET, llm = get_llm_for_embeddings()\n", "    vector_emb = llm.embed_documents([\"dog doctor\"])[0]\n", "    results = index.query(\n", "        namespace=\"INBOUND_INDUSTRY_LIST\",\n", "        vector=vector_emb,\n", "        top_k=3,\n", "        include_metadata=True\n", "    )\n", "    print(results)\n", "\n", "rebuild_index()\n", "time.sleep(10)\n", "query_index()"]}, {"cell_type": "code", "execution_count": null, "id": "78e98cfa-dce0-4306-bc50-f2c853227a3a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}