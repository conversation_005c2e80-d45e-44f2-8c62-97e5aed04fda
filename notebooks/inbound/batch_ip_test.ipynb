{"cells": [{"cell_type": "code", "execution_count": null, "id": "e0601a45-006d-43b0-ace8-aa55940e8759", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import csv\n", "import time\n", "from api.inbound_handler import InboundProvider\n", "\n", "\n", "# Load the existing CSV file\n", "print(os.getcwd())\n", "\n", "input_file = 'inbound/ip_slug_mapping.csv'\n", "output_file = 'inbound/ip_slug_mapping_with_responses.csv'\n", "\n", "\n", "# Open the input CSV file\n", "with open(input_file, mode='r', newline='') as infile:\n", "    reader = csv.<PERSON><PERSON><PERSON><PERSON><PERSON>(infile)\n", "    \n", "    # Prepare to write to the output CSV file\n", "    with open(output_file, mode='w', newline='') as outfile:\n", "        # Add new fields for happierleads domain and type\n", "        fieldnames = reader.fieldnames + ['happierleads_domain', 'happierleads_type', 'leadfeeder_domain', 'happierleads_response', 'leadfeeder_response']\n", "        writer = csv.DictWriter(outfile, fieldnames=fieldnames)\n", "        writer.writeheader()\n", "        \n", "        # Iterate through each row in the input CSV\n", "        for row in reader:\n", "            ip = row['ip_address']\n", "            \n", "            # Query the InboundProvider for the current IP address\n", "            try:\n", "                res1, res2 = InboundProvider.test_all_providers(ip)\n", "                \n", "                # Convert the happierleads response to JSON\n", "                happierleads_data = res1 or {}\n", "                leadfeeder_data = res2 or {}\n", "\n", "                # Extract domain and type from happierleads response\n", "                row['happierleads_domain'] = happierleads_data.get('domain', '')\n", "                row['happierleads_type'] = happierleads_data.get('type', '')\n", "\n", "                # Extract domain and industry from leadfeeder response\n", "                row['leadfeeder_domain'] = leadfeeder_data.get(\"company\", {}).get(\"domain\", \"\")\n", "                \n", "                # Add the full responses to the row\n", "                row['happierleads_response'] = happierleads_data\n", "                row['leadfeeder_response'] = leadfeeder_data    # Convert response to JSON\n", "                time.sleep(10)\n", "                \n", "            except Exception as e:\n", "                # Handle potential errors and add empty responses\n", "                row['happierleads_response'] = f\"Error: {str(e)}\"\n", "                row['leadfeeder_response'] = f\"Error: {str(e)}\"\n", "                row['happierleads_domain'] = ''\n", "                row['happierleads_type'] = ''\n", "                row['leadfeeder_domain'] = ''\n", "            \n", "            # Write the updated row to the output CSV file\n", "            writer.writerow(row)\n", "\n", "print(f\"Finished processing. Results saved to {output_file}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}