{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from django.forms.models import model_to_dict\n", "from api.models import Content, Playbook\n", "\n", "\n", "def copy_contents(\n", "    contents_list_to_copy,\n", "    playbook_mapping={},\n", "    content_mapping={},\n", "    default_user=None,\n", "    default_playbook=None,\n", "    eval_prefix=None,\n", "):\n", "    update_only = all(content in content_mapping for content in contents_list_to_copy)\n", "    if not update_only:\n", "        if not playbook_mapping and not default_user:\n", "            raise Exception(\"both playbook_mapping and default_user are not set\")\n", "        if not playbook_mapping and not default_playbook:\n", "            raise Exception(\"both playbook_mapping and default_playbook are not set\")\n", "\n", "    contents_copied = []\n", "    for content in Content.objects.filter(id__in=contents_list_to_copy):\n", "        if content.content_group is not None:\n", "            print(f\"Error: campaign is not supported yet for {content.id}\")\n", "            continue\n", "\n", "        print(f\"copy for {content.id}\")\n", "        if content.id in content_mapping:\n", "            # content mapping exist, do copy\n", "            source_dict = model_to_dict(content)\n", "            target_content = Content.objects.get(pk=content_mapping[content.id])\n", "            # Update fields from the source instance to the target instance, skipping specified fields\n", "            for field, value in source_dict.items():\n", "                if field not in [\"creator\", \"playbook\", \"content_name\"]:\n", "                    setattr(target_content, field, value)\n", "\n", "            # Save the updated target instance\n", "            target_content.save()\n", "\n", "            # copy content report\n", "            contents_copied.append(content.id)\n", "            print(\n", "                f\"updated existing content: {content.id} to {content_mapping[content.id]}\"\n", "            )\n", "            continue\n", "        else:\n", "            # create new content\n", "            playbook = content.playbook\n", "\n", "            if default_playbook:\n", "                copy_playbook = Playbook.objects.get(pk=default_playbook)\n", "            elif playbook.id not in playbook_mapping:\n", "                print(\n", "                    f\"Error: playbook {playbook.id} does not have a copy for content {content.id}\"\n", "                )\n", "                continue\n", "            else:\n", "                copy_playbook = Playbook.objects.filter(\n", "                    pk=playbook_mapping[playbook.id]\n", "                )[0]\n", "\n", "            if default_user:\n", "                copy_user = TofuUser.objects.get(pk=default_user)\n", "            else:\n", "                copy_user = copy_playbook.users.all()[0]\n", "\n", "            copy_content_data = model_to_dict(content)\n", "            copy_content_data.update(\n", "                {\n", "                    \"creator\": copy_user,\n", "                    \"playbook\": copy_playbook,\n", "                    \"content_name\": content.content_name\n", "                    if not eval_prefix\n", "                    else f\"{eval_prefix}-{content.content_name}\",\n", "                }\n", "            )\n", "            # Create a new content instance with the new content\n", "            copy_content = Content.objects.create(**copy_content_data)\n", "\n", "            # Step 2: Save the new instance to the database\n", "            copy_content.save()\n", "\n", "            print(\n", "                f\"PLEASE UPDATE content_mapping for: {content.id}:{copy_content.id}, # {copy_playbook.users.all()[0].username}\"\n", "            )\n", "            contents_copied.append(content.id)\n", "\n", "    print(\n", "        f\"not copied: {[x for x in contents_list_to_copy if x not in contents_copied]}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# copy for re-generation\n", "\n", "eval_prefix = \"eval_batch_2023_08_21\"\n", "# Fetch all contents\n", "contents_list_to_copy = []\n", "\n", "copy_contents(\n", "    contents_list_to_copy=contents_list_to_copy,\n", "    eval_prefix=eval_prefix,\n", "    default_user=default_user,\n", "    default_playbook=default_playbook,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# copy for benchmark\n", "\n", "# please remember to update this map after copy\n", "content_mapping = {\n", "    1537: 1560,  # tofuadmin-eval-stampli\n", "    1367: 1581,  # tofuadmin-eval-stampli\n", "    1169: 1582,  # tofuadmin-eval-igloo\n", "    1539: 1584,  # tofuadmin-eval-stampli\n", "    1165: 1585,  # tofuadmin-eval-igloo\n", "    1593: 1596,  # tofuadmin-eval-stampli\n", "    1511: 1605,  # tofuadmin-eval-uipath\n", "    945: 1606,  # tofuadmin-eval-uipath\n", "    966: 1607,  # tofuadmin-eval-uipath\n", "    1049: 1608,  # tofuadmin-eval-uipath\n", "}\n", "# Please update playbook to avoid creating new ones\n", "playbook_mapping = {\n", "    13: 115,  # tofuadmin-eval-stampli\n", "    69: 117,  # tofuadmin-eval-igloo\n", "    74: 118,  # tofuadmin-eval-uipath\n", "}\n", "user_mapping = {\n", "    12: 119,  # tofuadmin-eval-stampli\n", "    70: 121,  # tofuadmin-eval-igloo\n", "    78: 122,  # tofuadmin-eval-uipath\n", "}\n", "\n", "# Fetch all contents\n", "contents_list_to_copy = [1049]\n", "\n", "copy_contents(\n", "    contents_list_to_copy=contents_list_to_copy,\n", "    content_mapping=content_mapping,\n", "    playbook_mapping=playbook_mapping,\n", "    eval_prefix=\"Eval:\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# delete metric contents\n", "eval_prefix = \"eval_batch_2023_08_21\"\n", "query_set = Content.objects.filter(content_name__startswith=eval_prefix)\n", "print(\"to delete:\", query_set)\n", "# query_set.delete()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}