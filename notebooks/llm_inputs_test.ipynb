{"cells": [{"cell_type": "code", "execution_count": null, "id": "5bc18d20-7109-4a31-b375-b9df8f70559a", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from api.release_tests.llm_inout_check.llm_check_runner import LLMInOutCheckRunner\n", "\n", "\n", "LLMInOutCheckRunner.update_golden_set()"]}, {"cell_type": "code", "execution_count": null, "id": "b5dde9a4-30f8-4fb3-8787-8f71c935f553", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.release_tests.llm_inout_check.llm_check_runner import LLMInOutCheckRunner\n", "\n", "all_pass, errors = LLMInOutCheckRunner.test_with_golden_set()\n", "if not all_pass:\n", "    for error in errors:\n", "        print(error)\n", "else:\n", "    print(errors)"]}, {"cell_type": "code", "execution_count": 2, "id": "ff348c54-5102-4186-8697-4a99bc384740", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-05-28 15:38:10,456 INFO: Checking test result for LLM inout: test-beea27dd-e8f0-4df8-bfa6-e5040524cf53\n", "2024-05-28 15:38:22,983 INFO: list_runs: 368\n", "2024-05-28 15:38:36,885 INFO: number of test_llm_runs: 46, golden_llm_runs: 46\n", "2024-05-28 15:38:37,804 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/b874ee61-40d7-4f94-9ab5-dbbe6b692c62.html\n", "2024-05-28 15:38:37,810 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/8ddd9a70-6197-4148-b619-fd1da073900a.html\n", "2024-05-28 15:38:37,815 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/e89c8c55-96c0-4d16-b6fb-fc6b09357f62.html\n", "2024-05-28 15:38:37,825 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/1af6581c-1036-4128-a40a-30aeaed0effa.html\n", "2024-05-28 15:38:37,863 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/020e7a5e-541c-4a8b-998b-755f71822d5a.html\n", "2024-05-28 15:38:37,865 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/aeec7bf7-e50f-454c-9739-b9e9cf164f56.html\n", "2024-05-28 15:38:37,868 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/678f6306-1dd1-4991-80dd-b04922bc5e27.html\n", "2024-05-28 15:38:37,899 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/c99a0313-3939-4b53-8d54-5bb7483f4f2b.html\n", "2024-05-28 15:38:37,939 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/2dbc1540-0d1d-4cac-b185-b02f2576b3ef.html\n", "2024-05-28 15:38:38,216 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/c0e5da65-2af0-42ff-87eb-0a27027b9233.html\n", "2024-05-28 15:38:39,307 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/1af6581c-1036-4128-a40a-30aeaed0effa.html\n", "2024-05-28 15:38:39,341 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/8ddd9a70-6197-4148-b619-fd1da073900a.html\n", "2024-05-28 15:38:39,374 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/2dbc1540-0d1d-4cac-b185-b02f2576b3ef.html\n", "2024-05-28 15:38:39,395 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/aeec7bf7-e50f-454c-9739-b9e9cf164f56.html\n", "2024-05-28 15:38:39,502 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/c0e5da65-2af0-42ff-87eb-0a27027b9233.html\n", "2024-05-28 15:38:39,541 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/678f6306-1dd1-4991-80dd-b04922bc5e27.html\n", "2024-05-28 15:38:39,585 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/b874ee61-40d7-4f94-9ab5-dbbe6b692c62.html\n", "2024-05-28 15:38:39,618 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/020e7a5e-541c-4a8b-998b-755f71822d5a.html\n", "2024-05-28 15:38:39,663 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/e89c8c55-96c0-4d16-b6fb-fc6b09357f62.html\n", "2024-05-28 15:38:39,669 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/c99a0313-3939-4b53-8d54-5bb7483f4f2b.html\n", "2024-05-28 15:38:40,110 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/429c98f0-f4a9-4af6-9adc-8882ea656393.html\n", "2024-05-28 15:38:40,123 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/d714aaeb-8028-4bb7-8206-b214b617864e.html\n", "2024-05-28 15:38:40,194 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/dd98b85d-8232-4aac-8fc8-8f19248b3e5d.html\n", "2024-05-28 15:38:40,199 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/a12efbe1-4853-43e3-868a-259e5451abe9.html\n", "2024-05-28 15:38:40,258 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/3e7d9e61-1519-4341-baf4-81dd5649e8d4.html\n", "2024-05-28 15:38:40,273 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/baeba6a2-7e03-4509-ab5f-56134bfabb32.html\n", "2024-05-28 15:38:40,443 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/61dce3c1-6573-4587-8b41-d24871c1be75.html\n", "2024-05-28 15:38:40,498 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/785ad90a-8db6-4598-bdae-37913da3e795.html\n", "2024-05-28 15:38:40,509 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/b1850a3f-48ee-4e83-b8ed-61d82e5806bb.html\n", "2024-05-28 15:38:40,514 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/ee88122a-0a98-4b21-8e86-ae652ffb0198.html\n", "2024-05-28 15:38:41,280 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/429c98f0-f4a9-4af6-9adc-8882ea656393.html\n", "2024-05-28 15:38:41,443 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/dd98b85d-8232-4aac-8fc8-8f19248b3e5d.html\n", "2024-05-28 15:38:41,565 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/3e7d9e61-1519-4341-baf4-81dd5649e8d4.html\n", "2024-05-28 15:38:41,635 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/d714aaeb-8028-4bb7-8206-b214b617864e.html\n", "2024-05-28 15:38:41,686 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/b1850a3f-48ee-4e83-b8ed-61d82e5806bb.html\n", "2024-05-28 15:38:41,715 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/baeba6a2-7e03-4509-ab5f-56134bfabb32.html\n", "2024-05-28 15:38:41,782 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/a12efbe1-4853-43e3-868a-259e5451abe9.html\n", "2024-05-28 15:38:41,828 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/e995f24f-d64e-4e77-9ecd-3f3f7daf912e.html\n", "2024-05-28 15:38:41,854 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/ee88122a-0a98-4b21-8e86-ae652ffb0198.html\n", "2024-05-28 15:38:41,904 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/785ad90a-8db6-4598-bdae-37913da3e795.html\n", "2024-05-28 15:38:42,005 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/61dce3c1-6573-4587-8b41-d24871c1be75.html\n", "2024-05-28 15:38:42,218 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/a5c2aa24-759e-43b4-919b-c746db895eb9.html\n", "2024-05-28 15:38:42,289 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/1d8fbad5-5fd3-4155-a9c2-267caf074bfc.html\n", "2024-05-28 15:38:42,306 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/f4e3e43e-ec5e-4754-a071-86674bc0aac6.html\n", "2024-05-28 15:38:42,320 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/84320bb0-0c5e-449b-a3eb-bbc0524c68c9.html\n", "2024-05-28 15:38:42,393 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/7eda400c-da52-4d17-ada4-fb81ecd29d70.html\n", "2024-05-28 15:38:42,560 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/a568adca-dcbd-4e4c-9c0f-88038be80320.html\n", "2024-05-28 15:38:42,670 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/fbbb14e6-9a6b-405c-88c9-6d218f1269b8.html\n", "2024-05-28 15:38:42,674 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/c4dd87b0-aeb2-4916-beae-28f93014b4bd.html\n", "2024-05-28 15:38:42,968 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/3cb261ec-a48d-4a07-84bc-d4f4ecf7955a.html\n", "2024-05-28 15:38:43,397 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/1d8fbad5-5fd3-4155-a9c2-267caf074bfc.html\n", "2024-05-28 15:38:43,475 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/7eda400c-da52-4d17-ada4-fb81ecd29d70.html\n", "2024-05-28 15:38:43,520 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/a5c2aa24-759e-43b4-919b-c746db895eb9.html\n", "2024-05-28 15:38:43,684 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/a568adca-dcbd-4e4c-9c0f-88038be80320.html\n", "2024-05-28 15:38:43,687 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/84320bb0-0c5e-449b-a3eb-bbc0524c68c9.html\n", "2024-05-28 15:38:43,728 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/f4e3e43e-ec5e-4754-a071-86674bc0aac6.html\n", "2024-05-28 15:38:43,805 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/c4dd87b0-aeb2-4916-beae-28f93014b4bd.html\n", "2024-05-28 15:38:43,864 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/fbbb14e6-9a6b-405c-88c9-6d218f1269b8.html\n", "2024-05-28 15:38:44,129 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/e995f24f-d64e-4e77-9ecd-3f3f7daf912e.html\n", "2024-05-28 15:38:44,176 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/3cb261ec-a48d-4a07-84bc-d4f4ecf7955a.html\n", "2024-05-28 15:38:44,196 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/d43bc24a-95c1-48eb-b9ca-315bf7916e2a.html\n", "2024-05-28 15:38:44,203 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/5fa9247c-3065-47dd-aaf8-24c1b0f95e7a.html\n", "2024-05-28 15:38:44,257 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/3c49b478-efa6-463e-8dda-e9fb790f1c26.html\n", "2024-05-28 15:38:44,334 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/85b92455-4742-4193-997a-cf47e26f8fcb.html\n", "2024-05-28 15:38:44,341 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/bd3dc472-2307-4c9c-a34b-6cdc1650d070.html\n", "2024-05-28 15:38:44,400 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/0e2f5b8f-09a5-4923-894f-30dd566d6c10.html\n", "2024-05-28 15:38:44,441 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/dd34ad19-4d1f-4a65-95c9-60ae3d6a5b31.html\n", "2024-05-28 15:38:44,488 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/3cb44235-622f-4f2d-a796-e8670838bb2d.html\n", "2024-05-28 15:38:44,758 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/d761957d-52f8-45cb-9ced-b481673ae0dd.html\n", "2024-05-28 15:38:44,790 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/b623dd02-84b0-44a7-96a5-cb019b64a1ab.html\n", "2024-05-28 15:38:45,276 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/d43bc24a-95c1-48eb-b9ca-315bf7916e2a.html\n", "2024-05-28 15:38:45,350 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/3c49b478-efa6-463e-8dda-e9fb790f1c26.html\n", "2024-05-28 15:38:45,434 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/5fa9247c-3065-47dd-aaf8-24c1b0f95e7a.html\n", "2024-05-28 15:38:45,462 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/0e2f5b8f-09a5-4923-894f-30dd566d6c10.html\n", "2024-05-28 15:38:45,484 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/dd34ad19-4d1f-4a65-95c9-60ae3d6a5b31.html\n", "2024-05-28 15:38:45,498 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/3cb44235-622f-4f2d-a796-e8670838bb2d.html\n", "2024-05-28 15:38:45,610 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/bd3dc472-2307-4c9c-a34b-6cdc1650d070.html\n", "2024-05-28 15:38:45,802 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/d761957d-52f8-45cb-9ced-b481673ae0dd.html\n", "2024-05-28 15:38:45,847 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/18709213-52a1-49c6-8452-9dd53b87d930.html\n", "2024-05-28 15:38:45,892 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/a0584694-5502-4676-a797-85aafb9d8347.html\n", "2024-05-28 15:38:46,080 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/0b10d58b-0395-48e3-ae5d-44f1d236b0b6.html\n", "2024-05-28 15:38:46,087 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/db871bcd-af5b-4f07-a7d4-78667f2a06c9.html\n", "2024-05-28 15:38:46,091 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/85b92455-4742-4193-997a-cf47e26f8fcb.html\n", "2024-05-28 15:38:46,098 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/97e1184c-f0e7-4748-8d7c-7a0011247536.html\n", "2024-05-28 15:38:46,101 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/b623dd02-84b0-44a7-96a5-cb019b64a1ab.html\n", "2024-05-28 15:38:46,130 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/inputs/4197b0b7-2d71-4ee1-b349-1867b7f140c7.html\n", "2024-05-28 15:38:46,900 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/a0584694-5502-4676-a797-85aafb9d8347.html\n", "2024-05-28 15:38:46,977 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/18709213-52a1-49c6-8452-9dd53b87d930.html\n", "2024-05-28 15:38:47,142 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/db871bcd-af5b-4f07-a7d4-78667f2a06c9.html\n", "2024-05-28 15:38:47,240 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/97e1184c-f0e7-4748-8d7c-7a0011247536.html\n", "2024-05-28 15:38:47,283 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/4197b0b7-2d71-4ee1-b349-1867b7f140c7.html\n", "2024-05-28 15:38:47,321 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/outputs/0b10d58b-0395-48e3-ae5d-44f1d236b0b6.html\n", "2024-05-28 15:38:47,939 INFO: link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/llm_report.html\n", "2024-05-28 15:38:47,941 INFO: final_report_brief: Error cases: 5, Warning cases: 17\n", "Link to the report: https://tofu-public-files.s3.amazonaws.com/llm_inouts_diff/testrun_test-beea27dd-e8f0-4df8-bfa6-e5040524cf53/llm_report.html\n"]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.release_tests.llm_inout_check.llm_check_runner import LLMInOutCheckRunner\n", "\n", "all_pass, errors = LLMInOutCheckRunner.check_test_result(\"test-beea27dd-e8f0-4df8-bfa6-e5040524cf53\")\n", "# all_pass, errors = LLMInOutCheckRunner.check_test_result(\"test-6a19ed4c-bc30-42e5-a34a-d5ffa1a06732\")"]}, {"cell_type": "code", "execution_count": null, "id": "5d8e30dc-447c-497b-9ef1-e04862eb12fa", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from api.release_tests.llm_check_runner import LLMInOutCheckRunner\n", "\n", "\n", "LLMInOutCheckRunner.get_latest_golden_tag()"]}, {"cell_type": "code", "execution_count": null, "id": "94ccea57-0f5b-4c59-9243-275f6a145ddb", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "# from api.release_tests.llm_check_runner import LLMInOutCheckRunner\n", "from api.release_tests.llm_inout_check.llm_output_check import LLMOutputChecker\n", "from api.release_tests.llm_inout_check.llm_inout_report import LLMReporter\n", "\n", "test_run_id=\"c7c355ea-2c4d-40fc-bff1-0468acf158ef\"\n", "golden_run_id=\"fc06dde4-952f-495b-ae07-6dccae77955f\"\n", "from langsmith import Client\n", "\n", "client = Client()\n", "runs = client.list_runs(id=[test_run_id, golden_run_id])\n", "runs = list(runs)\n", "print(len(runs))\n", "\n", "test_run = runs[0]\n", "golden_run = runs[1]\n", "\n", "report = LLMReporter(\"abc\")\n", "report.init_test(test_run, golden_run, {})\n", "checker = LLMOutputChecker(report, test_run, golden_run)\n", "\n", "\n", "checker.check()"]}, {"cell_type": "code", "execution_count": null, "id": "af8cc91f-7608-4042-ab14-7939e53d3b4e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}