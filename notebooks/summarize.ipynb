{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.document_loaders import WebBaseLoader\n", "from langchain.chat_models import ChatOpenAI\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.prompts import PromptTemplate\n", "from langchain.chains.summarize import load_summarize_chain\n", "import sys\n", "import os\n", "\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir)\n", "from server.api.data_loaders.url_loader import TofuURLLoader\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "# loader = WebBaseLoader([\"https://eand.co/is-britain-finally-ready-to-admit-brexit-was-a-catastrophic-mistake-60b9a76a585c\"])\n", "\n", "# loader = WebBaseLoader([\"https://tofuhq.com\"])\n", "loader = TofuURLLoader(\"stampli.com\")\n", "documents = loader.load()\n", "print(documents)\n", "\n", "llm = ChatOpenAI(\n", "    model_name=\"gpt-3.5-turbo\",\n", "    temperature=0,\n", "    max_tokens=None,  # this will set the max_tokens to default on openai side which is inf\n", ")\n", "\n", "summary_length = 512\n", "prompt_template = f\"\"\"Write a summary of the following. Only use the information provided and do not make stuff up by adding information that are not present in the following. However, you absolutely MUST (super important) try your best to include as many details as possible in the summary, as long as it is not longer than {summary_length} words.\n", "\n", "{{text}}\n", "\n", "\"\"\"\n", "# prompt_template = f\"\"\"Write a {summary_length} words summary (could be shorter if you don't have enough information). Only use the information provided and and do not make stuff up. Include as many details as possible.\n", "\n", "# {{text}}\n", "\n", "# \"\"\"\n", "PROMPT = PromptTemplate(template=prompt_template, input_variables=[\"text\"])\n", "total_char_length = sum(len(doc.page_content) for doc in documents)\n", "print(f\"Total char length from all the fetched content: {total_char_length}\")\n", "if total_char_length > 12000:\n", "    print(\"Using map reduce chain to generate summary...\")\n", "    text_splitter = RecursiveCharacterTextSplitter(\n", "        chunk_size=12000,\n", "        chunk_overlap=600,\n", "    )\n", "    text_chuncks = text_splitter.split_documents(documents)\n", "    chain = load_summarize_chain(\n", "        llm, combine_prompt=PROMPT, chain_type=\"map_reduce\", verbose=False\n", "    )\n", "    summary = chain.run(text_chuncks)\n", "else:\n", "    print(\"Using stuff chain to generate summary...\")\n", "    chain = load_summarize_chain(llm, prompt=PROMPT, chain_type=\"stuff\", verbose=False)\n", "    summary = chain.run(documents)\n", "print(summary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.document_loaders import GoogleDriveLoader\n", "\n", "loader = GoogleDriveLoader(\n", "    service_account_key=\"../server/.credentials/google_service_account_key.json\",\n", "    folder_id=\"1Ces93ghkr42LIAXUVfnYR56skufyG73l\",\n", "    # Optional: configure whether to recursively fetch files from subfolders. Defaults to False.\n", "    recursive=False,\n", ")\n", "docs = loader.load()\n", "print(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_summary(self, docs, current_summary, **kwargs):\n", "    \"\"\"\n", "    Refine current summary with the changed data\n", "    summary_length: number of tokens in the summary\n", "    \"\"\"\n", "    logging.info(\"Updating summary...\")\n", "    if not docs:\n", "        logging.warning(\"No docs to update summary\")\n", "        return current_summary\n", "\n", "    model_budget, llm = get_llm_for_refine_summarization()\n", "\n", "    # load configs\n", "    summary_length = kwargs.get(\"summary_length\", 600)\n", "    buffer_budget = kwargs.get(\"buffer_budget\", 500)\n", "    upper_rewrite_limit = kwargs.get(\"upper_rewrite_limit\", 1.2)\n", "\n", "    # calculate budget\n", "    context_budget = model_budget - summary_length - buffer_budget\n", "    chunk_size = context_budget - 100\n", "\n", "    text_splitter = SentenceSplitter(\n", "        chunk_size=chunk_size,\n", "        chunk_overlap=20,\n", "    )\n", "    text_chunks = text_splitter.split_documents(docs)\n", "    optimized_text_chunks = DocLoader.optimize_docs(text_chunks)\n", "\n", "    # TODO: right now, the refinement chain doesn't work well with deletion, so we are exluding deletion for now\n", "    optimized_text_chunks = [\n", "        doc\n", "        for doc in optimized_text_chunks\n", "        if doc.metadata.get(\"change_type\", None) != \"deleted\"\n", "    ]\n", "\n", "    # Break optimized_text_chunks into batches, and each batch should have total len(doc.page_content) less than 4 * CONTEXT_BUDGET\n", "    # This is to avoid the case where a single doc is too long and exceed the token limit\n", "    batches = []\n", "    batch = []\n", "    batch_len = 0\n", "    for chunk in optimized_text_chunks:\n", "        chunk_len = get_token_count(chunk.page_content)\n", "        if batch and batch_len + chunk_len > context_budget:\n", "            batches.append(batch)\n", "            batch = []\n", "            batch_len = 0\n", "        batch.append(chunk)\n", "        batch_len += chunk_len\n", "    if batch:\n", "        batches.append(batch)\n", "    logging.info(f\"Divided data into {len(batches)} batches\")\n", "    for i, text_chunck_batch in enumerate(batches):\n", "        logging.info(f\"Refining summary with data in batch {i+1} ...\")\n", "        chain = load_summarize_chain(\n", "            llm=llm,\n", "            document_prompt=REFINE_SUMMARY_DOCUMENT_FORMAT,\n", "            prompt=REFINE_SUMMARY,\n", "            chain_type=\"stuff\",\n", "            verbose=False,\n", "        )\n", "        current_summary = chain.invoke(\n", "            input={\n", "                \"summary_length\": int(summary_length * 0.75),\n", "                \"input_documents\": text_chunck_batch,\n", "                \"current_summary\": current_summary,\n", "            },\n", "            return_only_outputs=True,\n", "        ).get(\"output_text\")\n", "\n", "    if len(current_summary.split()) > upper_rewrite_limit * summary_length:\n", "        logging.info(\"Summary too long. Truncating...\")\n", "        logging.info(f\"Original summary length: {len(current_summary.split())}\")\n", "        current_summary = rewrite_length_limit(\n", "            None, [], current_summary, summary_length\n", "        )\n", "        logging.info(f\"New summary length: {len(current_summary.split())}\")\n", "    return current_summary"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}