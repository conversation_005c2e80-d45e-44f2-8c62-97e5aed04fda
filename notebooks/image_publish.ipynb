{"cells": [{"cell_type": "code", "execution_count": null, "id": "66fcf52d-3cd2-4591-8813-4314a7d739b6", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.utils import publish_image, get_published_cdn_image_url, get_published_image_url\n", "\n", "image_url = \"/api/web/storage/s3-presigned-url?file=084a273115fa472958b8b49637a14522.jpg&fileType=image/jpeg&directory=tofu-uploaded-files\"\n", "\n", "publish_image(image_url)"]}, {"cell_type": "code", "execution_count": null, "id": "a1524913-b3d8-4a65-8628-2208d672c279", "metadata": {}, "outputs": [], "source": ["\n", "\n", "import boto3\n", "client = boto3.client(\"cloudfront\")\n", "def list():\n", "    \"\"\"Lists all distributions in Cloudfront.\n", "\n", "    Relies on NextMarker to paginate through all distributions.\n", "\n", "    Returns:\n", "        List: List of distributions.\n", "    \"\"\"\n", "    response = client.list_distributions()\n", "    distributions = []\n", "    while True:\n", "        for dist in response[\"DistributionList\"][\"Items\"]:\n", "            distributions.append(dist)\n", "        if \"NextMarker\" not in response[\"DistributionList\"]:\n", "            break\n", "\n", "        response = client.list_distributions(Marker=response[\"DistributionList\"][\"NextMarker\"])\n", "    return distributions\n", "\n", "list()"]}, {"cell_type": "code", "execution_count": null, "id": "0d62a89b-0857-4fad-a853-67fb32b16bee", "metadata": {}, "outputs": [], "source": ["get_published_cdn_image_url(image_url)"]}, {"cell_type": "code", "execution_count": null, "id": "5d8e30dc-447c-497b-9ef1-e04862eb12fa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}