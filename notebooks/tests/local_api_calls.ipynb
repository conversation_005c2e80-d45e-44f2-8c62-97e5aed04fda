{"cells": [{"cell_type": "code", "execution_count": null, "id": "7df5e7fb-8abe-43ca-826a-f8e2e5e46005", "metadata": {}, "outputs": [], "source": ["import requests\n", "import base64\n", "import json\n", "import time\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from typing import Dict, List, Tuple, Any, Optional\n", "from collections import defaultdict\n", "import seaborn as sns\n", "\n", "# Configuration\n", "class Config:\n", "    API_SERVER = \"http://localhost:8000\"\n", "    USERNAME = \"tofuadmin-jian13\"\n", "    PASSWORD = \"\"\n", "    NUM_RUNS = 10  # Number of times to call each API\n", "    PLAYBOOK_ID = 24966  # Your playbook ID\n", "\n", "# Authentication\n", "class Auth:\n", "    @staticmethod\n", "    def authenticate(username: str, password: str) -> Dict:\n", "        \"\"\"Authenticate user and return user data\"\"\"\n", "        auth_url = f\"{Config.API_SERVER}/api/user/authentication/\"\n", "        payload = {\n", "            \"username\": username,\n", "            \"password\": password\n", "        }\n", "        \n", "        response = requests.post(auth_url, json=payload)\n", "        if response.status_code != 200:\n", "            raise Exception(f\"Authentication failed: {response.status_code} - {response.text}\")\n", "        \n", "        return response.json()\n", "    \n", "    @staticmethod\n", "    def get_access_token(username: str, password: str) -> str:\n", "        \"\"\"Get access token using Basic Auth\"\"\"\n", "        username_b64 = base64.b64encode(username.encode('utf-8')).decode('utf-8')\n", "        token_url = f\"{Config.API_SERVER}/api/user/{username_b64}/get_access_token/\"\n", "        \n", "        response = requests.get(token_url, auth=(username, password))\n", "        \n", "        if response.status_code != 200:\n", "            raise Exception(f\"Failed to get access token: {response.status_code} - {response.text}\")\n", "        \n", "        return response.json()['access_token']\n", "\n", "# API Client\n", "class APIClient:\n", "    def __init__(self, access_token: str):\n", "        self.access_token = access_token\n", "        self.headers = {\n", "            'Authorization': f\"Bearer {access_token}\",\n", "            'Content-Type': 'application/json'\n", "        }\n", "    \n", "    def call_api(self, endpoint: str, method: str = \"GET\", data: Dict = None) -> Tuple[Dict, float]:\n", "        \"\"\"\n", "        Make an API call and return response and latency\n", "        \n", "        Args:\n", "            endpoint: API endpoint (relative to API_SERVER)\n", "            method: HTTP method (GET, POST, etc.)\n", "            data: Request payload for POST/PUT\n", "            \n", "        Returns:\n", "            Tuple of (response_data, latency_in_seconds)\n", "        \"\"\"\n", "        url = f\"{Config.API_SERVER}{endpoint}\"\n", "        \n", "        start_time = time.time()\n", "        \n", "        if method.upper() == \"GET\":\n", "            response = requests.get(url, headers=self.headers)\n", "        elif method.upper() == \"POST\":\n", "            response = requests.post(url, headers=self.headers, json=data)\n", "        else:\n", "            raise ValueError(f\"Unsupported HTTP method: {method}\")\n", "        \n", "        end_time = time.time()\n", "        latency = end_time - start_time\n", "        \n", "        if response.status_code != 200:\n", "            print(f\"API call failed: {response.status_code} - {response.text}\")\n", "            return None, latency\n", "        \n", "        return response.json(), latency\n", "\n", "# API Test Framework\n", "class APITester:\n", "    def __init__(self, client: APIClient):\n", "        self.client = client\n", "        self.results = defaultdict(list)\n", "    \n", "    def test_endpoint(self, name: str, endpoint: str, method: str = \"GET\", data: Dict = None, runs: int = Config.NUM_RUNS) -> Dict:\n", "        \"\"\"\n", "        Test an API endpoint multiple times and collect latency metrics\n", "        \n", "        Args:\n", "            name: Descriptive name for the test\n", "            endpoint: API endpoint to test\n", "            method: HTTP method\n", "            data: Request data for POST/PUT\n", "            runs: Number of test runs\n", "            \n", "        Returns:\n", "            Dictionary with test results\n", "        \"\"\"\n", "        print(f\"\\n--- Testing {name} ({runs} runs) ---\")\n", "        \n", "        latencies = []\n", "        success_count = 0\n", "        \n", "        for i in range(runs):\n", "            print(f\"  Run {i+1}/{runs}...\", end=\"\")\n", "            \n", "            try:\n", "                response, latency = self.client.call_api(endpoint, method, data)\n", "                latencies.append(latency)\n", "                \n", "                if response is not None:\n", "                    success_count += 1\n", "                    print(f\" Success - {latency:.3f}s\")\n", "                else:\n", "                    print(f\" Failed - {latency:.3f}s\")\n", "            except Exception as e:\n", "                print(f\" Error: {str(e)}\")\n", "        \n", "        # Calculate metrics\n", "        if latencies:\n", "            metrics = {\n", "                'name': name,\n", "                'endpoint': endpoint,\n", "                'method': method,\n", "                'runs': runs,\n", "                'success_rate': success_count / runs * 100,\n", "                'latencies': latencies,\n", "                'avg_latency': np.mean(latencies),\n", "                'min_latency': np.min(latencies),\n", "                'max_latency': np.max(latencies),\n", "                'p50_latency': np.percentile(latencies, 50),\n", "                'p80_latency': np.percentile(latencies, 80),\n", "                'p90_latency': np.percentile(latencies, 90),\n", "                'p95_latency': np.percentile(latencies, 95),\n", "                'p99_latency': np.percentile(latencies, 99)\n", "            }\n", "        else:\n", "            metrics = {\n", "                'name': name,\n", "                'endpoint': endpoint,\n", "                'method': method,\n", "                'runs': runs,\n", "                'success_rate': 0,\n", "                'latencies': [],\n", "            }\n", "        \n", "        self.results[name] = metrics\n", "        return metrics\n", "    \n", "    def print_results(self):\n", "        \"\"\"Print a summary of test results\"\"\"\n", "        print(\"\\n=== Test Results ===\")\n", "        \n", "        for name, metrics in self.results.items():\n", "            print(f\"\\n{name}:\")\n", "            print(f\"  Endpoint: {metrics['endpoint']}\")\n", "            print(f\"  Success Rate: {metrics['success_rate']:.1f}%\")\n", "            \n", "            if metrics['latencies']:\n", "                print(f\"  Avg Latency: {metrics['avg_latency']:.3f}s\")\n", "                print(f\"  Min/Max Latency: {metrics['min_latency']:.3f}s / {metrics['max_latency']:.3f}s\")\n", "                print(f\"  P50 Latency: {metrics['p50_latency']:.3f}s\")\n", "                print(f\"  P80 Latency: {metrics['p80_latency']:.3f}s\")\n", "                print(f\"  P90 Latency: {metrics['p90_latency']:.3f}s\")\n", "                print(f\"  P95 Latency: {metrics['p95_latency']:.3f}s\")\n", "    \n", "    def plot_results(self):\n", "        \"\"\"Generate visualizations of test results\"\"\"\n", "        if not self.results:\n", "            print(\"No results to plot\")\n", "            return\n", "        \n", "        # Create DataFrame for easy plotting\n", "        df_data = []\n", "        for name, metrics in self.results.items():\n", "            for latency in metrics['latencies']:\n", "                df_data.append({\n", "                    'API': name,\n", "                    'Latency (s)': latency\n", "                })\n", "        \n", "        df = pd.DataFrame(df_data)\n", "        \n", "        # Set up the matplotlib figure\n", "        plt.figure(figsize=(14, 10))\n", "        \n", "        # Plot 1: Boxplot of latencies\n", "        plt.subplot(2, 1, 1)\n", "        sns.boxplot(x='API', y='Latency (s)', data=df)\n", "        plt.title('API Latency Distribution')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        \n", "        # Plot 2: Bar chart of percentiles\n", "        plt.subplot(2, 1, 2)\n", "        metrics_df = pd.DataFrame([\n", "            {\n", "                'API': name,\n", "                'Avg': metrics['avg_latency'],\n", "                'P50': metrics['p50_latency'],\n", "                'P80': metrics['p80_latency'],\n", "                'P90': metrics['p90_latency'],\n", "                'P95': metrics['p95_latency']\n", "            }\n", "            for name, metrics in self.results.items() if 'p50_latency' in metrics\n", "        ])\n", "        \n", "        metrics_df.set_index('API').plot(kind='bar', figsize=(10, 6))\n", "        plt.title('API Latency Metrics')\n", "        plt.ylabel('Latency (s)')\n", "        plt.xticks(rotation=45)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        # Create a table with all metrics\n", "        summary_data = [\n", "            {\n", "                'API': name,\n", "                'Success Rate (%)': f\"{metrics['success_rate']:.1f}\",\n", "                'Avg (s)': f\"{metrics.get('avg_latency', 'N/A') if isinstance(metrics.get('avg_latency'), (int, float)) else 'N/A':.3f}\",\n", "                'Min (s)': f\"{metrics.get('min_latency', 'N/A') if isinstance(metrics.get('min_latency'), (int, float)) else 'N/A':.3f}\",\n", "                'Max (s)': f\"{metrics.get('max_latency', 'N/A') if isinstance(metrics.get('max_latency'), (int, float)) else 'N/A':.3f}\",\n", "                'P50 (s)': f\"{metrics.get('p50_latency', 'N/A') if isinstance(metrics.get('p50_latency'), (int, float)) else 'N/A':.3f}\",\n", "                'P80 (s)': f\"{metrics.get('p80_latency', 'N/A') if isinstance(metrics.get('p80_latency'), (int, float)) else 'N/A':.3f}\",\n", "                'P90 (s)': f\"{metrics.get('p90_latency', 'N/A') if isinstance(metrics.get('p90_latency'), (int, float)) else 'N/A':.3f}\",\n", "                'P95 (s)': f\"{metrics.get('p95_latency', 'N/A') if isinstance(metrics.get('p95_latency'), (int, float)) else 'N/A':.3f}\"\n", "            }\n", "            for name, metrics in self.results.items()\n", "        ]\n", "        \n", "        summary_df = pd.DataFrame(summary_data)\n", "        display(summary_df)\n", "\n", "# Main Test Execution\n", "def run_tests():\n", "    # Authentication\n", "    print(f\"Authenticating user: {Config.USERNAME}\")\n", "    user_data = Auth.authenticate(Config.USERNAME, Config.PASSWORD)\n", "    print(f\"Authenticated successfully: {user_data}\")\n", "    \n", "    print(\"Getting access token...\")\n", "    access_token = Auth.get_access_token(Config.USERNAME, Config.PASSWORD)\n", "    print(f\"Got access token...\")\n", "    \n", "    # Initialize API client and tester\n", "    client = APIClient(access_token)\n", "    tester = APITester(client)\n", "    \n", "    # Define API endpoints to test\n", "    endpoints = [\n", "        {\n", "            'name': 'List Playbooks',\n", "            'endpoint': '/api/playbook/',\n", "            'method': 'GET'\n", "        },\n", "        {\n", "            'name': 'Playbook Status',\n", "            'endpoint': f'/api/playbook/{Config.PLAYBOOK_ID}/status',\n", "            'method': 'GET'\n", "        },\n", "        # Add more endpoints here as needed\n", "    ]\n", "    \n", "    # Run tests for each endpoint\n", "    for endpoint in endpoints:\n", "        tester.test_endpoint(\n", "            name=endpoint['name'],\n", "            endpoint=endpoint['endpoint'],\n", "            method=endpoint['method']\n", "        )\n", "    \n", "    # Print and visualize results\n", "    tester.print_results()\n", "    tester.plot_results()\n", "    \n", "    return tester.results\n", "\n", "# Execute tests\n", "if __name__ == \"__main__\":\n", "    results = run_tests()"]}, {"cell_type": "code", "execution_count": null, "id": "a3a59696-83a5-4ffb-8231-f3b5204083bc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}