{"cells": [{"cell_type": "code", "execution_count": null, "id": "a1151cab-9b64-4b22-9e1f-3c051c6538f9", "metadata": {}, "outputs": [], "source": ["!pip install faker"]}, {"cell_type": "code", "execution_count": null, "id": "ac1e7b67-0749-44e9-b0c0-c36650501c59", "metadata": {}, "outputs": [], "source": ["import csv\n", "from faker import Faker\n", "\n", "def generate_fake_contacts(num_contacts):\n", "    fake = Faker()\n", "    contacts = []\n", "\n", "    for _ in range(num_contacts):\n", "        first_name = fake.first_name()\n", "        last_name = fake.last_name()\n", "        email = f\"{first_name}.{last_name}@{fake.free_email_domain()}\"\n", "        company = fake.company()\n", "        title = fake.job()\n", "\n", "        # Generate a fake company and website\n", "        company = fake.company()\n", "        domain = fake.domain_name()\n", "        website = f\"https://{domain}\"\n", "\n", "\n", "        contacts.append({\n", "            \"First Name\": first_name,\n", "            \"Last Name\": last_name,\n", "            \"Email\": email,\n", "            \"Company\": company,\n", "            \"Title\": title,\n", "            \"Website\": website,\n", "        })\n", "\n", "    return contacts\n", "\n", "def save_contacts_to_csv(contacts, filename):\n", "    with open(filename, mode='w', newline='', encoding='utf-8') as file:\n", "        writer = csv.DictWriter(file, fieldnames=[\"First Name\", \"Last Name\", \"Email\", \"Company\", \"Title\", \"Website\"])\n", "        writer.writeheader()\n", "        for contact in contacts:\n", "            writer.writer<PERSON>(contact)\n", "\n", "# Generate 1000 fake contacts\n", "fake_contacts = generate_fake_contacts(1000)\n", "\n", "# Save the contacts to a CSV file\n", "save_contacts_to_csv(fake_contacts, 'fake_contacts.csv')\n", "\n", "print(\"Fake contact data has been saved to 'fake_contacts.csv'\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}