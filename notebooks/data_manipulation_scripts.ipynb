{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Scripts to directly manipulate data in our database\n", "# Use them with !!EXTREME CAUSION!!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 2024-08-13\n", "# set all html_tag_index to null\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import ContentGroup, Content, ContentVariation\n", "import logging\n", "import copy\n", "\n", "all_content_groups = ContentGroup.objects.all()\n", "for content_group in all_content_groups:\n", "    components = copy.deepcopy(content_group.components)\n", "    if not components:\n", "        continue\n", "    for id, component_data in components.items():\n", "        if \"html_tag_index\" in component_data.get(\"meta\", {}):\n", "            components[id][\"meta\"][\"html_tag_index\"] = None\n", "    if components != content_group.components:\n", "        # logging.error(content_group)\n", "        # logging.error(f\"before: {content_group.components}\")\n", "        # logging.error(f\"after: {components}\")\n", "        content_group.components = components\n", "        # content_group.save()\n", "        # break\n", "\n", "all_contents = Content.objects.all()\n", "for content in all_contents:\n", "    components = copy.deepcopy(content.components)\n", "    if not components:\n", "        continue\n", "    for id, component_data in components.items():\n", "        if \"html_tag_index\" in component_data.get(\"meta\", {}):\n", "            components[id][\"meta\"][\"html_tag_index\"] = None\n", "    if components != content.components:\n", "        content.components = components\n", "        # content.save()\n", "        # break\n", "\n", "all_content_variations = ContentVariation.objects.all()\n", "for content_variation in all_content_variations:\n", "    variations = copy.deepcopy(content_variation.variations)\n", "    if not variations:\n", "        continue\n", "    for id, variation_data in variations.items():\n", "        if \"html_tag_index\" in variation_data.get(\"meta\", {}):\n", "            variations[id][\"meta\"][\"html_tag_index\"] = None\n", "    if variations != content_variation.variations:\n", "        content_variation.variations = variations\n", "        # content_variation.save()\n", "        # break\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 2024-07-09\n", "# remove domain from eval accounts\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import TofuUser, Playbook\n", "import logging\n", "\n", "playbooks = Playbook.objects.all()\n", "for playbook in playbooks:\n", "    if playbook.name.startswith(\"tofuadmin-eval\"):\n", "        if playbook.company_domain:\n", "            logging.error(f\"playbook {playbook.id} - {playbook.name} to check: {playbook.company_domain}\")\n", "            playbook.company_domain = None\n", "            playbook.save()\n", "        "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024-05-20\n", "# migrate content_type and content_source_format in content\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "cnt_updated = 0\n", "# Iterate over all contents\n", "for content in contents:\n", "    if not content.content_params:\n", "        continue\n", "    old_content_type = content.content_params.get(\"content_type\") or \"\"\n", "    \n", "    old_content_source_format =  content.content_params.get(\"content_source_format\") or \"\"\n", "\n", "    new_content_type = old_content_type\n", "    new_content_source_format = old_content_source_format\n", "    \n", "    if old_content_type == \"\" or old_content_type == \"Text\":\n", "        new_content_type = \"Other\"\n", "    elif old_content_type == \"Social Post\":\n", "        new_content_type = \"Social - General\"\n", "    elif old_content_type == \"Ad Campaign\":\n", "        new_content_type = \"Ad Campaign - General\"\n", "    elif old_content_type == \"Landing page\":\n", "        new_content_type = \"Landing Page\"\n", "    elif old_content_type == \"Email - marketing\":\n", "        new_content_type = \"Email - Marketing\"\n", "    elif old_content_type == \"ebook\":\n", "        new_content_type = \"eBook\"\n", "    elif old_content_type == \"LinkedIn Ads\":\n", "        new_content_type = \"Ad Campaign - LinkedIn\"\n", "    elif old_content_type == \"Linkedin\":\n", "        new_content_type = \"Social - LinkedIn\"\n", "\n", "    if old_content_source_format == \"\":\n", "        new_content_source_format = \"Text\"\n", "\n", "    if new_content_type == old_content_type and new_content_source_format == old_content_source_format:\n", "        continue\n", "\n", "    print(f\"before:{content.content_params.get('content_type')} => after {new_content_type}\")\n", "    content.content_params[\"content_type\"] = new_content_type\n", "    content.content_params[\"content_source_format\"] = new_content_source_format\n", "    content.save()\n", "    \n", "    cnt_updated += 1\n", "\n", "print(f\"{cnt_updated} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024-05-20\n", "# migrate content_type and content_source_format in content_group\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "\n", "# Fetch all contents\n", "content_groups = ContentGroup.objects.all()\n", "\n", "cnt_updated = 0\n", "# Iterate over all contents\n", "for content_group in content_groups:\n", "    if not content_group.content_group_params:\n", "        continue\n", "    old_content_type = content_group.content_group_params.get(\"content_type\") or \"\"\n", "    \n", "    old_content_source_format =  content_group.content_group_params.get(\"content_source_format\") or \"\"\n", "\n", "    new_content_type = old_content_type\n", "    new_content_source_format = old_content_source_format\n", "\n", "    \n", "    if old_content_type == \"\" or old_content_type == \"Text\":\n", "        new_content_type = \"Other\"\n", "    elif old_content_type == \"Social Post\":\n", "        new_content_type = \"Social - General\"\n", "    elif old_content_type == \"Ad Campaign\":\n", "        new_content_type = \"Ad Campaign - General\"\n", "    elif old_content_type == \"Landing page\":\n", "        new_content_type = \"Landing Page\"\n", "    elif old_content_type == \"Email - marketing\":\n", "        new_content_type = \"Email - Marketing\"\n", "    elif old_content_type == \"ebook\":\n", "        new_content_type = \"eBook\"\n", "    elif old_content_type == \"LinkedIn Ads\":\n", "        new_content_type = \"Ad Campaign - LinkedIn\"\n", "    elif old_content_type == \"Linkedin\":\n", "        new_content_type = \"Social - LinkedIn\"\n", "\n", "    if old_content_source_format == \"\":\n", "        new_content_source_format = \"Text\"\n", "\n", "    if new_content_type == old_content_type and new_content_source_format == old_content_source_format:\n", "        continue\n", "\n", "    print(f\"before:{content_group.content_group_params.get('content_type')} => after {new_content_type}\")\n", "    content_group.content_group_params[\"content_type\"] = new_content_type\n", "    content_group.content_group_params[\"content_source_format\"] = new_content_source_format\n", "    # content_group.save()\n", "    \n", "    cnt_updated += 1\n", "\n", "print(f\"{cnt_updated} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-11-09\n", "# migrate gen_status from params to status\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "contents = Content.objects.all()\n", "\n", "for content in contents:\n", "    if \"gen_status\" not in content.content_params:\n", "        continue\n", "    content.content_status[\"gen_status\"] = content.content_params[\"gen_status\"]\n", "    # content.save()\n", "\n", "content_groups = ContentGroup.objects.all()\n", "\n", "for content_group in content_groups:\n", "    if (\n", "        not content_group.content_group_params\n", "        or \"gen_status\" not in content_group.content_group_params\n", "    ):\n", "        continue\n", "    content_group.content_group_status[\n", "        \"gen_status\"\n", "    ] = content_group.content_group_params[\"gen_status\"]\n", "    # content_group.save()\n", "\n", "campaigns = Campaign.objects.all()\n", "\n", "for campaign in campaigns:\n", "    if not campaign.campaign_params or \"gen_status\" not in campaign.campaign_params:\n", "        continue\n", "    campaign.campaign_status[\"gen_status\"] = campaign.campaign_params[\"gen_status\"]\n", "    # campaign.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-10-10\n", "# migrate custom_instructions\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import copy\n", "\n", "\n", "def get_new_instructions(old_instructions):\n", "    new_instructions = []\n", "    for old_inst in old_instructions:\n", "        if isinstance(old_inst, str):\n", "            new_instructions.append(\n", "                {\n", "                    \"assets\": None,\n", "                    \"instruction\": old_inst,\n", "                }\n", "            )\n", "        else:\n", "            new_instructions.append(old_inst)\n", "    return new_instructions\n", "\n", "\n", "def get_new_components(old_components):\n", "    new_components = {}\n", "    has_change = False\n", "    for k, v in old_components.items():\n", "        new_components[k] = copy.deepcopy(v)\n", "        if (\n", "            \"component_params\" in v[\"meta\"]\n", "            and \"custom_instructions\" in v[\"meta\"][\"component_params\"]\n", "            and v[\"meta\"][\"component_params\"][\"custom_instructions\"]\n", "        ):\n", "            has_change = True\n", "            new_components[k][\"meta\"][\"component_params\"][\n", "                \"custom_instructions\"\n", "            ] = get_new_instructions(\n", "                v[\"meta\"][\"component_params\"][\"custom_instructions\"]\n", "            )\n", "    return has_change, new_components\n", "\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "print(\"=\" * 20 + \"Content update\")\n", "contents = Content.objects.all()\n", "\n", "num_updated_content = 0\n", "for content in contents:\n", "    is_changed = False\n", "    if (\n", "        content.content_params\n", "        and \"custom_instructions\" in content.content_params\n", "        and content.content_params[\"custom_instructions\"]\n", "    ):\n", "        old_instructions = content.content_params[\"custom_instructions\"]\n", "        new_instructions = get_new_instructions(old_instructions)\n", "        print(f\"in params: before:{old_instructions}\\nafter:{new_instructions}\\n\")\n", "        content.content_params[\"custom_instructions\"] = new_instructions\n", "        is_changed = True\n", "\n", "    if content.components:\n", "        old_components = content.components\n", "        has_change, new_components = get_new_components(old_components)\n", "        if has_change:\n", "            print(\n", "                f\"for content {content.id} in components before:{old_components}\\nafter:{new_components}\\n\"\n", "            )\n", "        content.components = new_components\n", "        is_changed = is_changed or has_change\n", "\n", "    if is_changed:\n", "        # content.save()\n", "        num_updated_content += 1\n", "        print(\"-\" * 20)\n", "\n", "print(\"=\" * 20 + \"ContentGroup update\")\n", "content_groups = ContentGroup.objects.all()\n", "\n", "num_updated_content_group = 0\n", "for content_group in content_groups:\n", "    is_changed = False\n", "    if (\n", "        content_group.content_group_params\n", "        and \"custom_instructions\" in content_group.content_group_params\n", "        and content_group.content_group_params[\"custom_instructions\"]\n", "    ):\n", "        old_instructions = content_group.content_group_params[\"custom_instructions\"]\n", "        new_instructions = get_new_instructions(old_instructions)\n", "        print(f\"in params before:{old_instructions}\\nafter:{new_instructions}\\n\")\n", "        content_group.content_group_params[\"custom_instructions\"] = new_instructions\n", "        is_changed = True\n", "\n", "    if content_group.components:\n", "        old_components = content_group.components\n", "        has_change, new_components = get_new_components(old_components)\n", "        if has_change:\n", "            print(\n", "                f\"in components for {content_group.id} before:{old_components}\\nafter:{new_components}\\n\"\n", "            )\n", "        content_group.components = new_components\n", "        is_changed = is_changed or has_change\n", "\n", "    if is_changed:\n", "        # content_group.save()\n", "        num_updated_content_group += 1\n", "        print(\"-\" * 20)\n", "\n", "\n", "print(\"=\" * 20 + \"Campaign update\")\n", "campaigns = Campaign.objects.all()\n", "\n", "num_updated_campaign = 0\n", "for campaign in campaigns:\n", "    is_changed = False\n", "    if (\n", "        campaign.campaign_params\n", "        and \"custom_instructions\" in campaign.campaign_params\n", "        and campaign.campaign_params[\"custom_instructions\"]\n", "    ):\n", "        old_instructions = campaign.campaign_params[\"custom_instructions\"]\n", "        new_instructions = get_new_instructions(old_instructions)\n", "        print(\n", "            f\"for campaign {campaign.id} before:{old_instructions}\\nafter:{new_instructions}\"\n", "        )\n", "        campaign.campaign_params[\"custom_instructions\"] = new_instructions\n", "        is_changed = True\n", "    if is_changed:\n", "        # campaign.save()\n", "        num_updated_campaign += 1\n", "\n", "print(f\"update contents: {num_updated_content}\")\n", "print(f\"update content_groups: {num_updated_content_group}\")\n", "print(f\"update campaigns: {num_updated_campaign}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-09-25\n", "# check Variations for content\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "contents = Content.objects.all()\n", "\n", "num_content_to_delete = 0\n", "\n", "for content in contents:\n", "    variations = ContentVariation.objects.filter(content=content).order_by(\"id\")\n", "    if len(variations) > 1:\n", "        print(f\"Error: {content.id} has {len(variations)} variations\")\n", "        last_id = variations.last()\n", "        for variation in variations:\n", "            if variation != last_id:\n", "                print(f\"deleting: {variation.id}\")\n", "                num_content_to_delete += 1\n", "                # variation.delete()\n", "\n", "print(f\"num_content_to_delete = {num_content_to_delete}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-09-25\n", "# check Variations for content\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "contents = Content.objects.all()\n", "\n", "num_content_to_delete = 0\n", "\n", "for content in contents:\n", "    variations = ContentVariation.objects.filter(content=content).order_by(\"id\")\n", "    if len(variations) > 1:\n", "        # print(f\"Error: {content.id} has {len(variations)} variations\")\n", "        last_id = variations.last()\n", "        for variation in variations:\n", "            if variation != last_id:\n", "                print(f\"deleting: {variation.id}\")\n", "                num_content_to_delete += 1\n", "                variation.delete()\n", "\n", "print(f\"num_content_to_delete = {num_content_to_delete}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-09-22\n", "# delete Contents which don't have campaign\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "contents = Content.objects.all()\n", "\n", "num_content_to_delete = 0\n", "\n", "for content in contents:\n", "    if not content.content_group:\n", "        num_content_to_delete += 1\n", "        # content.delete()\n", "\n", "print(f\"would delete {num_content_to_delete} in total {len(contents)} contents\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-09-18\n", "# delete ContentVariation which don't match content's tag\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "variations = ContentVariation.objects.all()\n", "\n", "num_variation_wo_content = 0\n", "num_variation_to_delete = 0\n", "\n", "for variation in variations:\n", "    if not variation.content:\n", "        print(f\"variation do not have content: {variation.id}\")\n", "        num_variation_wo_content += 1\n", "        num_variation_to_delete += 1\n", "        # variation.delete()\n", "    else:\n", "        if variation.tag != variation.content.latest_variation_tag:\n", "            num_variation_to_delete += 1\n", "            # variation.delete()\n", "\n", "print(f\"num_variation_wo_content={num_variation_wo_content}\")\n", "print(\n", "    f\"num_variation_to_delete={num_variation_to_delete} in total {len(variations)} variations\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-09-15\n", "# Update the content owner to make it consistent with playbook and/or campaign\n", "# The field is part of params for content/component/content_group/variation\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup, Campaign\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "num_mismatch = 0\n", "mismatches = []\n", "\n", "# Iterate over all contents\n", "for content in contents:\n", "    if not content.content_group:\n", "        continue\n", "    if content.creator.id == 129:\n", "        continue\n", "    campaign_owner = content.content_group.campaign.creator\n", "    if content.creator.id != campaign_owner.id:\n", "        num_mismatch += 1\n", "        mismatches.append((content.id, content.creator.id, campaign_owner.id))\n", "\n", "        # fix the owner\n", "        content.creator = campaign_owner\n", "        content.save()\n", "\n", "print(\"num_mismatch:\", num_mismatch)\n", "print(\"mismatches:\", mismatches)\n", "for mismatch in mismatches:\n", "    print(mismatch)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-08-18\n", "# Update the field name prompts to custom_instructions and change the value from a dict to a list.\n", "# The field is part of params for content/component/content_group/variation\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content, ContentVariation, ContentGroup\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "num_changes = 0\n", "# Iterate over all contents\n", "for content in contents:\n", "    old_prompts = content.content_params.get(\"prompts\", None)\n", "    if old_prompts is None:\n", "        continue\n", "    print(f\"old content_params: {content.content_params}\")\n", "    new_prompts = []\n", "    for prompt in old_prompts:\n", "        assert len(prompt) == 1 and isinstance(prompt, dict)\n", "        new_prompts.append(list(prompt.values())[0])\n", "    del content.content_params[\"prompts\"]\n", "    content.content_params[\"custom_instructions\"] = new_prompts\n", "    print(f\"new content_params: {content.content_params} for {content.id}\")\n", "\n", "    # content.save()\n", "    num_changes += 1\n", "print(f\"Total changes needed: {num_changes}\")\n", "\n", "\n", "# Fetch all ContentVariation\n", "variations = ContentVariation.objects.all()\n", "\n", "num_changes = 0\n", "# Iterate over all ContentVariation\n", "for variation in variations:\n", "    old_prompts = variation.params.get(\"prompts\", None)\n", "    if old_prompts is None:\n", "        continue\n", "    print(f\"old variation params: {variation.params}\")\n", "    new_prompts = []\n", "    for prompt in old_prompts:\n", "        assert len(prompt) == 1 and isinstance(prompt, dict)\n", "        new_prompts.append(list(prompt.values())[0])\n", "    del variation.params[\"prompts\"]\n", "    variation.params[\"custom_instructions\"] = new_prompts\n", "    print(f\"new variation params: {variation.params} for {variation.id}\")\n", "\n", "    # variation.save()\n", "    num_changes += 1\n", "print(f\"Total changes needed: {num_changes}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-07-25\n", "# Update Content.content_params.prompt_overrides keys to make FE/BE consistent\n", "# change from system to sys_msg, instruction to instruct_msg, reference to ref_content, static to static_context\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "num_changes = 0\n", "# Iterate over all contents\n", "for content in contents:\n", "    old_prompt_template_params = content.content_params.get(\"prompt_overrides\", {})\n", "    if not old_prompt_template_params:\n", "        continue\n", "    new_prompt_template_params = {}\n", "    for key, value in old_prompt_template_params.items():\n", "        if key == \"instruction\":\n", "            new_prompt_template_params[\"instruct_msg\"] = value\n", "        elif key == \"system\":\n", "            new_prompt_template_params[\"sys_msg\"] = value\n", "        elif key == \"reference\":\n", "            new_prompt_template_params[\"ref_content\"] = value\n", "        elif key == \"static\":\n", "            new_prompt_template_params[\"static_context\"] = value\n", "        else:\n", "            new_prompt_template_params[key] = value\n", "\n", "    print(f\"old: {old_prompt_template_params.keys()}\")\n", "    print(f\"new: {new_prompt_template_params.keys()}\")\n", "\n", "    content.content_params[\"prompt_overrides\"] = new_prompt_template_params\n", "    # content.save()\n", "    print(f\"{content.content_params} updated\")\n", "    num_changes += 1\n", "print(f\"Total changes needed: {num_changes}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-07-25\n", "# Update Content.content_params data schema\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "# Iterate over all contents\n", "for content in contents:\n", "    old_content_source = content.content_params.get(\"content_source\", None)\n", "    old_content_source_format = content.content_params.get(\n", "        \"content_source_format\", None\n", "    )\n", "    if old_content_source_format is None or not old_content_source:\n", "        continue\n", "\n", "    print(f\"{content.content_params} before update\")\n", "\n", "    content_source_upload_method = None\n", "    if old_content_source_format == \"Html\":\n", "        if old_content_source == \"url\":\n", "            content_source_upload_method = \"File\"\n", "        else:\n", "            if old_content_source[:4] != \"http\":\n", "                raise Exception(\n", "                    f\"incorrect assumption {old_content_source} with {content.id}\"\n", "                )\n", "            content_source_upload_method = \"Link\"\n", "    elif old_content_source_format == \"PDF\":\n", "        content_source_upload_method = \"File\"\n", "    elif old_content_source_format == \"Text\":\n", "        content_source_upload_method = \"Text\"\n", "    else:\n", "        raise Exception(f\"unknown format: {old_content_source_format}\")\n", "\n", "    content.content_params[\n", "        \"content_source_upload_method\"\n", "    ] = content_source_upload_method\n", "    # content.save()\n", "    print(f\"{content.content_params} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-07-25\n", "# Update Content.content_params data schema\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "# Iterate over all contents\n", "for content in contents:\n", "    old_content_type = content.content_params.get(\"content_type\", None)\n", "    if old_content_type is None:\n", "        continue\n", "\n", "    print(f\"{content.content_params} before update\")\n", "\n", "    new_content_type = None\n", "    new_content_source_format = None\n", "    if old_content_type == \"Landing Page\":\n", "        new_content_type = old_content_type\n", "        new_content_source_format = \"Html\"\n", "    elif old_content_type == \"Email\":\n", "        new_content_type = \"Email - Marketing\"\n", "        new_content_source_format = \"Html\"\n", "    elif old_content_type == \"PDF\":\n", "        new_content_type = \"Other\"\n", "        new_content_source_format = \"PDF\"\n", "    elif old_content_type == \"Text\":\n", "        new_content_type = \"Other\"\n", "        new_content_source_format = \"Text\"\n", "\n", "    content.content_params[\"content_type\"] = new_content_type\n", "    content.content_params[\"content_source_format\"] = new_content_source_format\n", "    # content.save()\n", "    print(f\"{content.content_params} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-07-25\n", "# Delete all contents whose content_source_copy url is not a s3 presigned url\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "# Iterate over all contents\n", "for content in contents:\n", "    content_source_copy = content.content_params.get(\"content_source_copy\", \"\")\n", "    # check whether content_params['content_source_copy'] starts with /api/web/storage/s3-presigned-url?, if not, delete it\n", "    if not content_source_copy.startswith(\"/api/web/storage/s3-presigned-url\"):\n", "        print(content_source_copy)\n", "        print(f\"{content} deleted\")\n", "        # content.delete()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-07-21\n", "# Update the ContentVariation.variations data schema\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import ContentVariation\n", "\n", "# Fetch all contents\n", "content_variations = ContentVariation.objects.all()\n", "\n", "# Iterate over all contents\n", "for convar in content_variations:\n", "    content = convar.content\n", "\n", "    old_variations = convar.variations\n", "    if not type(old_variations) == list or len(old_variations) == 0:\n", "        print(\"Not a list or empty list\")\n", "        print(old_variations)\n", "        continue\n", "    new_variations = content.components\n", "    for old_var in old_variations:\n", "        for component_id in new_variations.keys():\n", "            if component_id not in old_var:\n", "                continue\n", "            text = old_var[component_id][\"text\"]\n", "            if \"meta\" not in new_variations[component_id]:\n", "                new_variations[component_id][\"meta\"] = {}\n", "            if \"variations\" not in new_variations[component_id][\"meta\"]:\n", "                new_variations[component_id][\"meta\"][\"variations\"] = []\n", "            new_variations[component_id][\"meta\"][\"variations\"].append({\"text\": text})\n", "            new_variations[component_id][\"meta\"][\"current_variation_index\"] = 0\n", "\n", "    convar.variations = new_variations\n", "    # convar.save()\n", "    print(f\"{convar} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-06-22\n", "# Update the content_source_copy url for all contents\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Content\n", "\n", "# Fetch all contents\n", "contents = Content.objects.all()\n", "\n", "# Iterate over all contents\n", "for content in contents:\n", "    content_source_copy = content.content_params.get(\"content_source_copy\", \"\")\n", "    # check whether content_params['content_source_copy'] starts with /api/web/storage/pdf?, if so, replace it with /api/web/storage/s3-presigned-url?\n", "    if content_source_copy.startswith(\"/api/web/storage/pdf?\"):\n", "        content_source_copy = content_source_copy.replace(\n", "            \"/api/web/storage/pdf?\", \"/api/web/storage/s3-presigned-url?\"\n", "        )\n", "        content.content_params[\"content_source_copy\"] = content_source_copy\n", "        # content.save()\n", "        print(f\"{content} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-05-25\n", "# Migrate the old data to add an id field to all the data entries\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Playbook\n", "import uuid\n", "\n", "# Fetch all playbooks\n", "playbooks = Playbook.objects.all()\n", "\n", "# Iterate over all playbooks\n", "for playbook in playbooks:\n", "    # Iterate over company_info data\n", "    for key in playbook.company_info:\n", "        if key == \"meta\":\n", "            continue\n", "        if \"data\" in playbook.company_info[key]:\n", "            for item in playbook.company_info[key][\"data\"]:\n", "                item[\"id\"] = str(uuid.uuid4())\n", "\n", "    # Iterate over target_info data\n", "    for key1 in playbook.target_info:\n", "        if key1 == \"meta\":\n", "            continue\n", "        for key2 in playbook.target_info[key1]:\n", "            if key2 == \"meta\":\n", "                continue\n", "            if \"data\" in playbook.target_info[key1][key2]:\n", "                for item in playbook.target_info[key1][key2][\"data\"]:\n", "                    item[\"id\"] = str(uuid.uuid4())\n", "    # print(playbook.company_info)\n", "    # print(playbook.target_info)\n", "\n", "    # Save the playbook with the updated data\n", "    # playbook.save()\n", "    print(f\"{playbook} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-05-25\n", "# Add some targets to a playbook\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Playbook\n", "import uuid\n", "\n", "# Fetch all playbooks\n", "playbook = Playbook.objects.get(id=2)\n", "\n", "target_name = \"Marketing\"\n", "sub_targets = \"\"\"\n", "Brand & Content Director, Corporate\n", "Brand Chief & Marketing Officer\n", "Business Relationship Manager\n", "Chief Marketing Officer\n", "Content Management Specialist\n", "Digital Internal Communications Specialist\n", "Digital Transfomation PMO Consultant\n", "Director Brand Development & Engagement\n", "Director Marketing Technology\n", "Director of Brand\n", "Director of Design Research and Strategy\n", "Director of Digital Marketing\n", "Director of Marketing\n", "Director, Industrial & Corporate Business Development\n", "\"\"\"\n", "\n", "if target_name not in playbook.target_info:\n", "    playbook.target_info[target_name] = {}\n", "    for sub_target_name in sub_targets.split(\"\\n\"):\n", "        sub_target_name = sub_target_name.strip()\n", "        if sub_target_name == \"\":\n", "            continue\n", "        playbook.target_info[target_name][sub_target_name] = {\"data\": []}\n", "playbook.target_info[target_name][\"meta\"] = {\"position\": 5}\n", "print(playbook.target_info[target_name])\n", "\n", "# Save the playbook with the updated data\n", "# playbook.save()\n", "print(f\"{playbook} updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-06-08\n", "# Force to re-build the context for all playbooks\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Playbook\n", "from api.playbook import PlaybookHandler\n", "\n", "# Fetch all playbooks\n", "playbooks = Playbook.objects.all()\n", "\n", "# Iterate over all playbooks\n", "for playbook in playbooks:\n", "    if playbook.id not in [13, 21, 52, 68]:\n", "        continue\n", "    handler = PlaybookHandler.load_from_db_instance(playbook)\n", "    handler.force_context_full_refresh([\"company_info\", \"target_info\"])\n", "\n", "    print(f\"---{playbook} updated---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023-06-08\n", "# Force to re-build the context for all playbooks\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "from api.models import TofuUser\n", "\n", "users = TofuUser.objects.all()\n", "gpt4_user_count = 0\n", "for user in users:\n", "    if user.context:\n", "        if user.context.get(\"model\"):\n", "            if (\n", "                user.context.get(\"model\") == \"gpt-4o\"\n", "            ):\n", "                gpt4_user_count += 1\n", "                user.context[\"model\"] = \"gpt-4-0125-preview\"\n", "                user.save()\n", "                print(\"Updating to gpt-4 turbo\", user.username)\n", "print(f\"Updated {gpt4_user_count} gpt-4 users\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "from api.models import TofuUser\n", "\n", "users = TofuUser.objects.all()\n", "user_repurposing_count = 0\n", "user_personalization_count = 0\n", "for user in users:\n", "    if user.context:\n", "        if user.context.get(\"model_for_repurpose\") == \"claude-3-opus-20240229\":\n", "            user_repurposing_count += 1\n", "            user.context[\"model_for_repurpose\"] = (\n", "                \"anthropic.claude-3-opus-20240229-v1:0\"\n", "            )\n", "            print(\"Updating to claude repurposing\", user.username)\n", "        if user.context.get(\"model\") == \"claude-3-opus-20240229\":\n", "            user_personalization_count += 1\n", "            user.context[\"model\"] = \"anthropic.claude-3-opus-20240229-v1:0\"\n", "            print(\"Updating to claude personalization\", user.username)\n", "        user.save()\n", "print(f\"Updated {user_repurposing_count} repurpose models to claude\")\n", "print(f\"Updated {user_personalization_count} personalization models to claude\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import ContentGroup, Content, Campaign\n", "\n", "content_groups = ContentGroup.objects.all()\n", "\n", "for content_group in content_groups:\n", "    # remove prompt_overrides from content_group_params\n", "    if content_group.content_group_params and \"prompt_overrides\" in content_group.content_group_params:\n", "        content_group.content_group_params.pop(\"prompt_overrides\")\n", "        content_group.save()\n", "        print(f\"Removed prompt_overrides from content group {content_group.id}\")\n", "\n", "contents = Content.objects.all()\n", "for content in contents:\n", "    # remove prompt_overrides from content_params\n", "    if content.content_params and \"prompt_overrides\" in content.content_params:\n", "        content.content_params.pop(\"prompt_overrides\")\n", "        content.save()\n", "        print(f\"Removed prompt_overrides from content {content.id}\")\n", "\n", "campaigns = Campaign.objects.all()\n", "for campaign in campaigns:\n", "    # remove prompt_overrides from campaign_params\n", "    if campaign.campaign_params and \"prompt_overrides\" in campaign.campaign_params:\n", "        campaign.campaign_params.pop(\"prompt_overrides\")\n", "        campaign.save()\n", "        print(f\"Removed prompt_overrides from campaign {campaign.id}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import TofuUser\n", "\n", "users = TofuUser.objects.all()\n", "user_repurposing_count = 0\n", "user_personalization_count = 0\n", "for user in users:\n", "    user_personalization_count += 1\n", "    user.context[\"model\"] = \"us.anthropic.claude-3-7-sonnet-20250219-v1:0\"\n", "    print(\"Updating to claude 3.7\", user.username)\n", "    \n", "    user_repurposing_count += 1\n", "    user.context[\"model_for_repurpose\"] = (\n", "        \"us.anthropic.claude-3-7-sonnet-20250219-v1:0\"\n", "    )\n", "    print(\"Updating to claude 3.7 repurposing\", user.username)\n", "    user.save()\n", "print(f\"Updated {user_repurposing_count} repurpose models to claude\")\n", "print(f\"Updated {user_personalization_count} personalization models to claude\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import ContentGroup, Campaign\n", "\n", "# Disable auto_now for updated_at for content group and campaign.\n", "ContentGroup.updated_at.auto_now = False\n", "Campaign.updated_at.auto_now = False\n", "\n", "content_groups = ContentGroup.objects.all()\n", "num_content_groups = len(content_groups)\n", "for i, content_group in enumerate(content_groups):\n", "    print(f\"Checking content group {i+1}/{num_content_groups}\")\n", "    has_changes = False\n", "    if not content_group.content_group_params:\n", "        pass\n", "    else:\n", "        content_group_custom_instructions = content_group.content_group_params.get(\"custom_instructions\", [])\n", "        if not content_group_custom_instructions:\n", "            pass\n", "        else:\n", "            for custom_instruction in content_group_custom_instructions:\n", "                assets = custom_instruction.get(\"assets\", [])\n", "                if not assets:\n", "                    pass\n", "                if not isinstance(assets, list):\n", "                    pass\n", "                else:\n", "                    if not assets[0]:\n", "                        pass\n", "                    elif not isinstance(assets[0], dict):\n", "                        raise Exception(f\"assets[0] must be a dict for content group {content_group.id}\")\n", "                    else:\n", "                        asset = assets[0]\n", "                        for key in asset.keys():\n", "                            if isinstance(asset[key], str):\n", "                                asset[key] = [asset[key]]\n", "                        custom_instruction[\"assets\"] = asset\n", "                        has_changes = True\n", "    if not content_group.components:\n", "        pass\n", "    else:\n", "        for key, value in content_group.components.items():\n", "            if not \"meta\" in value:\n", "                pass\n", "            else:\n", "                meta = value[\"meta\"]\n", "                if not \"component_params\" in meta:\n", "                    pass\n", "                else:\n", "                    component_params = meta[\"component_params\"]\n", "                    if not \"custom_instructions\" in component_params:\n", "                        pass\n", "                    else:\n", "                        custom_instructions = component_params[\"custom_instructions\"]\n", "                        if not isinstance(custom_instructions, list):\n", "                            pass\n", "                        else:\n", "                            for custom_instruction in custom_instructions:\n", "                                if not \"assets\" in custom_instruction:\n", "                                    pass\n", "                                else:\n", "                                    assets = custom_instruction[\"assets\"]\n", "                                    if not isinstance(assets, list):\n", "                                        pass\n", "                                    else:\n", "                                        if not assets[0]:\n", "                                            pass\n", "                                        elif not isinstance(assets[0], dict):\n", "                                            raise Exception(f\"assets[0] must be a dict for content group {content_group.id}\")\n", "                                        else:\n", "                                            asset = assets[0]\n", "                                            for key in asset.keys():\n", "                                                if isinstance(asset[key], str):\n", "                                                    asset[key] = [asset[key]]\n", "                                            custom_instruction[\"assets\"] = asset\n", "                                            has_changes = True\n", "    if has_changes:\n", "        content_group.save(update_fields=[\"content_group_params\", \"components\"])\n", "        print(f\"Updated content group {content_group.id}\")\n", "    campaign = content_group.campaign\n", "    has_changes = False\n", "    if not campaign:\n", "        pass\n", "    else:\n", "        if not campaign.campaign_params:\n", "            pass\n", "        else:\n", "            custom_instructions = campaign.campaign_params.get(\"custom_instructions\", [])\n", "            if not custom_instructions:\n", "                pass\n", "            else:\n", "                for custom_instruction in custom_instructions:\n", "                    if not \"assets\" in custom_instruction:\n", "                        pass\n", "                    else:\n", "                        assets = custom_instruction[\"assets\"]\n", "                        if not isinstance(assets, list):\n", "                            pass\n", "                        else:\n", "                            if not assets[0]:\n", "                                pass\n", "                            elif not isinstance(assets[0], dict):\n", "                                raise Exception(f\"assets[0] must be a dict for campaign {campaign.id}\")\n", "                            else:\n", "                                asset = assets[0]\n", "                                for key in asset.keys():\n", "                                    if isinstance(asset[key], str):\n", "                                        asset[key] = [asset[key]]\n", "                                custom_instruction[\"assets\"] = asset\n", "                                has_changes = True\n", "    if has_changes:\n", "        campaign.save(update_fields=[\"campaign_params\"])\n", "        print(f\"Updated campaign {campaign.id}\")\n", "\n", "# Re-enable auto_now for updated_at for content group and campaign.\n", "ContentGroup.updated_at.auto_now = True\n", "Campaign.updated_at.auto_now = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import TofuUser, Playbook\n", "\n", "ringcentral_playbook_id=665\n", "ringcentral_playbook_id_2 = 3050\n", "ringcentral_playbook = Playbook.objects.get(id=ringcentral_playbook_id)\n", "ringcentral_playbook_users = ringcentral_playbook.users.all()\n", "ringcentral_playbook_2 = Playbook.objects.get(id=ringcentral_playbook_id_2)\n", "ringcentral_playbook_2_users = ringcentral_playbook_2.users.all()\n", "\n", "# for all user not in ringcentral_playbook_users, set the user context campaignV3Enabled to True.\n", "users = TofuUser.objects.all()\n", "for user in users:\n", "    if user not in ringcentral_playbook_users and user not in ringcentral_playbook_2_users:\n", "        if not user.context:\n", "            user.context = {}\n", "        user.context[\"campaignV3Enabled\"] = True\n", "        user.save()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}