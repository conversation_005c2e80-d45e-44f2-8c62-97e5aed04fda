{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-04-01 13:53:11,228 INFO: found organization id: tofu-ykka\n", "2025-04-01 13:53:11,410 INFO: Found credentials in environment variables.\n", "2025-04-01 13:53:11,511 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n", "2025-04-01 13:53:11,687 INFO: Note: NumExpr detected 12 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "2025-04-01 13:53:11,687 INFO: NumExpr defaulting to 8 threads.\n", "2025-04-01 13:53:15,151 ERROR: No playbook found\n", "2025-04-01 13:53:15,314 INFO: Image size: (761, 1024), format: PNG\n", "2025-04-01 13:53:16,834 INFO: Image uploaded successfully to https://tofu-generated-images.s3.amazonaws.com/playbook_None/0031154c-0e48-4059-a01c-cc145d0d50cb.png?AWSAccessKeyId=AKIA3FZRO7D4HMS6NKUM&Signature=RwdQpfBQC2VWfDTTupIGSMcFCXQ%3D&Expires=1744145596\n"]}, {"data": {"text/plain": ["('https://tofu-generated-images.s3.amazonaws.com/playbook_None/0031154c-0e48-4059-a01c-cc145d0d50cb.png?AWSAccessKeyId=AKIA3FZRO7D4HMS6NKUM&Signature=RwdQpfBQC2VWfDTTupIGSMcFCXQ%3D&Expires=1744145596',\n", " '\\n\\nMeow.')"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "# Image gen test\n", "\n", "\n", "from api.task_registry import GenerationGoal\n", "from api.model_config import ModelConfigResolver\n", "from api.model_caller import ModelCaller\n", "generation_goal = GenerationGoal.IMAGE_GENERATION\n", "foundation_model = \"gemini-2.0-flash-exp-image-generation\"\n", "model_config = ModelConfigResolver.resolve(\n", "    generation_goal,\n", "    foundation_model=foundation_model,\n", "    n=1,\n", ")\n", "model_caller = ModelCaller(model_config)\n", "model_caller.get_image_generation_with_fallback(\n", "    \"Generate an image of a cat and say meow\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}