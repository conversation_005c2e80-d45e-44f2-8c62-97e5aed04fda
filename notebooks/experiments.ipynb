{"cells": [{"cell_type": "code", "execution_count": 12, "id": "cfd96d83", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:40 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n", "2023-03-12 22:47:40 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET / HTTP/1.1\" 200 None\n", "2023-03-12 22:47:40 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n", "2023-03-12 22:47:41 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET / HTTP/1.1\" 200 None\n", "2023-03-12 22:47:41 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/index.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:41 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:41 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/companies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:42 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/clinician-network HTTP/1.1\" 200 None\n", "2023-03-12 22:47:42 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinician-network.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:42 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/technology HTTP/1.1\" 200 None\n", "2023-03-12 22:47:42 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/technology.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:42 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/partnership HTTP/1.1\" 200 None\n", "2023-03-12 22:47:43 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/partnership.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:43 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/telehealth HTTP/1.1\" 200 None\n", "2023-03-12 22:47:43 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/telehealth.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:43 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/lab-testing HTTP/1.1\" 200 None\n", "2023-03-12 22:47:43 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/lab-testing.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:43 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/digital-health-companies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:43 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/digital-health-companies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:44 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/labs-diagnostics HTTP/1.1\" 200 None\n", "2023-03-12 22:47:44 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/labs-diagnostics.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:44 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/retailers-pharmacies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:44 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/retailers-pharmacies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:45 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/urgent-care HTTP/1.1\" 200 None\n", "2023-03-12 22:47:45 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/urgent-care.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:45 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians HTTP/1.1\" 200 None\n", "2023-03-12 22:47:45 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinicians.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:46 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/physicians HTTP/1.1\" 200 None\n", "2023-03-12 22:47:46 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/physicians.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:46 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/nurse-practitioners HTTP/1.1\" 200 None\n", "2023-03-12 22:47:46 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/nurse-practitioners.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:47 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/registered-nurses-at-wheel HTTP/1.1\" 200 None\n", "2023-03-12 22:47:47 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/registered-nurses-at-wheel.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:47 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/behavioral-health-specialists HTTP/1.1\" 200 None\n", "2023-03-12 22:47:47 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/behavioral-health-specialists.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:47 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/latest-opportunities HTTP/1.1\" 200 None\n", "2023-03-12 22:47:48 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/latest-opportunities.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:48 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/clinician-faqs HTTP/1.1\" 200 None\n", "2023-03-12 22:47:48 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinician-faqs.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:48 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /refer-a-clinician HTTP/1.1\" 200 None\n", "2023-03-12 22:47:48 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/refer-a-clinician.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:49 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /about HTTP/1.1\" 200 None\n", "2023-03-12 22:47:49 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/about.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:49 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /careers HTTP/1.1\" 200 None\n", "2023-03-12 22:47:49 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/careers.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:49 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /press HTTP/1.1\" 200 None\n", "2023-03-12 22:47:49 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/press.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:50 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /contact-us HTTP/1.1\" 200 None\n", "2023-03-12 22:47:50 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/contact-us.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:50 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /resources HTTP/1.1\" 200 None\n", "2023-03-12 22:47:50 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/resources.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:50 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /resources HTTP/1.1\" 200 None\n", "2023-03-12 22:47:50 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/resources.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:51 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /roi-calculator HTTP/1.1\" 200 None\n", "2023-03-12 22:47:51 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/roi-calculator.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:51 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /state-telehealth-regulations HTTP/1.1\" 200 None\n", "2023-03-12 22:47:51 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/state-telehealth-regulations.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies-blog HTTP/1.1\" 200 None\n", "2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/companies-blog.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /blog HTTP/1.1\" 200 None\n", "2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/blog.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/companies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians HTTP/1.1\" 200 None\n", "2023-03-12 22:47:52 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinicians.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:53 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:53 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/companies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:53 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians HTTP/1.1\" 200 None\n", "2023-03-12 22:47:53 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinicians.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:53 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /careers HTTP/1.1\" 200 None\n", "2023-03-12 22:47:53 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/careers.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:54 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:54 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/companies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:54 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/clinician-network HTTP/1.1\" 200 None\n", "2023-03-12 22:47:54 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinician-network.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:54 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/technology HTTP/1.1\" 200 None\n", "2023-03-12 22:47:54 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/technology.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:55 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/partnership HTTP/1.1\" 200 None\n", "2023-03-12 22:47:55 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/partnership.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:55 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /roi-calculator HTTP/1.1\" 200 None\n", "2023-03-12 22:47:55 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/roi-calculator.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:55 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /state-telehealth-regulations HTTP/1.1\" 200 None\n", "2023-03-12 22:47:55 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/state-telehealth-regulations.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:56 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /resources HTTP/1.1\" 200 None\n", "2023-03-12 22:47:56 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/resources.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:56 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/digital-health-companies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:56 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/digital-health-companies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:56 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/labs-diagnostics HTTP/1.1\" 200 None\n", "2023-03-12 22:47:56 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/labs-diagnostics.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:56 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/retailers-pharmacies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:57 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/retailers-pharmacies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:57 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/technology-companies HTTP/1.1\" 200 None\n", "2023-03-12 22:47:57 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/technology-companies.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:57 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/urgent-care HTTP/1.1\" 200 None\n", "2023-03-12 22:47:57 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/urgent-care.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:57 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies/provider-groups HTTP/1.1\" 200 None\n", "2023-03-12 22:47:57 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/provider-groups.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:58 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /companies-blog HTTP/1.1\" 200 None\n", "2023-03-12 22:47:58 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/companies-blog.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:58 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /telemedicine-software HTTP/1.1\" 200 None\n", "2023-03-12 22:47:58 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/telemedicine-software.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:58 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians HTTP/1.1\" 200 None\n", "2023-03-12 22:47:58 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinicians.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:59 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/physicians HTTP/1.1\" 200 None\n", "2023-03-12 22:47:59 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/physicians.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:47:59 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/nurse-practitioners HTTP/1.1\" 200 None\n", "2023-03-12 22:47:59 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/nurse-practitioners.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:00 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/registered-nurses-at-wheel HTTP/1.1\" 200 None\n", "2023-03-12 22:48:00 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/registered-nurses-at-wheel.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:00 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/behavioral-health-specialists HTTP/1.1\" 200 None\n", "2023-03-12 22:48:00 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/behavioral-health-specialists.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:00 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/latest-opportunities HTTP/1.1\" 200 None\n", "2023-03-12 22:48:00 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/latest-opportunities.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:01 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/clinician-faqs HTTP/1.1\" 200 None\n", "2023-03-12 22:48:01 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/clinician-faqs.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:01 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /refer-a-clinician HTTP/1.1\" 200 None\n", "2023-03-12 22:48:01 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/refer-a-clinician.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:02 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /clinicians/sitemap-remote-jobs-by-state HTTP/1.1\" 200 None\n", "2023-03-12 22:48:02 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/sitemap-remote-jobs-by-state.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:02 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /blog HTTP/1.1\" 200 None\n", "2023-03-12 22:48:02 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/blog.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:02 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /about HTTP/1.1\" 200 None\n", "2023-03-12 22:48:02 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/about.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:03 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /about HTTP/1.1\" 200 None\n", "2023-03-12 22:48:03 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/about.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:03 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /careers HTTP/1.1\" 200 None\n", "2023-03-12 22:48:03 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/careers.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:03 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /press HTTP/1.1\" 200 None\n", "2023-03-12 22:48:03 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/press.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:03 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /contact-us HTTP/1.1\" 200 None\n", "2023-03-12 22:48:04 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/contact-us.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:04 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /privacy-policy HTTP/1.1\" 200 None\n", "2023-03-12 22:48:04 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/privacy-policy.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:04 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /api-terms-of-use HTTP/1.1\" 200 None\n", "2023-03-12 22:48:04 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/api-terms-of-use.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:05 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /wheel-provider-group-terms-of-use HTTP/1.1\" 200 None\n", "2023-03-12 22:48:05 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/wheel-provider-group-terms-of-use.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:05 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /wheel-health-inc-terms-of-use HTTP/1.1\" 200 None\n", "2023-03-12 22:48:05 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/wheel-health-inc-terms-of-use.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:05 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /notice-of-privacy-practices HTTP/1.1\" 200 None\n", "2023-03-12 22:48:05 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/notice-of-privacy-practices.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:06 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /mobile-terms-conditions HTTP/1.1\" 200 None\n", "2023-03-12 22:48:06 [urllib3.connectionpool] DEBUG: Starting new HTTPS connection (1): www.wheel.com:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/mobile-terms-conditions.html\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2023-03-12 22:48:06 [urllib3.connectionpool] DEBUG: https://www.wheel.com:443 \"GET /wheel-provider-group-telehealth-informed-consent HTTP/1.1\" 200 None\n"]}, {"name": "stdout", "output_type": "stream", "text": ["../data/wheel/wheel-provider-group-telehealth-informed-consent.html\n"]}], "source": ["import os\n", "import requests\n", "from urllib.parse import urlparse\n", "from bs4 import BeautifulSoup\n", "\n", "\n", "def download(url, folder_path):\n", "    \"\"\"\n", "    Downloads a file from the given URL and saves it in the given folder path.\n", "    \"\"\"\n", "    response = requests.get(url)\n", "    file_name = url.split(\"/\")[-1]\n", "    file_name = \"index.html\" if file_name.strip() == \"\" else file_name + \".html\"\n", "    file_path = os.path.join(folder_path, file_name)\n", "    print(file_path)\n", "    with open(file_path, \"wb\") as f:\n", "        f.write(response.content)\n", "\n", "\n", "def crawl(url, folder_path):\n", "    \"\"\"\n", "    Crawls all the pages and images of the wheel.com domain starting from the given URL and saves them in the given folder path.\n", "    \"\"\"\n", "    # Create the folder if it does not exist\n", "    if not os.path.exists(folder_path):\n", "        os.makedirs(folder_path)\n", "\n", "    # Download the page\n", "    response = requests.get(url)\n", "    soup = BeautifulSoup(response.content, \"html.parser\")\n", "\n", "    # Find all the links in the page\n", "    links = [link.get(\"href\") for link in soup.find_all(\"a\")]\n", "    # links += [img.get(\"src\") for img in soup.find_all(\"img\")]\n", "\n", "    # Download all the links within the wheel.com domain\n", "    for link in links:\n", "        if (\n", "            link\n", "            and not link.startswith(\"#\")\n", "            and not link.startswith(\"mailto:\")\n", "            and not link.startswith(\"tel:\")\n", "        ):\n", "            parsed_link = urlparse(link)\n", "            if parsed_link.netloc == \"www.wheel.com\" or not parsed_link.netloc:\n", "                if parsed_link.scheme and parsed_link.netloc:\n", "                    # Download external links within the wheel.com domain\n", "                    download(link, folder_path)\n", "                else:\n", "                    # Download internal links within the wheel.com domain\n", "                    link_url = (\n", "                        url + link if link.startswith(\"/\") else os.path.join(url, link)\n", "                    )\n", "                    download(link_url, folder_path)\n", "\n", "\n", "# Set the domain name and the folder path\n", "domain = \"www.wheel.com\"\n", "folder_path = \"../data/wheel\"\n", "\n", "# Crawl the domain\n", "crawl(\"https://\" + domain, folder_path)"]}, {"cell_type": "code", "execution_count": 4, "id": "401759a2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "INFO:unstructured:Reading document from string ...\n", "INFO:unstructured:Reading document ...\n", "INFO:root:> [build_index_from_documents] Total LLM token usage: 0 tokens\n", "INFO:root:> [build_index_from_documents] Total embedding token usage: 63457 tokens\n"]}], "source": ["from pathlib import Path\n", "from llama_index import download_loader, GPTSimpleVectorIndex, MockLLMPredictor\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "SimpleDirectoryReader = download_loader(\"SimpleDirectoryReader\")\n", "\n", "loader = SimpleDirectoryReader(\n", "    \"../data/wheel\",\n", "    file_extractor={\n", "        \".html\": \"UnstructuredReader\",\n", "    },\n", ")\n", "documents = loader.load_data()\n", "\n", "# llm_predictor = MockLLMPredictor(max_tokens=256)\n", "# index = GPTSimpleVectorIndex(documents, llm_predictor=llm_predictor)\n", "# # get number of tokens used\n", "# print(llm_predictor.last_token_usage)\n", "\n", "index = GPTSimpleVectorIndex(documents)\n", "# save to disk\n", "index.save_to_disk(\"../data/index/wheel.json\")"]}, {"cell_type": "code", "execution_count": 18, "id": "cae865c6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:> [query] Total LLM token usage: 2497 tokens\n", "INFO:root:> [query] Total embedding token usage: 3 tokens\n"]}, {"name": "stdout", "output_type": "stream", "text": ["> Source (Doc id: 235cf437-b99d-408b-a586-22877cbc1ab7): Virtual care services for lab companies\n", "\n", "Wheel provides lab companies with clinician oversight for nationwide, direct-to-consumer lab testing programs you and your customers can trust.\n", "\n", "Work with u...\n", "\n", "> Source (Doc id: 61be2ef3-7a1f-4c32-a375-dd689a93de6a): Quality care from a world-class team\n", "\n", "Access Wheel’s nationwide network of tech-enabled clinicians ready to deliver your brand of care.\n", "\n", "Work with us\n", "\n", "A deep clinical bench\n", "\n", "Deliver safe and profes...\n", "\n", "> Source (Doc id: bf2ab11e-718e-46bc-81a8-aab2c44e308b): More than a vendor, a strategic partner\n", "\n", "From implementation to business growth, Wheel partners with clients to build healthcare solutions for the future.\n", "\n", "Let's talk\n", "\n", "Extend your team with experti...\n", "Wheel's clients are healthcare providers who want to deliver high-quality virtual care to their patients. Wheel is more than just a vendor, as they partner with clients to build healthcare solutions for the future. They provide expertise in healthtech engineering, legal counsel, medical expertise, and healthcare operations to help clients focus on delivering great patient care. Wheel's clinical operations leverage evidence-based treatment pathways for safe and effective patient care, developed in partnership with Wheel medical directors and clinical leadership. They also provide implementation services, regulatory and legal support, care services, and industry and strategy consulting. Wheel's top partners grew their business 350% year over year in 2021, and clients who choose Wheel stay with Wheel. As a white-labeled solution without a patient front door, Wheel never competes for clients' customers and always puts their needs first. Testimonials from Wheel's clients highlight their ease of working with Wheel and the positive impact on their business.\n"]}], "source": ["from langchain.chat_models import ChatOpenAI\n", "from llama_index import GPTSimpleVectorIndex, SimpleDirectoryReader, LLMPredictor\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "llm_predictor = LLMPredictor(llm=ChatOpenAI(temperature=0, model_name=\"gpt-3.5-turbo\"))\n", "# index = GPTSimpleVectorIndex.load_from_disk(\"../data/index/wheel.json\")\n", "response = index.query(\"Our Clients\", similarity_top_k=3, llm_predictor=llm_predictor)\n", "# get sources\n", "# source_texts = []s\n", "# for source_node in response.source_nodes:\n", "#     source_texts.append(source_node.source_text)\n", "# print(\"\\n--------------------------------------\\n\".join(source_texts))\n", "\n", "print(response.get_formatted_sources(length=200))\n", "print(response)"]}, {"cell_type": "code", "execution_count": 35, "id": "847f62bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running on local URL:  http://127.0.0.1:7873\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7873/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:> [query] Total LLM token usage: 3904 tokens\n", "INFO:root:> [query] Total embedding token usage: 4 tokens\n"]}], "source": ["import gradio as gr\n", "\n", "\n", "def run_text(text, state):\n", "    response = index.query(text, similarity_top_k=5, llm_predictor=llm_predictor)\n", "    # response = index.query(text, similarity_top_k=5)\n", "    reply = f\"-------Answer--------: \\n{response} \\n\\n ---------Sources-------: \\n {response.get_formatted_sources(length=200)}\"\n", "\n", "    state = state + [(text, reply)]\n", "    return state, state\n", "\n", "\n", "with gr.<PERSON><PERSON>() as demo:\n", "    chatbot = gr.<PERSON><PERSON><PERSON>().style(height=1200)\n", "    state = gr.State([])\n", "    with gr.<PERSON>():\n", "        with gr.<PERSON>(scale=0.85):\n", "            txt = gr.Textbox(show_label=False, placeholder=\"\")\n", "        with gr.<PERSON>n(scale=0.15, min_width=0):\n", "            clear = gr.<PERSON>(\"Clear\")\n", "\n", "    txt.submit(run_text, [txt, state], [chatbot, state])\n", "    txt.submit(lambda: \"\", None, txt)\n", "\n", "    clear.click(lambda: [], None, chatbot)\n", "    clear.click(lambda: [], None, state)\n", "\n", "    demo.launch()"]}, {"cell_type": "code", "execution_count": 41, "id": "d6caf132", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:> [build_index_from_documents] Total LLM token usage: 0 tokens\n", "INFO:root:> [build_index_from_documents] Total embedding token usage: 0 tokens\n", "INFO:root:> [query] Total LLM token usage: 1553 tokens\n", "INFO:root:> [query] Total embedding token usage: 0 tokens\n"]}, {"data": {"text/markdown": ["**`Final Response:`** Buildkite is a powerful CI/CD platform that allows users to build their dream workflows without sharing their secrets or source code. The platform is designed to scale on users' infrastructure and has no concurrency limits. Buildkite offers real-time tracking and monitoring for tests, making it easy to identify and fix issues. The platform is highly extensible, allowing users to build pipelines with code for just-in-time CI. Buildkite runs on any OS, giving users full control of their DevOps workflow.\n", "\n", "One of the key features of Buildkite is its autoscaling capabilities. Users can orchestrate agents to scale up when needed, dramatically cutting down the time needed to run their test suite. Buildkite's Elastic CI Stack for AWS gives users a private, autoscaling Buildkite Agent cluster in their own AWS account. This allows users to keep costs under control by scaling up their fleet of agents only when there's work to do. Users can also bring their own compute and scale up agents on their own hardware.\n", "\n", "Buildkite integrates seamlessly with other developer tools and platforms, making it easy to fit into any workflow. The platform offers deep observability to test suites, surfacing unreliable and slow tests so users can take action. Buildkite's premium support team is available to help users get the most out of the platform and unblock engineering teams.\n", "\n", "Pipelines can be generated dynamically at runtime, unlocking DevOps workflows impossible with other CI/CD providers. The behavior of a build can change during execution to fit any number of scenarios. Many pipelines can be generated from the same tooling, making it simple to scale and maintain large fleets of pipelines. Users can bring the languages they know to defining their build process, or configure pipelines with YAML.\n", "\n", "Buildkite's enterprise plan options are available for companies that require the highest level of features and support. The platform offers 24/7 on-call emergencies, quarterly roadmap review, 99.95% Uptime SLA, Technical Account Manager, and more.\n", "\n", "Buildkite has been used by companies such as PagerDuty, which achieved over 500 builds a day since switching to Buildkite. The platform offers webinars and case studies to help users optimize their workflows and get the most out of the platform. Buildkite's Build Matrix is a new way of creating multiple jobs from a single, multi-variant step definition. The platform also offers a customer spotlight series, where users can learn from other companies that have successfully implemented Buildkite.\n", "\n", "In conclusion, Buildkite is"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["---"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["**`Source Node 1/1`**"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "AttributeError", "evalue": "'SourceNode' object has no attribute 'image'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[41], line 22\u001b[0m\n\u001b[1;32m     20\u001b[0m response \u001b[39m=\u001b[39m index\u001b[39m.\u001b[39mquery(\u001b[39m\"\u001b[39m\u001b[39mWrite a 512 words summary. Try to use only the information provided and include as many details as possible.\u001b[39m\u001b[39m\"\u001b[39m, response_mode\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mcompact\u001b[39m\u001b[39m\"\u001b[39m, llm_predictor\u001b[39m=\u001b[39mllm_predictor)\n\u001b[1;32m     21\u001b[0m \u001b[39m# # index.query(\"Give me a summary of the website\", similarity_top_k=3, llm_predictor=llm_predictor)\u001b[39;00m\n\u001b[0;32m---> 22\u001b[0m display_response(response)\n\u001b[1;32m     24\u001b[0m \u001b[39m# from langchain.chains.summarize import load_summarize_chain\u001b[39;00m\n\u001b[1;32m     25\u001b[0m \u001b[39m# # llm = OpenAI(temperature=0, model_name=\"text-davinci-003\", max_tokens=512)\u001b[39;00m\n\u001b[1;32m     26\u001b[0m \u001b[39m# llm=ChatOpenAI(temperature=0, model_name=\"gpt-3.5-turbo\", max_tokens=64)\u001b[39;00m\n\u001b[1;32m     27\u001b[0m \u001b[39m# # chain = load_summarize_chain(llm, chain_type=\"map_reduce\")\u001b[39;00m\n\u001b[1;32m     28\u001b[0m \u001b[39m# chain = load_summarize_chain(llm, chain_type=\"refine\")\u001b[39;00m\n\u001b[1;32m     29\u001b[0m \u001b[39m# chain.run([doc.to_langchain_format() for doc in documents])\u001b[39;00m\n", "File \u001b[0;32m~/.local/share/virtualenvs/tofu-IH5mfc4J/lib/python3.9/site-packages/llama_index/response/notebook_utils.py:51\u001b[0m, in \u001b[0;36mdisplay_response\u001b[0;34m(response, source_length)\u001b[0m\n\u001b[1;32m     49\u001b[0m     display(Markdown(\u001b[39m\"\u001b[39m\u001b[39m---\u001b[39m\u001b[39m\"\u001b[39m))\n\u001b[1;32m     50\u001b[0m     display(Markdown(\u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m**`Source Node \u001b[39m\u001b[39m{\u001b[39;00mind\u001b[39m \u001b[39m\u001b[39m+\u001b[39m\u001b[39m \u001b[39m\u001b[39m1\u001b[39m\u001b[39m}\u001b[39;00m\u001b[39m/\u001b[39m\u001b[39m{\u001b[39;00m\u001b[39mlen\u001b[39m(response\u001b[39m.\u001b[39msource_nodes)\u001b[39m}\u001b[39;00m\u001b[39m`**\u001b[39m\u001b[39m\"\u001b[39m))\n\u001b[0;32m---> 51\u001b[0m     display_source_node(source_node, source_length\u001b[39m=\u001b[39;49msource_length)\n\u001b[1;32m     52\u001b[0m \u001b[39mif\u001b[39;00m response\u001b[39m.\u001b[39mextra_info \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[1;32m     53\u001b[0m     display_extra_info(response\u001b[39m.\u001b[39mextra_info)\n", "File \u001b[0;32m~/.local/share/virtualenvs/tofu-IH5mfc4J/lib/python3.9/site-packages/llama_index/response/notebook_utils.py:28\u001b[0m, in \u001b[0;36mdisplay_source_node\u001b[0;34m(source_node, source_length)\u001b[0m\n\u001b[1;32m     22\u001b[0m source_text_fmt \u001b[39m=\u001b[39m truncate_text(source_node\u001b[39m.\u001b[39msource_text\u001b[39m.\u001b[39mstrip(), source_length)\n\u001b[1;32m     23\u001b[0m text_md \u001b[39m=\u001b[39m (\n\u001b[1;32m     24\u001b[0m     \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m**Document ID:** \u001b[39m\u001b[39m{\u001b[39;00msource_node\u001b[39m.\u001b[39mdoc_id\u001b[39m}\u001b[39;00m\u001b[39m<br>\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m     25\u001b[0m     \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m**Similarity:** \u001b[39m\u001b[39m{\u001b[39;00msource_node\u001b[39m.\u001b[39msimilarity\u001b[39m}\u001b[39;00m\u001b[39m<br>\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m     26\u001b[0m     \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m**Text:** \u001b[39m\u001b[39m{\u001b[39;00msource_text_fmt\u001b[39m}\u001b[39;00m\u001b[39m<br>\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m     27\u001b[0m )\n\u001b[0;32m---> 28\u001b[0m \u001b[39mif\u001b[39;00m source_node\u001b[39m.\u001b[39;49mimage \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[1;32m     29\u001b[0m     text_md \u001b[39m+\u001b[39m\u001b[39m=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39m**Image:**\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m     30\u001b[0m display(Markdown(text_md))\n", "\u001b[0;31mAttributeError\u001b[0m: 'SourceNode' object has no attribute 'image'"]}], "source": ["from llama_index import (\n", "    GPTSimpleVectorIndex,\n", "    download_loader,\n", "    GPTListIndex,\n", "    SimpleDirectoryReader,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    Pro<PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from llama_index.response.notebook_utils import display_response\n", "from langchain.chat_models import ChatOpenAI\n", "from langchain.llms import OpenAI\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(\"../.env\")\n", "\n", "llm_predictor = LLMPredictor(\n", "    llm=ChatOpenAI(temperature=0, model_name=\"gpt-3.5-turbo\", max_tokens=512)\n", ")\n", "# llm_predictor = LLMPredictor(llm=OpenAI(temperature=0, model_name=\"text-davinci-003\", max_tokens=1024))\n", "\n", "BeautifulSoupWebReader = download_loader(\"BeautifulSoupWebReader\")\n", "\n", "loader = BeautifulSoupWebReader()\n", "documents = loader.load_data(urls=[\"https://buildkite.com/\"])\n", "# # index = GPTSimpleVectorIndex(documents)\n", "index = GPTListIndex(documents)\n", "\n", "\n", "response = index.query(\n", "    \"Write a 512 words summary. Try to use only the information provided and include as many details as possible.\",\n", "    response_mode=\"compact\",\n", "    llm_predictor=llm_predictor,\n", ")\n", "# # index.query(\"Give me a summary of the website\", similarity_top_k=3, llm_predictor=llm_predictor)\n", "display_response(response)\n", "\n", "# from langchain.chains.summarize import load_summarize_chain\n", "# # llm = OpenAI(temperature=0, model_name=\"text-davinci-003\", max_tokens=512)\n", "# llm=ChatOpenAI(temperature=0, model_name=\"gpt-3.5-turbo\", max_tokens=64)\n", "# # chain = load_summarize_chain(llm, chain_type=\"map_reduce\")\n", "# chain = load_summarize_chain(llm, chain_type=\"refine\")\n", "# chain.run([doc.to_langchain_format() for doc in documents])"]}], "metadata": {"kernelspec": {"display_name": "tofu-IH5mfc4J", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "vscode": {"interpreter": {"hash": "d2f0285ec9736aa4d70dc260702de9434ca912a36542aa30ce506dc877fca5fe"}}}, "nbformat": 4, "nbformat_minor": 5}