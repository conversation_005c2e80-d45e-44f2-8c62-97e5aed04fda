{"cells": [{"cell_type": "code", "execution_count": null, "id": "7fa398f5-035f-4edd-816d-7e2c8f351056", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup, TargetInfo, CompanyInfo\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.object_builder import TargetObjectBuilder, CompanyObjectBuilder\n", "\n", "companies_to_check = [237]\n", "for company_info_id in companies_to_check:\n", "    company_info = CompanyInfo.objects.get(id=company_info_id)\n", "    builder = CompanyObjectBuilder(company_info)\n", "    builder.build_docs(rebuild=True, check_and_rebuild=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "5857dca0-5375-4dda-8610-3a1e914447b9", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup, TargetInfo\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.object_builder import TargetObjectBuilder\n", "\n", "targets_to_check = [40576]\n", "for target_id in targets_to_check:\n", "    target = TargetInfo.objects.get(id=target_id)\n", "    builder = TargetObjectBuilder(target)\n", "    builder.build_docs(rebuild=True, check_and_rebuild=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "6cb2f267-60bb-47cb-a763-9a2b45652a00", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup, TargetInfo, AssetInfo\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.object_builder import TargetObjectBuilder, AssetObjectBuilder\n", "\n", "assets_to_check = [7027]\n", "for asset_id in assets_to_check:\n", "    asset = AssetInfo.objects.get(id=asset_id)\n", "    builder = AssetObjectBuilder(asset)\n", "    builder.build_docs(rebuild=True, check_and_rebuild=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "53bc87e6-f50c-40a4-a169-86b1da214ffd", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.INFO)\n", "\n", "from api.models import Playbook, TargetInfoGroup, TargetInfo, AssetInfo\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.object_builder import ObjectBuilder\n", "from api.playbook_build.doc_loader import DocLoader\n", "\n", "import time\n", "\n", "# For each playbook in the list, get each kind of object (company, asset and target), check if it has index and if so rebuild the index.\n", "playbook_list = Playbook.objects.all()\n", "done_list = []\n", "fail_list = []\n", "for playbook in playbook_list:\n", "    try:\n", "        done_list.append(playbook.id)\n", "        if playbook.id == 286 or playbook.id == 328:\n", "            continue\n", "        playbook_builder = PlaybookBuilder(playbook)\n", "        objects = playbook_builder.get_all_objects()\n", "        print(\"Building index for playbook: \" + str(playbook.id) + \" for user \" + str(playbook.users.first()))\n", "        for object in objects:\n", "            print(object)\n", "            object_builder = ObjectBuilder.get_builder(object)\n", "            if object.index:\n", "                docs, _errors, _doc_status = object_builder.doc_loader.extract_docs(object.docs)\n", "                object_builder.docs_build_processed = DocLoader.optimize_docs(docs)\n", "                object.index = object_builder.create_index()\n", "                print(object.index)\n", "                time.sleep(5)\n", "        print(done_list)\n", "    except Exception as e:\n", "        fail_list.append(playbook.id)\n", "        print(\"Error in playbook: \" + str(playbook.id))\n", "        print(e)\n", "        print(fail_list)\n", "        print(done_list)\n", "    \n", "print(\"Done!\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}