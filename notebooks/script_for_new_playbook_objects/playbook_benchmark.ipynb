{"cells": [{"cell_type": "code", "execution_count": null, "id": "e153165c-e7e1-43ae-a4ca-090ddfa273c3", "metadata": {}, "outputs": [], "source": ["def run_benchmark(playbook_ids):\n", "    import os\n", "    import sys\n", "    import django\n", "    \n", "    # Add the following block to import and setup the django project\n", "    notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "    os.ch<PERSON>(notebook_dir)\n", "    tofu_dir = os.path.dirname(notebook_dir)\n", "    sys.path.append(tofu_dir + '/server')\n", "    os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "    os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "    django.setup()\n", "    # End of django setup block\n", "    \n", "    import logging\n", "    logging.getLogger().setLevel(logging.WARNING)\n", "    \n", "    from api.models import Playbook, TargetInfoGroup, TargetInfo, AssetInfo\n", "    from api.playbook import PlaybookHandler\n", "    from api.serializers import PlaybookSerializer\n", "    from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "    from api.playbook_build.playbook_builder_util import playbook_compare\n", "    \n", "    def run_old_flow(playbook_ids):\n", "        column_ids = [\"company_info\", \"target_info\", \"assets\"]\n", "        for playbook_id in playbook_ids:\n", "            playbook_handler = PlaybookHandler.load_from_db(playbook_id)\n", "            playbook_instance = playbook_handler.playbook_instance\n", "            \n", "            # old flow\n", "            playbook_handler.force_context_full_refresh(column_ids=column_ids)\n", "            \n", "    def run_new_flow(playbook_ids):\n", "        for playbook_id in playbook_ids:\n", "            playbook_handler = PlaybookHandler.load_from_db(playbook_id)\n", "            playbook_instance = playbook_handler.playbook_instance\n", "            \n", "            # new flow\n", "            playbook_builder = PlaybookBuilder(playbook_instance)\n", "            playbook_builder.force_context_full_refresh(column_ids=[])\n", "    \n", "    import time\n", "    def measure_execution_time(func, *args, **kwargs):\n", "        start_time = time.time()\n", "        func(*args, **kwargs)\n", "        end_time = time.time()\n", "        return end_time - start_time\n", "    \n", "    time_new_flow = measure_execution_time(run_new_flow, playbook_ids)\n", "    time_old_flow = measure_execution_time(run_old_flow, playbook_ids)\n", "    \n", "    print(f\"time spend: old flow: {time_old_flow} new flow: {time_new_flow}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "dd7ac0b7-314f-4e3a-bf45-98a2b53ea1d2", "metadata": {}, "outputs": [], "source": ["# playbook 286 has size of targets: 1167\n", "# playbook 183 has size of targets: 1000\n", "# playbook 269 has size of targets: 536\n", "# playbook 111 has size of targets: 529\n", "\n", "# playbook 13 has size of targets: 355\n", "# playbook 33 has size of targets: 284\n", "# playbook 101 has size of targets: 259\n", "# playbook 117 has size of targets: 259\n", "# playbook 69 has size of targets: 259\n", "# playbook 155 has size of targets: 158\n", "# playbook 115 has size of targets: 151\n", "# playbook 265 has size of targets: 149\n", "# playbook 238 has size of targets: 149\n", "# playbook 148 has size of targets: 149\n", "# playbook 248 has size of targets: 126\n", "# playbook 3 has size of targets: 166\n", "# playbook 234 has size of targets: 111\n", "\n", "large_size_benchmark = [111, 183, 269, 286]\n", "\n", "# 245 - tofuadmin-tenable - 35 + 2\n", "# 251 - tofuad<PERSON>-knowde - 40 + 1\n", "# 175 - tofuadmin-gra<PERSON>a - 30 + 0\n", "middle_size_benchmark = [245, 251, 175]\n", "\n", "small_size_benchmark = [143, 273]"]}, {"cell_type": "code", "execution_count": null, "id": "7f2e4f1a-c83e-4537-8ee8-422ca2ee5ed7", "metadata": {}, "outputs": [], "source": ["# run_benchmark(middle_size_benchmark)"]}, {"cell_type": "code", "execution_count": null, "id": "9012e713-9161-43ec-916c-7644f8499536", "metadata": {}, "outputs": [], "source": ["# profiling\n", "import cProfile\n", "cProfile.run('run_benchmark(small_size_benchmark)', 'profile_stats.prof')"]}, {"cell_type": "code", "execution_count": null, "id": "db52fb1a-3856-4d99-a6c4-baf3c47ffd67", "metadata": {}, "outputs": [], "source": ["# profiling display\n", "import pstats\n", "import re\n", "\n", "p = pstats.Stats('profile_stats.prof')\n", "p.sort_stats('tottime').print_stats()"]}, {"cell_type": "code", "execution_count": null, "id": "6b88d07f-62e9-44ce-a212-98d7198572aa", "metadata": {}, "outputs": [], "source": ["p.sort_stats('cumtime').print_stats(100)"]}, {"cell_type": "code", "execution_count": null, "id": "166436b7-2cd6-458c-a0cd-41052793016d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}