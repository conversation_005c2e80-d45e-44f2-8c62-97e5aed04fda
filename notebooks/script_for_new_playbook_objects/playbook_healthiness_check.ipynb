{"cells": [{"cell_type": "code", "execution_count": null, "id": "27ffa2d7-8f1c-4bf5-94b9-41c15b383afa", "metadata": {"scrolled": true}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.scheduled_tasks import health_check\n", "from api.playbook_health import CheckOption\n", "\n", "# Create CheckOption with specific settings\n", "check_options_action_only = CheckOption(\n", "    playbook_list=[282, 287, 868],\n", "    check_playbook=False,\n", "    check_campaign=False,\n", "    check_content=False,\n", "    check_content_group=False,\n", "    check_content_variation=False,\n", "    check_action=True,\n", "    check_asset=False,\n", "    check_target=False,\n", ")\n", "\n", "# Run health check with specific parameters\n", "health_check(\n", "    test_user=False,\n", "    test_playbook=True,\n", "    check_options=check_options_action_only\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "b96085ca-0687-4fe7-9fb9-be533138bd2c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}