{"cells": [{"cell_type": "code", "execution_count": null, "id": "0b4fcdea-49da-4eb2-93be-037696b200b9", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TofuUser\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.playbook_builder_util import playbook_compare\n", "\n", "\n", "all_users = TofuUser.objects.all()\n", "for user in all_users:\n", "    if user.username.startswith('tofuadmin'):\n", "        playbooks = Playbook.objects.filter(users=user)\n", "        if len(playbooks) > 1:\n", "            print(f\"error: more than one playbook for user {user.username}\")\n", "        else:\n", "            if not playbooks or playbooks[0].id != 286:\n", "                # enable it\n", "                print(f\"enabling for {user.username} with playbos: {playbooks}\")\n", "                user.context['enableTargetObject'] = True\n", "                # user.save()"]}, {"cell_type": "code", "execution_count": null, "id": "f5c8a735-ac20-4df2-938e-f02ff2308b59", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}