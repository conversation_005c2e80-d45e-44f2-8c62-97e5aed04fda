{"cells": [{"cell_type": "code", "execution_count": null, "id": "4caf4490-8686-4a84-a11e-c1ff296d6570", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from django.db.models import Q, Max, F, OuterRef, Subquery\n", "from django.utils import timezone\n", "from datetime import timedelta\n", "from api.models import Playbook, TargetInfoGroup, AssetInfoGroup, TargetInfo, AssetInfo, Campaign, ContentGroup, Content\n", "from django.db.models.functions import Greatest\n", "\n", "def get_inactive_playbooks(months=6):\n", "    # Calculate the cutoff date\n", "    cutoff_date = timezone.now() - <PERSON><PERSON><PERSON>(days=30 * months)\n", "    \n", "    # Get latest activity time for each playbook by combining all related objects\n", "    inactive_playbooks = Playbook.objects.annotate(\n", "        # Get latest creation from playbook itself\n", "        playbook_creation=F('created_at'),\n", "        \n", "        # Get latest creation from directly related objects\n", "        latest_target_group=Subquery(\n", "            TargetInfoGroup.objects.filter(playbook=OuterRef('pk'))\n", "            .order_by('-created_at')\n", "            .values('created_at')[:1]\n", "        ),\n", "        latest_asset_group=Subquery(\n", "            AssetInfoGroup.objects.filter(playbook=OuterRef('pk'))\n", "            .order_by('-created_at')\n", "            .values('created_at')[:1]\n", "        ),\n", "        latest_target_info=Subquery(\n", "            TargetInfo.objects.filter(target_info_group__playbook=OuterRef('pk'))\n", "            .order_by('-created_at')\n", "            .values('created_at')[:1]\n", "        ),\n", "        latest_asset_info=Subquery(\n", "            AssetInfo.objects.filter(asset_info_group__playbook=OuterRef('pk'))\n", "            .order_by('-created_at')\n", "            .values('created_at')[:1]\n", "        ),\n", "        latest_campaign=Subquery(\n", "            Campaign.objects.filter(playbook=OuterRef('pk'))\n", "            .order_by('-created_at')\n", "            .values('created_at')[:1]\n", "        ),\n", "        latest_content_group=Subquery(\n", "            ContentGroup.objects.filter(campaign__playbook=OuterRef('pk'))\n", "            .order_by('-created_at')\n", "            .values('created_at')[:1]\n", "        ),\n", "        latest_content=Subquery(\n", "            Content.objects.filter(playbook=OuterRef('pk'))\n", "            .order_by('-created_at')\n", "            .values('created_at')[:1]\n", "        ),\n", "        company_info_creation=F('company_object__created_at'),\n", "        \n", "        # Calculate the latest activity across all related objects\n", "        latest_activity=Greatest(\n", "            F('playbook_creation'),\n", "            F('latest_target_group'),\n", "            F('latest_asset_group'),\n", "            F('latest_target_info'),\n", "            F('latest_asset_info'),\n", "            F('latest_campaign'),\n", "            F('latest_content_group'),\n", "            F('latest_content'),\n", "            F('company_info_creation'),\n", "        )\n", "    ).filter(\n", "        latest_activity__lt=cutoff_date\n", "    ).order_by('latest_activity')\n", "\n", "    return inactive_playbooks\n", "    \n", "import csv\n", "from datetime import datetime\n", "\n", "# Get inactive playbooks\n", "inactive_playbooks = get_inactive_playbooks(months=6)\n", "\n", "# Define the CSV filename with timestamp\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "csv_filename = f'inactive_playbooks_{timestamp}.csv'\n", "\n", "# Write to CSV\n", "with open(csv_filename, 'w', newline='') as csvfile:\n", "    writer = csv.writer(csvfile)\n", "    # Write header\n", "    writer.writerow(['Playbook ID', 'Playbook Name', 'User Name', 'Latest Activity'])\n", "    \n", "    # Write data\n", "    for playbook in inactive_playbooks:\n", "        user = playbook.get_owner_or_first_user()\n", "        writer.writerow([\n", "            playbook.id,\n", "            playbook.name,\n", "            user.username if user else None,\n", "            playbook.latest_activity\n", "        ])\n", "\n", "print(f\"Data has been saved to {csv_filename}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f5eac8e4-9e4d-482a-b4b2-1768417dc52e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}