{"cells": [{"cell_type": "code", "execution_count": null, "id": "9d5f2e41-c951-4bc9-8532-fdc68abd0779", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.playbook_builder_util import playbook_compare\n", "\n", "for playbook_id in range(210, 290):\n", "    playbook = Playbook.objects.get(id=playbook_id)\n", "    print(\"checking for playbook:\", playbook.id)\n", "\n", "    try:\n", "        # playbook_handler = PlaybookHandler.load_from_db(playbook.id)\n", "        # healthiness, column_ids_to_rebuild, errors = playbook_handler.check_healthiness()\n", "        # if not healthiness:\n", "        #     print(f\"[ERROR]: Playbook {playbook.id} under {creator.username} is not healthy for columns {column_ids_to_rebuild} with errors: {errors}\")\n", "        #     playbook_handler.force_context_full_refresh(column_ids_to_rebuild)\n", "        # else:\n", "        #     print(f\"[INFO]: Playbook {playbook.id} is good\")\n", "\n", "        playbook_builder = PlaybookBuilder(playbook)\n", "        \n", "        healthiness, errors, objects_to_rebuild = playbook_builder.check_healthiness()\n", "        if healthiness == PlaybookStatus.ERROR:\n", "            print(f\"[ERROR]: Playbook {playbook.id} is not healthy for with errors: {errors}\")\n", "            playbook_builder.force_context_full_refresh(column_ids=[], objects_to_rebuild=objects_to_rebuild)\n", "        elif healthiness == PlaybookStatus.IN_PROGRESS:\n", "            print(f\"[WARNING]: Playbook {playbook.id} is in progress, skip\")\n", "            # playbook_builder.force_context_full_refresh(column_ids=[], objects_to_rebuild=[])\n", "            # playbook_compare(playbook, enable_on_parity_eval = True)\n", "        else:\n", "            print(f\"[INFO]: New playbook {playbook.id} is good\")\n", "            # playbook_compare(playbook, enable_on_parity_eval = True)\n", "            # playbook_compare(playbook)\n", "        \n", "    except Exception as e:\n", "        print(\"fail to build due to:\", e)\n", "        raise e\n", "        continue\n"]}, {"cell_type": "code", "execution_count": null, "id": "c1ac42dd-a236-4a62-8ac9-acf794351e4d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}