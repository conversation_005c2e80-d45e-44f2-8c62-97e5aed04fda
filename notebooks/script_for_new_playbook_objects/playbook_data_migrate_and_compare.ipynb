{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc539d23-8bd2-4887-bd20-d27e22dd803a", "metadata": {}, "outputs": [], "source": ["## data comparison\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder\n", "from api.playbook_build.playbook_builder_util import (\n", "    playbook_object_compare,\n", ")\n", "\n", "all_playbooks = Playbook.objects.all()\n", "for playbook in all_playbooks:\n", "    if playbook_object_compare(playbook):\n", "        print(f\"Mismatch for playbook {playbook.id}\")"]}, {"cell_type": "code", "execution_count": null, "id": "5176e03a-985e-4e2b-a2df-6c5ece598957", "metadata": {}, "outputs": [], "source": ["# data migration\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder\n", "from api.playbook_build.playbook_builder_util import (\n", "    playbook_object_compare,\n", ")\n", "\n", "all_playbooks = Playbook.objects.all()\n", "for playbook in all_playbooks:\n", "    # creator = playbook.users.first()\n", "    # if not creator or not creator.username.startswith('tofuadmin'):\n", "    #     # print(f\"playbook {playbook.id} not the user we want to handle {creator.username if creator else 'no_creator'}\")\n", "    #     continue\n", "\n", "    print(\"checking for playbook:\", playbook.id)\n", "\n", "    serializer = PlaybookSerializer(instance=playbook)\n", "    try:\n", "        update_serializer = PlaybookSerializer(instance=playbook, data=serializer.data)\n", "        if update_serializer.is_valid():\n", "            update_serializer.save()\n", "        else:\n", "            print(\"Error: \", update_serializer.errors)\n", "    except Exception as e:\n", "        print(\"fail to build due to:\", e)\n", "        raise e\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "id": "56de8ac6-93ac-4d1c-aae3-564288b9d177", "metadata": {}, "outputs": [], "source": ["# create company object if there isn't\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder\n", "from api.playbook_build.playbook_builder_util import (\n", "    playbook_object_compare,\n", ")\n", "\n", "all_playbooks = Playbook.objects.all()\n", "for playbook in all_playbooks:\n", "    if playbook.company_object:\n", "        continue\n", "    print(f\"update for: playbook: {playbook.id}\")\n", "\n", "    serializer = PlaybookSerializer(instance=playbook)\n", "    try:\n", "        update_serializer = PlaybookSerializer(instance=playbook, data=serializer.data)\n", "        if update_serializer.is_valid():\n", "            update_serializer.save()\n", "        else:\n", "            print(\"Error: \", update_serializer.errors)\n", "    except Exception as e:\n", "        print(\"fail to build due to:\", e)\n", "        raise e\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "id": "d265bf3c-6ded-43d9-9fd3-78891a89a5aa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}