{"cells": [{"cell_type": "code", "execution_count": null, "id": "67621095-4890-46dc-8b87-7a3b9038a8ac", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetGroup\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.playbook_builder_util import playbook_compare\n", "\n", "all_playbooks = Playbook.objects.all()\n", "\n", "playbooks = {\n", "    # 31: 'tofuadmin-schema',\n", "    # 138: 'tofuadmin-deepgram',\n", "    225: \"tofuadmin-zeotap\",\n", "    224: \"tofuadmin-ternary\",\n", "    199: \"tofuadmin-outsized\",\n", "    # 75: 'tofuadmin-ash',\n", "    # 51: 'tofuadmin-meq',\n", "    # 77: 'tofuadmin-laetro',\n", "    173: \"tofuadmin-workato\",\n", "    222: \"tofuadmin-cliezen\",\n", "    # 155: 'tofuadmin-evisort',\n", "    230: \"tofuadmin-marketerhire\",\n", "    273: \"tofuadmin-onetrust\",\n", "    22: \"tofuadmin-usergems\",\n", "    249: \"tofuadmin-koddi\",\n", "    182: \"tofuadmin-verkada\",\n", "    220: \"tofuadmin-highspot\",\n", "    93: \"tofuadmin-datwony\",\n", "    48: \"tofuadmin-matrix\",\n", "    27: \"tofuadmin-drata\",\n", "    15: \"tofuadmin-argyle\",\n", "    256: \"tofuadmin-roboflow\",\n", "    69: \"tofuadmin-igloo\",\n", "    32: \"tofuadmin-humanitec\",\n", "    24: \"tofuadmin-picnichealth\",\n", "    # 13: 'tofuadmin-stampli',\n", "    # 102: 'tofuadmin-ignloo',\n", "    # 85: 'tofuadmin-fraction',\n", "    # 100: 'tofuadmin-golioth',\n", "    # 201: 'tofuadmin-rebuy',\n", "    # 162: 'tofuadmin-amplifai',\n", "    # 8: 'tofuadmin-demandwell',\n", "    # 29: 'tofuadmin-dean',\n", "    # 165: 'tofuadmin-lacework',\n", "    # 175: 'tofuadmin-grafana',\n", "    # 7: 'tofuadmin-mosaic',\n", "    # 163: 'tofuadmin-vividly',\n", "    # 176: 'tofuadmin-clari',\n", "    # 127: 'tofuadmin-bluecore',\n", "    # 168: 'tofuadmin-seismic',\n", "    # 149: 'tofuadmin-servicetitan',\n", "    # 26: 'tofuadmin-vultr',\n", "    # 1: 'tofuadmin',\n", "    # 16: 'tofuadmin-hstpathways',\n", "    # 81: 'tofuadmin-olivine',\n", "    # 283: 'tofuadmin-zuora',\n", "    # 196: 'tofuadmin-evernow',\n", "    # 200: 'tofuadmin-hypr',\n", "    # 177: 'tofuadmin-vercel',\n", "    # 233: 'tofuadmin-upperinc',\n", "    # 80: 'tofuadmin-tofu',\n", "    # 198: 'tofuadmin-vanta',\n", "    # 202: 'tofuadmin-northstar',\n", "    # 94: 'tofuadmin-danny',\n", "    # 205: 'tofuadmin-keepfinancial',\n", "    # 188: 'tofuadmin-flocksafety',\n", "    # 10: 'tofuadmin-wheel',\n", "    # 17: 'tofuadmin-buildkite',\n", "    # 232: 'tofuadmin-learnworlds',\n", "    # 276: 'tofuadmin-iab',\n", "    # 221: 'tofuadmin-zluri',\n", "    # 228: 'tofuadmin-duplocloud',\n", "    # 235: 'tofuadmin-spaceo',\n", "    # 167: 'tofuadmin-brightplan',\n", "    # 270: 'tofuadmin-skedsocial',\n", "    # 251: 'tofuadmin-knowde',\n", "    # 240: 'tofuadmin-brightai',\n", "    # 195: 'tofuadmin-reprise',\n", "    # 272: 'tofuadmin-offerfit',\n", "    # 239: 'tofuadmin-cakewalk',\n", "    # 223: 'tofuadmin-monad',\n", "    # 253: 'tofuadmin-bardeen',\n", "    # 242: 'tofuadmin-groundswell',\n", "    # 241: 'tofuadmin-peruseml',\n", "    # 181: 'tofuadmin-vida',\n", "    # 274: 'tofuadmin-onloop',\n", "    # 245: 'tofuadmin-tenable',\n", "    # 252: 'tofuadmin-pinwheel',\n", "    # 254: 'tofuadmin-streamline',\n", "    # 142: 'tofuadmin-turntide',\n", "    # 271: 'tofuadmin-bikky',\n", "    # 247: 'tofuadmin-meter',\n", "    # 234: 'tofuadmin-upper',\n", "    # 260: 'tofuadmin-bloomfilter',\n", "    # 255: 'tofuadmin-washn',\n", "    # 148: 'tofuadmin-wunderkind',\n", "    # 259: 'tofuadmin-stacksync',\n", "    # 275: 'tofuadmin-caura',\n", "    # 281: 'tofuadmin-boon',\n", "    # 231: 'tofuadmin-cocoon',\n", "    # 280: 'tofuadmin-getrev',\n", "    # 174: 'tofuadmin-avanan',\n", "    # 257: 'tofuadmin-vivdly',\n", "    # 170: 'tofuadmin-retool',\n", "    # 183: 'tofuadmin-largetargets',\n", "    # 74: 'tofuadmin-uipath',\n", "}\n", "playbooks_rebuild = []\n", "\n", "for playbook in all_playbooks:\n", "    creator = playbook.users.first()\n", "    if not creator or not creator.username.startswith(\"tofuadmin\"):\n", "        # print(f\"playbook {playbook.id} not the user we want to handle {creator.username if creator else 'no_creator'}\")\n", "        continue\n", "\n", "    if playbook.id not in playbooks:\n", "        continue\n", "    print(\"checking for playbook:\", playbook.id)\n", "\n", "    try:\n", "        playbook_handler = PlaybookHandler.load_from_db(playbook.id)\n", "        (\n", "            healthiness,\n", "            column_ids_to_rebuild,\n", "            errors,\n", "        ) = playbook_handler.check_healthiness()\n", "        if not healthiness:\n", "            print(\n", "                f\"[ERROR]: Playbook {playbook.id} under {creator.username} is not healthy for columns {column_ids_to_rebuild} with errors: {errors}\"\n", "            )\n", "            playbook_handler.force_context_full_refresh(column_ids_to_rebuild)\n", "        else:\n", "            print(f\"[INFO]: Playbook {playbook.id} is good\")\n", "\n", "        playbook_builder = PlaybookBuilder(playbook)\n", "        healthiness, errors, objects_to_rebuild = playbook_builder.check_healthiness()\n", "        if healthiness == PlaybookStatus.ERROR:\n", "            print(\n", "                f\"[ERROR]: Playbook {playbook.id} under {creator.username} is not healthy for columns {column_ids_to_rebuild} with errors: {errors}\"\n", "            )\n", "            playbook_builder.force_context_full_refresh(\n", "                column_ids=column_ids_to_rebuild\n", "            )\n", "        elif healthiness == PlaybookStatus.IN_PROGRESS:\n", "            print(\n", "                f\"[WARNING]: Playbook {playbook.id} under {creator.username} is in progress, skip\"\n", "            )\n", "        else:\n", "            print(f\"[INFO]: New playbook {playbook.id} is good\")\n", "\n", "    except Exception as e:\n", "        print(\"fail to build due to:\", e)\n", "        continue\n", "\n", "    playbook_compare(playbook)"]}, {"cell_type": "code", "execution_count": null, "id": "e5a400fe-9991-4791-b43e-4a7246b46d96", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetGroup\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.playbook_builder_util import playbook_compare\n", "\n", "all_playbooks = Playbook.objects.all()\n", "\n", "playbooks = {\n", "    # 31: 'tofuadmin-schema',\n", "    # 138: 'tofuadmin-deepgram',\n", "    225: 'tofuadmin-zeotap',\n", "    224: 'tofuadmin-ternary',\n", "    199: 'tofuadmin-outsized', \n", "    # 75: 'tofuadmin-ash',\n", "    # 51: 'tofuadmin-meq',\n", "    # 77: 'tofuadmin-laetro',\n", "    173: 'tofuadmin-workato',\n", "    222: 'tofuadmin-cliezen',\n", "    # 155: 'tofuadmin-evisort',\n", "    230: 'tofuadmin-marketerhire',\n", "    273: 'tofuadmin-onetrust',\n", "    22: 'tofuadmin-usergems',\n", "    249: 'tofuadmin-koddi',\n", "    182: 'tofuadmin-verkada',\n", "    220: 'tofuadmin-highspot',\n", "    93: 'tofuadmin-datwony',\n", "    48: 'tofuadmin-matrix',\n", "    27: 'tofuadmin-drata',\n", "    15: 'tofuadmin-argyle',\n", "    256: 'tofuadmin-roboflow',\n", "    69: 'tofuadmin-igloo',\n", "    32: 'tofuadmin-humanitec',\n", "    24: 'tofuadmin-picnichealth',\n", "    # 13: 'tofuadmin-stampli',\n", "    # 102: 'tofuadmin-ignloo',\n", "    # 85: 'tofuadmin-fraction',\n", "    # 100: 'tofuadmin-golioth',\n", "    # 201: 'tofuadmin-rebuy',\n", "    # 162: 'tofuadmin-amplifai',\n", "    # 8: 'tofuadmin-demandwell',\n", "    # 29: 'tofuadmin-dean',\n", "    # 165: 'tofuadmin-lacework',\n", "    # 175: 'tofuadmin-grafana',\n", "    # 7: 'tofuadmin-mosaic',\n", "    # 163: 'tofuadmin-vividly',\n", "    # 176: 'tofuadmin-clari',\n", "    # 127: 'tofuadmin-bluecore',\n", "    # 168: 'tofuadmin-seismic',\n", "    # 149: 'tofuadmin-servicetitan',\n", "    # 26: 'tofuadmin-vultr',\n", "    # 1: 'tofuadmin',\n", "    # 16: 'tofuadmin-hstpathways',\n", "    # 81: 'tofuadmin-olivine',\n", "    # 283: 'tofuadmin-zuora',\n", "    # 196: 'tofuadmin-evernow',\n", "    # 200: 'tofuadmin-hypr',\n", "    # 177: 'tofuadmin-vercel',\n", "    # 233: 'tofuadmin-upperinc',\n", "    # 80: 'tofuadmin-tofu',\n", "    # 198: 'tofuadmin-vanta',\n", "    # 202: 'tofuadmin-northstar',\n", "    # 94: 'tofuadmin-danny',\n", "    # 205: 'tofuadmin-keepfinancial',\n", "    # 188: 'tofuadmin-flocksafety',\n", "    # 10: 'tofuadmin-wheel',\n", "    # 17: 'tofuadmin-buildkite',\n", "    # 232: 'tofuadmin-learnworlds',\n", "    # 276: 'tofuadmin-iab',\n", "    # 221: 'tofuadmin-zluri',\n", "    # 228: 'tofuadmin-duplocloud',\n", "    # 235: 'tofuadmin-spaceo',\n", "    # 167: 'tofuadmin-brightplan',\n", "    # 270: 'tofuadmin-skedsocial',\n", "    # 251: 'tofuadmin-knowde',\n", "    # 240: 'tofuadmin-brightai',\n", "    # 195: 'tofuadmin-reprise',\n", "    # 272: 'tofuadmin-offerfit',\n", "    # 239: 'tofuadmin-cakewalk',\n", "    # 223: 'tofuadmin-monad',\n", "    # 253: 'tofuadmin-bardeen',\n", "    # 242: 'tofuadmin-groundswell',\n", "    # 241: 'tofuadmin-peruseml',\n", "    # 181: 'tofuadmin-vida',\n", "    # 274: 'tofuadmin-onloop',\n", "    # 245: 'tofuadmin-tenable',\n", "    # 252: 'tofuadmin-pinwheel',\n", "    # 254: 'tofuadmin-streamline',\n", "    # 142: 'tofuadmin-turntide',\n", "    # 271: 'tofuadmin-bikky',\n", "    # 247: 'tofuadmin-meter',\n", "    # 234: 'tofuadmin-upper',\n", "    # 260: 'tofuadmin-bloomfilter',\n", "    # 255: 'tofuadmin-washn',\n", "    # 148: 'tofuadmin-wunderkind',\n", "    # 259: 'tofuadmin-stacksync',\n", "    # 275: 'tofuadmin-caura',\n", "    # 281: 'tofuadmin-boon',\n", "    # 231: 'tofuadmin-cocoon',\n", "    # 280: 'tofuadmin-getrev',\n", "    # 174: 'tofuadmin-avanan',\n", "    # 257: 'tofuadmin-vivdly',\n", "    # 170: 'tofuadmin-retool',\n", "\n", "    # 183: 'tofuadmin-largetargets',\n", "    # 74: 'tofuadmin-uipath',\n", "}\n", "playbooks_rebuild = []\n", "\n", "for playbook in all_playbooks:\n", "    creator = playbook.users.first()\n", "    if not creator:\n", "        continue\n", "    # if not creator or not creator.username.startswith('tofuadmin'):\n", "    #     # print(f\"playbook {playbook.id} not the user we want to handle {creator.username if creator else 'no_creator'}\")\n", "    #     continue\n", "\n", "    # if playbook.id not in playbooks:\n", "    #     continue\n", "    if playbook.id <= 270:\n", "        continue\n", "    print(\"checking for playbook:\", playbook.id)\n", "\n", "    try:\n", "        # playbook_handler = PlaybookHandler.load_from_db(playbook.id)\n", "        # healthiness, column_ids_to_rebuild, errors = playbook_handler.check_healthiness()\n", "        # if not healthiness:\n", "        #     print(f\"[ERROR]: Playbook {playbook.id} under {creator.username} is not healthy for columns {column_ids_to_rebuild} with errors: {errors}\")\n", "        #     playbook_handler.force_context_full_refresh(column_ids_to_rebuild)\n", "        # else:\n", "        #     print(f\"[INFO]: Playbook {playbook.id} is good\")\n", "\n", "        playbook_builder = PlaybookBuilder(playbook)\n", "        healthiness, errors, objects_to_rebuild = playbook_builder.check_healthiness()\n", "        if healthiness == PlaybookStatus.ERROR:\n", "            print(f\"[ERROR]: Playbook {playbook.id} under {creator.username} is not healthy for with errors: {errors}\")\n", "            # playbook_builder.force_context_full_refresh(column_ids=[], objects_to_rebuild=objects_to_rebuild)\n", "        elif healthiness == PlaybookStatus.IN_PROGRESS:\n", "            print(f\"[WARNING]: Playbook {playbook.id} under {creator.username} is in progress, skip\")\n", "        else:\n", "            print(f\"[INFO]: New playbook {playbook.id} is good\")\n", "            playbook_compare(playbook)\n", "        \n", "    except Exception as e:\n", "        print(\"fail to build due to:\", e)\n", "        raise e\n", "        continue\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "0151a4e8-baa2-4c5e-a31b-c215d7810a81", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetGroup\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.playbook_builder_util import playbook_compare\n", "\n", "for playbook_id in range(268, 270):\n", "    playbook = Playbook.objects.get(id=playbook_id)\n", "    print(\"checking for playbook:\", playbook.id)\n", "\n", "    try:\n", "        # playbook_handler = PlaybookHandler.load_from_db(playbook.id)\n", "        # healthiness, column_ids_to_rebuild, errors = playbook_handler.check_healthiness()\n", "        # if not healthiness:\n", "        #     print(f\"[ERROR]: Playbook {playbook.id} under {creator.username} is not healthy for columns {column_ids_to_rebuild} with errors: {errors}\")\n", "        #     playbook_handler.force_context_full_refresh(column_ids_to_rebuild)\n", "        # else:\n", "        #     print(f\"[INFO]: Playbook {playbook.id} is good\")\n", "\n", "        playbook_builder = PlaybookBuilder(playbook)\n", "        # playbook_builder.force_context_full_refresh(column_ids=[], objects_to_rebuild=objects_to_rebuild)\n", "        \n", "        healthiness, errors, objects_to_rebuild = playbook_builder.check_healthiness()\n", "        if healthiness == PlaybookStatus.ERROR:\n", "            print(f\"[ERROR]: Playbook {playbook.id} is not healthy for with errors: {errors}\")\n", "            # playbook_builder.force_context_full_refresh(column_ids=[], objects_to_rebuild=objects_to_rebuild)\n", "        elif healthiness == PlaybookStatus.IN_PROGRESS:\n", "            print(f\"[WARNING]: Playbook {playbook.id} is in progress, skip\")\n", "        else:\n", "            print(f\"[INFO]: New playbook {playbook.id} is good\")\n", "            playbook_compare(playbook)\n", "        \n", "    except Exception as e:\n", "        print(\"fail to build due to:\", e)\n", "        raise e\n", "        continue\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "id": "ff2fc4b2-13a9-4701-b493-d43797273ad0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-12-19 14:35:40,583 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:35:40,586 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:35:40,593 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:35:40,597 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:35:59,175 ERROR: Failed to load data from url: https://www.carawayhome.com/\n", "2023-12-19 14:35:59,180 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.carawayhome.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:35:59,186 ERROR: Failed to load data from url: https://www.carawayhome.com/\n", "2023-12-19 14:35:59,190 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.carawayhome.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:35:59,196 ERROR: Failed to load data from url: https://tialupitafoods.com/\n", "2023-12-19 14:35:59,199 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Ftialupitafoods.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:35:59,204 ERROR: Failed to load data from url: https://tialupitafoods.com/\n", "2023-12-19 14:35:59,208 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Ftialupitafoods.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:35:59,285 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:35:59,291 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:35:59,301 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:35:59,305 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:36:00,816 ERROR: Failed to load data from url: https://company1.com\n", "2023-12-19 14:36:00,820 ERROR: Error 500 when crawling https://company1.com\n", "2023-12-19 14:36:00,825 ERROR: Failed to load data from url: https://company1.com\n", "2023-12-19 14:36:00,828 ERROR: Error 500 when crawling https://company1.com\n", "2023-12-19 14:36:00,833 ERROR: Failed to load data from url: https://company1.com\n", "2023-12-19 14:36:00,836 ERROR: Error 500 when crawling https://company1.com\n", "2023-12-19 14:36:00,839 ERROR: Failed to load data from url: https://company1.com\n", "2023-12-19 14:36:00,842 ERROR: Error 500 when crawling https://company1.com\n", "2023-12-19 14:36:16,059 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:36:16,066 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:36:16,074 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:36:16,079 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:36:18,966 ERROR: Failed to load data from url: https://figma.com\n", "2023-12-19 14:36:18,981 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Ffigma.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:18,994 ERROR: Failed to load data from url: https://figma.com\n", "2023-12-19 14:36:19,001 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Ffigma.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:19,009 ERROR: Failed to load data from url: https://slack.com\n", "2023-12-19 14:36:19,014 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fslack.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:19,021 ERROR: Failed to load data from url: https://slack.com\n", "2023-12-19 14:36:19,026 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fslack.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:19,030 ERROR: Failed to load data from url: https://okta.com\n", "2023-12-19 14:36:19,034 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fokta.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:19,039 ERROR: Failed to load data from url: https://okta.com\n", "2023-12-19 14:36:19,042 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fokta.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:19,046 ERROR: Failed to load data from url: https://salesforce.com\n", "2023-12-19 14:36:19,050 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fsalesforce.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:19,053 ERROR: Failed to load data from url: https://salesforce.com\n", "2023-12-19 14:36:19,057 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fsalesforce.com&render_js=false (Caused by None)\n", "2023-12-19 14:36:26,885 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:36:26,896 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:36:26,906 ERROR: Failed to load data from url: https://autozone.com\n", "2023-12-19 14:36:26,913 ERROR: Error 500 when crawling https://autozone.com\n", "2023-12-19 14:36:32,156 ERROR: Failed to load data from url: https://www.carawayhome.com/\n", "2023-12-19 14:36:32,160 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.carawayhome.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:36:32,165 ERROR: Failed to load data from url: https://www.carawayhome.com/\n", "2023-12-19 14:36:32,169 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.carawayhome.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:36:32,173 ERROR: Failed to load data from url: https://tialupitafoods.com/\n", "2023-12-19 14:36:32,176 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Ftialupitafoods.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:36:32,180 ERROR: Failed to load data from url: https://tialupitafoods.com/\n", "2023-12-19 14:36:32,183 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Ftialupitafoods.com%2F&render_js=false (Caused by None)\n", "2023-12-19 14:37:04,188 ERROR: Failed to load data from s3_filename: f36ef968-5825-4497-d55c-4f54e9ce07af with exception Invalid file /var/folders/hg/vrskr9612n744vd4qv9dtf500000gn/T/tmpd843t9te/f36ef968-5825-4497-d55c-4f54e9ce07af. The FileType.UNK file type is not supported in partition.\n", "2023-12-19 14:37:04,489 ERROR: Failed to load data from s3_filename: f36ef968-5825-4497-d55c-4f54e9ce07af with exception Invalid file /var/folders/hg/vrskr9612n744vd4qv9dtf500000gn/T/tmp10354mav/f36ef968-5825-4497-d55c-4f54e9ce07af. The FileType.UNK file type is not supported in partition.\n", "2023-12-19 14:37:04,514 ERROR: Failed to load data from url: https://www.bankofny.com\n", "2023-12-19 14:37:04,519 ERROR: Error 500 when crawling https://www.bankofny.com\n", "2023-12-19 14:37:04,524 ERROR: Failed to load data from url: https://www.bankofny.com\n", "2023-12-19 14:37:04,530 ERROR: Error 500 when crawling https://www.bankofny.com\n", "2023-12-19 14:37:04,537 ERROR: Failed to load data from url: https://LSO.IDAHO.GOV\n", "2023-12-19 14:37:04,541 ERROR: Error 500 when crawling https://LSO.IDAHO.GOV\n", "2023-12-19 14:37:04,546 ERROR: Failed to load data from url: https://LSO.IDAHO.GOV\n", "2023-12-19 14:37:04,550 ERROR: Error 500 when crawling https://LSO.IDAHO.GOV\n", "2023-12-19 14:37:04,555 ERROR: Failed to load data from url: https://www.avanan.com/email-security-in-the-age-of-saas\n", "2023-12-19 14:37:04,559 ERROR: Error 404 when crawling https://www.avanan.com/email-security-in-the-age-of-saas\n", "2023-12-19 14:37:04,562 ERROR: Failed to load data from url: https://www.avanan.com/email-security-in-the-age-of-saas\n", "2023-12-19 14:37:04,565 ERROR: Error 404 when crawling https://www.avanan.com/email-security-in-the-age-of-saas\n", "2023-12-19 14:37:04,571 ERROR: Failed to load data from url: https://ati.pe.gov.br\n", "2023-12-19 14:37:04,574 ERROR: Error 500 when crawling https://ati.pe.gov.br\n", "2023-12-19 14:37:04,577 ERROR: Failed to load data from url: https://ati.pe.gov.br\n", "2023-12-19 14:37:04,580 ERROR: Error 500 when crawling https://ati.pe.gov.br\n", "2023-12-19 14:37:04,584 ERROR: Failed to load data from url: https://qg.com.pe\n", "2023-12-19 14:37:04,587 ERROR: Error 500 when crawling https://qg.com.pe\n", "2023-12-19 14:37:04,590 ERROR: Failed to load data from url: https://qg.com.pe\n", "2023-12-19 14:37:04,592 ERROR: Error 500 when crawling https://qg.com.pe\n", "2023-12-19 14:37:04,598 ERROR: Failed to load data from url: https://www.rbccm.com\n", "2023-12-19 14:37:04,600 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.rbccm.com&render_js=false (Caused by None)\n", "2023-12-19 14:37:04,603 ERROR: Failed to load data from url: https://www.rbccm.com\n", "2023-12-19 14:37:04,606 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.rbccm.com&render_js=false (Caused by None)\n", "2023-12-19 14:37:04,610 ERROR: Failed to load data from url: https://www.vtg.admin.ch/de/organisation/kdo-op/lw.html\n", "2023-12-19 14:37:04,613 ERROR: Error 404 when crawling https://www.vtg.admin.ch/de/organisation/kdo-op/lw.html\n", "2023-12-19 14:37:04,616 ERROR: Failed to load data from url: https://www.vtg.admin.ch/de/organisation/kdo-op/lw.html\n", "2023-12-19 14:37:04,619 ERROR: Error 404 when crawling https://www.vtg.admin.ch/de/organisation/kdo-op/lw.html\n", "2023-12-19 14:37:04,627 ERROR: Failed to load data from url: https://saude.ce.gov.br\n", "2023-12-19 14:37:04,630 ERROR: Error 404 when crawling https://saude.ce.gov.br\n", "2023-12-19 14:37:04,633 ERROR: Failed to load data from url: https://saude.ce.gov.br\n", "2023-12-19 14:37:04,637 ERROR: Error 404 when crawling https://saude.ce.gov.br\n", "2023-12-19 14:37:04,641 ERROR: Failed to load data from url: https://aon.it\n", "2023-12-19 14:37:04,648 ERROR: Error 500 when crawling https://aon.it\n", "2023-12-19 14:37:04,658 ERROR: Failed to load data from url: https://aon.it\n", "2023-12-19 14:37:04,661 ERROR: Error 500 when crawling https://aon.it\n", "2023-12-19 14:37:04,671 ERROR: Failed to load data from url: https://cybersecuritygrantadvisors.com\n", "2023-12-19 14:37:04,674 ERROR: Error 404 when crawling https://cybersecuritygrantadvisors.com\n", "2023-12-19 14:37:04,677 ERROR: Failed to load data from url: https://cybersecuritygrantadvisors.com\n", "2023-12-19 14:37:04,680 ERROR: Error 404 when crawling https://cybersecuritygrantadvisors.com\n", "2023-12-19 14:37:04,686 ERROR: Failed to load data from url: https://saude.ce.gov.br\n", "2023-12-19 14:37:04,689 ERROR: Error 404 when crawling https://saude.ce.gov.br\n", "2023-12-19 14:37:04,692 ERROR: Failed to load data from url: https://saude.ce.gov.br\n", "2023-12-19 14:37:04,694 ERROR: <PERSON>rror 404 when crawling https://saude.ce.gov.br\n", "2023-12-19 14:37:04,701 ERROR: Failed to load data from url: https://eafit.edu.co\n", "2023-12-19 14:37:04,704 ERROR: Error 500 when crawling https://eafit.edu.co\n", "2023-12-19 14:37:04,707 ERROR: Failed to load data from url: https://eafit.edu.co\n", "2023-12-19 14:37:04,710 ERROR: Error 500 when crawling https://eafit.edu.co\n", "2023-12-19 14:37:04,713 ERROR: Failed to load data from url: https://ccffaa.mil.pe\n", "2023-12-19 14:37:04,716 ERROR: Error 500 when crawling https://ccffaa.mil.pe\n", "2023-12-19 14:37:04,719 ERROR: Failed to load data from url: https://ccffaa.mil.pe\n", "2023-12-19 14:37:04,721 ERROR: Error 500 when crawling https://ccffaa.mil.pe\n", "2023-12-19 14:37:04,724 ERROR: Failed to load data from url: https://navy.gov.in\n", "2023-12-19 14:37:04,727 ERROR: Error 500 when crawling https://navy.gov.in\n", "2023-12-19 14:37:04,730 ERROR: Failed to load data from url: https://navy.gov.in\n", "2023-12-19 14:37:04,733 ERROR: Error 500 when crawling https://navy.gov.in\n", "2023-12-19 14:37:04,740 ERROR: Failed to load data from url: https://cybercodeone.com\n", "2023-12-19 14:37:04,742 ERROR: Error 500 when crawling https://cybercodeone.com\n", "2023-12-19 14:37:04,746 ERROR: Failed to load data from url: https://cybercodeone.com\n", "2023-12-19 14:37:04,749 ERROR: Error 500 when crawling https://cybercodeone.com\n", "2023-12-19 14:37:04,754 ERROR: Failed to load data from url: https://www.finca.org\n", "2023-12-19 14:37:04,757 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.finca.org&render_js=false (Caused by None)\n", "2023-12-19 14:37:04,761 ERROR: Failed to load data from url: https://www.finca.org\n", "2023-12-19 14:37:04,763 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.finca.org&render_js=false (Caused by None)\n", "2023-12-19 14:37:04,772 ERROR: Failed to load data from url: https://www.emergentbiosolutions.com\n", "2023-12-19 14:37:04,775 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.emergentbiosolutions.com&render_js=false (Caused by None)\n", "2023-12-19 14:37:04,779 ERROR: Failed to load data from url: https://www.emergentbiosolutions.com\n", "2023-12-19 14:37:04,782 ERROR: None: Max retries exceeded with url: /api/v1/?api_key=GZ7K5XXTS7COG8C8DDYYX3W2DU23L89Y4OX5BXAGQ6N46Y5FGEL6BU8653XYICQ34YIAP7TDXCRAL3BV&url=https%3A%2F%2Fwww.emergentbiosolutions.com&render_js=false (Caused by None)\n", "2023-12-19 14:37:04,789 ERROR: Failed to load data from url: https://highered.texas.gov\n", "2023-12-19 14:37:04,792 ERROR: Error 500 when crawling https://highered.texas.gov\n", "2023-12-19 14:37:04,795 ERROR: Failed to load data from url: https://highered.texas.gov\n", "2023-12-19 14:37:04,798 ERROR: Error 500 when crawling https://highered.texas.gov\n", "2023-12-19 14:37:04,801 ERROR: Failed to load data from url: https://sakscloudservices.com\n", "2023-12-19 14:37:04,804 ERROR: Error 500 when crawling https://sakscloudservices.com\n", "2023-12-19 14:37:04,807 ERROR: Failed to load data from url: https://sakscloudservices.com\n", "2023-12-19 14:37:04,810 ERROR: Error 500 when crawling https://sakscloudservices.com\n", "2023-12-19 14:37:04,815 ERROR: Failed to load data from url: https://visiercorp.com\n", "2023-12-19 14:37:04,817 ERROR: Error 502 when crawling https://visiercorp.com\n", "2023-12-19 14:37:04,821 ERROR: Failed to load data from url: https://visiercorp.com\n", "2023-12-19 14:37:04,823 ERROR: Error 502 when crawling https://visiercorp.com\n", "2023-12-19 14:37:04,828 ERROR: Failed to load data from url: https://www.st.com\n", "2023-12-19 14:37:04,830 ERROR: Error 500 when crawling https://www.st.com\n", "2023-12-19 14:37:04,834 ERROR: Failed to load data from url: https://www.st.com\n", "2023-12-19 14:37:04,836 ERROR: Error 500 when crawling https://www.st.com\n", "2023-12-19 14:37:04,844 ERROR: Failed to load data from url: https://64loft.com\n", "2023-12-19 14:37:04,847 ERROR: Error 500 when crawling https://64loft.com\n", "2023-12-19 14:37:04,850 ERROR: Failed to load data from url: https://64loft.com\n", "2023-12-19 14:37:04,853 ERROR: Error 500 when crawling https://64loft.com\n", "2023-12-19 14:37:04,864 ERROR: Failed to load data from url: https://tlc.nyc.gov\n", "2023-12-19 14:37:04,867 ERROR: Error 500 when crawling https://tlc.nyc.gov\n", "2023-12-19 14:37:04,870 ERROR: Failed to load data from url: https://tlc.nyc.gov\n", "2023-12-19 14:37:04,873 ERROR: Error 500 when crawling https://tlc.nyc.gov\n", "2023-12-19 14:37:04,877 ERROR: Failed to load data from url: https://uk.ey.com\n", "2023-12-19 14:37:04,880 ERROR: Error 500 when crawling https://uk.ey.com\n", "2023-12-19 14:37:04,883 ERROR: Failed to load data from url: https://uk.ey.com\n", "2023-12-19 14:37:04,886 ERROR: Error 500 when crawling https://uk.ey.com\n", "2023-12-19 14:37:04,893 ERROR: Failed to load data from url: https://reliablene.com\n", "2023-12-19 14:37:04,896 ERROR: Error 500 when crawling https://reliablene.com\n", "2023-12-19 14:37:04,899 ERROR: Failed to load data from url: https://reliablene.com\n", "2023-12-19 14:37:04,902 ERROR: Error 500 when crawling https://reliablene.com\n", "2023-12-19 14:37:04,906 ERROR: Failed to load data from url: https://ccbctaunton.org\n", "2023-12-19 14:37:04,908 ERROR: Error 500 when crawling https://ccbctaunton.org\n", "2023-12-19 14:37:04,911 ERROR: Failed to load data from url: https://ccbctaunton.org\n", "2023-12-19 14:37:04,914 ERROR: Error 500 when crawling https://ccbctaunton.org\n", "2023-12-19 14:37:04,918 ERROR: Failed to load data from url: https://houndapps.com\n", "2023-12-19 14:37:04,921 ERROR: Error 500 when crawling https://houndapps.com\n", "2023-12-19 14:37:04,924 ERROR: Failed to load data from url: https://houndapps.com\n", "2023-12-19 14:37:04,927 ERROR: Error 500 when crawling https://houndapps.com\n", "2023-12-19 14:37:04,930 ERROR: Failed to load data from url: https://ldcapp.com\n", "2023-12-19 14:37:04,933 ERROR: Error 500 when crawling https://ldcapp.com\n", "2023-12-19 14:37:04,937 ERROR: Failed to load data from url: https://ldcapp.com\n", "2023-12-19 14:37:04,939 ERROR: Error 500 when crawling https://ldcapp.com\n", "2023-12-19 14:37:04,942 ERROR: Failed to load data from url: https://kintegra.org\n", "2023-12-19 14:37:04,945 ERROR: Error 500 when crawling https://kintegra.org\n", "2023-12-19 14:37:04,949 ERROR: Failed to load data from url: https://kintegra.org\n", "2023-12-19 14:37:04,951 ERROR: Error 500 when crawling https://kintegra.org\n", "2023-12-19 14:37:05,806 ERROR: Failed to load data from url: https://alexysryan.com\n", "2023-12-19 14:37:05,811 ERROR: Error 500 when crawling https://alexysryan.com\n", "2023-12-19 14:37:05,816 ERROR: Failed to load data from url: https://alexysryan.com\n", "2023-12-19 14:37:05,820 ERROR: Error 500 when crawling https://alexysryan.com\n", "2023-12-19 14:37:05,829 ERROR: Failed to load data from url: https://www.classichotels.com\n", "2023-12-19 14:37:05,832 ERROR: Error 500 when crawling https://www.classichotels.com\n", "2023-12-19 14:37:05,836 ERROR: Failed to load data from url: https://www.classichotels.com\n", "2023-12-19 14:37:05,840 ERROR: Error 500 when crawling https://www.classichotels.com\n", "2023-12-19 14:37:05,847 ERROR: Failed to load data from url: https://rorschachconsulting.com\n", "2023-12-19 14:37:05,851 ERROR: Error 503 when crawling https://rorschachconsulting.com\n", "2023-12-19 14:37:05,854 ERROR: Failed to load data from url: https://rorschachconsulting.com\n", "2023-12-19 14:37:05,857 ERROR: Error 503 when crawling https://rorschachconsulting.com\n", "2023-12-19 14:37:22,500 ERROR: Failed to load data from url: https://company1.com\n", "2023-12-19 14:37:22,509 ERROR: Error 500 when crawling https://company1.com\n", "2023-12-19 14:37:22,518 ERROR: Failed to load data from url: https://company1.com\n", "2023-12-19 14:37:22,524 ERROR: Error 500 when crawling https://company1.com\n"]}], "source": ["# compare docs extracted behavior\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup, TargetInfo\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.playbook_builder_util import playbook_compare\n", "from api.playbook_build.object_builder import ObjectBuilder\n", "from django.db.models import Prefetch\n", "\n", "playbook_ids = range(300, 337)\n", "\n", "# Prefetch the targets for each target_info_group\n", "targets_prefetch = Prefetch(\n", "    'targets',  # Corrected path based on related_name in TargetInfoGroup\n", "    queryset=TargetInfo.objects.all(),\n", "    to_attr='prefetched_targets'  # Optional, to use a custom attribute name\n", ")\n", "\n", "# Prefetch the target_info_groups for the playbooks, including the prefetched targets\n", "all_playbooks = Playbook.objects.filter(id__in=playbook_ids).prefetch_related(\n", "    Prefetch(\n", "        'target_info_groups',  # Corrected path based on related_name in Playbook\n", "        queryset=TargetInfoGroup.objects.prefetch_related(targets_prefetch),\n", "        to_attr='prefetched_target_info_groups'  # Optional\n", "    )\n", ")\n", "\n", "def diff_dicts(dict1, dict2):\n", "    # Find keys that are only in dict1\n", "    only_in_dict1 = {k: dict1[k] for k in dict1 if k not in dict2}\n", "    # Find keys that are only in dict2\n", "    only_in_dict2 = {k: dict2[k] for k in dict2 if k not in dict1}\n", "    # Find keys that are in both but have different values\n", "    different_values = {\n", "        k: (dict1[k], dict2[k]) for k in dict1 if k in dict2 and dict1[k] != dict2[k]\n", "    }\n", "    return only_in_dict1, only_in_dict2, different_values\n", "\n", "def diff_docs(docs_old, docs_new):\n", "    mismatches = []\n", "    if len(docs_old) != len(docs_new):\n", "        mismatches.append(f\"size doesnot match: {len(docs_old)} : {len(docs_new)}\")\n", "        return mismatches\n", "    \n", "    docs_new_mapped = {x.metadata['data_id']:x for x in docs_new}\n", "    for doc_old in docs_old:\n", "        doc_new = docs_new_mapped[doc_old.metadata['data_id']]\n", "        # compare content\n", "        if doc_old.page_content != doc_new.page_content:\n", "            mismatches.append(f\"page content mismatch:\")\n", "            \n", "        meta_old = doc_old.metadata\n", "        meta_new = doc_new.metadata\n", "\n", "        only_in_dict1, only_in_dict2, different_values = diff_dicts(meta_old, meta_new)\n", "        if only_in_dict1:\n", "            mismatches.append(f\"only_in_dict1: {only_in_dict1}\")\n", "        if only_in_dict2:\n", "            mismatches.append(f\"only_in_dict2: {only_in_dict2}\")\n", "        if different_values:\n", "            mismatches.append(f\"different_values: {different_values}\")\n", "    return mismatches\n", "    \n", "\n", "# Now you can access target_info_groups and targets with reduced database hits\n", "for playbook in all_playbooks:\n", "    playbook_handler = PlaybookHandler(playbook)\n", "    for target_info_group in playbook.prefetched_target_info_groups:\n", "        for target in target_info_group.prefetched_targets:\n", "            obj_builder = ObjectBuilder.get_builder(target)\n", "            \n", "            docs_old = playbook_handler.extract_docs(\"target_info\", [target_info_group.target_info_group_key, target.target_key])\n", "            docs_new, docs_errors, docs_status = obj_builder.doc_loader.extract_docs(target.docs)\n", "\n", "            mismatches = diff_docs(docs_old, docs_new)\n", "            if mismatches:\n", "                print(f\"mismatch found: for target {target.id} and playbook {playbook.id} {mismatches}\")\n", "        \n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "7eabcfcd-d15b-45a9-b49a-35219310fe0b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}