{"cells": [{"cell_type": "code", "execution_count": null, "id": "dd9c4dee-c94f-44ba-87b1-4b191106f48c", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "\n", "from api.playbook_health import PlaybookHealthChecker\n", "from api.models import Playbook\n", "\n", "for playbook in Playbook.objects.all():\n", "    if playbook.id != 350:\n", "        continue\n", "    checker = PlaybookHealthChecker(playbook)\n", "    checker._check_object_files()\n", "    print(checker.errors)"]}, {"cell_type": "code", "execution_count": null, "id": "0159e890-a75a-4f46-94e1-2dc6d9b629e7", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "import json\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "import boto3\n", "\n", "def rename_s3_object(bucket_name, old_key, new_key):\n", "    s3 = boto3.client('s3')\n", "    \n", "    # Copy the object to the new key\n", "    copy_source = {\n", "        'Bucket': bucket_name,\n", "        'Key': old_key\n", "    }\n", "    s3.copy(copy_source, bucket_name, new_key)\n", "\n", "rename_s3_object(\"tofu-uploaded-files\", \"f42a55ef-3fa9-1942-f122-6ab028b3dcc4-Tofu Overview 2-pager.pdf\", \"f42a55ef-3fa9-1942-f122-6ab028b3dcc4.pdf\")"]}, {"cell_type": "code", "execution_count": null, "id": "e2da251f-5acd-4cac-9b0e-ff2138946d8f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}