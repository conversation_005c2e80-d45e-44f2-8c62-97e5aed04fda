{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0e0dc56c-6c57-4dd3-82b3-81b886f9faf9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Documents/Projects/tofu/backend/tofu/venv/lib/python3.11/site-packages/pinecone/index.py:4: TqdmExperimentalWarning: Using `tqdm.autonotebook.tqdm` in notebook mode. Use `tqdm.tqdm` instead to force console mode (e.g. in jupyter console)\n", "  from tqdm.autonotebook import tqdm\n", "[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-12-01 16:02:58,740 INFO: found organization id: tofu-ykka\n", "playbook_id\tusername\tnum_objects\tnum_targets\tnum_assets\n", "286\ttofuadmin-levitate\t1034\t1032\t2\n", "183\ttofuadmin-largetargets\t1000\t1000\t0\n", "269\t<EMAIL>\t538\t536\t2\n", "111\ttofuadmin-test-stamp<PERSON>\t529\t529\t0\n", "13\ttofuadmin-<PERSON><PERSON>\t362\t355\t7\n", "33\t<EMAIL>\t286\t284\t2\n", "69\ttofuadmin-igloo\t261\t259\t2\n", "117\ttofuadmin-eval-igloo\t259\t259\t0\n", "101\ttofuadmin-test-igloo\t259\t259\t0\n", "238\t<EMAIL>\t195\t193\t2\n", "3\t<EMAIL>\t170\t166\t4\n", "155\ttofuadmin-evisort\t165\t158\t7\n", "115\ttofuadmin-eval-<PERSON><PERSON>\t151\t151\t0\n", "265\t<EMAIL>\t151\t149\t2\n", "148\ttofuadmin-wund<PERSON><PERSON>\t151\t149\t2\n", "248\t<EMAIL>\t142\t139\t3\n", "234\ttofuadmin-upper\t121\t111\t10\n", "80\ttofuadmin-tofu\t119\t93\t26\n", "190\t<EMAIL>\t112\t95\t17\n", "127\ttofuadmin-bluecore\t99\t96\t3\n", "268\t<EMAIL>\t99\t96\t3\n", "203\t<EMAIL>\t95\t90\t5\n", "142\ttofuadmin-turntide\t95\t93\t2\n", "100\ttofuadmin-<PERSON><PERSON><PERSON>\t81\t69\t12\n", "151\t\t70\t68\t2\n", "195\ttofuadmin-reprise\t61\t59\t2\n", "85\ttofuadmin-fraction\t60\t60\t0\n", "104\t\t53\t53\t0\n", "177\ttofuadmin-vercel\t49\t11\t38\n", "133\ttofuadmin-test-bluecore\t48\t48\t0\n", "99\t\t48\t48\t0\n", "163\ttofuadmin-vividly\t47\t47\t0\n", "2\t<EMAIL>\t44\t36\t8\n", "31\ttofuadmin-schema\t42\t42\t0\n", "251\ttofuadmin-<PERSON><PERSON>\t41\t40\t1\n", "256\ttofuadmin-rob<PERSON><PERSON>\t38\t36\t2\n", "1\ttofuadmin\t38\t34\t4\n", "144\t<EMAIL>\t37\t9\t28\n", "245\ttofuadmin-tenable\t37\t35\t2\n", "246\t\t32\t25\t7\n", "187\t<EMAIL>\t30\t30\t0\n", "175\ttofuadmin-grafana\t30\t30\t0\n", "182\ttofuadmin-verkada\t30\t30\t0\n", "229\ttofuadmin-loop\t27\t26\t1\n", "149\ttofuadmin-servicetitan\t27\t27\t0\n", "200\ttofuadmin-hypr\t26\t24\t2\n", "284\ttofuadmin-checkpoint\t26\t25\t1\n", "103\ttofuadmin-test-uipath\t26\t21\t5\n", "227\ttofuadmin-project44\t26\t26\t0\n", "118\ttofuadmin-e<PERSON>-<PERSON><PERSON>ath\t26\t21\t5\n", "98\t\t26\t21\t5\n", "74\ttofuadmin-u<PERSON>ath\t26\t21\t5\n", "242\ttofuadmin-groundswell\t25\t24\t1\n", "181\ttofuadmin-vida\t25\t24\t1\n", "199\ttofuadmin-outsized\t24\t23\t1\n", "171\ttofuadmin-owner\t24\t24\t0\n", "168\ttofuadmin-seismic\t22\t20\t2\n", "86\ttofuadmin-muuk<PERSON>\t21\t21\t0\n", "202\ttofuadmin-northstar\t21\t21\t0\n", "4\t<EMAIL>\t19\t10\t9\n", "174\ttofu<PERSON>min-<PERSON><PERSON><PERSON>\t19\t16\t3\n", "225\ttofuadmin-zeotap\t19\t19\t0\n", "138\ttofuadmin-deepgram\t18\t18\t0\n", "273\ttofuadmin-onetrust\t18\t18\t0\n", "26\ttofuadmin-vultr\t18\t18\t0\n", "170\ttofuadmin-retool\t17\t16\t1\n", "249\ttofuadmin-kod<PERSON>\t16\t16\t0\n", "88\t<EMAIL>\t16\t15\t1\n", "167\ttofuadmin-brightplan\t16\t15\t1\n", "247\ttofuadmin-meter\t16\t15\t1\n", "283\ttofuadmin-<PERSON><PERSON>\t16\t11\t5\n", "230\ttofuadmin-marketerhire\t15\t14\t1\n", "173\ttofuadmin-workato\t15\t14\t1\n", "53\t\t13\t13\t0\n", "28\t<EMAIL>\t13\t13\t0\n", "290\t<EMAIL>\t13\t11\t2\n", "205\ttofuadmin-keepfinancial\t13\t10\t3\n", "153\tkatyah<PERSON><PERSON><PERSON><EMAIL>\t13\t13\t0\n", "176\ttofuadmin-clari\t12\t12\t0\n", "272\ttofuadmin-offerfit\t12\t12\t0\n", "16\ttofuadmin-hstpathways\t12\t11\t1\n", "220\ttofuadmin-highspot\t12\t10\t2\n", "198\ttofuadmin-vanta\t12\t9\t3\n", "201\ttofuadmin-rebuy\t11\t9\t2\n", "271\ttofuadmin-bikky\t11\t10\t1\n", "274\ttofuadmin-onloop\t11\t10\t1\n", "221\ttofuadmin-zluri\t11\t10\t1\n", "139\t<EMAIL>\t11\t11\t0\n", "224\ttofuadmin-ternary\t10\t9\t1\n", "75\ttofuadmin-ash\t10\t7\t3\n", "68\t<EMAIL>\t10\t10\t0\n", "281\ttofuadmin-boon\t10\t9\t1\n", "280\ttofuadmin-getrev\t10\t10\t0\n", "231\ttofuadmin-cocoon\t10\t9\t1\n", "52\ttofuadmin-change\t10\t10\t0\n", "239\ttofuadmin-cakewalk\t10\t9\t1\n", "276\ttofuadmin-iab\t9\t8\t1\n", "243\t<EMAIL>\t9\t9\t0\n", "253\ttofuadmin-bardeen\t9\t9\t0\n", "254\ttofuadmin-streamline\t9\t8\t1\n", "292\ttofuadmin-kimi2\t9\t3\t6\n", "188\ttofuadmin-flocksafety\t9\t8\t1\n", "240\ttofuadmin-brightai\t9\t9\t0\n", "259\ttofuadmin-stacksync\t9\t9\t0\n", "12\t<EMAIL>\t8\t8\t0\n", "76\ttofuadmin-writer\t8\t8\t0\n", "162\ttofuadmin-amplifai\t8\t6\t2\n", "165\ttofuadmin-lacework\t8\t8\t0\n", "228\ttofuadmin-duplocloud\t8\t8\t0\n", "252\ttofuadmin-pinwheel\t8\t8\t0\n", "89\talex.tsi<PERSON><PERSON>@gmail.com\t8\t5\t3\n", "223\ttofuadmin-monad\t7\t6\t1\n", "260\ttofuadmin-bloomfilter\t7\t6\t1\n", "84\t<EMAIL>\t7\t7\t0\n", "270\ttofuadmin-skedsocial\t7\t6\t1\n", "134\t<EMAIL>\t7\t5\t2\n", "78\ttofuadmin-heap\t7\t7\t0\n", "295\ttofuadmin-greenplaces\t7\t6\t1\n", "18\t<EMAIL>\t7\t7\t0\n", "77\ttofuadmin-laetro\t6\t6\t0\n", "125\ttofu-review\t6\t4\t2\n", "255\ttofuadmin-washn\t6\t5\t1\n", "206\t<EMAIL>\t6\t3\t3\n", "232\ttofuadmin-learnworlds\t6\t6\t0\n", "178\ttofuadmin-tomorrow\t6\t6\t0\n", "106\t<EMAIL>\t6\t4\t2\n", "241\ttofuadmin-peruseml\t5\t5\t0\n", "32\ttofuadmin-humanitec\t5\t5\t0\n", "50\t<EMAIL>\t5\t5\t0\n", "222\ttofuadmin-cliezen\t5\t5\t0\n", "8\ttofuadmin-demandwell\t5\t5\t0\n", "287\ttofuadmin-kimi\t5\t3\t2\n", "279\t<EMAIL>\t5\t5\t0\n", "45\ttofuadmin-test5\t5\t5\t0\n", "95\t<EMAIL>\t5\t4\t1\n", "143\t<EMAIL>\t4\t4\t0\n", "51\ttofuadmin-meq\t4\t4\t0\n", "49\tj<PERSON><PERSON><PERSON><PERSON>@gmail.com\t4\t4\t0\n", "94\ttofuadmin-danny\t4\t3\t1\n", "179\ttofuadmin-honglei\t4\t2\t2\n", "48\ttofuadmin-matrix\t4\t4\t0\n", "293\ttofuadmin-kimi3\t4\t3\t1\n", "141\t<EMAIL>\t3\t3\t0\n", "92\ttofuadmin-carlos-test\t3\t3\t0\n", "79\t\t3\t3\t0\n", "235\ttofuadmin-spaceo\t3\t3\t0\n", "29\ttofu<PERSON>min-<PERSON>\t3\t3\t0\n", "119\t<EMAIL>\t3\t3\t0\n", "147\ttofuadmin-ejtest\t3\t3\t0\n", "121\t<EMAIL>\t3\t3\t0\n", "124\t<EMAIL>\t3\t3\t0\n", "116\t<EMAIL>\t3\t3\t0\n", "257\ttofuadmin-vivdly\t3\t3\t0\n", "180\ttofuadmin-jian\t3\t3\t0\n", "122\t<EMAIL>\t2\t0\t2\n", "108\t\t2\t2\t0\n", "107\tjacqueline<PERSON><EMAIL>\t2\t2\t0\n", "192\ttofuadmin-heartbeat\t1\t1\t0\n"]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Playbook, TargetInfoGroup, TargetInfo, AssetInfo\n", "from api.playbook import PlaybookHandler\n", "from api.serializers import PlaybookSerializer\n", "from api.playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus\n", "from api.playbook_build.playbook_builder_util import playbook_compare\n", "\n", "# for playbook_id in [3]:\n", "#     playbook = Playbook.objects.get(id=playbook_id)\n", "\n", "all_playbooks = Playbook.objects.all()\n", "results = []\n", "for playbook in all_playbooks:\n", "    creator = playbook.users.first()\n", "\n", "    targets = TargetInfo.objects.filter(\n", "        target_info_group__playbook=playbook\n", "    )\n", "    assets = AssetInfo.objects.filter(\n", "        asset_info_group__playbook=playbook\n", "    )\n", "    if len(targets)+len(assets):\n", "        results.append((playbook.id, \"\" if not creator else creator.username, len(targets)+len(assets), len(targets), len(assets)))\n", "\n", "results.sort(key=lambda x: x[2], reverse=True)\n", "\n", "print(f\"playbook_id\\tusername\\tnum_objects\\tnum_targets\\tnum_assets\")\n", "for result in results:\n", "    playbook_id, username, num_objects, num_targets, num_assets = result\n", "    print(f\"{playbook_id}\\t{username}\\t{num_objects}\\t{num_targets}\\t{num_assets}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f1b0a0d4-d9b6-4d1e-80a8-7d509466447a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}