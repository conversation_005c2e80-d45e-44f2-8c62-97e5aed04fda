{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024-08-13\n", "# set all html_tag_index to null\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.slides.flashdocs import sync_generate_flashdocs_slides\n", "\n", "prompt = \"\"\"<slide_1>\n", "# Scaling Your 1:1 ABM Campaigns\n", "</slide_1>\n", "\n", "<slide_2>\n", "## Today's Agenda\n", "- 05 min: Introductions\n", "- 10 min: Tofu Platform Overview\n", "- 15 min: Omnichannel Personalization Demo\n", "- 15 min: Alignment with Your ABM Strategy\n", "- 10 min: Next Steps and Q&A\n", "</slide_2>\n", "\n", "<slide_3>\n", "# Tofu: Your AI Copilot for B2B Marketing\n", "</slide_3>\n", "\n", "<slide_4>\n", "## What is Tofu?\n", "- Unified platform for hyper-personalized, omnichannel campaigns\n", "- Leverages your brand guidelines and segmentation\n", "- Uses generative AI to create unlimited on-brand content variations\n", "- Optimizes campaign performance through insights and analytics\n", "</slide_4>\n", "\n", "<slide_5>\n", "## Key Capabilities\n", "- 1:1 ABM campaign scaling\n", "- Industry-specific content generation\n", "- Persona-based customization\n", "- Multi-channel content repurposing\n", "- Continuous feedback loop for optimization\n", "</slide_5>\n", "\n", "<slide_6>\n", "## Omnichannel Personalization\n", "- Deeply understand each customer\n", "- Create compelling content across channels\n", "- Personalize emails, landing pages, white papers, sales decks, and ads\n", "- Target specific industries and personas\n", "- Shorten sales cycles and win more deals\n", "</slide_6>\n", "\n", "<slide_7>\n", "## Use Cases\n", "1. Marketing Emails\n", "2. SDR Emails\n", "3. <PERSON>\n", "4. <PERSON><PERSON> <PERSON><PERSON>\n", "5. Whitepapers\n", "6. Case Studies\n", "7. <PERSON><PERSON><PERSON><PERSON>\n", "8. Social Posts\n", "9. Ad Campaigns\n", "10. Sales Decks\n", "</slide_7>\n", "\n", "<slide_8>\n", "## Benefits for Your Team\n", "- Ship integrated campaigns 8x faster\n", "- Reduce martech bloat\n", "- Improve campaign performance\n", "- Increase conversion on each channel\n", "- Scale output without increasing headcount\n", "</slide_8>\n", "\n", "<slide_9>\n", "# Next Steps\n", "- Schedule a custom demo\n", "- Discuss your specific ABM needs\n", "- Explore how Tofu can integrate with your current workflow\n", "</slide_9>\n", "\"\"\"\n", "\n", "\n", "\n", "sync_generate_flashdocs_slides(\n", "    prompt=prompt,\n", "    google_document_editors=[\"<EMAIL>\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024-08-13\n", "# set all html_tag_index to null\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.slides.flashdocs import (\n", "    sync_generate_flashdocs_slides,\n", "    DEFAULT_FLASHDOCS_TEMPLATE_LIBRARY,\n", "    get_document_config,\n", ")\n", "\n", "get_document_config(DEFAULT_FLASHDOCS_TEMPLATE_LIBRARY, True, True, True, False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024-08-13\n", "# set all html_tag_index to null\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.slides.core import _get_s3_pptx_slides_text\n", "\n", "s3_bucket = \"tofu-uploaded-files\"\n", "s3_file_name = \"tofu_deck_2.pptx\"\n", "slides_text = _get_s3_pptx_slides_text(s3_bucket, s3_file_name)\n", "print(slides_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024-08-13\n", "# set all html_tag_index to null\n", "\n", "import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.gdrive_utils import set_google_drive_permissions_public\n", "\n", "gdrive_url = \"https://docs.google.com/presentation/d/1zTRPxA7ICZkFHpbvUCXi8UVckvGqfRCNdLeAcLgiuf4/edit?usp=sharing\"\n", "set_google_drive_permissions_public(gdrive_url)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.gdrive_utils import copy_google_drive_file\n", "\n", "gdrive_url = \"https://docs.google.com/presentation/d/1zTRPxA7ICZkFHpbvUCXi8UVckvGqfRCNdLeAcLgiuf4/edit?usp=sharing\"\n", "copy_google_drive_file(gdrive_url)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from api.slides.flashdocs import upload_google_slides_to_flashdocs\n", "gdrive_url = \"https://docs.google.com/presentation/d/1zTRPxA7ICZkFHpbvUCXi8UVckvGqfRCNdLeAcLgiuf4/edit?\"\n", "upload_google_slides_to_flashdocs(google_document_link=gdrive_url)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from api.slides.flashdocs import get_document_config, sync_generate_flashdocs_slides\n", "import logging\n", "import json\n", "\n", "from api.model_config import ModelConfigResolver\n", "from api.model_caller import ModelCaller\n", "from api.task_registry import GenerationGoal\n", "from langchain_core.messages import HumanMessage\n", "model_config = ModelConfigResolver.resolve(GenerationGoal.EXPANSION)\n", "model_caller = ModelCaller(model_config)\n", "\n", "def generate_placeholder_manual_insertions(\n", "    slide_index: int, placeholders: list[str], variation_text: str\n", "):\n", "    \"\"\"\n", "    Given the variation_text and the placeholders, give a mapping from placeholder to value.\n", "    \"\"\"\n", "\n", "    placeholder_mapping = []\n", "\n", "    prompt = f\"\"\"Generate a mapping from the placeholders to the value for the following slide:\n", "    {variation_text}\n", "\n", "    Placeholders:\n", "    {placeholders}\n", "\n", "    Generate the output in the following format:\n", "    {[{\n", "        \"placeholder\": \"placeholder\",\n", "        \"value\": \"value\"\n", "    }]}\n", "\n", "    Output using double quotes.\n", "    Ensure that all the content of the original slide is kept in the output.\n", "    You should include every placeholder exactly once in the output.\n", "    Only use placeholders that are present in the original content.\n", "    Keep any markdown formatting of the original content.\n", "    \"\"\"\n", "\n", "    logging.info(f\"Prompt: {prompt}\")\n", "\n", "    response = model_caller.get_results_with_fallback([HumanMessage(content=prompt)])\n", "\n", "    # parse the response\n", "    response = response[0].text\n", "    logging.info(f\"Response: {response}\")\n", "    # Check if response contains code blocks and extract JSON if present\n", "    if \"```json\" in response:\n", "        response = response.split(\"```json\")[1].split(\"```\")[0]\n", "    elif \"```\" in response:\n", "        # Handle case where JSON might be in a code block without language specifier\n", "        response = response.split(\"```\")[1].split(\"```\")[0]\n", "\n", "    response_json = json.loads(response)\n", "    for placeholder in response_json:\n", "        if \"placeholder\" in placeholder and \"value\" in placeholder and placeholder[\"placeholder\"] in placeholders:\n", "            placeholder_mapping.append(\n", "                {\n", "                    \"placeholder\": placeholder[\"placeholder\"],\n", "                    \"value\": placeholder[\"value\"],\n", "                    \"slide_index\": slide_index,\n", "                }\n", "            )\n", "\n", "    # merge values of mappings that share the same placeholder and slide_index\n", "    merged_placeholder_mapping = {}\n", "    for mapping in placeholder_mapping:\n", "        if (mapping[\"placeholder\"], mapping[\"slide_index\"]) not in merged_placeholder_mapping:\n", "            merged_placeholder_mapping[(mapping[\"placeholder\"], mapping[\"slide_index\"])] = mapping[\"value\"]\n", "        else:\n", "            merged_placeholder_mapping[(mapping[\"placeholder\"], mapping[\"slide_index\"])] += \"\\n\" + mapping[\"value\"]\n", "\n", "    placeholder_mapping = []\n", "    for mapping in merged_placeholder_mapping:\n", "        placeholder_mapping.append(\n", "            {\n", "                \"placeholder\": mapping[0],\n", "                \"value\": merged_placeholder_mapping[mapping],\n", "                \"slide_index\": mapping[1],\n", "            }\n", "        )\n", "\n", "    return placeholder_mapping\n", "\n", "doc_id = \"053e8851-d768-40de-90f9-dc0944d59199\"\n", "doc_config = get_document_config(document_id=doc_id, include_slides=True)\n", "text_placeholder_manual_insertions = []\n", "slides_markdown = [\n", "    {\"markdown\": \"# Supercharging\\nYour Marketing\\nwith Generative AI\"},\n", "    {\n", "        \"markdown\": \"## TODAY'S AGENDA\\n05 min: Introductions\\n10 min: Understanding Generative Marketing\\n15 min: Tofu's Approach to Personalization\\n15 min: Demo of Tofu's Platform\\n10 min: Q&A and Next Steps\"\n", "    },\n", "    {\"content_instruction\": \"DNE\"},\n", "    {\n", "        \"markdown\": \"## What is Generative Marketing?\\n\\nGenerative Marketing uses machine learning to:\\n- Create and optimize personalized content at scale\\n- Increase conversion rates with tailored messaging\\n- Enable granular audience segmentation\\n- Achieve both personalization and scale through AI\\n\\nKey benefits:\\n1. Personalize content across channels (email, landing pages, ads, etc.)\\n2. Repurpose existing content into new formats\\n3. Generate fresh content from scratch or by combining assets\\n4. Act as an AI assistant with deep knowledge of your brand\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Limitations of Generative Marketing\\n\\nWhile powerful, Generative Marketing tools have some constraints:\\n\\n- Cannot generate images or videos from scratch\\n- Don't create audio content\\n- Require clear instructions (like explaining to a human)\\n- Need oversight to ensure brand consistency\\n\\nRemember: Generative AI augments human creativity, it doesn't replace it!\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Your First 90 Days with Tofu\\n\\n1. Initial Training Sessions\\n   - Schedule kick-off meetings with key team members\\n   - Hands-on platform tutorials\\n\\n2. Dedicated Support Channel\\n   - Direct line to our team for questions and feedback\\n   - Continuous improvement based on your input\\n\\n3. Custom Template Creation\\n   - Build repeatable workflows for your specific needs\\n   - Leverage our pre-built templates as starting points\\n\\nOur goal: Get you up and running quickly with measurable results\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Aligning Tofu with Your Priorities\\n\\nTop Use Cases for B2B Marketing Teams:\\n\\n1. Personalized Outreach Campaigns\\n   - Tailor emails for specific industries or accounts\\n   - Create targeted landing pages for market segments\\n\\n2. Content Repurposing & Distribution\\n   - Transform existing assets into multi-channel content\\n   - Generate blog posts from webinars or case studies\\n\\n3. Automated Nurture Sequences\\n   - Design personalized, multi-touch flows in your CRM\\n   - Optimize content and timing with AI-driven insights\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Upcoming Campaign Opportunities\\n\\nLet's focus on your immediate needs:\\n\\n- Event Promotion: Boost signups for your Madison event\\n- Follow-up Campaign: Engage attendees post-event\\n- Industry-Specific Outreach: Target key verticals\\n\\nWhat other initiatives are on your radar?\\nWe'll tailor our onboarding to support these goals.\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Onboarding Schedule\\n\\nWeek 1: Platform Introduction\\n- Account setup and user onboarding\\n- Overview of key features and capabilities\\n\\nWeek 2: Campaign Planning\\n- Identify first use case (e.g., event promotion)\\n- Template creation and content strategy\\n\\nWeek 3: Execution & Optimization\\n- Launch initial campaign\\n- Review performance and refine approach\\n\\nWeek 4: Scaling & Advanced Features\\n- Explore additional use cases\\n- Training on advanced personalization techniques\"\n", "    },\n", "]\n", "\n", "slides = doc_config.get(\"slides\", [])\n", "for slide in slides:\n", "    placeholders = []\n", "    slide_index = slide.get(\"index\")\n", "    slide_placeholders = slide.get(\"text_placeholders\", [])\n", "    for placeholder in slide_placeholders:\n", "        placeholders.append(placeholder.get(\"placeholder\"))\n", "\n", "    manual_insertions = generate_placeholder_manual_insertions(\n", "        slide_index, placeholders, slides_markdown[slide_index].get(\"markdown\")\n", "    )\n", "    text_placeholder_manual_insertions.extend(manual_insertions)\n", "\n", "print(text_placeholder_manual_insertions)\n", "sync_generate_flashdocs_slides(\n", "    prompt=\"\",\n", "    source_document_id=doc_id,\n", "    text_placeholder_manual_insertions=text_placeholder_manual_insertions,\n", "    google_document_editors=[\"<EMAIL>\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "from api.slides.flashdocs import get_document_config, sync_generate_flashdocs_slides, Outline\n", "import logging\n", "import json\n", "\n", "from api.model_config import ModelConfigResolver\n", "from api.model_caller import ModelCaller\n", "from api.task_registry import GenerationGoal\n", "from langchain_core.messages import HumanMessage\n", "\n", "from api.slides.flashdocs import upload_google_slides_to_flashdocs, get_document_config\n", "\n", "model_config = ModelConfigResolver.resolve(\n", "    GenerationGoal.EXPANSION,\n", ")\n", "model_caller = ModelCaller(model_config)\n", "\n", "slides_markdown = [\n", "    {\"markdown\": \"# Supercharging\\nYour Marketing\\nwith Generative AI\"},\n", "    {\n", "        \"markdown\": \"## TODAY'S AGENDA\\n05 min: Introductions\\n10 min: Understanding Generative Marketing\\n15 min: Tofu's Approach to Personalization\\n15 min: Demo of Tofu's Platform\\n10 min: Q&A and Next Steps\"\n", "    },\n", "    {\"content_instruction\": \"DNE\"},\n", "    {\n", "        \"markdown\": \"## What is Generative Marketing?\\n\\nGenerative Marketing uses machine learning to:\\n- Create and optimize personalized content at scale\\n- Increase conversion rates with tailored messaging\\n- Enable granular audience segmentation\\n- Achieve both personalization and scale through AI\\n\\nKey benefits:\\n1. Personalize content across channels (email, landing pages, ads, etc.)\\n2. Repurpose existing content into new formats\\n3. Generate fresh content from scratch or by combining assets\\n4. Act as an AI assistant with deep knowledge of your brand\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Limitations of Generative Marketing\\n\\nWhile powerful, Generative Marketing tools have some constraints:\\n\\n- Cannot generate images or videos from scratch\\n- Don't create audio content\\n- Require clear instructions (like explaining to a human)\\n- Need oversight to ensure brand consistency\\n\\nRemember: Generative AI augments human creativity, it doesn't replace it!\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Your First 90 Days with Tofu\\n\\n1. Initial Training Sessions\\n   - Schedule kick-off meetings with key team members\\n   - Hands-on platform tutorials\\n\\n2. Dedicated Support Channel\\n   - Direct line to our team for questions and feedback\\n   - Continuous improvement based on your input\\n\\n3. Custom Template Creation\\n   - Build repeatable workflows for your specific needs\\n   - Leverage our pre-built templates as starting points\\n\\nOur goal: Get you up and running quickly with measurable results\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Aligning Tofu with Your Priorities\\n\\nTop Use Cases for B2B Marketing Teams:\\n\\n1. Personalized Outreach Campaigns\\n   - Tailor emails for specific industries or accounts\\n   - Create targeted landing pages for market segments\\n\\n2. Content Repurposing & Distribution\\n   - Transform existing assets into multi-channel content\\n   - Generate blog posts from webinars or case studies\\n\\n3. Automated Nurture Sequences\\n   - Design personalized, multi-touch flows in your CRM\\n   - Optimize content and timing with AI-driven insights\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Upcoming Campaign Opportunities\\n\\nLet's focus on your immediate needs:\\n\\n- Event Promotion: Boost signups for your Madison event\\n- Follow-up Campaign: Engage attendees post-event\\n- Industry-Specific Outreach: Target key verticals\\n\\nWhat other initiatives are on your radar?\\nWe'll tailor our onboarding to support these goals.\"\n", "    },\n", "    {\n", "        \"markdown\": \"## Onboarding Schedule\\n\\nWeek 1: Platform Introduction\\n- Account setup and user onboarding\\n- Overview of key features and capabilities\\n\\nWeek 2: Campaign Planning\\n- Identify first use case (e.g., event promotion)\\n- Template creation and content strategy\\n\\nWeek 3: Execution & Optimization\\n- Launch initial campaign\\n- Review performance and refine approach\\n\\nWeek 4: Scaling & Advanced Features\\n- Explore additional use cases\\n- Training on advanced personalization techniques\"\n", "    },\n", "]\n", "\n", "google_slides_url = \"https://docs.google.com/presentation/d/1Al1OIkBnH0rlqbfHy9yu2JgdZjXuVI4m9gY1vmyfmYU/edit?slide=id.p1#slide=id.p1\"\n", "\n", "# 1. upload the google slides to flashdocs\n", "# 2. get the placeholder descriptions\n", "\n", "response = upload_google_slides_to_flashdocs(google_document_link=google_slides_url)\n", "document_id = response.get(\"document_id\")\n", "# document_id = \"e184f3d7-5d79-4e2e-aebe-be02556e1846\"\n", "\n", "print(f'Document ID: {document_id}')\n", "\n", "doc_config = get_document_config(document_id=document_id, include_slides=True)\n", "\n", "print(f'Document Config: {doc_config}')\n", "\n", "\n", "def get_text_placeholder_manual_insertions(text_placeholders, markdown):\n", "    text_placeholder_manual_insertions = []\n", "    placeholder_map = []\n", "    for placeholder in text_placeholders:\n", "        placeholder_map.append(\n", "            {\n", "                \"placeholder\": placeholder.get(\"placeholder\"),\n", "                \"original_text\": placeholder.get(\"original_text\"),\n", "            }\n", "        )\n", "\n", "    prompt = f\"\"\"Given the markdown text, and a map from placeholder to original text value, generate new mappings from placeholder to markdown text, preserving the length of the original text and the contents of the new markdown text. The locations for the new text should match the locations of the original text.\n", "\n", "Markdown text: {markdown}\n", "Placeholder map: {placeholder_map}\n", "\n", "Return the mapping in this JSON format:\n", "[{{\"placeholder\": [placeholder_1], \"replacement_text\": markdown_text_1}}]\n", "Return only the JSON, no other text.\n", "\"\"\"\n", "\n", "    # logging.info(f\"prompt: {prompt}\")\n", "\n", "    response = model_caller.get_results_with_fallback([HumanMessage(content=prompt)])\n", "\n", "    # parse the response\n", "    response = response[0].text\n", "    # logging.info(f\"Response: {response}\")\n", "\n", "    # clean response of ```json and ``` if it exists.\n", "    # and remove any text before and after the ```json and ```\n", "    if \"```json\" in response:\n", "        response = response.split(\"```json\")[1].split(\"```\")[0]\n", "    elif \"```\" in response:\n", "        response = response.split(\"```\")[1].split(\"```\")[0]\n", "\n", "    # parse the response as json\n", "    try:\n", "        text_placeholder_manual_insertions = json.loads(response)\n", "    except json.JSONDecodeError:\n", "        raise ValueError(\"Invalid JSON response\" + response)\n", "\n", "    # check each entry has placeholder and replacement_text\n", "    for entry in text_placeholder_manual_insertions:\n", "        if \"placeholder\" not in entry or \"replacement_text\" not in entry:\n", "            raise ValueError(\"Each entry must have placeholder and replacement_text\")\n", "        # rename replacement_text to value\n", "        entry[\"value\"] = entry[\"replacement_text\"]\n", "        # remove replacement_text\n", "        del entry[\"replacement_text\"]\n", "        entry[\"resize\"] = True\n", "\n", "    return text_placeholder_manual_insertions\n", "\n", "\n", "outline = []\n", "\n", "for idx, slide in enumerate(doc_config.get(\"slides\", [])):\n", "    slide_id = slide.get(\"id\")\n", "    text_placeholders = slide.get(\"text_placeholders\", [])\n", "    text_placeholder_manual_insertions = get_text_placeholder_manual_insertions(\n", "        text_placeholders, slides_markdown[idx].get(\"markdown\")\n", "    )\n", "    outline.append(\n", "        Outline(\n", "            slide_id=slide_id,\n", "            text_placeholder_manual_insertions=text_placeholder_manual_insertions,\n", "        ).to_dict()\n", "    )\n", "logging.info(f\"Outline: {outline}\")\n", "sync_generate_flashdocs_slides(\n", "    prompt=\"\",\n", "    source_document_id=document_id,\n", "    outline=outline,\n", "    google_document_editors=[\"<EMAIL>\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", " \n", "from api.slides.flashdocs import get_flashdocs_document_id\n", "get_flashdocs_document_id(\n", "    \"https://docs.google.com/presentation/d/145aYgEBCWpil6WtghbsdLeoSZDZJIgW9PMFEKPz6CnY/edit?slide=id.p1#slide=id.p1\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", " \n", "from api.slides.flashdocs import sync_generate_flashdocs_slides\n", "source_document_id = \"51c478dc-6e88-4b93-aa20-bad8500ab9e8\"\n", "outline = [\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\n", "                \"placeholder\": \"[title]\",\n", "                \"value\": \"Scaling Your Marketing Efforts\",\n", "                \"resize\": True,\n", "            },\n", "            {\"placeholder\": \"[subheader]\", \"value\": \"\", \"resize\": True},\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"e4057227-46a4-485d-8e46-093f68c4a16e\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\"placeholder\": \"[title]\", \"value\": \"## Today's Agenda\", \"resize\": True},\n", "            {\n", "                \"placeholder\": \"[list_1]\",\n", "                \"value\": \"**05 min:** Introductions\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_2]\",\n", "                \"value\": \"**05 min:** Tofu Overview\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_3]\",\n", "                \"value\": \"**10 min:** Alignment with Your Goals\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_4]\",\n", "                \"value\": \"**10 min:** Onboarding Timeline\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_5]\",\n", "                \"value\": \"**20 min:** Platform Demo\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_6]\",\n", "                \"value\": \"**05 min:** Next Steps & Wrap-up\",\n", "                \"resize\": True,\n", "            },\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"48ea9fe2-e4b8-4264-944b-b917528f1125\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"77e041a9-07be-470f-9ea4-0bab213027aa\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\"placeholder\": \"[title]\", \"value\": \"What is Tofu?\", \"resize\": True},\n", "            {\n", "                \"placeholder\": \"[paragraph]\",\n", "                \"value\": \"Tofu is a unified platform for B2B marketing teams that:\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_1]\",\n", "                \"value\": \"Creates hyper-personalized, omnichannel campaigns at scale\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_2]\",\n", "                \"value\": \"Converts leads using AI-generated content\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_3]\",\n", "                \"value\": \"Leverages your brand guidelines and segmentation\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_4]\",\n", "                \"value\": \"Optimizes campaign performance with insights and analytics\",\n", "                \"resize\": True,\n", "            },\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"98ca9e73-8a56-461a-bb31-6bd01839b3fc\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\"placeholder\": \"[title]\", \"value\": \"Tofu's Capabilities\", \"resize\": True},\n", "            {\"placeholder\": \"[paragraph]\", \"value\": \"Tofu excels at:\", \"resize\": True},\n", "            {\n", "                \"placeholder\": \"[list_1]\",\n", "                \"value\": \"- Generating personalized content across multiple channels\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_2]\",\n", "                \"value\": \"- Creating unlimited variations of on-brand content\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_3]\",\n", "                \"value\": \"- Producing various content types (ads, emails, landing pages, etc.)\",\n", "                \"resize\": True,\n", "            },\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"1ba39a7f-14f4-4cb7-ac91-8113723e6f02\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\n", "                \"placeholder\": \"[list_1]\",\n", "                \"value\": \"- Schedule initial sessions for all team members\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_2]\",\n", "                \"value\": \"- Offer recurring or ad hoc meetings based on your needs\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_3]\",\n", "                \"value\": \"- Create a Slack channel for questions and feedback\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subsubheader_1]\",\n", "                \"value\": \"**1. Hands-on Training**\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subheader_1]\",\n", "                \"value\": \"## Your First 3 Months with Tofu\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_4]\",\n", "                \"value\": \"- Provide ongoing assistance throughout onboarding\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_5]\",\n", "                \"value\": \"- Develop templates for your repeatable use cases\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subsubheader_2]\",\n", "                \"value\": \"**2. Dedicated Support**\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subheader_2]\",\n", "                \"value\": \"## Your First 3 Months with Tofu\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_6]\",\n", "                \"value\": \"- Leverage pre-built templates to jumpstart your efforts\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_7]\",\n", "                \"value\": \"- Learn to scale your marketing quickly and efficiently\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subsubheader_3]\",\n", "                \"value\": \"**3. Custom Templates**\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subheader_3]\",\n", "                \"value\": \"## Your First 3 Months with Tofu\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[title]\",\n", "                \"value\": \"## Your First 3 Months with Tofu\",\n", "                \"resize\": True,\n", "            },\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"c08a4598-15ed-46cb-9db3-70500ea0d0ab\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\"placeholder\": \"[title]\", \"value\": \"Priority Use Cases\", \"resize\": True},\n", "            {\"placeholder\": \"[subheader]\", \"value\": \"\", \"resize\": True},\n", "            {\n", "                \"placeholder\": \"[list_1]\",\n", "                \"value\": \"**1. Personalized Outreach**\\n- Craft tailored email sequences for specific industries\\n- Design targeted landing pages for market segments\\n- Develop customized messaging using account data\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_2]\",\n", "                \"value\": \"**2. Content Repurposing**\\n- Transform existing assets into multi-channel content\\n- Create blog posts from webinars and case studies\\n- Generate personalized follow-ups based on engagement\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_3]\",\n", "                \"value\": \"**3. Nurture Sequence Optimization**\\n- Build multi-touch, personalized sequences\\n- Develop targeted ABM flows for high-value accounts\\n- Use AI-generated insights to refine content and timing\",\n", "                \"resize\": True,\n", "            },\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"5f4d1179-5a38-4c1e-84de-73db8210950d\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\n", "                \"placeholder\": \"[title]\",\n", "                \"value\": \"Aligning Tofu with Your Initiatives\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subheader_1]\",\n", "                \"value\": \"First Campaign:\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_1]\",\n", "                \"value\": \"- Focus on promoting your upcoming Madison event\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_2]\",\n", "                \"value\": \"- Drive sign-ups and increase attendance\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[list_3]\",\n", "                \"value\": \"- Create buzz and generate business opportunities\",\n", "                \"resize\": True,\n", "            },\n", "            {\"placeholder\": \"[list_4]\", \"value\": \"Next Campaigns:\", \"resize\": True},\n", "            {\n", "                \"placeholder\": \"[subheader_2]\",\n", "                \"value\": \"- To be discussed and planned together\",\n", "                \"resize\": True,\n", "            },\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"720c64b7-31b3-4a4f-bf6a-81ef94a6dda4\",\n", "    },\n", "    {\n", "        \"content_instruction\": None,\n", "        \"layout_instruction\": None,\n", "        \"markdown\": None,\n", "        \"text_placeholder_manual_insertions\": [\n", "            {\"placeholder\": \"[title]\", \"value\": \"Onboarding Schedule\", \"resize\": True},\n", "            {\n", "                \"placeholder\": \"[subheader_1]\",\n", "                \"value\": \"Week 1: Foundation\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[paragraph_1]\",\n", "                \"value\": \"- Platform overview and setup\\n- Defining initial use cases\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subheader_2]\",\n", "                \"value\": \"Week 2: Content Creation\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[paragraph_2]\",\n", "                \"value\": \"- Hands-on training with Tofu's AI\\n- Developing your first campaigns\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subheader_3]\",\n", "                \"value\": \"Week 3: Campaign Execution\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[paragraph_3]\",\n", "                \"value\": \"- Launching and monitoring initial campaigns\\n- Fine-tuning based on early results\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[subheader_4]\",\n", "                \"value\": \"Week 4: Optimization and Scale\",\n", "                \"resize\": True,\n", "            },\n", "            {\n", "                \"placeholder\": \"[paragraph_4]\",\n", "                \"value\": \"- Analyzing performance metrics\\n- Expanding use cases and scaling efforts\",\n", "                \"resize\": True,\n", "            },\n", "        ],\n", "        \"image_placeholder_manual_insertions\": None,\n", "        \"slide_id\": \"20a40542-40fb-4749-a8a3-dcb87dd3f729\",\n", "    },\n", "]\n", "\n", "sync_generate_flashdocs_slides(\n", "    prompt=\"\",\n", "    source_document_id=source_document_id,\n", "    outline=outline,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.gdrive_utils import resize_google_slides_text_boxes\n", "\n", "google_slides_url = \"https://docs.google.com/presentation/d/1IVWEowJQSfs88EUq2Arjkqn4n3eXp8MXTyE52g3oRaY/edit?slide=id.p1#slide=id.p1\"\n", "\n", "resize_google_slides_text_boxes(google_slides_url)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading secrets from AWS Secrets Manager: tofu-backend-local-secrets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_arn\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-05-19 11:00:43,835 INFO: found organization id: tofu-ykka\n", "2025-05-19 11:00:44,093 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n", "2025-05-19 11:00:44,994 INFO: Note: NumExpr detected 12 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "2025-05-19 11:00:44,995 INFO: NumExpr defaulting to 8 threads.\n"]}, {"data": {"text/plain": ["[{'slide_number': 'slide 1',\n", "  'placeholder_map': [{'placeholder': '[COMPANY NAME]',\n", "    'original_text': None}]},\n", " {'slide_number': 'slide 2', 'placeholder_map': []},\n", " {'slide_number': 'slide 3', 'placeholder_map': []},\n", " {'slide_number': 'slide 4', 'placeholder_map': []},\n", " {'slide_number': 'slide 5', 'placeholder_map': []},\n", " {'slide_number': 'slide 6', 'placeholder_map': []},\n", " {'slide_number': 'slide 7', 'placeholder_map': []},\n", " {'slide_number': 'slide 8', 'placeholder_map': []},\n", " {'slide_number': 'slide 9', 'placeholder_map': []}]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.slides.template import (\n", "    _get_google_slides_flashdocs_document_id,\n", "    get_document_config,\n", "    _extract_placeholder_mappings,\n", ")\n", "\n", "google_slides_url = \"https://docs.google.com/presentation/d/11LkfMOx_2ixb20rqFFNqzI9Mn61APF_cdBVHe1zAvdo/edit?slide=id.p1#slide=id.p1\"\n", "# flashdocs_document_id = _get_google_slides_flashdocs_document_id(google_slides_url)\n", "flashdocs_document_id = \"13691483-5d45-4af7-9d76-150c6af6b68d\"\n", "doc_config = get_document_config(document_id=flashdocs_document_id, include_slides=True)\n", "placeholder_mappings = _extract_placeholder_mappings(doc_config.get(\"slides\", []))\n", "placeholder_mappings\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}