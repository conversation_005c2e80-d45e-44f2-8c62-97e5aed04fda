{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6b695c86-e7e1-478e-8fe4-f004bbe0b109", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Python-dotenv could not parse statement starting at line 2\n", "Python-dotenv could not parse statement starting at line 3\n", "Python-dotenv could not parse statement starting at line 4\n", "Python-dotenv could not parse statement starting at line 6\n", "Python-dotenv could not parse statement starting at line 7\n", "Python-dotenv could not parse statement starting at line 8\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading secrets from AWS Secrets Manager: tofu-backend-local-secrets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "/Users/<USER>/Documents/Projects/tofu/backend/tofu/venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_arn\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-05-09 12:07:43,295 INFO: found organization id: tofu-ykka\n", "2025-05-09 12:07:43,444 WARNING: USER_AGENT environment variable not set, consider setting it to identify your requests.\n", "2025-05-09 12:07:44,479 WARNING: Python-dotenv could not parse statement starting at line 2\n", "2025-05-09 12:07:44,480 WARNING: Python-dotenv could not parse statement starting at line 3\n", "2025-05-09 12:07:44,480 WARNING: Python-dotenv could not parse statement starting at line 4\n", "2025-05-09 12:07:44,480 WARNING: Python-dotenv could not parse statement starting at line 6\n", "2025-05-09 12:07:44,481 WARNING: Python-dotenv could not parse statement starting at line 7\n", "2025-05-09 12:07:44,481 WARNING: Python-dotenv could not parse statement starting at line 8\n", "Analysis for Target Info Group: TOFU | CFO Role\n", "Target Info Group ID: 15893\n", "Cutoff date for preservation: 2025-05-02 19:07:44.870502+00:00\n", "\n", "Targets to be deleted (excluding targets created in the last 7 days):\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - stacie.yama<PERSON>@appcast.io\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@bradyenterprises.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ms<PERSON><PERSON><PERSON>@hebrewseniorlife.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jmclaugh<PERSON>@freymiller.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ben.mac<PERSON><PERSON><PERSON><PERSON>@efi.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - david.paul<PERSON><PERSON>@cmhc.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - m<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - null\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - grenzu<PERSON>@arisglobal.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - enem<PERSON><PERSON>@bradysullivan.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - tatiana.uscoco<PERSON>@veris.com.ec\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - do<PERSON><PERSON><PERSON>@luminochem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@msphousing.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><EMAIL>\n", "  - j<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@mutual-distributing.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@tggallagher.com\n", "  - <EMAIL>\n", "  - c<PERSON>el<PERSON>@kdmpop.com\n", "  - <EMAIL>\n", "  - lui<PERSON><PERSON><PERSON>@echannelsinc.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - n<PERSON><PERSON><PERSON>@circuitclinical.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - t<PERSON><PERSON><PERSON>@jobcase.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - david<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - diben<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - lisar<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ashley.groffe<PERSON><PERSON>@boston.gov\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - olga.thillayed<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ptaw<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON>@forwardair.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - don.br<PERSON><PERSON>@emergeortho.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - n<PERSON><PERSON><PERSON>@vygr.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - danie<PERSON>@aubuchon.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@crst.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON>@cpllabs.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jenny<PERSON>@serruyaequity.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - p_s<PERSON><PERSON><PERSON>@litex.bg\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jason.fae<PERSON>@bruker.com\n", "  - chris<PERSON><PERSON><PERSON>@fenixparts.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@flstransport.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ab<PERSON><PERSON><PERSON><PERSON><PERSON>@gmaul.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><PERSON>@nyby.no\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - c<PERSON><PERSON><PERSON>@nycominc.com\n", "  - <EMAIL>\n", "  - steve.he<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - m<PERSON><PERSON>@bcbpromotions.com\n", "  - rc<PERSON><PERSON>@lineagelogistics.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - micha<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - patrick.<PERSON><PERSON><PERSON>@kayem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - na<PERSON><PERSON>@vncindia.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - chris.batto<PERSON><PERSON>@heritagesvs.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@go-axion.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dick.david<PERSON>@gitkraken.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - s<PERSON><PERSON>@campaldersgate.net\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - brian.bau<PERSON>@pilotthomas.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - tghering<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dmit<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@generalinsulation.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - da<PERSON><PERSON><PERSON>@andersonautomotivegroup.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - scott.he<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jya<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - danh<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - mbeau<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - rjmc<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - me<PERSON><PERSON>@cdc.gov\n", "  - <EMAIL>\n", "  - sha<PERSON><PERSON>@aam.ae\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - sfred<PERSON><PERSON>@ifutr.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - d<PERSON><PERSON><PERSON><PERSON>@winholt.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - a<PERSON><PERSON><PERSON>@lbconstructioninc.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <PERSON><PERSON><PERSON><PERSON>@johnston.k12.nc.us\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@elevate.bio\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@dillonsupply.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - johnson<PERSON>@schneider.com\n", "  - j<PERSON><PERSON><PERSON>@dober.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@twinlakes.net\n", "  - antoi<PERSON><PERSON><PERSON><PERSON><PERSON>@wcps.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j.ha<PERSON><PERSON>@shell.com\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - p<PERSON><PERSON>@lifework.edu\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dean<PERSON><PERSON><PERSON>@adamsremco.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - zta<PERSON><PERSON>@dchem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - a<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - robby<PERSON><PERSON>@standardmeat.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON>@stevenstransport.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j.schu<PERSON><PERSON>@zwillingbeautygroup.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - micha<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "\n", "Hubspot Record IDs to be tracked:\n", "  - 78816278800\n", "  - 102435311646\n", "  - 66478285124\n", "  - 107852389807\n", "  - 100099131535\n", "  - 14555601\n", "  - 107852530822\n", "  - 102415520146\n", "  - 15709601\n", "  - 98404253732\n", "  - 107857194641\n", "  - 102436631823\n", "  - 13210313\n", "  - 102399051291\n", "  - 107856091017\n", "  - 80541870211\n", "  - 102416449879\n", "  - 72807871826\n", "  - 71586413670\n", "  - 13213816\n", "  - 23455351\n", "  - 78550476879\n", "  - 66133875357\n", "  - 20122351\n", "  - 66133582718\n", "  - 26826403400\n", "  - 66126210120\n", "  - 98380825452\n", "  - 102410546436\n", "  - 32887811236\n", "  - 12972354\n", "  - 102403231027\n", "  - 98329030014\n", "  - 107854972066\n", "  - 74987860577\n", "  - 107854225977\n", "  - 107855202378\n", "  - 102406793853\n", "  - 102433148009\n", "  - 86259923235\n", "  - 107854515569\n", "  - 59403690916\n", "  - 97788722548\n", "  - 102424461698\n", "  - 9079020051\n", "  - 6480574849\n", "  - 107863289468\n", "  - 98387209222\n", "  - 23287601\n", "  - 13217151\n", "  - 82886093707\n", "  - 100112115834\n", "  - 107852906497\n", "  - 102882197519\n", "  - 20968180072\n", "  - 20193501\n", "  - 107854583187\n", "  - 22905951\n", "  - 98712703871\n", "  - 66126381961\n", "  - 107863408985\n", "  - 102425808154\n", "  - 12244002\n", "  - 6188572684\n", "  - 109326068230\n", "  - 107843470880\n", "  - 75927731294\n", "  - 79209482593\n", "  - 114297248568\n", "  - 38308188681\n", "  - 66126290834\n", "  - 118068466513\n", "  - 79482590545\n", "  - 23225701\n", "  - 107856107703\n", "  - 71583115183\n", "  - 78526093646\n", "  - 79012298283\n", "  - 107855202380\n", "  - 107861112454\n", "  - 78717462848\n", "  - 107859942542\n", "  - 12974251\n", "  - 75854627143\n", "  - 102397815693\n", "  - 13216862\n", "  - 78534446726\n", "  - 98405307524\n", "  - 100108867703\n", "  - 75865814424\n", "  - 100095880336\n", "  - 6678682124\n", "  - 83946895233\n", "  - 98378765832\n", "  - 107861112462\n", "  - 6670710145\n", "  - 75909726849\n", "  - 23288051\n", "  - 98338951020\n", "  - 22954401\n", "  - 107854225980\n", "  - 75852210873\n", "  - 12974252\n", "  - 23074701\n", "  - 102414241196\n", "  - 75884343315\n", "  - 25096563210\n", "  - 100113666467\n", "  - 75020506478\n", "  - 107861757736\n", "  - 102417687621\n", "  - 100110877482\n", "  - 71808421259\n", "  - 107860791328\n", "  - 76689841995\n", "  - 100096035897\n", "  - 19377801\n", "  - 102417533460\n", "  - 11227651\n", "  - 75908339286\n", "  - 6741826850\n", "  - 107861091121\n", "  - 107863009590\n", "  - 22738801\n", "  - 98407477796\n", "  - 81739543705\n", "  - 81456864174\n", "  - 93180649773\n", "  - 107851203132\n", "  - 85056382733\n", "  - 47362168906\n", "  - 107846697229\n", "  - 100110104994\n", "  - 56332175940\n", "  - 20272851\n", "  - 28869946498\n", "  - 98381521766\n", "  - 107861757752\n", "  - 107855202374\n", "  - 75860608390\n", "  - 71583510017\n", "  - 78664464237\n", "  - 8676854\n", "  - 85596427369\n", "  - 21935261107\n", "  - 29132527248\n", "  - 98380388140\n", "  - 21874551\n", "  - 94870024073\n", "  - 107854423419\n", "  - 78138578783\n", "  - 75875155328\n", "  - 102397505165\n", "  - 77929956403\n", "  - 98379875604\n", "  - 80541787479\n", "  - 96035073168\n", "  - 107861757760\n", "  - 79962264217\n", "  - 71600774795\n", "  - 100171357828\n", "  - 13212456\n", "  - 107852412212\n", "  - 66088560664\n", "  - 107852734541\n", "  - 19651151\n", "  - 44682700594\n", "  - 19088251\n", "  - 107863496460\n", "  - 13215814\n", "  - 102429105319\n", "  - 107857074279\n", "  - 83880337842\n", "  - 33786462527\n", "  - 78549855027\n", "  - 33023566681\n", "  - 107818980003\n", "  - 100288223888\n", "  - 17519451\n", "  - 107862733834\n", "  - 21264258672\n", "  - 79974066818\n", "  - 26823584107\n", "  - 78442252891\n", "  - 78767543618\n", "  - 67369678764\n", "  - 112981171263\n", "  - 71587199759\n", "  - 75957605667\n", "  - 107820512296\n", "  - 107859963533\n", "  - 47748240984\n", "  - 98405694226\n", "  - 16400201\n", "  - 79920284830\n", "  - 71602834749\n", "  - 66395714105\n", "  - 86084042030\n", "  - 107864470902\n", "  - 107852554764\n", "  - 100118547530\n", "  - 115883956576\n", "  - 102404624704\n", "  - 25840077367\n", "  - 44440481147\n", "  - 62324757001\n", "  - 12246903\n", "  - 107854445841\n", "  - 66070836499\n", "  - 102401373719\n", "  - 23287304\n", "  - 107861091127\n", "  - 107851455127\n", "  - 102416606030\n", "  - 107861138276\n", "  - 107854911879\n", "  - 71670533473\n", "  - 16266151\n", "  - 102415213742\n", "  - 98296514621\n", "  - 81441633924\n", "  - 98994357099\n", "  - 71983970982\n", "  - 34424866904\n", "  - 90250085034\n", "  - 107862733843\n", "  - 107851203137\n", "  - 11893871747\n", "  - 94229083406\n", "  - 79176282663\n", "  - 75857420347\n", "  - 73702527856\n", "  - 23454351\n", "  - 90926018370\n", "  - 78787771532\n", "  - 65445975164\n", "  - 100104826477\n", "  - 74147362478\n", "  - 107852496152\n", "  - 90761171013\n", "  - 107865588031\n", "  - 20550901\n", "  - 75861563041\n", "  - 98390293876\n", "  - 102414745939\n", "  - 40354399284\n", "  - 75852991076\n", "  - 71672097081\n", "  - 22754701\n", "  - 107863226914\n", "  - 66132232297\n", "  - 97614205017\n", "  - 78368517938\n", "  - 71620370693\n", "  - 66084034445\n", "  - 75911197544\n", "  - 81696697641\n", "  - 13214570\n", "  - 102401528960\n", "  - 78825407762\n", "  - 75849173588\n", "  - 71598573840\n", "  - 92238032766\n", "  - 107852906499\n", "  - 75216400414\n", "  - 88167130729\n", "  - 78864739974\n", "  - 22012451\n", "  - 13212466\n", "  - 75862582566\n", "  - 107861046407\n", "  - 53696417330\n", "  - 102416759383\n", "  - 75861784075\n", "  - 75953865122\n", "  - 80796311333\n", "  - 107851203135\n", "  - 47361726216\n", "  - 107857217921\n", "  - 75866608041\n", "  - 87067396716\n", "  - 98405694037\n", "  - 100110877480\n", "  - 22258051\n", "  - 75192093321\n", "  - 107847002691\n", "  - 102418963871\n", "  - 107855342613\n", "  - 16512601\n", "  - 82698919729\n", "  - 75853650271\n", "  - 100093873976\n", "  - 98381716482\n", "  - 71895897682\n", "  - 107863009589\n", "  - 78911618166\n", "  - 71583230391\n", "  - 102403541546\n", "  - 27018819484\n", "  - 45350514300\n", "  - 78879883393\n", "  - 76045951515\n", "  - 21268364361\n", "  - 102399206524\n", "  - 13211379\n", "  - 71586512040\n", "  - 5813257793\n", "  - 107862401601\n", "  - 83840655790\n", "  - 78526866690\n", "  - 12246601\n", "  - 71690791981\n", "  - 100094646164\n", "  - 66133311657\n", "  - 107856511924\n", "  - 74898120608\n", "  - 12247058\n", "  - 71586429204\n", "  - 102406638489\n", "  - 107861155954\n", "  - 98372608824\n", "  - 98097209372\n", "  - 71823382067\n", "  - 22851051\n", "  - 100140074001\n", "  - 88183393563\n", "  - 17372501\n", "  - 102403231022\n", "  - 22802251\n", "  - 66132561742\n", "  - 98405291826\n", "  - 97453050927\n", "  - 23470851\n", "  - 107854911885\n", "  - 101647422054\n", "  - 82168265065\n", "  - 21920931741\n", "  - 102410546438\n", "  - 56442526732\n", "  - 74942547632\n", "  - 78536590740\n", "  - 90416174209\n", "  - 81402084609\n", "  - 17519601\n", "  - 87197715304\n", "  - 47752896130\n", "  - 79568315576\n", "  - 102402148499\n", "  - 19197686131\n", "  - 86729827174\n", "  - 81292582806\n", "  - 102418811754\n", "  - 107853768247\n", "  - 78532576830\n", "  - 100095416729\n", "  - 12827101\n", "  - 12244202\n", "  - 70044396898\n", "  - 23288752\n", "  - 107836897114\n", "  - 80559002294\n", "  - 32931996177\n", "  - 21922421125\n", "  - 107856107699\n", "  - 87135159423\n", "  - 75850226809\n", "  - 107851203143\n", "  - 107855342614\n", "  - 107854492325\n", "  - 75862004529\n", "  - 84165256232\n", "  - 45344746851\n", "  - 66004026374\n", "  - 107856107776\n", "  - 66075621730\n", "  - 107863644847\n", "  - 13210910\n", "  - 80295677301\n", "  - 102402148502\n", "  - 107857148176\n", "  - 75915090352\n", "  - 23168801\n", "  - 81623639197\n", "  - 47603577673\n", "  - 100293539512\n", "  - 78553731334\n", "  - 107857316420\n", "  - 107854911870\n", "  - 67147753757\n", "  - 80991742042\n", "  - 82595964928\n", "  - 73280347307\n", "  - 13212906\n", "  - 71585853111\n", "  - 100111187203\n", "  - 114297248569\n", "  - 98405291818\n", "  - 107852279407\n", "  - 80162818405\n", "  - 108706978710\n", "  - 12247652\n", "  - 71589739444\n", "  - 75973015704\n", "  - 79191773056\n", "  - 19389551\n", "  - 71596454226\n", "  - 72050050965\n", "  - 78907020076\n", "  - 88710323308\n", "  - 98402432265\n", "  - 75910572974\n", "  - 107857217918\n", "  - 19036001\n", "  - 58909219387\n", "  - 107854423420\n", "  - 78403890517\n", "  - 98387363162\n", "  - 107857148181\n", "  - 71616356714\n", "  - 107855368030\n", "  - 79832373555\n", "  - 107855940122\n", "  - 20741751\n", "  - 72001061248\n", "  - 20248151\n", "  - 6645465\n", "  - 107862401595\n", "  - 74934657714\n", "  - 76681038850\n", "  - 100370921378\n", "  - 22817951\n", "  - 102406329168\n", "  - 95557104448\n", "  - 100107782065\n", "  - 107865588033\n", "  - 78234247849\n", "  - 81916414083\n", "  - 78529189713\n", "  - 101544752721\n", "  - 107853750687\n", "  - 75875188864\n", "  - 102432862513\n", "  - 107855313038\n", "  - 58904130078\n", "  - 107862978741\n", "  - 16262751\n", "  - 98381636489\n", "  - 100123294279\n", "  - 102424461691\n", "  - 107857148182\n", "  - 79148975417\n", "  - 17577051\n", "  - 102414592521\n", "  - 72381508726\n", "  - 98407477623\n", "  - 75860051499\n", "  - 79809232162\n", "  - 87085180191\n", "  - 98405918337\n", "  - 81438732958\n", "  - 11446601\n", "  - 28887933065\n", "  - 102415365774\n", "  - 79415968533\n", "  - 107861757749\n", "  - 78297272092\n", "  - 98378765671\n", "  - 17577451\n", "  - 98405307560\n", "  - 81367074158\n", "  - 94337313917\n", "  - 98405918295\n", "  - 21271351\n", "  - 18983851\n", "  - 66679902014\n", "  - 72270508914\n", "  - 71610779397\n", "  - 96255014795\n", "  - 107863496370\n", "  - 107854007045\n", "  - 78283053146\n", "  - 81708323762\n", "  - 51747117372\n", "  - 6731762354\n", "  - 102398433436\n", "  - 16976629604\n", "  - 107854423408\n", "  - 100094497380\n", "  - 107855292942\n", "  - 107855940121\n", "  - 66134313796\n", "  - 95878162831\n", "  - 71912325927\n", "  - 21252869298\n", "  - 102401683063\n", "  - 107855313034\n", "  - 102398900584\n", "  - 78970271334\n", "  - 22832051\n", "  - 76064054291\n", "  - 102415520145\n", "  - 9853292\n", "  - 98404383573\n", "  - 107818980005\n", "  - 107852389814\n", "  - 83934483493\n", "  - 102397658516\n", "  - 98372608689\n", "  - 98402432279\n", "  - 100105279020\n", "  - 107846697230\n", "  - 78547533313\n", "  - 107854007064\n", "  - 91767437710\n", "  - 102879678889\n", "  - 10344169\n", "  - 81547151747\n", "  - 97333009329\n", "  - 19640951\n", "  - 100112422976\n", "  - 74934738840\n", "  - 66135374377\n", "  - 107855226649\n", "  - 13210751\n", "  - 29941438738\n", "  - 77017216048\n", "  - 107859963550\n", "  - 107855008270\n", "  - 71674893468\n", "  - 79867419231\n", "  - 107856091014\n", "  - 102402612245\n", "  - 98686461290\n", "  - 73682658209\n", "  - 105185119005\n", "  - 88138236188\n", "  - 107860516788\n", "  - 17284851\n", "  - 102436476929\n", "  - 45843046574\n", "  - 47737124462\n", "  - 33872332427\n", "  - 45292174190\n", "  - 100099131528\n", "  - 107856091018\n", "  - 98381649163\n", "  - 107855430456\n", "  - 118054287752\n", "  - 107847002643\n", "  - 102401220695\n", "  - 75914106205\n", "  - 107854515570\n", "  - 80001960086\n", "  - 66126381930\n", "  - 77872793164\n", "  - 107847002634\n", "  - 107855940130\n", "  - 98390311486\n", "  - 107854007059\n", "  - 94390233663\n", "  - 107863311637\n", "  - 107854225990\n", "  - 102435477925\n", "  - 107854492315\n", "  - 66126210116\n", "  - 89949397280\n", "  - 75877273138\n", "  - 76678658315\n", "  - 107855292937\n", "  - 96014750131\n", "  - 107861155947\n", "  - 98384693791\n", "  - 78080506901\n", "  - 100096189827\n", "  - 66122060348\n", "  - 107863226929\n", "  - 78532243559\n", "  - 17922904347\n", "  - 75854891030\n", "  - 100096966438\n", "  - 102425808153\n", "  - 98389361849\n", "  - 98389378910\n", "  - 102416606035\n", "  - 107854007062\n", "  - 107817859713\n", "  - 75865940342\n", "  - 98388151322\n", "  - 107853728063\n", "  - 23289051\n", "  - 100112888928\n", "  - 102427940526\n", "  - 19380351\n", "  - 107860791350\n", "  - 22846551\n", "  - 17285951\n", "  - 79032384772\n", "  - 81451883798\n", "  - 107863409004\n", "  - 107864470897\n", "  - 102423036248\n", "  - 71586390143\n", "  - 66133354167\n", "  - 71588317038\n", "  - 13213420\n", "  - 107863464595\n", "  - 102401220699\n", "  - 83548765067\n", "  - 75946817707\n", "  - 100113514032\n", "  - 107862733846\n", "  - 74942748327\n", "  - 98678979490\n", "  - 78546758577\n", "  - 102404781400\n", "  - 75860968006\n", "  - 82141761903\n", "  - 107847002638\n", "  - 82192413532\n", "  - 66126846793\n", "  - 102399669811\n", "  - 81385086057\n", "  - 107863689744\n", "  - 107852554772\n", "  - 79722808974\n", "  - 22724551\n", "  - 13213821\n", "  - 75877420640\n", "  - 79031160210\n", "  - 78203698261\n", "  - 76015089968\n", "  - 90120634218\n", "  - 71595267075\n", "  - 107854972055\n", "  - 102403852328\n", "  - 39640211759\n", "  - 102401683066\n", "  - 77935573562\n", "  - 56338752927\n", "  - 98404367123\n", "  - 75930079242\n", "  - 107852389800\n", "  - 75903241494\n", "  - 107861046400\n", "  - 98381685943\n", "  - 107854561073\n", "  - 102404624709\n", "  - 77957239297\n", "  - 71585767210\n", "  - 66133875537\n", "  - 107863009575\n", "  - 36653995281\n", "  - 102416914578\n", "  - 78550009710\n", "  - 102429282392\n", "  - 107863289460\n", "  - 107862958879\n", "  - 47750317965\n", "  - 81549391679\n", "  - 81329234079\n", "  - 82748237444\n", "  - 89881564456\n", "  - 98390311537\n", "  - 22548651\n", "  - 107855456541\n", "  - 75903250796\n", "  - 76054238864\n", "  - 78900745377\n", "  - 96516431366\n", "  - 66122060369\n", "  - 76265266991\n", "  - 107863226928\n", "  - 64774652943\n", "  - 93237705012\n", "  - 46142083705\n", "  - 107852496168\n", "  - 107859878721\n", "  - 16878851\n", "  - 23453853\n", "  - 78767832918\n", "  - 12974101\n", "  - 18564451\n", "  - 47750317967\n", "  - 46132076819\n", "  - 100081915055\n", "  - 56309845035\n", "  - 102436631822\n", "  - 78548616301\n", "  - 79866743481\n", "  - 101770709569\n", "  - 23518101\n", "  - 102436821316\n", "  - 75883106821\n", "  - 95562251353\n", "  - 48154434975\n", "  - 11899851\n", "  - 102398433443\n", "  - 74284684916\n", "  - 76044142134\n", "  - 46136909587\n", "  - 20193451\n", "  - 98387209344\n", "  - 74918361257\n", "  - 81662055338\n", "  - 71587704699\n", "  - 79507316783\n", "  - 98384417134\n", "  - 87346367122\n", "  - 98390167061\n", "  - 71917606288\n", "  - 95317626723\n", "  - 107856091022\n", "  - 107818980004\n", "  - 107863408986\n", "  - 107852906512\n", "  - 107855430467\n", "  - 98330331136\n", "  - 107857217907\n", "  - 107856107697\n", "  - 51879256347\n", "  - 100095416734\n", "  - 98405307500\n", "  - 49204167799\n", "  - 71686390899\n", "  - 13680242814\n", "  - 66133939520\n", "  - 75862225584\n", "  - 102401373720\n", "  - 58947389008\n", "  - 12827052\n", "  - 107861757737\n", "  - 87605066934\n", "  - 47189792666\n", "  - 102435977332\n", "  - 46437843799\n", "  - 19374351\n", "  - 108461408692\n", "  - 8865820\n", "  - 66459170565\n", "  - 98384693807\n", "  - 102406485176\n", "  - 84173996937\n", "  - 75982938910\n", "  - 26845638434\n", "  - 78310198649\n", "  - 75865559722\n", "  - 107855292941\n", "  - 75853400333\n", "  - 100600062029\n", "  - 98725557325\n", "  - 13213402\n", "  - 118030504006\n", "  - 107857148184\n", "  - 102397505164\n", "  - 107854911874\n", "  - 71677052695\n", "  - 107861046408\n", "  - 79161295507\n", "  - 98378765831\n", "  - 80992851477\n", "  - 13217402\n", "  - 107862494810\n", "  - 52272331073\n", "  - 98404383605\n", "  - 52482700908\n", "  - 107855008272\n", "  - 80406672907\n", "  - 20441251\n", "  - 59283068167\n", "  - 56335533749\n", "  - 49478062632\n", "  - 47748132979\n", "  - 71585465679\n", "  - 107863289471\n", "  - 107863009586\n", "  - 22800851\n", "  - 71619567169\n", "  - 66133163300\n", "  - 75860234248\n", "  - 75956695831\n", "  - 102438575619\n", "  - 77276708268\n", "  - 98381521797\n", "  - 78528414343\n", "  - 102406021459\n", "  - 98381552197\n", "  - 107863464592\n", "  - 107855368026\n", "  - 79758596745\n", "  - 81848902979\n", "  - 22240051\n", "  - 112708481925\n", "  - 107863009579\n", "  - 107855202376\n", "  - 17024651\n", "  - 71583069590\n", "  - 17578651\n", "\n", "Campaigns to be updated:\n", "\n", "Campaign ID: 124940\n", "Name: Top-of-funnel-CFO-audience\n", "Targets to be removed from campaign_params:\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - stacie.yama<PERSON>@appcast.io\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@bradyenterprises.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ms<PERSON><PERSON><PERSON>@hebrewseniorlife.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jmclaugh<PERSON>@freymiller.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ben.mac<PERSON><PERSON><PERSON><PERSON>@efi.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - david.paul<PERSON><PERSON>@cmhc.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - m<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - null\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - grenzu<PERSON>@arisglobal.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - enem<PERSON><PERSON>@bradysullivan.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - tatiana.uscoco<PERSON>@veris.com.ec\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - do<PERSON><PERSON><PERSON>@luminochem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@msphousing.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><EMAIL>\n", "  - j<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@mutual-distributing.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@tggallagher.com\n", "  - <EMAIL>\n", "  - c<PERSON>el<PERSON>@kdmpop.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - lui<PERSON><PERSON><PERSON>@echannelsinc.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - n<PERSON><PERSON><PERSON>@circuitclinical.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - t<PERSON><PERSON><PERSON>@jobcase.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - david<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - diben<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - lisar<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ashley.groffe<PERSON><PERSON>@boston.gov\n", "  - <EMAIL>\n", "  - olga.thillayed<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ptaw<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON>@forwardair.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - don.br<PERSON><PERSON>@emergeortho.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - n<PERSON><PERSON><PERSON>@vygr.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - danie<PERSON>@aubuchon.com\n", "  - b<PERSON><PERSON><PERSON>@crst.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON>@cpllabs.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jenny<PERSON>@serruyaequity.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - p_s<PERSON><PERSON><PERSON>@litex.bg\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jason.fae<PERSON>@bruker.com\n", "  - chris<PERSON><PERSON><PERSON>@fenixparts.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@flstransport.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ab<PERSON><PERSON><PERSON><PERSON><PERSON>@gmaul.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><PERSON>@nyby.no\n", "  - <EMAIL>\n", "  - c<PERSON><PERSON><PERSON>@nycominc.com\n", "  - steve.he<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - rc<PERSON><PERSON>@lineagelogistics.com\n", "  - m<PERSON><PERSON>@bcbpromotions.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - micha<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - patrick.<PERSON><PERSON><PERSON>@kayem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - na<PERSON><PERSON>@vncindia.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - chris.batto<PERSON><PERSON>@heritagesvs.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@go-axion.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dick.david<PERSON>@gitkraken.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - s<PERSON><PERSON>@campaldersgate.net\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - brian.bau<PERSON>@pilotthomas.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - tghering<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dmit<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@generalinsulation.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - da<PERSON><PERSON><PERSON>@andersonautomotivegroup.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - scott.he<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jya<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - danh<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - mbeau<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - rjmc<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - me<PERSON><PERSON>@cdc.gov\n", "  - <EMAIL>\n", "  - sha<PERSON><PERSON>@aam.ae\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - sfred<PERSON><PERSON>@ifutr.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - d<PERSON><PERSON><PERSON><PERSON>@winholt.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - a<PERSON><PERSON><PERSON>@lbconstructioninc.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <PERSON><PERSON><PERSON><PERSON>@johnston.k12.nc.us\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@elevate.bio\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@dillonsupply.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - johnson<PERSON>@schneider.com\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@dober.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@twinlakes.net\n", "  - antoi<PERSON><PERSON><PERSON><PERSON><PERSON>@wcps.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j.ha<PERSON><PERSON>@shell.com\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - p<PERSON><PERSON>@lifework.edu\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dean<PERSON><PERSON><PERSON>@adamsremco.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - zta<PERSON><PERSON>@dchem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - a<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - robby<PERSON><PERSON>@standardmeat.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON>@stevenstransport.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j.schu<PERSON><PERSON>@zwillingbeautygroup.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - micha<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "\n", "Campaign ID: 130361\n", "Name: ABM Top-of-funnel audience\n", "Targets to be removed from campaign_params:\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - stacie.yama<PERSON>@appcast.io\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@bradyenterprises.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ms<PERSON><PERSON><PERSON>@hebrewseniorlife.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jmclaugh<PERSON>@freymiller.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ben.mac<PERSON><PERSON><PERSON><PERSON>@efi.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - david.paul<PERSON><PERSON>@cmhc.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - m<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - null\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - grenzu<PERSON>@arisglobal.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - enem<PERSON><PERSON>@bradysullivan.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - tatiana.uscoco<PERSON>@veris.com.ec\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - do<PERSON><PERSON><PERSON>@luminochem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@msphousing.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><EMAIL>\n", "  - j<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@mutual-distributing.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@tggallagher.com\n", "  - <EMAIL>\n", "  - c<PERSON>el<PERSON>@kdmpop.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - lui<PERSON><PERSON><PERSON>@echannelsinc.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - n<PERSON><PERSON><PERSON>@circuitclinical.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - t<PERSON><PERSON><PERSON>@jobcase.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - david<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - diben<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - lisar<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ashley.groffe<PERSON><PERSON>@boston.gov\n", "  - <EMAIL>\n", "  - olga.thillayed<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ptaw<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON>@forwardair.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - don.br<PERSON><PERSON>@emergeortho.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - n<PERSON><PERSON><PERSON>@vygr.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - danie<PERSON>@aubuchon.com\n", "  - b<PERSON><PERSON><PERSON>@crst.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON>@cpllabs.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jenny<PERSON>@serruyaequity.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - p_s<PERSON><PERSON><PERSON>@litex.bg\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jason.fae<PERSON>@bruker.com\n", "  - chris<PERSON><PERSON><PERSON>@fenixparts.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@flstransport.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - ab<PERSON><PERSON><PERSON><PERSON><PERSON>@gmaul.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - k<PERSON><PERSON>@nyby.no\n", "  - <EMAIL>\n", "  - c<PERSON><PERSON><PERSON>@nycominc.com\n", "  - steve.he<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - rc<PERSON><PERSON>@lineagelogistics.com\n", "  - m<PERSON><PERSON>@bcbpromotions.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - micha<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - patrick.<PERSON><PERSON><PERSON>@kayem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - na<PERSON><PERSON>@vncindia.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - chris.batto<PERSON><PERSON>@heritagesvs.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@go-axion.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dick.david<PERSON>@gitkraken.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - s<PERSON><PERSON>@campaldersgate.net\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - brian.bau<PERSON>@pilotthomas.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - tghering<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dmit<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@generalinsulation.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - da<PERSON><PERSON><PERSON>@andersonautomotivegroup.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - scott.he<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - jya<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - danh<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - mbeau<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - rjmc<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - me<PERSON><PERSON>@cdc.gov\n", "  - <EMAIL>\n", "  - sha<PERSON><PERSON>@aam.ae\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - sfred<PERSON><PERSON>@ifutr.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - d<PERSON><PERSON><PERSON><PERSON>@winholt.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - a<PERSON><PERSON><PERSON>@lbconstructioninc.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <PERSON><PERSON><PERSON><PERSON>@johnston.k12.nc.us\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - b<PERSON><PERSON><PERSON>@elevate.bio\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@dillonsupply.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - johnson<PERSON>@schneider.com\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@dober.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j<PERSON><PERSON><PERSON>@twinlakes.net\n", "  - antoi<PERSON><PERSON><PERSON><PERSON><PERSON>@wcps.org\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j.ha<PERSON><PERSON>@shell.com\n", "  - <EMAIL>\n", "  - j<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - p<PERSON><PERSON>@lifework.edu\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - dean<PERSON><PERSON><PERSON>@adamsremco.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - zta<PERSON><PERSON>@dchem.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - a<PERSON><EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - robby<PERSON><PERSON>@standardmeat.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - r<PERSON><PERSON>@stevenstransport.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - j.schu<PERSON><PERSON>@zwillingbeautygroup.com\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - micha<PERSON>.<EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "  - <EMAIL>\n", "\n", "Executing deletions in dry run mode:\n", "DRY RUN - No changes will be made to the database\n", "\n", "Campaign 124940 would be updated with new targets:\n", "Old targets: {'<EMAIL>', '<EMAIL>', '<EMAIL>', 'stacie.yama<PERSON>@appcast.io', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'msjo<PERSON><PERSON>@hebrewseniorlife.org', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'null', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'}\n", "New targets: ['if<PERSON><PERSON><PERSON><PERSON>@newrelic.com', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'j<PERSON><PERSON>@springbuk.com', '<EMAIL>']\n", "\n", "Campaign 130361 would be updated with new targets:\n", "Old targets: {'<EMAIL>', '<EMAIL>', '<EMAIL>', 'stacie.yama<PERSON>@appcast.io', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'msjo<PERSON><PERSON>@hebrewseniorlife.org', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'null', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'}\n", "New targets: ['<EMAIL>', 'if<PERSON><PERSON><PERSON><PERSON>@newrelic.com', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']\n", "\n", "Campaign 124940 would be updated with new targets:\n", "Old targets: {'<EMAIL>', '<EMAIL>', '<EMAIL>', 'stacie.yama<PERSON>@appcast.io', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'msjo<PERSON><PERSON>@hebrewseniorlife.org', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'null', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'}\n", "New targets: ['if<PERSON><PERSON><PERSON><PERSON>@newrelic.com', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'j<PERSON><PERSON>@springbuk.com', '<EMAIL>']\n", "\n", "Campaign 130361 would be updated with new targets:\n", "Old targets: {'<EMAIL>', '<EMAIL>', '<EMAIL>', 'stacie.yama<PERSON>@appcast.io', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'msjo<PERSON><PERSON>@hebrewseniorlife.org', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'null', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'}\n", "New targets: ['<EMAIL>', 'if<PERSON><PERSON><PERSON><PERSON>@newrelic.com', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']\n"]}], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "from django.db.models import Q\n", "from api.models import TargetInfoGroup, Campaign, ContentGroup, Content\n", "from django.db import transaction\n", "from django.utils import timezone\n", "from datetime import timedelta\n", "from django.db import transaction\n", "from django.utils import timezone\n", "from datetime import timedelta\n", "\n", "def analyze_unused_targets(target_info_group_id):\n", "    \"\"\"\n", "    Analyze and prepare for deletion of unused targets in a target info group.\n", "    Returns the targets that can be safely deleted, preserving targets created in the last 7 days.\n", "    \"\"\"\n", "    # Get the target info group\n", "    target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)\n", "    \n", "    # Get all targets in this group\n", "    all_targets = target_info_group.targets.all()\n", "    \n", "    # Calculate the date 7 days ago\n", "    seven_days_ago = timezone.now() - <PERSON><PERSON><PERSON>(days=7)\n", "    \n", "    # Get all target keys and their hubspot_record_ids, excluding those created in the last 7 days\n", "    target_info = {\n", "        target.target_key: target.meta.get('hubspot_record_id') if target.meta else None\n", "        for target in all_targets \n", "        if target.created_at < seven_days_ago\n", "    }\n", "    \n", "    all_target_keys = set(target_info.keys())\n", "    \n", "    # Get all campaigns using this target info group\n", "    campaigns = Campaign.objects.filter(\n", "        campaign_params__targets__contains=[{target_info_group.target_info_group_key: []}]\n", "    )\n", "    \n", "    # Track which targets are used in reviewed content\n", "    used_target_keys = set()\n", "    campaigns_to_update = []\n", "    \n", "    for campaign in campaigns:\n", "        campaign_params = campaign.campaign_params or {}\n", "        targets = campaign_params.get('targets', [])\n", "        \n", "        # Find the target dict for this target info group\n", "        target_dict = None\n", "        for target in targets:\n", "            if isinstance(target, dict) and target_info_group.target_info_group_key in target:\n", "                target_dict = target\n", "                break\n", "        \n", "        if target_dict:\n", "            current_targets = set(target_dict[target_info_group.target_info_group_key])\n", "            \n", "            # Get content groups for this campaign\n", "            content_groups = ContentGroup.objects.filter(campaign=campaign)\n", "            \n", "            for content_group in content_groups:\n", "                reviewed_content_list = content_group.content_group_params.get('reviewed_content_list', [])\n", "                \n", "                for reviewed_content in reviewed_content_list:\n", "                    content_id = reviewed_content.get('content_id')\n", "                    if content_id:\n", "                        content = Content.objects.filter(id=content_id).first()\n", "                        if content:\n", "                            content_params = content.content_params or {}\n", "                            targets = content_params.get('targets', {})\n", "                            used_target_keys.update(targets.values())\n", "            \n", "            # Find unused targets in this campaign, excluding recent ones\n", "            unused_targets = current_targets - used_target_keys\n", "            unused_targets = {\n", "                target for target in unused_targets \n", "                if target in all_target_keys  # Only include targets that are older than 7 days\n", "            }\n", "            \n", "            if unused_targets:\n", "                campaigns_to_update.append({\n", "                    'campaign': campaign,\n", "                    'unused_targets': unused_targets\n", "                })\n", "    \n", "    # Find targets that are completely unused and older than 7 days\n", "    unused_target_keys = all_target_keys - used_target_keys\n", "    \n", "    # Collect hubspot_record_ids for deleted targets\n", "    deleted_hubspot_ids = [\n", "        target_info[key] for key in unused_target_keys \n", "        if target_info[key] is not None\n", "    ]\n", "    \n", "    return {\n", "        'targets_to_delete': unused_target_keys,\n", "        'campaigns_to_update': campaigns_to_update,\n", "        'target_info_group': target_info_group,\n", "        'seven_days_ago': seven_days_ago,\n", "        'deleted_hubspot_ids': deleted_hubspot_ids\n", "    }\n", "\n", "def print_deletion_plan(analysis):\n", "    \"\"\"Print the planned deletions in a readable format\"\"\"\n", "    print(f\"Analysis for Target Info Group: {analysis['target_info_group'].target_info_group_key}\")\n", "    print(f\"Target Info Group ID: {analysis['target_info_group'].id}\")\n", "    print(f\"Cutoff date for preservation: {analysis['seven_days_ago']}\")\n", "    \n", "    print(\"\\nTargets to be deleted (excluding targets created in the last 7 days):\")\n", "    for target_key in analysis['targets_to_delete']:\n", "        print(f\"  - {target_key}\")\n", "    \n", "    print(\"\\nHubspot Record IDs to be tracked:\")\n", "    for hubspot_id in analysis['deleted_hubspot_ids']:\n", "        print(f\"  - {hubspot_id}\")\n", "    \n", "    print(\"\\nCampaigns to be updated:\")\n", "    for campaign_update in analysis['campaigns_to_update']:\n", "        campaign = campaign_update['campaign']\n", "        print(f\"\\nCampaign ID: {campaign.id}\")\n", "        print(f\"Name: {campaign.campaign_name}\")\n", "        print(\"Targets to be removed from campaign_params:\")\n", "        for target in campaign_update['unused_targets']:\n", "            print(f\"  - {target}\")\n", "\n", "def execute_deletions(analysis, dry_run=True):\n", "    \"\"\"\n", "    Execute the deletions (or simulate them in dry run mode)\n", "    \n", "    Args:\n", "        analysis: The analysis result from analyze_unused_targets\n", "        dry_run: If True, only print what would be deleted without actually deleting\n", "    \"\"\"\n", "    if dry_run:\n", "        print(\"DRY RUN - No changes will be made to the database\")\n", "    \n", "    with transaction.atomic():\n", "        # Update target_info_group meta with deleted hubspot_record_ids\n", "        if not dry_run:\n", "            target_info_group = analysis['target_info_group']\n", "            meta = target_info_group.meta or {}\n", "            \n", "            # Get existing imported_record_ids or initialize empty list\n", "            existing_ids = meta.get('imported_record_ids', [])\n", "            \n", "            # Add new hubspot_record_ids\n", "            new_ids = existing_ids + analysis['deleted_hubspot_ids']\n", "            \n", "            # Update meta\n", "            meta['imported_record_ids'] = new_ids\n", "            target_info_group.meta = meta\n", "            target_info_group.save()\n", "        \n", "        # Delete unused targets\n", "        if not dry_run:\n", "            analysis['target_info_group'].targets.filter(\n", "                target_key__in=analysis['targets_to_delete']\n", "            ).delete()\n", "        \n", "        # Update campaign_params\n", "        for campaign_update in analysis['campaigns_to_update']:\n", "            campaign = campaign_update['campaign']\n", "            campaign_params = campaign.campaign_params or {}\n", "            targets = campaign_params.get('targets', [])\n", "            \n", "            # Update the targets list\n", "            for target in targets:\n", "                if isinstance(target, dict) and analysis['target_info_group'].target_info_group_key in target:\n", "                    current_targets = set(target[analysis['target_info_group'].target_info_group_key])\n", "                    new_targets = current_targets - campaign_update['unused_targets']\n", "                    target[analysis['target_info_group'].target_info_group_key] = list(new_targets)\n", "            \n", "            if not dry_run:\n", "                campaign.campaign_params = campaign_params\n", "                campaign.save()\n", "            \n", "            print(f\"\\nCampaign {campaign.id} would be updated with new targets:\")\n", "            print(f\"Old targets: {campaign_update['unused_targets']}\")\n", "            print(f\"New targets: {list(new_targets)}\")\n", "\n", "# Usage example:\n", "target_info_group_id = 15893\n", "\n", "# First, analyze what would be deleted\n", "analysis = analyze_unused_targets(target_info_group_id)\n", "print_deletion_plan(analysis)\n", "\n", "# Then, execute the deletions in dry run mode\n", "print(\"\\nExecuting deletions in dry run mode:\")\n", "execute_deletions(analysis, dry_run=True)\n", "\n", "# To actually perform the deletions, uncomment the following line:\n", "execute_deletions(analysis, dry_run=False)"]}, {"cell_type": "code", "execution_count": null, "id": "269fd32c-ff55-4ef7-bec6-5a71af4d9c1b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}