{"cells": [{"cell_type": "code", "execution_count": null, "id": "5818dd9b-3afc-44c4-a99a-b2be63182237", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "import json\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Campaign, Content, EventLogs\n", "from api.logger import log_campaign_creation, log_content_creation\n", "\n", "def backfill_event_logs():\n", "    # Backfill for Campaigns\n", "    campaigns = Campaign.objects.all()\n", "    event_type=\"campaign_creation\"\n", "    for campaign in campaigns:\n", "        if EventLogs.objects.filter(campaign_id=campaign.id, event_type=event_type).exists():\n", "            continue\n", "        # EventLogs.objects.create(\n", "        #     event_type=event_type,\n", "        #     user_id=campaign.creator_id,\n", "        #     playbook_id=campaign.playbook_id,\n", "        #     campaign_id=campaign.id,\n", "        #     payload=json.dumps({}),\n", "        #     created_at=campaign.created_at,\n", "        # )\n", "        print(f\"backfilled campaign: {campaign.id}\")\n", "\n", "backfill_event_logs()\n"]}, {"cell_type": "code", "execution_count": null, "id": "ef85a93e-3420-4af2-8430-6af48ee29e76", "metadata": {}, "outputs": [], "source": ["from django.db.models import Prefetch\n", "\n", "# Assuming `contents` is your queryset of Content objects\n", "contents = Content.objects.select_related('content_group__campaign', 'creator').all()\n", "\n", "event_type=\"content_creation\"\n", "\n", "# Prefetch existing event logs for relevant campaign_ids and event_types in one query\n", "campaign_ids = [content.content_group.campaign.id for content in contents if content.content_group and content.content_group.campaign]\n", "existing_logs = EventLogs.objects.filter(event_type=event_type, campaign_id__in=campaign_ids).values_list('campaign_id', 'payload')\n", "\n", "# Prepare a dict for quick lookups\n", "existing_content_ids_by_campaign = {}\n", "for campaign_id, payload in existing_logs:\n", "    if payload and 'content_id' in payload:\n", "        if campaign_id not in existing_content_ids_by_campaign:\n", "            existing_content_ids_by_campaign[campaign_id] = set()\n", "        existing_content_ids_by_campaign[campaign_id].add(payload['content_id'])\n", "\n", "# Collect entries to be bulk created\n", "entries_to_create = []\n", "\n", "for content in contents:\n", "    try:\n", "        campaign_id = content.content_group.campaign.id\n", "        content_id = content.id\n", "\n", "        # Check if this content_id already logged for this campaign\n", "        if campaign_id in existing_content_ids_by_campaign and content_id in existing_content_ids_by_campaign[campaign_id]:\n", "            continue\n", "\n", "        entry = EventLogs(\n", "            event_type=event_type,\n", "            user_id=content.creator_id,\n", "            playbook_id=content.playbook_id,\n", "            campaign_id=campaign_id,\n", "            payload={\"content_id\": content_id},\n", "            created_at=content.created_at,\n", "        )\n", "        entries_to_create.append(entry)\n", "\n", "    except Exception as e:\n", "        print(f\"failed for content {content_id} due to {e}\")\n", "\n", "# Bulk create the entries\n", "if entries_to_create:\n", "    # EventLogs.objects.bulk_create(entries_to_create)\n", "    print(f\"Backfilled {len(entries_to_create)} contents.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "308f3b1d-c37a-4acb-b76a-68692621837a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}