{"cells": [{"cell_type": "code", "execution_count": null, "id": "52267103-069b-4b96-b8a1-851e43f596fb", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "import json\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "from api.models import Campaign, Content, ContentGroup\n", "from api.utils import parse_s3_presigned_url, get_s3_file\n", "from urllib.parse import parse_qs, urlparse\n", "import boto3\n", "from api.playbook_health import PlaybookHealthChecker\n", "\n", "def rename_s3_object(bucket_name, old_key, new_key):\n", "    s3 = boto3.client('s3')\n", "    \n", "    # Copy the object to the new key\n", "    copy_source = {\n", "        'Bucket': bucket_name,\n", "        'Key': old_key\n", "    }\n", "    s3.copy(copy_source, bucket_name, new_key)\n", "\n", "def resolve(s3_url):\n", "    try:\n", "        orig_filename, filetype, s3_bucket = parse_s3_presigned_url(content_source_copy)\n", "    except KeyError as e:\n", "        print(f\"error: {e} for {content_source_copy}\")\n", "        \n", "        # Parse the URL\n", "        parsed_url = urlparse(content_source_copy)\n", "    \n", "        # Extract query parameters\n", "        query_params = parse_qs(parsed_url.query)\n", "    \n", "        # Get the file name and S3 bucket name from the query parameters\n", "        orig_filename = query_params[\"file\"][0]\n", "        filetype = query_params[\"fileType\"][0]\n", "        s3_bucket = \"tofu-uploaded-files\"\n", "\n", "    filename = orig_filename\n", "\n", "    if len(filename) >= 40:\n", "        print(filename[36:])\n", "        filename = filename[:36]\n", "\n", "    if filetype == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n", "        filename += '.docx'\n", "    elif filetype == 'application/vnd.openxmlformats-officedocument.presentationml.presentation':\n", "        filename += '.pptx'\n", "    elif filetype == 'application/pdf':\n", "        if filename[-4:] != '.pdf':\n", "            filename += '.pdf'\n", "    elif filetype == 'application/json':\n", "        filename += '.json'\n", "\n", "    if filename != orig_filename:\n", "        rename_s3_object(s3_bucket, orig_filename, filename)\n", "            \n", "    return f\"/api/web/storage/s3-presigned-url?file={filename}&fileType={filetype}&directory={s3_bucket}\"\n", "    \n", "for content_group in ContentGroup.objects.all():\n", "    if not content_group.content_group_params:\n", "        continue\n", "    content_source_copy = content_group.content_group_params.get(\n", "        \"content_source_copy\", \"\"\n", "    )\n", "    if not content_source_copy:\n", "        continue\n", "    updated_url = resolve(content_source_copy)\n", "    if content_source_copy != updated_url:\n", "        print(f\"before: {content_source_copy}\")\n", "        print(f\"after {updated_url}\")\n", "        try:\n", "            checker = PlaybookHealthChecker(None)\n", "            checker._check_link(updated_url, 'content_source_copy', str(content_group))\n", "            if checker.errors:\n", "                logging.error(f\"failed for case: {str(content_group)} {content_source_copy} with new url {updated_url} with errors:\")\n", "                for k, v in checker.errors.items():\n", "                    for v0 in v:\n", "                        print(v0)\n", "                break\n", "            else:\n", "                content_group.content_group_params[\"content_source_copy\"] = updated_url\n", "                content_group.save()\n", "                logging.error(f\"updated success: {content_source_copy} for {str(content_group)}\")\n", "        except Exception as e:\n", "            logging.error(f\"fail to load: {updated_url} due to {e}\")\n", "            break\n", "            "]}, {"cell_type": "code", "execution_count": null, "id": "3c58c21b-b36a-4cc9-b281-af9e35ce2178", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "import logging\n", "import json\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "import traceback\n", "\n", "from api.models import Campaign, Content, ContentGroup, Playbook\n", "from api.playbook_build.playbook_builder import PlaybookBuilder\n", "\n", "from api.utils import parse_s3_presigned_url, get_s3_file\n", "from urllib.parse import parse_qs, urlparse\n", "import boto3\n", "from api.playbook_health import PlaybookHealthChecker\n", "\n", "def rename_s3_object(bucket_name, old_key, new_key):\n", "    s3 = boto3.client('s3')\n", "    \n", "    # Copy the object to the new key\n", "    copy_source = {\n", "        'Bucket': bucket_name,\n", "        'Key': old_key\n", "    }\n", "    s3.copy(copy_source, bucket_name, new_key)\n", "\n", "def resolve(s3_url):\n", "    try:\n", "        orig_filename, filetype, s3_bucket = parse_s3_presigned_url(s3_url)\n", "    except KeyError as e:\n", "        print(f\"error: {e} for {s3_url}\")\n", "        \n", "        # Parse the URL\n", "        parsed_url = urlparse(s3_url)\n", "    \n", "        # Extract query parameters\n", "        query_params = parse_qs(parsed_url.query)\n", "    \n", "        # Get the file name and S3 bucket name from the query parameters\n", "        orig_filename = query_params[\"file\"][0]\n", "        filetype = query_params[\"fileType\"][0]\n", "        s3_bucket = \"tofu-uploaded-files\"\n", "\n", "    filename = orig_filename\n", "\n", "    if filename.find('%20') != -1 or filename.find(' ') != -1:\n", "        filename = filename[:36]\n", "\n", "    if filetype == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' and not filename.endswith('.docx'):\n", "        filename += '.docx'\n", "    elif filetype == 'application/vnd.openxmlformats-officedocument.presentationml.presentation' and not filename.endswith('.pptx'):\n", "        filename += '.pptx'\n", "\n", "\n", "    if filename != orig_filename:\n", "        logging.error(f\"filename before and after: {orig_filename} vs {filename}\")\n", "        rename_s3_object(s3_bucket, orig_filename, filename)\n", "            \n", "    return f\"/api/web/storage/s3-presigned-url?file={filename}&fileType={filetype}&directory={s3_bucket}\", filename\n", "\n", "\n", "for playbook in Playbook.objects.all():\n", "    playbook_builder = PlaybookBuilder(playbook)\n", "    all_objects = playbook_builder.get_all_objects()\n", "\n", "    for obj in all_objects:\n", "        has_obj_change = False\n", "\n", "        prev_data = f\"{obj.docs}\"\n", "        \n", "        for k, v in obj.docs.items():\n", "            try:\n", "                if v.get(\"type\") == \"url\":\n", "                    pass\n", "                elif v.get(\"type\") == \"file\":\n", "                    file_value = v.get(\"value\", {})\n", "                    if not file_value or \"s3_filename\" not in file_value:\n", "                        # TODO: tackle the failure that value is empty for file\n", "                        continue\n", "\n", "                    key = v.get(\"value\", {}).get(\"s3_filename\", \"\")\n", "                    if key:\n", "                        presigned = v.get(\"value\", {}).get(\"s3_presigned_path\", \"\")\n", "                        filename = key\n", "\n", "                        updated_url, updated_filename = resolve(presigned)\n", "                        if presigned != updated_url:\n", "                            print(f\"before: {presigned} with name: {filename}\")\n", "                            print(f\"after: {updated_url} with name: {updated_filename}\")\n", "\n", "                            has_obj_change = True\n", "                            \n", "                            # update\n", "                            obj.docs[k][\"value\"].update({\n", "                                \"s3_presigned_path\": updated_url,\n", "                                \"s3_filename\": updated_filename,\n", "                            })\n", "                    else:\n", "                        logging.error(f\"failed to get s3_filename: {v}\")\n", "            except Exception as e:\n", "                logging.error(\n", "                    f\"failed to get file link for object {str(obj)}: {v} with error: {e}\\n{traceback.format_exc()}\"\n", "                )\n", "        if has_obj_change:\n", "            obj.save()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "28e6a8ab-a780-4602-a61a-9de95ddc4e7f", "metadata": {}, "outputs": [], "source": ["def rename_s3_object(bucket_name, old_key, new_key):\n", "    s3 = boto3.client('s3')\n", "    \n", "    # Copy the object to the new key\n", "    copy_source = {\n", "        'Bucket': bucket_name,\n", "        'Key': old_key\n", "    }\n", "    s3.copy(copy_source, bucket_name, new_key)\n", "\n", "rename_s3_object(\"tofu-uploaded-files\", \"891d6f1d-f589-400b-bb02-63faf8a349f7\", \"891d6f1d-f589-400b-bb02-63faf8a349f7.html\")"]}, {"cell_type": "code", "execution_count": null, "id": "a23b7841-ac4f-4df8-a081-acf41d4fdd36", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}