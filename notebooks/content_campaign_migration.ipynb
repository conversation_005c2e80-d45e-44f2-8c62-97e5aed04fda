{"cells": [{"cell_type": "code", "execution_count": null, "id": "07100b19-3128-4533-bcd4-08499b43e995", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.abspath(\"__file__\"))\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + \"/server\")\n", "\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"server.settings\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "# End of django setup block\n", "\n", "from api.models import Playbook, Content, Campaign, ContentVariation, ContentGroup\n", "from api.utils import iter_campaign_target_dict\n", "from api.content_group import ContentGroupHandler\n", "import copy\n", "import uuid\n", "\n", "from api.views import CampaignViewSet\n", "from django.http import HttpRequest\n", "\n", "failures = {}\n", "\n", "\n", "def call_gen_status(campaign):\n", "    fake_request = HttpRequest()\n", "    args = ()  # Positional URL arguments\n", "    kwargs = {\"campaign_id\": campaign.id}  # Keyword URL arguments\n", "\n", "    campaign_view = CampaignViewSet()\n", "    campaign_view.setup(fake_request, *args, **kwargs)\n", "    response = campaign_view.gen_status(fake_request, *args, **kwargs)\n", "\n", "\n", "def migrate_content(content):\n", "    campaign_params = {\n", "        \"targets\": [copy.deepcopy(target_params)],\n", "        \"assets\": copy.deepcopy(content.content_params.get(\"assets\", {})),\n", "        \"custom_instructions\": copy.deepcopy(\n", "            content.content_params.get(\"custom_instructions\", [])\n", "        ),\n", "        \"foundation_model\": content.content_params.get(\"foundation_model\", \"\"),\n", "        \"prompt_overrides\": copy.deepcopy(\n", "            content.content_params.get(\"prompt_overrides\", {})\n", "        ),\n", "        \"num_of_variations\": content.content_params.get(\"num_of_variations\", 3),\n", "        \"campaign_goal\": \"Personalization\",\n", "        \"campaign_stage\": \"Export\",\n", "        \"gen_status\": {\n", "            \"status\": \"NOT_STARTED\",\n", "            \"content_groups\": {},\n", "        },\n", "    }\n", "\n", "    # migration part\n", "    # step 1: create campaign\n", "    # Create new object\n", "    new_campaign = Campaign.objects.create(\n", "        creator=content.creator,\n", "        playbook=content.playbook,\n", "        campaign_name=f\"[Migrate]{content.content_name} from content {content.id}\",\n", "        campaign_params=campaign_params,\n", "    )\n", "    print(f\"new_campaign:{new_campaign.id}\")\n", "\n", "    try:\n", "        content_group_params = {\n", "            \"content_type\": content.content_params.get(\"content_type\", \"\"),\n", "            \"content_source\": content.content_params.get(\"content_source\", \"\"),\n", "            \"content_source_copy\": content.content_params.get(\n", "                \"content_source_copy\", \"\"\n", "            ),\n", "            \"content_source_format\": content.content_params.get(\n", "                \"content_source_format\", \"\"\n", "            ),\n", "            \"content_source_upload_method\": content.content_params.get(\n", "                \"content_source_upload_method\", \"\"\n", "            ),\n", "            \"custom_instructions\": [],\n", "            \"assets\": {},\n", "            \"prompt_overrides\": {},\n", "            \"gen_status\": {\"status\": \"NOT_STARTED\", \"contents\": {}},\n", "        }\n", "        new_content_group = ContentGroup.objects.create(\n", "            creator=content.creator,\n", "            campaign=new_campaign,\n", "            content_group_name=content.content_name,\n", "            content_group_params=content_group_params,\n", "            components=copy.deepcopy(content.components),\n", "        )\n", "        print(f\"new_content_group:{new_content_group.id}\")\n", "        content_group_handler = ContentGroupHandler(new_content_group)\n", "        content_group_handler.bulk_create_content()\n", "\n", "        old_variations = ContentVariation.objects.filter(content=content)\n", "        if old_variations:\n", "            for old_variation in old_variations:\n", "                matched = False\n", "                for new_content in Content.objects.filter(\n", "                    content_group=new_content_group\n", "                ):\n", "                    if (\n", "                        new_content.content_params[\"targets\"]\n", "                        == old_variation.params[\"targets\"]\n", "                    ):\n", "                        new_variation = ContentVariation.objects.create(\n", "                            content=new_content,\n", "                            params=copy.deepcopy(old_variation.params),\n", "                            variations=copy.deepcopy(old_variation.variations),\n", "                        )\n", "                        for component_id in new_variation.variations.keys():\n", "                            new_variation.variations[component_id][\"meta\"][\n", "                                \"current_version\"\n", "                            ] = {\n", "                                \"text\": new_variation.variations[component_id][\"meta\"][\n", "                                    \"variations\"\n", "                                ][0][\"text\"]\n", "                            }\n", "                        new_variation.save()\n", "\n", "                        new_content.content_params[\"gen_status\"][\"status\"] = \"FINISHED\"\n", "                        new_content.save()\n", "\n", "                        # find a match\n", "                        matched = True\n", "                        break\n", "                if not matched:\n", "                    print(\n", "                        f\"not found matched old variation for {old_variation.id} in content group {new_content_group.id}\"\n", "                    )\n", "                    print(old_variation.params[\"targets\"])\n", "                    for new_content in Content.objects.filter(\n", "                        content_group=new_content_group\n", "                    ):\n", "                        print(new_content.content_params[\"targets\"])\n", "                    raise Exception(\"unmatch here\")\n", "    except Exception as e:\n", "        print(\n", "            f\"Error: failed to covert from content {content.id} to {new_campaign.id} for {str(e)}\"\n", "        )\n", "        failures[content.id] = (new_campaign.id, str(e))\n", "    content.content_name = (\n", "        f\"[DEPRECATED]{content.content_name} to campaign {new_campaign.id}\"\n", "    )\n", "    content.save()\n", "    call_gen_status(new_campaign)\n", "\n", "    # content.delete()\n", "    print(f\"content {content.id} migrated to {new_campaign.id}\")\n", "    print(\"-\" * 20)\n", "\n", "\n", "all_contents = Content.objects.all()\n", "cnt_target_violation = 0\n", "cnt_migrated = 0\n", "for content in all_contents:\n", "    if content.content_group:\n", "        continue\n", "    if content.content_name.startswith(\"[DEPRECATED]\"):\n", "        # already migrated\n", "        continue\n", "    if content.id in [1607, 1651, 1003]:\n", "        continue\n", "\n", "    # number of targets\n", "    target_params = content.content_params.get(\"targets\", {})\n", "    if any(\n", "        isinstance(value, list) and len(value) > 1 for value in target_params.values()\n", "    ):\n", "        cnt_target_violation += 1\n", "    else:\n", "        continue\n", "\n", "    # verify target_params\n", "    all_targets = list(iter_campaign_target_dict(target_params))\n", "    if len(all_targets) <= 1:\n", "        raise Exception(\n", "            f\"Target checks inconsistent: {target_params} and {all_targets}\"\n", "        )\n", "        continue\n", "    for value in target_params.values():\n", "        if not isinstance(value, list):\n", "            raise Exception(f\"Unexpected target: {target_params}\")\n", "\n", "    cnt_migrated += 1\n", "    # TODO: remove this break when migration\n", "    # TODO: remove user check for gen_status query\n", "    # migrate_content(content)\n", "\n", "print(f\"Total {cnt_target_violation} contents have target violation\")\n", "print(f\"Total {cnt_migrated} contents migrated\")\n", "print(failures)"]}, {"cell_type": "code", "execution_count": null, "id": "66fcf52d-3cd2-4591-8813-4314a7d739b6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}