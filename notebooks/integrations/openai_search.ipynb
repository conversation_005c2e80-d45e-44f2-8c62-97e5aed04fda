{"cells": [{"cell_type": "code", "execution_count": 4, "id": "109a574f-d0d8-459d-9da0-6b8b95acb025", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feb 11, 2022 ... They archive results and environment in way that is not meant to be maintained. You get a new notebook. You work with the data. You develop ... Mar 1, 2023 ... Please let me know if this fix causes any other issue. ... try adding the prompt saying that the final answer must give in markdown format. May 8, 2024 ... I know there are some packages out there that claim to do the job, but I can't seem to get good results from it. Moreover, my work laptop kinda ... Sep 16, 2010 ... ... financial sense. Even if you round off your results at the last minute before output, you can still occasionally get a result using doubles ... Oct 11, 2020 ... Please listen to the issues above - these are genuine reports! ... Which to me looked like the latest build (Desert Sunflower)? (please let ... Before submitting your manuscript for peer review, you are kindly requested to do the following: to download the Copernicus manuscript templates for LaTeX ... Jun 1, 2023 ... I want to build a custom chat bot which can answer questions based on the data in my databse. Below are my tries and the problems I am facing. Apr 23, 2023 ... I want to make a small disclaimer before you start reading — I am not a finance guru. Instead, I am a tech guy who is curious to try out new ... Apr 7, 2023 ... Start a new chat frequently. I often see ChatGPT losing its train of thought during a long string of prompts. I've had better results when I ... To make your site eligible for appearance as one of these rich results ... course info rich result in search results, Education and Science. Course list.\n"]}], "source": ["import os\n", "from langchain.chains import LLMChain\n", "from langchain.llms import OpenAI\n", "from langchain.tools import Tool\n", "from langchain.prompts import PromptTemplate\n", "# from langchain.document_loaders import GoogleSearchAPI\n", "\n", "from langchain_google_community import GoogleSearchAPIWrapper\n", "\n", "\n", "OPENAI_API_KEY=\"***************************************************\"\n", "GOOGLE_API_KEY=\"AIzaSyCwHIIJcycnRUT6LPKJOwqw-rRlbZ-g4Jc\"\n", "\n", "search = GoogleSearchAPIWrapper(google_api_key=GOOGLE_API_KEY, google_cse_id=\"8544584a7f79b4460\")\n", "\n", "# 1. Configure OpenAI model\n", "openai_model = OpenAI(model=\"gpt-4\", temperature=0.7, openai_api_key=OPENAI_API_KEY)\n", "\n", "# 3. Define a prompt to guide the OpenAI model on how to respond with search results\n", "prompt = PromptTemplate(\n", "    input_variables=[\"query\"],\n", "    template=\"Use the search results provided to answer this question: {query}\"\n", ")\n", "\n", "search_tool = Tool(\n", "    name=\"google_search\",\n", "    description=\"Search Google for recent results.\",\n", "    func=search.run,\n", ")\n", "\n", "result = search_tool.run(\"Latest financial report from Google and please make the result in markdown format\")\n", "\n", "# # 6. Define the LangChain chain with OpenAI model and search tool\n", "# search_chain = LLMChain(\n", "#     llm=openai_model,\n", "#     prompt=prompt,\n", "#     tools=[search_tool],\n", "#     verbose=True\n", "# )\n", "\n", "# # 7. Run a query through the chain\n", "# query = \"What are the latest advancements in AI language models?\"\n", "# response = search_chain.run(query=query)\n", "\n", "# print(\"Response:\", response)\n", "print(result)"]}, {"cell_type": "code", "execution_count": 3, "id": "d2e59175-9f3c-4caa-bbb2-6f8562efa4e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["type(result)"]}, {"cell_type": "code", "execution_count": null, "id": "e6240243-8bcb-44d3-a513-1675e5342cf3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}