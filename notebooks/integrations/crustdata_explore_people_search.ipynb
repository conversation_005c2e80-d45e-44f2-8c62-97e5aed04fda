{"cells": [{"cell_type": "code", "execution_count": 1, "id": "02362254-7333-47a7-9c4d-338ac679c6ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching page 1...\n", "Found 25 profiles on page 1\n", "Fetching page 2...\n", "Found 25 profiles on page 2\n", "Fetching page 3...\n", "Found 25 profiles on page 3\n", "Fetching page 4...\n", "Found 25 profiles on page 4\n", "Fetching page 5...\n", "Found 25 profiles on page 5\n", "Fetching page 6...\n", "Found 25 profiles on page 6\n", "Fetching page 7...\n", "Found 25 profiles on page 7\n", "Fetching page 8...\n", "Found 25 profiles on page 8\n", "Fetching page 9...\n", "Found 25 profiles on page 9\n", "Fetching page 10...\n", "Found 25 profiles on page 10\n", "Total profiles collected: 250\n"]}], "source": ["import requests\n", "import pandas as pd\n", "import json\n", "\n", "token = \"3cf1d239ff1044cbc582e808674ad4620d131348\"\n", "\n", "# Initialize a list to store all profiles\n", "all_profiles = []\n", "\n", "# Iterate through 10 pages\n", "for page in range(1, 11):\n", "    print(f\"Fetching page {page}...\")\n", "    response = requests.post(\n", "        \"https://api.crustdata.com/screener/person/search\",\n", "        headers={\n", "          \"Content-Type\": \"application/json\",\n", "            \"Authorization\": f\"Token {token}\",\n", "        },\n", "        json={\n", "          \"filters\": [\n", "            {\n", "                \"filter_type\": \"CURRENT_TITLE\",\n", "                \"type\": \"in\",\n", "                \"value\": [\"COO\", \"CIO\"]\n", "            },\n", "        {\n", "          \"filter_type\": \"COMPANY_HEADCOUNT\",\n", "          \"type\": \"in\",\n", "          \"value\": [\"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n", "        },\n", "            {\n", "              \"filter_type\": \"INDUSTRY\",\n", "              \"type\": \"in\",\n", "              \"value\": [\n", "                      \"Hospitals and Health Care\",\n", "                      \"Home Health Care Services\",\n", "                      \"Biotechnology Research\",\n", "                      \"Medical and Diagnostic Laboratories\",\n", "              ]\n", "            },\n", "              \n", "            {\n", "              \"filter_type\": \"REGION\",\n", "              \"type\": \"in\",\n", "              \"value\": [\"United States\", \"United Kingdom\",]\n", "            },\n", "          ],\n", "          \"page\": page\n", "        }\n", "    )\n", "    \n", "    # Parse the response\n", "    data = response.json()\n", "    \n", "    # Check if 'profiles' exists in the response\n", "    if 'profiles' in data:\n", "        profiles = data['profiles']\n", "        all_profiles.extend(profiles)\n", "        print(f\"Found {len(profiles)} profiles on page {page}\")\n", "    else:\n", "        print(f\"No profiles found on page {page} or unexpected response format\")\n", "        break\n", "\n", "print(f\"Total profiles collected: {len(all_profiles)}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "1e7e58d6-f184-486a-b0a1-2898dbde8fc0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>linkedin_profile_url</th>\n", "      <th>default_position_title</th>\n", "      <th>default_position_company_linkedin_id</th>\n", "      <th>default_position_is_decision_maker</th>\n", "      <th>flagship_profile_url</th>\n", "      <th>headline</th>\n", "      <th>summary</th>\n", "      <th>current_employer</th>\n", "      <th>emails</th>\n", "      <th>current_title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON>, MHA</td>\n", "      <td>https://www.linkedin.com/in/ACwAABxXQiQBOwAD7H...</td>\n", "      <td>VP/COO</td>\n", "      <td>858214</td>\n", "      <td>True</td>\n", "      <td>https://www.linkedin.com/in/stacey-pyndell-mha...</td>\n", "      <td>VP/COO</td>\n", "      <td>None</td>\n", "      <td>AdventHealth</td>\n", "      <td></td>\n", "      <td>VP/COO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON></td>\n", "      <td>https://www.linkedin.com/in/ACwAABgQ1sgBCemzRI...</td>\n", "      <td>Chief Information Security Officer</td>\n", "      <td>227143</td>\n", "      <td>False</td>\n", "      <td>https://www.linkedin.com/in/kathy-lanceley-634...</td>\n", "      <td><PERSON><PERSON><PERSON> | Deputy SIRO |  Imperial College Healthc...</td>\n", "      <td>Currently working on the drafting the Cyber Se...</td>\n", "      <td>Imperial College Healthcare NHS Trust</td>\n", "      <td></td>\n", "      <td>Joint IT Director</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON> <PERSON></td>\n", "      <td>https://www.linkedin.com/in/ACwAAAL04cIB-_IrJA...</td>\n", "      <td>Vice President COO</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>https://www.linkedin.com/in/roget-de-percin-be...</td>\n", "      <td>Vice President at FirstLight Home Care of Guil...</td>\n", "      <td>Experienced Vice President with a demonstrated...</td>\n", "      <td>Firstlight Home Care of Jacksonville</td>\n", "      <td></td>\n", "      <td>Vice President COO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON>, MBA, CHCIO, PMP</td>\n", "      <td>https://www.linkedin.com/in/ACwAAAEwSG0Bhi3VQj...</td>\n", "      <td>Vice President and CIO, Acute Care Hospitals</td>\n", "      <td>52488</td>\n", "      <td>True</td>\n", "      <td>https://www.linkedin.com/in/m-scott-rester-mba...</td>\n", "      <td>Vice President &amp; C<PERSON>, Acute Care Hospitals at ...</td>\n", "      <td><PERSON> serves as Vice President and CIO ...</td>\n", "      <td>LifePoint Health®</td>\n", "      <td></td>\n", "      <td>Vice President and CIO, Acute Care Hospitals</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON> MS, CHCIO, CDH-E, FCHIME, LCHIME</td>\n", "      <td>https://www.linkedin.com/in/ACwAAABKmEIBhOZf-r...</td>\n", "      <td>Interim <PERSON></td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>https://www.linkedin.com/in/richpollack</td>\n", "      <td>semi-retired highly experienced healthcare CIO...</td>\n", "      <td>A results focused C-Level Executive, drawing u...</td>\n", "      <td>Sturdy Memorial Health System</td>\n", "      <td></td>\n", "      <td>Fellow</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            name  \\\n", "0                            <PERSON>, MHA   \n", "1                                 <PERSON>   \n", "2                       <PERSON><PERSON><PERSON> <PERSON>   \n", "3               <PERSON><PERSON>, MBA, CHCIO, PMP   \n", "4  <PERSON>ack MS, CHCIO, CDH-E, FCHIME, LCHIME   \n", "\n", "                                linkedin_profile_url  \\\n", "0  https://www.linkedin.com/in/ACwAABxXQiQBOwAD7H...   \n", "1  https://www.linkedin.com/in/ACwAABgQ1sgBCemzRI...   \n", "2  https://www.linkedin.com/in/ACwAAAL04cIB-_IrJA...   \n", "3  https://www.linkedin.com/in/ACwAAAEwSG0Bhi3VQj...   \n", "4  https://www.linkedin.com/in/ACwAAABKmEIBhOZf-r...   \n", "\n", "                         default_position_title  \\\n", "0                                        VP/COO   \n", "1            Chief Information Security Officer   \n", "2                            Vice President COO   \n", "3  Vice President and CIO, Acute Care Hospitals   \n", "4                                   Interim CIO   \n", "\n", "  default_position_company_linkedin_id  default_position_is_decision_maker  \\\n", "0                               858214                                True   \n", "1                               227143                               False   \n", "2                                 None                                True   \n", "3                                52488                                True   \n", "4                                 None                               False   \n", "\n", "                                flagship_profile_url  \\\n", "0  https://www.linkedin.com/in/stacey-pyndell-mha...   \n", "1  https://www.linkedin.com/in/kathy-lanceley-634...   \n", "2  https://www.linkedin.com/in/roget-de-percin-be...   \n", "3  https://www.linkedin.com/in/m-scott-rester-mba...   \n", "4            https://www.linkedin.com/in/richpollack   \n", "\n", "                                            headline  \\\n", "0                                             VP/COO   \n", "1  C<PERSON><PERSON> | Deputy SIRO |  Imperial College Healthc...   \n", "2  Vice President at FirstLight Home Care of Guil...   \n", "3  Vice President & CIO, Acute Care Hospitals at ...   \n", "4  semi-retired highly experienced healthcare CIO...   \n", "\n", "                                             summary  \\\n", "0                                               None   \n", "1  Currently working on the drafting the Cyber Se...   \n", "2  Experienced Vice President with a demonstrated...   \n", "3  <PERSON> serves as Vice President and CIO ...   \n", "4  A results focused C-Level Executive, drawing u...   \n", "\n", "                        current_employer emails  \\\n", "0                           AdventHealth          \n", "1  Imperial College Healthcare NHS Trust          \n", "2   Firstlight Home Care of Jacksonville          \n", "3                      LifePoint Health®          \n", "4          Sturdy Memorial Health System          \n", "\n", "                                  current_title  \n", "0                                        VP/COO  \n", "1                            Joint IT Director   \n", "2                            Vice President COO  \n", "3  Vice President and CIO, Acute Care Hospitals  \n", "4                                        Fellow  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extract required fields from each profile\n", "extracted_data = []\n", "\n", "for profile in all_profiles:\n", "    # Find current employer (where end_date is null in employer list)\n", "    current_employer = None\n", "    if 'employer' in profile:\n", "        for emp in profile['employer']:\n", "            if emp.get('end_date') is None:\n", "                current_employer = emp.get('company_name')\n", "                break\n", "    \n", "    # Extract all the required fields\n", "    profile_data = {\n", "        'name': profile.get('name'),\n", "        'linkedin_profile_url': profile.get('linkedin_profile_url'),\n", "        'default_position_title': profile.get('default_position_title'),\n", "        'default_position_company_linkedin_id': profile.get('default_position_company_linkedin_id'),\n", "        'default_position_is_decision_maker': profile.get('default_position_is_decision_maker'),\n", "        'flagship_profile_url': profile.get('flagship_profile_url'),\n", "        'headline': profile.get('headline'),\n", "        'summary': profile.get('summary'),\n", "        'current_employer': current_employer,\n", "        'emails': ', '.join(profile.get('emails', [])),\n", "        'current_title': profile.get('current_title')\n", "    }\n", "    \n", "    extracted_data.append(profile_data)\n", "\n", "# Create a DataFrame from the extracted data\n", "df = pd.DataFrame(extracted_data)\n", "\n", "# Display the first few rows\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "927ce54d-b73a-41c8-a443-cb8675e4842f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to healthcare_executives.csv\n"]}], "source": ["# Save to CSV\n", "output_file = \"healthcare_executives.csv\"\n", "df.to_csv(output_file, index=False)\n", "print(f\"Data saved to {output_file}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b432025b-b671-49b8-9843-37f8a21eda85", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}