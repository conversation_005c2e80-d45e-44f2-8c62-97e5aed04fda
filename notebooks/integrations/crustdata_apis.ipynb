{"cells": [{"cell_type": "code", "execution_count": null, "id": "60a65a3a-1da5-425b-849c-dbf5b8f1bba8", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/person/enrich?linkedin_profile_url=https://www.linkedin.com/in/diana-jovin-8b02/&enrich_realtime=True' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "88d75e02-4d47-4b06-b362-35f1717913fb", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/person/enrich?linkedin_profile_url=https://www.linkedin.com/in/richpollack' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "75d14fde-0068-4aa1-93a8-4af62bea4cdb", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/company?company_name=TofuHQ,Reprise,warmly' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "a26ff84d-05de-483c-b3e0-987b7a16003b", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/company?company_name=TofuHQ' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "88b5a34c-cc31-49d5-a649-364eca4400f2", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/company?company_domain=tofuhq.com' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "d5b5f1d0-3e90-4631-9293-eeec7e5e0601", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/company?company_linkedin_url=https://www.linkedin.com/company/tofuai' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "cc1b917d-5b50-4bcd-87ee-a56089e0c412", "metadata": {}, "outputs": [], "source": ["!curl --location 'https://api.crustdata.com/screener/person/search' \\\n", "--header 'Content-Type: application/json' \\\n", "--header 'Accept: application/json, text/plain, */*' \\\n", "--header 'Accept-Language: en-US,en;q=0.9' \\\n", "--header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348' \\\n", "--data '{\n", "    \"linkedin_sales_navigator_search_url\": \"https://www.linkedin.com/search/results/people/?geoUrn=%5B%22*********%22%5D&industry=%5B%221594%22%5D&keywords=cmo&origin=FACETED_SEARCH&pastCompany=%5B%221441%22%5D&schoolFilter=%5B%224867%22%2C%221792%22%5D&searchId=df81483c-5209-4c8d-bfb4-f90f232bf1ae&sid=Aa~\"\n", "}'"]}, {"cell_type": "code", "execution_count": null, "id": "477c9417-30fb-44d5-8aca-f3bb9be189da", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "url = 'https://api.crustdata.com/screener/person/search'\n", "\n", "headers = {\n", "    'Content-Type': 'application/json',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'Accept-Language': 'en-US,en;q=0.9',\n", "    'Authorization': 'Token 3cf1d239ff1044cbc582e808674ad4620d131348'  # Replace $token with your actual token\n", "}\n", "\n", "data = {\n", "    \"linkedin_sales_navigator_search_url\": \"https://www.linkedin.com/sales/search/people?query=(filters:List((type:FIRST_NAME,values:List((text:Shari,selectionType:INCLUDED))),(type:LAST_NAME,values:List((text:<PERSON>,selectionType:INCLUDED))),(type:COMPANY,values:List((text:Winning%20by%20Design,selectionType:INCLUDED)))))\"\n", "}\n", "\n", "response = requests.post(url, headers=headers, data=json.dumps(data))\n", "\n", "print(response.status_code)\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": null, "id": "6ffac1f6-d44d-4f46-8b9a-f93995b9724d", "metadata": {}, "outputs": [], "source": ["!curl --location 'https://api.crustdata.com/screener/person/search' \\\n", "--header 'Content-Type: application/json' \\\n", "--header 'Accept: application/json, text/plain, */*' \\\n", "--header 'Accept-Language: en-US,en;q=0.9' \\\n", "--header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348' \\\n", "--data '{\n", "    \"linkedin_sales_navigator_search_url\": \"https://www.linkedin.com/sales/search/people?query=(recentSearchParam%3A(id%3A4000273340%2CdoLogHistory%3Atrue)%2Cfilters%3AList((type%3AFUNCTION%2Cvalues%3AList((id%3A15%2Ctext%3AMarketing%2CselectionType%3AINCLUDED)))%2C(type%3ACOMPANY_HEADCOUNT%2Cvalues%3AList((id%3AE%2Ctext%3A201-500%2CselectionType%3AINCLUDED)%2C(id%3AF%2Ctext%3A501-1000%2CselectionType%3AINCLUDED)%2C(id%3AG%2Ctext%3A1001-5000%2CselectionType%3AINCLUDED)))%2C(type%3ACURRENT_TITLE%2Cvalues%3AList((id%3A26584%2Ctext%3AHead%2520of%2520Demand%2520Generation%2CselectionType%3AINCLUDED)%2C(text%3AVP%2520demand%2520generation%2CselectionType%3AINCLUDED)%2C(text%3ADirector%2520of%2520demand%2520generation%2CselectionType%3AINCLUDED)))%2C(type%3ASENIORITY_LEVEL%2Cvalues%3AList((id%3A300%2Ctext%3AVice%2520President%2CselectionType%3AINCLUDED)%2C(id%3A220%2Ctext%3ADirector%2CselectionType%3AINCLUDED)))))&sessionId=xZGlkn1wSae2aTU60h9row%3D%3D\"\n", "}'"]}, {"cell_type": "code", "execution_count": null, "id": "643ba71a-62fb-4807-adab-c659cd9d7694", "metadata": {}, "outputs": [], "source": ["linkedin_list = [\n", "    \"https://www.linkedin.com/in/tomasztunguz/\",\n", "\"https://www.linkedin.com/in/amy-bolton-91877a11\",\n", "\"https://www.linkedin.com/in/michael-maday\",\n", "\"https://www.linkedin.com/in/thomas-sullivan-83552780\",\n", "\"https://www.linkedin.com/in/markledgerprofile\",\n", "\"https://www.linkedin.com/in/colleen-ennis\",\n", "\"https://www.linkedin.com/in/ryanhelft\",\n", "\"https://www.linkedin.com/in/tristramgillen\",\n", "\"https://www.linkedin.com/in/michelle-darnall-halushka-39243623\",\n", "\"https://www.linkedin.com/in/justintklim\",\n", "\"https://www.linkedin.com/in/eric-bonnette/\",\n", "\"https://www.linkedin.com/in/salvatore-coniglio-5007a065/\",\n", "\"https://www.linkedin.com/in/mitchell-friedlander-b3964112\",\n", "\"https://www.linkedin.com/in/robert-mechem-107b8747/\",\n", "\"https://www.linkedin.com/in/ricardo-campo-0a66462/\",\n", "\"https://www.linkedin.com/in/benoitmangeard\",\n", "\"https://www.linkedin.com/in/ACwAAAQSrvcBGs8hgGye0nAceJbI8AvbFS4rwKw\",\n", "\"https://www.linkedin.com/in/ACwAAAOjkpkBnK8t9z-u9rnlV866KWfaTAdsL1E\",\n", "\"https://www.linkedin.com/in/ACwAAB2OeWsBELn1B3zasj6IN6ot8keN332Srhc\",\n", "\"https://www.linkedin.com/in/ACwAAA3HQT8B-GCUziHn2EzAkX3TTekRganEczc\",\n", "\"https://www.linkedin.com/in/ACwAAAI9AgABHAe9xNHuta4RANOQImaL0XIcEBw\",\n", "\"https://www.linkedin.com/in/mark-hebenstreit-146222123/\",\n", "\"https://www.linkedin.com/in/edward-<PERSON>-w<PERSON><PERSON>-36b04829/\",\n", "\"https://www.linkedin.com/in/sean-mcshane-09020651/\",\n", "\"https://www.linkedin.com/in/trent-mere-b54125146/\",\n", "\"https://www.linkedin.com/in/paul-sarzoza-47b7a92a/\",\n", "\"https://www.linkedin.com/in/ctgerard/\",\n", "\"https://www.linkedin.com/in/george-adams-0b626537/\",\n", "\"https://www.linkedin.com/in/david-workman-88109a14/\",\n", "\"https://www.linkedin.com/in/anirudh-sathya/\",\n", "\"https://www.linkedin.com/in/todd-williams-1658a11b/\",\n", "\"https://www.linkedin.com/in/thessalonian-leblanc-mba-8b84b731/\",\n", "\"https://www.linkedin.com/in/scott-jordan-4829327/\",\n", "\"https://www.linkedin.com/in/ryan<PERSON><PERSON>/\",\n", "\"https://www.linkedin.com/in/stephen-and<PERSON><PERSON>-b3987360/\",\n", "\"https://www.linkedin.com/in/scott-crennan-0a101512\",\n", "\"https://www.linkedin.com/in/scott-goldberg-759778106/\",\n", "\"https://www.linkedin.com/in/ctgerard/\",\n", "\"https://www.linkedin.com/in/george-adams-0b626537/\",\n", "\"https://www.linkedin.com/in/david-workman-88109a14/\",\n", "\"https://www.linkedin.com/in/trent-mere-b54125146/\",\n", "\"https://www.linkedin.com/in/sean-mcshane-09020651/\",\n", "\"https://www.linkedin.com/in/thessalonian-leblanc-mba-8b84b731/\",\n", "\"https://www.linkedin.com/in/scott-jordan-4829327/\",\n", "\"https://www.linkedin.com/in/stephen-and<PERSON><PERSON>-b3987360/\",\n", "\"https://www.linkedin.com/in/scott-crennan-0a101512\",\n", "\"https://www.linkedin.com/in/ryan<PERSON><PERSON>/\",\n", "\"https://www.linkedin.com/in/anirudh-sathya/\",\n", "\"https://www.linkedin.com/in/scott-goldberg-759778106/\",\n", "\"https://www.linkedin.com/in/kevin-mel<PERSON>-0a768321/\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0e227f4a-f5dd-4933-8be0-2f86758f222f", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "# Your list of LinkedIn profile links\n", "linkedin_profiles = linkedin_list[20:40]\n", "\n", "# Your API token\n", "token = \"3cf1d239ff1044cbc582e808674ad4620d131348\"\n", "\n", "# API endpoint\n", "url = \"https://api.crustdata.com/screener/person/enrich\"\n", "\n", "# Prepare the LinkedIn profile URLs as a comma-separated string\n", "linkedin_urls = \",\".join(linkedin_profiles)\n", "\n", "# Set up the headers\n", "headers = {\n", "    \"Accept\": \"application/json, text/plain, */*\",\n", "    \"Accept-Language\": \"en-US,en;q=0.9\",\n", "    \"Authorization\": f\"Token {token}\"\n", "}\n", "\n", "# Set up the parameters\n", "params = {\n", "    \"linkedin_profile_url\": linkedin_urls\n", "}\n", "\n", "# Make the API request\n", "response = requests.get(url, headers=headers, params=params)\n", "\n", "# Check if the request was successful\n", "if response.status_code == 200:\n", "    # Parse the JSON response\n", "    results = response.json()\n", "    \n", "    # Print the results as a list of JSONs\n", "    print(json.dumps(results, indent=2))\n", "else:\n", "    print(f\"Error: {response.status_code}\")\n", "    print(response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "7363aa3d-e9f3-42f1-8696-ea9c24f92c30", "metadata": {}, "outputs": [], "source": ["len(linkedin_list[20:40])"]}, {"cell_type": "code", "execution_count": null, "id": "816ad8d9-e29e-4ad5-8174-b7b1d940b75a", "metadata": {}, "outputs": [], "source": ["4 / (len(set(linkedin_list[:40])))"]}, {"cell_type": "code", "execution_count": null, "id": "5cf1c21e-9ba5-4bbc-9daf-7501725bad0f", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/linkedin_posts?company_domain=octo.com&page=1' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "5c2229e1-8c86-4982-ac71-c24eae850a32", "metadata": {}, "outputs": [], "source": ["import requests\n", "token = \"3cf1d239ff1044cbc582e808674ad4620d131348\"\n", "\n", "response = requests.post(\n", "    \"https://api.crustdata.com/screener/company/search\",\n", "    headers={\n", "      \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Token {token}\",\n", "    },\n", "    json={\n", "      \"filters\": [\n", "    {\n", "      \"filter_type\": \"COMPANY_HEADCOUNT\",\n", "      \"type\": \"in\",\n", "      \"value\": [\"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n", "    },\n", "    {\n", "      \"filter_type\": \"ANNUAL_REVENUE\",\n", "      \"type\": \"between\",\n", "      \"value\": { \"min\": 100, \"max\": 1000 },\n", "      \"sub_filter\": \"USD\"\n", "    },\n", "        {\n", "          \"filter_type\": \"INDUSTRY\",\n", "          \"type\": \"in\",\n", "          \"value\": [\n", "                  \"Hospitals and Health Care\",\n", "                  \"Home Health Care Services\",\n", "                  \"Biotechnology Research\",\n", "                  \"Medical and Diagnostic Laboratories\",\n", "          ]\n", "        },\n", "          \n", "        {\n", "          \"filter_type\": \"REGION\",\n", "          \"type\": \"in\",\n", "          \"value\": [\"United States\", \"United Kingdom\",]\n", "        }\n", "      ],\n", "      \"page\": 1\n", "    }\n", ")\n", "response.text"]}, {"cell_type": "code", "execution_count": null, "id": "8c43a1ea-4e1f-427a-8d35-ea272df5ae69", "metadata": {"scrolled": true}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/company?company_domain=gene.com&fields=decision_makers' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "cb8fc418-951a-45a4-86e4-1bc8d7db35af", "metadata": {}, "outputs": [], "source": ["import requests\n", "token = \"3cf1d239ff1044cbc582e808674ad4620d131348\"\n", "\n", "linkedin_query = \"https://www.linkedin.com/sales/search/company?query=healthcare&industryIncluded=4%2C8%2C124&geoIncluded=*********%2C101165590&companySize=G%2CH%2CI&annualRevenue=D%2CE&ownership=PRIVATE_EQUITY\"\n", "\n", "\n", "response = requests.post(\n", "    \"https://api.crustdata.com/screener/company/search\",\n", "    headers={\n", "      \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Token {token}\",\n", "    },\n", "    json={\n", "          \"linkedin_sales_navigator_search_url\": linkedin_query,\n", "    }\n", ")\n", "response.text"]}, {"cell_type": "code", "execution_count": null, "id": "3fbd5a0c-7ed6-410d-a02f-7bc44b6cd764", "metadata": {}, "outputs": [], "source": ["import requests\n", "token = \"3cf1d239ff1044cbc582e808674ad4620d131348\"\n", "\n", "response = requests.post(\n", "    \"https://api.crustdata.com/screener/company/search\",\n", "    headers={\n", "      \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Token {token}\",\n", "    },\n", "    json={\n", "      \"filters\": [\n", "    {\n", "      \"filter_type\": \"COMPANY_HEADCOUNT\",\n", "      \"type\": \"in\",\n", "      \"value\": [\"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n", "    },\n", "    {\n", "      \"filter_type\": \"ANNUAL_REVENUE\",\n", "      \"type\": \"between\",\n", "      \"value\": { \"min\": 100, \"max\": 1000 },\n", "      \"sub_filter\": \"USD\"\n", "    },\n", "        {\n", "          \"filter_type\": \"INDUSTRY\",\n", "          \"type\": \"in\",\n", "          \"value\": [\n", "                  \"Hospitals and Health Care\",\n", "                  \"Home Health Care Services\",\n", "                  \"Biotechnology Research\",\n", "                  \"Medical and Diagnostic Laboratories\",\n", "          ]\n", "        },\n", "          \n", "        {\n", "          \"filter_type\": \"REGION\",\n", "          \"type\": \"in\",\n", "          \"value\": [\"United States\", \"United Kingdom\",]\n", "        },\n", "          \n", "        {\n", "          \"filter_type\": \"KEYWORD\",\n", "          \"type\": \"in\",\n", "          \"value\": [\"Privately Held\"],\n", "          \n", "        },\n", "      ],\n", "      \"page\": 1\n", "    }\n", ")\n", "response.text"]}, {"cell_type": "code", "execution_count": null, "id": "405bdd68-9ac2-4e13-9152-c24e50ec435e", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/company?company_domain=chenmed.com&fields=decision_makers' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "e356da35-59e9-48dc-9b40-c2b96d45a72e", "metadata": {}, "outputs": [], "source": ["!curl 'https://api.crustdata.com/screener/screen/' \\\n", "-H 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348' \\\n", "-H 'Content-Type: application/json' \\\n", "--data-raw '{\n", "    \"filters\": {\n", "        \"op\": \"or\",\n", "        \"conditions\": [\n", "            {\"column\": \"company_website_domain\", \"type\": \"=\", \"value\": \"gene.com\", \"allow_null\": false}\n", "        ]\n", "    },\n", "    \"hidden_columns\": [],\n", "    \"offset\": 0,\n", "    \"count\": 100,\n", "    \"sorts\": []\n", "}' \\\n", "--compressed"]}, {"cell_type": "code", "execution_count": null, "id": "fb13e75b-9e13-4a01-8441-7d76d07fedfb", "metadata": {}, "outputs": [], "source": ["import requests\n", "token = \"3cf1d239ff1044cbc582e808674ad4620d131348\"\n", "\n", "response = requests.post(\n", "    \"https://api.crustdata.com/screener/person/search\",\n", "    headers={\n", "      \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Token {token}\",\n", "    },\n", "    json={\n", "      \"filters\": [\n", "        {\n", "            \"filter_type\": \"CURRENT_TITLE\",\n", "            \"type\": \"in\",\n", "            \"value\": [\"COO\", \"CIO\"]\n", "        },\n", "    {\n", "      \"filter_type\": \"COMPANY_HEADCOUNT\",\n", "      \"type\": \"in\",\n", "      \"value\": [\"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n", "    },\n", "    # {\n", "    #   \"filter_type\": \"ANNUAL_REVENUE\",\n", "    #   \"type\": \"between\",\n", "    #   \"value\": { \"min\": 100, \"max\": 1000 },\n", "    #   \"sub_filter\": \"USD\"\n", "    # },\n", "        {\n", "          \"filter_type\": \"INDUSTRY\",\n", "          \"type\": \"in\",\n", "          \"value\": [\n", "                  \"Hospitals and Health Care\",\n", "                  \"Home Health Care Services\",\n", "                  \"Biotechnology Research\",\n", "                  \"Medical and Diagnostic Laboratories\",\n", "          ]\n", "        },\n", "          \n", "        {\n", "          \"filter_type\": \"REGION\",\n", "          \"type\": \"in\",\n", "          \"value\": [\"United States\", \"United Kingdom\",]\n", "        },\n", "      ],\n", "      \"page\": 1\n", "    }\n", ")\n", "response.text"]}, {"cell_type": "code", "execution_count": null, "id": "d94d6434-8642-48b6-8510-ef30934afd33", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "url = 'https://api.crustdata.com/screener/screen/'\n", "headers = {\n", "    'Authorization': 'Token 3cf1d239ff1044cbc582e808674ad4620d131348',\n", "    'Content-Type': 'application/json'\n", "}\n", "data = {\n", "    \"filters\": {\n", "        \"op\": \"or\",\n", "        \"conditions\": [\n", "            {\"column\": \"company_website_domain\", \"type\": \"=\", \"value\": \"openai.com\", \"allow_null\": True}\n", "        ]\n", "    },\n", "    \"hidden_columns\": [],\n", "    \"offset\": 0,\n", "    \"count\": 100,\n", "    \"sorts\": []\n", "}\n", "\n", "response = requests.post(url, headers=headers, json=data)\n", "result = response.json()\n", "print(result)"]}, {"cell_type": "code", "execution_count": 1, "id": "5d4ac2e0-22d9-4c6e-8ecf-0f5f2e741fb7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{\"linkedin_profile_url\":\"https://www.linkedin.com/in/ACwAAACCaawBvzlB2hnPGItNzo4PTiUUSbHsQBE\",\"linkedin_flagship_url\":\"https://www.linkedin.com/in/stossounian\",\"name\":\"<PERSON><PERSON>\",\"location\":\"Ashburn, Virginia, United States\",\"email\":null,\"title\":\"Director Of Information Technology,IT Infrastructure & Operations Manager,SVP, Chief Information Officer,Vice President of Information Technology\",\"last_updated\":\"2025-05-06T20:11:11.926338+00:00\",\"headline\":\"<PERSON><PERSON>, Chief Information Officer\",\"summary\":null,\"num_of_connections\":1689,\"profile_picture_url\":\"https://media.licdn.com/dms/image/v2/D4D03AQF5laQ1U2gfLA/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1642616282026?e=**********&v=beta&t=wdjX_KtHoHQDteM22aj5ab9GUgp3fD9-uIkj7aBpNFM\",\"twitter_handle\":null,\"linkedin_joined_date\":null,\"business_email\":[\"stosso<PERSON><EMAIL>\"],\"enriched_realtime\":false,\"query_linkedin_profile_urn_or_slug\":[\"stossounian\"]},{\"linkedin_profile_url\":\"https://www.linkedin.com/in/ACwAAASYRbsBcfOlKhZ1xQGhTluPy78ANy8IZ74\",\"linkedin_flagship_url\":\"https://www.linkedin.com/in/shay-mcmahon-27799721\",\"name\":\"Shay McMahon\",\"location\":\"Ireland\",\"email\":\"\",\"title\":\"Chief Operating Officer\",\"last_updated\":\"2025-05-06T20:11:12.085648+00:00\",\"headline\":\"Chief Operating Officer at Corin\",\"summary\":null,\"num_of_connections\":1104,\"profile_picture_url\":\"https://media.licdn.com/dms/image/v2/C4D03AQFaX0YtDFxI2w/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1516954936057?e=**********&v=beta&t=55rzAXyQNw9xBofrFxTIk8zE-25CyeYjbi55IK_dIWA\",\"twitter_handle\":null,\"linkedin_joined_date\":null,\"business_email\":[\"<EMAIL>\"],\"enriched_realtime\":false,\"query_linkedin_profile_urn_or_slug\":[\"shay-mcmahon-27799721\"]}]"]}], "source": ["!curl 'https://api.crustdata.com/screener/person/enrich?fields=business_email&linkedin_profile_url=https://www.linkedin.com/in/stossounian,https://www.linkedin.com/in/shay-mcmahon-27799721' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": 8, "id": "e7956605-1222-4e50-a425-b1bcbc8f58fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"results\":[\"Chief Information Officer\",\"Chief Information Security Officer\",\"Assistant Chief Information Officer\",\"Deputy Chief Information Officer\",\"Group Chief Information Officer\"]}"]}], "source": ["!curl 'https://api.crustdata.com/screener/linkedin_filter/autocomplete?filter_type=title&query=Chief-Information-Officer&count=5' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": 6, "id": "ad73d634-ad06-449b-8f03-e3659b8f64e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{\"linkedin_profile_url\":\"https://www.linkedin.com/in/ACwAAARRigYBlSE3qvQk9V1KIAREFDbfg3soOr0\",\"linkedin_flagship_url\":\"https://www.linkedin.com/in/ron-sprinkle-71a32020\",\"name\":\"<PERSON>rinkle\",\"location\":\"Mason, Ohio, United States\",\"email\":null,\"title\":null,\"last_updated\":\"2025-05-06T21:26:37.426637+00:00\",\"headline\":\"Self employed\",\"summary\":null,\"num_of_connections\":447,\"skills\":[\"Practice Management\",\"EMR\",\"Healthcare\",\"Team Building\",\"Process Scheduler\",\"Healthcare Management\"],\"profile_picture_url\":\"https://media.licdn.com/dms/image/v2/C5603AQHJiGVLgQUf8Q/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1519756679466?e=**********&v=beta&t=GSPAvSG4cbqo2NhJF-1vJMgNXQIWRYEajLJG0cQyEd8\",\"twitter_handle\":null,\"languages\":[],\"linkedin_joined_date\":null,\"all_employers\":[],\"past_employers\":[],\"current_employers\":[],\"education_background\":[{\"degree_name\":\"Bachelor of Business Administration (B.B.A.)\",\"institute_name\":\"The Ohio State University\",\"institute_linkedin_id\":\"3173\",\"institute_linkedin_url\":\"https://www.linkedin.com/school/3173/\",\"institute_logo_url\":\"https://media.licdn.com/dms/image/v2/C4E0BAQEw433uw5btkQ/company-logo_400_400/company-logo_400_400/0/1631327096366?e=**********&v=beta&t=ZlQhQURwg4Jt00y9Os2NHT7HnbRH2U7DMWlebjVbT_U\",\"field_of_study\":\"\",\"start_date\":null,\"end_date\":null}],\"all_employers_company_id\":[],\"all_titles\":[],\"all_schools\":[\"The Ohio State University\"],\"all_degrees\":[\"Bachelor of Business Administration (B.B.A.)\"],\"enriched_realtime\":false,\"query_linkedin_profile_urn_or_slug\":[\"ron-sprinkle-71a32020\"]}]"]}], "source": ["!curl 'https://api.crustdata.com/screener/person/enrich?linkedin_profile_url=https://www.linkedin.com/in/ron-sprinkle-71a32020/&realtime=true' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": 7, "id": "db3c2dab-3c5e-46d8-912f-a0614caeb75f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{\"linkedin_profile_url\":\"https://www.linkedin.com/in/ACwAACM-_BsBsf7ja2yGCi0YCuQPNiMAgVLU1mI\",\"linkedin_flagship_url\":\"https://www.linkedin.com/in/brenda-tanguma-*********\",\"name\":\"<PERSON>\",\"location\":\"Albuquerque, New Mexico, United States\",\"email\":null,\"title\":\"Substance Abuse Counselor\",\"last_updated\":\"2025-05-06T21:26:38.439225+00:00\",\"headline\":\"Substance Abuse Counselor at Centurion Health\",\"summary\":\"Experienced Chief Operating Officer with a demonstrated history of working in the individual and family services industry. Skilled in Research, Customer Service, Management, Microsoft Word, and Strategic Planning. Strong operations professional graduated from University of Phoenix. \",\"num_of_connections\":83,\"skills\":[\"Customer Service\",\"Microsoft Office\",\"Leadership\",\"Management\",\"Strategic Planning\",\"Research\",\"Microsoft Word\"],\"profile_picture_url\":\"https://media.licdn.com/dms/image/v2/D5603AQH85qwIerC4LQ/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1685848646502?e=**********&v=beta&t=ZXLeaPD6NyVPuiprXfmBKU6OaqOAujGSQYpH5VB1OXg\",\"twitter_handle\":null,\"languages\":[],\"linkedin_joined_date\":null,\"all_employers\":[\"Bernalillo County\",\"Centurion Health\",\"New Season\"],\"past_employers\":[{\"employer_name\":\"New Season\",\"employer_linkedin_id\":\"5032947\",\"employer_logo_url\":\"https://media.licdn.com/dms/image/v2/D4E0BAQEXnw0dA3VlrA/company-logo_400_400/company-logo_400_400/0/1727885407075/newseason_logo?e=**********&v=beta&t=58mE3KdU_3dKBd2XFVHumOzc2E7_bxmFO_1RVYmJv_0\",\"employer_linkedin_description\":\"New Season (Operated by Colonial Management Group, LP (CMG)) is a unique organization of eighty-one (81) private outpatient substance use treatment centers that have been successfully treating opiate dependence since 1986. The Company, which is headquartered in Orlando, Florida, takes great pride in establishing and maintaining the values, mission, and direction of the organization. We are continuously searching for the most innovative techniques to utilize in our facilities to ensure the most comprehensive treatment experience resulting in the best outcome possible., , , New Season operates as outpatient centers specifically designed for persons addicted to the opioid class of drugs, including prescription pain medications and heroin. Patients are enrolled into a comprehensive program, which includes individual and group counseling to address psychological and social needs in addition to their chemical dependence. Our national treatment values are to:, , •Support the recovery, stabilization and well-being of patients , •Enhance and promote the quality of life for all patients served through a harm reduction model , •Reduce symptoms associated with opioid use and dependency and build individual resilience as part of a relapse prevention strategy , •Teach and model ways to help patients restore and/or improve their daily functioning and life skills , •Support patients as they re-integrate into their communities of choice, families, and/or support systems\",\"employer_company_id\":[1110608],\"employer_company_website_domain\":[\"newseason.com\"],\"employee_position_id\":**********,\"employee_title\":\"Counselor\",\"employee_description\":null,\"employee_location\":\"Albuquerque, New Mexico, United States\",\"start_date\":\"2020-11-01T00:00:00+00:00\",\"end_date\":\"2021-03-01T00:00:00+00:00\"},{\"employer_name\":\"Bernalillo County\",\"employer_linkedin_id\":\"51180\",\"employer_logo_url\":\"https://media.licdn.com/dms/image/v2/C560BAQFqSDl7hs30UQ/company-logo_400_400/company-logo_400_400/0/1660845109794/bernalillo_county_logo?e=**********&v=beta&t=OsoLEPAY_arfyGMzBhpYGIKz1dARvywT666kYXxR-yE\",\"employer_linkedin_description\":\"The mission of Bernalillo County is to be an effective steward of county resources and a partner in building a high quality of life for county residents, communities, and businesses.\",\"employer_company_id\":[1910101],\"employer_company_website_domain\":[\"bernco.gov\"],\"employee_position_id\":**********,\"employee_title\":\"Substance Abuse Technician Lead\",\"employee_description\":null,\"employee_location\":null,\"start_date\":\"2020-03-01T00:00:00+00:00\",\"end_date\":\"2021-03-01T00:00:00+00:00\"}],\"current_employers\":[{\"employer_name\":\"Centurion Health\",\"employer_linkedin_id\":\"35561284\",\"employer_logo_url\":\"https://media.licdn.com/dms/image/v2/D4E0BAQEsTKad2Fl80g/company-logo_400_400/company-logo_400_400/0/1702933050828/centurion_health_logo?e=**********&v=beta&t=jUVGqjXoLZuy-Wzq5Cv0NEmgMChBw4mUUPWQmnNoBnk\",\"employer_linkedin_description\":\"Welcome to Centurion Health, a leading force in healthcare dedicated to serving special populations. We specialize in providing healthcare services to multi-site large populations. Currently, we serve 12 state correctional systems, providing correctional healthcare services to nearly 275,000 incarcerated individuals in more than 250 facilities through 18 programs in 15 states across the nation. , , At Centurion Health, we’re not just setting the standard; we’re going beyond patient care and redefining it with excellence in healthcare across the nation.\",\"employer_company_id\":[1111033],\"employer_company_website_domain\":[\"teamcenturion.com\"],\"employee_position_id\":**********,\"employee_title\":\"Substance Abuse Counselor\",\"employee_description\":\"WNMCF\",\"employee_location\":\"Grants, New Mexico, United States\",\"start_date\":\"2021-06-01T00:00:00+00:00\",\"end_date\":null}],\"education_background\":[{\"degree_name\":\"Master's degree\",\"institute_name\":\"University of Phoenix\",\"institute_linkedin_id\":\"3050\",\"institute_linkedin_url\":\"https://www.linkedin.com/school/3050/\",\"institute_logo_url\":\"https://media.licdn.com/dms/image/v2/C560BAQEGQYvlDi-5OQ/company-logo_400_400/company-logo_400_400/0/1630646463815/university_of_phoenix_logo?e=**********&v=beta&t=MlUFMj3kto4rAPKIBzhNv69TQPcIMO0czsJFkFAYvnY\",\"field_of_study\":\"Psychology\",\"start_date\":\"2019-01-01T00:00:00+00:00\",\"end_date\":\"2020-01-01T00:00:00+00:00\"}],\"all_employers_company_id\":[1111033,1110608,1910101],\"all_titles\":[\"Counselor\",\"Substance Abuse Counselor\",\"Substance Abuse Technician Lead\"],\"all_schools\":[\"University of Phoenix\"],\"all_degrees\":[\"Master's degree\"],\"enriched_realtime\":false,\"query_linkedin_profile_urn_or_slug\":[\"brenda-tanguma-*********\"]}]"]}], "source": ["!curl 'https://api.crustdata.com/screener/person/enrich?linkedin_profile_url=https://www.linkedin.com/in/brenda-tanguma-*********/&realtime=true' \\\n", "  --header 'Accept: application/json, text/plain, */*' \\\n", "  --header 'Accept-Language: en-US,en;q=0.9' \\\n", "  --header 'Authorization: Token 3cf1d239ff1044cbc582e808674ad4620d131348'"]}, {"cell_type": "code", "execution_count": null, "id": "56f3b851-0449-4d91-b83e-3dc954e694de", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}