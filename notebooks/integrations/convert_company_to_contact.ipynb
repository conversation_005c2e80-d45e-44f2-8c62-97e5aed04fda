{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8dd85213-03a7-4ced-8913-bd97b89eb437", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching page 1...\n", "{'profiles': [{'name': 'Deanna Hill', 'location': 'Holland, Michigan, United States', 'linkedin_profile_url': 'https://www.linkedin.com/in/ACwAABs4zFMBC__9omUk-wPeBDOZ-48w7So-DKo', 'linkedin_profile_urn': 'ACwAABs4zFMBC__9omUk-wPeBDOZ-48w7So-DKo', 'default_position_title': 'Assembler', 'default_position_company_linkedin_id': '17363', 'default_position_is_decision_maker': False, 'flagship_profile_url': 'https://www.linkedin.com/in/deanna-hill-157b4a108', 'profile_picture_url': 'https://media.licdn.com/dms/image/v2/C5603AQFpM9IxY35UOQ/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1517430850232?e=**********&v=beta&t=wAxA8kSxZ-Yj7JUME3XIKScYAZYRskDz6BjxGBfTP5k', 'headline': 'COO at Hill Capital Advisors', 'summary': None, 'num_of_connections': 4, 'related_colleague_company_id': 17363, 'skills': [], 'employer': [{'title': 'Assembler', 'company_name': 'Gentex Corporation', 'company_linkedin_id': '17363', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C4E0BAQG2SEwJwDQ5hw/company-logo_400_400/company-logo_400_400/0/1630575507997/gentex_corporation_logo?e=**********&v=beta&t=Kgr5lpZlFuzWiG4m3x49AuwLNP6r1yRkkl_lw5N5Okw', 'start_date': None, 'end_date': None, 'position_id': *********, 'description': None, 'location': None, 'rich_media': []}, {'title': 'COO', 'company_name': 'Hill Capital Advisors', 'company_linkedin_id': '43181783', 'company_logo_url': None, 'start_date': '2020-05-01T00:00:00', 'end_date': None, 'position_id': 1620996786, 'description': 'Commercial capital financing specialists ', 'location': None, 'rich_media': []}], 'education_background': [], 'emails': [], 'websites': [], 'twitter_handle': None, 'languages': [], 'pronoun': None, 'query_person_linkedin_urn': 'ACwAABs4zFMBC__9omUk-wPeBDOZ-48w7So-DKo', 'linkedin_slug_or_urns': ['deanna-hill-157b4a108', 'ACwAABs4zFMBC__9omUk-wPeBDOZ-48w7So-DKo'], 'current_title': 'COO'}], 'total_display_count': '1'}\n", "Found 1 profiles on page 1\n", "Total profiles collected: 1\n"]}], "source": ["import requests\n", "import pandas as pd\n", "import json\n", "\n", "token = \"3cf1d239ff1044cbc582e808674ad4620d131348\"\n", "\n", "# Initialize a list to store all profiles\n", "all_profiles = []\n", "companies = [\n", "#     \"sphp.com\",\n", "#     \"soundunited.com\",\n", "#     \"soctelemed.com\",\n", "#     \"cullmanregional.com\",\n", "#     \"salonps.com\",\n", "#     \"nshorehc.com\",\n", "#     \"teamcenturion.com\",\n", "#     \"mbhs.org\",\n", "#     \"ihv.umaryland.edu\",\n", "#     \"coringroup.com\"\n", "# ] + [\n", "#     \"St. Peters Health Partners\",\n", "#     \"Sound United\",\n", "#     \"SOC Telemed\",\n", "#     \"Cullman Regional Medical Center\",\n", "#     \"Ps Salon & Spa\",\n", "#     \"North Shore Healthcare, Llc\",\n", "#     \"Centurion Health\",\n", "#     \"Mississippi Baptist Health Systems\",\n", "#     \"University of Maryland, Baltimore\",\n", "#     \"Corin Group\"\n", "    # \"https://www.linkedin.com/company/gentex/\"\n", "    \"Gentex Corp.\"\n", "]\n", "\n", "# Iterate through 10 pages\n", "for page in range(1, 2):\n", "    print(f\"Fetching page {page}...\")\n", "    response = requests.post(\n", "        \"https://api.crustdata.com/screener/person/search\",\n", "        headers={\n", "          \"Content-Type\": \"application/json\",\n", "            \"Authorization\": f\"Token {token}\",\n", "        },\n", "        json={\n", "          \"filters\": [\n", "            {\n", "                \"filter_type\": \"CURRENT_TITLE\",\n", "                \"type\": \"in\",\n", "                \"value\": [\"COO\", \"CIO\"]\n", "            },\n", "        {\n", "          \"filter_type\": \"CURRENT_COMPANY\",\n", "          \"type\": \"in\",\n", "          \"value\": companies\n", "        },\n", "          ],\n", "          \"page\": page\n", "        }\n", "    )\n", "    \n", "    # Parse the response\n", "    data = response.json()\n", "    print(data)\n", "    \n", "    # Check if 'profiles' exists in the response\n", "    if 'profiles' in data:\n", "        profiles = data['profiles']\n", "        all_profiles.extend(profiles)\n", "        print(f\"Found {len(profiles)} profiles on page {page}\")\n", "    else:\n", "        print(f\"No profiles found on page {page} or unexpected response format\")\n", "        break\n", "\n", "print(f\"Total profiles collected: {len(all_profiles)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "9b13c3b8-8ddf-4b01-9775-57d8ec213151", "metadata": {}, "outputs": [], "source": ["all_profiles"]}, {"cell_type": "code", "execution_count": null, "id": "ccc85105-e78b-4893-bbf9-90f8fe1cabd3", "metadata": {}, "outputs": [], "source": ["# Extract required fields from each profile\n", "extracted_data = []\n", "\n", "for profile in all_profiles:\n", "    # Find current employer (where end_date is null in employer list)\n", "    current_employer = None\n", "    if 'employer' in profile:\n", "        for emp in profile['employer']:\n", "            if emp.get('end_date') is None:\n", "                current_employer = emp.get('company_name')\n", "                break\n", "    \n", "    # Extract all the required fields\n", "    profile_data = {\n", "        'name': profile.get('name'),\n", "        'linkedin_profile_url': profile.get('linkedin_profile_url'),\n", "        'default_position_title': profile.get('default_position_title'),\n", "        'default_position_company_linkedin_id': profile.get('default_position_company_linkedin_id'),\n", "        'default_position_is_decision_maker': profile.get('default_position_is_decision_maker'),\n", "        'flagship_profile_url': profile.get('flagship_profile_url'),\n", "        'headline': profile.get('headline'),\n", "        'summary': profile.get('summary'),\n", "        'current_employer': current_employer,\n", "        'emails': ', '.join(profile.get('emails', [])),\n", "        'current_title': profile.get('current_title')\n", "    }\n", "    \n", "    extracted_data.append(profile_data)\n", "\n", "print(len(extracted_data))\n", "\n", "# Create a DataFrame from the extracted data\n", "df = pd.DataFrame(extracted_data)\n", "\n", "# Display the first few rows\n", "df.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "a54b78f0-7d6b-41b0-b585-3ba12ee1a030", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching page 1...\n", "{'profiles': [{'name': '<PERSON>', 'location': 'Albuquerque, New Mexico, United States', 'linkedin_profile_url': 'https://www.linkedin.com/in/ACwAACM-_BsBsf7ja2yGCi0YCuQPNiMAgVLU1mI', 'linkedin_profile_urn': 'ACwAACM-_BsBsf7ja2yGCi0YCuQPNiMAgVLU1mI', 'default_position_title': 'Substance Abuse Counselor', 'default_position_company_linkedin_id': '********', 'default_position_is_decision_maker': False, 'flagship_profile_url': 'https://www.linkedin.com/in/brenda-tanguma-*********', 'profile_picture_url': 'https://media.licdn.com/dms/image/v2/D5603AQH85qwIerC4LQ/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1685848646502?e=**********&v=beta&t=ZXLeaPD6NyVPuiprXfmBKU6OaqOAujGSQYpH5VB1OXg', 'headline': 'Substance Abuse Counselor at Centurion Health', 'summary': 'Experienced Chief Operating Officer with a demonstrated history of working in the individual and family services industry. Skilled in Research, Customer Service, Management, Microsoft Word, and Strategic Planning. Strong operations professional graduated from University of Phoenix. ', 'num_of_connections': 83, 'related_colleague_company_id': ********, 'skills': ['Customer Service', 'Microsoft Office', 'Leadership', 'Management', 'Strategic Planning', 'Research', 'Microsoft Word'], 'employer': [{'title': 'Substance Abuse Counselor', 'company_name': 'Centurion Health', 'company_linkedin_id': '********', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEsTKad2Fl80g/company-logo_400_400/company-logo_400_400/0/*************/centurion_health_logo?e=**********&v=beta&t=jUVGqjXoLZuy-Wzq5Cv0NEmgMChBw4mUUPWQmnNoBnk', 'start_date': '2021-06-01T00:00:00', 'end_date': None, 'position_id': **********, 'description': 'WNMCF', 'location': 'Grants, New Mexico, United States', 'rich_media': []}, {'title': 'Chief Operations Officer', 'company_name': 'Home', 'company_linkedin_id': None, 'company_logo_url': None, 'start_date': '1999-01-01T00:00:00', 'end_date': None, 'position_id': **********, 'description': None, 'location': None, 'rich_media': []}, {'title': 'Counselor', 'company_name': 'New Season', 'company_linkedin_id': '5032947', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEXnw0dA3VlrA/company-logo_400_400/company-logo_400_400/0/1727885407075/newseason_logo?e=**********&v=beta&t=58mE3KdU_3dKBd2XFVHumOzc2E7_bxmFO_1RVYmJv_0', 'start_date': '2020-11-01T00:00:00', 'end_date': '2021-03-01T00:00:00', 'position_id': **********, 'description': None, 'location': 'Albuquerque, New Mexico, United States', 'rich_media': []}, {'title': 'Substance Abuse Technician Lead', 'company_name': 'Bernalillo County', 'company_linkedin_id': '51180', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFqSDl7hs30UQ/company-logo_400_400/company-logo_400_400/0/1660845109794/bernalillo_county_logo?e=**********&v=beta&t=OsoLEPAY_arfyGMzBhpYGIKz1dARvywT666kYXxR-yE', 'start_date': '2020-03-01T00:00:00', 'end_date': '2021-03-01T00:00:00', 'position_id': 1613335186, 'description': None, 'location': None, 'rich_media': []}], 'education_background': [{'degree_name': \"Master's degree\", 'institute_name': 'University of Phoenix', 'field_of_study': 'Clinical Psychology', 'start_date': '2020-01-01T00:00:00', 'end_date': '2023-01-01T00:00:00', 'institute_linkedin_id': '3050', 'institute_linkedin_url': 'https://www.linkedin.com/school/3050/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQEGQYvlDi-5OQ/company-logo_400_400/company-logo_400_400/0/1630646463815/university_of_phoenix_logo?e=**********&v=beta&t=MlUFMj3kto4rAPKIBzhNv69TQPcIMO0czsJFkFAYvnY'}, {'degree_name': \"Master's degree\", 'institute_name': 'University of Phoenix', 'field_of_study': 'Psychology', 'start_date': '2019-01-01T00:00:00', 'end_date': '2020-01-01T00:00:00', 'institute_linkedin_id': '3050', 'institute_linkedin_url': 'https://www.linkedin.com/school/3050/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQEGQYvlDi-5OQ/company-logo_400_400/company-logo_400_400/0/1630646463815/university_of_phoenix_logo?e=**********&v=beta&t=MlUFMj3kto4rAPKIBzhNv69TQPcIMO0czsJFkFAYvnY'}], 'emails': [], 'websites': [], 'twitter_handle': None, 'languages': [], 'pronoun': None, 'query_person_linkedin_urn': 'ACwAACM-_BsBsf7ja2yGCi0YCuQPNiMAgVLU1mI', 'linkedin_slug_or_urns': ['brenda-tanguma-*********', 'ACwAACM-_BsBsf7ja2yGCi0YCuQPNiMAgVLU1mI'], 'current_title': 'Chief Operations Officer'}, {'name': 'Shant Tossounian', 'location': 'Ashburn, Virginia, United States', 'linkedin_profile_url': 'https://www.linkedin.com/in/ACwAAACCaawBvzlB2hnPGItNzo4PTiUUSbHsQBE', 'linkedin_profile_urn': 'ACwAAACCaawBvzlB2hnPGItNzo4PTiUUSbHsQBE', 'default_position_title': 'SVP, Chief Information Officer', 'default_position_company_linkedin_id': '********', 'default_position_is_decision_maker': True, 'flagship_profile_url': 'https://www.linkedin.com/in/stossounian', 'profile_picture_url': 'https://media.licdn.com/dms/image/v2/D4D03AQF5laQ1U2gfLA/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1642616282026?e=**********&v=beta&t=wdjX_KtHoHQDteM22aj5ab9GUgp3fD9-uIkj7aBpNFM', 'headline': 'SVP, Chief Information Officer', 'summary': None, 'num_of_connections': 1689, 'related_colleague_company_id': ********, 'skills': ['DoD', 'Cyber Security', 'Network Security', 'Managed Services', 'VPN', 'Computer Security', 'Security', 'Management', 'Strategy', 'Cloud Computing', 'CRM', 'Sales', 'Integration', 'Networking', 'Leadership', 'Enterprise Software', 'Solution Selling', 'VoIP', 'Data Center', 'Professional Services', 'SaaS', 'Program Management', 'Telecommunications', 'Strategic Partnerships', 'Team Leadership', 'Wireless', 'Business Development', 'Vendor Management', 'Outsourcing', 'Business Analysis', 'Team Management', 'Product Management', 'Pre-sales', 'Project Management', 'Product Marketing', 'Risk Management', 'Requirements Analysis', 'IT Strategy', 'Cross-functional Team Leadership', 'Business Strategy', 'Business Intelligence', 'Operations Management', 'Software Development', 'Visio', 'Business Process Improvement', 'WAN', 'E-commerce', 'Systems Management', 'Routing and Remote Access Service ', 'Customer Relationship Management (CRM)'], 'employer': [{'title': 'SVP, Chief Information Officer', 'company_name': 'Centurion Health', 'company_linkedin_id': '********', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEsTKad2Fl80g/company-logo_400_400/company-logo_400_400/0/*************/centurion_health_logo?e=**********&v=beta&t=jUVGqjXoLZuy-Wzq5Cv0NEmgMChBw4mUUPWQmnNoBnk', 'start_date': '2022-06-01T00:00:00', 'end_date': None, 'position_id': **********, 'description': None, 'location': 'Sterling, Virginia, United States', 'rich_media': []}, {'title': 'Vice President of Information Technology', 'company_name': 'Centurion Health', 'company_linkedin_id': '********', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEsTKad2Fl80g/company-logo_400_400/company-logo_400_400/0/*************/centurion_health_logo?e=**********&v=beta&t=jUVGqjXoLZuy-Wzq5Cv0NEmgMChBw4mUUPWQmnNoBnk', 'start_date': '2019-11-01T00:00:00', 'end_date': '2022-06-01T00:00:00', 'position_id': **********, 'description': None, 'location': 'Vienna, Virginia', 'rich_media': []}, {'title': 'Director Of Information Technology', 'company_name': 'Centurion Health', 'company_linkedin_id': '********', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEsTKad2Fl80g/company-logo_400_400/company-logo_400_400/0/*************/centurion_health_logo?e=**********&v=beta&t=jUVGqjXoLZuy-Wzq5Cv0NEmgMChBw4mUUPWQmnNoBnk', 'start_date': '2017-10-01T00:00:00', 'end_date': '2019-11-01T00:00:00', 'position_id': **********, 'description': None, 'location': 'Vienna, Virginia', 'rich_media': []}, {'title': 'IT Infrastructure & Operations Manager', 'company_name': 'Centurion Health', 'company_linkedin_id': '********', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEsTKad2Fl80g/company-logo_400_400/company-logo_400_400/0/*************/centurion_health_logo?e=**********&v=beta&t=jUVGqjXoLZuy-Wzq5Cv0NEmgMChBw4mUUPWQmnNoBnk', 'start_date': '2016-04-01T00:00:00', 'end_date': '2017-10-01T00:00:00', 'position_id': *********, 'description': None, 'location': 'Vienna, Virginia', 'rich_media': []}, {'title': 'Network Operations Manager', 'company_name': 'New Directions Technologies Inc. (The US Patent & Trademark Office)', 'company_linkedin_id': '1694297', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQEdHAVluTaJsA/company-logo_400_400/company-logo_400_400/0/1631391134465?e=**********&v=beta&t=EB4hEhvF49x-NzPRl-vR1QTwpZpP4ENebaDxy1SSGoY', 'start_date': '2014-12-01T00:00:00', 'end_date': '2016-04-01T00:00:00', 'position_id': *********, 'description': None, 'location': 'Alexandria, VA', 'rich_media': []}, {'title': 'Director of Operations', 'company_name': 'Blue Ridge Networks', 'company_linkedin_id': '24121', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQEs2P3fbmZBWQ/company-logo_400_400/company-logo_400_400/0/1631329925852?e=**********&v=beta&t=0YbZTEy3Tyu0dApxhFXIe7llzyBUzWDmrxVuKQi5SFI', 'start_date': '2011-02-01T00:00:00', 'end_date': '2014-06-01T00:00:00', 'position_id': *********, 'description': None, 'location': 'Chantilly, VA', 'rich_media': []}, {'title': 'Operations Manager & Sr. Level 2 Network Engineer', 'company_name': 'Blue Ridge Networks', 'company_linkedin_id': '24121', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQEs2P3fbmZBWQ/company-logo_400_400/company-logo_400_400/0/1631329925852?e=**********&v=beta&t=0YbZTEy3Tyu0dApxhFXIe7llzyBUzWDmrxVuKQi5SFI', 'start_date': '2007-05-01T00:00:00', 'end_date': '2011-02-01T00:00:00', 'position_id': 11995442, 'description': None, 'location': 'Chantilly, VA', 'rich_media': []}, {'title': 'Network Administrator', 'company_name': 'Fitch Ratings', 'company_linkedin_id': '163842', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4D0BAQHyftISy46h1Q/company-logo_400_400/company-logo_400_400/0/1720022681636/fitch_ratings_logo?e=**********&v=beta&t=JUwHZRmSNq4tO29VpyIfSHB9kPmYR73awkkHuM0Ridk', 'start_date': '2001-02-01T00:00:00', 'end_date': '2007-05-01T00:00:00', 'position_id': 17595814, 'description': None, 'location': 'Greater New York City Area', 'rich_media': []}, {'title': 'Network Administrator', 'company_name': 'Lincoln Financial Group', 'company_linkedin_id': '4307', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQHo6GO-Sp6VsQ/company-logo_400_400/company-logo_400_400/0/*************/lincoln_financial_group_logo?e=**********&v=beta&t=OxEsaqdWWEIITjYqPEspIme-6j-NOGVzPoC7EI067p8', 'start_date': '2000-07-01T00:00:00', 'end_date': '2001-02-01T00:00:00', 'position_id': ********, 'description': None, 'location': 'Morris Town, New Jersey', 'rich_media': []}, {'title': 'Network Engineer', 'company_name': 'Chase Manhattan Bank', 'company_linkedin_id': '1068', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQGxpntCyRgsuA/company-logo_400_400/company-logo_400_400/0/*************/jpmorganchase_logo?e=**********&v=beta&t=0wcTaV0UxRM0MngbEKHSlbfqIa3NxbGQVn49LYtjDE0', 'start_date': '1999-08-01T00:00:00', 'end_date': '2000-07-01T00:00:00', 'position_id': ********, 'description': None, 'location': 'Woodcliff Lake, New Jersey', 'rich_media': []}, {'title': 'Technical Account Representative', 'company_name': 'NOBEL Systems Inc.', 'company_linkedin_id': '2326722', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C4E0BAQHWV8ZIjT7uxA/company-logo_400_400/company-logo_400_400/0/*************?e=**********&v=beta&t=Ah-7fEXkTuV1gtKv8TQv6NSK7AKcIaIquhnHkxahbDc', 'start_date': '1999-01-01T00:00:00', 'end_date': '1999-08-01T00:00:00', 'position_id': ********, 'description': None, 'location': 'Fort Lee, New Jersey', 'rich_media': []}], 'education_background': [{'degree_name': None, 'institute_name': 'Chubb Institute of Technology', 'field_of_study': '', 'start_date': '1998-01-01T00:00:00', 'end_date': '2000-01-01T00:00:00', 'institute_linkedin_id': None, 'institute_linkedin_url': None, 'institute_logo_url': None}, {'degree_name': 'Diploma', 'institute_name': 'Hasbrouck Heights, High School', 'field_of_study': '', 'start_date': '1994-01-01T00:00:00', 'end_date': '1998-01-01T00:00:00', 'institute_linkedin_id': None, 'institute_linkedin_url': None, 'institute_logo_url': None}], 'emails': [], 'websites': [], 'twitter_handle': None, 'languages': ['English', 'Armenian'], 'pronoun': None, 'query_person_linkedin_urn': 'ACwAAACCaawBvzlB2hnPGItNzo4PTiUUSbHsQBE', 'linkedin_slug_or_urns': ['ACwAAACCaawBvzlB2hnPGItNzo4PTiUUSbHsQBE', 'stossounian'], 'current_title': 'SVP, Chief Information Officer'}, {'name': 'Mike Turek', 'location': 'West Chester, Pennsylvania, United States', 'linkedin_profile_url': 'https://www.linkedin.com/in/ACwAAAApv8YBeU4f3A0jGkBogdo71R_67ELLK1E', 'linkedin_profile_urn': 'ACwAAAApv8YBeU4f3A0jGkBogdo71R_67ELLK1E', 'default_position_title': 'Chief Operating Officer', 'default_position_company_linkedin_id': '202443', 'default_position_is_decision_maker': True, 'flagship_profile_url': 'https://www.linkedin.com/in/mike-turek-45aabb', 'profile_picture_url': 'https://media.licdn.com/dms/image/v2/C4E03AQHAySIRO7gnig/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1547820898971?e=**********&v=beta&t=WyrR2pTCPJ_TvTOCn6HzLIqT3Is3aScJZI7VR9V-Fbw', 'headline': 'Chief Operating Officer', 'summary': None, 'num_of_connections': 3139, 'related_colleague_company_id': 202443, 'skills': ['Six Sigma', 'Manufacturing', 'Cross-functional Team Leadership', 'Process Improvement', 'Continuous Improvement', 'Lean Manufacturing', 'Supply Chain', 'Supply Chain Management', 'Aerospace', 'Value Stream Mapping', 'Management', 'Program Management', 'Materials', 'Project Planning', 'Project Management', 'Logistics', 'Root Cause Analysis', 'Leadership', 'Operations Management', 'Kaizen', 'Materials Management', 'Procurement', 'Green Belt', 'Strategic Planning', 'Engineering Management', 'Change Management', 'Business Process Improvement', 'ERP'], 'employer': [{'title': 'Chief Operating Officer', 'company_name': 'Gentex Corp.', 'company_linkedin_id': '202443', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C510BAQFyO715cmAqqg/company-logo_400_400/company-logo_400_400/0/1631393859715?e=**********&v=beta&t=uTNV63SgDMnBP9ajtJVmeO5BBKtrX8i3-uG1fLv7hAM', 'start_date': '2023-02-01T00:00:00', 'end_date': None, 'position_id': 2141426300, 'description': None, 'location': 'Carbondale, Pennsylvania, United States', 'rich_media': []}, {'title': 'Chief Operations Officer', 'company_name': 'OMEGA Engineering', 'company_linkedin_id': '746936', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEAdm74OfkS6w/company-logo_400_400/company-logo_400_400/0/1692296742453/omega_engineering_logo?e=**********&v=beta&t=dAwovUj-_SEIyovCrQAvt03FAUh4Y5r02ywKG6Ny6Zg', 'start_date': '2020-10-01T00:00:00', 'end_date': '2023-01-01T00:00:00', 'position_id': 1897469325, 'description': None, 'location': 'Swedesboro, New Jersey, United States', 'rich_media': []}, {'title': 'VP Global Operations & Product Management', 'company_name': 'OMEGA Engineering', 'company_linkedin_id': '746936', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEAdm74OfkS6w/company-logo_400_400/company-logo_400_400/0/1692296742453/omega_engineering_logo?e=**********&v=beta&t=dAwovUj-_SEIyovCrQAvt03FAUh4Y5r02ywKG6Ny6Zg', 'start_date': '2019-11-01T00:00:00', 'end_date': '2020-10-01T00:00:00', 'position_id': 1897469121, 'description': None, 'location': 'Swedesboro, New Jersey, United States', 'rich_media': []}, {'title': 'VP Global Operations', 'company_name': 'OMEGA Engineering', 'company_linkedin_id': '746936', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQEAdm74OfkS6w/company-logo_400_400/company-logo_400_400/0/1692296742453/omega_engineering_logo?e=**********&v=beta&t=dAwovUj-_SEIyovCrQAvt03FAUh4Y5r02ywKG6Ny6Zg', 'start_date': '2017-10-01T00:00:00', 'end_date': '2019-11-01T00:00:00', 'position_id': 1107876438, 'description': 'Maintains global responsibility for operations, including manufacturing at all sites,\\nprocurement of raw material & vendor product, planning, and logistics.', 'location': 'Swedesboro, NJ', 'rich_media': []}, {'title': 'Director DC Operations', 'company_name': 'Ingersoll Rand', 'company_linkedin_id': '3175', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/D560BAQFuLDksICvcxA/company-logo_400_400/company-logo_400_400/0/1693490037280/ingersoll_rand_logo?e=**********&v=beta&t=eKrHMbGQ--tYpoEvL1XZPXbYeHZIj-Sw9mCEC_Dqio8', 'start_date': '2015-11-01T00:00:00', 'end_date': '2017-09-01T00:00:00', 'position_id': *********, 'description': 'Led the 650,000 sq. feet, high-volume distribution center supporting a $600M aftermarket business.  Oversaw all aspects of the operation, while driving the lean transformation activities required to drive significant improvements in on-time shipping performance, quality, and productivity.', 'location': 'Greater Memphis Area', 'rich_media': []}, {'title': 'Site Leader/GM', 'company_name': 'Honeywell Aerospace', 'company_linkedin_id': '1344', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFvcIh3UnA5zw/company-logo_400_400/company-logo_400_400/0/1630621634841/honeywell_logo?e=**********&v=beta&t=w6rgZsx6WMjKfc62BvhFivQZU6UNa6oCNIEssWMcPSs', 'start_date': '2013-06-01T00:00:00', 'end_date': '2015-10-01T00:00:00', 'position_id': *********, 'description': 'Led a 400-person aerospace OEM and R&O facility that produced new and overhauled engine fuel controls and accessories for commercial and military aircraft engines.  Responsible for safety, delivery, quality, working capital management, employee relations, and the deployment of the Honeywell Operating System.', 'location': 'Rocky Mount, North Carolina Area', 'rich_media': []}, {'title': 'Sr. Director Materials Execution', 'company_name': 'Honeywell Aerospace', 'company_linkedin_id': '1344', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFvcIh3UnA5zw/company-logo_400_400/company-logo_400_400/0/1630621634841/honeywell_logo?e=**********&v=beta&t=w6rgZsx6WMjKfc62BvhFivQZU6UNa6oCNIEssWMcPSs', 'start_date': '2012-01-01T00:00:00', 'end_date': '2013-05-01T00:00:00', 'position_id': *********, 'description': 'Led the factory scheduling, logistics, and supply specialist personnel supporting the Americas Mechanical Operations Center within the Honeywell Aerospace business.  Organization included 450 employees across 9 sites, plus 200 contract logistics personnel.', 'location': 'Tempe, AZ', 'rich_media': []}, {'title': 'Customer & Product Support Director, Honeywell Operating Sytem', 'company_name': 'Honeywell Aerospace', 'company_linkedin_id': '1344', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFvcIh3UnA5zw/company-logo_400_400/company-logo_400_400/0/1630621634841/honeywell_logo?e=**********&v=beta&t=w6rgZsx6WMjKfc62BvhFivQZU6UNa6oCNIEssWMcPSs', 'start_date': '2010-05-01T00:00:00', 'end_date': '2011-12-01T00:00:00', 'position_id': *********, 'description': 'Led the first non-factory deployment of the Honeywell Operating System in the $11B Aerospace business group.  The 1,800 person global customer service organization oversaw activities such as order management, disputes, quotes, technical support, technical publications, training, asset availability, and voice of the customer administration. ', 'location': None, 'rich_media': []}, {'title': 'Global Manufacturing / Transitions Planning Manager', 'company_name': 'Honeywell Aerospace', 'company_linkedin_id': '1344', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFvcIh3UnA5zw/company-logo_400_400/company-logo_400_400/0/1630621634841/honeywell_logo?e=**********&v=beta&t=w6rgZsx6WMjKfc62BvhFivQZU6UNa6oCNIEssWMcPSs', 'start_date': '2009-03-01T00:00:00', 'end_date': '2010-05-01T00:00:00', 'position_id': *********, 'description': 'Led the manufacturing planning team supporting the $1.2B Engine Product Center.  Responsibilities included inventory management, capacity planning, cell SIOP implementation, demand management, and MPS improvement.  Also responsible for all materials planning and procurement activities associated with the relocation of two factories to emerging regions.', 'location': 'Engines Product Center', 'rich_media': []}, {'title': 'Manufacturing Planning Manager', 'company_name': 'Honeywell Aerospace', 'company_linkedin_id': '1344', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFvcIh3UnA5zw/company-logo_400_400/company-logo_400_400/0/1630621634841/honeywell_logo?e=**********&v=beta&t=w6rgZsx6WMjKfc62BvhFivQZU6UNa6oCNIEssWMcPSs', 'start_date': '2008-02-01T00:00:00', 'end_date': '2009-03-01T00:00:00', 'position_id': 3714304, 'description': 'Led the manufacturing planning team supporting the $1.2B Engine Product Center.  Responsibilities included inventory management, capacity planning, cell SIOP implementation, demand management, and MPS improvement.  Also responsible for all materials planning and procurement activities associated with the relocation of two factories to emerging regions.', 'location': None, 'rich_media': []}, {'title': 'Aftermarket Planning Manager', 'company_name': 'Honeywell', 'company_linkedin_id': '1344', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFvcIh3UnA5zw/company-logo_400_400/company-logo_400_400/0/1630621634841/honeywell_logo?e=**********&v=beta&t=w6rgZsx6WMjKfc62BvhFivQZU6UNa6oCNIEssWMcPSs', 'start_date': '2007-02-01T00:00:00', 'end_date': '2008-02-01T00:00:00', 'position_id': 45158442, 'description': 'Led a group of eight new and repaired part planners and material coordinators responsible for a $35M inventory AOP.  Owned global material planning to support Honeywell repair facilities, consigned material partners, and external trade customers.', 'location': None, 'rich_media': []}, {'title': 'Supply Chain Analyst', 'company_name': 'Honeywell', 'company_linkedin_id': '1344', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQFvcIh3UnA5zw/company-logo_400_400/company-logo_400_400/0/1630621634841/honeywell_logo?e=**********&v=beta&t=w6rgZsx6WMjKfc62BvhFivQZU6UNa6oCNIEssWMcPSs', 'start_date': '2005-07-01T00:00:00', 'end_date': '2007-02-01T00:00:00', 'position_id': 24864379, 'description': 'Supply chain analyst in the Mechanical Aftermarket organization.  Supported global engine repair and overhaul facilities & direct aftermarket sales.', 'location': None, 'rich_media': []}, {'title': 'Mine Supervisor & Production/Shipping Supervisor', 'company_name': 'Unimin Corporation', 'company_linkedin_id': '57489', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQE5S-eNFQ08QA/company-logo_400_400/company-logo_400_400/0/1673980948581/unimin_corporation_logo?e=**********&v=beta&t=iyFsLInZnxJoaOL4lT_oVe0piFKmjgLKTQUhUpEqL7Y', 'start_date': '2001-09-01T00:00:00', 'end_date': '2003-05-01T00:00:00', 'position_id': 24864487, 'description': 'Supervisor at a 1-million tons-per-year silica opertion.  Led union employees in the production & shipping operations.  Also led the heavy equipment operators, the primary cleaning operations, and the drilling & blasting activities.', 'location': None, 'rich_media': []}, {'title': 'Mineral Processing Engineer', 'company_name': 'Unimin Corporation', 'company_linkedin_id': '57489', 'company_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQE5S-eNFQ08QA/company-logo_400_400/company-logo_400_400/0/1673980948581/unimin_corporation_logo?e=**********&v=beta&t=iyFsLInZnxJoaOL4lT_oVe0piFKmjgLKTQUhUpEqL7Y', 'start_date': '2000-10-01T00:00:00', 'end_date': '2001-09-01T00:00:00', 'position_id': 24864613, 'description': 'Rotated through roles as a mineral processing engineer, geologist, and mine planning engineer.', 'location': None, 'rich_media': []}], 'education_background': [{'degree_name': 'MBA', 'institute_name': 'Carnegie Mellon - Tepper School of Business', 'field_of_study': 'Strategy, Finance, Operations', 'start_date': '2003-07-01T00:00:00', 'end_date': '2005-05-01T00:00:00', 'institute_linkedin_id': '265652', 'institute_linkedin_url': 'https://www.linkedin.com/school/265652/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/C4E0BAQFkkNkl-Je41Q/company-logo_400_400/company-logo_400_400/0/1630615132482/carnegie_mellon_tepper_school_of_business_logo?e=**********&v=beta&t=4qxtSExQf06prKEdhGt72IEQXlhma8aH1QYjCnFcENw'}, {'degree_name': 'M.S.', 'institute_name': 'Penn State University', 'field_of_study': 'Mineral Processing', 'start_date': '1999-01-01T00:00:00', 'end_date': '2000-01-01T00:00:00', 'institute_linkedin_id': '3657', 'institute_linkedin_url': 'https://www.linkedin.com/school/3657/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQEFv0lqpE-gsQ/company-logo_400_400/company-logo_400_400/0/1631308876392?e=**********&v=beta&t=eGsSV4TTPAcjMSqc1_fvqQ0SKCm6aE6IlNzZB_WAHEs'}, {'degree_name': 'B.S.', 'institute_name': 'Penn State University', 'field_of_study': 'GeoEnvironmental Engineering', 'start_date': '1994-01-01T00:00:00', 'end_date': '1998-01-01T00:00:00', 'institute_linkedin_id': '3657', 'institute_linkedin_url': 'https://www.linkedin.com/school/3657/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/C560BAQEFv0lqpE-gsQ/company-logo_400_400/company-logo_400_400/0/1631308876392?e=**********&v=beta&t=eGsSV4TTPAcjMSqc1_fvqQ0SKCm6aE6IlNzZB_WAHEs'}], 'emails': [], 'websites': [], 'twitter_handle': None, 'languages': [], 'pronoun': None, 'query_person_linkedin_urn': 'ACwAAAApv8YBeU4f3A0jGkBogdo71R_67ELLK1E', 'linkedin_slug_or_urns': ['ACwAAAApv8YBeU4f3A0jGkBogdo71R_67ELLK1E', 'mike-turek-45aabb'], 'current_title': 'Chief Operating Officer'}], 'total_display_count': '3'}\n"]}], "source": ["for page in range(1, 2):\n", "    print(f\"Fetching page {page}...\")\n", "    response = requests.post(\n", "        \"https://api.crustdata.com/screener/person/search\",\n", "        headers={\n", "          \"Content-Type\": \"application/json\",\n", "            \"Authorization\": f\"Token {token}\",\n", "        },\n", "        json={\n", "          \"filters\": [\n", "            {\n", "                \"filter_type\": \"CURRENT_TITLE\",\n", "                \"type\": \"in\",\n", "                \"value\": [\"COO\", \"C<PERSON>\", \"Chief Operating Officer\", \"Chief Information Officer\"]\n", "            },\n", "        {\n", "          \"filter_type\": \"CURRENT_COMPANY\",\n", "          \"type\": \"in\",\n", "          \"value\": [\"https://www.linkedin.com/company/gentex/\", \"https://www.linkedin.com/company/centurion-health/\"]\n", "        },\n", "          ],\n", "          \"page\": page\n", "        }\n", "    )\n", "    \n", "    # Parse the response\n", "    data = response.json()\n", "    print(data)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "66fcd6ed-2408-4cdc-8b37-be5b16203b3f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}