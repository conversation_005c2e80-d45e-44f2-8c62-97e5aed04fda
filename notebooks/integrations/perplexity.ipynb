{"cells": [{"cell_type": "code", "execution_count": null, "id": "a5e4a212-221f-4527-9edd-6f466a6da0fd", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = \"https://api.perplexity.ai/chat/completions\"\n", "\n", "token = \"\"\n", "\n", "payload = {\n", "    \"model\": \"llama-3.1-sonar-small-128k-online\",\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"Be precise and concise.\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"How many stars are there in our galaxy?\"\n", "        }\n", "    ],\n", "    \"max_tokens\": 10000,\n", "    \"temperature\": 0.2,\n", "    \"top_p\": 0.9,\n", "    \"return_citations\": True,\n", "    \"search_domain_filter\": [\"perplexity.ai\"],\n", "    \"return_images\": <PERSON><PERSON><PERSON>,\n", "    \"return_related_questions\": <PERSON><PERSON><PERSON>,\n", "    \"search_recency_filter\": \"month\",\n", "    \"top_k\": 0,\n", "    \"stream\": <PERSON><PERSON><PERSON>,\n", "    \"presence_penalty\": 0,\n", "    \"frequency_penalty\": 1\n", "}\n", "headers = {\n", "    \"Authorization\": f\"Bearer {token}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "response = requests.request(\"POST\", url, json=payload, headers=headers)\n", "\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "12808422-ea77-4b17-a7e6-1a7de6f92fe6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}