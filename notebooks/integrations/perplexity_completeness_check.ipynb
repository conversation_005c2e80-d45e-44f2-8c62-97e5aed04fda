{"cells": [{"cell_type": "code", "execution_count": null, "id": "26be5623-4323-47ba-b680-28dcbe998efd", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import django\n", "\n", "# Add the following block to import and setup the django project\n", "notebook_dir = os.path.dirname(os.path.dirname(os.path.abspath(\"__file__\")))\n", "os.ch<PERSON>(notebook_dir)\n", "tofu_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(tofu_dir + '/server')\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'server.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "django.setup()\n", "# End of django setup block\n", "\n", "\n", "import logging\n", "from typing import Dict, List, Optional, Set, Tuple\n", "\n", "from django.db import transaction\n", "\n", "from api.playbook_build.object_builder_target import TargetObjectBuilder\n", "from api.models import TargetInfo, TargetInfoGroup\n", "\n", "\n", "def check_target_tofu_research(target: TargetInfo) -> Tuple[bool, List[str]]:\n", "    \"\"\"\n", "    Check if a target has all required tofu research queries completed.\n", "    Returns (is_complete, missing_queries)\n", "    \"\"\"\n", "    target_info_group = target.target_info_group\n", "    if not target_info_group or not target_info_group.meta:\n", "        return False, [\"No target info group or meta found\"]\n", "\n", "    tofu_research_queries = target_info_group.meta.get(\"tofu_research\", {})\n", "    if not tofu_research_queries:\n", "        return True, []  # No queries required\n", "\n", "    missing_queries = []\n", "    \n", "    # Check docs_build_status\n", "    docs_build_status = target.docs_build_status or {}\n", "    tofu_research_status = docs_build_status.get(\"tofu_research\", {})\n", "    \n", "    # Check additional_info\n", "    additional_info = target.additional_info or {}\n", "    tofu_research_results = additional_info.get(\"tofu_research\", {})\n", "\n", "    for query_id in tofu_research_queries:\n", "        # Check if query exists in both status and results\n", "        if query_id not in tofu_research_status or tofu_research_status[query_id] != \"success\":\n", "            missing_queries.append(f\"Missing or failed status for query {query_id}\")\n", "        if query_id not in tofu_research_results:\n", "            missing_queries.append(f\"Missing result for query {query_id}\")\n", "\n", "    return len(missing_queries) == 0, missing_queries\n", "\n", "\n", "def check_and_fix_target_tofu_research(target_info_group_id: int, fix: bool = False) -> List[Dict]:\n", "    \"\"\"\n", "    Check all targets in a target info group for missing tofu research queries.\n", "    Optionally fix them if fix=True.\n", "    \n", "    Returns a list of dicts with target info and missing queries.\n", "    \"\"\"\n", "    try:\n", "        target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)\n", "    except TargetInfoGroup.DoesNotExist:\n", "        logging.error(f\"Target info group {target_info_group_id} not found\")\n", "        return []\n", "\n", "    targets = TargetInfo.objects.filter(target_info_group=target_info_group)\n", "    results = []\n", "\n", "    for target in targets:\n", "        is_complete, missing_queries = check_target_tofu_research(target)\n", "        \n", "        if not is_complete:\n", "            result = {\n", "                \"target_id\": target.id,\n", "                \"target_key\": target.target_key,\n", "                \"missing_queries\": missing_queries\n", "            }\n", "            \n", "            if fix:\n", "                try:\n", "                    with transaction.atomic():\n", "                        builder = TargetObjectBuilder(target)\n", "                        builder.trigger_full_tofu_research()\n", "                    result[\"fixed\"] = True\n", "                except Exception as e:\n", "                    logging.error(f\"Failed to fix target {target.id}: {e}\")\n", "                    result[\"fixed\"] = False\n", "                    result[\"error\"] = str(e)\n", "            \n", "            results.append(result)\n", "            \n", "    return results\n", "\n", "\n", "def run(target_info_group_id, fix=False):\n", "    results = check_and_fix_target_tofu_research(target_info_group_id, fix)\n", "    \n", "    if not results:\n", "        print(f\"No targets with missing tofu research queries found for target info group {target_info_group_id}\")\n", "        return\n", "    \n", "    print(f\"\\nFound {len(results)} targets with missing tofu research queries:\")\n", "    for result in results:\n", "        print(f\"\\nTarget ID: {result['target_id']}\")\n", "        print(f\"Target Key: {result['target_key']}\")\n", "        print(\"Missing Queries:\")\n", "        for query in result[\"missing_queries\"]:\n", "            print(f\"  - {query}\")\n", "        if fix:\n", "            print(f\"Fixed: {'Yes' if result.get('fixed') else 'No'}\")\n", "            if not result.get('fixed'):\n", "                print(f\"Error: {result.get('error')}\")\n", "\n", "\n", "run(44745, fix=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8735d928-4289-44e1-a43d-8d0c4bbeb6e6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}