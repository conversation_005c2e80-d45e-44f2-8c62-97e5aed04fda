version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR.....
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URL
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - cd backend/tofu/server
      - docker build --build-arg REPOSITORY_URL=$REPOSITORY_URL -t $REPOSITORY_URL:$IMAGE_TAG -f Dockerfile.celery .
      - docker tag $REPOSITORY_URL:$IMAGE_TAG $REPOSITORY_URL:latest
      - docker push $REPOSITORY_URL:latest

  build:
    commands:
      - echo Pushing the Docker image...
      - docker push $REPOSITORY_URL:$IMAGE_TAG
      - echo Writing image definitions files...
      - cd $CODEBUILD_SRC_DIR
      - printf '[{"name":"celery-worker","imageUri":"%s"}]' "$REPOSITORY_URL:$IMAGE_TAG" > imagedefinitions-worker.json
      - printf '[{"name":"celery-beat","imageUri":"%s"}]' "$REPOSITORY_URL:$IMAGE_TAG" > imagedefinitions-beat.json

cache:
  paths:
    - '/opt/venv/.cache/pip/**/*'

artifacts:
  files:
    - imagedefinitions-worker.json
    - imagedefinitions-beat.json
  name: build 