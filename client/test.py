import requests
import os
import jwt
from dotenv import load_dotenv

load_dotenv("../.env")

jwt_key = os.environ["JWT_KEY"]
token = jwt.encode({"username": "<EMAIL>"}, jwt_key, algorithm="HS256")


# endpoint = "https://*************/api/test"
endpoint = "http://localhost:8000/api/test/"
headers = {"Authorization": f"Bearer {token}"}
# cookies = {
#     'next-auth.session-token': token
# }

data = {
    "test": "test",
}
get_response = requests.get(endpoint, json=data, headers=headers)
print(get_response.json())
