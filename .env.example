# Create a separate .env file
# Ask <PERSON><PERSON><PERSON> for the keys needed for the .env file
TOFU_ENV=xxx
DEBUG=xxx
LOCAL_DB=xxx
# OpenAI
OPENAI_API_KEY=xxx
# SengGrid
SENDGRID_API_KEY=xxx
# JWT Auth
JWT_KEY=xxx
# Django
SECRET_KEY=xxx
# Database
# Redis for Celery
CELERY_BROKER_URL=xxx
CELERY_RESULT_BACKEND=xxx
# Redis for Cache
CACHE_BACKEND=xxx
# ScrapingBee
SCRAPING_BEE_API_KEY=xxx
# Pinecone - development (this is a shared instance for dev purpose, ideally you should create your own pinecone instance)
PINECONE_API_KEY=xxx
PINECONE_ENV=xxx

# AWS PDF Upload to s3
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx
AWS_DEFAULT_REGION=xxx

# Axiom logging
AXIOM_ORG_ID=xxx
AXIOM_TOKEN=xxx

# Tofu Pages
DISABLE_TOFU_PAGES=xxx