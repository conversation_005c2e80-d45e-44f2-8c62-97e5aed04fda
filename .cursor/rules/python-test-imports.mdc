---
description: 
globs: 
alwaysApply: false
---
# Python Test Import Patching Rule (Generic)

When writing tests in Python using `unittest.mock.patch` (or similar), **always patch the symbol where it is used (imported) in the code under test, not where it is defined**.

This rule applies to all classes, functions, or any other symbols you want to mock in your tests.

## Why?
If you patch the wrong path (e.g., where the symbol is defined, not where it is imported and used), your mock will not be applied and the real implementation will be used during the test.

## General Example
Suppose you have the following structure:

- `some_module.py` defines `SomeClass`:
  ```python
  # some_module.py
  class SomeClass:
      ...
  ```
- `another_module.py` imports and uses `SomeClass`:
  ```python
  # another_module.py
  from some_module import SomeClass
  def do_something():
      return SomeClass()
  ```
- Your test is for `another_module.py`:
  ```python
  # test_another_module.py
  from unittest.mock import patch
  
  @patch("another_module.SomeClass")  # Patch where it is used, not where it is defined
  def test_do_something(mock_some_class):
      ...
  ```

**Do NOT patch:**
```python
@patch("some_module.SomeClass")  # This will NOT work if the code under test imports it elsewhere
```

## Guidance
- Patch the path as it appears in the module under test (where it is imported and used).
- If unsure, check the import statements in the file containing the code under test.
- This applies to all classes, functions, and symbols you want to mock.
- When writing tests, always import modules from the api package root (e.g., from api.paragon_wrapper import HubspotAgent) rather than using the full path (e.g., from server.api.paragon_wrapper import HubspotAgent).


**Following this rule will ensure your mocks are effective and your tests are isolated from real implementations.**


