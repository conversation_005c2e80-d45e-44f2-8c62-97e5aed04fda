-- First, handle PlaybookUser records
-- Check if users already exist in destination playbook
INSERT INTO api_playbookuser (playbook_id, user_id, type)
SELECT 1058, user_id, type 
FROM api_playbookuser 
WHERE playbook_id = 1114
AND user_id NOT IN (SELECT user_id FROM api_playbookuser WHERE playbook_id = 1058);

-- Update all campaigns
UPDATE api_campaign SET playbook_id = 1058 WHERE playbook_id = 1114;

-- Update all content
UPDATE api_content SET playbook_id = 1058 WHERE playbook_id = 1114;

-- Update content templates 
UPDATE api_contenttemplate SET playbook_id = 1058 WHERE playbook_id = 1114;

-- Update asset_info_group where name is NOT 'Campaign Assets'
UPDATE api_assetinfogroup 
SET playbook_id = 1058 
WHERE playbook_id = 1114 
AND asset_info_group_key IN ('Quarterly Release Notes', 'Campaign Assets (Robyn Bean)', 'Campaign Assets (fastly-productmarketing)');

-- Optional: Remove the users from the old playbook if desired
DELETE FROM api_playbookuser WHERE playbook_id = 1114;