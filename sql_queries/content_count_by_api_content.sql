SELECT u.username, u.full_name, COUNT(1)
FROM api_content c
JOIN api_playbook p ON c.playbook_id = p.id
JOIN (
    SELECT pb.id AS playbook_id, MIN(pu.id) AS first_user_link_id
    FROM api_playbook pb
    JOIN api_playbookuser pu ON pb.id = pu.playbook_id
    GROUP BY pb.id
) first_link ON p.id = first_link.playbook_id
JOIN api_playbookuser pu ON pu.id = first_link.first_user_link_id
JOIN api_tofuuser u ON pu.user_id = u.id
WHERE EXTRACT(YEAR FROM c.created_at) = 2024 
  AND EXTRACT(MONTH FROM c.created_at) BETWEEN 10 AND 12
  AND c.content_name NOT LIKE '%benchmark%' 
  AND c.content_name NOT LIKE '%eval%' 
  AND c.content_name NOT LIKE '%e2e%'
GROUP BY u.username, u.full_name order by count desc LIMIT 100