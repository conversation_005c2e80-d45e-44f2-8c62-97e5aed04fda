# First-time Setup

### Setup Python Environments

Before proceeding, ensure Python 3.11 is installed and that the python command points to python3.11

To install python 3.11:
https://chatgpt.com/share/6785a23c-80d8-8009-8b68-ca70cc73c81d

```
cd backend/tofu
python -m venv venv
source venv/bin/activate
cd server
pip install -r requirements.txt
```

### Install and Start Redis Server (used by Celery)

For macOS

```commandline
brew install redis # install redis
brew services start redis # start redis and enable it to automatically start on boot

# verify redis is running
brew services list
# by running below you should see the response "PONG", which indicates that Redis is up and running.
redis-cli ping
```

For linux OS

```
sudo apt install redis
sudo systemctl start redis.service
sudo systemctl enable redis.service
```

### Set up environment variables

```commandline
cd backend/tofu
touch .env
```
*find the backend environment variables in 1Password*

### [Optional] Install and Start RabbitMQ (used by <PERSON><PERSON><PERSON>)

- This step is optional. If RabbitM<PERSON> is not running and not set in .env, celery will use Redis for broker.
- Please make sure CELERY_BROKER_URL is set amqp://guest:guest@localhost:5672// when enabling RabbitMQ, and set redis://localhost:6379 for Redis.
  For macOS

```commandline
brew install rabbitmq
rabbitmq-server
```

- If there's any issue, please try to run the full command:
  CONF_ENV_FILE="/opt/homebrew/etc/rabbitmq/rabbitmq-env.conf" /opt/homebrew/opt/rabbitmq/sbin/rabbitmq-server
- RabbitMQ manager on local is http://localhost:15672/#/ and default user/password are both guest.

### [Optional] Setup Local Postgres Database For Dev

- Create an `.env` file under `backend/tofu` (note: ask the admin to get full env variables), make sure to have the below command in the file:
  ```
  LOCAL_DB=True
  ```
- Install PostgreSQL (note: a latest version like v14.7 works well) on your local machine if you haven't already. You can download it from the official website and follow the installation instructions.

- Once installed, open a terminal or command prompt and use the `createdb` command to create a new database with the name `tofudev` and a user `postgres` with password `postgres`:

  ```
  createdb -h 127.0.0.1 -p 5432 -U postgres -W tofudev
  ```

- run migrations on the new db (DO NOT DO THIS UNLESS YOU ARE 100% SURE USING A LOCAL DB)
  ```
  python manage.py makemigrations
  python manage.py migrate
  ```
- Verify your db and tables are created:

  - run `psql tofudev` to start a postgres shell under the `tofudev` db
  - within the postgres shell, run `\dt`, it should return all the tables under `tofudev` db.

- Create some required data for local testing
  - create a superuser with username as `tofuadmin`, this is needed for frontend authentication
  - `python manage.py createsuperuser`

# Start Backend Service Locally

### Start Django Server

First ensure you created a .env file based on the .env.sample file in the backend/tofu/.env directory.

```bash
cd backend/tofu
./django.sh
```

or

```
cd backend/tofu/server
source ../venv/bin/activate
python manage.py collectstatic
uvicorn server.asgi:application --host 0.0.0.0 --port 8000 --reload
```

### Start Celery Worker

```bash
cd backend/tofu
./celery.sh
```

or

```
cd backend/tofu/server
source ../venv/bin/activate
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES; celery -A server worker -l info 
```

In new terminal tab, run `celery beat`
```
cd backend/tofu/server
source ../venv/bin/activate
celery -A server beat -l info --scheduler redbeat.RedBeatScheduler
```

### [Optional] Start Flower (monitor tool for Celery)

```
cd backend/tofu/server
source ../venv/bin/activate
celery -A server flower --address=0.0.0.0 --port=5555
```

- Use http://0.0.0.0:5555/ to check celery workers and tasks at dev.
- For staging, it's: https://dev.api.tofuhq.com/flower/, user/password is saved in 1password.
- For product, it's: https://api.tofuhq.com//flower/, user/password is saved in 1password.

### Dev Tips

- Once the backend server is up, it's recommended to leverage http://localhost:8000/api/docs/ to execute/debug APIs and view data models
- [pgAmin](https://www.pgadmin.org/) can be used to query postgres database (note: please be careful & do _not_ modify tables unless we move from production DB to staging/local ones in the future)

#### Copy postgres database from prod to local

```
pg_dump -h tofu-prod.cos7ruzupmrp.us-west-2.rds.amazonaws.com -U postgres -d tofu > rds_dump.sql
psql -h localhost -U postgres -d tofudev < rds_dump.sql
```

# Unit Tests

To protect user experience and build trust, it's important that we write unit tests for important functions

How to run unit tests

```
cd backend/tofu
source venv/bin/activate
cd server
pytest
```

If you want to see test coverage report, run:

```
pytest --cov=.
```

How to dev unit test:

- most unit tests are under `api/unit_tests` directory, but as long as the test file is under a `unit_tests` subdirectory, pytest will be able to find and run it
- test files should follow the naming convention: `test_*.py` or `*_tests.py`
- to run specific tests, you can use:
  ```
  pytest path/to/test_file.py
  ```

# Manage Database Schema (DO NOT DO THIS UNLESS YOU ARE 100% SURE WHAT YOU ARE DOING)

### Do this everytime data model is changed

```
python manage.py makemigrations
python manage.py migrate
```

### Admin Interface

```
python manage.py createsuperuser
```

# Manual Deployment to Production

### Deploy to AWS ElasticBeanstalk

```
cd backend/tofu/server
eb init
eb deploy
```

# Debugging in VSCode
If you are using VSCode, you can use the following configurations to setup the debugger for the backend server and celery worker.
You can leverage breakpoints to debug the code, inspect variables, and step through the code.

1. Create a launch.json file in the .vscode directory of the backend/tofu/ directory.
2. Add the following configurations:
```
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Django",
            "type": "debugpy",
            "request": "launch",
            "args": [
                "runserver"
            ],
            "django": true,
            "autoStartBrowser": false,
            "program": "${workspaceFolder}/server/manage.py"
        },
        {
            "name": "Celery",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/venv/bin/celery",
            "args": ["-A", "server", "worker", "--loglevel=DEBUG", "--beat"],
            "console": "integratedTerminal",
            "env": {
                "DJANGO_SETTINGS_MODULE": "server.settings",
                "OBJC_DISABLE_INITIALIZE_FORK_SAFETY": "YES"
            },
            "cwd": "${workspaceFolder}/server" 
        }
    ],
    "compounds": [
        {
            "name": "Django + Celery",
            "configurations": ["Python Debugger: Django", "Celery"]
        }
    ]
}
```
3. Run the Django + Celery compound configuration (command + shift + D -> select Django + Celery) to start the backend server and celery worker.
4. Set breakpoints in the code and run the configurations to debug the code.

Note: 
- The Django configuration is for running the Django development server.
- The Celery configuration is for running the Celery worker.
- The compound configuration is for running both the Django server and Celery worker.
- The root directory is the backend/tofu/ directory not backend/ directory.