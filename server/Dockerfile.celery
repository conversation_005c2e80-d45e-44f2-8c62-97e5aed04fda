ARG REPOSITORY_URL
FROM ${REPOSITORY_URL}:base

WORKDIR /app

# Copy and install requirements (in case there are new dependencies)
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy the application code
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV DJANGO_SETTINGS_MODULE=server.settings

# Create log directory
RUN mkdir -p /var/log/celery

# Set the entrypoint
ENTRYPOINT ["/bin/sh", "entrypoint.sh"]