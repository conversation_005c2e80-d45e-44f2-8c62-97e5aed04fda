#!/bin/bash

# List of environments where Celery should not run
NO_CELERY_ENVS=("load_test")

# Set up environment
cd /var/app/current/
source /var/app/venv/*/bin/activate

# Check if current environment is in the list
if [[ " ${NO_CELERY_ENVS[*]} " =~ " ${TOFU_ENV} " ]]; then
    echo "Skipping Celery start for environment: ${TOFU_ENV}"
else
    echo "Starting Celery for environment: ${TOFU_ENV}"
    celery -A server worker -l info --logfile=/var/log/celery_worker.log -n worker@%h.%i --detach
    celery -A server beat -l info --scheduler redbeat.RedBeatScheduler --logfile=/var/log/celery_beat.log --detach
fi

celery -A server flower --address=0.0.0.0 --port=5555 --url_prefix=flower --basic_auth=flower:TofuFlower >> /var/log/flower.log 2>&1 &
echo "Celery started."