#!/bin/bash
echo "Checking for LibreOffice installation..."

# Check if the LibreOffice executable exists
if [ -f /opt/libreoffice7.6/program/soffice ]; then
    echo "LibreOffice is already installed."
else
    echo "Installing packages..."
    yum update -y
    yum install -y wget tar java-1.8.0-openjdk

    # Download and install LibreOffice if not installed
    if wget https://downloadarchive.documentfoundation.org/libreoffice/old/7.6.7.2/rpm/x86_64/LibreOffice_7.6.7.2_Linux_x86-64_rpm.tar.gz; then
        tar -xvf LibreOffice_7.6.7.2_Linux_x86-64_rpm.tar.gz
        cd LibreOffice_*_Linux_x86-64_rpm/RPMS/
        rpm -Uvh *.rpm
        echo "Done"
    else
        echo "Failed to download LibreOffice"
        exit 1
    fi
fi
