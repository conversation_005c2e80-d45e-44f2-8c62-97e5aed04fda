#!/bin/bash

# Create logrotate configuration for Celery logs
cat > /etc/logrotate.d/celery << 'EOF'
/var/log/celery_worker.log
/var/log/celery_shutdown.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
    sharedscripts
    postrotate
        [ -f /var/run/celery/celery.pid ] && kill -USR1 $(cat /var/run/celery/celery.pid)
    endscript
}
EOF

# Set proper permissions
chmod 644 /etc/logrotate.d/celery
chown root:root /etc/logrotate.d/celery 