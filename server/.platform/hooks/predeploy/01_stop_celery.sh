#!/bin/bash

LOGFILE="/var/log/celery_shutdown.log"
WORKER_NAME="celery.*worker"
FLOWER_NAME="celery.*flower"
SCHEDULER_NAME="celery.*beat"

# Log start of the shutdown process
touch $LOGFILE
echo "[$(date)] Starting Celery shutdown process..." | tee -a $LOGFILE

# Step 1: Send SIGTERM to gracefully stop all Celery workers, Flower, and scheduler
echo "[$(date)] Sending SIGTERM to Celery workers, Flower, and scheduler for graceful shutdown..." | tee -a $LOGFILE
pkill -f "$WORKER_NAME" --signal TERM || true
pkill -f "$FLOWER_NAME" --signal TERM || true
pkill -f "$SCHEDULER_NAME" --signal TERM || true

# Step 2: Wait for the processes to terminate (adjustable waiting time)
echo "[$(date)] Waiting 10 seconds for processes to terminate..." | tee -a $LOGFILE
sleep 10

# Step 3: Check if any Celery processes are still running, and force stop if necessary
if pgrep -f "$WORKER_NAME" || pgrep -f "$FLOWER_NAME" || pgrep -f "$SCHEDULER_NAME"; then
  echo "[$(date)] Force stopping remaining Celery processes with SIGKILL..." | tee -a $LOGFILE
  pkill -f "$WORKER_NAME" --signal KILL || true
  pkill -f "$FLOWER_NAME" --signal KILL || true
  pkill -f "$SCHEDULER_NAME" --signal KILL || true
else
  echo "[$(date)] All Celery processes terminated gracefully." | tee -a $LOGFILE
fi

# Final Step: Log completion of shutdown
echo "[$(date)] Celery shutdown process completed." | tee -a $LOGFILE