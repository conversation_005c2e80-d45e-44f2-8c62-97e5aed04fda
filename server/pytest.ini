[pytest]
DJ<PERSON><PERSON><PERSON>_SETTINGS_MODULE = server.settings_for_test
python_files = tests.py test_*.py *_tests.py
testpaths = 
    **/unit_tests
    **/integration_tests

addopts = -s

env =
    DEBUG=True
    TOFU_ENV=unit_test
    LOCAL_DB=True

    # Celery settings for testing
    CELERY_ALWAYS_EAGER=True
    CELERY_EAGER_PROPAGATES_EXCEPTIONS=True
    CELERY_TASK_ALWAYS_EAGER=True
    CELERY_TASK_EAGER_PROPAGATES=True

    # make sure test is only using local redis
    REDIS_URL=redis://localhost:6379/0
    CELERY_RESULT_BACKEND=redis://localhost:6379/0
    CACHE_BACKEND=redis://localhost:6379/1

    # make sure test is not touching prod pinecone
    PINECONE_API_KEY=not_valid
    PINECONE_INDEX_HOST=not_valid

    # make sure test is not touching prod database
    DB_NAME=not_valid
    TEST_DB_NAME=not_valid
    DB_USER=not_valid
    DB_PASSWORD=not_valid
    DB_HOST=not_valid
    DB_PORT=not_valid

    # make sure test is not touching prod aws
    AWS_ACCESS_KEY_ID=not_valid
    AWS_SECRET_ACCESS_KEY=not_valid
    AWS_DEFAULT_REGION=not_valid

    # make sure test is not touching model providers
    OPENAI_API_KEY=not_valid
    ANTHROPIC_API_KEY=not_valid
    GOOGLE_API_KEY=not_valid
    GEMINI_ULTRA_TOKEN=not_valid

    # make sure test is not prod integrations
    PARAGON_PROJECT_ID=not_valid

    # make sure test is not touching slack
    SLACK_BOT_TOKEN=not_valid

    # inbound provider
    HAPPIERLEADS_PRIVATE_KEY=not_valid
    LEADFEEDER_PRIVATE_KEY=not_valid

    # data enrichment
    PERPLEXITY_AI_API_KEY=not_valid
    CRUSTDATA_API_KEY=not_valid

    # stripe
    STRIPE_API_KEY=not_valid
    STRIPE_WEBHOOK_SECRET=not_valid
    STRIPE_DOMAIN_URL=not_valid
