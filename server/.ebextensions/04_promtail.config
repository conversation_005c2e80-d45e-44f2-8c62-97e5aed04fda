packages:
  yum:
    wget: []
    unzip: []

files:
  "/opt/elasticbeanstalk/hooks/appdeploy/pre/01_install_promtail.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/bin/bash
      # Download and install Promtail
      wget https://github.com/grafana/loki/releases/download/v2.9.2/promtail-linux-amd64.zip
      unzip promtail-linux-amd64.zip
      mv promtail-linux-amd64 /usr/local/bin/promtail
      chmod +x /usr/local/bin/promtail

  "/opt/elasticbeanstalk/hooks/appdeploy/pre/02_promtail_config.yml":
    mode: "000644"
    owner: root
    group: root
    content: |
      server:
        http_listen_port: 9080
        grpc_listen_port: 0

      positions:
        filename: /tmp/positions.yaml

      clients:
        - url: LOKI_URL_PLACEHOLDER

      scrape_configs:
        - job_name: nginx_access
          static_configs:
            - targets:
                - localhost
              labels:
                job: nginx
                type: access
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/nginx/access.log
          pipeline_stages:
            - match:
                selector: '{job="nginx", type="access"}'
                stages:
                  - regex:
                      expression: '.*ELB-HealthChecker/2.0.*'
                  - drop:
                      expression: '.*ELB-HealthChecker/2.0.*'

        - job_name: nginx_error
          static_configs:
            - targets:
                - localhost
              labels:
                job: nginx
                type: error
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/nginx/error.log

        - job_name: django
          static_configs:
            - targets:
                - localhost
              labels:
                job: django
                application: web
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/web.stdout.log

        - job_name: elasticbeanstalk
          static_configs:
            - targets:
                - localhost
              labels:
                job: elasticbeanstalk
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/eb-*

        - job_name: celery_worker
          static_configs:
            - targets:
                - localhost
              labels:
                job: celery
                application: celery-worker
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/celery_worker.log

        - job_name: celery_beat
          static_configs:
            - targets:
                - localhost
              labels:
                job: celery
                application: celery-beat
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/celery_beat.log

        - job_name: celery_shutdown
          static_configs:
            - targets:
                - localhost
              labels:
                job: celery
                application: celery-shutdown
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/celery_shutdown.log

        - job_name: flower
          static_configs:
            - targets:
                - localhost
              labels:
                job: flower
                application: flower
                environment: TOFU_ENV_PLACEHOLDER
                __path__: /var/log/flower.log

  "/opt/elasticbeanstalk/hooks/appdeploy/pre/03_start_promtail.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/bin/bash
      # Create service file if it doesn't exist
      if [ ! -f /etc/systemd/system/promtail.service ]; then
        cat > /etc/systemd/system/promtail.service << EOF
      [Unit]
      Description=Promtail service
      After=network.target

      [Service]
      Type=simple
      User=root
      Environment=LOKI_URL=\$(grep LOKI_URL /opt/elasticbeanstalk/deployment/env.vars | cut -d'=' -f2)
      Environment=TOFU_ENV=\${TOFU_ENV}
      ExecStart=/usr/local/bin/promtail -config.file /opt/elasticbeanstalk/hooks/appdeploy/pre/02_promtail_config.yml
      Restart=always

      [Install]
      WantedBy=multi-user.target
      EOF
        systemctl daemon-reload
        systemctl enable promtail
      fi

      # Restart Promtail to pick up new config
      systemctl stop promtail
      systemctl start promtail

container_commands:
  01_install_promtail:
    command: |
      bash /opt/elasticbeanstalk/hooks/appdeploy/pre/01_install_promtail.sh
  02_set_loki_url:
    command: |
      if [ "$TOFU_ENV" = "production" ]; then
        echo "http://loki.prod.tofu.internal:3100/loki/api/v1/push" > /opt/elasticbeanstalk/deployment/env.vars
      else
        echo "http://loki.dev.tofu.internal:3100/loki/api/v1/push" > /opt/elasticbeanstalk/deployment/env.vars
      fi
  03_configure_promtail:
    command: |
      # Replace placeholders with actual values
      LOKI_URL=$(cat /opt/elasticbeanstalk/deployment/env.vars)
      sed -i -e "s|LOKI_URL_PLACEHOLDER|$LOKI_URL|g" -e "s|TOFU_ENV_PLACEHOLDER|$TOFU_ENV|g" /opt/elasticbeanstalk/hooks/appdeploy/pre/02_promtail_config.yml
      # Verify the replacement
      echo "Verifying configuration:"
      grep -A 2 "clients:" /opt/elasticbeanstalk/hooks/appdeploy/pre/02_promtail_config.yml
      grep -A 2 "environment:" /opt/elasticbeanstalk/hooks/appdeploy/pre/02_promtail_config.yml
  04_start_promtail:
    command: |
      bash /opt/elasticbeanstalk/hooks/appdeploy/pre/03_start_promtail.sh 