packages:
  yum:
    amazon-cloudwatch-agent: []

files:
  "/etc/amazon-cloudwatch-agent/amazon-cloudwatch-agent.json":
    mode: "000644"
    owner: root
    group: root
    content: |
      {
        "agent": {
          "metrics_collection_interval": 60,
          "run_as_user": "root"
        },
        "metrics": {
          "append_dimensions": {
            "AutoScalingGroupName": "${aws:AutoScalingGroupName}",
            "InstanceId": "${aws:InstanceId}"
          },
          "aggregation_dimensions": [["InstanceId"], ["AutoScalingGroupName"]],
          "metrics_collected": {
            "mem": {
              "measurement": [
                "mem_used_percent",
                "mem_available_percent",
                "mem_available",
                "mem_total"
              ],
              "metrics_collection_interval": 60
            },
            "swap": {
              "measurement": [
                "swap_used_percent"
              ],
              "metrics_collection_interval": 60
            },
            "disk": {
              "measurement": [
                "disk_used_percent"
              ],
              "metrics_collection_interval": 60
            }
          }
        },
        "logs": {
          "logs_collected": {
            "files": {
              "collect_list": [
                {
                  "file_path": "/var/log/celery_shutdown.log",
                  "log_group_name": "/aws/elasticbeanstalk/celery/<environment-name>/shutdown_logs",
                  "log_stream_name": "{instance_id}",
                  "retention_in_days": 7
                },
                {
                  "file_path": "/var/log/celery_worker.log",
                  "log_group_name": "/aws/elasticbeanstalk/celery/<environment-name>/worker_logs",
                  "log_stream_name": "{instance_id}",
                  "retention_in_days": 7
                },
                {
                  "file_path": "/var/log/celery_beat.log",
                  "log_group_name": "/aws/elasticbeanstalk/celery/<environment-name>/beat_logs",
                  "log_stream_name": "{instance_id}",
                  "retention_in_days": 7
                },
                {
                  "file_path": "/var/log/flower.log",
                  "log_group_name": "/aws/elasticbeanstalk/celery/<environment-name>/flower_logs",
                  "log_stream_name": "{instance_id}",
                  "retention_in_days": 7
                }
              ]
            }
          }
        }
      }

container_commands:
  01_get_environment_name:
    command: |
      sed -i -e "s|<environment-name>|${TOFU_ENV}|" /etc/amazon-cloudwatch-agent/amazon-cloudwatch-agent.json
  02_configure_cloudwatch_agent:
    command: |
      sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/etc/amazon-cloudwatch-agent/amazon-cloudwatch-agent.json -s
  03_enable_and_start_cloudwatch_agent:
    command: |
      sudo systemctl enable amazon-cloudwatch-agent
      sudo systemctl start amazon-cloudwatch-agent
