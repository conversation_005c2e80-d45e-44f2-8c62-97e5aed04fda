commands:
  01_install_fedora_repo:
    command: |
      echo '[fedora]
      name=Fedora 36 - $basearch
      metalink=https://mirrors.fedoraproject.org/metalink?repo=fedora-36&arch=$basearch
      enabled=1
      metadata_expire=7d
      repo_gpgcheck=0
      type=rpm
      gpgcheck=1
      gpgkey=https://getfedora.org/static/fedora.gpg
            https://src.fedoraproject.org/rpms/fedora-repos/raw/f36/f/RPM-GPG-KEY-fedora-36-primary
      skip_if_unavailable=False' > /etc/yum.repos.d/fedora.repo

  02_install_poppler_utils:
    command: "yum install -y poppler-utils"
    ignoreErrors: "false"

  03_install_tesseract:
    command: "yum install -y tesseract"
    ignoreErrors: "false"

option_settings:
  aws:elasticbeanstalk:environment:proxy:staticfiles:
    /static: static
  aws:elasticbeanstalk:application:environment:
    DJANGO_SETTINGS_MODULE: server.settings