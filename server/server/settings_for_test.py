from .settings import *

# Override any settings that are specific to the test environment
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "tofudev",
        "USER": "postgres",
        "PASSWORD": "postgres",
        "HOST": "127.0.0.1",
        "PORT": "5432",
        "TEST": {
            "NAME": "tofu_test",
        },
        "CONN_MAX_AGE": int(
            os.getenv("DB_CONN_MAX_AGE", 600)
        ),  # Increase to 600 seconds (10 minutes)
        "OPTIONS": {
            "options": "-c statement_timeout=0 -c idle_in_transaction_session_timeout=0",  # Disable statement and idle timeouts
        },
    }
}

# Celery test settings
CELERY_ALWAYS_EAGER = True
CELERY_EAGER_PROPAGATES_EXCEPTIONS = True
CELERY_TASK_ALWAYS_EAGER = True
