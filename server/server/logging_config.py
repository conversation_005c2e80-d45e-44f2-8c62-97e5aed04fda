import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict

import pytz
from django.conf import settings
from pythonjsonlogger import jsonlogger


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter that adds additional fields and handles datetime serialization."""

    def add_fields(
        self,
        log_record: Dict[str, Any],
        record: logging.LogRecord,
        message_dict: Dict[str, Any],
    ) -> None:
        """Add custom fields to the log record."""
        super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)

        # Add timestamp using timezone from Django settings
        server_tz = pytz.timezone(settings.TIME_ZONE)
        server_time = datetime.now(server_tz)
        log_record["timestamp"] = (
            server_time.isoformat() + f" ({settings.TIME_ZONE})"
        )  # Using timezone from settings

        # Add log level
        log_record["level"] = record.levelname

        # Add module and function information
        log_record["module"] = record.module
        log_record["function"] = record.funcName
        log_record["line"] = record.lineno

        # Add process and thread information
        log_record["process"] = record.process
        log_record["thread"] = record.thread
        log_record["threadName"] = record.threadName


def setup_logging():
    """Configure logging with JSON formatter."""
    # Create JSON formatter
    formatter = CustomJsonFormatter(
        "%(timestamp)s %(level)s %(name)s %(module)s %(function)s %(line)s %(message)s"
    )

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicate logs
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Add our JSON handler
    root_logger.addHandler(console_handler)

    # Configure Django logger
    django_logger = logging.getLogger("django")
    django_logger.setLevel(logging.INFO)
    django_logger.addHandler(console_handler)

    # Configure database logger
    db_logger = logging.getLogger("django.db.backends")
    db_logger.setLevel(logging.INFO)
    db_logger.addHandler(console_handler)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name."""
    return logging.getLogger(name)


def setup_json_logging():
    """Configure additional JSON logging alongside existing logging."""
    # Create JSON formatter
    formatter = CustomJsonFormatter(
        "%(timestamp)s %(level)s %(name)s %(module)s %(function)s %(line)s %(message)s"
    )

    # Create console handler for JSON logs
    json_handler = logging.StreamHandler(sys.stdout)
    json_handler.setFormatter(formatter)
    json_handler.setLevel(logging.INFO)

    # Add JSON handler to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(json_handler)

    # Add JSON handler to Django logger
    django_logger = logging.getLogger("django")
    django_logger.addHandler(json_handler)

    # Add JSON handler to database logger
    db_logger = logging.getLogger("django.db.backends")
    db_logger.addHandler(json_handler)


def get_json_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name that will output JSON format."""
    logger = logging.getLogger(name)
    # Ensure the logger has our JSON handler
    if not any(
        isinstance(h, logging.StreamHandler)
        and isinstance(h.formatter, CustomJsonFormatter)
        for h in logger.handlers
    ):
        formatter = CustomJsonFormatter(
            "%(timestamp)s %(level)s %(name)s %(module)s %(function)s %(line)s %(message)s"
        )
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    return logger
