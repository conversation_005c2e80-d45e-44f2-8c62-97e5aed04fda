from __future__ import absolute_import, unicode_literals

import os
import socket

from billiard import current_process
from celery import Celery, signals
from server.logging_config import get_json_logger

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")

app = Celery("server")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.conf.task_queue_max_priority = 10
app.autodiscover_tasks()

# Configure Celery to use our JSON logger
logger = get_json_logger(__name__)


@signals.worker_process_init.connect
def rename_worker_process(sender, **kwargs):
    worker_name = f"worker-{socket.gethostname()}-{os.getpid()}"
    if current_process().name != worker_name:
        current_process().name = worker_name
        logger.info(
            "Worker process renamed",
            extra={
                "worker_name": worker_name,
                "process_id": os.getpid(),
                "hostname": socket.gethostname(),
            },
        )
