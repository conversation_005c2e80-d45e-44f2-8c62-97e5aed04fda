"""server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from api import views
from api.auth import SwaggerBasicAuthentication
from django.contrib import admin
from django.contrib.auth.decorators import login_required
from django.urls import include, path
from drf_yasg import openapi
from drf_yasg.generators import OpenAPISchemaGenerator
from drf_yasg.views import get_schema_view
from rest_framework import permissions, routers


class BothHttpAndHttpsSchemaGenerator(OpenAPISchemaGenerator):
    def get_schema(self, request=None, public=False):
        schema = super().get_schema(request, public)
        schema.schemes = ["http", "https"]
        return schema


schema_view = get_schema_view(
    openapi.Info(
        title="Tofu APIs",
        default_version="v1",
        description=None,
        terms_of_service=None,
        contact=None,
        license=None,
    ),
    generator_class=BothHttpAndHttpsSchemaGenerator,
    public=False,
    permission_classes=[permissions.IsAuthenticated],
    authentication_classes=[SwaggerBasicAuthentication],
)

# Create protected schema views that require login
protected_schema_view = login_required(
    schema_view.with_ui("swagger", cache_timeout=0), login_url="/admin/login/"
)
protected_redoc_view = login_required(
    schema_view.with_ui("redoc", cache_timeout=0), login_url="/admin/login/"
)

router = routers.DefaultRouter()
router.register(r"user", views.UserViewSet, basename="user")
router.register(r"playbook", views.PlaybookViewSet, basename="playbook")
router.register(
    r"target_info_group", views.TargetInfoGroupViewSet, basename="target-info-group"
)
router.register(r"target_info", views.TargetInfoViewSet, basename="target-info")
router.register(
    r"asset_info_group", views.AssetInfoGroupViewSet, basename="asset-info-group"
)
router.register(r"asset_info", views.AssetInfoViewSet, basename="asset-info")
router.register(r"company_info", views.CompanyInfoViewSet, basename="company-info")
router.register(r"campaign", views.CampaignViewSet, basename="campaign")
router.register(r"action", views.ActionViewSet, basename="action")
router.register(r"content_group", views.ContentGroupViewSet, basename="content-group")
router.register(r"content", views.ContentViewSet, basename="content")
router.register(
    r"content/variation", views.ContentVariationViewSet, basename="content-variation"
)
router.register(r"chatbot", views.ChatbotViewSet, basename="chatbot")
router.register(r"eval", views.EvalViewSet, basename="eval")
router.register(r"task", views.TaskViewSet, basename="task")
router.register(r"public", views.PublicViewSet, basename="public")
router.register(r"data", views.DataViewSet, basename="data")
router.register(r"heartbeat", views.HeartBeatViewSet, basename="heartbeat")
router.register(
    r"content_template", views.ContentTemplateViewSet, basename="content-template"
)
router.register(
    r"feature_announcement",
    views.FeatureAnnouncementViewSet,
    basename="feature-announcement",
)
router.register(r"tag", views.TagViewSet, basename="tag")

urlpatterns = [
    path("", views.index, name="index"),
    path(
        "api/docs/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path(
        "api/docs/redoc/",
        schema_view.with_ui("redoc", cache_timeout=0),
        name="schema-redoc",
    ),
    path("admin/", admin.site.urls),
    path("api/", include(router.urls)),
]
