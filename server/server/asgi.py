"""
ASGI config for server project (with WebSocket support).
"""

import json
import os

from channels.auth import AuthMiddlewareStack
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.routing import ProtocolType<PERSON>outer, URLRouter
from django.core.asgi import get_asgi_application
from django.urls import path

# Set up Django settings
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")

# Initialize Django ASGI application early to ensure models and apps are loaded
django_asgi_app = get_asgi_application()


# Define a mock WebSocket consumer for testing WebSockets
class MockWebSocketConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()
        await self.send(
            text_data=json.dumps({"message": "Connected to mock WebSocket"})
        )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json.get("message", "")
        await self.send(text_data=json.dumps({"message": f"Echo: {message}"}))

    async def disconnect(self, close_code):
        pass


# WebSocket routing for the app
websocket_urlpatterns = [
    path("ws/mock/", MockWebSocketConsumer.as_asgi()),
]

# Combine HTTP and WebSocket protocols
application = ProtocolTypeRouter(
    {
        "http": django_asgi_app,  # Handles HTTP traffic
        "websocket": AuthMiddlewareStack(
            URLRouter(websocket_urlpatterns)  # Handle WebSocket routes
        ),
    }
)
