import logging
import time
from functools import wraps
from typing import Any, Dict, Optional

from .logging_config import get_logger

# TODO: zaicheng will consolidate other task logging utils to this file, we are not using these decorators for now


def log_with_context(logger_name: str, **default_context):
    """
    Decorator to add context to all log messages in a function.

    Usage:
        @log_with_context('module_name', service='api', component='auth')
        def my_function():
            logger = get_logger(__name__)
            logger.info('This message will include the context')
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            # Add function name to context
            context = {**default_context, "function": func.__name__}

            # Create a context manager for the logger
            class LoggerContext:
                def __init__(self, logger, context):
                    self.logger = logger
                    self.context = context
                    self.original_log = logger._log

                def __enter__(self):
                    def new_log(level, msg, *args, **kwargs):
                        if "extra" not in kwargs:
                            kwargs["extra"] = {}
                        kwargs["extra"].update(self.context)
                        self.original_log(level, msg, *args, **kwargs)

                    self.logger._log = new_log
                    return self.logger

                def __exit__(self, exc_type, exc_val, exc_tb):
                    self.logger._log = self.original_log

            with LoggerContext(logger, context):
                return func(*args, **kwargs)

        return wrapper

    return decorator


def log_execution_time(logger_name: str):
    """
    Decorator to log function execution time.

    Usage:
        @log_execution_time('module_name')
        def my_function():
            # function code here
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"Function {func.__name__} completed",
                    extra={
                        "execution_time_ms": round(execution_time * 1000, 2),
                        "function": func.__name__,
                    },
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Function {func.__name__} failed",
                    extra={
                        "execution_time_ms": round(execution_time * 1000, 2),
                        "function": func.__name__,
                        "error": str(e),
                    },
                    exc_info=True,
                )
                raise

        return wrapper

    return decorator


def add_request_context(logger: logging.Logger, request) -> None:
    """
    Add request context to a logger.

    Usage:
        logger = get_logger(__name__)
        add_request_context(logger, request)
        logger.info("Processing request")
    """
    if hasattr(request, "user"):
        logger.info(
            "Request context added",
            extra={
                "request_id": getattr(request, "id", "unknown"),
                "user_id": getattr(request.user, "id", "anonymous"),
                "method": request.method,
                "path": request.path,
                "ip": request.META.get("REMOTE_ADDR", "unknown"),
            },
        )
