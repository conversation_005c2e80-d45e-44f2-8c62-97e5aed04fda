"""
Django settings for server project.

Generated by 'django-admin startproject' using Django 4.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

import os
import sys

# NOTE: This is a workaround for the libreoffice issue on the server
# All approaches trying to set the PATH in Elastic Beanstalk failed
os.environ["PATH"] += os.pathsep + "/opt/libreoffice7.6/program"

import json

# Load ENV VARS into system
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv


def load_aws_secrets_to_env(secret_name, region_name="us-west-2"):
    """Fetch secrets from AWS Secrets Manager and load into environment variables if not already set."""
    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name="secretsmanager",
        region_name=region_name,
        aws_access_key_id=os.environ["SECRETS_ACCESS_KEY_ID"],
        aws_secret_access_key=os.environ["SECRETS_ACCESS_KEY"],
    )
    print(f"Loading secrets from AWS Secrets Manager: {secret_name}")

    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        secret_data = get_secret_value_response.get("SecretString")
        secrets = json.loads(secret_data) if secret_data else {}

        # Set each key in secrets as environment variables only if not already set
        for key, value in secrets.items():
            if not os.getenv(key):
                os.environ[key] = value
    except ClientError as e:
        print(f"Failed to retrieve secrets from AWS Secrets Manager: {e}")


# Load .env first
load_dotenv("../.env")

# Load AWS Secrets into env vars if not already set
tofu_env = os.environ.get("TOFU_ENV")

environments_to_load_secrets = {
    "development",
    "production",
    "local",
    "integration_test",
    "unit_test",
    "load_test",
}
if tofu_env in environments_to_load_secrets:
    load_aws_secrets_to_env(
        secret_name=f"tofu-backend-{tofu_env}-secrets", region_name="us-west-2"
    )

# Prevent running tests using manage.py
if "test" in sys.argv or "test_coverage" in sys.argv:
    raise RuntimeError(
        "Tests should not be run using 'python manage.py test' or 'python manage.py test_coverage'. Use pytest instead."
    )

# Get runtime environment
if os.environ.get("TOFU_ENV"):
    TOFU_ENV = os.environ.get("TOFU_ENV")
else:
    raise RuntimeError("TOFU_ENV has to be set as an environment variable")

# SECURITY WARNING: don't run with debug turned on in production!
if os.environ.get("DEBUG") == "True":
    DEBUG = True
else:
    DEBUG = False

# make sure local db is used when running tests
if os.environ.get("LOCAL_DB") == "True":
    LOCAL_DB = True
else:
    LOCAL_DB = False

DATA_UPLOAD_MAX_MEMORY_SIZE = 52428800  # 50MB

# Set up the logging configuration
import logging
import re
import sys

from .logging_config import setup_json_logging

# Keep existing logging configuration
logging.basicConfig(
    level=logging.INFO,  # Change this to the desired logging level
    format="%(asctime)s %(levelname)s: %(message)s",  # Change this to the desired log format
    handlers=[
        logging.StreamHandler(sys.stdout)
    ],  # Add a StreamHandler that logs to stdout
)

# Add JSON logging alongside existing logging
setup_json_logging()

# Only modify logging if the env variable is set
if os.environ.get("TOFU_ENABLE_DB_LOG", ""):
    # Define the slow query filter
    class SlowQueryFilter(logging.Filter):
        def __init__(self):
            super().__init__()
            self.threshold = float(os.environ.get("TOFU_SLOW_QUERY_THRESHOLD", "1.0"))

        def filter(self, record):
            # Check if this record has a duration attribute
            if hasattr(record, "duration"):
                return record.duration > self.threshold
            return False

    # Define the Django logging configuration
    LOGGING = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "sql": {
                "format": "%(levelname)s [%(asctime)s] %(message)s",
            },
            "json": {
                "()": "server.logging_config.CustomJsonFormatter",
                "format": "%(timestamp)s %(level)s %(name)s %(module)s %(function)s %(line)s %(message)s",
            },
        },
        "filters": {
            "slow_queries": {
                "()": "server.settings.SlowQueryFilter",
            }
        },
        "handlers": {
            "console": {
                "level": "DEBUG",
                "class": "logging.StreamHandler",
                "formatter": "sql",
                "filters": ["slow_queries"],
            },
            "json_console": {
                "level": "DEBUG",
                "class": "logging.StreamHandler",
                "formatter": "json",
                "filters": ["slow_queries"],
            },
        },
        "loggers": {
            "django.db.backends": {
                "handlers": ["console", "json_console"],
                "level": "DEBUG",
                "propagate": False,
            },
            "celery": {
                "handlers": ["json_console"],
                "level": "INFO",
                "propagate": False,
            },
            "celery.task": {
                "handlers": ["json_console"],
                "level": "INFO",
                "propagate": False,
            },
            "celery.worker": {
                "handlers": ["json_console"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }

    threshold = os.environ.get("TOFU_SLOW_QUERY_THRESHOLD", "1.0")

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get("SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("No SECRET_KEY found in environment")

ALLOWED_HOSTS = ["*"]

from corsheaders.defaults import default_headers

CORS_ORIGIN_ALLOW_ALL = True
# we whitelist localhost:3000 because that's where frontend will be served
# CORS_ORIGIN_WHITELIST = (
#     'http://localhost:3000',
# )
CORS_ALLOW_HEADERS = list(default_headers) + [
    "x-e2e-test",
    "x-coverage-flag",
    "cache-control",
]

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "api.auth.TofuAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
}
AUTH_USER_MODEL = "api.TofuUser"

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": os.environ.get("CACHE_BACKEND"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    },
}

# Configure django-ratelimit to use the ratelimit cache
RATELIMIT_USE_CACHE = "default"
RATELIMIT_ENABLE = True

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_filters",
    "django_svelte_jsoneditor",
    "corsheaders",
    "rest_framework",
    "drf_yasg",
    "api",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "api.request_middleware.RequestMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "api.metrics.APIMetricsMiddleware",
]

ROOT_URLCONF = "server.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "server.wsgi.application"

ASGI_APPLICATION = "server.asgi.application"

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

DATABASES = {
    # 'default': {
    #     'ENGINE': 'django.db.backends.sqlite3',
    #     'NAME': BASE_DIR / 'db.sqlite3',
    # }
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "tofudev" if LOCAL_DB else os.environ.get("DB_NAME"),
        "USER": "postgres" if LOCAL_DB else os.environ.get("DB_USER"),
        "PASSWORD": "postgres" if LOCAL_DB else os.environ.get("DB_PASSWORD"),
        "HOST": "127.0.0.1" if LOCAL_DB else os.environ.get("DB_HOST"),
        "PORT": "5432" if LOCAL_DB else os.environ.get("DB_PORT"),
        "TEST": {
            "NAME": "tofu_test",
        },
        "CONN_MAX_AGE": int(
            os.getenv("DB_CONN_MAX_AGE", 600)
        ),  # Increase to 600 seconds (10 minutes)
        "OPTIONS": {
            "options": "-c statement_timeout=0 -c idle_in_transaction_session_timeout=0",  # Disable statement and idle timeouts
        },
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "US/Pacific"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/
STATIC_URL = "/static/"
STATIC_ROOT = "static"

# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Celery definition
CELERY_BROKER_URL = os.environ.get("CELERY_BROKER_URL")
CELERY_RESULT_BACKEND = os.environ.get("CELERY_RESULT_BACKEND")
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE
CELERY_ENABLE_UTC = False
CELERY_BEAT_SCHEDULER = "redbeat.RedBeatScheduler"
REDBEAT_REDIS_URL = os.environ.get("REDBEAT_REDIS_URL")  ## need to change to /2
REDBEAT_LOCK_TIMEOUT = 600

# Celery Insights configurations
CELERY_WORKER_SEND_TASK_EVENTS = True  # Enables task events
CELERY_TASK_SEND_SENT_EVENT = True  # Enables sent events
CELERY_TASK_TRACK_STARTED = True  # Update task result state to STARTED
CELERY_RESULT_EXTENDED = True  # Store args and kwargs in the result

if TOFU_ENV == "production":
    from celery.schedules import crontab

    CELERY_BEAT_SCHEDULE = {
        "export_auto_sync": {
            "task": "export_auto_sync",
            "schedule": crontab(minute="*"),  # run every minute
            "options": {},
        },
    }
    CELERY_WORKER_CONCURRENCY = 32
elif TOFU_ENV == "development":
    from celery.schedules import crontab

    CELERY_BEAT_SCHEDULE = {
        "daily_health_check": {
            "task": "health_check",
            # schedule format: https://docs.celeryq.dev/en/stable/userguide/periodic-tasks.html#crontab-schedules
            "schedule": crontab(minute=0, hour=3),  # run 3am everyday
            "options": {},
        },
        "clean_llm_cache": {
            "task": "clean_llm_cache",
            "schedule": crontab(minute=30, hour=2),  # run 2:30am everyday
            "options": {},
        },
        "clean_e2e_test_accounts": {
            "task": "clean_e2e_test_accounts",
            "schedule": crontab(minute=0, hour=4),  # run 4am everyday
            "options": {},
        },
    }
    CELERY_WORKER_CONCURRENCY = 48

# Change default settings for celery
CELERY_TASK_ACKS_LATE = True  # Only acknowledge tasks after successful execution
CELERY_WORKER_MAX_TASKS_PER_CHILD = 15
CELERY_WORKER_MAX_MEMORY_PER_CHILD = 2 * 1024 * 1024
CELERY_WORKER_PREFETCH_MULTIPLIER = 1

# Set the worker_proc_alive_timeout
# Set to 60 seconds to avoid worker process being terminated prematurely
CELERY_WORKER_PROC_ALIVE_TIMEOUT = 60

# download nltk data
import ssl

prev_ssl_context = ssl._create_default_https_context
ssl._create_default_https_context = ssl._create_unverified_context
import nltk

nltk.download("punkt")
nltk.download("averaged_perceptron_tagger")
ssl._create_default_https_context = prev_ssl_context

# sentry config
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

sentry_sdk.init(
    dsn="https://<EMAIL>/***************8",
    integrations=[
        DjangoIntegration(),
    ],
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for performance monitoring.
    # We recommend adjusting this value in production.
    traces_sample_rate=(
        1.0 if TOFU_ENV in ("production", "development", "integration_test") else 0.0
    ),
    # Disable sampling for unit tests, local env
    sample_rate=(
        1.0 if TOFU_ENV in ("production", "development", "integration_test") else 0.0
    ),
    # If you wish to associate users to errors (assuming you are using
    # django.contrib.auth) you may enable sending PII data.
    send_default_pii=True,
    environment=TOFU_ENV,
)


# Pinecone
PINECONE_PLAYBOOK_INDEX_NAME = os.environ.get(
    "PINECONE_PLAYBOOK_INDEX_NAME", "playbook4"
)

# Swagger UI Basic Auth credentials
SWAGGER_UI_USERNAME = os.environ.get("SWAGGER_UI_USERNAME", "tofuadmin")
SWAGGER_UI_PASSWORD = os.environ.get("SWAGGER_UI_PASSWORD", "tofuadmin")
