from langchain_core.prompts import PromptTemplate

######## For playbook updates ########

# For generating new summaries
summarize_map_template = """Write a summary of the following:

{text}

"""
SUMMARIZE_MAP = PromptTemplate(
    template=summarize_map_template, input_variables=["text"]
)

summarize_template = """Write a {summary_length_words} words summary. Try to use only the information provided and include as many details as possible.

{text}

"""
SUMMARIZE = PromptTemplate(
    template=summarize_template, input_variables=["summary_length_words", "text"]
)

# For updating existing summary given data updates
refine_summary_template = """Your job is to refine a current summary based on changed context, so it can include the most update to date information.
We have provided the following summary that is generated from some existing context:

"{current_summary}"

Now the context has been changed, we have the opportunity to refine the existing summary (only if needed) with the specific context changes listed below.

{text}

Given the changes to context, refine the original summary and generate a new {summary_length_words} words summary.
If the changed context isn't useful, return the original summary.

Output the content of the new summary (even if it's the same as the original summary), nothing else.
"""
REFINE_SUMMARY = PromptTemplate(
    template=refine_summary_template,
    input_variables=["current_summary", "text", "summary_length_words"],
)
REFINE_SUMMARY_DOCUMENT_FORMAT = PromptTemplate(
    input_variables=["change_type", "page_content"],
    template="---{change_type}---\n{page_content}",
)

summarize_map_company_template = """Write a summary of the following text about {company_name}:

{text}

"""
SUMMARIZE_MAP_COMPANY = PromptTemplate(
    template=summarize_map_company_template, input_variables=["text", "company_name"]
)

summarize_company_template = """Write a {summary_length_words} words summary. Try to use only the information provided and include as many details as possible about {company_name} without repeating yourself.

{text}

"""
SUMMARIZE_COMPANY = PromptTemplate(
    template=summarize_company_template,
    input_variables=["summary_length_words", "text", "company_name"],
)
