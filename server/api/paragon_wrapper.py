import logging
import os
import re
import time
import uuid
from copy import deepcopy
from datetime import datetime, timedelta
from io import StringIO
from typing import Any, Dict, List, Optional, Set, Tuple

import boto3
import pandas as pd
import pytz
import requests
from requests.adapters import H<PERSON>PAdapter
from rest_framework import status
from rest_framework.response import Response
from urllib3.util.retry import Retry

from .auth import gen_paragon_token
from .logger import tofu_axiom_logger
from .utils import parse_s3_presigned_url


def parse_csv(csv_data, skip_blank_lines=True):
    # Convert the string data to a file-like object
    csv_file_like_object = StringIO(csv_data)

    # Now use pandas to read this data
    df = pd.read_csv(csv_file_like_object, skip_blank_lines=True)
    return df.to_dict(orient="records")


class BaseParagonAgent:
    def __init__(self, project_id, playbook):
        self.project_id = project_id
        self.playbook = playbook
        self.paragon_token = None
        self.paragon_subject = None

    def _is_request_success(self, status_code):
        return (
            status_code == status.HTTP_200_OK or status_code == status.HTTP_201_CREATED
        )

    def gen_paragon_token(self) -> tuple[str, str]:
        if not self.paragon_token:
            if self.playbook:
                user = self.playbook.users.first()
                if not user:
                    logging.error("Error getting user from playbook")
                    return None, None
            else:
                logging.error(f"Error getting user from playbook {self.playbook}")
                return None, None

            self.paragon_token, self.paragon_subject = gen_paragon_token(
                user, self.playbook
            )
        return self.paragon_token, self.paragon_subject

    def get_request_url(self, integration_name, api_path):
        if not api_path or not isinstance(api_path, str):
            logging.error(f"Invalid API path: {api_path}")
            return None

        if api_path[0] != "/":
            api_path = f"/{api_path}"
        request_url = f"https://api.useparagon.com/projects/{self.project_id}/sdk/proxy/{integration_name}{api_path}"
        return request_url

    def _with_retry(self, request_func, enable_retry=True, max_attempts=3):
        """
        Execute a request function with retry logic for rate limiting errors.

        Args:
            request_func: Callable that executes the HTTP request
            enable_retry: Whether to enable retry logic
            max_attempts: Maximum number of retry attempts

        Returns:
            Response object from the successful request

        Raises:
            Exception: When all retries are exhausted or non-retriable error occurs

        Note:
            Handles Marketo's special case where 606 error code is embedded in JSON
            response body while HTTP status remains 200.
        """
        RETRIABLE_STATUS_CODES = [429, 606]  # 606 is custom for Marketo rate limit
        attempt = 0
        wait_base = 10  # seconds

        while attempt < max_attempts:
            response = request_func()
            try:
                resp_json = response.json()
            except Exception:
                resp_json = {}

            # Check for Marketo rate limit error (code 606 in JSON, but status 200)
            errors = resp_json.get("errors")
            error_code = None
            if isinstance(errors, list) and errors:
                first_error = errors[0]
                if isinstance(first_error, dict):
                    error_code = first_error.get("code")

            status_code = response.status_code
            if error_code == "606":
                status_code = 606  # Treat as retriable

            if enable_retry and status_code in RETRIABLE_STATUS_CODES:
                if attempt < max_attempts - 1:
                    wait_time = (2**attempt) * wait_base
                    logging.warning(
                        f"Retriable error (status {status_code}). Retrying in {wait_time} seconds..."
                    )
                    time.sleep(wait_time)
                    attempt += 1
                    continue
                else:
                    raise Exception(f"Retriable error: {status_code} - {resp_json}")

            return response

    def get_request(self, request_url, enable_retry=True):
        paragon_token, paragon_subject = self.gen_paragon_token()
        if not paragon_token:
            return Response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data={"error": "Error getting paragon token"},
            )

        headers = {
            "content-type": "application/json",
            "Authorization": f"Bearer {paragon_token}",
        }

        def do_request():
            return requests.get(request_url, headers=headers)

        try:
            response = self._with_retry(do_request, enable_retry=enable_retry)
        except Exception as e:
            tofu_axiom_logger.log_axiom(
                event_type="paragon_request_backend",
                request_url=request_url,
                method="GET",
                error=str(e),
                paragon_subject=paragon_subject or "unknown",
            )
            raise e
        tofu_axiom_logger.log_axiom(
            event_type="paragon_request_backend",
            request_url=request_url,
            method="GET",
            response=self._safe_parse_json(response),
            paragon_subject=paragon_subject or "unknown",
        )
        return response

    def post_request(self, request_url, headers, payload, enable_retry=True):
        paragon_token, paragon_subject = self.gen_paragon_token()
        if not paragon_token:
            return Response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data={"error": "Error getting paragon token"},
            )

        headers.update(
            {
                "content-type": "application/json",
                "Authorization": f"Bearer {paragon_token}",
            }
        )

        prepared_request = requests.Request(
            "POST", request_url, headers=headers, json=payload
        ).prepare()

        def do_request():
            with requests.Session() as session:
                return session.send(prepared_request)

        try:
            response = self._with_retry(do_request, enable_retry=enable_retry)
        except Exception as e:
            tofu_axiom_logger.log_axiom(
                event_type="paragon_request_backend",
                request_url=request_url,
                method="POST",
                payload=payload,
                error=str(e),
                paragon_subject=paragon_subject or "unknown",
            )
            raise e
        tofu_axiom_logger.log_axiom(
            event_type="paragon_request_backend",
            request_url=request_url,
            method="POST",
            payload=payload,
            response=self._safe_parse_json(response),
            paragon_subject=paragon_subject or "unknown",
        )
        return response

    def patch_request(self, request_url, headers, payload, enable_retry=True):
        paragon_token, paragon_subject = self.gen_paragon_token()
        if not paragon_token:
            return Response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data={"error": "Error getting paragon token"},
            )

        headers.update(
            {
                "content-type": "application/json",
                "Authorization": f"Bearer {paragon_token}",
            }
        )

        prepared_request = requests.Request(
            "PATCH", request_url, headers=headers, json=payload
        ).prepare()

        def do_request():
            with requests.Session() as session:
                return session.send(prepared_request)

        try:
            response = self._with_retry(do_request, enable_retry=enable_retry)
        except Exception as e:
            tofu_axiom_logger.log_axiom(
                event_type="paragon_request_backend",
                request_url=request_url,
                method="PATCH",
                payload=payload,
                error=str(e),
                paragon_subject=paragon_subject or "unknown",
            )
            raise e
        tofu_axiom_logger.log_axiom(
            event_type="paragon_request_backend",
            request_url=request_url,
            method="PATCH",
            payload=payload,
            response=self._safe_parse_json(response),
            paragon_subject=paragon_subject or "unknown",
        )
        return response

    def _safe_parse_json(self, response):
        """Safely parse JSON response, returning None if parsing fails."""
        try:
            return response.json()
        except ValueError:  # includes simplejson.decoder.JSONDecodeError
            logging.warning(
                f"Failed to parse JSON response: {response.text[:200]}..."
            )  # Truncate long responses
            return None

    def get_user_integrations(self):
        request_url = f"https://api.useparagon.com/projects/{self.project_id}/sdk/me"
        user_response = self.get_request(request_url)
        if user_response.status_code != 200:
            raise Exception(
                f"Error getting user integrations from Paragon with response {user_response.text}"
            )
        try:
            user_response = user_response.json()
        except ValueError:
            logging.error(f"Invalid JSON response: {user_response.text}")
            raise Exception(f"Invalid JSON response from Paragon: {user_response.text}")
        integrations = user_response.get("integrations", {})
        return integrations


class HubspotAgent(BaseParagonAgent):
    def __init__(self, project_id, playbook):
        super().__init__(project_id, playbook)

    def get_hubspot_email_stats(self, email_id):
        api_path = (
            f"marketing-emails/v1/emails/with-statistics/{email_id}?workflowNames=true"
        )
        request_url = self.get_request_url("hubspot", api_path)
        response = self.get_request(request_url)
        if response.status_code != 200:
            if response.status_code == 404:
                pass  # email not found which is fine
            else:
                message = f"Error getting Hubspot email stats with email_id {email_id}. Response: {response.status_code} {response.text}"
                raise Exception(message)
            return {}

        response_data = response.json()
        result = response_data.get("output", {}).get("stats", {})
        return {
            "counters": result.get("counters", {}),
            "ratios": result.get("ratios", {}),
        }

    # returns a dict like:
    # 'newVisitorRawViews': 5,
    # 'exits': 3,
    # 'pageviewsMinusExits': 3,
    # 'exitsPerPageview': 0.5,
    # 'rawViews': 6,
    # 'pageBounces': 1,
    # 'pageTime': 567,
    # 'timePerPageview': 189,
    # 'standardViews': 6,
    # 'entrances': 3,
    # 'pageBounceRate': 0.3333333333333333}
    def get_hubspot_landing_page_stats(self, page_id, start_from=None):
        api_path = f"analytics/v2/reports/landing-pages/{page_id}/sources/total"
        if start_from:
            api_path += f"?start={start_from}"
        request_url = self.get_request_url("hubspot", api_path)
        response = self.get_request(request_url)
        if response.status_code != 200:
            if response.status_code == 404:
                pass
            elif response.status_code == 403:
                raise PermissionError(f"{response.text}")
            else:
                message = f"Error getting Hubspot landing page stats with page_id {page_id}. Response: {response.status_code} {response.text}"
                raise Exception(message)
            return {}
        response_data = response.json()
        result = response_data.get("output", {}).get("totals", {}) or {}
        return result

    def get_list_records(self, list_id):
        api_path = f"/crm/v3/lists/{list_id}/memberships?limit=250"
        request_url = self.get_request_url("hubspot", api_path)

        all_results = []
        while request_url is not None:
            response = self.get_request(request_url)
            if response.status_code != 200:
                message = f"Error getting Hubspot list records with list_id {list_id}. Response: {response.status_code}"
                raise Exception(message)

            response_data = response.json()
            output = response_data.get("output", {})
            results = output.get("results", [])
            all_results.extend(results)

            paging = output.get("paging", {})
            next_page = paging.get("next", {})
            after = next_page.get("after")

            if after:
                request_url = self.get_request_url(
                    "hubspot",
                    f"/crm/v3/lists/{list_id}/memberships?limit=250&after={after}",
                )
            else:
                request_url = None

        return all_results

    def get_batch_records(self, record_ids, record_type, column_names):
        api_path = f"/crm/v3/objects/{record_type}/batch/read"
        base_url = self.get_request_url("hubspot", api_path)

        batch_size = 100
        all_results = []

        for i in range(0, len(record_ids), batch_size):
            batch_record_ids = record_ids[i : i + batch_size]
            payload = {
                "inputs": [{"id": record_id} for record_id in batch_record_ids],
                "properties": column_names,
            }
            response = self.post_request(base_url, {}, payload)
            if response.status_code != 200:
                message = f"Error getting HubSpot batch records. Response: {response.status_code}"
                raise Exception(message)

            response_data = response.json()
            results = response_data.get("output", {}).get("results", [])
            formatted_results = [
                {
                    key: (
                        item["properties"].get(key)
                        if "properties" in item and key in item["properties"]
                        else item.get(key)
                    )
                    for key in column_names
                    if (
                        "properties" in item and item["properties"].get(key) is not None
                    )
                    or item.get(key) is not None
                }
                for item in results
            ]
            all_results.extend(formatted_results)

        return all_results

    def update_crm_property(self, record_type_plural, hubspot_id, properties):
        api_path = f"/crm/v3/objects/{record_type_plural}/{hubspot_id}"
        request_url = self.get_request_url("hubspot", api_path)

        headers = {}
        payload = {
            "properties": properties,
        }

        response = self.patch_request(request_url, headers, payload)
        if response.status_code != 200:
            message = f"Error updating Hubspot CRM proeprty. Response: {response.status_code} {response.text}"
            logging.error(
                f"Error updating Hubspot CRM proeprty. Response: {response.text}"
            )
            raise Exception(message)
        return response.json()

    def _create_crm_group(self, object_type, name, label):
        """Create a CRM property group in HubSpot if it doesn't already exist.

        Args:
            object_type: The HubSpot object type (e.g., 'contact', 'company')
            name: The internal name of the property group
            label: The display label for the property group
        """
        # First, check if the group already exists
        api_path = f"/crm/v3/properties/{object_type}/groups/{name}"
        request_url = self.get_request_url("hubspot", api_path)

        response = self.get_request(request_url)
        if response.status_code == 200:
            response_data = response.json().get("output", {})
            if (
                response_data.get("name") == name
                and response_data.get("archived") is False
            ):
                return response_data
        elif response.status_code != 404:
            logging.error(f"Error checking if HubSpot group exists: {response.text}")

        # Create the group
        api_path = f"/crm/v3/properties/{object_type}/groups"
        request_url = self.get_request_url("hubspot", api_path)

        headers = {}
        payload = {"name": name, "label": label}

        response = self.post_request(request_url, headers, payload)
        if response.status_code != 200 and response.status_code != 201:
            message = f"Error creating HubSpot CRM property group. Response: {response.status_code} {response.text}"
            logging.exception(message)
            raise Exception(message)

        return response.json().get("output", {})

    def create_crm_field(
        self,
        object_type,
        name,
        label,
        type="string",
        field_type="html",
        group_name="tofu",
    ):
        """Create a CRM property in HubSpot if it doesn't already exist.

        Args:
            object_type: The HubSpot object type (e.g., 'contact', 'company')
            name: The internal name of the property
            label: The display label for the property
            type: The type of the property
            field_type: How the property is displayed and interacted with in the HubSpot UI
            group_name: The name of the property group this property belongs to
        """
        # check and create property group if it doesn't exist
        self._create_crm_group(object_type, group_name, group_name)

        # First, check if the property already exists
        api_path = f"/crm/v3/properties/{object_type}/{name}"
        request_url = self.get_request_url("hubspot", api_path)

        response = self.get_request(request_url)
        if response.status_code == 200:
            response_data = response.json().get("output", {})
            if (
                response_data.get("name") == name
                and response_data.get("archived") is False
            ):
                return response_data
        elif response.status_code != 404:
            logging.error(f"Error checking if HubSpot property exists: {response.text}")

        # Create the property
        api_path = f"/crm/v3/properties/{object_type}"
        request_url = self.get_request_url("hubspot", api_path)

        headers = {}
        payload = {
            "name": name,
            "label": label,
            "type": type,
            "fieldType": field_type,
            "groupName": group_name,
        }

        response = self.post_request(request_url, headers, payload)
        if response.status_code != 200 and response.status_code != 201:
            message = f"Error creating HubSpot CRM property. Response: {response.status_code} {response.text}"
            logging.error(
                f"Error creating HubSpot CRM property. Response: {response.text}"
            )
            raise Exception(message)

        return response.json().get("output", {})

    def get_list_by_id(self, list_id):
        api_path = f"/crm/v3/lists/{list_id}"
        request_url = self.get_request_url("hubspot", api_path)
        response = self.get_request(request_url)
        if response.status_code != 200:
            raise Exception(
                f"Error getting HubSpot list by id {list_id}. Response: {response.status_code} {response.text}"
            )
        result = response.json().get("output", {})
        result_list = result.get("list", {})
        if not result_list:
            raise Exception(
                f"Error getting HubSpot list by id {list_id}. Response: {response.status_code} {response.text}"
            )
        return result_list

    def create_content_template(
        self,
        source: str,
        template_path: str,
        template_type=2,
        folder="tofu",
        category_id=2,
        is_available_for_new_content=False,
    ) -> dict:
        api_path = f"/content/api/v2/templates"
        request_url = self.get_request_url("hubspot", api_path)
        payload = {
            "category_id": category_id,
            "folder": folder,
            "is_available_for_new_content": is_available_for_new_content,
            "template_type": template_type,
            "path": template_path,
            "source": source,
        }
        response = self.post_request(request_url, {}, payload)
        if response.status_code != 200 and response.status_code != 201:
            raise Exception(
                f"Error creating HubSpot content template. Response: {response.status_code} {response.text}"
            )
        return response.json().get("output", {})

    def create_marketing_email(
        self,
        fileName: str,
        template_path: str,
        subject: str,
        email_type: str,
        authorName: str = "Tofu",
        subcategory: str = "batch",
    ) -> dict:
        api_path = "/marketing-emails/v1/emails"
        request_url = self.get_request_url("hubspot", api_path)

        payload = {
            "slug": fileName,
            "templatePath": template_path,
            "name": fileName,
            "authorName": authorName,
            "subject": subject,
            "subcategory": subcategory,  # I have no idea what this is, but looks like it's required by hubspot now.
        }

        if email_type == "automated":
            payload["emailType"] = "AUTOMATED_EMAIL"
            payload["state"] = "AUTOMATED_DRAFT"

        response = self.post_request(request_url, {}, payload)
        if response.status_code != 200 and response.status_code != 201:
            raise Exception(
                f"Error creating HubSpot marketing email. Response: {response.status_code} {response.text}"
            )
        return response.json().get("output", {})

    def get_account_details(self) -> dict:
        api_path = "/account-info/v3/details"
        request_url = self.get_request_url("hubspot", api_path)
        response = self.get_request(request_url)
        if response.status_code != 200:
            raise Exception(
                f"Error getting HubSpot account details. Response: {response.status_code} {response.text}"
            )
        return response.json().get("output", {})

    def create_company_list_from_contact(
        self,
        name: str,
        object_type_id: str,
        processing_type: str,
        original_list_id: str,
    ) -> dict:
        """
        Tries to create a company list from contact. First tries the desired name, then retries with a timestamp if duplicate.
        """
        base_name = f"{name} (converted using Tofu)".strip()
        filter_branch = {
            "filterBranches": [
                {
                    "filterBranches": [
                        {
                            "filterBranches": [],
                            "filters": [
                                {
                                    "listId": original_list_id,
                                    "operator": "IN_LIST",
                                    "filterType": "IN_LIST",
                                }
                            ],
                            "objectTypeId": "0-1",
                            "operator": "IN_LIST",
                            "associationTypeId": 280,
                            "associationCategory": "HUBSPOT_DEFINED",
                            "filterBranchOperator": "AND",
                            "filterBranchType": "ASSOCIATION",
                        }
                    ],
                    "filters": [],
                    "filterBranchOperator": "AND",
                    "filterBranchType": "AND",
                }
            ],
            "filters": [],
            "filterBranchOperator": "OR",
            "filterBranchType": "OR",
        }

        def try_create(list_name) -> Tuple[dict, bool]:
            body = {
                "name": list_name,
                "objectTypeId": object_type_id,
                "processingType": processing_type,
                "filterBranch": filter_branch,
            }
            api_path = "/crm/v3/lists/"
            request_url = self.get_request_url("hubspot", api_path)
            response = self.post_request(request_url, {}, body)
            if response.status_code == 400:
                error_json = response.json()
                output = error_json.get("output", {})
                sub_category = output.get("subCategory")
                if sub_category == "ILS.DUPLICATE_LIST_NAMES":
                    return None, True
            return response, False

        response, is_duplicate = try_create(base_name)
        if is_duplicate:
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            name = f"{base_name} - {timestamp}"
            response, _ = try_create(name)

        if response.status_code != 200 and response.status_code != 201:
            raise Exception(
                f"Error creating HubSpot company list from contact. Response: {response.status_code} {response.text}"
            )
        return response.json().get("output", {}).get("list", {})


class MarketoAgent(BaseParagonAgent):
    EMAIL_ACTIVITY_MAP = {
        6: "sent",
        7: "delivered",
        8: "bounced",
        9: "unsubscribed",
        10: "opened",
        11: "clicked",
    }

    def __init__(self, project_id, playbook):
        super().__init__(project_id, playbook)
        self.marketo_endpoint = self.get_marketo_endpoint()

    def get_marketo_endpoint(self):
        request_url = f"https://api.useparagon.com/projects/{self.project_id}/sdk/me"
        user_response = self.get_request(request_url)
        if user_response.status_code != 200:
            logging.error(
                f"Error getting Marketo endpoint from Paragon with response {user_response.text}"
            )
            raise Exception(
                f"Error getting Marketo endpoint from Paragon with response {user_response.text}"
            )
        user_response = user_response.json()
        integrations = user_response.get("integrations", {})
        marketo_credentials = integrations.get("marketo", {}).get("allCredentials", {})
        if not marketo_credentials:
            logging.error(
                f"Error getting Marketo credentials from Paragon with integrations {integrations}"
            )
            raise Exception(
                f"Error getting Marketo credentials from Paragon with integrations {integrations}"
            )

        marketo_credential = marketo_credentials[0]
        marketo_endpoint = marketo_credential.get("providerData", {}).get(
            "endpointUrl", ""
        )
        if not marketo_endpoint:
            logging.error("Error getting Marketo endpoint from Paragon")
            raise Exception("Error getting Marketo endpoint from Paragon")
        marketo_endpoint = marketo_endpoint.strip()
        if marketo_endpoint.endswith("/rest"):
            marketo_endpoint = marketo_endpoint[:-5]

        return marketo_endpoint

    def get_marketo_nextpagetoken(self):
        day30_datetime = datetime.now() - timedelta(days=30)
        marketo_endpoint = self.marketo_endpoint
        day30_datetime = day30_datetime.strftime("%Y-%m-%dT%H:%M:%SZ")
        api_path = f"{marketo_endpoint}/rest/v1/activities/pagingtoken.json?sinceDatetime={day30_datetime}"
        request_url = self.get_request_url("marketo", api_path)
        result = self.get_request(request_url)
        if result.status_code != 200:
            logging.error(
                f"Error getting pageToken from Paragon with response {result.text}"
            )
            raise Exception(
                f"Error getting pageToken from Paragon with response {result.text}"
            )
        result = result.json()
        if (
            not result
            or not isinstance(result, dict)
            or "output" not in result
            or "nextPageToken" not in result["output"]
        ):
            logging.error(f"Error getting pageToken from Paragon: {result}")
            raise Exception(f"Error getting pageToken from Paragon: {result}")
        return result["output"]["nextPageToken"]

    def get_marketo_activities(self, activity_type_ids, asset_ids):
        page_token = self.get_marketo_nextpagetoken()
        marketo_endpoint = self.marketo_endpoint

        str_activity_type_ids = ",".join([str(i) for i in activity_type_ids])

        all_activities = []
        while True:
            api_path = f"{marketo_endpoint}/rest/v1/activities.json?activityTypeIds={str_activity_type_ids}&nextPageToken={page_token}&assetIds={asset_ids}"
            request_url = self.get_request_url("marketo", api_path)
            response = self.get_request(request_url)
            if response.status_code != 200:
                logging.error(
                    f"Error getting Marketo activities from Paragon with response {response.text}"
                )
                raise Exception(
                    f"Error getting Marketo activities from Paragon with response {response.text}"
                )
            response_data = response.json()
            # check if this fails

            activities = response_data.get("output", {}).get("result", [])
            all_activities.extend(activities)

            # Check if there are more
            more_results = response_data.get("output", {}).get("moreResult")
            if more_results != True:
                break

            # Update the nextPageToken for the next request
            page_token = response_data.get("output", {}).get("nextPageToken")
        return all_activities

    def get_marketo_landing_page_stats(self, page_id):
        raise Exception("Not implemented")

        # APIs
        # query landing page:
        #   /asset/v1/landingPage/{page_id}.json
        # acitivities:
        #   1: visit web page

    def get_marketo_email_stats(self, email_id):
        activity_type_map = self.EMAIL_ACTIVITY_MAP

        all_activities = self.get_marketo_activities(
            list(activity_type_map.keys()),
            email_id,
        )

        current_time = datetime.now(pytz.utc)

        def within_x_day(activity_date_str, num_of_days):
            activity_date = datetime.strptime(activity_date_str, "%Y-%m-%dT%H:%M:%S%z")
            time_difference = current_time - activity_date
            return time_difference <= timedelta(days=num_of_days)

        activities_1_day = [
            x for x in all_activities if within_x_day(x["activityDate"], 1)
        ]
        activities_7_day = [
            x for x in all_activities if within_x_day(x["activityDate"], 7)
        ]
        activities_30_day = [
            x for x in all_activities if within_x_day(x["activityDate"], 30)
        ]

        def get_metrics(activities):
            counts = {name: 0 for name in activity_type_map.values()}

            # Count occurrences of each activity type
            for activity in activities:
                activity_type = activity.get("activityTypeId")
                if activity_type in activity_type_map:
                    counts[activity_type_map[activity_type]] += 1

            ratios = {}
            if counts["sent"] > 0:
                ratios["openratio"] = counts["opened"] / counts["sent"] * 100
                ratios["clickratio"] = counts["clicked"] / counts["delivered"] * 100
                ratios["clickthroughratio"] = counts["clicked"] / counts["opened"] * 100
                ratios["deliveredratio"] = counts["delivered"] / counts["sent"] * 100
                ratios["unsubscribedratio"] = (
                    counts["unsubscribed"] / counts["sent"] * 100
                )
            else:
                ratios["openratio"] = 0
                ratios["clickratio"] = 0
                ratios["clickthroughratio"] = 0
                ratios["deliveredratio"] = 0
                ratios["unsubscribedratio"] = 0

            return {"counters": counts, "ratios": ratios}

        total_metrics = get_metrics(all_activities)
        metrics_1_day = get_metrics(activities_1_day)
        metrics_7_day = get_metrics(activities_7_day)
        metrics_30_day = get_metrics(activities_30_day)
        return total_metrics, metrics_1_day, metrics_7_day, metrics_30_day

    def get_list_records(self, list_id, list_type, fields_to_include):
        fields_to_include = list(set(fields_to_include))

        # step 1: create export job
        api_path = f"{self.marketo_endpoint}/bulk/v1/leads/export/create.json"

        body = {
            "fields": fields_to_include,
            "format": "CSV",
            "filter": {
                f"{list_type.lower()}ListId": list_id,
            },
        }
        request_url = self.get_request_url("marketo", api_path)
        response = self.post_request(request_url, {}, body)
        if response.status_code != 200:
            logging.error(
                f"Error creating Marketo export job with response {response.text}"
            )
            raise Exception(
                f"Error creating Marketo export job with response {response.text}"
            )

        response_data = response.json()
        if response_data.get("output", {}).get("success") != True:
            logging.error(
                f"Error creating Marketo export job with response {response_data}"
            )
            raise Exception(
                f"Error creating Marketo export job with response {response_data}"
            )
        result_data = response_data.get("output", {}).get("result", [])
        if not result_data:
            logging.error(
                f"Error creating Marketo export job with response {result_data}"
            )
            raise Exception(
                f"Error creating Marketo export job with response {result_data}"
            )
        if result_data[0].get("status") != "Created":
            logging.error(
                f"Error creating Marketo export job with response {result_data}"
            )
            raise Exception(
                f"Error creating Marketo export job with response {result_data}"
            )

        if not result_data[0].get("exportId"):
            logging.error(
                f"Error creating Marketo export job with response {result_data}"
            )
            raise Exception(
                f"Error creating Marketo export job with response {result_data}"
            )

        export_id = result_data[0].get("exportId")
        if not export_id:
            logging.error(
                f"Error creating Marketo export job with response {response_data}"
            )
            raise Exception(
                f"Error creating Marketo export job with response {response_data}"
            )

        # step 2: enqueue export job
        api_path = (
            f"{self.marketo_endpoint}/bulk/v1/leads/export/{export_id}/enqueue.json"
        )
        request_url = self.get_request_url("marketo", api_path)
        response = self.post_request(request_url, {}, {})
        if response.status_code != 200:
            logging.error(
                f"Error enqueueing Marketo export job with response {response.text}"
            )
            raise Exception(
                f"Error enqueueing Marketo export job with response {response.text}"
            )

        # step 3: poll job status
        max_wait_time = 10 * 60  # 10 minutes in seconds
        start_time = time.time()
        while True:
            api_path = (
                f"{self.marketo_endpoint}/bulk/v1/leads/export/{export_id}/status.json"
            )
            request_url = self.get_request_url("marketo", api_path)
            response = self.get_request(request_url)
            if response.status_code != 200:
                logging.error(
                    f"Error polling Marketo export job with response {response.text}"
                )
                raise Exception(
                    f"Error polling Marketo export job with response {response.text}"
                )

            response_data = response.json()
            output_data = response_data.get("output", {})
            if output_data.get("success") != True:
                logging.error(
                    f"Error polling Marketo export job with response {response_data}"
                )
                raise Exception(
                    f"Error polling Marketo export job with response {response_data}"
                )
            if output_data.get("result", [])[0].get("status") == "Completed":
                break
            if time.time() - start_time >= max_wait_time:
                logging.error("Timeout: Export took too long to complete.")
                raise Exception("Timeout: Export took too long to complete.")
            time.sleep(5)  # Sleep for 5 seconds

        # step 4: get exported data
        api_path = f"{self.marketo_endpoint}/bulk/v1/leads/export/{export_id}/file.json"
        request_url = self.get_request_url("marketo", api_path)
        response = self.get_request(request_url)
        if response.status_code != 200:
            logging.error(
                f"Error getting Marketo export job with response {response.text}"
            )
            raise Exception(
                f"Error getting Marketo export job with response {response.text}"
            )

        csv_data = response.json().get("output", "")
        parsed_data = parse_csv(csv_data, skip_blank_lines=True)

        def process_row(row):
            return {
                key: ("" if value == "null" else value) for key, value in row.items()
            }

        processed_data = [process_row(row) for row in parsed_data]
        return processed_data

    def update_crm_property(self, marketo_id, generations):
        api_path = f"{self.marketo_endpoint}/rest/v1/leads.json"
        request_url = self.get_request_url("marketo", api_path)
        generations["id"] = marketo_id

        headers = {}
        payload = {
            "action": "updateOnly",
            "lookupField": "id",
            "input": [generations],
        }

        response = self.post_request(request_url, headers, payload)
        if response.status_code != 200:
            message = (
                f"Error updating Marketo CRM proeprty. Response: {response.status_code}"
            )
            logging.error(
                f"Error updating Marketo CRM proeprty. Response: {response.text}"
            )
            raise Exception(message)
        # further check the success
        if response.json().get("output", {}).get("success") != True:
            message = f"Error updating Marketo CRM proeprty. Response: {response.text}"
            logging.error(
                f"Error updating Marketo CRM proeprty. Response: {response.text}"
            )
            raise Exception(message)
        return response.json()

    def batch_update_crm_property(self, update_data):
        api_path = f"{self.marketo_endpoint}/rest/v1/leads.json"
        request_url = self.get_request_url("marketo", api_path)

        headers = {}
        payload = {
            "action": "updateOnly",
            "lookupField": "id",
            "input": update_data,
        }

        response = self.post_request(request_url, headers, payload)
        if response.status_code != 200:
            message = (
                f"Error updating Marketo CRM proeprty. Response: {response.status_code}"
            )
            logging.error(
                f"Error updating Marketo CRM proeprty. Response: {response.text}"
            )
            raise Exception(message)
        return response.json()


class SalesforceAgent(BaseParagonAgent):
    def __init__(self, project_id, playbook):
        super().__init__(project_id, playbook)

        self.common_headers = {}

    def get_list_records(
        self,
        list_id: str,
        record_type: str,
        list_type: str,
        properties: Optional[List[str]] = None,
    ) -> List[Dict[str, Any]]:
        if properties is None:
            properties = []
        list_data = {"id": list_id, "type": record_type, "listType": list_type}
        return self.import_list_or_report(list_data, properties)

    def get_all_object_type_properties(self, object_type: str) -> Dict[str, Any]:
        allowed_object_types = [
            "Contact",
            "Account",
            "Lead",
        ]  # we don't support Opportunity yet
        if object_type not in allowed_object_types:
            raise ValueError(f"Unsupported object type: {object_type}")

        def fetch_data_and_modify_fields(
            object_type: str, prefix: str = ""
        ) -> List[Dict[str, Any]]:
            response = self.get_request(
                self.get_request_url("salesforce", f"sobjects/{object_type}/describe/")
            )
            if response.status_code != 200:
                raise Exception(
                    f"Error fetching Salesforce object properties: {response.text}"
                )

            fields = response.json().get("output", {}).get("fields", [])
            if prefix:
                return [
                    {
                        **field,
                        "name": f"{prefix}.{field['name']}",
                        "label": f"{prefix}.{field['label']}",
                    }
                    for field in fields
                ]
            return fields

        data1_fields = fetch_data_and_modify_fields(object_type)
        data2_fields = (
            fetch_data_and_modify_fields("Account", "Account")
            if object_type == "Contact"
            else []
        )

        fields = data1_fields + data2_fields

        if fields:
            properties = sorted(
                [
                    {
                        "id": result["name"],
                        "name": result["name"],
                        "label": result["label"],
                        "fieldType": result["type"],
                    }
                    for result in fields
                ],
                key=lambda x: x["label"],
            )

            return {prop["name"]: prop for prop in properties}
        return {}

    def get_sub_target_value_from_list(
        self,
        list_item: Dict[str, Any],
        table_data: List[Dict[str, Any]],
        object_type_properties: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        filtered_table_data = [
            item
            for item in table_data
            if item.get("dataType") not in ["subtarget_name", "salesforce_identifier"]
        ]
        return [
            {
                "id": str(uuid.uuid4()),
                "type": table_data_item.get("dataType"),
                "meta": {
                    "field_name": table_data_item.get("columnName"),
                    "label": object_type_properties.get(
                        table_data_item.get("columnName", ""), {}
                    ).get("label"),
                },
                "value": list_item.get(table_data_item.get("columnName", "")),
            }
            for table_data_item in filtered_table_data
        ]

    def import_list_or_report(
        self, list_data: Dict[str, Any], properties: List[str]
    ) -> List[Dict[str, Any]]:
        if list_data["listType"] == "List":
            return self.import_list(list_data, properties)
        elif list_data["listType"] == "Report":
            return self.import_report(list_data, properties)
        else:
            raise ValueError(f"Unsupported list type: {list_data['listType']}")

    def import_list(
        self, list_data: Dict[str, Any], properties: List[str]
    ) -> List[Dict[str, Any]]:
        final_records = []
        has_more = True
        offset = 0
        limit = 2000
        all_record_ids = []

        while has_more:
            try:
                # Include properties in the request URL
                request_url = self.get_request_url(
                    "salesforce",
                    f"sobjects/{list_data['type']}/listviews/{list_data['id']}/results?limit={limit}&offset={offset}",
                )
                response = self.get_request(request_url)

                if response.status_code != 200:
                    raise Exception(
                        f"Error getting Salesforce list records: {response.text}"
                    )

                output_data = response.json().get("output", {})
                records = output_data.get("records", [])

                # Extract all the "Id" from each record
                record_ids = []
                for record in records:
                    for column in record.get("columns", []):
                        if column.get("fieldNameOrPath") == "Id":
                            record_ids.append(column.get("value"))
                            break

                # Add the IDs to our collection
                all_record_ids.extend(record_ids)

                has_more = not output_data.get("done", True)
                if has_more:
                    offset += len(records)

            except Exception as e:
                logging.exception(f"Error in import_list: {str(e)}")
                raise

        # Get detailed records using the IDs we collected
        if all_record_ids:
            return self.fetch_records_by_ids(
                all_record_ids, list_data["type"], properties
            )

        return []

    def import_report(
        self, report: Dict[str, Any], properties: List[str]
    ) -> List[Dict[str, Any]]:
        if not properties:
            raise ValueError(f"properties is empty for {report['type']}")

        record_ids = self.get_record_ids_from_report(report["id"], [report["type"]])
        records = self.fetch_records_by_ids(
            record_ids.get(report["type"], []), report["type"], properties
        )
        return records

    def pull_report_data_and_ids(
        self,
        report_id: str,
        report_metadata: Dict[str, Any],
        detail_columns: List[str],
        id_field_api_names: Dict[str, str],
    ) -> Dict[str, List[str]]:
        try:
            pagination = 1
            all_extracted_ids: Dict[str, Set[str]] = {}

            # Initialize Sets for each sobjectType
            for sobject_type in id_field_api_names.keys():
                all_extracted_ids[sobject_type] = set()

            while pagination <= 3:  # at most 3 pages, 6000 records
                request_body = {
                    "reportMetadata": {
                        **report_metadata,
                        "detailColumns": detail_columns,
                    }
                }

                # Add filters for each sobjectType
                if any(len(id_set) > 0 for id_set in all_extracted_ids.values()):
                    filters = []
                    for sobject_type, id_set in all_extracted_ids.items():
                        if len(id_set) > 0:
                            filters.append(
                                {
                                    "column": id_field_api_names[sobject_type],
                                    "filterType": "fieldValue",
                                    "isRunPageEditable": True,
                                    "operator": "notEqual",
                                    "value": ",".join(id_set),
                                }
                            )
                    request_body["reportMetadata"]["reportFilters"] = [
                        *request_body["reportMetadata"].get("reportFilters", []),
                        *filters,
                    ]

                api_path = f"analytics/reports/{report_id}"
                response = self.post_request(
                    self.get_request_url("salesforce", api_path),
                    self.common_headers,
                    request_body,
                )

                if response.status_code != 200:
                    raise Exception(f"Error running report: {response.text}")

                next_page_data = response.json().get("output", {})
                fact_map = next_page_data.get("factMap", {})
                rows_collected = False

                # Extract IDs for the available sObjectTypes
                for section in fact_map.values():
                    if section and section.get("rows"):
                        rows_collected = True
                        for row in section["rows"]:
                            for sobject_type, id_field in id_field_api_names.items():
                                id_index = detail_columns.index(id_field)
                                if id_index == -1:
                                    raise Exception(
                                        f"Failed to import {sobject_type} ID from report {report_id}. Are there any {sobject_type} records in the report?"
                                    )
                                if row["dataCells"][id_index].get("value"):
                                    all_extracted_ids[sobject_type].add(
                                        row["dataCells"][id_index]["value"]
                                    )

                if not rows_collected:
                    break

                if next_page_data.get("allData"):
                    break

                pagination += 1

            # Check if we collected any IDs after processing all pages
            if all(len(id_set) == 0 for id_set in all_extracted_ids.values()):
                pass  # we are throwing an exception on the frontend but not here because in autopilot, there could be a case that there is no data in the report

            # Convert Sets to arrays and return
            return {
                sobject_type: list(id_set)
                for sobject_type, id_set in all_extracted_ids.items()
            }

        except Exception as e:
            logging.exception(f"Error in pull_report_data_and_ids: {str(e)}")
            raise

    def get_record_ids_from_report(
        self, report_id: str, sobject_types: List[str]
    ) -> Dict[str, List[str]]:
        # Step 1: Retrieve the existing report metadata
        api_path = f"analytics/reports/{report_id}/describe"
        report_describe_response = self.get_request(
            self.get_request_url("salesforce", api_path)
        )
        if report_describe_response.status_code != 200:
            raise Exception(
                f"Error getting report metadata: {report_describe_response.text}"
            )

        output_data = report_describe_response.json().get("output", {})

        report_metadata = output_data.get("reportMetadata", {})
        detail_columns = report_metadata.get("detailColumns", [])
        report_type_metadata = output_data.get("reportTypeMetadata", {})

        if not report_metadata:
            logging.error(f"No report metadata found for report {report_id}")
            raise Exception(f"No report metadata found for report {report_id}")

        if not report_type_metadata:
            logging.error(f"No report metadata found for report {report_id}")
            raise Exception(f"No report metadata found for report {report_id}")

        # Step 2: Build a map of available columns from reportTypeMetadata
        available_columns = {}
        for category in report_type_metadata.get("categories", []):
            for field_key, field_info in category.get("columns", {}).items():
                available_columns[field_key] = field_info
        if not available_columns:
            raise Exception(f"No available columns found for report {report_id}")

        # Step 3: For each sObjectType, attempt to find and add its Id field
        id_field_api_names = {}  # Map of sObjectType to its Id field API name
        for sobject_type in sobject_types:
            # Search for the Id field for the sObjectType
            id_field_entry = next(
                (
                    (field_key, field_info)
                    for field_key, field_info in available_columns.items()
                    if field_key == f"{sobject_type.upper()}_ID"
                    and field_info.get("dataType") == "id"
                ),
                None,
            )

            if id_field_entry:
                id_field_api_name, _ = id_field_entry
                id_field_api_names[sobject_type] = id_field_api_name

                # Add the ID field to detailColumns if not already included
                if id_field_api_name not in detail_columns:
                    detail_columns.insert(0, id_field_api_name)
            else:
                raise Exception(
                    f"Failed to import {sobject_type} ID from report {report_id}. Are there any {sobject_type} records in the report?"
                )

        # Step 4: Poll the report data and extract the IDs
        report_data = self.pull_report_data_and_ids(
            report_id, report_metadata, detail_columns, id_field_api_names
        )
        return report_data

    def fetch_records_by_ids(
        self, ids: List[str], record_type: str, properties: List[str]
    ) -> List[Dict[str, Any]]:
        if not properties:
            raise ValueError(f"properties is empty for {record_type}")

        MAX_BATCH_SIZE = 200
        all_records = []

        # Sanitize inputs
        sanitized_record_type = self.sanitize_soql_input(record_type)
        sanitized_properties = [self.sanitize_soql_input(prop) for prop in properties]

        for i in range(0, len(ids), MAX_BATCH_SIZE):
            batch_ids = ids[i : i + MAX_BATCH_SIZE]
            sanitized_ids = [self.sanitize_soql_input(id) for id in batch_ids]
            id_string = "', '".join(sanitized_ids)
            query = f"SELECT {', '.join(sanitized_properties)} FROM {sanitized_record_type} WHERE Id IN ('{id_string}')"
            api_path = f"query/?q={requests.utils.quote(query)}"

            request_url = self.get_request_url("salesforce", api_path)
            response = self.get_request(request_url)

            if response.status_code != 200:
                raise Exception(
                    f"Error fetching Salesforce records with IDs {batch_ids}: {response.text}"
                )

            data = response.json().get("output", {})
            if data and "records" in data:
                all_records.extend(data["records"])
            else:
                break

        return self.flatten_accounts_for_records(all_records)

    def flatten_accounts(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        flattened_records = []
        for record in records:
            flattened = {}
            columns = record.get("columns", [])
            for column in columns:
                field_name = column.get("fieldNameOrPath")
                if not field_name:
                    logging.error(f"field_name is empty for {column}")
                    continue
                value = column.get("value")
                flattened[field_name] = value
            flattened_records.append(flattened)
        return flattened_records

    def flatten_accounts_for_records(
        self, records: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        flattened_records = []
        for record in records:
            flattened = {}
            for key, value in record.items():
                if isinstance(value, dict):
                    for k, v in value.items():
                        flattened[f"{key}.{k}"] = v
                else:
                    flattened[key] = value
            if not flattened:
                logging.error(f"flattened is empty for {record}")
            flattened_records.append(flattened)
        return flattened_records

    def get_missing_fields(self, object_type: str, field_names: List[str]) -> List[str]:
        # Sanitize object_type
        sanitized_object_type = self.sanitize_soql_input(object_type)

        # Sanitize field names
        sanitized_field_names = [self.sanitize_soql_input(name) for name in field_names]
        field_names_str = "', '".join(sanitized_field_names)

        query = f"SELECT QualifiedApiName FROM FieldDefinition WHERE EntityDefinition.QualifiedApiName = '{sanitized_object_type}' AND QualifiedApiName IN ('{field_names_str}')"
        response = self.get_request(
            self.get_request_url(
                "salesforce", f"tooling/query/?q={requests.utils.quote(query)}"
            )
        )

        if response.status_code != 200:
            raise Exception(
                f"Error getting Salesforce field definitions: {response.text}"
            )

        data = response.json().get("output", {})
        existing_fields = [
            record["QualifiedApiName"] for record in data.get("records", [])
        ]
        return [name for name in field_names if name not in existing_fields]

    def sanitize_soql_input(self, input_string: str) -> str:
        """
        Sanitize input for SOQL query to prevent injection.
        """
        # Remove any single quotes and replace with two single quotes (SOQL escape character)
        sanitized = input_string.replace("'", "''")

        # Remove any semicolons
        sanitized = sanitized.replace(";", "")
        return sanitized

    def create_salesforce_html_fields(
        self, object_type: str, field_names: List[str]
    ) -> Dict[str, Any]:
        missing_field_names = self.get_missing_fields(object_type, field_names)

        created_fields = []
        for field_name in missing_field_names:
            label = field_name.replace("__c", "")
            response = self.post_request(
                self.get_request_url("salesforce", "tooling/sobjects/CustomField/"),
                {},
                {
                    "Metadata": {
                        "label": label,
                        "type": "Html",
                        "length": 32768,
                        "visibleLines": 25,
                        "required": False,
                        "inlineHelpText": "Field created by TOFU",
                        "description": "Field created by TOFU",
                    },
                    "FullName": f"{object_type}.{field_name}",
                },
            )

            created_fields.append(
                {
                    "label": label,
                    "fieldName": field_name,
                    "success": response.status_code < 300,
                    "response": (
                        response.json() if response.status_code < 300 else response.text
                    ),
                }
            )

        # Add field level security for the new fields
        profiles_response = self.get_request(
            self.get_request_url(
                "salesforce", "tooling/query/?q=SELECT+Id,Name+FROM+Profile"
            )
        )
        if profiles_response.status_code != 200:
            raise Exception(
                f"Error getting Salesforce profiles: {profiles_response.text}"
            )

        profiles_response_output = profiles_response.json().get("output", {})
        all_profiles = profiles_response_output.get("records", [])

        security_results = []
        for field_name in missing_field_names:
            for profile in all_profiles:
                response = self.post_request(
                    self.get_request_url("salesforce", "composite/"),
                    {},
                    {
                        "allOrNone": True,
                        "compositeRequest": [
                            {
                                "referenceId": "Profile",
                                "url": f"/services/data/v60.0/query/?q=SELECT+Id+FROM+Profile+Where+Name='{profile['Name']}'",
                                "method": "GET",
                            },
                            {
                                "referenceId": "PermissionSet",
                                "url": "/services/data/v60.0/query/?q=SELECT+Id+FROM+PermissionSet+WHERE+ProfileId='@{Profile.records[0].Id}'",
                                "method": "GET",
                            },
                            {
                                "referenceId": "NewFieldPermission",
                                "body": {
                                    "ParentId": "@{PermissionSet.records[0].Id}",
                                    "SobjectType": object_type,
                                    "Field": f"{object_type}.{field_name}",
                                    "PermissionsEdit": "true",
                                    "PermissionsRead": "true",
                                },
                                "url": "/services/data/v60.0/sobjects/FieldPermissions/",
                                "method": "POST",
                            },
                        ],
                    },
                )
                security_results.append(
                    {
                        "fieldName": field_name,
                        "success": response.status_code == 200,
                        "response": (
                            response.json()
                            if response.status_code == 200
                            else response.text
                        ),
                    }
                )

        return {
            "createdFields": created_fields,
            "securityResults": security_results,
            "missingFieldsInitially": missing_field_names,
        }

    def update_object_fields(
        self, salesforce_ids: Dict[str, str], data_to_push: Dict[str, Any]
    ) -> None:
        if (
            not salesforce_ids
            or "object_type" not in salesforce_ids
            or "Id" not in salesforce_ids
        ):
            raise Exception(f"Invalid salesforce_ids: {salesforce_ids}")

        object_type = salesforce_ids["object_type"]
        record_id = salesforce_ids["Id"]

        if object_type not in ["Account", "Lead", "Contact"]:
            raise Exception(
                "We currently only support exporting to targets that have the Salesforce object type (Account, Lead or Contact). Please select targets with the Salesforce object type."
            )

        request_url = self.get_request_url(
            "salesforce", f"sobjects/{object_type}/{record_id}"
        )
        response = self.patch_request(
            request_url,
            {},
            data_to_push,
        )
        if response.status_code != 204:
            raise Exception(
                f"Failed to update Salesforce object fields: {response.text}"
            )


class LinkedInMarketingAgent(BaseParagonAgent):
    # paragon doesn't return human keys for these ids but these uuids
    ORGANIZATION_ID_FIELD = "5d2fc82d-17e6-4952-874a-9c2916a943e4"
    AD_ACCOUNT_ID_FIELD = "4e288c0b-1bf3-416c-bc9a-e4871674ec90"

    def __init__(self, project_id, playbook, ad_campaign_id):
        super().__init__(project_id, playbook)
        self.ad_campaign_id = ad_campaign_id

        self.common_headers = {
            "LinkedIn-Version": "202402",
            "X-Restli-Protocol-Version": "2.0.0",
        }

    def _resolve_integrations(self):
        request_url = f"https://api.useparagon.com/projects/{self.project_id}/sdk/me"
        user_response = self.get_request(request_url)
        if user_response.status_code != 200:
            logging.error(
                f"Error getting user response from Paragon with response {user_response.text}"
            )
            return status.HTTP_500_INTERNAL_SERVER_ERROR, {
                "error": f"Error getting user response from Paragon with response {user_response.text}"
            }
        user_response = user_response.json()
        integrations = user_response.get("integrations", {})
        if (
            "linkedinmarketing" not in integrations
            or integrations.get("linkedinmarketing", {}).get("enabled", False)
            is not True
        ):
            error_message = f"linkedinmarketing integration is not enabled for playbook {self.playbook.id}"
            logging.error(error_message)
            return status.HTTP_400_BAD_REQUEST, {"error": error_message}

        ids = (
            integrations.get("linkedinmarketing", {}).get("sharedSettings", {}).values()
        )
        if self.ORGANIZATION_ID_FIELD not in ids:
            logging.error(f"error getting organization id from {integrations}")
            return status.HTTP_500_INTERNAL_SERVER_ERROR, {
                "error": f"error getting organization id from {integrations}"
            }
        if self.AD_ACCOUNT_ID_FIELD not in ids:
            logging.error(f"error getting ad account id from {integrations}")
            return status.HTTP_500_INTERNAL_SERVER_ERROR, {
                "error": f"error getting ad account id from {integrations}"
            }

        self.organization_id = ids[self.ORGANIZATION_ID_FIELD]
        self.ad_account_id = ids[self.AD_ACCOUNT_ID_FIELD]

        logging.info(
            f"ids: {ids}, org: {self.organization_id}, ad: {self.ad_account_id}"
        )
        self.organization_urn = f"urn:li:organization:{self.organization_id}"
        return status.HTTP_200_OK, {}

    def _init_image_upload_path(self):
        # step 1: upload the image
        try:
            url = "images?action=initializeUpload"
            request_url = self.get_request_url("linkedinmarketing", url)
            body = {"initializeUploadRequest": {"owner": f"{self.organization_urn}"}}
            headers = {}
            headers.update(self.common_headers)
            response = self.post_request(request_url, headers, body)
            # response is like:
            # {
            #     "value": {
            #         "uploadUrlExpiresAt": *************,
            #         "uploadUrl": "https://www.linkedin.com/dms-uploads/sp/D5610AQH5vGolXo5ekQ/uploaded-image/0?ca=vector_ads&cn=uploads&sync=0&v=beta&ut=1p2k7HscXSyr81",
            #         "image": "urn:li:image:D5610AQH5vGolXo5ekQ",
            #     }
            # }
            if not self._is_request_success(response.status_code):
                logging.error(
                    f"error in step 1 to initialize image upload url: {response.text}"
                )
                return response.status_code, response.text

            results = response.json().get("output", {}).get("value", {})
            upload_url = results.get("uploadUrl")
            image_urn = results.get("image")
            return response.status_code, (upload_url, image_urn)
        except Exception as e:
            logging.error(f"error in step 1 to initialize image upload url: {e}")
            return status.HTTP_500_INTERNAL_SERVER_ERROR, {"error": str(e)}

    def _upload_image(self, image_path, upload_url):
        s3 = boto3.client("s3")

        filename, filetype, bucket_name = parse_s3_presigned_url(image_path)

        # Download the image from S3
        response = s3.get_object(Bucket=bucket_name, Key=filename)
        image_data = response["Body"].read()
        body = image_data
        headers = {
            "Content-Type": filetype,
        }

        response = requests.put(upload_url, headers=headers, data=body)
        if not self._is_request_success(response.status_code):
            logging.error(
                f"linkedin ads: upload failed. status code: {response.status_code} response: {response.text}"
            )
            return response.status_code, response.text
        logging.info("linkedin ads: image upload successful.")
        return response.status_code, {}

    def _create_post(
        self,
        generated_results,
        image_urn,
    ):
        dest_url = generated_results["dest_url"]
        introductory_text = generated_results["introductory_text"]
        headline = generated_results["headline"]
        description = generated_results["description"]
        call_to_action = generated_results["call_to_action"]
        ad_name = generated_results["ad_name"]

        url = "posts"
        request_url = self.get_request_url("linkedinmarketing", url)

        article_payload = {
            "source": dest_url,
            "title": headline,
            "description": description,
        }
        if image_urn:
            article_payload["thumbnail"] = image_urn
        body = {
            "adContext": {
                "dscAdAccount": f"urn:li:sponsoredAccount:{self.ad_account_id}",
                "dscStatus": "ACTIVE",
                "dscName": ad_name,
            },
            "contentLandingPage": dest_url,
            "author": f"{self.organization_urn}",
            "commentary": introductory_text,
            "visibility": "PUBLIC",
            "distribution": {
                "feedDistribution": "NONE",
                "targetEntities": [{"geoLocations": [], "seniorities": []}],
            },
            "content": {"article": article_payload},
            "contentCallToActionLabel": call_to_action,
            "lifecycleState": "PUBLISHED",
            "isReshareDisabledByAuthor": True,
        }
        response = self.post_request(request_url, self.common_headers, body)
        if not self._is_request_success(response.status_code):
            logging.error(f"error creating post: {response.text}")
            return response.status_code, response.text

        response_data = response.json()

        logging.info(f"linkedin ads: create post response: {response_data}")
        post_urn = response_data.get("headers", {}).get(
            "x-linkedin-id"
        ) or response_data.get("headers", {}).get("x-restli-id")
        if not post_urn:
            logging.error(f"error getting post_urn: {response_data.get('headers')}")
            return status.HTTP_500_INTERNAL_SERVER_ERROR, {
                "error": f"error getting post_urn from response headers {response_data.get('headers')}"
            }
        return response.status_code, post_urn

    def _create_ad(self, post_urn):
        url = f"https://api.linkedin.com/rest/adAccounts/{self.ad_account_id}/creatives"
        request_url = self.get_request_url("linkedinmarketing", url)
        body = {
            "content": {"reference": post_urn},
            "campaign": f"urn:li:sponsoredCampaign:{self.ad_campaign_id}",
            "intendedStatus": "DRAFT",
        }
        response = self.post_request(request_url, self.common_headers, body)
        if not self._is_request_success(response.status_code):
            logging.error(f"error creating ad: {response.text}")
            return response.status_code, response.text

        response_data = response.json()
        response_header = response_data.get("headers", {})
        ad_urn = response_header.get("x-linkedin-id") or response_header.get(
            "x-restli-id"
        )
        if not ad_urn:
            logging.error(f"error getting ad_urn: {response_header}")
            return status.HTTP_500_INTERNAL_SERVER_ERROR, {
                "error": f"error getting ad_urn from response headers {response_header}"
            }
        return response.status_code, ad_urn

    def export_linkedin_ads(self, generated_results):
        # step 0: resolve integrations
        status_code, data = self._resolve_integrations()
        if not self._is_request_success(status_code):
            return status_code, data

        image_path = generated_results.get("image_path")
        if image_path:
            # step 1: init image upload
            status_code, data = self._init_image_upload_path()
            if not self._is_request_success(status_code):
                return status_code, data
            upload_url, image_urn = data
            if not upload_url or not image_urn:
                logging.error(f"error getting upload_url or image_urn")
                return status.HTTP_500_INTERNAL_SERVER_ERROR, {
                    "error": f"error getting upload_url or image_urn with {status_code} and {data}"
                }

            # step 2: upload image
            status_code, data = self._upload_image(image_path, upload_url)
            if not self._is_request_success(status_code):
                return status_code, data
        else:
            image_urn = None

        # step 3: create post
        status_code, data = self._create_post(generated_results, image_urn)
        if not self._is_request_success(status_code):
            return status_code, data
        post_urn = data

        # step 4: create the ad
        status_code, data = self._create_ad(post_urn)
        if not self._is_request_success(status_code):
            return status_code, data
        ad_urn = data

        # step 5: optional: fetch preview
        # GET https://api.linkedin.com/rest/adPreviews?q=creative&creative=urn%3Ali%3AsponsoredCreative%3A123456789&account=urn%3Ali%3AsponsoredAccount%3A123456789

        # TODO: check if we shall return ad link
        return status.HTTP_200_OK, {
            "ad_urn": ad_urn,
        }


class ParagonWrapper:
    def __init__(self, playbook) -> None:
        self.project_id = os.environ.get("PARAGON_PROJECT_ID")
        self.playbook = playbook
        self.hubspot_agent = None
        self.marketo_agent = None
        self.linkedin_agent = None
        self.salesforce_agent = None
        self.paragon_agent = None

    def get_hubspot_agent(self):
        if not self.hubspot_agent:
            self.hubspot_agent = HubspotAgent(self.project_id, self.playbook)
        return self.hubspot_agent

    def get_marketo_agent(self):
        if not self.marketo_agent:
            self.marketo_agent = MarketoAgent(self.project_id, self.playbook)
        return self.marketo_agent

    def get_salesforce_agent(self):
        if not self.salesforce_agent:
            self.salesforce_agent = SalesforceAgent(self.project_id, self.playbook)
        return self.salesforce_agent

    def get_linkedin_marketing_agent(self, ad_campaign_id):
        if not self.linkedin_agent:
            self.linkedin_agent = LinkedInMarketingAgent(
                self.project_id,
                self.playbook,
                ad_campaign_id,
            )
        return self.linkedin_agent

    def get_paragon_agent(self):
        if not self.paragon_agent:
            self.paragon_agent = BaseParagonAgent(self.project_id, self.playbook)
        return self.paragon_agent

    def get_hubspot_email_stats(self, email_id):
        return self.get_hubspot_agent().get_hubspot_email_stats(email_id)

    def get_hubspot_landing_page_stats(self, page_id, start_from=None):
        return self.get_hubspot_agent().get_hubspot_landing_page_stats(
            page_id, start_from
        )

    def get_marketo_email_stats(self, email_id):
        return self.get_marketo_agent().get_marketo_email_stats(email_id)

    def get_marketo_landing_page_stats(self, page_id):
        return self.get_marketo_agent().get_marketo_landing_page_stats(page_id)

    def export_linkedin_ads(self, ad_campaign_id, generated_results):
        return self.get_linkedin_marketing_agent(ad_campaign_id).export_linkedin_ads(
            generated_results
        )

    def get_integrations(self):
        try:
            return self.get_paragon_agent().get_user_integrations()
        except Exception as e:
            return {}
