import logging
import re

from ..models import UrlRecord


def sanitize_text(text: str) -> str:
    return re.sub(r"[\x00-\x1f\x7f]", "", text)


def sanitize_doc_dict(doc_dict: dict) -> dict:
    if "page_content" in doc_dict and isinstance(doc_dict["page_content"], str):
        doc_dict["page_content"] = sanitize_text(doc_dict["page_content"])
    return doc_dict


from django.db import transaction


class UrlHandler:
    def __init__(self, url) -> None:
        self._url = url
        self._url_handler = UrlRecord.get_or_create_url_data(url)

    # The URL crawling success, we update counting status, and log the latest result
    def record_success(self, result) -> None:
        try:
            cleaned_docs = [sanitize_doc_dict(doc) for doc in result]
            if self._url_handler.crawling_status == UrlRecord.ExtractResult.NO_RECORD:
                self._url_handler.crawling_status = (
                    UrlRecord.ExtractResult.ALWAYS_SUCCEEDS
                )
            elif (
                self._url_handler.crawling_status
                == UrlRecord.ExtractResult.ALWAYS_FAILS
            ):
                self._url_handler.crawling_status = (
                    UrlRecord.ExtractResult.SUCCEEDS_SOMETIMES
                )
            self._url_handler.docs = cleaned_docs
            with transaction.atomic():
                self._url_handler.save()
        except Exception as e:
            logging.exception(f"Error saving url record: {e}")

    # The URL crawling failed, we update the status and add the error to error list if it isn't already present.
    def record_failure(self, error) -> None:
        if self._url_handler.crawling_status == UrlRecord.ExtractResult.NO_RECORD:
            self._url_handler.crawling_status = UrlRecord.ExtractResult.ALWAYS_FAILS
        elif (
            self._url_handler.crawling_status == UrlRecord.ExtractResult.ALWAYS_SUCCEEDS
        ):
            self._url_handler.crawling_status = (
                UrlRecord.ExtractResult.SUCCEEDS_SOMETIMES
            )

        existing_failures = self._url_handler.errors or []
        if error not in existing_failures:
            existing_failures.append(error)
        self._url_handler.errors = existing_failures
        try:
            with transaction.atomic():
                self._url_handler.save()
        except Exception as e:
            logging.exception(f"Error saving url record: {e}")

    def get_redirect_urls(self):
        return self._url_handler.redirect_urls

    def get_cached_result(self):
        return self._url_handler.docs

    # call from notebooks or other places from intuition based
    def set_redirect_urls(self, redirect_urls):
        self._url_handler.redirect_urls = redirect_urls
        self._url_handler.crawling_status = UrlRecord.ExtractResult.NO_RECORD
        self._url_handler.errors = []
        self._url_handler.docs = []
        try:
            with transaction.atomic():
                self._url_handler.save()
        except Exception as e:
            logging.exception(f"Error saving url record: {e}")
