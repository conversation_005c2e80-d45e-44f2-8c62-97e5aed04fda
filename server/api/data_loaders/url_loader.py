import hashlib
import logging
import mimetypes
import os
import re
import traceback
import urllib.parse
from typing import List
from urllib.parse import urlparse

from django.core.cache import cache
from langchain_community.document_loaders import PyPDFLoader
from langchain_core.document_loaders import BaseLoader
from langchain_core.documents import Document

from ..logger import tofu_axiom_logger
from ..s3_utils import download_url_content_to_s3
from .google_drive_loader import TofuGoogleDriveLoader
from .s3_file_loader import TofuS3FileLoader
from .s3_video_file_loader import TofuS3AudioVideoFileLoader
from .url_handler import UrlHandler
from .url_loader_utils import (
    is_google_drive_url,
    is_media_url,
    is_youtube_url,
    try_parse_google_url,
)
from .web_page_loader import TofuWebPageLoader
from .youtube_loader import TofuYoutubeLoader


class TofuURLLoader(BaseLoader):
    """
    Load data from url, including google doc url, notion page url, etc.
    Depending on the url type, this class will server as an orchestrator to route the request to the right loader.
    Returns an array of Langchain Documents.
    """

    def __init__(
        self,
        url: str,
    ):
        self.url = TofuURLLoader.add_schema_if_missing(url)
        self.url_handler = UrlHandler(url)

    @staticmethod
    def add_schema_if_missing(url: str) -> str:
        """Add schema to a URL if it is missing.

        Args:
            url: The URL to add schema to

        Returns:
            str: The URL with schema
        """
        url = url.strip()  # strip white spaces
        if url.startswith("http://"):
            return "https://" + url[7:]
        if not url.startswith("http://") and not url.startswith("https://"):
            return "https://" + url
        return url

    def _get_s3_info_for_media(self, url: str) -> dict:
        """Get consistent S3 path for a media URL"""
        decoded_url = urllib.parse.unquote(url)
        url_hash = hashlib.md5(decoded_url.encode()).hexdigest()
        parsed_url = urlparse(decoded_url)

        # Get mime type and extension
        mime_type, _ = mimetypes.guess_type(url)
        extension = TofuS3AudioVideoFileLoader.MIME_TYPE_TO_FORMAT.get(mime_type)

        # Clean the original filename to be AWS service compatible
        original_filename = os.path.basename(parsed_url.path)
        if not original_filename:
            original_filename = f"{url_hash}_media{extension}"

        # Clean special characters
        clean_filename = re.sub(r"[^0-9a-zA-Z._-]", "_", original_filename)

        # Create S3 path with directory
        s3_filename = f"url_media/{url_hash}-{clean_filename}"

        return {
            "s3_bucket": "tofu-uploaded-files",
            "s3_filename": s3_filename,
            "mime_file_type": mime_type,
        }

    @staticmethod
    def is_youtube_url(url):
        parsed = urllib.parse.urlparse(url)
        netloc = parsed.netloc
        if "youtube.com" in netloc or "youtu.be" in netloc:
            return True
        return False

    def get_loader(self, url: str, deep_crawl: bool):
        clean_url = url.split("?")[0]
        if clean_url.endswith(".pdf"):
            try:
                import pypdf
            except ImportError:
                raise ImportError("Please run `pip install pypdf` to use pdf loader")
            return PyPDFLoader(url)
        elif is_media_url(url):
            try:
                logging.info(f"Processing media URL {url}")
                s3_info = self._get_s3_info_for_media(url)
                download_url_content_to_s3(
                    url=url,
                    s3_bucket=s3_info["s3_bucket"],
                    s3_filename=s3_info["s3_filename"],
                    content_type=s3_info["mime_file_type"],
                )
                return TofuS3FileLoader(info=s3_info)
            except Exception as e:
                logging.error(f"Failed to process media URL {url}: {e}")
                raise

        google_drive_url = try_parse_google_url(url)
        if google_drive_url:
            service_account_key = ".credentials/google_service_account_key.json"
            return TofuGoogleDriveLoader(
                service_account_key=service_account_key,
                type=google_drive_url.get("type", "other"),
                id=google_drive_url.get("id", ""),
                recursive=False,
            )

        if is_youtube_url(url):
            return TofuYoutubeLoader(
                url=url,
                add_video_info=False,
            )

        redirect_urls = self.url_handler.get_redirect_urls()
        if redirect_urls:
            return TofuWebPageLoader(redirect_urls[0], deep_crawl)

        return TofuWebPageLoader(url, deep_crawl)

    def remove_empty_docs(self, docs: List[Document]) -> List[Document]:
        # remove all docs that has empty page_content
        docs = [doc for doc in docs if doc.page_content.strip() != ""]
        return docs

    def load(
        self,
        deep_crawl: bool = False,
        disable_cache: bool = False,
        update_cache: bool = False,
    ) -> List[Document]:
        try:
            logging.info(f"Loading data from url: {self.url}")
            # try to get the data from cache first
            cache_key = "{}:{}".format(self.url, deep_crawl)
            try:
                cached_data = cache.get(cache_key)
            except Exception as e:
                cached_data = None
            if not disable_cache and cached_data:
                logging.info(f"Cached data found for: {self.url}")
                tofu_axiom_logger.log_axiom(
                    event_type="url_loader_cache_hit", url=self.url, result=cached_data
                )
                if update_cache:
                    cache.set(
                        cache_key, cached_data, 60 * 60 * 24 * 7
                    )  # extend cache for 7 days
                if isinstance(cached_data, Exception):
                    raise cached_data
                return cached_data
            else:
                tofu_axiom_logger.log_axiom(
                    event_type="url_loader_cache_miss", url=self.url
                )
            loader = self.get_loader(self.url, deep_crawl)
            docs = loader.load()
            docs = self.remove_empty_docs(docs)
            if not docs:
                raise Exception(f"No content found in url {self.url}")

            self.url_handler.record_success([doc.__dict__ for doc in docs])

            logging.info(f"Content fetched from url: {self.url}")
            if is_google_drive_url(self.url):
                # cache for 1 minute
                cache.set(cache_key, docs, 60)
            # For YT and media files, cache for a year
            elif is_youtube_url(self.url) or is_media_url(self.url):
                cache.set(cache_key, docs, 60 * 60 * 24 * 365)  # cache for a year
            else:
                cache.set(cache_key, docs, 60 * 60 * 24 * 3)  # cache for 3 days
            return docs
        except Exception as e:
            logging.error(
                f"Failed to load data from url: {self.url} due to error: \n{e}\n{traceback.format_exc()}"
            )
            self.url_handler.record_failure(str(e))

            serialized_e = Exception(str(e))  # some exceptions are not serializable

            # For YT and GDrive, do not cache the error.
            if TofuURLLoader.is_short_error_cache(self.url):
                pass
            else:
                # Cache the error for 3 days
                cache.set(cache_key, serialized_e, 60 * 60 * 24 * 3)
            raise (e)

    def is_short_error_cache(url):
        # Check if url is YT or GDrive
        if is_youtube_url(url):
            return True
        google_drive_url = try_parse_google_url(url)
        if google_drive_url:
            return True
        return False
