import logging
from typing import Any, List, Optional, Union

from langchain_community.document_loaders import GoogleDriveLoader
from langchain_core.documents import Document


class TofuGoogleDriveLoader(GoogleDriveLoader):
    type: Optional[str] = None

    def __init__(
        self,
        type: str,
        id: str,
        **kwargs,
    ):
        if type == "folder":
            super().__init__(folder_id=id, **kwargs)
        elif type == "file":
            super().__init__(file_ids=[id], **kwargs)
        elif type in ("document", "presentation", "spreadsheet"):
            super().__init__(document_ids=[id], **kwargs)
        else:
            raise ValueError(f"Type {type} is not supported for TofuGoogleDriveLoader")
        self.type = type
        logging.info(f"Loading data from Google Drive: {type} {id}")

    # override the load method because the original code is loading spreadsheet into one doc for one row
    # we also do some compression to reduce token usage
    def _load_sheet_from_id(self, id: str) -> List[Document]:
        """Load a sheet and all tabs from an ID."""

        from googleapiclient.discovery import build

        creds = self._load_credentials()
        sheets_service = build("sheets", "v4", credentials=creds)
        spreadsheet = sheets_service.spreadsheets().get(spreadsheetId=id).execute()
        sheets = spreadsheet.get("sheets", [])

        documents = []
        for sheet in sheets:
            sheet_name = sheet["properties"]["title"]
            result = (
                sheets_service.spreadsheets()
                .values()
                .get(spreadsheetId=id, range=sheet_name)
                .execute()
            )
            values = result.get("values", [])
            metadata = {
                "source": (
                    f"https://docs.google.com/spreadsheets/d/{id}/"
                    f"edit?gid={sheet['properties']['sheetId']}"
                ),
                "title": f"{spreadsheet['properties']['title']} - {sheet_name}",
            }
            content = []
            for i, row in enumerate(values):
                row_content = []
                for j, v in enumerate(row):
                    row_content.append(f"{v.strip()}")
                row_string = "\t".join(row_content)
                if row_string.strip() != "":
                    content.append(f"[row-{i}] {row_string}")
            page_content = "\n".join(content)
            documents.append(Document(page_content=page_content, metadata=metadata))

        return documents

    # override the load method because the original code is not loading google slides
    def _load_documents_from_folder(self, folder_id: str) -> List[Document]:
        """Load documents from a folder."""
        from googleapiclient.discovery import build

        creds = self._load_credentials()
        service = build("drive", "v3", credentials=creds)
        files = self._fetch_files_recursive(service, folder_id)
        returns = []
        for file in files:
            if file["mimeType"] in (
                "application/vnd.google-apps.document",
                "application/vnd.google-apps.presentation",
            ):
                returns.append(self._load_document_from_id(file["id"]))  # type: ignore
            elif file["mimeType"] == "application/vnd.google-apps.spreadsheet":
                returns.extend(self._load_sheet_from_id(file["id"]))  # type: ignore
            elif file["mimeType"] == "application/pdf":
                returns.extend(self._load_file_from_id(file["id"]))  # type: ignore
            else:
                pass

        return returns

    def _load_document_from_id(self, id: str) -> Document:
        """Load a document from an ID."""
        from io import BytesIO

        from docx import Document as DocumentX
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
        from googleapiclient.http import MediaIoBaseDownload
        from pptx import Presentation

        creds = self._load_credentials()
        service = build("drive", "v3", credentials=creds)
        file = service.files().get(fileId=id, supportsAllDrives=True).execute()
        mimeType = file["mimeType"]

        if mimeType in (
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        ):
            request = service.files().get_media(fileId=id)
        else:
            request = service.files().export_media(fileId=id, mimeType="text/plain")

        fh = BytesIO()
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        try:
            while done is False:
                status, done = downloader.next_chunk()

        except HttpError as e:
            if e.resp.status == 404:
                logging.error("File not found: {}".format(id))
            else:
                logging.error("An error occurred: {}".format(e))

        if mimeType in (
            "application/vnd.google-apps.document",
            "application/vnd.google-apps.presentation",
        ):
            text = fh.getvalue().decode("utf-8")
        elif (
            mimeType
            == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ):
            fh.seek(0)
            document = DocumentX(fh)
            text = [paragraph.text for paragraph in document.paragraphs]
            text = "\n".join(text)
        elif (
            mimeType
            == "application/vnd.openxmlformats-officedocument.presentationml.presentation"
        ):
            fh.seek(0)
            presentation = Presentation(fh)
            text = [
                shape.text
                for slide in presentation.slides
                for shape in slide.shapes
                if shape.has_text_frame
            ]
            text = "\n".join(text)

        metadata = {
            "source": f"https://docs.google.com/document/d/{id}/edit",
            "title": f"{file.get('name')}",
        }
        return Document(page_content=text, metadata=metadata)

    def _load_documents_from_ids(self) -> List[Document]:
        """Load documents from a list of IDs."""
        if not self.document_ids:
            raise ValueError("document_ids must be set")

        return [self._load_document_from_id(doc_id) for doc_id in self.document_ids]

    # there is a problem for handling spreadsheet in the original code, so we need to override it
    def load(self) -> List[Document]:
        if self.type == "spreadsheet":
            logging.info("Loading spreadsheet")
            docs = self._load_sheet_from_id(self.document_ids[0])
        elif self.document_ids:
            logging.info("Loading documents")
            docs = self._load_documents_from_ids()
        else:
            docs = super().load()
        return docs
