import concurrent.futures
import json
import logging
import os
import re
from threading import Lock
from typing import Any, List, Union
from urllib.parse import urljoin, urlparse

import requests
import tldextract
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.documents import Document

from ..integrations.crustdata_adapter import CrustdataLinkedinCrawler
from ..logger import log_cloudwatch_metric
from ..thread_locals import get_current_playbook
from ..utils import CloudWatchMetrics, measure_latency
from .url_loader_utils import is_media_url, is_youtube_url, try_parse_google_url


class TofuWebPageLoader(WebBaseLoader):
    """
    Similar to WebBaseLoader but with additional post processing.
    """

    CRAWLER_NUM_THREAD = 4

    def __init__(
        self,
        url: str,
        deep_crawl: bool,
        crawler_max_depth: int = 1,
        crawler_max_page: int = 10,
        **kwargs: Any,
    ) -> None:
        super().__init__(url, **kwargs)
        self.url = url
        self.deep_crawl = deep_crawl
        self.crawler_max_depth = crawler_max_depth
        self.crawler_max_page = crawler_max_page
        extracted_url = tldextract.extract(self.url)
        self.domain, self.domain_suffix = extracted_url.domain, extracted_url.suffix
        self.bs_get_text_kwargs = {"separator": " "}

    def is_same_domain(self, url):
        extracted_url = tldextract.extract(url)
        return (
            extracted_url.domain == self.domain
            and extracted_url.suffix == self.domain_suffix
        )

    def extract_sub_urls(
        self,
        skipped_patterns: List[str] = [],
        deep_crawl_only_same_folder: bool = False,
    ):
        urls = set()
        soup = self._scrape(self.url)
        for link in soup.find_all("a"):
            href = link.get("href")
            if (
                href
                and not href.startswith("#")
                and not href.startswith("mailto:")
                and not href.startswith("tel:")
            ):
                absolute_url = urljoin(self.url, href)
                if self.is_same_domain(absolute_url):
                    urls.add(absolute_url)
        if self.url in urls:
            urls.remove(self.url)

        filtered_urls = [
            url
            for url in urls
            if TofuWebPageLoader._is_web_page_loader_supported_url(url)
        ]

        filtered_urls = [
            url
            for url in filtered_urls
            if not any(pattern in url for pattern in skipped_patterns)
        ]

        if deep_crawl_only_same_folder:
            filtered_urls = [url for url in filtered_urls if self.url in url]

        return filtered_urls

    def scrape(self, parser: str | None = None) -> Any:
        return self._scrape(self.url, parser)

    def load_and_extract(
        self,
        skipped_patterns: List[str] = [],
        deep_crawl_only_same_folder: bool = False,
    ):
        docs = self.load_shallow()
        sub_urls = self.extract_sub_urls(skipped_patterns, deep_crawl_only_same_folder)
        return docs, self.url, sub_urls

    # optimize token usage by further cleaning the html text extracted using BeautifulSoup
    def clean_text(self, docs: List[Document]) -> List[Document]:
        # remove all docs that has empty page_content
        docs = [doc for doc in docs if doc.page_content.strip() != ""]
        for doc in docs:
            clean_text = re.sub(" +", " ", doc.page_content)
            # Remove multiple newlines, carriage returns, tabs, and spaces between them and replace with a single newline
            doc.page_content = re.sub(r"(\s*[\n\r\t]+\s*)+", "\n", clean_text)
        return docs

    def load(self) -> List[Document]:
        docs = []
        if self.deep_crawl:
            docs = self.load_deep()
        else:
            docs = self.load_shallow()
        return self.clean_text(docs)

    def _is_linkedin_profile_page(self, url: str):
        try:
            return CrustdataLinkedinCrawler.is_linkedin_profile_url(url)
        except Exception as e:
            logging.exception(
                f"Failed to check if {url} is a LinkedIn profile page: {e}"
            )
            return False

    def _is_enabled_crustdata_for_linkedin_profile(self):
        playbook = get_current_playbook()
        if not playbook:
            return True
        return playbook.settings.get("enableCrustdataForLinkedInProfile", True)

    def _read_with_crustdata_for_linkedin_profile(self, url: str):
        try:
            crustdata_integration = CrustdataLinkedinCrawler()
            log_cloudwatch_metric("crustdata_linkedin_crawl", 1, url=url)

            result = crustdata_integration.enrich_linkedin_link(linkedin_url=url)

            if result:
                response = requests.Response()
                response.status_code = 200
                response._content = json.dumps(result).encode("utf-8")
                response.encoding = "utf-8"
                response.headers["Content-Type"] = "application/json"
                return response
            return None
        except Exception as e:
            logging.error(f"Failed to crawl LinkedIn profile {url}: {e}")
            return None

    def _scrape(
        self, url: str, parser: Union[str, None] = None, bs_kwargs: Any = None
    ) -> Any:
        from bs4 import BeautifulSoup

        if parser is None:
            if url.endswith(".xml"):
                parser = "xml"
            else:
                parser = self.default_parser

        self._check_parser(parser)

        if (
            self._is_linkedin_profile_page(url)
            and self._is_enabled_crustdata_for_linkedin_profile()
        ):
            try:
                scraping_response = self._read_with_crustdata_for_linkedin_profile(url)
                if not scraping_response:
                    return None
            except Exception as e:
                logging.error(f"Failed to crawl LinkedIn profile {url}: {e}")
                return None
        else:
            retry = False
            try:
                scraping_response = self.session.get(
                    url, timeout=10, **self.requests_kwargs
                )
            except Exception as e:
                logging.info(
                    f"Exception raised when crawling {url}: {e}. Will do a retry with scraping bee."
                )
                retry = True

            if retry or scraping_response.status_code != 200:
                scraping_response = self.read_with_scraping_bee(url)

        # Raise an exception if the status is still not 200 after retry.
        # This will make the data loader to return empty string which is better than returning the error message.
        if scraping_response.status_code != 200:
            raise WebPageFetchError(scraping_response.status_code, url)
        scraping_response.encoding = scraping_response.apparent_encoding
        return BeautifulSoup(scraping_response.text, parser)

    @measure_latency
    def load_shallow(self) -> List[Document]:
        """Load text from the url(s) in web_path."""
        return super().load()

    def normalize_url(self, url):
        parsed_url = urlparse(url)
        # normalize by discarding the scheme, lowercasing the netloc and strip leading www.
        netloc = parsed_url.netloc.lower()
        if netloc.startswith("www."):
            netloc = netloc[4:]
        # combine normalized components back into a URL
        normalized_url = f"{netloc}{parsed_url.path}{parsed_url.params}{parsed_url.query}{parsed_url.fragment}"
        return normalized_url

    @measure_latency
    def load_deep(
        self,
        skipped_patterns: List[str] = [],
        deep_crawl_only_same_folder: bool = False,
        always_crawl_next_page: bool = False,
    ) -> List[Document]:
        logging.info(f"Deep crawling for website {self.url}")
        visited_urls = {self.normalize_url(self.url): 0}
        visited_urls_set = set([self.normalize_url(self.url)])
        parsed_urs = set()
        documents = []

        lock = Lock()

        def thread_safe_load_url_job(url):
            url_docs, processed_url, nested_urls = TofuWebPageLoader(
                url, deep_crawl=False
            ).load_and_extract(skipped_patterns, deep_crawl_only_same_folder)
            with lock:
                parsed_urs.add(processed_url)
                documents.extend(url_docs)
            return processed_url, nested_urls

        # Create a ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(self.CRAWLER_NUM_THREAD) as executor:
            futures = {executor.submit(thread_safe_load_url_job, self.url): self.url}

            while futures:
                # Process the results as they become available
                for future in concurrent.futures.as_completed(futures):
                    url = futures[future]
                    try:
                        processed_url, nested_urls = future.result()
                    except WebPageFetchError as e:
                        logging.warning(
                            f"Loading URL job for {url} raised WebPageFetchError: {e}"
                        )
                        CloudWatchMetrics.put_metric(
                            "web_page_fetch_status_code_error",
                            1,
                            [{"Name": "url", "Value": url}],
                        )
                    except Exception as e:
                        logging.error(
                            f"Loading URL job for {url} raised exception: {e}"
                        )
                        CloudWatchMetrics.put_metric(
                            "web_page_fetch_exception",
                            1,
                            [{"Name": "url", "Value": url}],
                        )
                    else:
                        current_depth = visited_urls[self.normalize_url(url)]
                        # Process nested URLs with depth check bypass for pagination
                        for url in nested_urls:
                            is_next_page = self.is_next_page_url(url)
                            if (
                                always_crawl_next_page and is_next_page
                            ) or (  # Bypass depth for pagination
                                current_depth < self.crawler_max_depth
                            ):  # Normal depth check
                                normalized_url = self.normalize_url(url)
                                if normalized_url not in visited_urls_set:
                                    with lock:
                                        if normalized_url not in visited_urls_set:
                                            visited_urls_set.add(normalized_url)
                                            visited_urls[normalized_url] = (
                                                current_depth + 1
                                            )
                                            if (
                                                len(visited_urls)
                                                < self.crawler_max_page
                                            ):
                                                logging.info(
                                                    f"Adding {url} to the queue"
                                                )
                                                new_future = executor.submit(
                                                    thread_safe_load_url_job, url
                                                )
                                                futures[new_future] = url
                                            else:
                                                logging.warning(
                                                    f"Max page limit reached for {self.url}: {self.crawler_max_page}"
                                                )
                    del futures[future]
        logging.info(f"{len(parsed_urs)} are parsed for input url: {self.url}")
        return documents

    def read_with_scraping_bee(self, url: str):
        response = requests.get(
            url="https://app.scrapingbee.com/api/v1/",
            params={
                "api_key": os.environ.get("SCRAPING_BEE_API_KEY"),
                "url": url,
                "render_js": "false",
            },
        )
        return response

    def is_next_page_url(self, url: str) -> bool:
        """Determine if a URL is likely a pagination 'next page' URL."""
        parsed = urlparse(url.lower())
        pagination_keys = {"page", "p", "start", "next", "pagina", "pag", "offset"}

        # Check query parameters with pattern matching
        query_params = parsed.query.split("&")
        for param in query_params:
            if "=" in param:
                key, value = param.split("=", 1)
                # Match keys containing pagination terms (e.g. "6dbc1bb9_page")
                if any(p_key in key for p_key in pagination_keys):
                    return True
                # Match numeric values with pagination keys
                if any(p_key in param for p_key in pagination_keys) and value.isdigit():
                    return True

        # Check path segments for patterns like /page/2 or /blog/p/3
        path_segments = parsed.path.split("/")
        for i, segment in enumerate(path_segments):
            if segment in pagination_keys:
                return True
            if re.match(r"^(page|p)-?\d+$", segment):
                return True
            if segment.isdigit() and i > 0 and path_segments[i - 1] in pagination_keys:
                return True

        return False

    @staticmethod
    # TODO: support different url type loader in deep crawl
    def _is_web_page_loader_supported_url(url: str) -> bool:
        clean_url = url.split("?")[0]
        if clean_url.endswith(".pdf"):
            return False
        elif is_media_url(url):
            return False
        elif is_youtube_url(url):
            return False
        elif try_parse_google_url(url):
            return False
        return True


class WebPageFetchError(Exception):
    """Exception raised when failing to fetch a web page due to HTTP status code"""

    def __init__(self, status_code: int, url: str):
        self.status_code = status_code
        self.url = url
        super().__init__(f"Error {status_code} when crawling {url}")
