import json
import logging
import tempfile
import time
import traceback
from datetime import datetime, timezone
from json import JSONDecodeError
from typing import Any, List

import boto3
from botocore.exceptions import ClientError
from django.core.cache import cache
from langchain_core.documents import Document

from ..playbook_build.transcription_postprocess import postprocess_transcription
from ..s3_utils import check_file_exists, upload_file
from ..thread_locals import get_current_playbook

# Amazon Transcribe supports the following input formats:
# https://docs.aws.amazon.com/transcribe/latest/dg/how-input.html#how-input-audio


class TofuS3AudioVideoFileLoader:
    PROCESS_TIMEOUT = 1800  # 30 minutes
    LOCK_TIMEOUT = 1800  # 30 minutes
    STATUS_CACHE_TIMEOUT = 60 * 60 * 24 * 7  # 7 days
    MIME_TYPE_TO_FORMAT = {
        "audio/mpeg": "mp3",
        "audio/mp4": "mp4",
        "audio/x-m4a": "m4a",
        "audio/wav": "wav",
        "audio/x-wav": "wav",
        "audio/flac": "flac",
        "audio/amr": "amr",
        "audio/amr-wb": "amr",
        "audio/ogg": "ogg",
        "audio/webm": "webm",
        "video/mp4": "mp4",
        "video/ogg": "ogg",
        "video/webm": "webm",
    }

    def __init__(
        self,
        info: dict,
    ):
        self.s3_bucket = info["s3_bucket"]
        self.s3_filename = info["s3_filename"]
        self.file_type = info["mime_file_type"]
        self.total_duration = info.get("total_duration", None)
        self.file_size = info.get("file_size", None)

        self.transcribe = boto3.client("transcribe")

    @staticmethod
    def is_supported_audio_video_file(mime_type: str) -> bool:
        return mime_type in TofuS3AudioVideoFileLoader.MIME_TYPE_TO_FORMAT.keys()

    @property
    def s3_uri(self):
        return f"s3://{self.s3_bucket}/{self.s3_filename}"

    @property
    def job_name(self):
        return f"transcript_for_{self.s3_filename.replace('/', '_')}"

    @property
    def postprocess_transcript_file(self):
        return f"{self.job_name}.txt"

    @property
    def transcript_file(self):
        return f"{self.job_name}.json"

    @property
    def failure_prefix(self):
        return f"Error in video/audio processing for job {self.job_name}:"

    @property
    def media_format(self):
        # mp3 | mp4 | wav | flac | ogg | amr | webm
        return TofuS3AudioVideoFileLoader.MIME_TYPE_TO_FORMAT[self.file_type]

    def _check_job_status(self):
        # Get the job status
        response = self.transcribe.get_transcription_job(
            TranscriptionJobName=self.job_name
        )
        return response["TranscriptionJob"]["TranscriptionJobStatus"], response[
            "TranscriptionJob"
        ].get("FailureReason", None)

    def _update_doc_status(self, status):  # TODO: make this more generic
        playbook = get_current_playbook()
        if not playbook:
            logging.error(f"Cannot load playbook for video/audio processing.")
            return
        status_key = f"playbook_{playbook.id}_doc_status"
        status_expanded_key = f"playbook_{playbook.id}_doc_status_expanded"
        try:
            cur_data = cache.get(status_key)
        except Exception as e:
            logging.exception(f"debug: Failed to get doc status for {playbook.id}: {e}")
            cur_data = None
        if not cur_data:
            cur_data = {}
        cur_data[self.s3_filename] = status
        cache.set(status_key, cur_data, self.STATUS_CACHE_TIMEOUT)  # 7 days
        try:
            cur_expanded_data = cache.get(status_expanded_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get doc status expanded for {playbook.id}: {e}"
            )
            cur_expanded_data = None
        if not cur_expanded_data:
            cur_expanded_data = {}

        cur_expanded_data[self.s3_filename] = {
            "status": status,
            "s3_uri": self.s3_uri,
            "created_at": (
                datetime.now(timezone.utc).isoformat()
                if status == "IN_PROGRESS"
                else cur_expanded_data.get(self.s3_filename, {}).get("created_at", None)
            ),
            "total_duration": self.total_duration,
            "file_size": self.file_size,
        }
        cache.set(
            status_expanded_key, cur_expanded_data, self.STATUS_CACHE_TIMEOUT
        )  # 7 days

    def _submit_transcription_job(self):
        language_code = "en-US"  # Change this based on your video's language

        # Valid Values: mp3 | mp4 | wav | flac | ogg | amr | webm
        media_format = self.media_format

        # TODO: put this to rebuild function
        # self.transcribe.delete_transcription_job(TranscriptionJobName=self.job_name)

        self._update_doc_status("IN_PROGRESS")
        # Start the transcription job
        try:
            response = self.transcribe.start_transcription_job(
                TranscriptionJobName=self.job_name,
                Media={"MediaFileUri": self.s3_uri},
                MediaFormat=media_format,
                # LanguageCode=language_code,
                IdentifyLanguage=True,
                OutputBucketName=self.s3_bucket,
                Settings={
                    "ShowSpeakerLabels": True,
                    "MaxSpeakerLabels": 5,
                },
            )
            logging.info(f"Transcription job started: {self.job_name} - {response}")
        except Exception as e:
            logging.error(
                f"Failed to start transcription job: {self.failure_prefix} {e}"
            )

    def _check_transcript_exist(self):
        s3 = boto3.client("s3")
        try:
            s3.head_object(Bucket=self.s3_bucket, Key=self.postprocess_transcript_file)
            return True
        except ClientError as e:
            # If a client error is thrown, check if it was a 404 error
            # If it was a 404 error, it means the object does not exist
            if (
                e.response["Error"]["Code"] == "404"
                or e.response["Error"]["Code"] == "403"
            ):
                return False
            else:
                # If it was a different kind of error, re-raise the exception
                logging.error(f"{self.failure_prefix} Unexpected error: {e}")
        return False

    def acquire_lock_with_retries(self, lock_key, max_retries):
        attempts = 0
        while attempts < max_retries:
            if cache.add(lock_key, True, self.LOCK_TIMEOUT):
                return True  # Lock acquired
            attempts += 1
            if attempts >= max_retries:
                raise TimeoutError("Failed to acquire lock after maximum retries")
            sleep_time = min(10 * (attempts + 1), 30)
            time.sleep(sleep_time)
        return False  # Failed to acquire lock

    # mark default max retries is 20 and max wait time is 9.5 minutes
    def _fetch_result(self, max_retries=20):
        postprocess_lock_key = f"postprocess_lock_{self.job_name}"

        if self.acquire_lock_with_retries(postprocess_lock_key, max_retries):
            try:
                # First check if the postprocessed transcript exists.
                if check_file_exists(self.s3_bucket, self.postprocess_transcript_file):
                    try:
                        from .s3_file_loader import TofuS3FileLoader

                        loader = TofuS3FileLoader(
                            info={
                                "s3_bucket": self.s3_bucket,
                                "s3_filename": self.postprocess_transcript_file,
                                "mime_file_type": "text/plain",
                            }
                        )
                        docs = loader.load()
                        if docs:
                            return docs
                    except ClientError as e:
                        # If a client error is thrown, check if it was a 404 error
                        # If it was a 404 error, it means the object does not exist
                        if (
                            e.response["Error"]["Code"] == "404"
                            or e.response["Error"]["Code"] == "403"
                        ):
                            logging.info(
                                "Postprocessed transcript not found. Starting postprocessing."
                            )
                        else:
                            # If it was a different kind of error, re-raise the exception
                            logging.error(
                                f"{self.failure_prefix} Unexpected error: {e}"
                            )
                            raise e

                try:
                    from .s3_file_loader import TofuS3FileLoader

                    loader = TofuS3FileLoader(
                        info={
                            "s3_bucket": self.s3_bucket,
                            "s3_filename": self.transcript_file,
                            "mime_file_type": "application/json",
                        }
                    )
                    try:
                        docs = loader.load()
                    except ClientError as e:
                        # We do not expect to get here since we should be fetching result when transcription is already done.
                        logging.error(
                            f"{self.failure_prefix} Failed to fetch raw transcript: {e}"
                        )
                        raise e
                    except JSONDecodeError as e:
                        logging.error(
                            f"{self.failure_prefix} Failed to parse JSON for {self.transcript_file}: {e}"
                        )
                        raise e
                    # TODO: make sure the document is in the correct format
                    if not docs:
                        logging.error(
                            f"{self.failure_prefix} Transcript file is empty."
                        )
                        return []

                    docs = self.merge_aws_transcription(docs)
                    if not docs:
                        logging.error(
                            f"{self.failure_prefix} Failed to merge transcription."
                        )
                        return []
                    try:
                        postprocessed_docs = postprocess_transcription(docs)
                    except Exception as e:
                        logging.error(
                            "%s Failed to postprocess transcript: %s",
                            self.failure_prefix,
                            e,
                        )
                        raise e

                    # save the postprocessed transcript to s3
                    postprocessed_transcript = postprocessed_docs[0].page_content
                    with tempfile.NamedTemporaryFile() as temp_file:
                        temp_file.write(postprocessed_transcript.encode("utf-8"))
                        temp_file.flush()
                        upload_file(
                            temp_file.name,
                            "text/plain",
                            self.s3_bucket,
                            self.postprocess_transcript_file,
                        )

                    # return the postprocessed transcript
                    return postprocessed_docs
                except Exception as e:
                    logging.error(
                        f"{self.failure_prefix} Exception during postprocessing: {e}\n{traceback.format_exc()}"
                    )
                    raise e
            finally:
                cache.delete(postprocess_lock_key)
        else:
            # logging.error(f"{self.failure_prefix} Failed to acquire lock.")
            return []

    def _wait_and_return_status(self):
        start_time = time.time()  # Capture start time
        status, error = self._check_job_status()
        while status not in ["COMPLETED", "FAILED"]:
            current_time = time.time()
            elapsed_time = current_time - start_time
            if elapsed_time > self.PROCESS_TIMEOUT:
                logging.error(
                    f"{self.failure_prefix} Timeout reached after {self.PROCESS_TIMEOUT} seconds."
                )
                self._update_doc_status("FAILED")
                return []

            logging.info(f"Job status: {self.job_name} - {status}. Waiting...")
            time.sleep(10)

            status, error = self._check_job_status()
        # TODO: add error to status
        self._update_doc_status(status)
        if status == "FAILED" or error:
            logging.error(
                f"{self.failure_prefix} Job status is {status} due to: {error}."
            )
            return []

        return self._fetch_result()

    def load(self, fast_return=False) -> List[Document]:
        # if already exists, return the cached data
        try:
            job_status, error = self._check_job_status()
            # job_status can be "COMPLETED", but postprocessing is not done yet.
            if job_status == "COMPLETED":
                is_postprocessing_done = self._check_transcript_exist()
                if not is_postprocessing_done:
                    job_status = "IN_PROGRESS"
            self._update_doc_status(job_status)
            # if job_status exists, which means the job is submited and
            #   the record is still under retention which is 90 days
            #   we won't re-submit the same job
            # if job doesn't exist it would throw exception from _check_job_status
            if job_status == "COMPLETED":
                # already finished
                docs = self._fetch_result()
            elif job_status == "FAILED":
                logging.error(
                    f"{self.failure_prefix} Job status is FAILED due to: {error}."
                )
                docs = []
            elif job_status == "IN_PROGRESS":
                if fast_return:
                    return []
                docs = self._wait_and_return_status()
            else:
                logging.error(f"{self.failure_prefix} Unknown job status: {job_status}")
                return []
        # I expected it returns NotFoundException but it returns BadRequestException in test
        except (
            self.transcribe.exceptions.NotFoundException,
            self.transcribe.exceptions.BadRequestException,
            ClientError,
        ) as e:
            if self._check_transcript_exist():
                self._update_doc_status("COMPLETED")
                docs = self._fetch_result()
            else:
                if fast_return:
                    return []
                self._submit_transcription_job()
                docs = self._wait_and_return_status()
        except Exception as e:
            logging.error(
                f"{self.failure_prefix} Exception in doc load {e}\n{traceback.format_exc()}"
            )
            return []

        return docs

    def merge_aws_transcription(self, docs):
        try:
            transcript_data = json.loads(docs[0].page_content)
            items = transcript_data.get("results", {}).get("items", [])
        except Exception as e:
            logging.error(
                f"Fail to load JSON for results in transcription {e}\n{docs[0].page_content[:100]}"
            )
            return []

        def format_timestamp(seconds):
            try:
                minutes, seconds = divmod(int(float(seconds)), 60)
                hours, minutes = divmod(minutes, 60)
                return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            except Exception as e:
                logging.error(f"format_timestamp: exception for {seconds} {e}")
                return "00:00:00"

        formatted_transcript = []
        current_sentence = []
        sentence_start_time = None
        sentence_end_time = None
        current_speaker = None

        for item in items:
            if not sentence_start_time and "start_time" in item:
                sentence_start_time = item["start_time"]
            if "end_time" in item:
                sentence_end_time = item["end_time"]

            if (
                "type" not in item
                or "alternatives" not in item
                or not item["alternatives"]
                or "content" not in item["alternatives"][0]
            ):
                logging.error(f"merge_aws_transcription: invalid item for {item}")
                continue

            item_type = item["type"]
            item_content = item["alternatives"][0]["content"]
            if item_type == "pronunciation":
                word = item_content
                # Update speaker if available
                if "speaker_label" in item:
                    new_speaker = item["speaker_label"]
                    if new_speaker != current_speaker:
                        if current_sentence:
                            sentence = " ".join(current_sentence).strip()
                            speaker_prefix = (
                                f"{current_speaker}: " if current_speaker else ""
                            )
                            formatted_transcript.append(
                                f"[{format_timestamp(sentence_start_time)} - {format_timestamp(sentence_end_time)}] {speaker_prefix}{sentence}"
                            )

                        # Reset for the new speaker
                        current_sentence = []
                        sentence_start_time = item["start_time"]
                        current_speaker = new_speaker

                current_sentence.append(word)

            elif item_type == "punctuation":
                punctuation = item_content
                if current_sentence:
                    current_sentence[-1] = current_sentence[-1].rstrip() + punctuation
                else:
                    current_sentence.append(punctuation)

                if punctuation in ".!?":
                    sentence = " ".join(current_sentence).strip()
                    speaker_prefix = f"{current_speaker}: " if current_speaker else ""
                    formatted_transcript.append(
                        f"[{format_timestamp(sentence_start_time)} - {format_timestamp(sentence_end_time)}] {speaker_prefix}{sentence}"
                    )
                    current_sentence = []
                    sentence_start_time = None
                    sentence_end_time = None

        # Add any remaining words as a sentence
        if current_sentence:
            sentence = " ".join(current_sentence).strip()
            speaker_prefix = f"{current_speaker}: " if current_speaker else ""
            formatted_transcript.append(
                f"[{format_timestamp(sentence_start_time)} - {format_timestamp(sentence_end_time)}] {speaker_prefix}{sentence}"
            )

        formatted_transcript = "\n".join(formatted_transcript)

        return [
            Document(
                page_content=formatted_transcript, metadata={"source": self.s3_uri}
            )
        ]
