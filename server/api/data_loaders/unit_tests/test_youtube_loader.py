import logging
import os
import tempfile
import xml.etree.ElementTree as ET
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, mock_open, patch
from urllib.parse import urljoin

import pytest
import requests
from langchain_community.document_loaders.youtube import TranscriptFormat
from langchain_core.documents import Document
from youtube_transcript_api import (
    NoTranscriptFound,
    TranscriptsDisabled,
    YouTubeTranscriptApi,
)

from ..youtube_loader import TofuYoutubeLoader, _fetch_with_yt_dlp, _parse_vtt

# Apply django_db mark to all tests in this file
pytestmark = pytest.mark.django_db


class TestParseVtt:
    def test_parse_vtt_basic(self):
        """Test parsing a basic VTT file."""
        vtt_content = """WEBVTT

00:00:01.000 --> 00:00:04.000
Hello, world!

00:00:05.000 --> 00:00:08.000
This is a test.
"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".vtt", delete=False) as tmp:
            tmp.write(vtt_content)
            tmp.flush()

            result = _parse_vtt(Path(tmp.name))

            expected = [
                {"text": "Hello, world!", "start": 1.0, "duration": 3.0},
                {"text": "This is a test.", "start": 5.0, "duration": 3.0},
            ]

            assert result == expected

        # Clean up
        os.unlink(tmp.name)

    def test_parse_vtt_empty_file(self):
        """Test parsing an empty VTT file."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".vtt", delete=False) as tmp:
            tmp.write("WEBVTT\n\n")
            tmp.flush()

            result = _parse_vtt(Path(tmp.name))
            assert result == []

        os.unlink(tmp.name)

    def test_parse_vtt_no_arrows(self):
        """Test parsing a VTT file with no timestamp arrows."""
        vtt_content = """WEBVTT

This is not a timestamp line
"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".vtt", delete=False) as tmp:
            tmp.write(vtt_content)
            tmp.flush()

            result = _parse_vtt(Path(tmp.name))
            assert result == []

        os.unlink(tmp.name)


class TestFetchWithYtDlp:
    @patch("api.data_loaders.youtube_loader.yt_dlp.YoutubeDL")
    @patch("api.data_loaders.youtube_loader.Path")
    def test_fetch_with_yt_dlp_success(self, mock_path, mock_youtube_dl):
        """Test successful yt-dlp fetch."""
        # Mock the YoutubeDL instance
        mock_ydl = MagicMock()
        mock_youtube_dl.return_value.__enter__.return_value = mock_ydl

        # Mock the extract_info result
        mock_ydl.extract_info.return_value = {
            "requested_subtitles": {"en": {"filepath": "/tmp/test_video.en.vtt"}}
        }

        # Mock the Path and file operations
        mock_fp = MagicMock()
        mock_path.return_value = mock_fp

        # Mock _parse_vtt
        with patch("api.data_loaders.youtube_loader._parse_vtt") as mock_parse_vtt:
            mock_parse_vtt.return_value = [
                {"text": "Test transcript", "start": 0.0, "duration": 5.0}
            ]

            result = _fetch_with_yt_dlp("test_video_id")

            assert result == [
                {"text": "Test transcript", "start": 0.0, "duration": 5.0}
            ]
            mock_parse_vtt.assert_called_once_with(mock_fp)
            mock_fp.unlink.assert_called_once_with(missing_ok=True)

    @patch("api.data_loaders.youtube_loader.yt_dlp.YoutubeDL")
    def test_fetch_with_yt_dlp_unsupported_format(self, mock_youtube_dl):
        """Test yt-dlp fetch with unsupported format."""
        mock_ydl = MagicMock()
        mock_youtube_dl.return_value.__enter__.return_value = mock_ydl

        mock_ydl.extract_info.return_value = {
            "requested_subtitles": {"en": {"filepath": "/tmp/test_video.en.srt"}}
        }

        with pytest.raises(ValueError, match="Unsupported subtitle format"):
            _fetch_with_yt_dlp("test_video_id", fmt="srt")


class TestTofuYoutubeLoader:
    def test_extract_video_id_regular_url(self):
        """Test extracting video ID from regular YouTube URL."""
        url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        video_id = TofuYoutubeLoader.extract_video_id(url)
        assert video_id == "dQw4w9WgXcQ"

    def test_extract_video_id_short_url(self):
        """Test extracting video ID from short YouTube URL."""
        url = "https://youtu.be/dQw4w9WgXcQ"
        video_id = TofuYoutubeLoader.extract_video_id(url)
        assert video_id == "dQw4w9WgXcQ"

    @patch("api.data_loaders.youtube_loader.requests.Session")
    @patch("api.data_loaders.youtube_loader.TranscriptListFetcher")
    def test_list_transcripts_success(self, mock_fetcher_class, mock_session):
        """Test successful transcript listing."""
        # Mock the environment variable
        with patch.dict(os.environ, {"SCRAPING_BEE_API_KEY": "test_key"}):
            # Mock the fetcher
            mock_fetcher = MagicMock()
            mock_fetcher_class.return_value = mock_fetcher
            mock_transcript_list = MagicMock()
            mock_fetcher.fetch.return_value = mock_transcript_list

            # Mock the session
            mock_session_instance = MagicMock()
            mock_session.return_value.__enter__.return_value = mock_session_instance

            result = TofuYoutubeLoader.list_transcripts("test_video_id")

            assert result == mock_transcript_list
            mock_session_instance.verify = False
            assert mock_session_instance.proxies is not None

    @patch("api.data_loaders.youtube_loader.requests.Session")
    @patch("api.data_loaders.youtube_loader.TranscriptListFetcher")
    def test_list_transcripts_retry_with_premium(
        self, mock_fetcher_class, mock_session
    ):
        """Test transcript listing with retry using premium proxy."""
        with patch.dict(os.environ, {"SCRAPING_BEE_API_KEY": "test_key"}):
            mock_fetcher = MagicMock()
            mock_fetcher_class.return_value = mock_fetcher

            # First call fails, second succeeds
            mock_fetcher.fetch.side_effect = [
                requests.exceptions.RequestException("First attempt failed"),
                MagicMock(),
            ]

            mock_session_instance = MagicMock()
            mock_session.return_value.__enter__.return_value = mock_session_instance

            with patch("api.data_loaders.youtube_loader.sleep"):
                result = TofuYoutubeLoader.list_transcripts("test_video_id")

            # Should have been called twice
            assert mock_fetcher.fetch.call_count == 2

    @patch("api.data_loaders.youtube_loader.requests.Session")
    @patch("api.data_loaders.youtube_loader.TranscriptListFetcher")
    def test_list_transcripts_max_attempts_exceeded(
        self, mock_fetcher_class, mock_session
    ):
        """Test transcript listing when max attempts are exceeded."""
        with patch.dict(os.environ, {"SCRAPING_BEE_API_KEY": "test_key"}):
            mock_fetcher = MagicMock()
            mock_fetcher_class.return_value = mock_fetcher
            mock_fetcher.fetch.side_effect = requests.exceptions.RequestException(
                "Always fails"
            )

            mock_session_instance = MagicMock()
            mock_session.return_value.__enter__.return_value = mock_session_instance

            with patch("api.data_loaders.youtube_loader.sleep"):
                with pytest.raises(requests.exceptions.RequestException):
                    TofuYoutubeLoader.list_transcripts("test_video_id")

    def test_get_language_list_success(self):
        """Test successful language list generation."""
        mock_transcript_list = MagicMock()
        mock_transcript_list._manually_created_transcripts = {
            "es": MagicMock(),
            "fr": MagicMock(),
        }
        mock_transcript_list._generated_transcripts = {"de": MagicMock()}

        result = TofuYoutubeLoader.get_language_list(mock_transcript_list)

        # Should prefer "en" first, then add others
        assert result[0] == "en"
        assert "es" in result
        assert "fr" in result
        assert "de" in result

    def test_get_language_list_exception(self):
        """Test language list generation when exception occurs."""
        mock_transcript_list = MagicMock()
        mock_transcript_list._manually_created_transcripts = {
            "error": "causes exception"
        }

        # Mock the property to raise an exception
        type(mock_transcript_list)._manually_created_transcripts = property(
            lambda self: (_ for _ in ()).throw(Exception("Test exception"))
        )

        with patch("api.data_loaders.youtube_loader.logging.error") as mock_log:
            result = TofuYoutubeLoader.get_language_list(mock_transcript_list)

            assert result == ["en"]
            mock_log.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    def test_init(self, mock_get_language_list, mock_list_transcripts):
        """Test TofuYoutubeLoader initialization."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en", "es"]

        loader = TofuYoutubeLoader("https://www.youtube.com/watch?v=dQw4w9WgXcQ")

        assert loader.video_id == "dQw4w9WgXcQ"
        assert loader.transcript_list == mock_transcript_list
        assert loader.language == ["en", "es"]

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch.object(TofuYoutubeLoader, "_get_video_info")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_text_format(
        self,
        mock_fetch_yt_dlp,
        mock_get_video_info,
        mock_get_language_list,
        mock_list_transcripts,
    ):
        """Test loading with TEXT format using yt-dlp."""
        # Setup mocks
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]
        mock_get_video_info.return_value = {"title": "Test Video"}

        # Mock yt-dlp success
        mock_fetch_yt_dlp.return_value = [
            {"text": "Hello", "start": 0.0, "duration": 2.0},
            {"text": "World", "start": 2.0, "duration": 2.0},
        ]

        loader = TofuYoutubeLoader(
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            add_video_info=True,
            transcript_format=TranscriptFormat.TEXT,
        )

        documents = loader.load()

        assert len(documents) == 1
        assert documents[0].page_content == "Hello World"
        assert "title" in documents[0].metadata
        mock_fetch_yt_dlp.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_lines_format(
        self, mock_fetch_yt_dlp, mock_get_language_list, mock_list_transcripts
    ):
        """Test loading with LINES format using yt-dlp."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]

        # Mock yt-dlp success
        mock_fetch_yt_dlp.return_value = [
            {"text": "Hello", "start": 0.0, "duration": 2.0},
            {"text": "World", "start": 2.0, "duration": 2.0},
        ]

        loader = TofuYoutubeLoader(
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            transcript_format=TranscriptFormat.LINES,
        )

        documents = loader.load()

        assert len(documents) == 2
        assert documents[0].page_content == "Hello"
        assert documents[1].page_content == "World"
        assert documents[0].metadata["start"] == 0.0
        assert documents[1].metadata["start"] == 2.0
        mock_fetch_yt_dlp.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch.object(TofuYoutubeLoader, "_get_transcript_chunks")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_chunks_format(
        self,
        mock_fetch_yt_dlp,
        mock_get_chunks,
        mock_get_language_list,
        mock_list_transcripts,
    ):
        """Test loading with CHUNKS format using yt-dlp."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]

        # Mock yt-dlp success
        mock_fetch_yt_dlp.return_value = [
            {"text": "Hello", "start": 0.0, "duration": 2.0}
        ]

        mock_chunks = [
            Document(page_content="Chunk 1"),
            Document(page_content="Chunk 2"),
        ]
        mock_get_chunks.return_value = mock_chunks

        loader = TofuYoutubeLoader(
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            transcript_format=TranscriptFormat.CHUNKS,
        )

        documents = loader.load()

        assert documents == mock_chunks
        mock_fetch_yt_dlp.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_yt_dlp_transcripts_disabled_fallback(
        self, mock_fetch_yt_dlp, mock_get_language_list, mock_list_transcripts
    ):
        """Test loading when yt-dlp fails and youtube-transcript-api fallback hits transcripts disabled."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]

        # Mock yt-dlp failure
        mock_fetch_yt_dlp.side_effect = Exception("yt-dlp failed")

        # Mock youtube-transcript-api fallback with transcripts disabled
        mock_transcript = MagicMock()
        mock_transcript_list.find_transcript.return_value = mock_transcript
        mock_transcript.fetch.side_effect = TranscriptsDisabled("test_video_id")

        loader = TofuYoutubeLoader("https://www.youtube.com/watch?v=dQw4w9WgXcQ")

        with patch("api.data_loaders.youtube_loader.logging.warning") as mock_warning:
            documents = loader.load()

            assert documents == []
            mock_warning.assert_called_once()
            mock_fetch_yt_dlp.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_fallback_to_youtube_transcript_api(
        self, mock_fetch_yt_dlp, mock_get_language_list, mock_list_transcripts
    ):
        """Test falling back to youtube-transcript-api when yt-dlp fails."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]

        # Mock yt-dlp failure
        mock_fetch_yt_dlp.side_effect = Exception("yt-dlp failed")

        # Mock youtube-transcript-api success
        mock_transcript = MagicMock()
        mock_transcript_list.find_transcript.return_value = mock_transcript
        mock_transcript.fetch.return_value = [
            {"text": "Fallback transcript", "start": 0.0, "duration": 5.0}
        ]

        loader = TofuYoutubeLoader(
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            transcript_format=TranscriptFormat.TEXT,
        )

        with patch("api.data_loaders.youtube_loader.logging.info") as mock_info:
            documents = loader.load()

            assert len(documents) == 1
            assert documents[0].page_content == "Fallback transcript"
            # Should log yt-dlp failure and youtube-transcript-api success
            assert mock_info.call_count == 2
            mock_fetch_yt_dlp.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_both_methods_fail(
        self, mock_fetch_yt_dlp, mock_get_language_list, mock_list_transcripts
    ):
        """Test when both yt-dlp and youtube-transcript-api fail."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]

        # Mock yt-dlp failure
        mock_fetch_yt_dlp.side_effect = Exception("yt-dlp failed")

        # Mock youtube-transcript-api failure
        mock_transcript = MagicMock()
        mock_transcript_list.find_transcript.return_value = mock_transcript
        mock_transcript.fetch.side_effect = NoTranscriptFound(
            "test_video_id", ["en"], {}
        )

        loader = TofuYoutubeLoader("https://www.youtube.com/watch?v=dQw4w9WgXcQ")

        with patch("api.data_loaders.youtube_loader.logging.error") as mock_error:
            documents = loader.load()

            assert documents == []
            mock_error.assert_called_once()
            mock_fetch_yt_dlp.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_yt_dlp_success_primary(
        self, mock_fetch_yt_dlp, mock_get_language_list, mock_list_transcripts
    ):
        """Test successful loading with yt-dlp as primary method."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]

        # Mock yt-dlp success
        mock_fetch_yt_dlp.return_value = [
            {"text": "Primary yt-dlp transcript", "start": 0.0, "duration": 5.0}
        ]

        loader = TofuYoutubeLoader(
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            transcript_format=TranscriptFormat.TEXT,
        )

        with patch("api.data_loaders.youtube_loader.logging.info") as mock_info:
            documents = loader.load()

            assert len(documents) == 1
            assert documents[0].page_content == "Primary yt-dlp transcript"
            # Should log successful yt-dlp fetch
            mock_info.assert_called_once_with(
                "Successfully fetched transcript using yt-dlp"
            )
            mock_fetch_yt_dlp.assert_called_once()

    @patch.object(TofuYoutubeLoader, "list_transcripts")
    @patch.object(TofuYoutubeLoader, "get_language_list")
    @patch("api.data_loaders.youtube_loader._fetch_with_yt_dlp")
    def test_load_translation_with_fallback(
        self, mock_fetch_yt_dlp, mock_get_language_list, mock_list_transcripts
    ):
        """Test loading with translation when yt-dlp fails and falls back to youtube-transcript-api."""
        mock_transcript_list = MagicMock()
        mock_list_transcripts.return_value = mock_transcript_list
        mock_get_language_list.return_value = ["en"]

        # Mock yt-dlp failure
        mock_fetch_yt_dlp.side_effect = Exception("yt-dlp failed")

        # Mock youtube-transcript-api success with translation
        mock_transcript = MagicMock()
        mock_translated_transcript = MagicMock()
        mock_transcript.translate.return_value = mock_translated_transcript
        mock_transcript_list.find_transcript.return_value = mock_transcript
        mock_translated_transcript.fetch.return_value = [
            {"text": "Hola mundo", "start": 0.0, "duration": 2.0}
        ]

        loader = TofuYoutubeLoader(
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ", translation="es"
        )

        documents = loader.load()

        assert len(documents) == 1
        assert documents[0].page_content == "Hola mundo"
        mock_transcript.translate.assert_called_once_with("es")
        mock_fetch_yt_dlp.assert_called_once()

    def test_load_invalid_transcript_format(self):
        """Test loading with invalid transcript format."""
        with (
            patch.object(TofuYoutubeLoader, "list_transcripts"),
            patch.object(TofuYoutubeLoader, "get_language_list"),
        ):

            loader = TofuYoutubeLoader("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
            loader.transcript_format = "INVALID_FORMAT"

            with pytest.raises(ValueError, match="Unknown transcript format"):
                loader.load()
