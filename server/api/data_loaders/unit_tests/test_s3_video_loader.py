import json
import unittest
from unittest.mock import patch

from langchain_core.documents import Document

from ..s3_video_file_loader import TofuS3AudioVideoFileLoader


class TestTofuS3AudioVideoFileLoader(unittest.TestCase):
    @patch("boto3.client")
    def test_merge_aws_transcription(self, mock_boto3_client):
        # Mock boto3 client
        mock_boto3_client.return_value = None

        # Mock input data
        mock_transcript = {
            "results": {
                "items": [
                    {
                        "start_time": "0.0",
                        "end_time": "0.5",
                        "alternatives": [{"content": "Welcome"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "0.5",
                        "end_time": "0.7",
                        "alternatives": [{"content": "to"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "0.7",
                        "end_time": "1.1",
                        "alternatives": [{"content": "our"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "1.1",
                        "end_time": "1.5",
                        "alternatives": [{"content": "podcast"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {"alternatives": [{"content": "."}], "type": "punctuation"},
                    {
                        "start_time": "1.7",
                        "end_time": "2.0",
                        "alternatives": [{"content": "Today"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "2.0",
                        "end_time": "2.2",
                        "alternatives": [{"content": "we"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "2.2",
                        "end_time": "2.5",
                        "alternatives": [{"content": "have"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "2.5",
                        "end_time": "2.7",
                        "alternatives": [{"content": "a"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "2.7",
                        "end_time": "3.1",
                        "alternatives": [{"content": "special"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {
                        "start_time": "3.1",
                        "end_time": "3.5",
                        "alternatives": [{"content": "guest"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_0",
                    },
                    {"alternatives": [{"content": "."}], "type": "punctuation"},
                    {
                        "start_time": "3.7",
                        "end_time": "4.0",
                        "alternatives": [{"content": "Hello"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_1",
                    },
                    {
                        "start_time": "4.0",
                        "end_time": "4.5",
                        "alternatives": [{"content": "everyone"}],
                        "type": "pronunciation",
                        "speaker_label": "spk_1",
                    },
                    {"alternatives": [{"content": "!"}], "type": "punctuation"},
                ]
            }
        }
        mock_docs = [Document(page_content=json.dumps(mock_transcript))]

        # Create instance of TofuS3AudioVideoFileLoader
        loader = TofuS3AudioVideoFileLoader(
            info={
                "s3_bucket": "test-bucket",
                "s3_filename": "test-file.mp4",
                "mime_file_type": "video/mp4",
            }
        )

        # Call the method
        result = loader.merge_aws_transcription(mock_docs)

        expected_output = (
            "[00:00:00 - 00:00:01] spk_0: Welcome to our podcast.\n"
            "[00:00:01 - 00:00:03] spk_0: Today we have a special guest.\n"
            "[00:00:03 - 00:00:04] spk_1: Hello everyone!"
        )
        self.assertEqual(len(result), 1)
        self.assertIsInstance(result[0], Document)
        self.assertEqual(result[0].page_content, expected_output)
        self.assertEqual(
            result[0].metadata, {"source": "s3://test-bucket/test-file.mp4"}
        )


if __name__ == "__main__":
    unittest.main()
