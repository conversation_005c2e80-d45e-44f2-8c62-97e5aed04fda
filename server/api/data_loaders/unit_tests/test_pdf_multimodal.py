from unittest.mock import MagicMock, patch

from ..pdf_multimodal_loader import PDFPageProcessor


def test_process_page():
    pdf_processor = PDFPageProcessor("test.pdf")
    with patch.object(
        PDFPageProcessor, "convert_page_to_base64"
    ) as mock_convert_page_to_base64:
        mock_convert_page_to_base64.return_value = "test_base64"
        with patch.object(
            pdf_processor.model_caller, "get_results_with_fallback"
        ) as mock_get_results:
            mock_get_results.return_value = [MagicMock(text="Mocked response")]
            result = pdf_processor._process_page(1, None)

    assert result == "Mocked response"
    mock_convert_page_to_base64.assert_called_once_with(1)
    mock_get_results.assert_called_once()
