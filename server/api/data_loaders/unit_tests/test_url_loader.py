import mimetypes
import os
from unittest.mock import MagicMock, patch

import pytest
import requests
from langchain_community.document_loaders import PyPDFLoader
from langchain_core.documents import Document

from ..google_drive_loader import TofuGoogleDriveLoader
from ..s3_file_loader import TofuS3FileLoader
from ..url_loader import TofuURLLoader
from ..url_loader_utils import is_media_url, is_youtube_url, try_parse_google_url
from ..web_page_loader import TofuWebPageLoader
from ..youtube_loader import TofuYoutubeLoader

# Apply django_db mark to all tests in this file
pytestmark = pytest.mark.django_db


@pytest.fixture
def url_loader():
    return TofuURLLoader("https://example.com")


def test_add_schema_if_missing():
    assert TofuURLLoader("example.com").url == "https://example.com"
    assert TofuURLLoader("http://example.com").url == "https://example.com"
    assert TofuURLLoader("https://example.com").url == "https://example.com"


def test_try_parse_google_url():
    assert try_parse_google_url("https://drive.google.com/drive/folders/1234") == {
        "type": "folder",
        "id": "1234",
    }
    assert try_parse_google_url("https://docs.google.com/document/d/1234/edit") == {
        "type": "document",
        "id": "1234",
    }
    assert try_parse_google_url("https://example.com") is None


def test_is_youtube_url():
    assert is_youtube_url("https://www.youtube.com/watch?v=1234")
    assert is_youtube_url("https://youtu.be/1234")
    assert not is_youtube_url("https://example.com")


@pytest.mark.parametrize(
    "url,expected",
    [
        # Video formats
        ("https://example.com/video.mp4", True),
        ("https://example.com/video.webm", True),
        ("https://example.com/video.ogg", True),
        # Audio formats - only include formats that Python's mimetypes recognizes by default
        ("https://example.com/audio.mp3", True),
        ("https://example.com/audio.wav", True),
        ("https://example.com/audio.ogg", True),
        ("https://example.com/audio.webm", True),
        # Non-media formats
        ("https://example.com/doc.pdf", False),
        ("https://example.com/doc.txt", False),
        ("https://example.com/video.mp4.txt", False),
        # No filename in URL
        ("https://example.com/", False),
        ("https://example.com", False),
    ],
)
def test_is_media_url(url, expected):
    assert is_media_url(url) == expected


@pytest.mark.parametrize(
    "test_url,expected_filename",
    [
        # Video formats
        ("https://example.com/video.mp4", "video.mp4"),
        ("https://example.com/video.webm", "video.webm"),
        ("https://example.com/video.ogg", "video.ogg"),
        # Audio formats - only include formats that Python's mimetypes recognizes by default
        ("https://example.com/audio.mp3", "audio.mp3"),
        ("https://example.com/audio.wav", "audio.wav"),
        ("https://example.com/audio.ogg", "audio.ogg"),
        # Special characters
        ("https://example.com/测试音频.mp3", "____.mp3"),
        ("https://example.com/%E6%B5%8B%E8%AF%95.ogg", "__.ogg"),
        # URL encoded cases
        ("https://example.com/test%20audio.mp3", "test_audio.mp3"),
        # Query parameters
        ("https://example.com/audio.mp3?quality=high", "audio.mp3"),
        # Long filenames
        ("https://example.com/" + "a" * 100 + ".mp3", "a" * 100 + ".mp3"),
    ],
)
def test_get_s3_info_for_media_various_cases(test_url, expected_filename):
    loader = TofuURLLoader(test_url)
    s3_info = loader._get_s3_info_for_media(test_url)

    # Basic assertions
    assert s3_info["s3_bucket"] == "tofu-uploaded-files"
    mime_type, _ = mimetypes.guess_type(test_url)
    assert s3_info["mime_file_type"] == mime_type

    # Filename assertions
    assert s3_info["s3_filename"].startswith(
        "url_media/"
    )  # Keep url_videos to match implementation

    # Extract the actual filename part (after the hash)
    actual_filename = s3_info["s3_filename"].split("-", 1)[1]

    if expected_filename is None:
        # For URLs without filenames, just verify the pattern
        assert actual_filename.endswith(
            "_video.mp4"
        )  # Keep _video.mp4 to match implementation
        assert len(actual_filename) > len("_video.mp4")  # Should have hash prefix
    else:
        assert actual_filename == expected_filename


@patch("api.data_loaders.url_loader.download_url_content_to_s3")
def test_get_loader_media(mock_download):
    # Test different media types
    test_cases = [
        ("video.mp4", "video/mp4"),
        ("audio.mp3", "audio/mpeg"),
        ("audio.wav", "audio/x-wav"),
        ("video.webm", "video/webm"),
    ]

    for filename, content_type in test_cases:
        url = f"https://example.com/{filename}"
        loader = TofuURLLoader(url)

        result = loader.get_loader(url, False)

        # Verify download was called
        mock_download.assert_called()
        call_args = mock_download.call_args[1]
        assert call_args["url"] == url
        assert call_args["s3_bucket"] == "tofu-uploaded-files"
        assert call_args["s3_filename"].startswith("url_media/")
        assert call_args["content_type"] == content_type

        # Verify correct loader type returned
        assert isinstance(result, TofuS3FileLoader)

        mock_download.reset_mock()


@patch("api.data_loaders.url_loader.download_url_content_to_s3")
def test_get_loader_mp4(mock_download):
    url = "https://example.com/video.mp4"
    loader = TofuURLLoader(url)

    result = loader.get_loader(url, False)

    # Verify download was called
    mock_download.assert_called_once()
    call_args = mock_download.call_args[1]
    assert call_args["url"] == url
    assert call_args["s3_bucket"] == "tofu-uploaded-files"
    assert call_args["s3_filename"].startswith("url_media/")
    assert call_args["content_type"] == "video/mp4"

    # Verify correct loader type returned
    assert isinstance(result, TofuS3FileLoader)

    # Test cleanup on error
    mock_download.reset_mock()
    mock_download.side_effect = requests.exceptions.RequestException("Network error")

    with pytest.raises(requests.exceptions.RequestException):
        loader.get_loader(url, False)

    # Verify temp files are cleaned up
    import tempfile

    temp_files = os.listdir(tempfile.gettempdir())
    assert not any(f.startswith("tofu_") for f in temp_files)


@patch("api.data_loaders.url_loader.download_url_content_to_s3")
def test_get_loader_mp4_error(mock_download):
    url = "https://example.com/video.mp4"
    mock_download.side_effect = Exception("Download failed")
    loader = TofuURLLoader(url)

    with pytest.raises(Exception, match="Download failed"):
        loader.get_loader(url, False)


@pytest.mark.parametrize(
    "url,expected_loader",
    [
        ("https://example.com/file.pdf", PyPDFLoader),
        ("https://drive.google.com/file/d/1234/view", TofuGoogleDriveLoader),
        ("https://www.youtube.com/watch?v=1234", TofuYoutubeLoader),
        ("https://example.com", TofuWebPageLoader),
        ("https://example.com/file.mp4", TofuS3FileLoader),
    ],
)
@patch("api.data_loaders.url_loader.PyPDFLoader")
@patch("api.data_loaders.url_loader.TofuGoogleDriveLoader")
@patch("api.data_loaders.url_loader.TofuYoutubeLoader")
@patch("api.data_loaders.url_loader.TofuWebPageLoader")
@patch("api.data_loaders.url_loader.download_url_content_to_s3")
def test_get_loader(
    mock_download, mock_web, mock_youtube, mock_gdrive, mock_pdf, url, expected_loader
):
    loader = TofuURLLoader(url).get_loader(url, False)
    if expected_loader == PyPDFLoader:
        assert isinstance(loader, MagicMock)
        mock_pdf.assert_called_once_with(url)
    elif expected_loader == TofuGoogleDriveLoader:
        assert isinstance(loader, MagicMock)
        mock_gdrive.assert_called_once()
    elif expected_loader == TofuYoutubeLoader:
        assert isinstance(loader, MagicMock)
        mock_youtube.assert_called_once_with(url=url, add_video_info=False)
    elif expected_loader == TofuWebPageLoader:
        assert isinstance(loader, MagicMock)
        mock_web.assert_called_once_with(url, False)
    elif expected_loader == TofuS3FileLoader:
        assert isinstance(loader, TofuS3FileLoader)
        mock_download.assert_called_once()


def test_remove_empty_docs():
    docs = [
        Document(page_content=""),
        Document(page_content="Content"),
        Document(page_content="  "),
        Document(page_content="More content"),
    ]
    filtered_docs = TofuURLLoader("https://example.com").remove_empty_docs(docs)
    assert len(filtered_docs) == 2
    assert all(doc.page_content.strip() for doc in filtered_docs)


@patch("api.data_loaders.url_loader.cache")
@patch("api.data_loaders.url_loader.TofuURLLoader.get_loader")
def test_load_cache_hit(mock_get_loader, mock_cache, url_loader):
    mock_cache.get.return_value = [Document(page_content="Cached content")]
    docs = url_loader.load()
    assert len(docs) == 1
    assert docs[0].page_content == "Cached content"
    mock_get_loader.assert_not_called()


@patch("api.data_loaders.url_loader.cache")
@patch("api.data_loaders.url_loader.TofuURLLoader.get_loader")
def test_load_cache_miss(mock_get_loader, mock_cache, url_loader):
    mock_cache.get.return_value = None
    mock_loader = MagicMock()
    mock_loader.load.return_value = [Document(page_content="Fresh content")]
    mock_get_loader.return_value = mock_loader

    docs = url_loader.load()
    assert len(docs) == 1
    assert docs[0].page_content == "Fresh content"
    mock_get_loader.assert_called_once()
    mock_cache.set.assert_called_once()


@patch("api.data_loaders.url_loader.cache")
@patch("api.data_loaders.url_loader.TofuURLLoader.get_loader")
def test_load_error_handling(mock_get_loader, mock_cache, url_loader):
    mock_cache.get.return_value = None
    mock_loader = MagicMock()
    mock_loader.load.side_effect = Exception("Loading error")
    mock_get_loader.return_value = mock_loader

    with pytest.raises(Exception, match="Loading error"):
        url_loader.load()

    mock_cache.set.assert_called_once()


def test_is_short_error_cache():
    assert TofuURLLoader.is_short_error_cache("https://www.youtube.com/watch?v=1234")
    assert TofuURLLoader.is_short_error_cache(
        "https://drive.google.com/file/d/1234/view"
    )
    assert not TofuURLLoader.is_short_error_cache("https://example.com")
