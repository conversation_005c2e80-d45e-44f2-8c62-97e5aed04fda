from unittest.mock import MagicMock, patch

import pytest
import tldextract
from bs4 import BeautifulSoup
from langchain_core.documents import Document

from ..web_page_loader import TofuWebPageLoader

# Apply django_db mark to all tests in this file
pytestmark = pytest.mark.django_db


@pytest.fixture
def web_page_loader_folder():
    return TofuWebPageLoader("https://example.com/folder", deep_crawl=True)


@pytest.fixture
def web_page_loader():
    return TofuWebPageLoader("https://example.com", deep_crawl=True)


def test_load_deep_same_folder_filtering(web_page_loader_folder):
    # Mock load_shallow for different URLs
    def mock_load_shallow(self):
        if self.url == "https://example.com/folder":
            return [Document(page_content="Main page content")]
        elif self.url == "https://example.com/folder/page1":
            return [Document(page_content="Page 1 content")]
        elif self.url == "https://example.com/folder/page2":
            return [Document(page_content="Page 2 content")]
        elif self.url == "https://example.com/other/page3":
            return [Document(page_content="Page 3 content")]
        elif self.url == "https://example.com/cfolder":
            return [Document(page_content="CFolder content")]
        return []

    # Mock _scrape to return HTML with links
    def mock_scrape(self, url, parser=None, bs_kwargs=None):
        if url == "https://example.com/folder":
            html = """
            <html>
                <body>
                    <a href="/folder/page1">Page 1</a>
                    <a href="/folder/page2">Page 2</a>
                    <a href="/other/page3">Page 3</a>
                    <a href="/cfolder">CFolder</a>
                </body>
            </html>
            """
        elif url == "https://example.com/folder/page1":
            html = "<html><body><a href='/folder/subpage'>Subpage</a></body></html>"
        elif url == "https://example.com/folder/page2":
            html = "<html><body>No links</body></html>"
        elif url == "https://example.com/other/page3":
            html = "<html><body>No links</body></html>"
        elif url == "https://example.com/cfolder":
            html = "<html><body>No links</body></html>"
        else:
            html = "<html><body>No links</body></html>"
        return BeautifulSoup(html, "html.parser")

    def mock_init(self, url, deep_crawl=False, **kwargs):
        self.url = url
        extracted_url = tldextract.extract(url)
        self.domain = extracted_url.domain
        self.domain_suffix = extracted_url.suffix
        return None

    with (
        patch("api.data_loaders.web_page_loader.TofuWebPageLoader.__init__", mock_init),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader.load_shallow",
            mock_load_shallow,
        ),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader._scrape", mock_scrape
        ),
    ):
        # Test with deep_crawl_only_same_folder=True
        docs = web_page_loader_folder.load_deep(deep_crawl_only_same_folder=True)

        # Verify that only URLs in the same folder were processed
        assert len(docs) == 3  # Main page + two subpages in same folder
        assert all(
            any(
                content in doc.page_content
                for content in ["Main page content", "Page 1 content", "Page 2 content"]
            )
            for doc in docs
        )
        # Verify that URLs from other folders were not processed
        assert not any("CFolder content" in doc.page_content for doc in docs)
        assert not any("Page 3 content" in doc.page_content for doc in docs)

        # Test with deep_crawl_only_same_folder=False
        docs = web_page_loader_folder.load_deep(deep_crawl_only_same_folder=False)

        # Verify that all URLs were processed
        assert len(docs) == 5  # Main page + all subpages including cfolder
        assert all(
            any(
                content in doc.page_content
                for content in [
                    "Main page content",
                    "Page 1 content",
                    "Page 2 content",
                    "Page 3 content",
                    "CFolder content",
                ]
            )
            for doc in docs
        )


def test_load_deep_max_depth_limit(web_page_loader):
    # Mock load_shallow for different URLs
    def mock_load_shallow(self):
        if self.url == "https://example.com":
            return [Document(page_content="Main page content")]
        elif self.url == "https://example.com/page1":
            return [Document(page_content="Page 1 content")]
        elif self.url == "https://example.com/page1/subpage":
            return [Document(page_content="Subpage content")]
        return []

    # Mock _scrape to return HTML with links
    def mock_scrape(self, url, parser=None, bs_kwargs=None):
        if url == "https://example.com":
            html = """
            <html>
                <body>
                    <a href="/page1">Page 1</a>
                </body>
            </html>
            """
        elif url == "https://example.com/page1":
            html = """
            <html>
                <body>
                    <a href="/page1/subpage">Subpage</a>
                </body>
            </html>
            """
        else:
            html = "<html><body>No links</body></html>"
        return BeautifulSoup(html, "html.parser")

    def mock_init(self, url, deep_crawl=False, **kwargs):
        self.url = url
        extracted_url = tldextract.extract(url)
        self.domain = extracted_url.domain
        self.domain_suffix = extracted_url.suffix
        return None

    with (
        patch("api.data_loaders.web_page_loader.TofuWebPageLoader.__init__", mock_init),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader.load_shallow",
            mock_load_shallow,
        ),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader._scrape", mock_scrape
        ),
    ):
        # Set max depth to 1
        web_page_loader.crawler_max_depth = 1

        # Test deep crawling
        docs = web_page_loader.load_deep()

        # Verify that only URLs within max_depth were processed
        assert len(docs) == 2  # Main page + one subpage
        assert all(
            any(
                content in doc.page_content
                for content in ["Main page content", "Page 1 content"]
            )
            for doc in docs
        )


def test_load_deep_max_pages_limit(web_page_loader):
    # Mock load_shallow for different URLs
    def mock_load_shallow(self):
        if self.url == "https://example.com":
            return [Document(page_content="Main page content")]
        elif self.url.startswith("https://example.com/page"):
            page_num = int(self.url.split("/")[-1].replace("page", ""))
            return [Document(page_content=f"Page {page_num} content")]
        return []

    # Mock _scrape to return HTML with links
    def mock_scrape(self, url, parser=None, bs_kwargs=None):
        if url == "https://example.com":
            html = """
            <html>
                <body>
                    <a href="/page0">Page 0</a>
                    <a href="/page1">Page 1</a>
                    <a href="/page2">Page 2</a>
                    <a href="/page3">Page 3</a>
                    <a href="/page4">Page 4</a>
                </body>
            </html>
            """
        else:
            html = "<html><body>No links</body></html>"
        return BeautifulSoup(html, "html.parser")

    def mock_init(self, url, deep_crawl=False, **kwargs):
        self.url = url
        extracted_url = tldextract.extract(url)
        self.domain = extracted_url.domain
        self.domain_suffix = extracted_url.suffix
        return None

    with (
        patch("api.data_loaders.web_page_loader.TofuWebPageLoader.__init__", mock_init),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader.load_shallow",
            mock_load_shallow,
        ),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader._scrape", mock_scrape
        ),
    ):
        # Set max pages to 5
        web_page_loader.crawler_max_page = 5

        # Test deep crawling
        docs = web_page_loader.load_deep()

        # Verify that only max_pages were processed
        assert len(docs) == 4  # Main page + 3 subpages (limited by max_pages)


def test_load_deep_always_crawl_next_page(web_page_loader):
    # Mock load_shallow for different URLs
    def mock_load_shallow(self):
        if self.url == "https://example.com":
            return [Document(page_content="Main page content")]
        elif self.url == "https://example.com/page1":
            return [Document(page_content="Page 1 content")]
        elif self.url == "https://example.com/page2":
            return [Document(page_content="Page 2 content")]
        elif self.url == "https://example.com/page1/subpage":
            return [Document(page_content="Subpage content")]
        elif self.url == "https://example.com/page2?page=2":
            return [Document(page_content="Page 2 - Page 2 content")]
        elif self.url == "https://example.com/page2?page=3":
            return [Document(page_content="Page 2 - Page 3 content")]
        return []

    # Mock _scrape to return HTML with links
    def mock_scrape(self, url, parser=None, bs_kwargs=None):
        if url == "https://example.com":
            html = """
            <html>
                <body>
                    <a href="/page1">Page 1</a>
                    <a href="/page2">Page 2</a>
                </body>
            </html>
            """
        elif url == "https://example.com/page1":
            html = """
            <html>
                <body>
                    <a href="/page1/subpage">Subpage</a>
                </body>
            </html>
            """
        elif url == "https://example.com/page2":
            html = """
            <html>
                <body>
                    <a href="/page2?page=2">Next Page</a>
                </body>
            </html>
            """
        elif url == "https://example.com/page2?page=2":
            html = """
            <html>
                <body>
                    <a href="/page2?page=3">Next Page</a>
                </body>
            </html>
            """
        else:
            html = "<html><body>No links</body></html>"
        return BeautifulSoup(html, "html.parser")

    def mock_init(self, url, deep_crawl=False, **kwargs):
        self.url = url
        extracted_url = tldextract.extract(url)
        self.domain = extracted_url.domain
        self.domain_suffix = extracted_url.suffix
        return None

    with (
        patch("api.data_loaders.web_page_loader.TofuWebPageLoader.__init__", mock_init),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader.load_shallow",
            mock_load_shallow,
        ),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader._scrape", mock_scrape
        ),
    ):
        # Set max depth to 1 and max pages to 10 to allow all pagination URLs
        web_page_loader.crawler_max_depth = 1
        web_page_loader.crawler_max_page = 10

        # Test with always_crawl_next_page=False (default)
        docs = web_page_loader.load_deep(always_crawl_next_page=False)

        # Verify that pagination URLs were not processed beyond depth limit
        assert len(docs) == 3  # Main page + page1 + page2
        assert all(
            any(
                content in doc.page_content
                for content in ["Main page content", "Page 1 content", "Page 2 content"]
            )
            for doc in docs
        )
        assert not any("Page 2 - Page 2 content" in doc.page_content for doc in docs)
        assert not any("Page 2 - Page 3 content" in doc.page_content for doc in docs)
        assert not any("Subpage content" in doc.page_content for doc in docs)

        # Test with always_crawl_next_page=True
        docs = web_page_loader.load_deep(always_crawl_next_page=True)

        # Verify that pagination URLs were processed even beyond depth limit
        assert (
            len(docs) == 6
        )  # Main page + page1 + page2 + page2?page=2 + page2?page=3 + page1/subpage
        assert all(
            any(
                content in doc.page_content
                for content in [
                    "Main page content",
                    "Page 1 content",
                    "Page 2 content",
                    "Page 2 - Page 2 content",
                    "Page 2 - Page 3 content",
                    "Subpage content",
                ]
            )
            for doc in docs
        )


def test_load_deep_skipped_patterns(web_page_loader):
    # Mock load_shallow for different URLs
    def mock_load_shallow(self):
        if self.url == "https://example.com":
            return [Document(page_content="Main page content")]
        elif self.url == "https://example.com/page1":
            return [Document(page_content="Page 1 content")]
        elif self.url == "https://example.com/page2":
            return [Document(page_content="Page 2 content")]
        elif self.url == "https://example.com/admin/login":
            return [Document(page_content="Admin login content")]
        elif self.url == "https://example.com/api/v1/users":
            return [Document(page_content="API users content")]
        elif self.url == "https://example.com/private/dashboard":
            return [Document(page_content="Private dashboard content")]
        return []

    # Mock _scrape to return HTML with links
    def mock_scrape(self, url, parser=None, bs_kwargs=None):
        if url == "https://example.com":
            html = """
            <html>
                <body>
                    <a href="/page1">Page 1</a>
                    <a href="/page2">Page 2</a>
                    <a href="/admin/login">Admin Login</a>
                    <a href="/api/v1/users">API Users</a>
                    <a href="/private/dashboard">Dashboard</a>
                    <a href="https://example.com/page1/subpage.pdf">Subpage</a>
                    <a href="https://example.com/page1/subpage_video.mp4">Subpage</a>
                    <a href="https://example.com/page1/subpage_report.png?v=1">Subpage</a>
                </body>
            </html>
            """
        else:
            html = "<html><body>No links</body></html>"
        return BeautifulSoup(html, "html.parser")

    def mock_init(self, url, deep_crawl=False, **kwargs):
        self.url = url
        extracted_url = tldextract.extract(url)
        self.domain = extracted_url.domain
        self.domain_suffix = extracted_url.suffix
        return None

    with (
        patch("api.data_loaders.web_page_loader.TofuWebPageLoader.__init__", mock_init),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader.load_shallow",
            mock_load_shallow,
        ),
        patch(
            "api.data_loaders.web_page_loader.TofuWebPageLoader._scrape", mock_scrape
        ),
    ):
        # Set max pages to 10 to allow all URLs to be processed
        web_page_loader.crawler_max_page = 10

        # Test with no skipped patterns
        docs = web_page_loader.load_deep(skipped_patterns=[])

        # Verify that all URLs were processed
        assert len(docs) == 6  # All pages including admin, api, and private
        assert all(
            any(
                content in doc.page_content
                for content in [
                    "Main page content",
                    "Page 1 content",
                    "Page 2 content",
                    "Admin login content",
                    "API users content",
                    "Private dashboard content",
                ]
            )
            for doc in docs
        )

        # Test with skipped patterns
        skipped_patterns = ["/admin/", "/api/", "/private/"]
        docs = web_page_loader.load_deep(skipped_patterns=skipped_patterns)

        # Verify that URLs matching skipped patterns were not processed
        assert len(docs) == 3  # Only main page and public pages
        assert all(
            any(
                content in doc.page_content
                for content in ["Main page content", "Page 1 content", "Page 2 content"]
            )
            for doc in docs
        )
        # Verify that skipped URLs were not processed
        assert not any("Admin login content" in doc.page_content for doc in docs)
        assert not any("API users content" in doc.page_content for doc in docs)
        assert not any("Private dashboard content" in doc.page_content for doc in docs)


@patch("api.data_loaders.web_page_loader.is_media_url")
@patch("api.data_loaders.web_page_loader.is_youtube_url")
@patch("api.data_loaders.web_page_loader.try_parse_google_url")
def test_is_web_page_loader_supported_url(
    mock_try_parse_google_url, mock_is_youtube_url, mock_is_media_url
):
    # Set default return values for mocks
    mock_is_media_url.return_value = False
    mock_is_youtube_url.return_value = False
    mock_try_parse_google_url.return_value = None

    # Test case 1: Regular HTML page
    assert (
        TofuWebPageLoader._is_web_page_loader_supported_url(
            "https://example.com/page.html"
        )
        == True
    )

    # Test case 2: PDF file
    assert (
        TofuWebPageLoader._is_web_page_loader_supported_url(
            "https://example.com/document.pdf"
        )
        == False
    )

    # Test case 3: PDF file with query parameters
    assert (
        TofuWebPageLoader._is_web_page_loader_supported_url(
            "https://example.com/document.pdf?version=1"
        )
        == False
    )

    # Test case 4: Media URL
    mock_is_media_url.return_value = True
    assert (
        TofuWebPageLoader._is_web_page_loader_supported_url(
            "https://example.com/video.mp4"
        )
        == False
    )
    mock_is_media_url.return_value = False

    # Test case 5: YouTube URL
    mock_is_youtube_url.return_value = True
    assert (
        TofuWebPageLoader._is_web_page_loader_supported_url(
            "https://youtube.com/watch?v=12345"
        )
        == False
    )
    mock_is_youtube_url.return_value = False

    # Test case 6: Google URL
    mock_try_parse_google_url.return_value = {"type": "document", "id": "12345"}
    assert (
        TofuWebPageLoader._is_web_page_loader_supported_url(
            "https://docs.google.com/document/d/12345"
        )
        == False
    )
    mock_try_parse_google_url.return_value = None

    # Test case 7: URL with non-PDF extension
    assert (
        TofuWebPageLoader._is_web_page_loader_supported_url(
            "https://example.com/page.php"
        )
        == True
    )

    # Verify that the helper functions were called with the correct arguments
    mock_is_media_url.assert_called_with("https://example.com/page.php")
    mock_is_youtube_url.assert_called_with("https://example.com/page.php")
    mock_try_parse_google_url.assert_called_with("https://example.com/page.php")
