import logging
import os
import re
import traceback
import xml.etree.ElementTree as ET
from pathlib import Path
from time import sleep
from typing import Any, Dict, List

import requests
import yt_dlp
from langchain_community.document_loaders import YoutubeLoader
from langchain_community.document_loaders.youtube import TranscriptFormat
from langchain_core.documents import Document
from youtube_transcript_api._transcripts import TranscriptListFetcher

_VTT_TIMESTAMP = re.compile(r"(?P<h>\d\d):(?P<m>\d\d):(?P<s>\d\d)\.(?P<ms>\d{3})")


def _parse_vtt(fp: Path) -> List[Dict[str, Any]]:
    """
    Convert a WebVTT file to the same list-of-dicts structure returned by
    youtube-transcript-api.  Only supports plain cue blocks (no styling).
    """
    pieces: List[Dict[str, Any]] = []
    with fp.open(encoding="utf-8") as fh:
        lines = fh.readlines()
        i = 0
        while i < len(lines):
            line = lines[i]
            if " --> " not in line:
                i += 1
                continue  # skip headers & blank lines
            start_raw, end_raw = line.strip().split(" --> ")[:2]
            # Collect caption text (can be multi-line until blank line)
            text_lines = []
            i += 1
            while i < len(lines) and lines[i].strip():
                text_lines.append(lines[i].rstrip("\n"))
                i += 1
            text = " ".join(text_lines)

            def _ts_to_sec(ts: str) -> float:
                m = _VTT_TIMESTAMP.match(ts)
                return (
                    int(m["h"]) * 3600
                    + int(m["m"]) * 60
                    + int(m["s"])
                    + int(m["ms"]) / 1000
                )

            start = _ts_to_sec(start_raw)
            duration = _ts_to_sec(end_raw) - start
            pieces.append({"text": text, "start": start, "duration": duration})
            i += 1
    return pieces


def _fetch_with_yt_dlp(
    video_id: str,
    lang: str = "en",
    *,
    auto: bool = True,
    fmt: str = "vtt",
    tmp_dir: str = ".yt_subs_tmp",
) -> List[Dict[str, Any]]:
    """
    Download captions with yt-dlp and return them in a youtube-transcript-api-like
    list-of-dicts structure.
    """
    Path(tmp_dir).mkdir(exist_ok=True)

    ydl_opts = {
        "skip_download": True,
        "writesubtitles": not auto,
        "writeautomaticsub": auto,
        "subtitleslangs": [lang],
        "subtitlesformat": fmt,
        "outtmpl": f"{tmp_dir}/%(id)s.%(language)s.%(ext)s",
        "quiet": True,
        "nopart": True,
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(video_id, download=True)

    if "requested_subtitles" not in info or lang not in info["requested_subtitles"]:
        raise ValueError(f"No subtitles found for language: {lang}")

    meta = info["requested_subtitles"][lang]
    if not meta or "filepath" not in meta:
        raise ValueError(f"No subtitle file path found for language: {lang}")

    fp = Path(meta["filepath"])

    try:
        if fmt == "vtt":
            return _parse_vtt(fp)
        raise ValueError(f"Unsupported subtitle format: {fmt!r}")
    finally:
        fp.unlink(missing_ok=True)  # clean up


class TofuYoutubeLoader(YoutubeLoader):

    @staticmethod
    def list_transcripts(video_id):
        SCRAPING_BEE_API_KEY = os.getenv("SCRAPING_BEE_API_KEY")
        proxies = {
            "http": f"http://{SCRAPING_BEE_API_KEY}:render_js=<EMAIL>:8886",
            "https": f"https://{SCRAPING_BEE_API_KEY}:render_js=<EMAIL>:8887",
            "socks5": f"socks5://{SCRAPING_BEE_API_KEY}:render_js=<EMAIL>:8888",
        }
        premium_proxies = {
            "http": f"http://{SCRAPING_BEE_API_KEY}:render_js=False&premium_proxy=<EMAIL>:8886",
            "https": f"https://{SCRAPING_BEE_API_KEY}:render_js=False&premium_proxy=<EMAIL>:8887",
            "socks5": f"socks5://{SCRAPING_BEE_API_KEY}:render_js=False&premium_proxy=<EMAIL>:8888",
        }
        attempts = 0
        max_attempts = 2  # Original attempt + 1 retry

        while attempts < max_attempts:
            try:
                with requests.Session() as http_client:
                    # [Tofu customized code] disable SSL verification,
                    # otherwise, we will have to install the SSL certificate of the proxy server which is a lot of trouble
                    http_client.verify = False
                    http_client.proxies = (
                        premium_proxies
                        if attempts > 0 and premium_proxies
                        else proxies or {}
                    )
                    return TranscriptListFetcher(http_client).fetch(video_id)
            except Exception as e:
                attempts += 1
                if attempts >= max_attempts:
                    raise e
                else:
                    logging.error(
                        f"Attempt {attempts} failed to extract transcript from youtube video with id {video_id}, retrying... Error: {e}"
                    )
                    sleep(1)

    @staticmethod
    def get_language_list(transcript_list):
        try:
            # this will ensure english is the preferred language to return
            # otherwise, it will fall back to other languages available
            existing_transcript_languages = list(
                transcript_list._manually_created_transcripts.keys()
            ) + list(transcript_list._generated_transcripts.keys())
            return ["en"] + existing_transcript_languages
        except Exception as e:
            logging.error(
                f"Failed to get transcript languages due to {e}, using default language 'en'\n{traceback.format_exc()}"
            )
            return ["en"]

    def __init__(self, url: str, **kwargs):
        video_id = TofuYoutubeLoader.extract_video_id(url)
        super().__init__(video_id=video_id, **kwargs)

        self.transcript_list = TofuYoutubeLoader.list_transcripts(self.video_id)
        self.language = TofuYoutubeLoader.get_language_list(self.transcript_list)

    # Overwrite load method to use our version of list_transcripts in order to bypass ssl
    def load(self) -> List[Document]:
        """Load YouTube transcripts into `Document` objects with yt-dlp first priority."""
        try:
            from youtube_transcript_api import (
                NoTranscriptFound,
                TranscriptsDisabled,
                YouTubeTranscriptApi,
            )
        except ImportError as e:
            raise ImportError(
                'Missing "youtube_transcript_api". Install with '
                "`pip install youtube-transcript-api`."
            ) from e

        # -- gather optional video-meta --
        if self.add_video_info:
            self._metadata.update(self._get_video_info())

        pieces: List[Dict[str, Any]] = []

        # -------------------------------
        # 1️⃣  Primary attempt: yt-dlp
        # -------------------------------
        try:
            pieces = _fetch_with_yt_dlp(
                self.video_id,
                lang=(
                    self.language[0]
                    if isinstance(self.language, list) and len(self.language) > 0
                    else "en"
                ),
                auto=True,
                fmt="vtt",
            )
            logging.info("Successfully fetched transcript using yt-dlp")
        except Exception as yterr:
            logging.info(
                "yt-dlp failed (%s). Falling back to youtube-transcript-api …",
                yterr.__class__.__name__,
            )

            # -------------------------------
            # 2️⃣  youtube-transcript-api fallback
            # -------------------------------
            try:
                transcript = self.transcript_list.find_transcript(self.language)
            except NoTranscriptFound:
                transcript = self.transcript_list.find_transcript(["en"])

            if self.translation is not None:
                transcript = transcript.translate(self.translation)

            try:
                pieces = transcript.fetch()
                logging.info(
                    "Successfully fetched transcript using youtube-transcript-api fallback"
                )
            except (TranscriptsDisabled,) as fatal:
                # Creator explicitly disabled captions → nothing to do
                logging.warning("Transcripts disabled for %s: %s", self.video_id, fatal)
                return []
            except (NoTranscriptFound, ET.ParseError, Exception) as non_fatal:
                logging.error(
                    "Both yt-dlp and youtube-transcript-api failed. yt-dlp error: %s, youtube-transcript-api error: %s",
                    yterr.__class__.__name__,
                    non_fatal.__class__.__name__,
                )
                return []

        # ------------------------------------------------------------------
        # Re-use your existing post-processing paths (TEXT / LINES / CHUNKS)
        # ------------------------------------------------------------------
        if self.transcript_format == TranscriptFormat.TEXT:
            joined = " ".join(p["text"].strip() for p in pieces)
            return [Document(page_content=joined, metadata=self._metadata)]

        if self.transcript_format == TranscriptFormat.LINES:
            return [
                Document(
                    page_content=p["text"].strip(),
                    metadata={k: v for k, v in p.items() if k != "text"},
                )
                for p in pieces
            ]

        if self.transcript_format == TranscriptFormat.CHUNKS:
            return list(self._get_transcript_chunks(pieces))

        raise ValueError("Unknown transcript format.")
