import logging
import traceback
from typing import Any, List

import fitz
from django.core.cache import cache
from langchain_community.document_loaders import (
    <PERSON><PERSON><PERSON>oa<PERSON>,
    S3FileLoader,
    UnstructuredPowerPointLoader,
    UnstructuredWordDocumentLoader,
)
from langchain_core.document_loaders import BaseLoader
from langchain_core.documents import Document

from ..utils import TempS3File
from .pdf_multimodal_loader import PDFMultiModalLoader
from .s3_video_file_loader import TofuS3AudioVideoFileLoader

# TODO: unstructured_inference pytesseract
#   /Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/unstructured/partition/auto.py
# known bug: loading images have issues on tesseract dependencies

# Issue 1: Celery worker will meet crash on MacOS:
# objc[75040]: +[NSMutableString initialize] may have been in progress in another thread when fork() was called.
# objc[75040]: +[NSMutableString initialize] may have been in progress in another thread when fork() was called. We cannot safely call it or ignore it in the fork() child process. Crashing instead. Set a breakpoint on objc_initializeAfterForkError to debug.
# solution:
#  export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES; celery -A server worker -l info --beat
#  The error would be printed but executed correctly
#    The process has forked and you cannot use this CoreFoundation functionality safely. You MUST exec().
#    Break on __THE_PROCESS_HAS_FORKED_AND_YOU_CANNOT_USE_THIS_COREFOUNDATION_FUNCTIONALITY___YOU_MUST_EXEC__() to debug.
# Issue 2: It requires download data from nltk with disabling ssl cert for download as:
# import ssl
# ssl._create_default_https_context = ssl._create_unverified_context
# import nltk
# nltk.download('punkt')


class TofuS3FileLoader(BaseLoader):
    """
    Load data from file uploaded to AWS S3.
    Returns an array of Langchain Documents.
    Input is generated at FE, example:
    {
        mime_file_type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        original_filename: "tofu_test.docx",
        s3_bucket: "tofu-uploaded-files",
        s3_filename: "53fbfc8d-80fd-90f6-3f7e-b99e2a28d3ee-tofu_test.docx",
        s3_presigned_path: "/api/web/storage/s3-presigned-url?file=53fbfc8d-80…lformats-officedocument.wordprocessingml.document",
    }
    """

    def __init__(
        self,
        info: dict,
    ):
        self.s3_bucket = info["s3_bucket"]
        self.s3_filename = info["s3_filename"]
        self.file_type = info["mime_file_type"]
        self.total_duration = info.get("total_duration", None)
        self.file_size = info.get("file_size", None)

    def remove_empty_docs(self, docs: List[Document]) -> List[Document]:
        # remove all docs that has empty page_content
        docs = [doc for doc in docs if doc.page_content.strip() != ""]
        return docs

    def pdf_page_count(self):
        with TempS3File(self.s3_bucket, self.s3_filename) as tmp_pdf_file:
            with fitz.open(tmp_pdf_file) as doc:
                num_pages = doc.page_count

        return num_pages

    def load(
        self,
        disable_cache=False,
        update_cache=False,
        fast_return=False,
        enable_multimodal=True,
    ) -> List[Document]:
        try:
            logging.info(f"Loading data from file: {self.s3_filename}")
            # try to get the data from cache first
            try:
                cached_data = cache.get(self.s3_filename)
            except Exception as e:
                cached_data = None

            if not disable_cache and cached_data:
                if update_cache:
                    cache.set(
                        self.s3_filename, cached_data, 60 * 60 * 24 * 7
                    )  # extend cache for 7 days
                return cached_data

            if self.file_type == "application/json":
                # https://github.com/langchain-ai/langchain/issues/7944
                # langchain suggested use JSON loader instead of S3 File loader
                with TempS3File(self.s3_bucket, self.s3_filename) as tmp_json_file:
                    loader = JSONLoader(
                        file_path=str(tmp_json_file),
                        jq_schema=".",
                        text_content=False,
                    )
                    docs = loader.load()
            elif self.file_type == "application/pdf":
                num_pages = self.pdf_page_count()
                enable_multimodal = enable_multimodal and num_pages < 50
                if enable_multimodal:
                    try:
                        loader = PDFMultiModalLoader(self.s3_bucket, self.s3_filename)
                    except Exception as e:
                        logging.error(
                            f"Failed to parse the pdf with the multimodal loader, falling back to original parser."
                        )
                        enable_multimodal = False
                if not enable_multimodal:
                    loader = S3FileLoader(
                        self.s3_bucket, self.s3_filename, encoding="utf-8"
                    )
                docs = loader.load()
            elif (
                self.file_type
                == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            ):
                with TempS3File(self.s3_bucket, self.s3_filename) as tmp_s3_file:
                    loader = UnstructuredWordDocumentLoader(str(tmp_s3_file))
                    docs = loader.load()
            elif (
                self.file_type
                == "application/vnd.openxmlformats-officedocument.presentationml.presentation"
            ):
                with TempS3File(self.s3_bucket, self.s3_filename) as tmp_s3_file:
                    loader = UnstructuredPowerPointLoader(str(tmp_s3_file))
                    docs = loader.load()
            elif TofuS3AudioVideoFileLoader.is_supported_audio_video_file(
                self.file_type
            ):
                video_loader = TofuS3AudioVideoFileLoader(
                    info={
                        "s3_bucket": self.s3_bucket,
                        "s3_filename": self.s3_filename,
                        "mime_file_type": self.file_type,
                        "total_duration": self.total_duration,
                        "file_size": self.file_size,
                    }
                )
                docs = video_loader.load(fast_return=fast_return)
            elif self.file_type == "text/plain":

                loader = S3FileLoader(
                    self.s3_bucket,
                    self.s3_filename,
                    encoding="utf-8",
                    content_type="text/plain",
                )
                docs = loader.load()

            else:
                try:
                    loader = S3FileLoader(
                        self.s3_bucket,
                        self.s3_filename,
                        encoding="utf-8",
                        content_type=self.file_type,
                    )
                    docs = loader.load()
                except Exception as e:
                    # fallback without content_type to be consistent with prev prod behavior
                    logging.exception(
                        f"Failed to load data from s3_filename: {self.s3_filename} with content_type: {self.file_type}"
                    )
                    loader = S3FileLoader(
                        self.s3_bucket, self.s3_filename, encoding="utf-8"
                    )
                    docs = loader.load()

            docs = self.remove_empty_docs(docs)
            cache.set(self.s3_filename, docs, timeout=60 * 60 * 24 * 365)
            logging.info(f"Content fetched from s3_filename: {self.s3_filename}")
            return docs
        except Exception as e:
            logging.exception(
                f"Failed to load data from s3_filename: {self.s3_filename} with exception {e}\n{traceback.format_exc()}"
            )
            raise e
