import base64
import concurrent.futures
import logging
from io import Bytes<PERSON>
from typing import List

import fitz
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage
from PIL import Image

from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..models import TofuUser
from ..prompt.prompt_library.pdf_multimodal_prompt import pdf_multimodal_prompt
from ..task_registry import GenerationGoal
from ..thread_locals import get_current_user, set_current_user
from ..utils import TempS3File


class PDFPageProcessor(BaseTracableClass):
    def __init__(self, file: str) -> None:
        self._file = file
        self._model_name = None
        self.model_config = ModelConfigResolver.resolve(
            GenerationGoal.PDF_MULTIMODAL, foundation_model=self._model_name
        )
        self.model_caller = ModelCaller(self.model_config)
        self._num_workers = 10
        self._dpi = 100
        self._prompt = pdf_multimodal_prompt

    def get_metadata(self):
        # TODO: use context manager
        user = get_current_user()
        return {
            "username": user.username if user else None,
            "file": self._file,
        }

    def convert_page_to_base64(self, page_number: int, format: str = "JPEG") -> str:
        with fitz.open(self._file) as doc:
            buffered = BytesIO()
            page = doc.load_page(page_number - 1)
            pix = page.get_pixmap(dpi=self._dpi)
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

            # Resize if either dimension exceeds 8000 pixels, which is the maximum size allowed for claude 3.5 sonnet
            if img.width > 8000 or img.height > 8000:
                ratio = 8000 / max(img.width, img.height)
                new_width = int(img.width * ratio)
                new_height = int(img.height * ratio)
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            img.save(buffered, format=format)

        return base64.b64encode(buffered.getvalue()).decode("utf-8")

    @dynamic_traceable(name="pdf_multimodal_loader_process_page")
    def _process_page(self, page_number: int, current_user: TofuUser) -> str:
        set_current_user(current_user)

        image_base64 = self.convert_page_to_base64(page_number)
        message = HumanMessage(
            content=[
                {"type": "text", "text": self._prompt},
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                },
            ]
        )
        response = self.model_caller.get_results_with_fallback([message])
        if not isinstance(response, list):
            raise ValueError(f"Invalid response from model for page {page_number}")
        if not response:
            raise ValueError(f"No response from model for page {page_number}")
        text = response[0].text
        if not text:
            raise ValueError(f"No text in response from model for page {page_number}")
        return text

    @dynamic_traceable(name="pdf_multimodal_loader")
    def parse_to_documents(self, s3_bucket, s3_filename) -> List[Document]:
        # TODO: use context manager
        current_user = get_current_user()

        with fitz.open(self._file) as doc:
            num_pages = doc.page_count
            documents = [None] * num_pages

            try:
                with concurrent.futures.ThreadPoolExecutor(
                    self._num_workers
                ) as executor:
                    futures = {
                        executor.submit(self._process_page, i + 1, current_user): i
                        for i in range(num_pages)
                    }

                    for future in concurrent.futures.as_completed(futures):
                        index = futures[future]
                        documents[index] = future.result(timeout=300)

            except Exception as e:
                logging.error(f"Error processing page {index + 1}: {str(e)}")
                raise (e)

        full_text = "\n".join(documents)
        document = Document(
            page_content=full_text,
            metadata={
                "source": f"s3://{s3_bucket}/{s3_filename}",
            },
        )
        return [document]


class PDFMultiModalLoader:
    def __init__(self, s3_bucket, s3_filename) -> None:
        self._s3_bucket = s3_bucket
        self._s3_filename = s3_filename

    def load(self) -> List[Document]:
        with TempS3File(self._s3_bucket, self._s3_filename) as tmp_pdf_file:
            processor = PDFPageProcessor(tmp_pdf_file)
            docs = processor.parse_to_documents(self._s3_bucket, self._s3_filename)

        return docs
