import mimetypes
import urllib

from .s3_video_file_loader import TofuS3AudioVideoFileLoader


def is_media_url(url: str) -> bool:
    """Check if URL points to a supported audio/video file"""
    clean_url = url.split("?")[0].lower()
    mime_type, _ = mimetypes.guess_type(clean_url)
    return mime_type in TofuS3AudioVideoFileLoader.MIME_TYPE_TO_FORMAT


def try_parse_google_url(url):
    parsed = urllib.parse.urlparse(url)
    netloc = parsed.netloc
    path = parsed.path
    path_parts = path.split("/")

    if "drive.google.com" in netloc:
        if "folders" in path_parts:
            folder_id_index = path_parts.index("folders") + 1
            if folder_id_index < len(path_parts):
                return {"type": "folder", "id": path_parts[folder_id_index]}
        elif "file" in path_parts:
            file_id_index = path_parts.index("file") + 2
            if file_id_index < len(path_parts):
                return {"type": "file", "id": path_parts[file_id_index]}
        else:
            return {"type": "other"}
    elif "docs.google.com" in netloc:
        if "document" in path_parts:
            doc_id_index = path_parts.index("document") + 2
            if doc_id_index < len(path_parts):
                return {"type": "document", "id": path_parts[doc_id_index]}
        elif "spreadsheets" in path_parts:
            sheet_id_index = path_parts.index("spreadsheets") + 2
            if sheet_id_index < len(path_parts):
                return {"type": "spreadsheet", "id": path_parts[sheet_id_index]}
        elif "presentation" in path_parts:
            slide_id_index = path_parts.index("presentation") + 2
            if slide_id_index < len(path_parts):
                return {"type": "presentation", "id": path_parts[slide_id_index]}
        else:
            return {"type": "other"}

    return None


def is_youtube_url(url):
    parsed = urllib.parse.urlparse(url)
    netloc = parsed.netloc
    if "youtube.com" in netloc or "youtu.be" in netloc:
        return True
    return False


def is_google_drive_url(url):
    google_drive_url = try_parse_google_url(url)
    if google_drive_url:
        return True
    return False
