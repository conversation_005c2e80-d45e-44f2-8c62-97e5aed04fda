import logging
import os
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import requests
from django.core.cache import cache

from ..logger import log_cloudwatch_metric
from ..shared_definitions.crustdata_definition import (
    CATEGORIES_FOR_COMPANY,
    CATEGORIES_FOR_CONTACT,
    ENRICH_FIELD_WITH_CATEGORIES,
)
from ..utils import CloudWatchMetrics, tofu_rate_limit


def _collect_available_fields(categories_data, target_categories):
    available_fields = []
    for category_data in categories_data:
        if category_data["id"] not in target_categories:
            continue
        category_fields = [
            field["code"]
            for field in category_data["fields"]
            if not field.get("disabled", False)
        ]
        available_fields.extend(category_fields)
    return set(available_fields)


def _collect_disabled_fields(categories_data, target_categories):
    disabled_fields = []
    for category_data in categories_data:
        if category_data["id"] not in target_categories:
            continue
        category_fields = [
            field["code"]
            for field in category_data["fields"]
            if field.get("disabled", False)
        ]
        disabled_fields.extend(category_fields)
    return set(disabled_fields)


# Collect available fields for companies and people
COMPANY_AVAILABLE_FIELDS = _collect_available_fields(
    ENRICH_FIELD_WITH_CATEGORIES, CATEGORIES_FOR_COMPANY
)
PEOPLE_AVAILABLE_FIELDS = _collect_available_fields(
    ENRICH_FIELD_WITH_CATEGORIES, CATEGORIES_FOR_CONTACT + CATEGORIES_FOR_COMPANY
)
COMPANY_ADDITIONAL_QUERY_FIELDS = []  # ["decision_makers"]
PEOPLE_ADDITIONAL_QUERY_FIELDS = ["business_email"]

DISABLED_COMPANY_FIELDS = _collect_disabled_fields(
    ENRICH_FIELD_WITH_CATEGORIES, CATEGORIES_FOR_COMPANY
)
DISABLED_PEOPLE_FIELDS = _collect_disabled_fields(
    ENRICH_FIELD_WITH_CATEGORIES, CATEGORIES_FOR_CONTACT + CATEGORIES_FOR_COMPANY
)


class CrustdataConfig:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(CrustdataConfig, cls).__new__(cls)
            cls._instance._field_to_label_map = (
                cls._instance._compile_field_to_label_map()
            )
        return cls._instance

    def _compile_field_to_label_map(self):
        field_to_label_map = {}
        for category in ENRICH_FIELD_WITH_CATEGORIES:
            for field in category["fields"]:
                field_to_label_map[field["code"]] = field["label"]
        return field_to_label_map

    def get_field_label(self, field_code):
        return self._field_to_label_map.get(field_code, None)


class CrustdataClient:
    """
    Client for the Crustdata API for enrichment.
    Based on the Crustdata API documentation: https://crustdata.notion.site/Crustdata-Discovery-And-Enrichment-API-c66d5236e8ea40df8af114f6d447ab48#10ee4a7d95b1807bb965dad5a67086e3
    """

    BASE_URL = "https://api.crustdata.com"

    def __init__(self):
        api_token = os.getenv("CRUSTDATA_API_KEY")
        if not api_token:
            raise ValueError("CRUSTDATA_API_KEY is not set")
        self.api_token = api_token
        self.headers = {
            "Accept": "application/json",
            "Authorization": f"Token {api_token}",
        }

    def _log_crustdata_usage(self, usage_type: str, query_params: Dict[str, str]):
        log_cloudwatch_metric(f"crustdata_{usage_type}", 1, **query_params)

    def _call_api(self, url: str, params: Optional[Dict[str, str]] = None):
        """
        Make a GET request to the Crustdata API and return the JSON response.
        On error, logs the issue and returns None.

        Args:
            url: The API endpoint URL
            params: Optional query parameters

        Returns:
            The JSON response from the API or None if the request failed
        """
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            # Handle HTTP errors with response details
            error_detail = f"url: {url}, params: {params}"
            error_detail += f" | Status code: {e.response.status_code}"
            try:
                error_json = e.response.json()
                error_detail += f" | Response: {error_json}"
            except ValueError:
                error_detail += f" | Response text: {e.response.text}"

            # For 404 errors, log to CloudWatch instead of Sentry
            if e.response.status_code == 404:
                logging.info(f"API returned 404 (no data found): {error_detail}")
                # Log 404 metric to CloudWatch
                try:
                    CloudWatchMetrics.put_metric(
                        metric_name="crustdata_404_error",
                        value=1,
                        dimensions=[
                            {"Name": "url", "Value": url},
                            {
                                "Name": "status_code",
                                "Value": str(e.response.status_code),
                            },
                        ],
                    )
                except Exception as cloudwatch_error:
                    logging.error(
                        f"Failed to log 404 metric to CloudWatch: {cloudwatch_error}"
                    )
            else:
                # For non-404 errors, continue using logging.exception to trigger Sentry alerts
                logging.exception(f"Failed to call API: {error_detail}")
            return None
        except (requests.RequestException, ValueError) as e:
            # Handle all other request and JSON parsing errors
            error_detail = f"url: {url}, params: {params} | Error: {str(e)}"
            logging.exception(f"Failed to call API: {error_detail}")
            return None

    def _call_post_api(self, url: str, params: Optional[Dict[str, str]] = None):
        try:
            logging.info(f"Making API request to: {url}")
            logging.info(f"Request payload: {params}")

            response = requests.post(url, headers=self.headers, json=params)

            if response.status_code >= 400:
                error_message = f"API error - Status code: {response.status_code}"
                error_message += f" | URL: {url}"
                error_message += f" | Request payload: {params}"
                try:
                    error_body = response.json()
                    error_message += f" | Response body: {error_body}"
                except:
                    error_message += f" | Raw response: {response.text[:1000]}"

                # For 404 errors, log to CloudWatch instead of Sentry
                if response.status_code == 404:
                    logging.info(f"API returned 404 (no data found): {error_message}")
                    # Log 404 metric to CloudWatch
                    try:
                        CloudWatchMetrics.put_metric(
                            metric_name="crustdata_404_error",
                            value=1,
                            dimensions=[
                                {"Name": "url", "Value": url},
                                {
                                    "Name": "status_code",
                                    "Value": str(response.status_code),
                                },
                            ],
                        )
                    except Exception as cloudwatch_error:
                        logging.error(
                            f"Failed to log 404 metric to CloudWatch: {cloudwatch_error}"
                        )
                else:
                    # For non-404 errors, continue using logging.error
                    logging.error(f"Error when calling crustdata API: {error_message}")
                return None

            response.raise_for_status()
            return response.json()
        except Exception as e:
            logging.exception(f"Unexpected error when calling API: {e}")
            return None

    @tofu_rate_limit(calls=50, period=60, sleep_time=10, max_retries=10)
    def _enrich_company_impl(
        self,
        company_query_key: Optional[str] = None,
        company_query_value: Optional[str] = None,
        enrich_realtime: bool = True,
        fields: Optional[List[str]] = None,
    ):
        if not company_query_key or not company_query_value:
            logging.error(
                "At least one of company_query_key and company_query_value must be provided"
            )
            return {}

        # Build query parameters
        params = {}
        if company_query_key:
            params[company_query_key] = company_query_value
        if fields:
            copied_fields = fields[:]
            if "company_linkedin_profile_url" in copied_fields:
                copied_fields.remove("company_linkedin_profile_url")
                copied_fields.append("linkedin_profile_url")
            params["fields"] = ",".join(copied_fields)
        if enrich_realtime:
            params["enrich_realtime"] = "true"

        # Make API request
        url = urljoin(self.BASE_URL, "/screener/company")
        result = self._call_api(url, params)

        if result is None:
            return {}

        try:
            self._log_crustdata_usage("company_enrich", params)
        except Exception as e:
            logging.exception(f"Failed to log crustdata usage due to {e}")

        if isinstance(result, list) and len(result) > 0:
            result = result[0]
        if not isinstance(result, dict):
            logging.error(f"Unexpected company data format: {result}")
            return {}
        if "linkedin_profile_url" in result:
            result["company_linkedin_profile_url"] = result["linkedin_profile_url"]
            result.pop("linkedin_profile_url", None)
        return result

    def enrich_company(
        self,
        company_domain: Optional[str] = None,
        company_name: Optional[str] = None,
        company_linkedin_url: Optional[str] = None,
        enrich_realtime: bool = True,  # we always want realtime enrichment
    ) -> dict:
        """
        Fetch company data from the Crustdata API.

        Enriches and retrieves detailed information about companies using their domain,
        LinkedIn URL, or company name.

        Precedence when multiple identifiers are provided:
            1. company_domain
            2. company_linkedin_url
            3. company_name

        Args:
            company_domain (Optional[str]): Company domains
            company_name (Optional[str]): Company names
            company_linkedin_url (Optional[str]): LinkedIn company URLs
            enrich_realtime (bool, default=True): Whether to enrich data in real-time

        Returns:
            dict: Enriched company data from the API or empty dict if unsuccessful
        """
        query_key = None
        query_value = None
        if company_domain:
            query_key = "company_domain"
            query_value = company_domain
        elif company_linkedin_url:
            query_key = "company_linkedin_url"
            query_value = company_linkedin_url
        elif company_name:
            query_key = "company_name"
            query_value = company_name
        else:
            logging.error(
                "At least one of company_domain, company_name, or company_linkedin_url must be provided"
            )
            return {}

        # TODO: make this storage more permanent
        cache_key = f"crustdata_company:{query_key}:{query_value}"
        try:
            cached_data = cache.get(cache_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get crustdata company cache for {cache_key}: {e}"
            )
            cached_data = None
        if cached_data:
            return cached_data

        try:
            result = (
                self._enrich_company_impl(
                    company_query_key=query_key,
                    company_query_value=query_value,
                    enrich_realtime=enrich_realtime,
                )
                or {}
            )
        except Exception as e:
            logging.exception(f"Failed to call crustdata company API: {e}")
            return {}

        if COMPANY_ADDITIONAL_QUERY_FIELDS:
            try:
                additional_queries = (
                    self._enrich_company_impl(
                        company_query_key=query_key,
                        company_query_value=query_value,
                        enrich_realtime=enrich_realtime,
                        fields=COMPANY_ADDITIONAL_QUERY_FIELDS,
                    )
                    or {}
                )
                if additional_queries:
                    result.update(additional_queries)
            except Exception as e:
                logging.exception(
                    f"Failed to call crustdata company API for additional queries: {e}"
                )
                # just ignore the error and continue

        if result:
            company_domain = result.get("company_domain")
            if company_domain:
                cache_key = f"crustdata_company:company_domain:{company_domain}"
                cache.set(
                    cache_key, result, timeout=60 * 60 * 24 * 30
                )  # cache for 30 days
            company_name = result.get("company_name")
            if company_name:
                cache_key = f"crustdata_company:company_name:{company_name}"
                cache.set(
                    cache_key, result, timeout=60 * 60 * 24 * 30
                )  # cache for 30 days
            company_linkedin_url = result.get("company_linkedin_url")
            if company_linkedin_url:
                cache_key = (
                    f"crustdata_company:company_linkedin_url:{company_linkedin_url}"
                )
                cache.set(
                    cache_key, result, timeout=60 * 60 * 24 * 30
                )  # cache for 30 days

        return result

    @tofu_rate_limit(calls=50, period=60, sleep_time=10, max_retries=10)
    def _enrich_people_impl_batch(
        self,
        query_key: str,
        query_values: List[str],
        enrich_realtime: bool = True,
        fields: Optional[List[str]] = None,
    ):
        if not query_key or not query_values:
            logging.error("At least one of query_key and query_values must be provided")
            return None

        # Build query parameters
        params = {
            query_key: ",".join(query_values),
        }
        if fields:
            params["fields"] = ",".join(fields)
        if enrich_realtime:
            params["enrich_realtime"] = "true"

        # Make API request
        url = urljoin(self.BASE_URL, "/screener/person/enrich")
        result = self._call_api(url, params)

        try:
            self._log_crustdata_usage("people_enrich", params)
        except Exception as e:
            logging.exception(f"Failed to log crustdata usage due to {e}")

        if not result:
            return None
        if not isinstance(result, list):
            logging.error(f"Unexpected person data format: {result}")
            return None

        return result

    @tofu_rate_limit(calls=50, period=60, sleep_time=10, max_retries=10)
    def _enrich_people_impl(
        self,
        query_key: str,
        query_value: str,
        enrich_realtime: bool = True,
        fields: Optional[List[str]] = None,
    ):
        if not query_key or not query_value:
            logging.error("At least one of query_key and query_value must be provided")
            return None

        # Build query parameters
        params = {
            query_key: query_value,
        }
        if fields:
            params["fields"] = ",".join(fields)
        if enrich_realtime:
            params["enrich_realtime"] = "true"

        # Make API request
        url = urljoin(self.BASE_URL, "/screener/person/enrich")
        result = self._call_api(url, params)

        if result is None:
            return None

        try:
            self._log_crustdata_usage("people_enrich", params)
        except Exception as e:
            logging.exception(f"Failed to log crustdata usage due to {e}")

        if not result:
            return None

        if not isinstance(result, list) or not isinstance(result[0], dict):
            logging.error(f"Unexpected person data format: {result}")
            return None

        if "error" in result[0]:
            error = result[0]["error"]
            if "No data found" not in error:
                logging.error(f"Failed to fetch person data: {error}")
                return None
            else:
                return None

        return result[0]

    def enrich_people(
        self,
        linkedin_profile_url: Optional[str] = None,
        business_email: Optional[str] = None,
        enrich_realtime: bool = True,  # we always want realtime enrichment
    ) -> dict:
        """
        Fetch and enrich person data from the Crustdata API.

        Retrieves detailed information about individuals using their LinkedIn profile URLs
        or business email addresses.

        Args:
            linkedin_profile_url (Optional[str]): LinkedIn profile URL
            business_email (Optional[str]): Business email address
            enrich_realtime (bool, default=False): Whether to perform real-time web search
                if data is not found in database (costs additional credits)

        Returns:
            dict: Enriched person data from the API or None if unsuccessful
        """
        query_key = None
        query_value = None
        if linkedin_profile_url:
            query_key = "linkedin_profile_url"
            query_value = linkedin_profile_url
        elif business_email:
            query_key = "business_email"
            query_value = business_email
        else:
            logging.error(
                "At least one of linkedin_profile_url or business_email must be provided"
            )
            return {}

        cache_key = f"crustdata_people:{query_key}:{query_value}"
        try:
            cached_data = cache.get(cache_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get crustdata people cache for {cache_key}: {e}"
            )
            cached_data = None
        if cached_data:
            return cached_data

        try:
            result = (
                self._enrich_people_impl(
                    query_key=query_key,
                    query_value=query_value,
                    enrich_realtime=enrich_realtime,
                )
                or {}
            )
        except Exception as e:
            logging.exception(f"Failed to call crustdata people API: {e}")
            return {}

        if not business_email:
            try:
                additional_queries = (
                    self._enrich_people_impl(
                        query_key=query_key,
                        query_value=query_value,
                        enrich_realtime=enrich_realtime,
                        fields=PEOPLE_ADDITIONAL_QUERY_FIELDS,
                    )
                    or {}
                )
                if additional_queries:
                    result.update(additional_queries)
            except Exception as e:
                logging.exception(
                    f"Failed to call crustdata people API for additional queries: {e}"
                )
                # just ignore the error and continue
        else:
            if "business_email" not in result:
                result["business_email"] = business_email

        if result:
            linkedin_profile_url_from_result = result.get("linkedin_profile_url")
            business_email_from_result = result.get("business_email")
            if linkedin_profile_url_from_result:
                cache_key = f"crustdata_people:linkedin_profile_url:{linkedin_profile_url_from_result}"
                cache.set(
                    cache_key, result, timeout=60 * 60 * 24 * 30
                )  # cache for 30 days
            if business_email_from_result:
                cache_key = (
                    f"crustdata_people:business_email:{business_email_from_result}"
                )
                cache.set(
                    cache_key, result, timeout=60 * 60 * 24 * 30
                )  # cache for 30 days

        return result

    @tofu_rate_limit(calls=15, period=60, sleep_time=10, max_retries=10)
    def _find_contacts_for_company_and_titles_impl(
        self, companies: List[str], titles: List[str], page: int
    ) -> Optional[Dict[str, Any]]:
        url = urljoin(self.BASE_URL, "/screener/person/search")
        params = {
            "filters": [
                {"filter_type": "CURRENT_TITLE", "type": "in", "value": titles},
                {"filter_type": "CURRENT_COMPANY", "type": "in", "value": companies},
            ],
            "page": page,
        }
        result = self._call_post_api(url, params)
        return result

    def find_contacts_for_company_and_titles(
        self, companies: List[Any], titles: List[str]
    ) -> List[Dict[str, Any]]:
        RECORDS_PER_PAGE = 25
        MAX_PAGES = 40

        first_result = self._find_contacts_for_company_and_titles_impl(
            companies, titles, 1
        )
        if not first_result:
            return []

        profiles = first_result.get("profiles", [])

        # Use total_display_count instead of total_count
        total_display_count = int(first_result.get("total_display_count", "0"))
        if total_display_count == 0:
            return profiles

        # Assume 25 records per page instead of 100
        total_pages = (total_display_count + RECORDS_PER_PAGE - 1) // RECORDS_PER_PAGE

        # Cap at maximum of 40 pages
        total_pages = min(total_pages, MAX_PAGES)

        # Start from page 2 since we already have page 1
        for page in range(2, total_pages + 1):
            page_result = self._find_contacts_for_company_and_titles_impl(
                companies, titles, page
            )
            if page_result and "profiles" in page_result:
                profiles.extend(page_result["profiles"])

        return profiles
