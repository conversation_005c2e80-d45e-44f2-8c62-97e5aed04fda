import logging

from django.core.cache import cache
from langsmith import Client as LangsmithClient


def fetch_langsmith_links(request_id):

    # check if we have a run link cached for this request_id.
    cached_run_link = cache.get(f"langsmith_run_links_{request_id}")
    if cached_run_link:
        run_links = cached_run_link

    else:
        # Fetch the langsmith link for the given request
        client = LangsmithClient()
        runs = client.list_runs(
            project_name="tofu",
            filter=f"and(eq(metadata_key, 'request_id'), eq(metadata_value, '{request_id}'), eq(name, 'content_gen'))",
        )
        if not runs:
            logging.error(f"Could not find run for request_id {request_id}")
        run_ids = [run.id for run in runs]
        run_links = [
            f"https://smith.langchain.com/o/44ba7596-5fd3-5a13-9c7f-68a1dd91b341/projects/p/d6ccf463-ce35-4590-904d-9683895ba5bf?peek={run_id}"
            for run_id in run_ids
        ]
        # cache the run link
        cache.set(
            f"langsmith_run_links_{request_id}", run_links, timeout=60 * 60 * 24 * 7
        )

    if run_links:
        if len(run_links) > 1:
            run_link = "\n".join(run_links)
        else:
            run_link = run_links[0]

        return run_link
    else:
        logging.error(f"Could not find langsmith run link for request_id {request_id}")
        return None
