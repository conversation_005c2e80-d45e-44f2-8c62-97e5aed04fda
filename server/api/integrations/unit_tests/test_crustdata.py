import os
import unittest
import uuid
from unittest.mock import MagicMock, patch

import pytest
import requests
from django.core.cache import cache
from django.test import TestCase
from django.test.utils import override_settings

from ...models import TargetInfo, TargetInfoGroup
from ..crustdata_adapter import CrustdataAdapter
from ..crustdata_client import Crustdata<PERSON>lient


@override_settings(
    CACHES={
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": f"unique-{uuid.uuid4()}",
        }
    }
)
class TestCrustdataClient(TestCase):
    def setUp(self):
        # Use patch.dict as a context manager only for the initialization
        with patch.dict(os.environ, {"CRUSTDATA_API_KEY": "test_api_key"}):
            self.integration = CrustdataClient()
        cache.clear()

    def tearDown(self):
        cache.clear()

    def test_init_without_api_key(self):
        # Test the validation logic without modifying actual env vars
        with patch.object(os.environ, "get", return_value=None):
            with self.assertRaises(ValueError) as context:
                CrustdataClient()

        self.assertEqual(str(context.exception), "CRUSTDATA_API_KEY is not set")

    @patch("requests.get")
    def test_get_company_success(self, mock_get):
        # Mock successful API response with realistic data structure
        mock_response = MagicMock()
        mock_response.json.return_value = [
            {
                "company_name": "Test Company",
                "company_website_domain": "example.com",
                "headcount": {"linkedin_headcount": 500},
            }
        ]
        mock_get.return_value = mock_response

        result = self.integration.enrich_company(
            company_domain="example.com",
            company_name="Test Company",
            company_linkedin_url="https://linkedin.com/company/test",
        )

        # Verify the result matches expected structure
        self.assertEqual(result["company_name"], "Test Company")
        self.assertEqual(result["headcount"]["linkedin_headcount"], 500)

        # Verify API calls - we expect 1 calls because of SEPARATE_QUERY_FIELDS
        self.assertEqual(mock_get.call_count, 1)
        call_args = mock_get.call_args_list
        self.assertEqual(
            call_args[0][1]["headers"]["Authorization"], "Token test_api_key"
        )
        self.assertEqual(call_args[0][1]["params"]["company_domain"], "example.com")
        self.assertEqual(call_args[0][1]["params"]["enrich_realtime"], "true")

    @patch("requests.get")
    def test_get_company_cached_response(self, mock_get):
        # Set up cache
        cache_data = {"company": "cached_data"}
        cache_key = "crustdata_company:company_domain:example.com"
        cache.set(cache_key, cache_data)

        result = self.integration.enrich_company(
            company_domain="example.com", company_name="Test Company"
        )

        # Verify cached result is returned
        self.assertEqual(result, cache_data)
        # Verify no API call was made
        mock_get.assert_not_called()

    @patch("requests.get")
    def test_get_people_success(self, mock_get):
        # Mock successful API response with realistic data structure
        mock_response = MagicMock()
        mock_response.json.return_value = [
            {
                "name": "John Doe",
                "linkedin_profile_url": "https://linkedin.com/in/test",
                "email": "<EMAIL>",
                "title": "Software Engineer",
            }
        ]
        mock_get.return_value = mock_response

        result = self.integration.enrich_people(
            linkedin_profile_url="https://linkedin.com/in/test"
        )

        # Verify the result matches expected structure
        self.assertEqual(result["name"], "John Doe")
        self.assertEqual(result["title"], "Software Engineer")

        # Verify API call
        self.assertEqual(mock_get.call_count, 2)
        first_call_args = mock_get.call_args_list[0]
        self.assertEqual(
            first_call_args[1]["params"],
            {
                "linkedin_profile_url": "https://linkedin.com/in/test",
                "enrich_realtime": "true",
            },
        )
        second_call_args = mock_get.call_args_list[1]
        self.assertEqual(
            second_call_args[1]["params"],
            {
                "linkedin_profile_url": "https://linkedin.com/in/test",
                "enrich_realtime": "true",
                "fields": "business_email",
            },
        )

    @patch("requests.get")
    def test_get_people_no_params(self, mock_get):
        # Test calling enrich_people without required parameters
        # Instead of expecting an exception, expect {}
        result = self.integration.enrich_people()
        self.assertEqual(result, {})
        mock_get.assert_not_called()

    @patch("requests.get")
    def test_get_people_no_data_found(self, mock_get):
        # Mock API response for no data found
        mock_response = MagicMock()
        mock_response.json.return_value = [{"error": "No data found"}]
        mock_get.return_value = mock_response

        result = self.integration.enrich_people(
            linkedin_profile_url="https://linkedin.com/in/nonexistent"
        )

        self.assertEqual(result, {})

    @patch("requests.get")
    def test_get_people_api_error(self, mock_get):
        # Mock API error response
        mock_response = MagicMock()
        mock_response.json.return_value = [{"error": "API rate limit exceeded"}]
        mock_get.return_value = mock_response

        # Instead of expecting an exception, expect None
        result = self.integration.enrich_people(
            linkedin_profile_url="https://linkedin.com/in/test"
        )
        self.assertEqual(result, {})

    @patch("requests.get")
    def test_api_error_handling(self, mock_get):
        # Mock API error response
        mock_response = MagicMock()
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError(
            response=mock_response
        )
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_get.return_value = mock_response

        # Instead of expecting an exception, expect None
        result = self.integration._call_api("https://api.crustdata.com/test")
        self.assertIsNone(result)

    @patch("requests.get")
    def test_get_company_empty_response(self, mock_get):
        # Add test for empty response
        mock_response = MagicMock()
        mock_response.json.return_value = []
        mock_get.return_value = mock_response

        result = self.integration.enrich_company(company_domain="example.com")
        self.assertEqual(result, {})

    def test_fuzzy_match_contact_fields_ignores_personal_email(self):
        # Create mock TargetInfo with only personal email
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.docs = {
            "personal_email": {
                "value": "<EMAIL>",
                "meta": {"field_name": "personal_email"},
            },
            "some_field": {
                "value": "other value",
                "meta": {"field_name": "some_field"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        linkedin_url, business_email = integration._fuzzy_match_contact_fields()

        # Should ignore personal email (gmail)
        self.assertIsNone(business_email)
        self.assertIsNone(linkedin_url)

    def test_fuzzy_match_contact_fields_finds_work_email(self):
        # Create mock TargetInfo with work email
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.docs = {
            "work_email": {
                "value": "<EMAIL>",
                "meta": {"field_name": "work_email"},
            },
            "some_field": {
                "value": "other value",
                "meta": {"field_name": "some_field"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        linkedin_url, business_email = integration._fuzzy_match_contact_fields()

        # Should find the work email
        self.assertEqual(business_email, "<EMAIL>")
        self.assertIsNone(linkedin_url)

    def test_fuzzy_match_contact_fields_finds_linkedin_url(self):
        # Create mock TargetInfo with linkedin_url
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.docs = {
            "linkedin_profile_url": {
                "value": "https://linkedin.com/in/john",
                "meta": {"field_name": "linkedin_profile_url"},
            },
            "some_field": {
                "value": "other value",
                "meta": {"field_name": "some_field"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        linkedin_url, business_email = integration._fuzzy_match_contact_fields()

        # Should find the linkedin_url
        self.assertEqual(linkedin_url, "https://linkedin.com/in/john")
        self.assertIsNone(business_email)

    def test_flatten_data(self):
        """Test that the flatten_data method correctly flattens nested dictionaries and filters disabled fields"""
        # Create mock TargetInfo for adapter initialization
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "company"}

        integration = CrustdataAdapter(mock_target_info)

        # Test with company data that includes disabled fields from crustdata_fields.json
        company_data = {
            "company_name": "Acme Corp",
            "company_website_domain": "acme.com",
            "headcount": {
                "linkedin_headcount": 500,
                "linkedin_headcount_timeseries": [
                    {"date": "2022-01", "count": 450},
                    {"date": "2022-02", "count": 500},
                ],
                "linkedin_headcount_by_function_timeseries": {
                    "engineering": [{"date": "2022-01", "count": 200}]
                },
                "linkedin_headcount_by_role_absolute": {
                    "engineering": 150,
                    "sales": 75,
                },
            },
            "job_openings": {
                "job_openings_count": 25,
                "open_jobs_timeseries": [
                    {"date": "2022-01", "count": 20},
                    {"date": "2022-02", "count": 25},
                ],
            },
            "linkedin_followers": {
                "linkedin_followers": 10000,
                "linkedin_follower_count_timeseries": [
                    {"date": "2022-01", "count": 9500}
                ],
            },
            "funding_and_investment": {
                "crunchbase_total_investment_usd": 50000000,
                "funding_milestones_timeseries": [
                    {"date": "2021-05", "round": "Series B", "amount": 30000000}
                ],
            },
            "web_traffic": {
                "monthly_visitors": 500000,
                "monthly_visitors_timeseries": [
                    {"date": "2022-01", "visitors": 450000}
                ],
                "traffic_source_direct_pct_timeseries": [
                    {"date": "2022-01", "pct": 25}
                ],
                "traffic_source_search_pct_timeseries": [
                    {"date": "2022-01", "pct": 40}
                ],
                "traffic_source_social_pct_timeseries": [
                    {"date": "2022-01", "pct": 20}
                ],
                "traffic_source_referral_pct_timeseries": [
                    {"date": "2022-01", "pct": 10}
                ],
                "traffic_source_paid_referral_pct_timeseries": [
                    {"date": "2022-01", "pct": 5}
                ],
            },
        }

        flattened_company = integration.flatten_data(company_data)

        # Direct checks for regular expected values
        self.assertEqual(flattened_company["company_name"], "Acme Corp")
        self.assertEqual(flattened_company["company_website_domain"], "acme.com")
        self.assertEqual(flattened_company["headcount.linkedin_headcount"], 500)
        self.assertEqual(flattened_company["job_openings.job_openings_count"], 25)
        self.assertEqual(
            flattened_company["linkedin_followers.linkedin_followers"], 10000
        )
        self.assertEqual(
            flattened_company["funding_and_investment.crunchbase_total_investment_usd"],
            50000000,
        )
        self.assertEqual(flattened_company["web_traffic.monthly_visitors"], 500000)

        # Verify disabled fields are excluded
        self.assertNotIn("headcount.linkedin_headcount_timeseries", flattened_company)
        self.assertNotIn(
            "headcount.linkedin_headcount_by_function_timeseries", flattened_company
        )
        self.assertNotIn("job_openings.open_jobs_timeseries", flattened_company)
        self.assertNotIn(
            "linkedin_followers.linkedin_follower_count_timeseries", flattened_company
        )
        self.assertNotIn(
            "funding_and_investment.funding_milestones_timeseries", flattened_company
        )
        self.assertNotIn("web_traffic.monthly_visitors_timeseries", flattened_company)
        self.assertNotIn(
            "web_traffic.traffic_source_direct_pct_timeseries", flattened_company
        )
        self.assertNotIn(
            "web_traffic.traffic_source_search_pct_timeseries", flattened_company
        )
        self.assertNotIn(
            "web_traffic.traffic_source_social_pct_timeseries", flattened_company
        )
        self.assertNotIn(
            "web_traffic.traffic_source_referral_pct_timeseries", flattened_company
        )
        self.assertNotIn(
            "web_traffic.traffic_source_paid_referral_pct_timeseries", flattened_company
        )

        # Test with people data
        people_data = {
            "name": "John Doe",
            "title": "CTO",
            "email": "<EMAIL>",
            "location": {
                "city": "San Francisco",
                "state": "CA",
                "country": "United States",
            },
            "company": {"name": "Acme Corp", "domain": "acme.com"},
        }

        flattened_people = integration.flatten_data(people_data)

        # Direct checks for expected values
        self.assertEqual(flattened_people["name"], "John Doe")
        self.assertEqual(flattened_people["title"], "CTO")
        self.assertEqual(flattened_people["email"], "<EMAIL>")
        self.assertEqual(
            flattened_people["location"],
            {"city": "San Francisco", "state": "CA", "country": "United States"},
        )
        self.assertEqual(flattened_people["company.name"], "Acme Corp")
        self.assertEqual(flattened_people["company.domain"], "acme.com")

        # Additional edge cases
        # Empty dictionary
        self.assertEqual(integration.flatten_data({}), {})

        # Direct parent key test
        parent_result = integration.flatten_data({"x": 1, "y": 2}, parent_key="parent")
        self.assertEqual(parent_result, {"parent.x": 1, "parent.y": 2})

    def test_fuzzy_match_company_fields_finds_domain(self):
        # Create mock TargetInfo with domain
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "company"}
        mock_target_info.target_key = "Acme Corp"
        mock_target_info.docs = {
            "domain": {
                "value": "acme.com",
                "meta": {"field_name": "domain"},
            },
            "some_field": {
                "value": "other value",
                "meta": {"field_name": "some_field"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should find the domain
        self.assertEqual(domain, "acme.com")
        self.assertIsNone(linkedin_url)
        self.assertIsNone(company_name)

    def test_fuzzy_match_company_fields_finds_website(self):
        # Create mock TargetInfo with website instead of domain
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "company"}
        mock_target_info.target_key = "Acme Corp"
        mock_target_info.docs = {
            "website": {
                "value": "https://www.acme.com/products",
                "meta": {"field_name": "website"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should extract domain from website URL
        self.assertEqual(domain, "acme.com")
        self.assertIsNone(linkedin_url)
        self.assertIsNone(company_name)

    def test_fuzzy_match_company_fields_finds_business_email_for_contact(self):
        # Test that for contacts, it extracts domain from business email
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.target_key = "John Doe"
        mock_target_info.docs = {
            "email": {
                "value": "<EMAIL>",
                "meta": {"field_name": "email"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should extract domain from business email for contacts
        self.assertEqual(domain, "acme.com")
        self.assertIsNone(linkedin_url)
        self.assertIsNone(company_name)

    def test_fuzzy_match_company_fields_ignores_personal_email(self):
        # Test that it ignores personal emails for domain extraction
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.target_key = "John Doe"
        mock_target_info.docs = {
            "email": {
                "value": "<EMAIL>",
                "meta": {"field_name": "email"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should not extract domain from personal email
        self.assertIsNone(domain)
        self.assertIsNone(linkedin_url)
        self.assertEqual(company_name, "John Doe")  # Falls back to target_key

    def test_fuzzy_match_company_fields_finds_linkedin_url(self):
        # Create mock TargetInfo with LinkedIn company URL
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "company"}
        mock_target_info.target_key = "Acme Corp"
        mock_target_info.docs = {
            "linkedin": {
                "value": "https://linkedin.com/company/acme-corp",
                "meta": {"field_name": "linkedin"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should find the LinkedIn URL
        self.assertIsNone(domain)
        self.assertEqual(linkedin_url, "https://linkedin.com/company/acme-corp")
        self.assertIsNone(company_name)

    def test_fuzzy_match_company_fields_ignores_profile_linkedin_url(self):
        # Test that it ignores LinkedIn profile URLs for contacts
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.target_key = "John Doe"
        mock_target_info.docs = {
            "linkedin": {
                "value": "https://linkedin.com/in/john-doe",
                "meta": {"field_name": "linkedin"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should not extract LinkedIn URL for profile links
        self.assertIsNone(domain)
        self.assertIsNone(linkedin_url)
        self.assertEqual(company_name, "John Doe")  # Falls back to target_key

    def test_fuzzy_match_company_fields_finds_company_name(self):
        # Create mock TargetInfo with company name
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "company"}
        mock_target_info.target_key = "Target Key"
        mock_target_info.docs = {
            "company_name": {
                "value": "Acme Corporation",
                "meta": {"field_name": "company_name"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should find the company name
        self.assertIsNone(domain)
        self.assertIsNone(linkedin_url)
        self.assertEqual(company_name, "Acme Corporation")

    def test_fuzzy_match_company_fields_finds_company_name_for_contact(self):
        # Test company name extraction for contacts
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.target_key = "John Doe"
        mock_target_info.docs = {
            "company_name": {
                "value": "Acme Corporation",
                "meta": {"field_name": "company_name"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should find the company name for contacts
        self.assertIsNone(domain)
        self.assertIsNone(linkedin_url)
        self.assertEqual(company_name, "Acme Corporation")

    def test_fuzzy_match_company_fields_fallback_to_target_key(self):
        # Test fallback to target key when no matches found
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "company"}
        mock_target_info.target_key = "Acme Corp"
        mock_target_info.docs = {
            "irrelevant_field": {
                "value": "irrelevant value",
                "meta": {"field_name": "irrelevant_field"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should fall back to target_key
        self.assertIsNone(domain)
        self.assertIsNone(linkedin_url)
        self.assertEqual(company_name, "Acme Corp")

    def test_fuzzy_match_company_fields_multiple_matches(self):
        # Test with multiple potential matches
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "company"}
        mock_target_info.target_key = "Target Key"
        mock_target_info.docs = {
            "domain": {
                "value": "acme.com",
                "meta": {"field_name": "domain"},
            },
            "linkedin": {
                "value": "https://linkedin.com/company/acme-corp",
                "meta": {"field_name": "linkedin"},
            },
            "company_name": {
                "value": "Acme Corporation",
                "meta": {"field_name": "company_name"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        domain, linkedin_url, company_name = integration._fuzzy_match_company_fields()

        # Should find all matches
        self.assertEqual(domain, "acme.com")
        self.assertEqual(linkedin_url, "https://linkedin.com/company/acme-corp")
        self.assertEqual(company_name, "Acme Corporation")

    # Let's add more comprehensive tests for contact fields too
    def test_fuzzy_match_contact_fields_multiple_matches(self):
        # Test with both LinkedIn profile and business email
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.docs = {
            "linkedin": {
                "value": "https://linkedin.com/in/johndoe",
                "meta": {"field_name": "linkedin"},
            },
            "email": {
                "value": "<EMAIL>",
                "meta": {"field_name": "email"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        linkedin_url, business_email = integration._fuzzy_match_contact_fields()

        # Should find both
        self.assertEqual(linkedin_url, "https://linkedin.com/in/johndoe")
        self.assertEqual(business_email, "<EMAIL>")

    def test_fuzzy_match_contact_fields_handles_non_string_values(self):
        # Test with non-string values
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.docs = {
            "linkedin": {
                "value": 12345,  # Non-string value
                "meta": {"field_name": "linkedin"},
            },
            "email": {
                "value": None,  # None value
                "meta": {"field_name": "email"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        linkedin_url, business_email = integration._fuzzy_match_contact_fields()

        # Should handle non-string values gracefully
        self.assertIsNone(linkedin_url)
        self.assertIsNone(business_email)

    def test_fuzzy_match_contact_fields_no_matches(self):
        # Test with no useful fields
        mock_target_info = MagicMock()
        mock_target_info.target_info_group.meta = {"type": "contact"}
        mock_target_info.docs = {
            "name": {
                "value": "John Doe",
                "meta": {"field_name": "name"},
            },
            "title": {
                "value": "Software Engineer",
                "meta": {"field_name": "title"},
            },
        }

        integration = CrustdataAdapter(mock_target_info)
        linkedin_url, business_email = integration._fuzzy_match_contact_fields()

        # Should return None for both
        self.assertIsNone(linkedin_url)
        self.assertIsNone(business_email)

    @patch("requests.get")
    @patch("api.integrations.crustdata_client.CloudWatchMetrics.put_metric")
    @patch("api.integrations.crustdata_client.logging")
    def test_404_error_handling_call_api(self, mock_logging, mock_cloudwatch, mock_get):
        """Test that 404 errors are logged to CloudWatch instead of triggering Sentry alerts"""
        # Mock 404 HTTP error response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.json.return_value = [
            {
                "error": "No data found for the LinkedIn profile: https://www.linkedin.com/in/test",
                "linkedin_profile_url": "https://www.linkedin.com/in/test",
                "last_tried_linkedin_enrichment_date": "2025-01-01T00:00:00.000000Z",
                "did_last_linkedin_enrichment_succeed": False,
            }
        ]
        mock_response.text = "Not Found"

        http_error = requests.exceptions.HTTPError(response=mock_response)
        http_error.response = mock_response
        mock_response.raise_for_status.side_effect = http_error
        mock_get.return_value = mock_response

        # Call the API method
        result = self.integration._call_api(
            "https://api.crustdata.com/screener/person/enrich",
            {"linkedin_profile_url": "https://www.linkedin.com/in/test"},
        )

        # Verify the result is None (expected for errors)
        self.assertIsNone(result)

        # Verify that logging.info was called and logging.exception was not called
        self.assertTrue(mock_logging.info.called)
        mock_logging.exception.assert_not_called()

        # Check that one of the info calls was for the 404 error
        info_calls = [str(call) for call in mock_logging.info.call_args_list]
        has_404_log = any(
            "API returned 404 (no data found)" in call for call in info_calls
        )
        self.assertTrue(
            has_404_log, "Expected 404 error log message not found in info calls"
        )

        # Verify CloudWatch metric was sent
        mock_cloudwatch.assert_called_once_with(
            metric_name="crustdata_404_error",
            value=1,
            dimensions=[
                {
                    "Name": "url",
                    "Value": "https://api.crustdata.com/screener/person/enrich",
                },
                {"Name": "status_code", "Value": "404"},
            ],
        )

    @patch("requests.post")
    @patch("api.integrations.crustdata_client.CloudWatchMetrics.put_metric")
    @patch("api.integrations.crustdata_client.logging")
    def test_404_error_handling_call_post_api(
        self, mock_logging, mock_cloudwatch, mock_post
    ):
        """Test that 404 errors in POST API calls are also logged to CloudWatch"""
        # Mock 404 response for POST request
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.json.return_value = {"error": "No data found"}
        mock_response.text = "Not Found"
        mock_post.return_value = mock_response

        # Call the POST API method
        result = self.integration._call_post_api(
            "https://api.crustdata.com/screener/person/search",
            {
                "filters": [
                    {"filter_type": "CURRENT_TITLE", "type": "in", "value": ["CEO"]}
                ]
            },
        )

        # Verify the result is None
        self.assertIsNone(result)

        # Verify that logging.info was called (it gets called multiple times for request logging)
        self.assertTrue(mock_logging.info.called)
        mock_logging.error.assert_not_called()

        # Check that one of the info calls was for the 404 error
        info_calls = [str(call) for call in mock_logging.info.call_args_list]
        has_404_log = any(
            "API returned 404 (no data found)" in call for call in info_calls
        )
        self.assertTrue(
            has_404_log, "Expected 404 error log message not found in info calls"
        )

        # Verify CloudWatch metric was sent
        mock_cloudwatch.assert_called_once_with(
            metric_name="crustdata_404_error",
            value=1,
            dimensions=[
                {
                    "Name": "url",
                    "Value": "https://api.crustdata.com/screener/person/search",
                },
                {"Name": "status_code", "Value": "404"},
            ],
        )

    @patch("requests.get")
    @patch("api.integrations.crustdata_client.CloudWatchMetrics.put_metric")
    @patch("api.integrations.crustdata_client.logging")
    def test_non_404_error_still_triggers_sentry(
        self, mock_logging, mock_cloudwatch, mock_get
    ):
        """Test that non-404 errors still use logging.exception to trigger Sentry alerts"""
        # Mock 500 HTTP error response
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.json.return_value = {"error": "Internal Server Error"}
        mock_response.text = "Internal Server Error"

        http_error = requests.exceptions.HTTPError(response=mock_response)
        http_error.response = mock_response
        mock_response.raise_for_status.side_effect = http_error
        mock_get.return_value = mock_response

        # Call the API method
        result = self.integration._call_api(
            "https://api.crustdata.com/screener/person/enrich",
            {"linkedin_profile_url": "https://www.linkedin.com/in/test"},
        )

        # Verify the result is None
        self.assertIsNone(result)

        # Verify that logging.exception was called (not logging.info)
        mock_logging.exception.assert_called_once()
        mock_logging.info.assert_not_called()

        # Verify CloudWatch metric was NOT sent for non-404 errors
        mock_cloudwatch.assert_not_called()

        # Verify the exception log message contains expected information
        log_call_args = mock_logging.exception.call_args[0][0]
        self.assertIn("Failed to call API", log_call_args)
        self.assertIn("Status code: 500", log_call_args)

    @patch("requests.get")
    @patch("api.integrations.crustdata_client.CloudWatchMetrics.put_metric")
    @patch("api.integrations.crustdata_client.logging")
    def test_cloudwatch_failure_handling(self, mock_logging, mock_cloudwatch, mock_get):
        """Test that CloudWatch logging failures are handled gracefully"""
        # Mock 404 HTTP error response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.json.return_value = [{"error": "No data found"}]
        mock_response.text = "Not Found"

        http_error = requests.exceptions.HTTPError(response=mock_response)
        http_error.response = mock_response
        mock_response.raise_for_status.side_effect = http_error
        mock_get.return_value = mock_response

        # Make CloudWatch metric sending fail
        mock_cloudwatch.side_effect = Exception("CloudWatch unavailable")

        # Call the API method
        result = self.integration._call_api(
            "https://api.crustdata.com/screener/person/enrich",
            {"linkedin_profile_url": "https://www.linkedin.com/in/test"},
        )

        # Verify the result is still None
        self.assertIsNone(result)

        # Verify that logging.info was still called for the 404
        self.assertTrue(mock_logging.info.called)

        # Verify that logging.error was called for the CloudWatch failure
        self.assertTrue(mock_logging.error.called)

        # Check that one of the error calls was for CloudWatch failure
        error_calls = [str(call) for call in mock_logging.error.call_args_list]
        has_cloudwatch_error = any(
            "Failed to log 404 metric to CloudWatch" in call for call in error_calls
        )
        self.assertTrue(
            has_cloudwatch_error, "Expected CloudWatch failure log message not found"
        )


@pytest.fixture
def mock_company_response():
    return [
        {
            "company_name": "Test Company",
            "company_website_domain": "test.com",
            "linkedin_profile_url": "https://linkedin.com/company/test-company",
            "headcount": {
                "linkedin_headcount": 500,
                "linkedin_headcount_by_role_absolute": {
                    "engineering": 100,
                    "sales": 50,
                },
            },
        }
    ]


@pytest.fixture
def company_target_info():
    group = TargetInfoGroup(meta={"type": "company"})
    return TargetInfo(
        target_key="test.com",
        target_info_group=group,
        meta={"type": "company", "field": "domain"},
        docs={
            "domain": {
                "value": "test.com",
                "meta": {"field_name": "domain", "type": "company"},
            }
        },
    )


@pytest.mark.django_db
def test_get_company_data_with_domain(mock_company_response, company_target_info):
    # Change the patching target to where CrustdataClient is instantiated
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        # Setup mock
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_company.return_value = mock_company_response[
            0
        ]  # Notice we're returning the first item

        # Initialize integration
        integration = CrustdataAdapter(company_target_info)

        # Test simple field
        result = integration.enrich_data("company_name")
        assert result == "Test Company"

        # Test nested field
        result = integration.enrich_data("headcount.linkedin_headcount")
        assert result == 500

        # Verify mock was called correctly
        mock_instance.enrich_company.assert_called_with(
            company_domain="test.com", company_linkedin_url=None, company_name=None
        )

        # Test invalid field - should return None instead of raising ValueError
        result = integration.enrich_data("invalid_field")
        assert result is None

        # Test nested field that doesn't exist
        result = integration.enrich_data("headcount.invalid_field")
        assert result is None


@pytest.mark.django_db
def test_get_company_data_with_empty_response(
    mock_company_response, company_target_info
):
    # Add test for empty response handling in CrustdataAdapter
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_company.return_value = None

        integration = CrustdataAdapter(company_target_info)
        result = integration.enrich_data("company_name")
        assert result is None


@pytest.fixture
def mock_contact_response():
    return [
        {
            "name": "John Doe",
            "email": "<EMAIL>",
            "title": "Software Engineer",
            "location": "San Francisco, CA",
            "linkedin_profile_url": "https://linkedin.com/in/johndoe",
        }
    ]


@pytest.fixture
def contact_target_info():
    group = TargetInfoGroup(meta={"type": "contact"})
    return TargetInfo(
        target_key="<EMAIL>",
        target_info_group=group,
        meta={"type": "contact", "field": "email"},
        docs={
            "email": {
                "value": "<EMAIL>",
                "meta": {"field_name": "email", "type": "contact"},
            },
            "linkedin": {
                "value": "https://linkedin.com/in/johndoe",
                "meta": {"field_name": "linkedin", "type": "contact"},
            },
        },
    )


@pytest.mark.django_db
def test_enrich_data_for_fields_company(mock_company_response, company_target_info):
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        # Setup mock
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_company.return_value = mock_company_response[0]

        # Initialize integration
        integration = CrustdataAdapter(company_target_info)

        # Test multiple fields
        fields = [
            "company_name",
            "headcount.linkedin_headcount",
            "company_website_domain",
        ]
        result = integration.enrich_data_for_fields(fields)

        assert result == {
            "company_name": "Test Company",
            "headcount.linkedin_headcount": 500,
            "company_website_domain": "test.com",
        }

        # Verify mock was called correctly
        mock_instance.enrich_company.assert_called_once_with(
            company_domain="test.com", company_linkedin_url=None, company_name=None
        )


@pytest.mark.django_db
def test_enrich_data_for_fields_contact(mock_contact_response, contact_target_info):
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        # Setup mock
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_people.return_value = mock_contact_response[0]

        # Initialize integration
        integration = CrustdataAdapter(contact_target_info)

        # Test multiple fields
        fields = ["name", "title", "location"]
        result = integration.enrich_data_for_fields(fields)

        assert result == {
            "name": "John Doe",
            "title": "Software Engineer",
            "location": "San Francisco, CA",
        }

        # Verify mock was called correctly
        mock_instance.enrich_people.assert_called_once_with(
            linkedin_profile_url="https://linkedin.com/in/johndoe",
            business_email="<EMAIL>",
        )


@pytest.mark.django_db
def test_enrich_data_for_fields_mixed_types(
    mock_company_response, mock_contact_response, contact_target_info
):
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        # Setup mock
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_people.return_value = mock_contact_response[0]
        mock_instance.enrich_company.return_value = mock_company_response[0]

        # Initialize integration
        integration = CrustdataAdapter(contact_target_info)

        # Test with mixed field types (both company and contact fields)
        fields = ["name", "title", "company_name", "headcount.linkedin_headcount"]
        result = integration.enrich_data_for_fields(fields)

        assert result == {
            "name": "John Doe",
            "title": "Software Engineer",
            "company_name": "Test Company",
            "headcount.linkedin_headcount": 500,
        }

        # Verify both mocks were called correctly
        mock_instance.enrich_people.assert_called_once()
        mock_instance.enrich_company.assert_called_once()


@pytest.mark.django_db
def test_enrich_data_for_fields_invalid_fields(company_target_info):
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        # Setup mock
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_company.return_value = {}

        # Initialize integration
        integration = CrustdataAdapter(company_target_info)

        # Test with invalid fields
        fields = ["invalid_field1", "invalid_field2"]
        result = integration.enrich_data_for_fields(fields)

        # Should return empty dictionary since no valid fields are provided
        assert result == {}

        # The client should not be called since no valid fields were provided
        mock_instance.enrich_company.assert_not_called()
        mock_instance.enrich_people.assert_not_called()


@pytest.mark.django_db
def test_enrich_data_for_fields_api_error(company_target_info):
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        # Setup mock to simulate API error
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_company.side_effect = Exception("API Error")

        # Initialize integration
        integration = CrustdataAdapter(company_target_info)

        # Test with valid fields but API fails
        fields = ["company_name", "headcount.linkedin_headcount"]
        result = integration.enrich_data_for_fields(fields)

        # Should return empty dictionary when API fails
        assert result == {}


@pytest.mark.django_db
def test_enrich_data_for_fields_empty_response(company_target_info):
    with patch("api.integrations.crustdata_adapter.CrustdataClient") as MockCrustdata:
        # Setup mock
        mock_instance = MockCrustdata.return_value
        mock_instance.enrich_company.return_value = {}

        # Initialize integration
        integration = CrustdataAdapter(company_target_info)

        # Test with valid fields but empty data response
        fields = ["company_name", "headcount.linkedin_headcount"]
        result = integration.enrich_data_for_fields(fields)

        # Should return empty dictionary when no data is found
        assert result == {}


if __name__ == "__main__":
    unittest.main()
