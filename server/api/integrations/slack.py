import logging
import os
import ssl
import uuid

import certifi
from slack_sdk import Web<PERSON><PERSON>
from slack_sdk.errors import SlackApiError

ssl_context = ssl.create_default_context(cafile=certifi.where())

slack_client = WebClient(token=os.environ.get("SLACK_BOT_TOKEN", ""), ssl=ssl_context)


def send_slack_file(channel, file, title, comment):
    try:
        response = slack_client.files_upload(
            channels=channel,
            file=file,
            title=title,
            initial_comment=comment,
        )
        logging.info(f"File uploaded successfully: {response}")
    except SlackApiError as e:
        logging.error(f"Error uploading file: {e.response['error']}")


def send_slack_file_with_tmp_file(channel, content, title, comment):
    file_path = os.path.join("/tmp", f"{uuid.uuid4()}.txt")

    with open(file_path, "w") as file:
        file.write(content)

    send_slack_file(channel, file_path, title, comment)
    os.remove(file_path)


def send_slack_message(channel, message):
    try:
        response = slack_client.chat_postMessage(
            channel=channel,
            text=message,
        )
        logging.info(f"Sent a message to {channel}: {message}")
    except SlackApiError as e:
        logging.error(
            f"Got an error: {e.response['error']} with status code: {e.response.status_code}"
        )
