import logging
from typing import Any, Dict, Generator, List, Literal, Optional, Tuple

import numpy as np
from langchain.chains import <PERSON><PERSON>hain
from langchain.chat_models import ChatOpenAI
from langchain.output_parsers import OutputFixingParser, PydanticOutputParser
from langchain.prompts import PromptTemplate
from pydantic import BaseModel, Field

from ..llms import get_llm_for_embeddings
from .crustdata_client import (
    CrustdataClient,
)


# Wrap the original parser with automatic retry and format correction
class SmallLLMTaskResolver:
    """Handles small NLP tasks using LLMs"""

    def __init__(self):
        self._model = "gpt-4o-mini-2024-07-18"
        # fallback models: gpt-4o-2024-11-20, claude-3-5-haiku-20241022

    def call(
        self,
        arguments: Dict[str, Any],
        prompt_template: PromptTemplate,
        output_pydantic_model: BaseModel,
    ) -> Dict[str, Any]:
        """Match a job title with multiple search terms using LLM"""
        # Initialize model
        llm = ChatOpenAI(model=self._model, temperature=0)

        # Set up parser with error correction
        parser = PydanticOutputParser(pydantic_object=output_pydantic_model)
        retrying_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)

        # Create and run the chain
        chain = LLMChain(
            llm=llm,
            prompt=prompt_template,
            output_parser=retrying_parser,
        )
        response = chain.run(arguments)

        return response.dict()


# Pydantic model for title matching
class TitleMatchOutput(BaseModel):
    label: Literal["YES", "NO"] = Field(
        ..., description="Whether the title matches the search term"
    )
    confidence: Literal["HIGH", "MEDIUM", "LOW"] = Field(
        ..., description="Confidence of the match"
    )
    explanation: str = Field(..., description="Explanation of the reasoning")


# Title matching prompt template
prompt_template = PromptTemplate.from_template(
    """
You are a helpful assistant that evaluates whether a person's job title matches any of the desired search terms.
You must respond ONLY in JSON and follow the schema strictly.

Input:
- Title: {title}
- Search Terms: {search_titles}

Instructions:
1. Determine if the title aligns with any of the search terms (same role, similar responsibilities, or abbreviation match).
2. Use "label": "YES" or "NO".
3. Use "confidence": "HIGH", "MEDIUM", or "LOW" based on how obvious or fuzzy the match is.
4. Use "explanation" to explain your reasoning briefly, mentioning which search term matched if applicable.

Respond with a JSON object that matches this schema:
{{
  "label": "YES" or "NO",
  "confidence": "HIGH" or "MEDIUM" or "LOW",
  "explanation": "Short reasoning string"
}}
"""
)


class ContactDataCompanyDataMatcher:
    """Match contacts back to companies"""

    def __init__(
        self,
        titles: List[str],
        contact_data: List[Dict[str, Any]],
        company_data: List[Dict[str, Any]],
        company_name_field: str = "Company name",
    ):
        _, embedding_model = get_llm_for_embeddings()
        self._embedding_model = embedding_model

        self._titles = titles
        self._contact_data = contact_data
        self._company_name_field = company_name_field
        self._company_data = self._process_company_data(company_data)
        self._title_resolver = SmallLLMTaskResolver()
        self._title_embeddings = self._get_embeddings(self._titles)
        self._company_name_embeddings = {
            name: data["company_name_embedding"]
            for name, data in self._company_data.items()
        }

    def _process_company_data(
        self, company_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Process company data to prepare for matching"""
        company_name_to_data = {}

        for company in company_data:
            company_name = company.get(self._company_name_field, "")
            if not company_name:
                logging.error(f"No company name found for company: {company}")
                continue
            company_name_to_data[company_name] = {
                "company_name": company_name,
                "company_data": company,
                "company_name_embedding": self._embedding_model.embed_query(
                    company_name
                ),
            }

        return company_name_to_data

    def _find_best_match_for_employer(
        self,
        employer_name: str,
        employer_title: str,
        company_name_to_data: Dict[str, Any],
        company_name_to_embeddings: Dict[str, Any],
    ) -> Optional[Dict[str, Any]]:
        """Find the best match for an employer based on company similarity"""
        # Match title with search titles
        title_match_result = self._match_title_with_search_titles(employer_title)

        if not title_match_result["is_match"]:
            logging.error(f"No title match found for title '{employer_title}'")
            return None

        # Get embedding for the employer name
        employer_embedding = self._get_embeddings([employer_name])[0]

        # Track best match
        best_match = None
        best_similarity = -1

        # Find the best company match
        for company_name, company_embedding in company_name_to_embeddings.items():
            similarity = self._calculate_similarity(
                employer_embedding, company_embedding
            )

            # Only consider reasonable matches
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = {
                    "employer_name": employer_name,
                    "employer_title": employer_title,
                    "company_name": company_name,
                    "company_similarity": similarity,
                    "title_matched": title_match_result["matched_title"],
                    "title_match_method": title_match_result["match_method"],
                    "title_match_confidence": title_match_result["confidence"],
                    "title_match_explanation": title_match_result["explanation"],
                    "matched_company": company_name_to_data[company_name],
                }

        # Log the best match information
        if best_match:
            logging.info(
                f"Best company match for employer '{employer_name}' with title '{employer_title}': "
                f"'{best_match['company_name']}' with similarity {best_match['company_similarity']:.4f}"
            )
        else:
            logging.debug(
                f"No match found for employer '{employer_name}' with title '{employer_title}'"
            )

        return best_match

    def _find_best_employer_match(
        self,
        current_employers: List[Dict[str, Any]],
        company_name_to_data: Dict[str, Any],
        company_name_to_embeddings: Dict[str, Any],
    ) -> Optional[Dict[str, Any]]:
        """Find the best employer match based on company similarity

        Returns:
            best_match
        """
        best_match = None

        for employer in current_employers:
            employer_name = employer.get("company_name", "")
            employer_title = employer.get("title", "")

            if not employer_name or not employer_title:
                continue

            # Find the best match for this employer
            match = self._find_best_match_for_employer(
                employer_name=employer_name,
                employer_title=employer_title,
                company_name_to_data=company_name_to_data,
                company_name_to_embeddings=company_name_to_embeddings,
            )

            if not match:
                continue

            # Otherwise, keep track of best match so far
            if (
                not best_match
                or match["company_similarity"] > best_match["company_similarity"]
            ):
                best_match = match

                # If this match is above threshold, use it immediately
                if match["company_similarity"] >= 0.85:
                    break
        return best_match

    def _match_title_with_search_titles(self, employer_title: str) -> Dict[str, Any]:
        """Match an employer title with search titles using various methods"""
        result = {
            "is_match": False,
            "matched_title": None,
            "match_method": "none",
            "confidence": 0.0,
            "explanation": "",
        }

        # 1. First check for substring match
        employer_title_lower = employer_title.lower()
        for title in self._titles:
            if title.lower() in employer_title_lower:
                result.update(
                    {
                        "is_match": True,
                        "matched_title": title,
                        "match_method": "substring",
                        "confidence": 1.0,
                    }
                )
                return result

        # 2. If no substring match, use embeddings
        employer_title_embedding = self._get_embeddings([employer_title])[0]
        best_title_similarity = -1

        for i, title_embedding in enumerate(self._title_embeddings):
            title_similarity = self._calculate_similarity(
                employer_title_embedding, title_embedding
            )
            if title_similarity > best_title_similarity:
                best_title_similarity = title_similarity
                result.update(
                    {"matched_title": self._titles[i], "confidence": title_similarity}
                )

        # Consider it a match if similarity is high enough
        if best_title_similarity > 0.8:  # Adjust threshold as needed
            result.update({"is_match": True, "match_method": "embedding"})
            return result
        elif best_title_similarity > 0.5:
            # 3. If still no match, use SmallTaskResolver as fallback
            try:
                llm_result = self._title_resolver.call(
                    {"title": employer_title, "search_titles": self._titles},
                    prompt_template,
                    TitleMatchOutput,
                )
                if llm_result.get("label") == "YES":
                    confidence = {"HIGH": 0.9, "MEDIUM": 0.7, "LOW": 0.5}.get(
                        llm_result.get("confidence"), 0.5
                    )
                    result.update(
                        {
                            "is_match": True,
                            "match_method": "llm",
                            "confidence": confidence,
                            "explanation": llm_result.get("explanation", ""),
                        }
                    )
                    logging.info(
                        f"LLM title match: '{employer_title}' matches with confidence: {llm_result.get('confidence')} - {llm_result.get('explanation')}"
                    )
                    return result
            except Exception as e:
                logging.exception(
                    f"Error using SmallLLMTaskResolver for title matching: {e}"
                )

        return result

    def _match_single_contact(self, contact: Dict[str, Any]) -> Dict[str, Any]:
        # Find current employers
        current_employers = self._get_current_employers(contact)
        if not current_employers:
            logging.error(
                f"No current employers found for contact {contact.get('name', 'Unknown')}"
            )
            return None

        # Find the best employer match
        best_match = self._find_best_employer_match(
            current_employers=current_employers,
            company_name_to_data=self._company_data,
            company_name_to_embeddings=self._company_name_embeddings,
        )

        # Apply the best match found (or report no match)
        if best_match and "company_similarity" in best_match:
            if best_match["company_similarity"] >= 0.75:
                return best_match
            else:
                logging.debug(
                    f"Using best match below threshold for contact {contact.get('name', 'Unknown')}: "
                    f"Company '{best_match['company_name']}' (similarity: {best_match['company_similarity']:.4f})"
                )
                return None
        else:
            logging.debug(
                f"No matches found for contact {contact.get('name', 'Unknown')}"
            )
            return None

    def _get_current_employers(self, contact: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get current employers from contact data"""
        if "employer" in contact and isinstance(contact["employer"], list):
            return [
                emp
                for emp in contact["employer"]
                if emp.get("end_date") is None or "end_date" not in emp
            ]
        return []

    def match(self) -> Generator[Tuple[Dict[str, Any], Dict[str, Any]], None, None]:
        for contact in self._contact_data:
            best_match = self._match_single_contact(contact)
            if best_match:
                yield contact, best_match

    def _get_embeddings(self, texts: List[str]) -> List[Any]:
        """Get embeddings for a list of texts using the embedding model"""
        # Generate embeddings for each text
        embeddings = []
        for text in texts:
            if not text:
                # Handle empty strings with zero vectors
                embeddings.append([0.0] * 1536)  # Assuming 1536-dimensional embeddings
                continue
            embedding = self._embedding_model.embed_query(text)
            embeddings.append(embedding)

        return embeddings

    def _calculate_similarity(self, embedding1, embedding2):
        """Calculate cosine similarity between two embeddings."""
        # Convert to numpy arrays if they aren't already
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        # Avoid division by zero
        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)


class CrustdataAdapterForConversion:
    """Adapter for converting company data to contact data using Crustdata API"""

    def __init__(
        self,
        titles: List[str],
        company_name_field: str = "Company name",
        company_linkedin_profile_url_field: str = "LinkedIn profile URL",
    ):
        if (
            not titles
            or not isinstance(titles, list)
            or not all(isinstance(title, str) for title in titles)
        ):
            raise ValueError("Titles must be a list of strings")
        stripped_titles = [title.strip() for title in titles]
        stripped_titles = [title for title in stripped_titles if title]
        if not stripped_titles:
            raise ValueError("Titles must not be empty")
        self._titles = stripped_titles

        self._crustdata_client = CrustdataClient()
        self._company_name_field = company_name_field or "Company name"
        self._company_linkedin_profile_url_field = (
            company_linkedin_profile_url_field or "LinkedIn profile URL"
        )

    # interface
    def convert_company_data_to_contact_data(
        self, company_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert company data to contact data with specified job titles"""
        batch_size = 10
        contact_data = []
        for i in range(0, len(company_data), batch_size):
            company_data_batch = company_data[i : i + batch_size]
            contact_data.extend(
                self._convert_batch_company_data_to_contact_data(company_data_batch)
            )
        return contact_data

    def _convert_batch_company_data_to_contact_data(
        self, company_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process a batch of company data to find matching contacts"""
        company_names = [
            company.get(self._company_name_field, "") for company in company_data
        ]
        company_names = [name for name in company_names if name]
        if len(company_names) == 0:
            logging.error(f"No company names found in company_data: {company_data}")
            return []

        raw_contact_data = self._crustdata_client.find_contacts_for_company_and_titles(
            company_names, self._titles
        )

        matched_contact_data = self._match_contacts_with_companies(
            raw_contact_data, company_data
        )
        return matched_contact_data

    def _match_contacts_with_companies(
        self, contact_data: List[Dict[str, Any]], company_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Match contacts with companies using embeddings similarity for company names and title verification"""
        matcher = ContactDataCompanyDataMatcher(
            self._titles, contact_data, company_data, self._company_name_field
        )
        matched_contacts = []
        for matched_contact, best_matched_company in matcher.match():
            enriched_contact = self._update_contact_with_match(
                matched_contact, best_matched_company
            )

            matched_contacts.append(enriched_contact)
        return matched_contacts

    def _create_filtered_contact(self, contact: Dict[str, Any]) -> Dict[str, Any]:
        """Create a filtered contact with only the desired fields"""
        return {
            "name": contact.get("name", ""),
            "location": contact.get("location", ""),
            "linkedin_profile_url": contact.get(
                "flagship_profile_url", ""
            ),  # Renamed field
            "headline": contact.get("headline", ""),
            "summary": contact.get("summary", ""),
            "num_of_connections": contact.get("num_of_connections", 0),
            "business_email": contact.get("business_email", ""),
            "current_title": contact.get("current_title", ""),
        }

    def _update_contact_with_match(
        self, contact: Dict[str, Any], match: Dict[str, Any]
    ):
        """Update the contact with a valid match"""
        filtered_contact = self._create_filtered_contact(contact)
        if "matched_company" in match:
            for k, v in match["matched_company"].items():
                filtered_contact[f"matched_company_{k}"] = v
        filtered_contact["matched_title"] = match["title_matched"]
        filtered_contact["title_match_method"] = match["title_match_method"]
        filtered_contact["title_match_confidence"] = match["title_match_confidence"]
        filtered_contact["title_match_explanation"] = match["title_match_explanation"]
        filtered_contact["company_similarity"] = match["company_similarity"]
        return filtered_contact

    def search_for_business_email_batch(
        self, contacts: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        # temporary bypass it since it doesn't work well
        return []

        # 25 contacts per batch
        batch_size = 25
        updated_contacts = []
        for i in range(0, len(contacts), batch_size):
            contacts_batch = contacts[i : i + batch_size]

            """Search for a business email for a contact"""
            query_key = "linkedin_profile_url"
            query_values = [
                contact.get("linkedin_profile_url", "") for contact in contacts_batch
            ]
            fields = ["business_email"]
            enrich_realtime = True
            results = self._crustdata_client._enrich_people_impl_batch(
                query_key, query_values, enrich_realtime, fields
            )

            business_email_map = {}

            for result in results:
                linkedin_profile_url = result.get("linkedin_flagship_url", "")
                business_email = result.get("business_email", "")
                if linkedin_profile_url and business_email:
                    business_email_map[linkedin_profile_url] = business_email

            for contact in contacts_batch:
                linkedin_profile_url = contact.get("linkedin_profile_url", "")
                if linkedin_profile_url:
                    contact["business_email"] = business_email_map.get(
                        linkedin_profile_url, None
                    )
                else:
                    logging.error(
                        f"No linkedin profile url found for contact: {contact}"
                    )
                updated_contacts.append(contact)
        return updated_contacts
