import logging
from typing import Optional

import tldextract
from email_validator import validate_email

from ..models import DebugLogEntry, TargetInfo
from ..utils import CloudWatchMetrics
from .crustdata_client import (
    COMPANY_AVAILABLE_FIELDS,
    DISABLED_COMPANY_FIELDS,
    DISABLED_PEOPLE_FIELDS,
    PEOPLE_AVAILABLE_FIELDS,
    CrustdataClient,
)


# TODO: move to utils
def is_business_email_match(field_name: str, field_value: str):
    if not isinstance(field_value, str):
        return False
    field_value = field_value.strip()

    try:
        validate_email(field_value)
        # also return False for non business emails like gmail, hotmail, etc.
        known_non_business_email_providers = ["gmail", "hotmail", "yahoo"]
        if any(
            provider in field_value.lower()
            for provider in known_non_business_email_providers
        ):
            return False
        return True
    except Exception:
        return False


class CrustdataAdapter:
    def __init__(self, target_info: TargetInfo):
        self._target_info = target_info
        self._crustdata_client = CrustdataClient()

    @property
    def target_type(self):
        return self._target_info.target_info_group.meta.get("type") or ""

    def is_company(self):
        return self.target_type.lower() == "company"

    def is_contact(self):
        return self.target_type.lower() == "contact"

    @property
    def target_key_field_name(self):
        return self._target_info.meta.get("field") or "target_key"

    def _convert_doc_map(self):
        doc_map = {}
        doc_map[self.target_key_field_name] = self._target_info.target_key
        for doc in self._target_info.docs.values():
            field_name = doc.get("meta", {}).get("field_name")
            if not field_name:
                field_name = f"field_{len(doc_map)}"
            doc_map[field_name] = doc.get("value")
        return doc_map

    def _fuzzy_match_company_fields(self):
        doc_map = self._convert_doc_map()

        def is_domain_match(field_name: str, field_value: str):
            # Check if it's a regular domain field
            if "domain" in field_name.lower() or "website" in field_name.lower():
                return True
            # For contacts, also check if it's a business email
            return self.is_contact() and is_business_email_match(
                field_name, field_value
            )

        def is_company_linkedin_url_match(field_name: str, field_value: str):
            if self.is_contact():
                # if there's linkedin for contact, it's likely a personal LinkedIn URL
                return False

            # Check if field_value is a string before processing
            if not isinstance(field_value, str):
                return False

            # Check if it's a LinkedIn company URL
            return (
                "linkedin" in field_name.lower()
                or "linkedin.com/company/" in field_value.lower()
            )

        def is_company_name_match(field_name: str, field_value: str):
            if self.is_company():
                return "name" in field_name.lower() or "company" == field_name.lower()
            else:
                # only company might be too generic
                return (
                    "company name" in field_name.lower()
                    or "company_name" in field_name.lower()
                )

        def extract_domain(url: str):
            copied_url = url
            if not copied_url.startswith("http") and copied_url.find("@") != -1:
                copied_url = copied_url.split("@")[1]
            if not copied_url:
                return None
            extracted_url = tldextract.extract(copied_url)
            campany_domain = f"{extracted_url.domain}.{extracted_url.suffix}"
            return campany_domain

        company_domain = None
        company_linkedin_url = None
        company_name = None

        for field_name, value in doc_map.items():
            if is_domain_match(field_name, value):
                company_domain = extract_domain(value)
            elif is_company_linkedin_url_match(field_name, value):
                company_linkedin_url = value
            elif is_company_name_match(field_name, value):
                company_name = value
        # if nothing found, use the target_key as company_name
        if not company_domain and not company_linkedin_url and not company_name:
            company_name = self._target_info.target_key
        return company_domain, company_linkedin_url, company_name

    def _fuzzy_match_contact_fields(self):
        doc_map = self._convert_doc_map()

        linkedin_profile_url = None
        business_email = None

        def is_profile_linkedin_url_match(field_name: str, field_value: str):
            if not isinstance(field_value, str):
                return False
            return (
                "linkedin.com/in/" in field_value.lower()
                or "linkedin.com/profile/" in field_value.lower()
            )

        for field_name, value in doc_map.items():
            if is_profile_linkedin_url_match(field_name, value):
                linkedin_profile_url = value
            elif is_business_email_match(field_name, value):
                business_email = value

        return linkedin_profile_url, business_email

    def _get_field_value(self, data: dict, field: str):

        def get_field_value_recursive(data: dict, field_name: str):
            field_keys = field_name.split(".")
            for key in field_keys:
                if key not in data:
                    logging.warning(f"Field {field_name} not found in data: {data}")
                    return None
                data = data[key]
            return data

        if field in data:
            return data[field]
        return get_field_value_recursive(data, field)

    def _get_full_company_data(self):
        (
            company_domain,
            company_linkedin_url,
            company_name,
        ) = self._fuzzy_match_company_fields()

        if not company_domain and not company_linkedin_url and not company_name:
            return {}
        company_data = self._crustdata_client.enrich_company(
            company_domain=company_domain,
            company_linkedin_url=company_linkedin_url,
            company_name=company_name,
        )
        if not company_data:
            return {}

        # The data is now directly a dict, not a list
        if not isinstance(company_data, dict):
            logging.error(f"Unexpected company data format: {company_data}")
            return {}
        return company_data

    def _get_full_contact_data(self):
        linkedin_profile_url, business_email = self._fuzzy_match_contact_fields()
        if not linkedin_profile_url and not business_email:
            return {}
        contact_data = self._crustdata_client.enrich_people(
            linkedin_profile_url=linkedin_profile_url,
            business_email=business_email,
        )
        if not contact_data:
            return {}
        if not isinstance(contact_data, dict):
            logging.error(f"Unexpected contact data format: {contact_data}")
            return {}
        return contact_data

    def enrich_data(self, field: str) -> Optional[str]:
        result = self.enrich_data_for_fields([field])
        if field in result:
            return result[field]
        return None

    def enrich_data_for_fields(self, fields: list[str]) -> dict:
        invalid_fields = [
            field
            for field in fields
            if field not in COMPANY_AVAILABLE_FIELDS
            and field not in PEOPLE_AVAILABLE_FIELDS
        ]
        if invalid_fields:
            logging.error(f"Invalid fields for enrichment: {invalid_fields}")

        company_fields = [
            field for field in fields if field in COMPANY_AVAILABLE_FIELDS
        ]
        contact_fields = [field for field in fields if field in PEOPLE_AVAILABLE_FIELDS]
        if not company_fields and not contact_fields:
            logging.error(f"No valid fields for enrichment: {fields}")
            return {}

        result = {}
        if company_fields:
            try:
                company_data = self._get_full_company_data()
            except Exception as e:
                logging.exception(f"Failed to get company data: {e}")
                company_data = {}

            if company_data:
                for field in company_fields:
                    try:
                        field_value = self._get_field_value(
                            data=company_data, field=field
                        )
                    except Exception as e:
                        logging.exception(f"Failed to get field value for {field}: {e}")
                        field_value = None

                    # Log metric for field value found/not found
                    field_found = field_value is not None
                    CloudWatchMetrics.put_metric(
                        metric_name="CrustdataFieldValueFound",
                        value=1.0 if field_found else 0.0,
                        dimensions=[
                            {"Name": "FieldType", "Value": "company"},
                            {"Name": "FieldName", "Value": field},
                            {"Name": "TargetId", "Value": str(self._target_info.id)},
                        ],
                    )

                    if field_value:
                        result[field] = field_value

        if contact_fields:
            try:
                contact_data = self._get_full_contact_data()
            except Exception as e:
                logging.exception(f"Failed to get contact data: {e}")
                contact_data = {}

            if contact_data:
                for field in contact_fields:
                    try:
                        field_value = self._get_field_value(
                            data=contact_data, field=field
                        )
                    except Exception as e:
                        logging.exception(f"Failed to get field value for {field}: {e}")
                        field_value = None

                    # Log metric for field value found/not found
                    field_found = field_value is not None
                    CloudWatchMetrics.put_metric(
                        metric_name="CrustdataFieldValueFound",
                        value=1.0 if field_found else 0.0,
                        dimensions=[
                            {"Name": "FieldType", "Value": "contact"},
                            {"Name": "FieldName", "Value": field},
                            {"Name": "TargetId", "Value": str(self._target_info.id)},
                        ],
                    )

                    if field_value is not None:
                        result[field] = field_value

        self._log_crustdata_query(fields=fields, result=result)
        return result

    def enrich_data_for_all_fields(self):
        result = {}
        try:
            result.update(self._get_full_company_data())
        except Exception as e:
            logging.exception(f"Failed to get company data: {e}")

        if self.is_contact():
            try:
                result.update(self._get_full_contact_data())
            except Exception as e:
                logging.exception(f"Failed to get contact data: {e}")

        self._log_crustdata_query(fields=None, result=result)
        return result

    def _log_crustdata_query(self, fields: Optional[list[str]], result: dict):
        payload = {
            "target_id": self._target_info.id,
            "fields": fields,
            "result": result,
        }
        DebugLogEntry.objects.create(
            entry_type="crustdata_query_v2",
            payload=payload,
        )

    def flatten_data(self, data, parent_key="", separator=".", remove_null=True):
        """
        Converts a nested dictionary into a flat dictionary with keys
        representing the path in the original structure.
        Args:
            data: The nested dictionary to flatten
            parent_key: The parent key for the current recursion level (used internally)
            separator: The string used to join key components (default: '.')
        Returns:
            A flattened dictionary where keys are paths from the original dict
        """
        flattened = {}

        for key, value in data.items():
            if remove_null and value is None:
                continue

            new_key = f"{parent_key}{separator}{key}" if parent_key else key

            if new_key in DISABLED_COMPANY_FIELDS or new_key in DISABLED_PEOPLE_FIELDS:
                continue

            if (
                new_key in COMPANY_AVAILABLE_FIELDS
                or new_key in PEOPLE_AVAILABLE_FIELDS
            ):
                flattened[new_key] = value
            else:
                if isinstance(value, dict):
                    # Recursively flatten the nested dictionary
                    flattened.update(
                        self.flatten_data(value, new_key, separator, remove_null)
                    )
                else:
                    # Add the leaf value with its full path key
                    flattened[new_key] = value

        return flattened


class CrustdataLinkedinCrawler:
    def __init__(self):
        self._crustdata_client = CrustdataClient()

    @staticmethod
    def is_linkedin_company_profile_page(url: str):
        return "linkedin.com/company/" in url.lower()

    @staticmethod
    def is_linkedin_profile_url(linkedin_url: str):
        return any(
            pattern in linkedin_url.lower()
            for pattern in [
                "linkedin.com/in/",
                "linkedin.com/profile/",
                "linkedin.com/pub/",
            ]
        )

    def enrich_linkedin_link(self, linkedin_url: str):
        if not linkedin_url:
            return None

        # Check if it's a LinkedIn company URL
        if CrustdataLinkedinCrawler.is_linkedin_company_profile_page(linkedin_url):
            return self._crustdata_client.enrich_company(
                company_linkedin_url=linkedin_url
            )
        # Check if it's a LinkedIn profile URL
        elif CrustdataLinkedinCrawler.is_linkedin_profile_url(linkedin_url):
            return self._crustdata_client.enrich_people(
                linkedin_profile_url=linkedin_url
            )
        else:
            logging.error(f"Invalid LinkedIn URL: {linkedin_url}")
            return None
