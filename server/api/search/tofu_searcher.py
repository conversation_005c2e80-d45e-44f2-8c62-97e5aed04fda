import logging
import time
from abc import ABC, abstractmethod
from enum import Enum
from typing import List

from langchain_core.documents import Document
from langchain_pinecone import PineconeVectorStore

from ..llms import get_llm_for_embeddings
from ..utils import CloudWatchMetrics
from .pinecone_singleton import PineconeSingleton


class SearchObjectType(str, Enum):
    targets = "targets"
    assets = "assets"


class TofuSearcher(ABC):

    def __init__(self, playbook_id: str):
        self.playbook_id = playbook_id

    @abstractmethod
    def search(
        self, query: str, top_k: int, object_types: List[SearchObjectType]
    ) -> List[Document]:
        pass


class PineconeTofuSearcher(TofuSearcher):
    def __init__(self, playbook_id: str, use_mrmr: bool = True):
        super().__init__(playbook_id)
        _, llm = get_llm_for_embeddings()
        self.use_mrmr = use_mrmr
        self.namespace = f"{self.playbook_id}_serverless"
        self.pinecone_index = PineconeVectorStore(
            index=PineconeSingleton.get_instance(),
            embedding=llm,
            text_key="text",
            namespace=self.namespace,
        )
        self.column_id_mapping = {
            SearchObjectType.targets: "target_info",
            SearchObjectType.assets: "assets",
        }

    def search(
        self, query: str, top_k: int, object_types: List[SearchObjectType]
    ) -> List[Document]:
        start_time = time.time()

        object_type_metric_value = ",".join(object_types) or "all"

        try:
            filter = {}
            if len(object_types) > 0:
                filter = {
                    "$or": [
                        {"column_id": self.column_id_mapping[object_type]}
                        for object_type in object_types
                    ]
                }

            if self.use_mrmr:
                result_docs = self.pinecone_index.max_marginal_relevance_search(
                    query, top_k, namespace=self.namespace, filter=filter
                )
            else:
                result_docs = self.pinecone_index.similarity_search(
                    query, top_k, namespace=self.namespace, filter=filter
                )

            CloudWatchMetrics.put_metric(
                "pinecone_search_result_count",
                len(result_docs),
                [
                    {"Name": "playbook_id", "Value": str(self.playbook_id)},
                    {"Name": "object_types", "Value": object_type_metric_value},
                    {"Name": "use_mrmr", "Value": str(self.use_mrmr)},
                    {"Name": "top_k", "Value": str(top_k)},
                ],
            )

            return [
                {"content": doc.page_content, "metadata": doc.metadata}
                for doc in result_docs
            ]
        except Exception as e:
            logging.exception(f"Error searching for playbook {self.playbook_id}: {e}")
            CloudWatchMetrics.put_metric(
                "pinecone_search_error_count",
                1,
                [
                    {"Name": "playbook_id", "Value": str(self.playbook_id)},
                    {"Name": "object_types", "Value": object_type_metric_value},
                    {"Name": "use_mrmr", "Value": str(self.use_mrmr)},
                    {"Name": "top_k", "Value": str(top_k)},
                ],
            )
            raise e
        finally:
            end_time = time.time()
            CloudWatchMetrics.put_metric(
                "pinecone_search_latency",
                end_time - start_time,
                [
                    {"Name": "playbook_id", "Value": str(self.playbook_id)},
                    {"Name": "object_types", "Value": object_type_metric_value},
                    {"Name": "use_mrmr", "Value": str(self.use_mrmr)},
                    {"Name": "top_k", "Value": str(top_k)},
                ],
            )
