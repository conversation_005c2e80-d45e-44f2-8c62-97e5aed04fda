import logging
import os
import threading
from typing import Optional

import pinecone


class PineconeSingleton:
    _instance: Optional[pinecone.Index] = None
    _lock = threading.Lock()

    @classmethod
    def get_instance(cls) -> pinecone.Index:
        """Returns a singleton instance of the Pinecone Index.
        Creates the instance if it doesn't exist.

        Returns:
            pinecone.Index: The singleton Pinecone Index instance

        Raises:
            Exception: If PINECONE_INDEX_HOST environment variable is not set
            Exception: If unable to connect to Pinecone
        """
        with cls._lock:  # Use thread lock for thread safety
            if cls._instance is None:
                host = os.environ.get("PINECONE_INDEX_HOST")
                if not host:
                    raise Exception(
                        "PINECONE_INDEX_HOST environment variable is not set"
                    )

                try:
                    cls._instance = pinecone.Pinecone().Index(host=host)
                except Exception as e:
                    # Log the error
                    logging.exception(f"Error initializing Pinecone: {e}")
                    raise Exception(f"Failed to connect to Pinecone: {e}")

            return cls._instance
