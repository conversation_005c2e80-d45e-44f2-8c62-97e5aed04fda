import logging

import stripe
from api.models import TofuUser, UserCreditAdjustment
from django.db import transaction
from django.utils import timezone

from .tofu_pages_utils import get_credits_from_subscription_tier, get_tier_from_price_id


def add_credit_adjustment_to_user(
    user_id, credit_adjustment, payment_info=None, additional_info=None
):
    with transaction.atomic():
        user = TofuUser.objects.select_for_update().get(id=user_id)
        UserCreditAdjustment.objects.create(
            user=user,
            credit_adjustment=credit_adjustment,
            payment_info=payment_info,
            additional_info=additional_info,
        )

        user.credits_available += credit_adjustment
        user.credits_last_updated = timezone.now().isoformat()
        user.save()


def reset_credits_for_tofu_lite(tofu_user_id, payment_info):
    try:
        credit_amount = get_credit_amount_from_payment_info(payment_info)
    except Exception as e:
        if "No credit amount found for payment amount" in str(e):
            reset_for_prorated_subscription(tofu_user_id, payment_info)
            return
        raise e

    # base case, the payment amount is expected.
    with transaction.atomic():
        tofu_user = TofuUser.objects.select_for_update().get(id=tofu_user_id)
        # check for existing payment id, and skip if it matches.
        old_payment_id = tofu_user.stripe_payment_id
        if old_payment_id == payment_info.id:
            logging.info(f"Skipping reset for payment id {payment_info.id}")
            return
        old_tier = tofu_user.tofu_lite_subscription_tier
        new_tier = get_new_tier(payment_info)
        if old_tier == new_tier:
            current_credits = tofu_user.credits_available
            UserCreditAdjustment.objects.create(
                user=tofu_user,
                credit_adjustment=credit_amount - current_credits,
                payment_info=payment_info,
            )
            tofu_user.credits_available = credit_amount
            tofu_user.credits_last_updated = timezone.now().isoformat()
        else:
            # roll over the credits
            current_credits = tofu_user.credits_available
            UserCreditAdjustment.objects.create(
                user=tofu_user,
                credit_adjustment=credit_amount,
                payment_info=payment_info,
            )
            tofu_user.credits_available = credit_amount + current_credits
            tofu_user.credits_last_updated = timezone.now().isoformat()
            # update the tier
            tofu_user.tofu_lite_subscription_tier = new_tier
        # store the payment id in the user
        tofu_user.stripe_payment_id = payment_info.id
        tofu_user.save()


def reset_for_prorated_subscription(tofu_user_id, payment_info):
    logging.info(
        f"Resetting for prorated subscription for tofu user {tofu_user_id} for payment_info {payment_info}"
    )
    with transaction.atomic():
        tofu_user = TofuUser.objects.select_for_update().get(id=tofu_user_id)
        # check for existing payment id, and skip if it matches.
        old_payment_id = tofu_user.stripe_payment_id
        if old_payment_id == payment_info.id:
            logging.info(f"Skipping reset for payment id {payment_info.id}")
            return

        subscription_tier = tofu_user.tofu_lite_subscription_tier
        # validate that the tier matches the customer's subscription in Stripe.
        customer_id = tofu_user.stripe_customer_id
        if not customer_id:
            raise Exception(
                f"No customer id found for tofu user {tofu_user_id}, cannot reset for prorated subscription"
            )

        # retrieve the subscription
        # retrieve the subscription
        try:
            subscriptions = stripe.Subscription.list(customer=customer_id)
            if len(subscriptions) == 0:
                raise Exception(
                    f"No subscriptions found for tofu user {tofu_user_id}, cannot reset for prorated subscription"
                )
        except Exception as e:
            logging.error(
                f"Error retrieving subscription for customer {customer_id}: {str(e)}"
            )
            raise Exception(f"Error retrieving subscription: {str(e)}")
        subscriptions = subscriptions.data
        if len(subscriptions) != 1:
            raise Exception(
                f"Expected exactly one subscription for tofu user {tofu_user_id}, got {subscriptions}"
            )
        subscription = subscriptions[0]
        subscription_id = subscription.id
        subscription_items = stripe.SubscriptionItem.list(subscription=subscription_id)
        subscription_items = subscription_items.data
        if len(subscription_items) != 1:
            raise Exception(
                f"Expected exactly one subscription item for tofu user {tofu_user_id}, got {subscription_items}"
            )
        subscription_item = subscription_items[0]
        subscription_price_id = subscription_item.price.id
        # get the price amount from the price id.
        price_tier = get_tier_from_price_id(subscription_price_id)
        if price_tier != subscription_tier:
            raise Exception(
                f"Subscription tier {subscription_tier} does not match price tier {price_tier}"
            )

        # roll over the credits, only for tier 2.
        if subscription_tier == TofuUser.TofuLiteSubscriptionTier.TIER_2:
            credit_amount = get_credits_from_subscription_tier(subscription_tier)
            current_credits = tofu_user.credits_available
            UserCreditAdjustment.objects.create(
                user=tofu_user,
                credit_adjustment=credit_amount,
                payment_info=payment_info,
            )
            tofu_user.credits_available = credit_amount + current_credits
            tofu_user.credits_last_updated = timezone.now().isoformat()

        # store the payment id in the user
        tofu_user.stripe_payment_id = payment_info.id
        tofu_user.save()


def get_new_tier(payment_info):
    payment_amount = payment_info.amount
    if payment_amount == 4900:
        return TofuUser.TofuLiteSubscriptionTier.TIER_1
    elif payment_amount == 9900:
        return TofuUser.TofuLiteSubscriptionTier.TIER_2
    else:
        raise Exception(f"No new tier found for payment amount {payment_amount}")


def get_credit_amount_from_payment_info(payment_info):
    credit_amount_map = {4900: 100, 9900: 300}
    payment_amount = payment_info.amount
    if payment_amount in credit_amount_map:
        return credit_amount_map[payment_amount]
    else:
        raise Exception(f"No credit amount found for payment amount {payment_amount}")


def check_available_credits(user_id):
    with transaction.atomic():
        user = TofuUser.objects.select_for_update().get(id=user_id)
        return user.credits_available
