import base64
import logging
import os
import tempfile
from io import BytesIO

from PIL import Image

from .s3_utils import upload_file
from .utils import create_presigned_url


def write_base64_image(base64_string, output_path):
    """
    Writes a base64 encoded image to a file.

    Args:
        base64_string: The base64 encoded image string.
        output_path: The path to save the rendered image.

    Returns:
        bool: True if successful, False otherwise.
    """
    if not base64_string:
        logging.error("Empty base64 string provided")
        return False

    try:
        # Validate base64 string format
        if not isinstance(base64_string, str):
            logging.error("Base64 string must be a string")
            return False

        # Remove potential header if present (e.g., "data:image/jpeg;base64,")
        if "," in base64_string:
            base64_string = base64_string.split(",")[1]

        image_data = base64.b64decode(base64_string)

        # Check image size before processing (limit to 10MB for consistency)
        if len(image_data) > 10 * 1024 * 1024:
            logging.error(f"Image too large: {len(image_data) / (1024 * 1024):.2f}MB")
            return False

        # Verify output directory exists and is writable
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        image = Image.open(BytesIO(image_data))
        logging.info(f"Image size: {image.size}, format: {image.format}")
        image.save(output_path)
        logging.info(f"Image saved to {output_path}")
        return True
    except Exception as e:
        logging.error(f"Error rendering image: {e}")
        return False


def upload_image_to_s3(
    base64_bytes_data,
    file_name,
    s3_bucket="tofu-generated-images",
    playbook_id=None,
):
    """
    Uploads a base64 encoded image to an S3 bucket as a PNG file.

    Args:
        base64_string: The base64 encoded image string.
        file_name: The desired name for the file in S3 (without extension).
        s3_bucket: The S3 bucket to upload the image to.

    Returns:
        str|None: The S3 URL if successful, None otherwise.
    """

    try:
        # Check image size before uploading (limit to 10MB)
        if len(base64_bytes_data) > 10 * 1024 * 1024:
            logging.error(
                f"Image too large: {len(base64_bytes_data) / (1024 * 1024):.2f}MB"
            )
            return None

        # Validate that it's actually an image
        image = Image.open(BytesIO(base64_bytes_data))
        logging.info(f"Image size: {image.size}, format: {image.format}")

        with tempfile.NamedTemporaryFile(suffix=".png") as temp_file:
            temp_file.write(base64_bytes_data)
            temp_file.flush()
            upload_file(
                file_name=temp_file.name,
                file_type="png",
                bucket=s3_bucket,
                object_name=f"playbook_{playbook_id}/{file_name}.png",  # group by playbook id.
            )
            s3_url = create_presigned_url(
                s3_bucket, f"playbook_{playbook_id}/{file_name}.png"
            )
            logging.info(f"Image uploaded successfully to {s3_url}")
            return s3_url
    except Exception as e:
        logging.error(f"Error uploading image to S3: {e}")
        return None
