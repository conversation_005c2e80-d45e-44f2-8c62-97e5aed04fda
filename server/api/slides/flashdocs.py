import logging
import os
import shutil
import tempfile
import time
from enum import Enum

import requests

from ..utils import TempS3File, is_google_slides_url, is_pptx_s3_file

DEFAULT_FLASHDOCS_TEMPLATE_LIBRARY = os.environ.get(
    "DEFAULT_FLASHDOCS_TEMPLATE_LIBRARY"
)
FLASHDOCS_API_KEY = os.environ.get("FLASHDOCS_API_KEY")


class Outline:
    content_instruction: str = None
    layout_instruction: str = None
    markdown: str = None
    text_placeholder_manual_insertions: list = None
    image_placeholder_manual_insertions: list = None
    slide_id: str = None

    def __init__(
        self,
        content_instruction: str = None,
        layout_instruction: str = None,
        markdown: str = None,
        text_placeholder_manual_insertions: list = None,
        image_placeholder_manual_insertions: list = None,
        slide_id: str = None,
    ):
        self.content_instruction = content_instruction
        self.layout_instruction = layout_instruction
        self.markdown = markdown
        self.text_placeholder_manual_insertions = text_placeholder_manual_insertions
        self.image_placeholder_manual_insertions = image_placeholder_manual_insertions
        self.slide_id = slide_id

    def to_dict(self):
        return {
            "content_instruction": self.content_instruction,
            "layout_instruction": self.layout_instruction,
            "markdown": self.markdown,
            "text_placeholder_manual_insertions": self.text_placeholder_manual_insertions,
            "image_placeholder_manual_insertions": self.image_placeholder_manual_insertions,
            "slide_id": self.slide_id,
        }


class FlashdocsDocumentType(Enum):
    LIBRARY = "library"
    TEMPLATE = "template"


class FlashdocsDocumentFormat(Enum):
    GOOGLE = "google"
    MICROSOFT = "microsoft"


def upload_google_slides_to_flashdocs(
    google_document_link: str,
    document_type: FlashdocsDocumentType = FlashdocsDocumentType.TEMPLATE,
    name: str = "Presentation Name",
    description: str = "Add presentation description here...",
    reset_text_boxes: bool = True,
):
    """
    Upload a Google Slides presentation to Flashdocs.

    Args:
        google_document_link: URL link to the Google Slides document
        document_type: Type of document to create (template or library)
        name: Name to assign to the uploaded presentation
        description: Brief description of the presentation
        reset_text_boxes: Whether to convert all text boxes into placeholders

    Returns:
        dict: Response containing success status and document_id
    """
    if not is_google_slides_url(google_document_link):
        raise ValueError("File is not a Google Slides URL")

    # check if the url has https://, if not, add it.
    if not google_document_link.startswith("https://"):
        google_document_link = "https://" + google_document_link

    # remove any trailing query params.
    google_document_link = google_document_link.split("?")[0]

    url = "https://api.flashdocs.ai/v1/documents/google"
    headers = {"Authorization": f"Bearer {FLASHDOCS_API_KEY}"}
    params = {
        "google_document_link": google_document_link,
        "document_type": document_type.value,
        "name": name,
        "description": description,
        "reset_text_boxes": str(reset_text_boxes).lower(),
    }
    logging.info(f"Flashdocs params: {params}")
    response = requests.post(url, params=params, headers=headers, timeout=300)

    logging.info(f"Flashdocs response: {response.json()}")
    return response.json()


def upload_s3_ppt_to_flashdocs(
    s3_bucket: str,
    s3_filename: str,
    document_type: FlashdocsDocumentType = FlashdocsDocumentType.TEMPLATE,
    name: str = "Presentation Name",
    description: str = "Add presentation description here...",
    reset_text_boxes: bool = True,
):
    """
    Upload a PowerPoint presentation to Flashdocs.

    Args:
        s3_bucket: The S3 bucket containing the PowerPoint file
        s3_filename: The filename of the PowerPoint file in S3
        document_type: Type of document to create (template or library)
        name: Name to assign to the uploaded presentation
        description: Brief description of the presentation
        reset_text_boxes: Whether to convert all text boxes into placeholders

    Returns:
        dict: Response containing success status and document_id
    """
    url = "https://api.flashdocs.ai/v1/documents/pptx"
    headers = {"Authorization": f"Bearer {FLASHDOCS_API_KEY}"}

    # check if the s3 file exists and is a ppt(x) file.
    if not is_pptx_s3_file(s3_filename):
        raise ValueError("File is not a PowerPoint file")

    # download the file from s3.
    # download the file from s3.
    named_temp_filename: str | None = None
    try:
        with (
            TempS3File(s3_bucket, s3_filename) as filename,
            tempfile.NamedTemporaryFile(
                suffix=".pptx", delete=False
            ) as named_temp_file,
        ):
            named_temp_filename = named_temp_file.name
            # Copy the content from the original temp file to the named temp file
            shutil.copy(filename, named_temp_filename)

            # File is now closed, open it for reading
            with open(named_temp_filename, "rb") as file_obj:
                data = {
                    "document_type": document_type.value,
                    "name": name,
                    "description": description,
                    "reset_text_boxes": str(reset_text_boxes).lower(),
                }
                logging.info(f"Flashdocs data: {data}")

                response = requests.post(
                    url,
                    headers=headers,
                    files={"file": file_obj},
                    data=data,
                    timeout=300,
                )

                return response.json()
    except Exception as e:
        logging.error(f"Error uploading PowerPoint to Flashdocs: {e}")
        raise e
    finally:
        if named_temp_filename and os.path.exists(named_temp_filename):
            os.remove(named_temp_filename)


def sync_generate_flashdocs_slides(
    prompt: str = None,
    source_document_id: str = None,
    num_slides: int = None,
    outline: list[Outline] = None,
    audience_domain: str = None,
    presentation_name: str = None,
    export_options: dict = None,
):
    """
    Generate a Flashdocs presentation.

    Args:
        prompt: The prompt to generate the presentation from
        source_document_id: The ID of the source document to use as a template
        num_slides: Number of slides to generate
        outline: List of slide outlines
        audience_domain: Domain of the audience
        presentation_name: Name of the presentation
        export_options: Export options for the presentation
        text_placeholder_manual_insertions: List of text placeholder insertions
        image_placeholder_manual_insertions: List of image placeholder insertions

    Returns:
        dict: Response containing task_id, link_to_deck, runtime, success, and error
    """
    url = "https://api.flashdocs.ai/v3/generate/deck"
    headers = {
        "Authorization": f"Bearer {FLASHDOCS_API_KEY}",
        "Content-Type": "application/json",
    }
    if not source_document_id:
        source_document_id = DEFAULT_FLASHDOCS_TEMPLATE_LIBRARY
    payload = {
        "prompt": prompt,
        "source_document_id": source_document_id,
        "num_slides": num_slides,
        "outline": outline,
        "audience_domain": audience_domain,
        "presentation_name": presentation_name,
        "export_options": export_options,
    }
    logging.info(f"Flashdocs payload: {payload}")
    response = requests.post(url, json=payload, headers=headers)
    return response.json()


def async_generate_flashdocs_slides(
    prompt: str = None,
    source_document_id: str = None,
    num_slides: int = None,
    outline: list[dict] = None,
    audience_domain: str = None,
    presentation_name: str = None,
    export_options: dict = None,
    text_placeholder_manual_insertions: list[dict] = None,
    image_placeholder_manual_insertions: list[dict] = None,
):
    """
    Generate a Flashdocs presentation asynchronously.
    """
    url = "https://api.flashdocs.ai/v3/generate/deck/task"
    headers = {
        "Authorization": f"Bearer {FLASHDOCS_API_KEY}",
        "Content-Type": "application/json",
    }
    payload = {
        "prompt": prompt,
        "source_document_id": source_document_id,
        "num_slides": num_slides,
        "outline": outline,
        "audience_domain": audience_domain,
        "presentation_name": presentation_name,
        "export_options": export_options,
        "text_placeholder_manual_insertions": text_placeholder_manual_insertions,
        "image_placeholder_manual_insertions": image_placeholder_manual_insertions,
    }
    logging.info(f"Flashdocs payload: {payload}")
    response = requests.post(url, json=payload, headers=headers)
    return response.json()


def poll_task_until_done(task_id: str, max_attempts: int = 60, delay: int = 5):
    """
    Poll a task until it is done.
    Get the completed deck link and metadata given a task ID. Usage:

        Use the POST /v3/generate/deck/task endpoint to generate a task.
        Poll this GET /v3/generate/deck/task/{task_id} endpoint to check the status of the task.
        Responses:

        200 OK : If the generated deck is completed. It provides the response including a link to the deck.
        202 Accepted : The deck is still generating. Body is empty. Poll again in ~5 seconds.
        404 Not Found : The task id is not found or not available for your account.
        422 Unprocessable Entity : There is some error with the request.
    """

    url = f"https://api.flashdocs.ai/v3/generate/deck/task/{task_id}"
    headers = {"Authorization": f"Bearer {FLASHDOCS_API_KEY}"}

    for _ in range(max_attempts):
        response = requests.request("GET", url, headers=headers, timeout=300)

        if response.status_code == 200:
            return response.json()
        elif response.status_code == 202:
            # Task is still processing, wait before polling again
            time.sleep(delay)
        elif response.status_code == 404:
            raise ValueError(
                f"Task ID {task_id} not found or not available for your account"
            )
        elif response.status_code == 422:
            raise ValueError("Unprocessable request")
        else:
            raise ValueError(f"Unexpected status code: {response.status_code}")

    raise TimeoutError(
        f"Flashdocs task {task_id} did not finish after {max_attempts * delay}s"
    )


def get_slide_metadata(slide_id: str, embedding: bool = False, tree: bool = False):
    """
    Get the metadata for a slide in a Flashdocs presentation.

    Args:
        slide_id: The ID of the slide to get metadata for
        embedding: Whether to include embeddings in the response
        tree: Whether to include tree structure in the response

    Returns:
        dict: Slide metadata including id, format data, placeholders, and other details
    """
    url = f"https://api.flashdocs.ai/v1/slides/{slide_id}"
    headers = {"Authorization": f"Bearer {FLASHDOCS_API_KEY}"}
    params = {
        "embedding": str(embedding).lower(),
        "tree": str(tree).lower(),
    }
    response = requests.get(url, headers=headers, params=params, timeout=300)

    return response.json()


def get_document_config(
    document_id: str,
    include_slides: bool = False,
    include_questions: bool = False,
    include_knowledge_base: bool = False,
    include_embeddings: bool = False,
):
    """
    Get the configuration for a Flashdocs document.

    Args:
        document_id: The ID of the document to get config for
        include_slides: Whether to include slides in the response
        include_questions: Whether to include questions in the response
        include_knowledge_base: Whether to include knowledge base in the response
        include_embeddings: Whether to include embeddings in the response

    Returns:
        dict: Document configuration including id, type, name, description, and other metadata
    """
    url = f"https://api.flashdocs.ai/v1/documents/{document_id}/full"
    params = {
        "include_slides": str(include_slides).lower(),
        "include_questions": str(include_questions).lower(),
        "include_knowledge_base": str(include_knowledge_base).lower(),
        "include_embeddings": str(include_embeddings).lower(),
    }
    headers = {"Authorization": f"Bearer {FLASHDOCS_API_KEY}"}
    response = requests.get(url, headers=headers, params=params, timeout=300)

    return response.json()


def list_document_configurations(
    document_type: FlashdocsDocumentType, format: FlashdocsDocumentFormat
):
    """
    List all document configurations for a given document type and format.
    """
    url = f"https://api.flashdocs.ai/v1/documents/base"
    headers = {"Authorization": f"Bearer {FLASHDOCS_API_KEY}"}
    params = {
        "document_type": document_type.value,
        "format": format.value,
    }
    response = requests.get(url, headers=headers, params=params, timeout=300)

    return response.json()


def get_flashdocs_document_id(google_document_link: str):
    """
    Get the Flashdocs document ID for a given Google document link.

    Args:
        google_document_link: The URL of the Google document

    Returns:
        flashdocs_document_id: The ID of the Flashdocs document if found, otherwise None
    """
    try:
        google_document_id = google_document_link.split("/d/")[1].split("/edit")[0]
    except (IndexError, AttributeError):
        logging.error(f"Invalid Google document link format: {google_document_link}")
        return None
    document_configurations = list_document_configurations(
        FlashdocsDocumentType.TEMPLATE, FlashdocsDocumentFormat.GOOGLE
    )
    for document_configuration in document_configurations:
        if document_configuration.get("google_document_id") == google_document_id:
            return document_configuration.get("id")
    return None
