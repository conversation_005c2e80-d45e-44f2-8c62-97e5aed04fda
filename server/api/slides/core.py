import asyncio
import logging
import os
import time
from typing import Any, Dict

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..gdrive_file_ops import (
    copy_google_drive_file_with_retry,
    copy_permissions_from_template_to_slides,
    upload_cloudfront_pptx_file_to_google_drive,
)
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from .flashdocs import sync_generate_flashdocs_slides
from .parser import parse_output_as_slides_manual_insertions
from .template import get_repurpose_template_flashdocs_document_id


class SlidesDeliverableGenerator(DataWrapperHolder, BaseTracableClass):
    def __init__(self, gen_env: Any):
        self.gen_env = gen_env
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        BaseTracableClass.__init__(self)

    def get_metadata(self):
        return {
            "content_id": (self.content_instance.id if self.content_instance else None),
            "campaign_id": (
                self.campaign_instance.id if self.campaign_instance else None
            ),
            "content_type": self.content_type,
            "campaign_goal": self.campaign_goal,
            "environment": os.environ.get("TOFU_ENV", default="unknown"),
            "foundation_model": self.foundation_model,
        }

    @dynamic_traceable(name="flashdocs_slides_generation")
    def update_slides_variations(
        self, variation_meta: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update slides variations with generated content.

        Args:
            gen_env: Generation environment object
            variation_meta: Dictionary containing variation metadata

        Returns:
            Updated variation metadata with generated slides URLs
        """
        flashdocs_document_id = None
        if self._data_wrapper.should_use_slides_template:
            repurpose_template_content_source_copy = self.content_group_params.get(
                "repurpose_template_content_source_copy", None
            )
            flashdocs_document_id = get_repurpose_template_flashdocs_document_id(
                repurpose_template_content_source_copy
            )

        async def process_all_variations():
            tasks = []
            for variation in variation_meta["variations"]:
                task = self._process_single_variation_async(
                    variation, flashdocs_document_id
                )
                tasks.append(task)
            return await asyncio.gather(*tasks)

        try:
            # Try to get existing event loop first
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                # If no event loop exists or it's closed, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                should_close_loop = True
            else:
                should_close_loop = False

            try:
                # Run all tasks concurrently
                processed_variations = loop.run_until_complete(process_all_variations())
                variation_meta["variations"] = processed_variations
            finally:
                if should_close_loop:
                    loop.close()

        except Exception as e:
            logging.exception(f"Error in async slides variation processing: {e}")
            # Fall back to sequential processing if async fails
            for idx, variation in enumerate(variation_meta["variations"]):
                try:
                    processed_variation = self._process_single_variation(
                        variation, flashdocs_document_id
                    )
                    variation_meta["variations"][idx] = processed_variation
                except Exception as e:
                    logging.exception(f"Failed to process variation {idx}: {e}")

        if "google_slides_url" in variation_meta["variations"][0]:
            variation_meta["current_version"]["google_slides_url"] = variation_meta[
                "variations"
            ][0]["google_slides_url"]
        return variation_meta

    async def _process_single_variation_async(
        self,
        variation: Dict[str, Any],
        flashdocs_document_id: str = None,
    ) -> Dict[str, Any]:
        """
        Process a single variation asynchronously.

        Args:
            gen_env: Generation environment object
            variation: Single variation dictionary
            flashdocs_document_id: Optional document ID for template-based generation

        Returns:
            Updated variation dictionary with google_slides_url
        """
        # Run the blocking _process_single_variation in a thread pool
        loop = asyncio.get_running_loop()
        variation = await loop.run_in_executor(
            None,
            lambda: self._process_single_variation(variation, flashdocs_document_id),
        )
        return variation

    def _process_single_variation(
        self,
        variation: Dict[str, Any],
        flashdocs_document_id: str = None,
    ) -> Dict[str, Any]:
        """
        Process a single variation synchronously.

        Args:
            gen_env: Generation environment object
            variation: Single variation dictionary
            flashdocs_document_id: Optional document ID for template-based generation

        Returns:
            Updated variation dictionary with google_slides_url
        """
        if variation.get("google_slides_url"):
            return variation

        variation_text = variation["text"]
        if self._data_wrapper.should_use_slides_template:
            outline = parse_output_as_slides_manual_insertions(
                variation_text, flashdocs_document_id
            )
            logging.info(f"outline: {outline}")
            flashdocs_params = {
                "prompt": "",
                "source_document_id": flashdocs_document_id,
                "outline": outline,
            }
        else:
            flashdocs_params = {"prompt": variation_text}

        if (
            not self._data_wrapper.should_use_slides_template
            or self._data_wrapper.uses_google_slides_template
        ):
            flashdocs_params["export_options"] = {
                "google_slides_permission_options": {
                    "anyone_edit_with_link": False,
                    "anyone_view_with_link": False,
                    "google_document_editors": [
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                },
                "output_format": "google_slides_direct_link",
            }
        flashdocs_response = sync_generate_flashdocs_slides(**flashdocs_params)
        logging.info(f"flashdocs_response: {flashdocs_response}")

        link_to_deck = flashdocs_response.get("link_to_deck")
        if not link_to_deck:
            raise ValueError(f"Failed to generate link to deck: {flashdocs_response}")

        if "cloudfront.net/tofuhq.com/creations" in link_to_deck:
            google_slides_url = upload_cloudfront_pptx_file_to_google_drive(
                link_to_deck
            )
        else:
            google_slides_url = link_to_deck

        logging.info(f"google_slides_url: {google_slides_url}")
        if not google_slides_url:
            raise ValueError(
                f"Failed to generate google slides url: {flashdocs_response}"
            )

        # Make a copy owned by Tofu with retry logic
        tofu_owned_copy_url = copy_google_drive_file_with_retry(
            google_slides_url, name="Generated Slides"
        )
        logging.info(f"Created Tofu-owned copy: {tofu_owned_copy_url}")

        # If using a slides template, get the original template URL and copy its permissions
        if self._data_wrapper.should_use_slides_template:
            original_template_url = self.content_group_params.get(
                "repurpose_template_content_source_copy", None
            )
            if original_template_url:
                logging.info(
                    f"Copying permissions from template: {original_template_url}"
                )
                # The copy_google_drive_file function already copies permissions from the source
                # But we need to copy permissions specifically from the original template to our Tofu copy
                try:
                    copy_permissions_from_template_to_slides(
                        original_template_url, tofu_owned_copy_url
                    )
                except Exception as e:
                    logging.warning(
                        f"Failed to copy permissions from template {original_template_url} to {tofu_owned_copy_url}: {e}"
                    )

        variation["google_slides_url"] = tofu_owned_copy_url
        return variation
