import asyncio
import hashlib
import json
import logging
from typing import Any, Dict, List, Optional, Tuple

from django.core.cache import cache
from pptx import Presentation

from ..gdrive_utils import (
    copy_google_drive_file,
    get_gslides_text_contents,
    reset_google_slides_text_box_fonts,
    resize_google_slides_text_boxes,
    set_google_drive_permissions_flashdocs,
)
from ..utils import (
    TempS3File,
    is_google_slides_url,
    is_pptx_s3_file,
    is_s3_url,
    parse_s3_presigned_url,
)
from .flashdocs import (
    get_document_config,
    upload_google_slides_to_flashdocs,
    upload_s3_ppt_to_flashdocs,
)


def get_pptx_text_contents(pptx_url: str) -> List[Dict[str, Any]]:
    """
    Get text contents from a PPTX file.
    Returns a list of dicts, each containing:
        - slide_index: zero-based index of the slide
        - text: text content of the slide, ordered by reading order

    Args:
        pptx_url: URL of the PPTX file

    Returns:
        list[dict]: List of dictionaries containing slide index and text content
    """
    if not is_s3_url(pptx_url):
        raise ValueError(f"Invalid PPTX URL: {pptx_url}")

    cache_key = f"pptx_text_contents_{pptx_url}"
    cached_result = cache.get(cache_key)
    if cached_result:
        return cached_result

    try:
        source_file_name, _, s3_bucket = parse_s3_presigned_url(pptx_url)

        with TempS3File(s3_bucket, source_file_name) as local_path:
            # Extract text from the PPTX file
            presentation = Presentation(local_path)
            results = []

            for slide_idx, slide in enumerate(presentation.slides):
                # Store text elements with their positions (approximate)
                text_elements_with_pos = []

                for shape in slide.shapes:
                    if not shape.has_text_frame:
                        continue

                    # Get approximate position for ordering
                    # Use shape's top and left properties for positioning
                    y_pos = (
                        shape.top
                        if hasattr(shape, "top") and shape.top is not None
                        else 0
                    )
                    x_pos = (
                        shape.left
                        if hasattr(shape, "left") and shape.left is not None
                        else 0
                    )

                    # Extract text from the text frame
                    shape_text = []
                    for paragraph in shape.text_frame.paragraphs:
                        paragraph_text = []
                        for run in paragraph.runs:
                            if run.text.strip():
                                paragraph_text.append(run.text)
                        if paragraph_text:
                            # Join runs in the paragraph
                            para_content = "".join(paragraph_text)
                            if para_content.strip():
                                shape_text.append(para_content)

                    if shape_text:
                        # Join paragraphs with newlines
                        text_content = "\n".join(shape_text)
                        text_elements_with_pos.append(
                            {"y_pos": y_pos, "x_pos": x_pos, "text": text_content}
                        )

                # Sort text elements by y-position first, then x-position (reading order)
                text_elements_with_pos.sort(key=lambda x: (x["y_pos"], x["x_pos"]))

                # Join all text elements in order
                text = "\n".join(
                    elem["text"] for elem in text_elements_with_pos
                ).strip()
                if text:  # Only add slides that have text content
                    results.append({"slide_index": slide_idx, "text": text})

            # Cache for 1 minute
            cache.set(cache_key, results, 60)
            return results

    except Exception as e:
        logging.error(f"Error extracting text from PPTX file: {str(e)}")
        raise


def get_placeholder_mapping(template_url: str) -> List[Dict[str, Any]]:
    """
    Get placeholder mappings from a slides template (PPTX or Google Slides).

    Args:
        template_url: URL of the template (S3 presigned URL for PPTX or Google Slides URL)

    Returns:
        List of dictionaries containing slide placeholder mappings

    Raises:
        ValueError: If document configuration cannot be retrieved
    """
    flashdocs_document_id = get_repurpose_template_flashdocs_document_id(template_url)

    doc_config = get_document_config(
        document_id=flashdocs_document_id, include_slides=True
    )
    if not _is_valid_doc_config(doc_config):
        logging.error(f"Invalid response from get_document_config: {doc_config}")
        raise ValueError("Failed to retrieve document configuration")

    # logging.info(f"Doc config: {doc_config}")
    placeholder_mappings = _extract_placeholder_mappings(doc_config.get("slides", []))
    logging.info(f"Placeholder mappings: {placeholder_mappings}")
    return placeholder_mappings


def get_repurpose_template_flashdocs_document_id(
    repurpose_template_content_source_copy: str,
) -> str:
    """
    Get the FlashDocs document ID for a repurpose template.

    Args:
        repurpose_template_content_source_copy: The content source copy of the repurpose template

    Returns:
        FlashDocs document ID

    Raises:
        ValueError: If no template provided or upload fails
    """

    template_source_hash = hashlib.sha256(
        repurpose_template_content_source_copy.encode("utf-8")
    ).hexdigest()
    cache_key = f"flashdocs_document_id_{template_source_hash}"
    cached_document_id = cache.get(cache_key)
    if cached_document_id:
        logging.info(f"Using cached document id: {cached_document_id}")
        return cached_document_id

    if is_google_slides_url(repurpose_template_content_source_copy):
        flashdocs_document_id = _get_google_slides_flashdocs_document_id(
            repurpose_template_content_source_copy
        )
    else:
        flashdocs_document_id = _get_pptx_flashdocs_document_id(
            repurpose_template_content_source_copy
        )

    cache.set(cache_key, flashdocs_document_id, timeout=60 * 60 * 24 * 5)
    return flashdocs_document_id


def _get_pptx_flashdocs_document_id(template_url: str) -> str:
    """Handle uploading a PPTX template."""
    source_file_name, file_type, s3_bucket = parse_s3_presigned_url(template_url)
    if (
        file_type
        != "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    ):
        raise ValueError(f"Invalid template type: {file_type}")

    flashdocs_response = upload_s3_ppt_to_flashdocs(s3_bucket, source_file_name)
    logging.info(f"Flashdocs response: {flashdocs_response}")

    document_id = flashdocs_response.get("document_id")
    if not document_id:
        raise ValueError(f"Failed to upload pptx to FlashDocs: {flashdocs_response}")

    return document_id


def _is_valid_doc_config(doc_config: Optional[Dict[str, Any]]) -> bool:
    """Validate document configuration."""
    if not doc_config or not isinstance(doc_config, dict):
        return False
    if "slides" not in doc_config or not doc_config["slides"]:
        return False
    return True


def _get_google_slides_flashdocs_document_id(google_slides_url: str) -> str:
    """
    Upload new Google Slides document and return document ID.
    Cache the document ID for 5 days.
    """
    new_google_slides_url = copy_google_drive_file(google_slides_url)
    logging.info(f"New google slides url: {new_google_slides_url}")
    if not new_google_slides_url:
        raise ValueError("Failed to copy google slides")
    if not set_google_drive_permissions_flashdocs(new_google_slides_url):
        raise ValueError("Unable to set Google Drive permissions to public")

    # check if the slides already contains placeholders, if so set reset_text_boxes to False
    reset_text_boxes = True
    gslides_text_contents = get_gslides_text_contents(new_google_slides_url)
    if contains_placeholders(gslides_text_contents):
        reset_text_boxes = False
    else:
        # First reset the text box fonts.
        reset_google_slides_text_box_fonts(new_google_slides_url)
        # Then resize any text boxes to fit the text.
        resize_google_slides_text_boxes(new_google_slides_url)
    flashdocs_response = upload_google_slides_to_flashdocs(
        new_google_slides_url, reset_text_boxes=reset_text_boxes
    )
    flashdocs_document_id = flashdocs_response.get("document_id")
    if not flashdocs_document_id:
        raise ValueError(
            f"Failed to upload google slides to FlashDocs: {flashdocs_response}"
        )
    return flashdocs_document_id


def contains_placeholders(slides_text_contents: List[Dict[str, Any]]) -> bool:
    """Check if the slides already contains placeholders."""
    for slide in slides_text_contents:
        slide_text_contents = slide.get("text", "")
        # Check for [...] or {{...}} in the text contents
        if "[" in slide_text_contents and "]" in slide_text_contents:
            return True
        if "{{" in slide_text_contents and "}}" in slide_text_contents:
            return True
    return False


def _extract_placeholder_mappings(slides: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Extract placeholder mappings from slides configuration."""
    slides_placeholder_map = []
    for slide in slides:
        placeholder_map = []
        text_placeholders = slide.get("text_placeholders", [])
        for text_placeholder in text_placeholders:
            original_text = text_placeholder.get("original_text")
            # Convert \r to \n for proper newline handling
            if original_text:
                original_text = original_text.replace("\r", "\n")

            placeholder_map.append(
                {
                    "placeholder": text_placeholder.get("placeholder"),
                    "original_text": original_text,
                }
            )
        slides_placeholder_map.append(
            {
                "slide_number": f"slide {slide.get('index') + 1}",
                "placeholder_map": placeholder_map,
            }
        )
    return slides_placeholder_map


def validate_slides_template_source(
    source_url: str,
) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Validate if a source URL is a valid slides template (PPTX or Google Slides).

    Args:
        source_url: The URL or path to validate

    Returns:
        Tuple containing:
        - bool: Whether the source is valid
        - str: The type of template ('pptx' or 'google_slides', None if invalid)
        - str: Error message if invalid, None if valid
    """
    if not source_url:
        return False, None, "No template source provided"

    if is_s3_url(source_url):
        try:
            source_file_name, file_type, s3_bucket = parse_s3_presigned_url(source_url)
            if not is_pptx_s3_file(source_file_name):
                return False, None, f"Not a PowerPoint file: {source_file_name}"
            return True, "pptx", None
        except Exception as e:
            return False, None, f"Invalid S3 URL: {str(e)}"
    elif is_google_slides_url(source_url):
        return True, "google_slides", None
    else:
        return False, None, f"Not a valid slide template source: {source_url}"


def get_slides_template_content(template_content_source_copy):
    # cache the results for 5 days.
    source_hash = hashlib.sha256(template_content_source_copy.encode()).hexdigest()
    cache_key = f"slides_template_content_{source_hash}"
    cached_result = cache.get(cache_key)
    if cached_result:
        return cached_result
    try:
        is_valid, template_type, error_msg = validate_slides_template_source(
            template_content_source_copy
        )
        if not is_valid:
            raise ValueError(error_msg)

        if template_type == "pptx":
            source_file_name, file_type, s3_bucket = parse_s3_presigned_url(
                template_content_source_copy
            )
            try:
                pptx_text_contents = _get_s3_pptx_slides_text(
                    s3_bucket, source_file_name
                )
                if contains_placeholders(pptx_text_contents):
                    text = json.dumps(pptx_text_contents)
                else:
                    slides_placeholder_mapping = get_placeholder_mapping(
                        template_content_source_copy
                    )
                    text = json.dumps(slides_placeholder_mapping)
                # Convert \r to \n in text contents
                text = text.replace("\r", "\n")
            except Exception as exc:
                raise ValueError(
                    f"Failed to extract text from PowerPoint file: {str(exc)}"
                ) from exc
        else:  # google_slides
            # placeholder case.
            try:
                gslides_text_contents = get_gslides_text_contents(
                    template_content_source_copy
                )
                if contains_placeholders(gslides_text_contents):
                    text = json.dumps(gslides_text_contents)
                else:
                    text = json.dumps(
                        get_placeholder_mapping(template_content_source_copy)
                    )
            except Exception as exc:
                raise ValueError(
                    f"Failed to extract text from Google Slides: {str(exc)}"
                ) from exc

        cache.set(cache_key, {"text": text}, 60 * 60 * 24 * 5)
        return {"text": text}
    except ValueError as e:
        # Re-raise ValueError with the same message
        raise
    except Exception as exc:
        # Catch any other unexpected errors
        raise Exception(
            f"Unexpected error processing slides template: {str(exc)}"
        ) from exc


def _get_s3_pptx_slides_text(s3_bucket: str, s3_filename: str) -> List[Dict[str, Any]]:
    """
    Extract text content from a PPTX file stored in S3.

    Args:
        s3_bucket: The S3 bucket name
        s3_filename: The filename in S3

    Returns:
        List of dictionaries containing slide text and formatting

    Raises:
        ValueError: If file is not a PowerPoint file or cannot be read
    """
    if not is_pptx_s3_file(s3_filename):
        raise ValueError("File is not a PowerPoint file")

    def extract_text(prs: Presentation) -> List[Dict[str, Any]]:
        """Extract text and formatting from each slide."""
        slides_data = []

        for idx, slide in enumerate(prs.slides):
            slide_lines = []
            for shape in slide.shapes:
                if not shape.has_text_frame:
                    continue

                for paragraph in shape.text_frame.paragraphs:
                    line_parts = []
                    for run in paragraph.runs:
                        text = run.text.strip()
                        if not text:
                            continue
                        # Wrap with simple Markdown‑like marks for bold/italic
                        if run.font.bold:
                            text = f"**{text}**"
                        if run.font.italic:
                            text = f"*{text}*"
                        line_parts.append(text)

                    if line_parts:
                        indent = "  " * paragraph.level  # preserve bullet depth
                        slide_lines.append(indent + " ".join(line_parts))

            slides_data.append(
                {
                    "slide_index": idx,
                    "text": "\n".join(slide_lines),
                }
            )

        return slides_data

    try:
        with TempS3File(s3_bucket, s3_filename) as local_path:
            presentation = Presentation(local_path)
            return extract_text(presentation)
    except Exception as exc:
        logging.exception("Failed to extract PPTX text from S3: %s", exc)
        raise ValueError("Unable to read PowerPoint file from S3") from exc
