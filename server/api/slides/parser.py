import json
import logging
import re
from typing import Any, Dict, List

from ..utils import strip_for_json
from .flashdocs import Outline, get_document_config


def parse_output_as_slides_manual_insertions(
    text: str, flashdocs_document_id: str
) -> List[Dict[str, Any]]:
    """
    Parse text as a list of slides with manual insertions.
    Each slide is wrapped in tags like <slide_1>...</slide_1>.

    Args:
        text: String containing text with slide tags
        flashdocs_document_id: ID of the FlashDocs document

    Returns:
        List of dictionaries containing slide content and instructions

    Raises:
        ValueError: If parsing fails or document configuration is invalid
    """
    # Get document configuration
    doc_config = get_document_config(
        document_id=flashdocs_document_id, include_slides=True
    )
    if not _is_valid_doc_config(doc_config):
        logging.error(f"Invalid response from get_document_config: {doc_config}")
        raise ValueError("Failed to retrieve document configuration")

    slides = doc_config.get("slides", [])

    slides_output = _parse_slide_tags(text, slides)
    logging.info(f"Parsed slides output: {slides_output}")

    if len(slides) != len(slides_output):
        raise ValueError(
            f"Slide count mismatch: template expects {len(slides)}, "
            f"but parser found {len(slides_output)}"
        )

    return _create_slide_outline(slides, slides_output)


def _parse_slide_tags(
    text: str, original_slides: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    Parse slide tags from text and extract content.

    Args:
        text: Input text containing slide tags
        original_slides: List of original slide configurations

    Returns:
        List of parsed slide data with same length as original_slides
    """
    # Initialize output with same length as original slides
    slides_output = [{} for _ in original_slides]

    slide_pattern = r"<slide_(\d+)>(.*?)</slide_(\d+)>"
    matches = re.finditer(slide_pattern, text, re.DOTALL)

    for match in matches:
        slide_number = int(match.group(1))
        slide_content = match.group(2).strip()
        slide_index = slide_number - 1

        if slide_index < 0 or slide_index >= len(slides_output):
            logging.warning(f"Slide number {slide_number} is out of range")
            continue

        if not slide_content:
            slides_output[slide_index] = {"content_instruction": "DNE"}
        else:
            logging.info(
                f"Parsing slide {slide_number} content as JSON: {slide_content}"
            )
            try:
                parsed_json = json.loads(strip_for_json(slide_content))
                slides_output[slide_index] = {"json": parsed_json}
            except Exception as exc:
                logging.error(f"Failed to parse JSON for slide #{slide_number}: {exc}")
                slides_output[slide_index] = {"content_instruction": "DNE"}

    return slides_output


def _is_valid_doc_config(doc_config: Dict[str, Any]) -> bool:
    """
    Validate document configuration.

    Args:
        doc_config: Document configuration dictionary

    Returns:
        True if configuration is valid, False otherwise
    """
    if not doc_config or not isinstance(doc_config, dict):
        return False
    if "slides" not in doc_config or not doc_config["slides"]:
        return False
    return True


def _create_slide_outline(
    slides: List[Dict[str, Any]], slides_output: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    Create slide outline with placeholder insertions.

    Args:
        slides: Original slide configurations
        slides_output: Parsed slide output data

    Returns:
        List of slide outline dictionaries
    """
    outline = []
    for idx, slide in enumerate(slides):
        slide_id = slide.get("id")
        text_placeholders = slide.get("text_placeholders", [])
        json_data = slides_output[idx].get("json", {})

        text_placeholder_manual_insertions = get_text_placeholder_manual_insertions(
            text_placeholders, json_data
        )

        outline.append(
            Outline(
                slide_id=slide_id,
                text_placeholder_manual_insertions=text_placeholder_manual_insertions,
            ).to_dict()
        )
    return outline


def get_text_placeholder_manual_insertions(
    text_placeholders: List[Dict[str, Any]], json_mapping: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Get manual insertions for text placeholders.

    Args:
        text_placeholders: List of text placeholders
        json_mapping: JSON mapping for placeholder values

    Returns:
        List of dictionaries containing placeholder insertions

    Raises:
        ValueError: If entry format is invalid
    """
    # Handle empty placeholders case
    if not text_placeholders:
        return []

    # Handle single placeholder case
    if len(text_placeholders) == 1:
        value = _extract_single_placeholder_value(json_mapping)
        return [
            {
                "placeholder": text_placeholders[0].get("placeholder"),
                "value": value,
            }
        ]

    # Handle multiple placeholders
    logging.info(f"Processing multiple placeholders with JSON mapping: {json_mapping}")
    text_placeholder_manual_insertions = _prepare_multiple_placeholder_insertions(
        json_mapping
    )

    # Validate and normalize entries
    _validate_and_normalize_entries(
        text_placeholders, text_placeholder_manual_insertions
    )

    # Add empty values for missing placeholders
    _add_missing_placeholders(text_placeholders, text_placeholder_manual_insertions)

    # remove extra placeholders
    # for any placeholder that is not in the text_placeholders, remove it from the text_placeholder_manual_insertions
    text_placeholder_manual_insertions = _remove_extra_placeholders(
        text_placeholders, text_placeholder_manual_insertions
    )

    return text_placeholder_manual_insertions


def _remove_extra_placeholders(
    text_placeholders: List[Dict[str, Any]],
    text_placeholder_manual_insertions: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """Remove extra placeholders from the text_placeholder_manual_insertions."""
    all_text_placeholders = {
        placeholder["placeholder"] for placeholder in text_placeholders
    }
    text_placeholder_manual_insertions = [
        insertion
        for insertion in text_placeholder_manual_insertions
        if insertion["placeholder"] in all_text_placeholders
    ]
    return text_placeholder_manual_insertions


def _extract_single_placeholder_value(json_mapping: Dict[str, Any]) -> str:
    """Extract value for single placeholder from JSON mapping."""
    if isinstance(json_mapping, list):
        if not json_mapping:
            return ""
        elif len(json_mapping) > 1:
            logging.error(
                f"Single placeholder slide contains multiple values: {json_mapping}"
            )
            return json_mapping[0].get("value", "")
        else:
            return json_mapping[0].get("value", "")
    else:
        value = json_mapping.get("value")
        if value is None:
            logging.error(
                f"Single placeholder slide missing 'value' key: {json_mapping}"
            )
            return ""
        return value


def _prepare_multiple_placeholder_insertions(
    json_mapping: Dict[str, Any],
) -> List[Dict[str, Any]]:
    """Prepare insertions list for multiple placeholders."""
    if isinstance(json_mapping, list):
        return json_mapping
    else:
        return [json_mapping]


def _validate_and_normalize_entries(
    text_placeholders: List[Dict[str, Any]], entries: List[Dict[str, Any]]
) -> None:
    """Validate and normalize placeholder entries."""
    # Collect indices to remove (in reverse order to avoid index shifting)
    indices_to_remove = []

    for i, entry in enumerate(entries):
        if "placeholder" not in entry or "value" not in entry:
            # Handle case where "value" might be "original_text"
            if "placeholder" in entry and "original_text" in entry:
                entry["value"] = entry["original_text"]
                del entry["original_text"]
            else:
                # Mark for removal
                indices_to_remove.append(i)

    # Remove invalid entries in reverse order to avoid index shifting
    for i in reversed(indices_to_remove):
        entries.pop(i)

    # Convert placeholder formats from {} to {{}} if needed
    _convert_placeholder_formats(text_placeholders, entries)


def _is_well_formed_placeholder(placeholder: str) -> bool:
    """
    Validate that a placeholder is well-formed with properly paired and nested braces.

    Checks for:
    - Basic format (starts/ends with correct braces)
    - Proper brace pairing and nesting
    - No unmatched braces

    Args:
        placeholder: The placeholder string to validate

    Returns:
        True if placeholder is well-formed, False otherwise
    """
    if not placeholder or not isinstance(placeholder, str):
        return False

    # Determine expected format and validate basic structure
    is_double_brace = placeholder.startswith("{{") and placeholder.endswith("}}")
    is_single_brace = placeholder.startswith("{") and placeholder.endswith("}")

    if is_double_brace:
        if len(placeholder) <= 4:  # More than just {{}}
            return False
        inner_content = placeholder[2:-2]  # Remove outer {{}}
    elif is_single_brace:
        if len(placeholder) <= 2:  # More than just {}
            return False
        inner_content = placeholder[1:-1]  # Remove outer {}
    else:
        return False

    # Validate that all braces within the inner content are properly paired and nested
    return _validate_brace_pairing(inner_content)


def _validate_brace_pairing(content: str) -> bool:
    """
    Validate that braces within content are properly paired and nested.

    Args:
        content: The inner content to validate (without outer placeholder braces)

    Returns:
        True if all braces are properly paired and nested, False otherwise
    """
    stack = []
    i = 0

    while i < len(content):
        char = content[i]

        if char == "{":
            # Check for double brace opening
            if i + 1 < len(content) and content[i + 1] == "{":
                stack.append("{{")
                i += 2  # Skip next character
            else:
                stack.append("{")
                i += 1
        elif char == "}":
            # Check for double brace closing
            if i + 1 < len(content) and content[i + 1] == "}":
                # Need to match with {{
                if not stack or stack[-1] != "{{":
                    return False
                stack.pop()
                i += 2  # Skip next character
            else:
                # Need to match with {
                if not stack or stack[-1] != "{":
                    return False
                stack.pop()
                i += 1
        else:
            i += 1

    # All braces should be matched
    return len(stack) == 0


def _convert_placeholder_formats(
    text_placeholders: List[Dict[str, Any]], entries: List[Dict[str, Any]]
) -> None:
    """Convert placeholder formats from {} to {{}} if text_placeholders use {{}}."""
    # Check if text_placeholders use "{{}}" format
    has_double_braces = any(
        placeholder["placeholder"].startswith("{{") for placeholder in text_placeholders
    )

    if not has_double_braces:
        return

    # Convert entries from {} to {{}} format if needed
    for entry in entries:
        placeholder_value = entry["placeholder"]

        # Validate placeholder before conversion
        if not _is_well_formed_placeholder(placeholder_value):
            logging.warning(f"Malformed placeholder detected: '{placeholder_value}'")
            continue

        # Convert from {} to {{}} format if needed
        needs_conversion = (
            placeholder_value.startswith("{")
            and not placeholder_value.startswith("{{")
            and placeholder_value.endswith("}")
            and not placeholder_value.endswith("}}")
        )

        if needs_conversion:
            # Remove outer braces and add double braces
            inner_content = placeholder_value[1:-1]
            entry["placeholder"] = "{{" + inner_content + "}}"
            # logging.info(
            #     f"Converted placeholder format from '{placeholder_value}' "
            #     f"to '{entry['placeholder']}'"
            # )


def _add_missing_placeholders(
    text_placeholders: List[Dict[str, Any]], insertions: List[Dict[str, Any]]
) -> None:
    """Add empty values for missing placeholders."""
    insertion_placeholders = {entry["placeholder"] for entry in insertions}

    for placeholder in text_placeholders:
        placeholder_name = placeholder["placeholder"]
        if placeholder_name not in insertion_placeholders:
            insertions.append({"placeholder": placeholder_name, "value": ""})
