import json
import logging
from typing import Dict, List

from langchain_community.llms.sagemaker_endpoint import (
    LLMContentHandler,
    SagemakerEndpoint,
)
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage


def convert_messages_to_prompt(messages: List[List[BaseMessage]]) -> List[str]:
    json_messages = []
    for message_list in messages:  # List[BaseMessage]
        json_message_list = []
        for message in message_list:  # BaseMessage
            if isinstance(message, SystemMessage):
                json_message_list.append({"role": "system", "content": message.content})
            elif isinstance(message, HumanMessage):
                json_message_list.append({"role": "user", "content": message.content})
            elif isinstance(message, AIMessage):
                json_message_list.append(
                    {"role": "assistant", "content": message.content}
                )
            else:
                raise ValueError(f"Unknown message type {type(message)}")
        json_messages.append(json.dumps(json_message_list))
    return json_messages


class SageMakerContentHandler(LLMContentHandler):
    content_type = "application/json"
    accepts = "application/json"

    def transform_input(self, prompt: str, model_kwargs: dict) -> bytes:
        input_str = json.dumps(
            {
                "inputs": [json.loads(prompt)],
                "parameters": {**model_kwargs},
            },
        )
        return input_str.encode("utf-8")

    def transform_output(self, output: bytes) -> str:
        response_json = json.loads(output.read().decode("utf-8"))
        return response_json[0]["generation"]["content"]


def get_llama_llm(model_name, model_kwargs):
    handler = SageMakerContentHandler()
    endpoint_name_map = {
        "llama-2-7b-chat": "jumpstart-dft-meta-textgeneration-llama-2-7b-f",
        "llama-2-13b-chat": "jumpstart-dft-meta-textgeneration-llama-2-13b-f",
        "llama-2-70b-chat": "jumpstart-dft-meta-textgeneration-llama-2-70b-f",
    }
    if model_name not in endpoint_name_map:
        raise ValueError(f"Unknown model name {model_name}")
    endpoint_name = endpoint_name_map[model_name]
    region_name = "us-west-2"
    se = SagemakerEndpoint(
        endpoint_name=endpoint_name,
        region_name=region_name,
        content_handler=handler,
        model_kwargs=model_kwargs,
        endpoint_kwargs={
            "CustomAttributes": "accept_eula=true",
        },
    )
    return se
