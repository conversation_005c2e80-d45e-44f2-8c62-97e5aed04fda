# Type definitions shared between frontend and backend
# --- VERY IMPORTANT ---
# If you change anything here, you MUST also change the corresponding file in the frontend
# frontend/tofu/utils/sharedTypes.ts
# --- VERY IMPORTANT ---


from enum import Enum, auto


class ContentType(str, Enum):
    LandingPage = "Landing Page"
    EmailMarketing = "Email - Marketing"
    EmailSDR = "Email - SDR"
    Whitepaper = "Whitepaper"
    Brochure = "Brochure"
    CaseStudy = "Case Study"
    EBook = "eBook"
    AdCampaignGeneral = "Ad Campaign - General"
    AdCampaignLinkedin = "Ad Campaign - LinkedIn"
    AdCampaignLinkedinCarousel = "Ad Campaign - LinkedIn Carousel"
    AdCampaignLinkedinDocument = "Ad Campaign - LinkedIn Document"
    AdCampaignMeta = "Ad Campaign - Meta"
    AdCampaignGoogle = "Ad Campaign - Google"
    SocialGeneral = "Social - General"
    SocialLinkedin = "Social - LinkedIn"
    MessageLinkedin = "Message - LinkedIn"
    SalesDeck = "Sales Deck"
    SlideDeck = "Slide Deck"
    Webinar = "Webinar"
    BlogPost = "Blog Post"
    QuotesHighlights = "Quotes and Highlights"
    Statistics = "Statistics"
    Other = "Other"


class ContentSourceUploadMethod(str, Enum):
    URL = "Link"
    File = "File"
    Text = "Text"


class ContentSourceFormat(str, Enum):
    Html = "Html"
    PDF = "PDF"
    Text = "Text"
    Empty = ""


ContentTypeDetails = {
    ContentType.LandingPage: {
        "name": "landing page",
        "plural_name": "landing pages",
        "label": "Landing Page",
        "default_upload_method": ContentSourceUploadMethod.URL,
        "icon": "ComputerDesktopIcon",
    },
    ContentType.EmailMarketing: {
        "name": "marketing email",
        "plural_name": "marketing emails",
        "label": "Email - Marketing",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "EnvelopeIcon",
    },
    ContentType.EmailSDR: {
        "name": "SDR email",
        "plural_name": "SDR emails",
        "label": "Email - SDR",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "EnvelopeIcon",
    },
    ContentType.Whitepaper: {
        "name": "whitepaper",
        "plural_name": "whitepapers",
        "label": "Whitepaper",
        "default_upload_method": ContentSourceUploadMethod.File,
        "icon": "DocumentTextIcon",
    },
    ContentType.Brochure: {
        "name": "brochure",
        "plural_name": "brochures",
        "label": "Brochure",
        "default_upload_method": ContentSourceUploadMethod.File,
        "icon": "DocumentTextIcon",
    },
    ContentType.CaseStudy: {
        "name": "case study",
        "plural_name": "case studies",
        "label": "Case Study",
        "default_upload_method": ContentSourceUploadMethod.File,
        "icon": "DocumentTextIcon",
    },
    ContentType.EBook: {
        "name": "eBook",
        "plural_name": "eBooks",
        "label": "E-book",
        "default_upload_method": ContentSourceUploadMethod.File,
        "icon": "DocumentTextIcon",
    },
    ContentType.AdCampaignGeneral: {
        "name": "ad campaign",
        "plural_name": "ad campaigns",
        "label": "Ad Campaign - General",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "DocumentTextIcon",
    },
    ContentType.AdCampaignLinkedin: {
        "name": "linkedin ad campaign",
        "plural_name": "linkedin ad campaigns",
        "label": "Ad Campaign - LinkedIn",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "LinkedinIcon",
    },
    ContentType.AdCampaignMeta: {
        "name": "meta ad campaign",
        "plural_name": "meta ad campaigns",
        "label": "Ad Campaign - Meta",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "DocumentTextIcon",
    },
    ContentType.AdCampaignGoogle: {
        "name": "google ad campaign",
        "plural_name": "google ad campaigns",
        "label": "Ad Campaign - Google",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "DocumentTextIcon",
    },
    ContentType.AdCampaignLinkedinCarousel: {
        "name": "linkedin carousel ad campaign",
        "plural_name": "linkedin carousel ad campaigns",
        "label": "Ad Campaign - LinkedIn Carousel",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "LinkedinIcon",
    },
    ContentType.AdCampaignLinkedinDocument: {
        "name": "linkedin document ad campaign",
        "plural_name": "linkedin document ad campaigns",
        "label": "Ad Campaign - LinkedIn Document",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "LinkedinIcon",
    },
    ContentType.SocialGeneral: {
        "name": "social post",
        "plural_name": "social posts",
        "label": "Social - General",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "ChatBubbleOvalLeftIcon",
    },
    ContentType.SocialLinkedin: {
        "name": "linkedin post",
        "plural_name": "linkedin posts",
        "label": "Social - LinkedIn",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "DocumentTextIcon",
    },
    ContentType.MessageLinkedin: {
        "name": "linkedin direct message",
        "plural_name": "linkedin direct messages",
        "label": "Message - LinkedIn",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "DocumentTextIcon",
    },
    ContentType.SalesDeck: {
        "name": "sales deck",
        "plural_name": "sales decks",
        "label": "Sales Deck",
        "default_upload_method": ContentSourceUploadMethod.File,
        "icon": "DocumentTextIcon",
    },
    ContentType.SlideDeck: {
        "name": "slide deck",
        "plural_name": "slide decks",
        "label": "Slide Deck",
        "default_upload_method": ContentSourceUploadMethod.File,
        "icon": "DocumentTextIcon",
    },
    ContentType.Webinar: {
        "name": "webinar",
        "plural_name": "webinars",
        "label": "Webinar",
        "default_upload_method": ContentSourceUploadMethod.URL,
        "icon": "DocumentTextIcon",
    },
    ContentType.BlogPost: {
        "name": "blog post",
        "plural_name": "blog posts",
        "label": "Blog Post",
        "default_upload_method": ContentSourceUploadMethod.URL,
        "icon": "DocumentTextIcon",
    },
    ContentType.QuotesHighlights: {
        "name": "list of quotations and highlights",
        "plural_name": "lists of quotations and highlights",
        "label": "Quotes and Highlights",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "DocumentTextIcon",
    },
    ContentType.Statistics: {
        "name": "list of statistics",
        "plural_name": "lists of statistics",
        "label": "Statistics",
        "default_upload_method": ContentSourceUploadMethod.Text,
        "icon": "DocumentTextIcon",
    },
    ContentType.Other: {
        "name": "content",
        "plural_name": "contents",
        "label": "Other",
        "default_upload_method": ContentSourceUploadMethod.URL,
        "icon": "DocumentTextIcon",
    },
}


class SalesForceEmailPlatform(str, Enum):
    Outreach = "Outreach"
    Salesloft = "Salesloft"
    Pardot = "Pardot"


SALESFORCE_FIELD_SUFFIX = "__c"


class ConnectedAssetsSourceType(str, Enum):
    Url = "url"
