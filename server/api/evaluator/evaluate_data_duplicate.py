import logging
import re

from django.core.exceptions import ObjectDoesNotExist
from django.forms.models import model_to_dict

from ..actions.action_copier import ActionCopier
from ..actions.action_data_wrapper import ActionDataWrapper
from ..actions.legacy_converter.legacy_custom_instruction_converter import (
    AssetsV3ToV2Converter,
)
from ..campaign import CampaignHandler
from ..content_group import ContentGroupHandler
from ..feature.data_wrapper.data_wrapper import CampaignWrapper
from ..models import (
    Action,
    AssetInfo,
    AssetInfoGroup,
    Campaign,
    CompanyInfo,
    Content,
    ContentGroup,
    Playbook,
    TargetInfo,
    TargetInfoGroup,
    TofuUser,
)
from ..playbook_build.object_builder import ObjectBuilder
from ..serializers import PlaybookSerializer
from ..thread_locals import set_current_user


class TofuUserDuplicator:
    @staticmethod
    def duplicate_tofu_user(orig_tofu_user, campaign, copied_playbook) -> None:
        new_tofu_user_name = TofuUserDuplicator.get_duplicate_tofu_user_name(
            orig_tofu_user.id, campaign.id
        )
        tofu_user_check = TofuUser.objects.filter(username=new_tofu_user_name)
        if tofu_user_check.exists():
            return tofu_user_check.first()

        # do the copy
        copied_tofu_user = TofuUserDuplicator.copy_tofu_user(
            orig_tofu_user, new_tofu_user_name, copied_playbook
        )
        return copied_tofu_user

    @staticmethod
    def get_duplicate_tofu_user_name(user_id: int, campaign_id: int) -> str:
        return f"tofuadmin-eval-copy_{user_id}_{campaign_id}"

    @staticmethod
    def copy_tofu_user(orig_tofu_user, new_user_name, copied_playbook):
        # Convert model instance to dictionary
        orig_data = model_to_dict(
            orig_tofu_user,
            exclude=[
                "id",
                "username",
                "date_joined",
                "last_login",
                "groups",
                "user_permissions",
                "password",
            ],
        )

        # Create a new instance of the model
        copied_tofu_user = TofuUser.objects.create_user(
            username=new_user_name,
            password="eval",
            playbook=copied_playbook,
            **orig_data,
        )
        copied_tofu_user.set_password("eval")
        copied_tofu_user.save()

        copied_tofu_user.groups.set(orig_tofu_user.groups.all())
        copied_tofu_user.user_permissions.set(orig_tofu_user.user_permissions.all())

        logging.info(f"TofuUser {copied_tofu_user.id} is copied for eval")
        return copied_tofu_user


class PlaybookDuplicator:
    @staticmethod
    def duplicate_playbook(orig_playbook, campaign) -> None:
        new_playbook_name = PlaybookDuplicator.get_duplicate_playbook_name(
            orig_playbook.id, campaign.id
        )
        playbook_check = Playbook.objects.filter(name=new_playbook_name)
        if playbook_check.exists():
            return playbook_check.first()

        orig_tofu_user = orig_playbook.users.first()
        if not orig_tofu_user:
            raise ObjectDoesNotExist(
                f"TofuUser not found for Playbook {orig_playbook.id}"
            )

        # do the copy
        copied_playbook = PlaybookDuplicator.copy_playbook(
            orig_playbook,
            new_playbook_name,
        )

        copied_tofu_user = TofuUserDuplicator.duplicate_tofu_user(
            orig_tofu_user, campaign, copied_playbook
        )
        if not copied_tofu_user:
            raise Exception(
                f"Failed to duplicate tofu user for TofuUser {orig_tofu_user.id}"
            )

        copied_playbook.users.add(copied_tofu_user)
        return copied_playbook

    @staticmethod
    def get_duplicate_playbook_name(playbook_id: int, campaign_id: int) -> str:
        return f"tofuadmin-eval-copy_{playbook_id}_{campaign_id}"

    @staticmethod
    def copy_playbook(orig_playbook, new_playbook_name):
        # Convert model instance to dictionary
        orig_data = model_to_dict(
            orig_playbook,
            exclude=[
                "id",
                "users",
                "name",
                "created_at",
                "updated_at",
                # "company_info",
                # "company_info_expanded",
                "target_info",
                "target_info_expanded",
                # "assets",
                # "assets_expanded",
                "company_object",
                "company_domain",
            ],
        )
        orig_data["name"] = new_playbook_name
        orig_data["target_info"] = {}
        orig_data["target_info_expanded"] = {}

        # copy company_object
        company_object = orig_playbook.company_object
        company_object_data = model_to_dict(
            company_object,
            exclude=["id", "created_at", "updated_at"],
        )
        new_company_object = CompanyInfo.objects.create(**company_object_data)
        orig_data["company_object"] = new_company_object

        # Create a new instance of the model
        copied_playbook = Playbook.objects.create(**orig_data)

        return copied_playbook

    @staticmethod
    def post_copy_playbook_assets(copied_playbook, orig_playbook, assets_params):
        logging.info(f"Playbook {copied_playbook.id} is copied for eval")

        # copy assets
        assets_keys = list(assets_params.keys())
        asset_info_groups = AssetInfoGroup.objects.filter(
            playbook=orig_playbook
        ).filter(asset_info_group_key__in=assets_keys)

        for asset_info_group in asset_info_groups:
            asset_info_group_data = model_to_dict(
                asset_info_group,
                exclude=["id", "created_at", "updated_at", "playbook"],
            )
            new_asset_info_group = AssetInfoGroup.objects.create(
                playbook=copied_playbook, **asset_info_group_data
            )
            logging.info(f"new_asset_info_group: {new_asset_info_group}")
            assets_key_values = assets_params.get(
                asset_info_group.asset_info_group_key, []
            )
            assets = AssetInfo.objects.filter(asset_info_group=asset_info_group).filter(
                asset_key__in=assets_key_values
            )
            for asset in assets:
                asset_data = model_to_dict(
                    asset,
                    exclude=["id", "created_at", "updated_at", "asset_info_group"],
                )
                new_asset = AssetInfo.objects.create(
                    asset_info_group=new_asset_info_group, **asset_data
                )
                logging.info(f"new_asset: {new_asset}")

                object_builder = ObjectBuilder.get_builder(new_asset)
                object_builder.build_docs(rebuild=True)

        serializer = PlaybookSerializer(copied_playbook)
        serializer = PlaybookSerializer(
            copied_playbook, data=serializer.data, partial=True
        )
        if serializer.is_valid():
            serializer.save()

    @staticmethod
    def add_target_to_playbook(playbook, targets):
        new_playbook_name = playbook.name
        # Define the regular expression pattern
        pattern = r"tofuadmin-eval-copy_(\d+)_(\d+)"

        # Search for the pattern in the string
        match = re.search(pattern, new_playbook_name)
        if not match:
            raise ValueError(f"Invalid eval playbook name {new_playbook_name}")
        orig_playbook_id = match.group(1)
        orig_playbook = Playbook.objects.filter(id=orig_playbook_id)
        if not orig_playbook.exists():
            raise ObjectDoesNotExist(
                f"Original playbook {orig_playbook_id} not found for {playbook.id}"
            )
        orig_playbook = orig_playbook.first()

        # targets is a dict like {"Med Device and Pharma": "Johnson & Johnson"}
        for l1_key, l2_key in targets.items():
            orig_target_info_group = TargetInfoGroup.objects.filter(
                playbook=orig_playbook, target_info_group_key=l1_key
            )
            if not orig_target_info_group.exists():
                raise ObjectDoesNotExist(
                    f"TargetInfoGroup {l1_key} not found for playbook {orig_playbook_id}"
                )
            orig_target_info_group = orig_target_info_group.first()

            target_info_group = TargetInfoGroup.objects.filter(
                playbook=playbook, target_info_group_key=l1_key
            )
            if not target_info_group.exists():
                target_info_group_data = model_to_dict(
                    orig_target_info_group,
                    exclude=["id", "created_at", "updated_at", "playbook"],
                )
                target_info_group_data["playbook"] = playbook
                target_info_group_data["meta"].pop("importListSettings", None)
                target_info_group = TargetInfoGroup.objects.create(
                    **target_info_group_data
                )
            else:
                target_info_group = target_info_group.first()

            target_info = TargetInfo.objects.filter(
                target_info_group=target_info_group, target_key=l2_key
            )
            if not target_info.exists():
                orig_target_info = TargetInfo.objects.filter(
                    target_info_group=orig_target_info_group,
                    target_key=l2_key,
                )
                if not orig_target_info.exists():
                    raise ObjectDoesNotExist(
                        f"TargetInfo {l2_key} not found for TargetInfoGroup {l1_key} in playbook {orig_playbook_id}"
                    )
                orig_target_info = orig_target_info.first()

                target_info_data = model_to_dict(
                    orig_target_info,
                    exclude=["id", "created_at", "updated_at"],
                )
                target_info_data["target_info_group"] = target_info_group
                target_info_data["meta"].pop("synced_from", None)
                target_info_data["meta"].pop("marketo", None)
                target_info_data["meta"].pop("hubspot", None)
                target_info_data["meta"].pop("salesforce", None)
                target_info_data["meta"].pop("hubspot_record_id", None)
                target_info_data["meta"].pop("hubspot_object_type", None)
                target_info_data["meta"].pop("hubspot_object_identifier", None)

                new_target = TargetInfo.objects.create(**target_info_data)

                object_builder = ObjectBuilder.get_builder(new_target)
                object_builder.build_docs(rebuild=True)

        # Serialize the object
        serializer = PlaybookSerializer(playbook)
        serializer = PlaybookSerializer(playbook, data=serializer.data, partial=True)
        if serializer.is_valid():
            serializer.save()


class CampaignDuplicator:
    @staticmethod
    def duplicate_campaign(campaign) -> None:
        campaign_check = Campaign.objects.filter(
            campaign_name=CampaignDuplicator.get_duplicate_campaign_name(campaign.id)
        )
        if campaign_check.exists():
            return campaign_check.first()

        orig_playbook = campaign.playbook

        copied_playbook = PlaybookDuplicator.duplicate_playbook(orig_playbook, campaign)
        if not copied_playbook:
            raise Exception(
                f"Failed to duplicate playbook for Playbook {orig_playbook.id}"
            )

        # do the copy
        copied_campaign = CampaignDuplicator.copy_campaign(
            campaign,
            CampaignDuplicator.get_duplicate_campaign_name(campaign.id),
            copied_playbook,
        )
        # check if the campaign has assets
        assets_params = campaign.campaign_params.get("assets", {})
        # copy the assets
        PlaybookDuplicator.post_copy_playbook_assets(
            copied_playbook=copied_playbook,
            orig_playbook=orig_playbook,
            assets_params=assets_params,
        )
        # check for assets in the actions of the campaign and duplicate their assets as well.
        campaign_actions = Action.objects.filter(campaign=campaign)
        v3_assets_params = {}
        for action in campaign_actions:
            action_data_wrapper = ActionDataWrapper(action)
            inputs_dict = action_data_wrapper.get_all_inputs()
            if "anchor_content" in inputs_dict:
                assets = action_data_wrapper.get_input_by_name("anchor_content")
                assets_v3_to_v2_converter = AssetsV3ToV2Converter(assets)
                assets_v2 = assets_v3_to_v2_converter.convert_assets_v3_to_v2()
                for asset_group_key, asset_keys in assets_v2.items():
                    if asset_group_key not in v3_assets_params:
                        v3_assets_params[asset_group_key] = []
                    for asset_key in asset_keys:
                        if asset_key not in v3_assets_params[asset_group_key]:
                            v3_assets_params[asset_group_key].append(asset_key)
        logging.info(f"v3_assets_params: {v3_assets_params}")
        PlaybookDuplicator.post_copy_playbook_assets(
            copied_playbook=copied_playbook,
            orig_playbook=orig_playbook,
            assets_params=v3_assets_params,
        )
        return copied_campaign

    @staticmethod
    def get_duplicate_campaign_name(campaign_id: int) -> str:
        return f"tofuadmin-eval-copy_{campaign_id}"

    @staticmethod
    def copy_campaign(campaign, new_campaign_name, copied_playbook):
        # Convert model instance to dictionary
        orig_data = model_to_dict(
            campaign,
            exclude=[
                "id",
                "campaign_name",
                "created_at",
                "updated_at",
                "playbook",
                "creator",
            ],
        )

        # Update the necessary fields
        orig_data["creator"] = copied_playbook.users.first()
        orig_data["playbook"] = copied_playbook
        orig_data["campaign_name"] = new_campaign_name
        orig_data["campaign_status"]["gen_status"] = {}
        orig_data["campaign_params"]["targets"] = []  # would add later from content
        orig_data["campaign_params"]["orig_campaign_id"] = campaign.id
        orig_data["campaign_params"].pop("enable_auto_sync", None)

        # Create a new instance of the model
        new_campaign = Campaign.objects.create(**orig_data)
        logging.info(f"Campaign {new_campaign.id} is copied for eval")

        return new_campaign

    @staticmethod
    def add_target_to_campaign(campaign, targets):
        has_updates = False

        if not campaign.campaign_params["targets"]:
            campaign.campaign_params["targets"] = [{}]
            has_updates = True

        for l1_key, l2_key in targets.items():
            if l1_key not in campaign.campaign_params["targets"][0]:
                campaign.campaign_params["targets"][0][l1_key] = []

            if l2_key not in campaign.campaign_params["targets"][0][l1_key]:
                campaign.campaign_params["targets"][0][l1_key].append(l2_key)
                has_updates = True
        if has_updates:
            campaign.save()


class ContentGroupDuplicator:
    @staticmethod
    def duplicate_content_group(content_group) -> None:
        new_content_group_name = (
            ContentGroupDuplicator.get_duplicate_content_group_name(content_group.id)
        )
        content_group_check = ContentGroup.objects.filter(
            content_group_name=new_content_group_name
        )
        if content_group_check.exists():
            return content_group_check.first()

        copied_campaign = CampaignDuplicator.duplicate_campaign(content_group.campaign)
        if not copied_campaign:
            raise Exception(
                f"Failed to duplicate campaign for Campaign {content_group.campaign.id}"
            )

        # do the copy
        copied_content_group = ContentGroupDuplicator.copy_content_group(
            content_group,
            new_content_group_name,
            copied_campaign,
        )
        return copied_content_group

    @staticmethod
    def get_duplicate_content_group_name(content_group_id: int) -> str:
        return f"tofuadmin-eval-copy_{content_group_id}"

    @staticmethod
    def copy_content_group(
        content_group,
        new_content_group_name,
        new_campaign,
    ):
        # Convert model instance to dictionary
        orig_data = model_to_dict(
            content_group,
            exclude=[
                "id",
                "created_at",
                "updated_at",
                "creator",
                "campaign",
                "actions",
            ],
        )

        # Update the necessary fields
        orig_data["creator"] = new_campaign.creator
        orig_data["campaign"] = new_campaign
        orig_data["content_group_name"] = new_content_group_name
        orig_data["content_group_status"] = {}
        orig_data["content_group_params"].pop("export_response", None)
        orig_data["content_group_params"].pop("export_settings", None)
        orig_data["content_group_params"].pop("reviewed_content_list", None)
        orig_data["content_group_params"]["orig_content_group_id"] = content_group.id

        # Create a new instance of the model
        copied_content_group = ContentGroup.objects.create(**orig_data)

        return copied_content_group


class ContentDuplicator:
    @staticmethod
    def duplicate_content(content) -> None:
        copied_content_group = ContentGroupDuplicator.duplicate_content_group(
            content.content_group
        )
        if not copied_content_group:
            raise Exception(
                f"Failed to duplicate content group for Content {content.id}"
            )

        set_current_user(copied_content_group.creator)

        # copy content
        # update for targets
        targets = content.content_params.get("targets", {})
        if targets:
            copied_campaign = copied_content_group.campaign

            # copy target to playbook
            copied_playbook = copied_campaign.playbook
            # add target to playbook
            PlaybookDuplicator.add_target_to_playbook(copied_playbook, targets)

            # add target to campaign
            CampaignDuplicator.add_target_to_campaign(copied_campaign, targets)

        # call content_group's bulk_create
        content_group_handler = ContentGroupHandler(copied_content_group)
        copied_content = content_group_handler.bulk_create_content(targets=[targets])
        if not copied_content:
            raise Exception(f"Failed to duplicate content for Content {content.id}")
        copied_content = copied_content[0]
        copied_content.content_params["orig_content_id"] = content.id
        copied_content.save()

        logging.info(f"Content {copied_content.id} is copied for eval")
        return copied_content


def copy_content_for_eval(content):
    copied_content = ContentDuplicator.duplicate_content(content)
    return copied_content


def copy_content_collection_for_eval(campaign, collection_id):
    if not collection_id:
        return
    copied_campaign = CampaignDuplicator.duplicate_campaign(campaign)
    collection_content_groups = ContentGroup.objects.filter(
        campaign=campaign,
        content_group_params__content_collection__id=collection_id,
    )
    if not collection_content_groups.exists():
        raise ValueError(f"Content groups not found for collection {collection_id}")
    else:
        for content_group in collection_content_groups:
            contents = Content.objects.filter(content_group=content_group)
            for content in contents:
                copy_content_for_eval(content)

    CampaignHandler(copied_campaign).update_content_collection_params()
    return copied_campaign


class ActionDuplicator:
    @staticmethod
    def duplicate_action(action: Action):
        copied_campaign = CampaignDuplicator.duplicate_campaign(action.campaign)
        if not copied_campaign:
            raise Exception(
                f"Failed to duplicate campaign for Campaign {action.campaign.id}"
            )
        action_copier = ActionCopier()
        action_copier.copy_actions_for_campaign(
            action.campaign,
            copied_campaign,
            copy_components=True,
            actions_to_copy=[action.id],
        )
        return copied_campaign
