from unittest.mock import Mock, patch

import pytest
from api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck import (
    AnchorContentPrecheckEvaluator,
)
from api.evaluator.evaluators.pregen_evaluators.asset_eval_builder import (
    DocumentValidator,
)


@pytest.fixture
def anchor_content_precheck():
    return AnchorContentPrecheckEvaluator()


@pytest.fixture
def mock_data_wrapper():
    data_wrapper = Mock()
    data_wrapper.aggregated_asset_params = [
        {
            "meta": "repurpose_anchor_content",
            "assets": [{"group1": "asset1", "group2": "asset2"}],
        }
    ]
    data_wrapper.playbook_instance.id = 1
    return data_wrapper


@pytest.fixture
def mock_asset_info_fixture():
    """Create mock AssetInfo objects for testing"""

    def create_mock_asset(asset_id, status=None, additional_info=None):
        asset = Mock()
        asset.id = asset_id
        asset.docs_build_status = {"anchor_precheck_status": status} if status else {}
        asset.additional_info = additional_info or {}
        return asset

    return create_mock_asset


@pytest.fixture
def mock_asset_group():
    """Create mock AssetInfoGroup objects for testing"""

    def create_mock_group(group_key):
        group = Mock()
        group.asset_info_group_key = group_key
        return group

    return create_mock_group


@pytest.fixture
def mock_asset_for_validation():
    """Create a mock AssetInfo object for DocumentValidator testing"""
    asset = Mock()
    asset.id = 1
    asset.docs = {"doc1": {"type": "text", "value": "test content"}}
    asset.docs_build_status = {"docs": {"doc1": {"status": "success"}}}
    return asset


class TestDocumentValidator:
    """Test cases for DocumentValidator class"""

    def test_validate_regular_document_sufficient_content(
        self, mock_asset_for_validation
    ):
        """Test validation passes when document has sufficient content (>= 10 words)"""
        validator = DocumentValidator(mock_asset_for_validation)

        # Mock extracted document with sufficient content
        mock_doc = Mock()
        mock_doc.page_content = (
            "This is a test document with more than ten words to pass validation"
        )

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.return_value = [mock_doc]

            result = validator._validate_regular_document("text", "test content")

            assert result == {"label": "PASS", "comment": None}
            mock_doc_loader.extract_single_doc_from_loader.assert_called_once_with(
                "text", "test content", fast_return=True
            )

    def test_validate_regular_document_insufficient_content(
        self, mock_asset_for_validation
    ):
        """Test validation fails when document has insufficient content (< 10 words)"""
        validator = DocumentValidator(mock_asset_for_validation)

        # Mock extracted document with insufficient content (only 5 words)
        mock_doc = Mock()
        mock_doc.page_content = "Only five words here"

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.return_value = [mock_doc]

            result = validator._validate_regular_document("text", "test content")

            assert result["label"] == "FAIL"
            assert "Insufficient content (less than 10 words)" in result["comment"]

    def test_validate_regular_document_multiple_pages_sufficient_content(
        self, mock_asset_for_validation
    ):
        """Test validation passes when multiple pages combined have sufficient content"""
        validator = DocumentValidator(mock_asset_for_validation)

        # Mock extracted document with multiple pages that together have >= 10 words
        mock_doc1 = Mock()
        mock_doc1.page_content = "First page with some"  # 4 words
        mock_doc2 = Mock()
        mock_doc2.page_content = (
            "Second page with more content here"  # 6 words, total 10
        )

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.return_value = [
                mock_doc1,
                mock_doc2,
            ]

            result = validator._validate_regular_document("text", "test content")

            assert result == {"label": "PASS", "comment": None}

    def test_validate_regular_document_multiple_pages_insufficient_content(
        self, mock_asset_for_validation
    ):
        """Test validation fails when multiple pages combined have insufficient content"""
        validator = DocumentValidator(mock_asset_for_validation)

        # Mock extracted document with multiple pages that together have < 10 words
        mock_doc1 = Mock()
        mock_doc1.page_content = "First page"  # 2 words
        mock_doc2 = Mock()
        mock_doc2.page_content = "Second page content"  # 3 words, total 5

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.return_value = [
                mock_doc1,
                mock_doc2,
            ]

            result = validator._validate_regular_document("text", "test content")

            assert result["label"] == "FAIL"
            assert "Insufficient content (less than 10 words)" in result["comment"]

    def test_validate_regular_document_extraction_failure(
        self, mock_asset_for_validation
    ):
        """Test validation fails when document extraction fails"""
        validator = DocumentValidator(mock_asset_for_validation)

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.return_value = None

            result = validator._validate_regular_document("text", "test content")

            assert result["label"] == "FAIL"
            assert "Could not extract content" in result["comment"]

    def test_validate_regular_document_google_drive_permission_error(
        self, mock_asset_for_validation
    ):
        """Test validation fails with specific message for Google Drive permission errors"""
        validator = DocumentValidator(mock_asset_for_validation)

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.side_effect = Exception(
                "The caller does not have permission"
            )

            # Mock try_parse_google_url to return a Google URL
            with patch(
                "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.try_parse_google_url"
            ) as mock_parse:
                mock_parse.return_value = "https://drive.google.com/file/d/123/view"

                result = validator._validate_regular_document(
                    "url", "https://drive.google.com/file/d/123/view"
                )

                assert result["label"] == "FAIL"
                assert "don't have access to your file" in result["comment"]

    def test_validate_regular_document_empty_content(self, mock_asset_for_validation):
        """Test validation fails when document has empty content"""
        validator = DocumentValidator(mock_asset_for_validation)

        # Mock extracted document with empty content
        mock_doc = Mock()
        mock_doc.page_content = ""

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.return_value = [mock_doc]

            result = validator._validate_regular_document("text", "test content")

            assert result["label"] == "FAIL"
            assert "Insufficient content (less than 10 words)" in result["comment"]

    def test_validate_regular_document_edge_case_exactly_ten_words(
        self, mock_asset_for_validation
    ):
        """Test validation passes when document has exactly 10 words"""
        validator = DocumentValidator(mock_asset_for_validation)

        # Mock extracted document with exactly 10 words
        mock_doc = Mock()
        mock_doc.page_content = (
            "This document has exactly ten words in its content right here"
        )

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.ObjectBuilder"
        ) as mock_builder:
            mock_doc_loader = Mock()
            mock_builder.get_builder.return_value.doc_loader = mock_doc_loader
            mock_doc_loader.extract_single_doc_from_loader.return_value = [mock_doc]

            result = validator._validate_regular_document("text", "test content")

            assert result == {"label": "PASS", "comment": None}


@patch("api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetInfo")
@patch(
    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
)
def test_evaluate(
    mock_asset_eval_builder, mock_asset_info, anchor_content_precheck, mock_data_wrapper
):
    # Mock AssetInfo objects with proper additional_info structure
    mock_asset_1 = Mock(asset_key="asset1")
    mock_asset_1.additional_info = {}  # Proper dict instead of Mock
    mock_asset_1.docs = {"doc1": {"type": "text", "value": "content"}}
    mock_asset_1.docs_last_build = {"doc1": {"type": "text", "value": "content"}}

    # Mock AssetInfo.objects.filter().first()
    mock_asset_info.objects.filter.return_value.first.side_effect = [
        mock_asset_1,  # Existing asset
        None,  # Non-existing asset
    ]

    # Mock AssetEvalBuilder and its check_asset_info method
    mock_builder_instance = Mock()
    mock_asset_eval_builder.return_value = mock_builder_instance
    mock_builder_instance.check_asset_info.return_value = {
        "label": "PASS",
        "comment": "Asset is valid",
    }

    result = anchor_content_precheck.evaluate(mock_data_wrapper)

    assert len(result) == 2
    assert result["asset1"] == {"label": "PASS", "comment": "Asset is valid"}
    assert result["asset2"] == {
        "label": "FAIL",
        "comment": "Your anchor content asset2 was either renamed or deleted. Please delete and re-add the anchor content.",
    }

    # Verify method calls
    mock_asset_info.objects.filter.assert_called()
    mock_builder_instance.check_asset_info.assert_called_once()


@pytest.mark.parametrize(
    "llm_output,expected_result",
    [
        ('{"label": "PASS", "comment": null}', {"label": "PASS", "comment": None}),
        (
            '{"label": "FAIL", "comment": "Content too short"}',
            {"label": "FAIL", "comment": "Content too short"},
        ),
        ('{"label": "INVALID"}', {"label": "PASS", "comment": None}),
        ('{"label": "FAIL"}', {"label": "FAIL", "comment": None}),
        ("Invalid JSON", {"label": "PASS", "comment": None}),
    ],
)
def test_check(anchor_content_precheck, llm_output, expected_result):
    with patch.object(anchor_content_precheck, "model_caller") as mock_model_caller:
        mock_model_caller.get_results_with_fallback.return_value = [
            Mock(text=llm_output)
        ]
        mock_model_caller.model_name = "us.anthropic.claude-3-5-sonnet-20240620"

        result = anchor_content_precheck.check("Sample anchor content")

        assert result == expected_result
        mock_model_caller.get_results_with_fallback.assert_called_once()


@patch(
    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
)
def test_check_asset_info(mock_asset_eval_builder, anchor_content_precheck):
    # Mock AssetInfo object
    mock_asset = Mock()
    mock_asset.docs = {"doc1": {"type": "text", "value": "content"}}
    mock_asset.docs_last_build = {"doc1": {"type": "text", "value": "content"}}
    mock_asset.docs_build_status = {"docs": {"doc1": {"status": "success"}}}
    mock_asset.additional_info = {}

    # Mock AssetEvalBuilder instance and its check_asset_info method
    mock_builder_instance = Mock()
    mock_asset_eval_builder.return_value = mock_builder_instance

    # Test with a successful result
    mock_builder_instance.check_asset_info.return_value = {
        "label": "PASS",
        "comment": None,
    }

    result = anchor_content_precheck.check_asset_info(mock_asset)

    # Should return the result from AssetEvalBuilder.check_asset_info()
    assert result == {"label": "PASS", "comment": None}
    mock_asset_eval_builder.assert_called_once_with(
        mock_asset, llm_evaluator_callback=anchor_content_precheck._llm_check_content
    )
    mock_builder_instance.check_asset_info.assert_called_once()

    # Test with a failed result
    mock_builder_instance.check_asset_info.return_value = {
        "label": "FAIL",
        "comment": "Content too short",
    }

    result = anchor_content_precheck.check_asset_info(mock_asset)

    assert result == {"label": "FAIL", "comment": "Content too short"}


@patch(
    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetCollector"
)
def test_get_eval_status(
    mock_asset_collector,
    anchor_content_precheck,
    mock_asset_info_fixture,
):
    """Test get_eval_status method with various scenarios"""

    # Test case 1: Assets are running
    mock_assets = [
        mock_asset_info_fixture(1, "RUNNING"),
        mock_asset_info_fixture(2, "DONE"),
    ]
    mock_asset_collector.collect_all_asset_ids.return_value = [1, 2]

    # Mock AssetInfo.objects.filter to return our mock assets
    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetInfo"
    ) as mock_asset_info:
        mock_asset_info.objects.filter.return_value = mock_assets

        # Mock the _has_running_assets method
        anchor_content_precheck._has_running_assets = Mock(return_value=True)

        result = anchor_content_precheck.get_eval_status([1, 2])
        assert result == "RUNNING"

    # Test case 2: All assets are done
    mock_assets = [
        mock_asset_info_fixture(1, "DONE"),
        mock_asset_info_fixture(2, "DONE"),
    ]

    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetInfo"
    ) as mock_asset_info:
        mock_asset_info.objects.filter.return_value = mock_assets

        anchor_content_precheck._has_running_assets = Mock(return_value=False)
        anchor_content_precheck._all_assets_done = Mock(return_value=True)

        result = anchor_content_precheck.get_eval_status([1, 2])
        assert result == "DONE"

    # Test case 3: Assets are ready (not started)
    mock_assets = [
        mock_asset_info_fixture(1, None),
        mock_asset_info_fixture(2, None),
    ]

    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetInfo"
    ) as mock_asset_info:
        mock_asset_info.objects.filter.return_value = mock_assets

        anchor_content_precheck._has_running_assets = Mock(return_value=False)
        anchor_content_precheck._all_assets_done = Mock(return_value=False)

        result = anchor_content_precheck.get_eval_status([1, 2])
        assert result == "READY"


@patch(
    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetCollector"
)
def test_get_eval_results(
    mock_asset_collector,
    anchor_content_precheck,
    mock_asset_info_fixture,
):
    """Test get_eval_results method"""

    # Mock assets with precheck results
    asset1 = mock_asset_info_fixture(
        1,
        additional_info={"anchor_content_precheck": {"label": "PASS", "comment": None}},
    )
    asset2 = mock_asset_info_fixture(
        2,
        additional_info={
            "anchor_content_precheck": {"label": "FAIL", "comment": "Too short"}
        },
    )

    mock_asset_collector.collect_all_asset_ids.return_value = [1, 2]
    mock_asset_collector.get_asset_map.return_value = {1: asset1, 2: asset2}

    # Mock AssetEvalBuilder
    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
    ) as mock_builder:
        mock_builder_instance = Mock()
        mock_builder.return_value = mock_builder_instance
        mock_builder_instance.get_cached_precheck_result.side_effect = [
            {"label": "PASS", "comment": None},
            {"label": "FAIL", "comment": "Too short"},
        ]

        result = anchor_content_precheck.get_eval_results([1, 2])

        expected = {
            1: {"label": "PASS", "comment": None},
            2: {"label": "FAIL", "comment": "Too short"},
        }
        assert result == expected

    # Test with missing precheck result
    asset3 = mock_asset_info_fixture(3, additional_info={})
    mock_asset_collector.get_asset_map.return_value = {3: asset3}
    mock_asset_collector.collect_all_asset_ids.return_value = [3]

    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
    ) as mock_builder:
        mock_builder_instance = Mock()
        mock_builder.return_value = mock_builder_instance
        mock_builder_instance.get_cached_precheck_result.return_value = None

        result = anchor_content_precheck.get_eval_results([3])
        assert result == {}


@patch(
    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetCollector"
)
def test_set_assets_anchor_precheck_status(
    mock_asset_collector,
    anchor_content_precheck,
    mock_asset_info_fixture,
):
    """Test set_assets_anchor_precheck_status method"""

    # Mock assets
    asset1 = mock_asset_info_fixture(1)
    asset2 = mock_asset_info_fixture(2)

    mock_asset_collector.collect_all_asset_ids.return_value = [1, 2]
    mock_asset_collector.get_asset_map.return_value = {1: asset1, 2: asset2}

    # Mock AssetEvalBuilder
    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
    ) as mock_builder:
        mock_builder_instances = [Mock(), Mock()]
        mock_builder.side_effect = mock_builder_instances

        # Test setting status to QUEUED
        anchor_content_precheck.set_assets_anchor_precheck_status(
            [1, 2], status="QUEUED"
        )

        # Verify AssetEvalBuilder was created for each asset
        assert mock_builder.call_count == 2
        mock_builder.assert_any_call(asset1)
        mock_builder.assert_any_call(asset2)

        # Verify set_anchor_precheck_status was called for each builder
        for builder_instance in mock_builder_instances:
            builder_instance.set_anchor_precheck_status.assert_called_once_with(
                "QUEUED"
            )


@patch(
    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetCollector"
)
def test_evaluate_asset_ids(
    mock_asset_collector,
    anchor_content_precheck,
    mock_asset_info_fixture,
):
    """Test evaluate_asset_ids method"""

    # Mock assets with proper docs structure
    asset1 = mock_asset_info_fixture(1)
    asset1.docs = {"doc1": {"type": "text", "value": "content"}}
    asset2 = mock_asset_info_fixture(2)
    asset2.docs = {"doc2": {"type": "text", "value": "content"}}

    mock_asset_collector.collect_all_asset_ids.return_value = [1, 2]
    mock_asset_collector.get_asset_map.return_value = {1: asset1, 2: asset2}

    # Mock AssetEvalBuilder
    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
    ) as mock_builder:
        mock_builder_instances = [
            Mock(),
            Mock(),
            Mock(),
            Mock(),
        ]  # 2 for status setting, 2 for evaluation
        mock_builder.side_effect = mock_builder_instances

        # Mock the check_asset_info method on the builder instances
        mock_builder_instances[2].check_asset_info.return_value = {
            "label": "PASS",
            "comment": None,
        }
        mock_builder_instances[3].check_asset_info.return_value = {
            "label": "FAIL",
            "comment": "Too short",
        }

        result = anchor_content_precheck.evaluate_asset_ids([1, 2])

        expected = {
            1: {"label": "PASS", "comment": None},
            2: {"label": "FAIL", "comment": "Too short"},
        }
        assert result == expected

        # Verify AssetEvalBuilder was created for each asset (twice - once for status, once for evaluation)
        assert mock_builder.call_count == 4

        # Verify set_anchor_precheck_status was called for each asset
        for i in range(2):  # First two instances are for setting status
            mock_builder_instances[
                i
            ].set_anchor_precheck_status.assert_called_once_with("RUNNING")

        # Verify check_asset_info was called for each evaluation builder instance
        mock_builder_instances[2].check_asset_info.assert_called_once()
        mock_builder_instances[3].check_asset_info.assert_called_once()


def test_is_video_audio_file():
    """Test is_video_audio_file utility function"""

    # Test with video file
    video_data = {"mime_file_type": "video/mp4", "original_filename": "test.mp4"}
    assert DocumentValidator._is_video_audio_file("file", video_data) == True

    # Test with audio file
    audio_data = {"mime_file_type": "audio/mpeg", "original_filename": "test.mp3"}
    assert DocumentValidator._is_video_audio_file("file", audio_data) == True

    # Test with non-video/audio file
    text_data = {"mime_file_type": "text/plain", "original_filename": "test.txt"}
    assert DocumentValidator._is_video_audio_file("file", text_data) == False

    # Test with non-file data type
    assert DocumentValidator._is_video_audio_file("url", video_data) == False

    # Test with non-dict data value
    assert DocumentValidator._is_video_audio_file("file", "not_a_dict") == False

    # Test with missing mime_file_type
    incomplete_data = {"original_filename": "test.mp4"}
    assert DocumentValidator._is_video_audio_file("file", incomplete_data) == False


def test_asset_eval_builder_methods(anchor_content_precheck):
    """Test that AssetEvalBuilder methods work correctly"""

    # Test with asset that has no docs_build_status
    asset = Mock()
    asset.docs_build_status = None

    # Mock AssetCollector methods to avoid database access
    with patch(
        "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetCollector"
    ) as mock_asset_collector:
        mock_asset_collector.collect_all_asset_ids.return_value = [1]
        mock_asset_collector.get_asset_map.return_value = {1: asset}

        # Mock AssetEvalBuilder
        with patch(
            "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
        ) as mock_builder:
            mock_builder_instance = Mock()
            mock_builder.return_value = mock_builder_instance

            # Test setting status through the evaluator's method
            anchor_content_precheck.set_assets_anchor_precheck_status(
                [1], status="RUNNING"
            )

            # Verify the builder was created and method was called
            mock_builder_instance.set_anchor_precheck_status.assert_called_once_with(
                "RUNNING"
            )

        # Test with asset that already has docs_build_status
        asset.docs_build_status = {"other_status": "done"}

        with patch(
            "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AssetEvalBuilder"
        ) as mock_builder:
            mock_builder_instance = Mock()
            mock_builder.return_value = mock_builder_instance

            anchor_content_precheck.set_assets_anchor_precheck_status(
                [1], status="DONE"
            )

            mock_builder_instance.set_anchor_precheck_status.assert_called_once_with(
                "DONE"
            )
