import logging

from ...feature.data_wrapper.data_wrapper import BaseContentWrapper
from ...langsmith_integration import BaseTracableClass, dynamic_traceable
from ...shared_types import ContentType
from .pregen_evaluators import all_predefined_pregen_rule_map
from .pregen_evaluators.pregen_evaluator_types import (
    PregenRule,
    ScoreCategory,
    score_rule_mapping,
)


class ContentGroupScoringEvaluator(BaseTracableClass):
    def __init__(self, content_group, unsaved_data) -> None:
        super().__init__()

        self.content_group = content_group

        self.data_wrapper = BaseContentWrapper.from_data_instance_and_unsaved_data(
            self.content_group,
            unsaved_data=unsaved_data,
        )

    def get_metadata(self):
        playbook = self.data_wrapper.playbook_instance
        user = playbook.get_owner_or_first_user() if playbook else None
        return {
            "campaign_id": self.content_group.campaign.id,
            "content_group_id": self.content_group.id,
            "username": user.username if user else None,
        }

    def get_rules_to_check(self):
        if self.data_wrapper.campaign_goal == "Repurpose Content":
            return [
                PregenRule.ANCHOR_CONTENT_PRECHECK.value,
            ]

        rules = []
        if self.data_wrapper.content_type in (
            ContentType.EmailMarketing,
            ContentType.EmailSDR,
        ):
            rules.extend(
                [
                    PregenRule.EMAIL_PERSONALIZED.value,
                    PregenRule.EMAIL_NO_GREETING_SIGNATURE.value,
                    PregenRule.NO_STAT_SELECTED.value,
                    PregenRule.ASSET_CHECK.value,
                    PregenRule.COMPONENT_CHECK.value,
                ]
            )
        return rules

    def map_rules_to_evaluator(self, rules):
        evaluators = []
        for rule in rules:
            if rule in all_predefined_pregen_rule_map:
                evaluators.append(all_predefined_pregen_rule_map[rule])
            else:
                logging.error(
                    f"Rule {rule} is not found in {all_predefined_pregen_rule_map.keys()}"
                )
        return evaluators

    def map_category(self, results):
        category_results = {k.value: {} for k in ScoreCategory}
        for evaluator_name, result in results.items():
            try:
                category = score_rule_mapping.get(PregenRule.from_str(evaluator_name))
                if category:
                    category_results[category.value][evaluator_name] = result
                else:
                    logging.error(f"Category not found for {evaluator_name}")
            except Exception as e:
                logging.error(f"Error mapping category for {evaluator_name}: {e}")
        return category_results

    @dynamic_traceable(name="content_group_pregen_check")
    def check_pregen_scores(self):
        rules = self.get_rules_to_check()
        if not rules:
            return {}

        evaluators = self.map_rules_to_evaluator(rules)
        results = {}
        for evaluator in evaluators:
            check_results = evaluator.evaluate(data_wrapper=self.data_wrapper)
            if not check_results:
                continue
            if "message" not in check_results:
                if evaluator.name == PregenRule.ANCHOR_CONTENT_PRECHECK.value:
                    failed_cases = [
                        k for k, v in check_results.items() if v.get("label") == "FAIL"
                    ]
                    if failed_cases:
                        check_results["message"] = (
                            f"Anchor content precheck failed: {', '.join(failed_cases)}"
                        )
                    else:
                        check_results["message"] = "Anchor content precheck passed"
                else:
                    if "comment" in check_results:
                        logging.error(
                            f"Message not found in {evaluator.name} result, using comment instead"
                        )
                        check_results["message"] = check_results["comment"]
                    else:
                        logging.error(
                            f"Message not found in {evaluator.name} result: {check_results}"
                        )
            results[evaluator.name] = check_results

        # real scoring
        results = self.map_category(results)
        return results
