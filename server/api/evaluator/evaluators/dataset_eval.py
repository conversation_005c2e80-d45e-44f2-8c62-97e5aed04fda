import copy
import json
import logging

from langchain.schema import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langsmith import Client
from langsmith.evaluation import LangChainStringEvaluator, evaluate
from langsmith.schemas import Example, Run

from .gen_evaluators.evaluator_personalization import LLMEvaluatorPersonalization


class LLMEvaluatorDatasetEvalBase:
    def __init__(self, name, evaluator) -> None:
        self._name = name
        self._evaluator = evaluator

    def predicate(self, example: dict):
        generations = example["generation"]
        features = copy.deepcopy(example)
        del features["generation"]

        result = self._evaluator.evaluate(generations, features)
        return result

    def evaluate(self, run, example) -> dict:
        example_label = example.outputs["label"]
        run_label = run.outputs["label"]

        score = 1.0 if example_label == run_label else 0.0
        return {"key": self._name, "score": score}

    def evaluate_with_dataset(self, experiment_prefix, version):
        experiment_results = evaluate(
            self.predicate,
            data=self._evaluator.dataset_name,
            evaluators=[self.evaluate],
            experiment_prefix=experiment_prefix,
            metadata={"version": version},
        )
        return experiment_results


class LLMEvaluatorDatasetEvalPersonalization(LLMEvaluatorDatasetEvalBase):
    def __init__(self) -> None:
        super().__init__("personalization", LLMEvaluatorPersonalization())

    def evaluate_dataset(self):
        experiment_results = self.evaluate_with_dataset(
            experiment_prefix="test-dataset-personalization",
            version="personalization - init",
        )
        return experiment_results


def evaluate_against_dataset():
    personalization_eval = LLMEvaluatorDatasetEvalPersonalization()
    return personalization_eval.evaluate_dataset()
