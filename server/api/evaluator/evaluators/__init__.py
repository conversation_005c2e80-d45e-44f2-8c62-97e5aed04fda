from .gen_evaluators.evaluator_disallow_words import LLMEvaluatorDisallowWords
from .gen_evaluators.evaluator_following_template import LLMEvaluatorFollowingTemplate
from .gen_evaluators.evaluator_personalization import LLMEvaluatorPersonalization
from .gen_evaluators.evaluator_personalization_info import (
    LLMEvaluatorPersonalizationInfo,
)

all_predefined_rules = [
    LLMEvaluatorPersonalization(),
    LLMEvaluatorPersonalizationInfo(),
    LLMEvaluatorFollowingTemplate(),
    LLMEvaluatorDisallowWords(),
]


class PredefinedLLMEvaluators:
    def __init__(self) -> None:
        self._all_predefined_rules = all_predefined_rules
        self._all_predefined_rules_maps = {
            rule.name: rule for rule in all_predefined_rules
        }

    def get_all_predefined_rules(self):
        return self._all_predefined_rules

    def get_predefined_rule(self, rule_name):
        return self._all_predefined_rules_maps.get(rule_name, None)

    def get_all_predefined_rule_names(self):
        return [rule.name for rule in all_predefined_rules]


predefined_evaluators = PredefinedLLMEvaluators()
