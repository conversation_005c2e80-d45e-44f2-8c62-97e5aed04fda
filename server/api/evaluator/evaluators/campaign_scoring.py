import logging

from ...feature.data_wrapper.data_wrapper import BaseContentWrapper
from ...langsmith_integration import BaseTracableClass, dynamic_traceable
from .pregen_evaluators import all_predefined_pregen_rule_map
from .pregen_evaluators.pregen_evaluator_types import PregenRule


class CampaignScoringEvaluator(BaseTracableClass):
    def __init__(self, campaign) -> None:
        super().__init__()

        self.campaign = campaign

        self.data_wrapper = BaseContentWrapper.from_data_instance(self.campaign)

    def get_metadata(self):
        playbook = self.data_wrapper.playbook_instance
        user = playbook.get_owner_or_first_user() if playbook else None
        return {
            "campaign_id": self.campaign.id,
            "username": user.username if user else None,
        }

    def get_rules_to_check(self):
        if self.data_wrapper.campaign_goal == "Repurpose Content":
            return [
                PregenRule.ANCHOR_CONTENT_PRECHECK.value,
            ]

        return []

    def map_rules_to_evaluator(self, rules):
        evaluators = []
        for rule in rules:
            if rule in all_predefined_pregen_rule_map:
                evaluators.append(all_predefined_pregen_rule_map[rule])
            else:
                logging.error(
                    f"Rule {rule} is not found in {all_predefined_pregen_rule_map.keys()}"
                )
        return evaluators

    @dynamic_traceable(name="campaign_pregen_check")
    def check_pregen_scores(self):
        rules = self.get_rules_to_check()
        if not rules:
            return {}

        evaluators = self.map_rules_to_evaluator(rules)
        results = {}
        for evaluator in evaluators:
            check_results = evaluator.evaluate(data_wrapper=self.data_wrapper)
            results[evaluator.name] = check_results

        return results
