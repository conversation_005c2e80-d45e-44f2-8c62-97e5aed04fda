import logging

from ..evaluators import predefined_evaluators
from .evaluator_base import LLMGenEvaluatorBase


class LLMEvaluatorCombined(LLMGenEvaluatorBase):
    def __init__(self, rules=None) -> None:
        super().__init__("combined")

        if not rules:
            self.rules = predefined_evaluators.get_all_predefined_rules()
        else:
            self.rules = [
                predefined_evaluators.get_predefined_rule(rule_name)
                for rule_name in rules
            ]

        rule_names = [rule.name for rule in self.rules]

        self.instruction = f"""Here is the instruction you shall evaluate against, this includes any info I provide for each rule:
- Evaluate the generation for every rule considered in one LLM call (do not separate)
- For each rule, please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation.Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "FAIL", "comment": "The generation does include the personalization aspect but does not follow the template well"
- For example, if we only consider two rules such as "personalization" and "following-template", we should have two separate evaluation results of ("label": "comment). In our result, we should have a "personalization" key with its value as its evaluation result, and the same for "following-template" as a key with its value as its evaluation result.
- When naming the keys for the rules, use the corresponding names from {rule_names}"
"""

        self.generation = """Here is the generation you shall evaluate against for all rules:
{generation}"""

        prompt_list = ["I need you to evaluate the generations"]

        for rule in self.rules:
            prompt_list.append(rule.rule_criteria)

        for rule in self.rules:
            prompt_list.append(rule.info)

        prompt_list.append(self.instruction)
        prompt_list.append(self.generation)
        self.prompt = "\n".join(prompt_list)

    @property
    def label_type(self):
        return "boolean"

    @property
    def dataset_name(self):
        return "template"

    def add_to_dataset(self, unit):
        pass
        # self._langsmith_client.create_example(
        #     inputs={
        #         "generation": unit.generation,
        #         "original_text": unit._features["original_text"],
        #         #"target_context": unit._features["target_context"],
        #         #"template": unit._features["template"] ????
        #     },
        #     outputs=unit._results[self._name],
        #     metadata=unit.meta,
        #     dataset_id=self.get_dataset().id,
        # )
