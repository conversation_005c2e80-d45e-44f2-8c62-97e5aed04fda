import copy
import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from uuid import uuid4

from langchain.schema import HumanMessage
from langsmith import Client

from ...model_caller import ModelCaller
from ...model_config import ModelConfigResolver
from ...task_registry import GenerationGoal
from ...utils import fix_claude_outputs


class EvaluatorBase(ABC):
    def __init__(self, name) -> None:
        self._name = name

    @property
    def name(self):
        return str(self._name)

    @abstractmethod
    def evaluate(self, features):
        pass


class RuleEvaluatorBase(EvaluatorBase):
    def __init__(self, name) -> None:
        super().__init__(name)


class LLMEvaluatorBase(EvaluatorBase):
    def __init__(self, name, eval_model=None) -> None:
        super().__init__(name)
        self.model_config = ModelConfigResolver.resolve(
            GenerationGoal.EVALUATION, foundation_model=eval_model
        )
        self.model_caller = ModelCaller(self.model_config)

        self._dataset = None
        self._langsmith_client = Client()
        self._is_annotation = False

    @property
    def feature_list(self):
        return self._feature_list

    def parse_json_result(self, result):
        try:
            result = json.loads(result)
        except:
            cleaned_result = fix_claude_outputs([result])[0]
            try:
                result = json.loads(cleaned_result)
            except Exception as e:
                logging.error(
                    f"Failed to parse output as JSON for {self._name}: {result} due to {e}"
                )
                result = {
                    "label": "PASS",  # just make it not fail the pipeline, shall be fixed in the future
                    "comment": "",
                }
        return result

    def evaluate(self, features):
        # check if variable_map maps the prompt input_variables
        try:
            prompt_check = self.prompt.format(**features)
        except KeyError as e:
            logging.error(f"Missing features in the prompt: {self.prompt}")
            raise e

        messages = [
            HumanMessage(content=prompt_check),
        ]
        result = self.model_caller.get_results_with_fallback(messages)
        if not isinstance(result, list):
            raise Exception("Response is not a list")
        if not result:
            raise Exception("Response is empty")
        text = result[0].text
        if not text:
            raise Exception("Generated text is empty")
        parsed_result = self.parse_json_result(text)

        if self._is_annotation:
            self.create_run(features)

        return parsed_result

    def get_dataset(self):
        if not self._dataset:
            datasets = self._langsmith_client.list_datasets(
                dataset_name=self.dataset_name
            )
            datasets = list(datasets)
            if not datasets:
                logging.error(f"Dataset {self.dataset_name} not found")
                return None
            self._dataset = datasets[0]
        return self._dataset

    def set_annotation(self):
        self._is_annotation = True

    @abstractmethod
    def add_to_dataset(self, unit):
        pass

    def create_run(self, features):
        feature_list = self.feature_list
        run_id = str(uuid4())
        self._langsmith_client.create_run(
            name="AnnotatedRun",
            project_name="tofu",
            inputs={feature: features.get(feature) for feature in feature_list},
            outputs={"generation": features["generation"]},
            run_type="chain",
            id=run_id,
            tags=[self._name + "-annotation", "autoeval"],
            end_time=datetime.utcnow().isoformat() + "Z",
        )


class LLMGenEvaluatorBase(LLMEvaluatorBase):
    def __init__(self, name, eval_model=None) -> None:
        super().__init__(name, eval_model)

    def evaluate(self, generation, features):
        copied_features = copy.deepcopy(features)
        copied_features["generation"] = generation
        return super().evaluate(copied_features)

    def add_to_dataset(self, unit):
        self._langsmith_client.create_example(
            inputs={"generation": unit.generation},
            outputs=unit._results[self._name],
            metadata=unit.meta,
            dataset_id=self.get_dataset().id,
        )
