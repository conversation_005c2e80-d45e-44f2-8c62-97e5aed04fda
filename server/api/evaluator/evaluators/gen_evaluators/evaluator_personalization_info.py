import logging

from ..evaluator_base import LLMGenEvaluatorBase


class LLMEvaluatorPersonalizationInfo(LLMGenEvaluatorBase):
    def __init__(self) -> None:
        super().__init__("personalization-info")

        self.prompt = """I need you to evaluate the generations.
Here is the rule we want to check:
- The generated content shall mention the target or information about the target, including but not limited to.
  - the industry of the target
  - the business of the target
  - the product of the target
Including the target name only doesn't count, but vaguely mentioning specific info is good.
For example, if the target is "Apple", the generation could include "smartphone maker".
For example, if the target is "Johnson & Johnson", it's considered good with: Streamline content delivery across all your healthcare channels seamlessly. Since it talks about the industry "healthcare".

Here is the instruction you shall evaluate against, this includes the target name, their value prop and other information:
- Please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation.Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "FAIL", "comment": "The generation does not include the target specific information"

Here is the target information you shall consider during the evaluation:
{target_context}


Here is the generation you shall evaluate against:
{generation}"""

    @property
    def label_type(self):
        return "boolean"

    @property
    def dataset_name(self):
        return "personalization-info"

    def add_to_dataset(self, unit):
        self._langsmith_client.create_example(
            inputs={
                "generation": unit.generation,
                "target_context": unit._features["target_context"],
            },
            outputs=unit._results[self._name],
            metadata=unit.meta,
            dataset_id=self.get_dataset().id,
        )
