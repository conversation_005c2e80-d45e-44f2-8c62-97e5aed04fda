import logging
from uuid import uuid4

from ..evaluator_base import LLMGenEvaluatorBase


class LLMEvaluatorFollowingTemplate(LLMGenEvaluatorBase):
    def __init__(self) -> None:
        super().__init__("following-template")

        self.rule_criteria = """Here is the following-template rule we want to check:
- Carefully read the template and understand its structure.
- Check if the generations follows the template, like keeping the leading set of words in each clause, figuring out what placeholders will be good to fill in with appropriate words or phrases.
- Ensure that the generated content is relevant and coherent based on the given context.
- Difference in imformational detail and elements between the original content and generation does not matter since we only care about the template structure.

Here is an example of a generation that follows the orignal content's format:
- original: Bring innovation to the fight against aging, one benefit at a time.
- generation: Bring innovation to renewable energy, one solar panel at a time.
- You can see that this generation keeps the leading words of "Bring innovation to...".

Here is another example of a generation that follows the original content's format:
- original: Significantly increase the valuation of your company
- generation: Significantly increase the valuation of NorthStar
- You can see that this generation keeps the leading words of "Significantly increase the valuation of ..."
"""

        self.instruction = """Here is the instruction you shall evaluate against, this includes the template name and other information:
- Please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation. Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "FAIL", "comment": "The generation does not include the original content's structure points"
"""

        self.info = """Here is the original content you shall consider during the evaluation:
{original_text}"""
        self.generation = """Here is the generation you shall evaluate against:
{generation}"""

        self.prompt = "\n".join(
            [
                "I need you to evaluate the generations",
                self.rule_criteria,
                self.instruction,
                self.info,
                self.generation,
            ]
        )

        self._feature_list = ["original_text"]

    @property
    def label_type(self):
        return "boolean"

    @property
    def dataset_name(self):
        return "following-template"

    def add_to_dataset(self, unit):
        self._langsmith_client.create_example(
            inputs={
                "generation": unit.generation,
                "original_text": unit._features["original_text"],
                # "target_context": unit._features["target_context"],
                # "template": unit._features["template"] ????
            },
            outputs=unit._results[self._name],
            metadata=unit.meta,
            dataset_id=self.get_dataset().id,
        )
