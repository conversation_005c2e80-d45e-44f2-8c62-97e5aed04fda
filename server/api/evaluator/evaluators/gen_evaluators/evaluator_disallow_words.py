import logging

from langchain.schema import AIMessage, HumanMessage, SystemMessage

from ..evaluator_base import LLMGenEvaluatorBase


class LLMEvaluatorDisallowWords(LLMGenEvaluatorBase):
    def __init__(self) -> None:
        super().__init__("disallow_words")

        self.rule_criteria = """Here is the disallow_words rule we want to check:
- The generated results shall not include any of words in: "revolutionize", "navigating", "evolving", "transform"
"""

        self.instruction = """Here is the instruction you shall evaluate against:
- Please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation. Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "FAIL", "comment": "The generation includes the word 'revolutionize'"
"""

        self.info = """"""

        self.generation = (
            """Here is the generation you shall evaluate against: {generation}"""
        )

        self.prompt = "\n".join(
            [
                "I need you to evaluate the generations",
                self.rule_criteria,
                self.instruction,
                self.info,
                self.generation,
            ]
        )

    @property
    def label_type(self):
        return "boolean"

    @property
    def dataset_name(self):
        return "disallow_words"
