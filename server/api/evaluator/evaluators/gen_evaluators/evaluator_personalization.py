import logging

from ..evaluator_base import LLMGenEvaluatorBase


class LLMEvaluatorPersonalization(LLMGenEvaluatorBase):
    def __init__(self) -> None:
        super().__init__("personalization")

        self.rule_criteria = """Here is the personalization rule we want to check:
- The generated content shall mention the target or information about the target, including but not limited to.
    - the name of the target
    - the industry of the target
    - the business of the target
It's not necessary to include the target name, but vaguely mentioning specific info is good.
For example, if the target is "Apple", the generation could include "smartphone maker", and mentioning the company like "Apple's" is also good. Only mentioning the company name "Apple" is also good.
For example, if the target is "Intel", it's considered good with: Repurpose content seamlessly across Intel's channels automatically. Since it mentions the company name "Intel".
For example, if the target is "Johnson & Johnson", it's considered good with: Streamline content delivery across all your healthcare channels seamlessly. Since it talks about the industry "healthcare"."""

        self.instruction = """Here is the instruction you shall evaluate against, this includes the target name, their value prop and other information:
- Please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation.Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "FAIL", "comment": "The generation does not include the target specific information"
"""

        self.info = """Here is the target information you shall consider during the personalization evaluation:
{target_context}"""
        self.generation = """Here is the generation you shall evaluate against:
{generation}"""

        self.prompt = "\n".join(
            [
                "I need you to evaluate the generations.",
                self.rule_criteria,
                self.instruction,
                self.info,
                self.generation,
            ]
        )

        self._features_list = ["original_text", "target_context"]

    @property
    def label_type(self):
        return "boolean"

    @property
    def dataset_name(self):
        return "personalization"

    def add_to_dataset(self, unit):
        self._langsmith_client.create_example(
            inputs={
                "generation": unit.generation,
                "target_context": unit._features["target_context"],
            },
            outputs=unit._results[self._name],
            metadata=unit.meta,
            dataset_id=self.get_dataset().id,
        )
