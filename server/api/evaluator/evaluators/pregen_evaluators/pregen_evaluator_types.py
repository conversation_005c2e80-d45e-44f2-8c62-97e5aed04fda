from enum import Enum


class TofuEnum(Enum):
    # by default it returns the name of the enum member but we want the value
    def __str__(self):
        return self.value

    @classmethod
    def from_str(cls, value):
        for member in cls:
            if member.value == value:
                return member
        raise ValueError(f"{value} is not a valid {cls.__name__}")


class PregenRule(TofuEnum):
    ANCHOR_CONTENT_PRECHECK = "anchor_content_precheck"
    EMAIL_NO_GREETING_SIGNATURE = "email_no_greeting_signature"
    EMAIL_PERSONALIZED = "email_personalized"
    NO_STAT_SELECTED = "no_stat_selected"
    ASSET_CHECK = "asset_check"
    COMPONENT_CHECK = "component_check"


class ScoreCategory(TofuEnum):
    WRITING = "writing"
    COMPONENT = "component"
    INSTRUCTIONS_AND_CONTENT = "instructions and content"
    CAMPAIGN_SETUP = "campaign setup"


score_rule_mapping = {
    PregenRule.EMAIL_PERSONALIZED: ScoreCategory.WRITING,
    PregenRule.EMAIL_NO_GREETING_SIGNATURE: ScoreCategory.COMPONENT,
    PregenRule.NO_STAT_SELECTED: ScoreCategory.COMPONENT,
    PregenRule.ASSET_CHECK: ScoreCategory.INSTRUCTIONS_AND_CONTENT,
    PregenRule.COMPONENT_CHECK: ScoreCategory.COMPONENT,
    PregenRule.ANCHOR_CONTENT_PRECHECK: ScoreCategory.INSTRUCTIONS_AND_CONTENT,
}
