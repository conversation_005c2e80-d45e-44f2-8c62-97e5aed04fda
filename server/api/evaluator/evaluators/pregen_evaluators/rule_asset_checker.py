import logging

from ....models import AssetInfo
from ....playbook_build.object_builder import ObjectBuilder
from ..evaluator_base import RuleEvaluatorBase
from .pregen_evaluator_types import PregenRule


class AssetChecker(RuleEvaluatorBase):
    def __init__(self) -> None:
        super().__init__(PregenRule.ASSET_CHECK.value)

    def evaluate(self, data_wrapper):
        self.data_wrapper = data_wrapper
        custom_instructions = self.data_wrapper.aggregated_custom_instructions
        asset_params = self.data_wrapper.aggregated_asset_params
        for custom_instruction in custom_instructions:
            custom_instruction_assets = custom_instruction.get("assets", None)
            if custom_instruction_assets:
                asset_params.append(custom_instruction)

        is_pass = True
        error_messages = []

        failed_assets = []

        for asset_param in asset_params:
            # {"assets": {"Campaign Assets": ["tofu website"]}, "instruction": "None"},
            assets = asset_param.get("assets", {})
            for asset_l1_key, asset_l2_keys in assets.items():
                for asset_l2_key in asset_l2_keys:
                    asset_info = AssetInfo.objects.filter(
                        asset_info_group__playbook_id=self.data_wrapper.playbook_instance.id,
                        asset_info_group__asset_info_group_key=asset_l1_key,
                        asset_key=asset_l2_key,
                    )
                    if not asset_info:
                        is_pass = False
                        error_messages.append(
                            f"Asset not found by name: {asset_l1_key}/{asset_l2_key}. Please make sure it's not deleted or renamed"
                        )
                    else:
                        asset_info = asset_info[0]
                        asset_builder = ObjectBuilder.get_builder(asset_info)
                        _, errors, doc_failures = asset_builder.check_docs()

                        if doc_failures:
                            is_pass = False

                            for doc_id, _errors in doc_failures.items():
                                doc_meta = asset_info.docs_last_build.get(doc_id)
                                doc_type = doc_meta.get("type")
                                if doc_type == "url":
                                    doc_url = doc_meta.get("value")
                                    if (
                                        "drive.google.com" in doc_url
                                        or "docs.google.com" in doc_url
                                    ):
                                        error_messages.append(
                                            f"Check {doc_url} and make sure it’s publicly accessible or share <NAME_EMAIL>."
                                        )
                                    else:
                                        error_messages.append(
                                            f"{doc_url} is failed to crawl."
                                        )
                                else:
                                    error_messages.append(
                                        f"{doc_id} is failed to fetch meaningful content."
                                    )
                            failed_assets.append((asset_l1_key, asset_l2_key))

        if error_messages and is_pass or not error_messages and not is_pass:
            logging.error(
                f"debug: inconsistent error check: {error_messages} while is_pass: {is_pass}"
            )
            is_pass = True  # fallback

        message = ""
        if is_pass:
            if asset_params:
                message = "All reference content loaded successfully."
            else:
                message = "No reference content to check."
        else:
            error_messages = "\n".join(error_messages)
            message = f"These documents are failed to load: {error_messages}"

        check_results = {
            "label": "PASS" if is_pass else "FAIL",
            "comment": message,
            "message": message,
        }
        return check_results
