import logging

from django.core.cache import cache

from ..evaluator_base import LLMEvaluatorBase
from .pregen_evaluator_types import PregenRule


class LLMEvaluatorEmailNoGreetingSignature(LLMEvaluatorBase):
    def __init__(self) -> None:
        super().__init__(PregenRule.EMAIL_NO_GREETING_SIGNATURE.value)

        self.prompt = """I need you to evaluate the email template.
Here is the rule we want to check:
The content pieces selected shall not include any greeting or signature in the email.
- The greeting includes but not limited to: "Dear", "Hi", "Hello", "Greetings", "Good morning", "Good afternoon", "Good evening", "Good day", "Hey", "To whom it may concern", "Dear Sir/Madam", "Dear [Name]", "Hi [Name]", "Hello [Name]", "Hey [Name]", "Greetings [Name]", "Good morning [Name]", "Good afternoon [Name]", "Good evening [Name]", "Good day [Name]"
- The first sentence in email like "As a company etc" is not considered as greeting.
- The signature includes but not limited to: "Best regards", sender's name, etc.

Here is the instruction you shall follow:
- Please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation.Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "PASS", "comment": "None of the content pieces doesn't include greeting or signature information"


Here is the content pieces you shall evaluate against:
{component_selections}"""

    @property
    def label_type(self):
        return "boolean"

    @property
    def dataset_name(self):
        return "email_no_greeting_signature"

    def get_cache_key(self, components):
        all_keys = list(components.keys())
        all_keys.sort()
        return f"autoeval_{self._name}_" + "_".join(all_keys)

    def evaluate(self, data_wrapper):
        self._data_wrapper = data_wrapper
        components = self._data_wrapper.text_gen_components
        if not components:
            return {}

        cache_key = self.get_cache_key(components)
        # check if cache_key exist in redis cache
        try:
            existing_result = cache.get(cache_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get {self._name} cache for {cache_key}: {e}"
            )
            existing_result = None
        if existing_result:
            logging.info(f"Cache hit for {self._name} for {cache_key}")
            return existing_result

        component_selections = []
        for component in components.values():
            # TODO: check if text component
            component_selections.append(component["text"])

        component_selections = "\n\n".join(component_selections)
        result = super().evaluate({"component_selections": component_selections})
        if "label" not in result:
            result["label"] = "PASS"

        if not "message" in result:
            result["message"] = (
                "None of the selected components include greeting or signature information."
                if result["label"] == "PASS"
                else "Some of the selected components include greeting or signature information."
            )

        cache.set(cache_key, result, None)
        return result

    def add_to_dataset(self, unit):
        # self._langsmith_client.create_example(
        #     inputs={
        #         "generation": unit.generation,
        #         "target_context": unit._features["target_context"],
        #     },
        #     outputs=unit._results[self._name],
        #     metadata=unit.meta,
        #     dataset_id=self.get_dataset().id,
        # )
        pass
