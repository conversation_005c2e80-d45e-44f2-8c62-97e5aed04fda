import logging

from django.core.cache import cache

from ..evaluator_base import LLMEvaluatorBase
from . import pregen_rules


class LLMPregenCombinedEvaluator(LLMEvaluatorBase):
    def __init__(self, rules) -> None:
        super().__init__("pregen_combined")

        if not rules:
            self.rules = pregen_rules.get_all_predefined_pregen_rules()
        else:
            self.rules = pregen_rules.get_rules_by_name(rules)

    def get_prompt(self, rules_to_eval):
        rule_names = [rule.name for rule in rules_to_eval]

        self.instruction = f"""Here is the instruction you shall evaluate against, this includes any info I provide for each rule:
- Evaluate the generation for every rule considered in one LLM call (do not separate)
- For each rule, please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation.Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "FAIL", "comment": "The generation does include the personalization aspect but does not follow the template well"
- For example, if we only consider two rules such as "personalization" and "following-template", we should have two separate evaluation results of ("label": "comment). In our result, we should have a "personalization" key with its value as its evaluation result, and the same for "following-template" as a key with its value as its evaluation result.
- When naming the keys for the rules, use the corresponding names from {rule_names}"
"""

        self.generation = """Here is the generation you shall evaluate against for all rules:
{generation}"""

        prompt_list = ["I need you to evaluate the generations"]

        for rule in rules_to_eval:
            prompt_list.append(rule.rule_criteria)

        for rule in rules_to_eval:
            prompt_list.append(rule.info)

        prompt_list.append(self.instruction)
        prompt_list.append(self.generation)
        self.prompt = "\n".join(prompt_list)

    @property
    def label_type(self):
        return "boolean"

    def evaluate(self):
        rules_to_eval = []
        for rule in self.rules:
            if not rule.in_cache():
                rules_to_eval.append(rule)

        prompt = self.get_prompt(rules_to_eval)

        result = self.llm.invoke(prompt)
        return result
