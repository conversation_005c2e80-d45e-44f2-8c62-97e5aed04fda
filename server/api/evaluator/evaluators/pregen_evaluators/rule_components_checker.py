from ..evaluator_base import RuleEvaluatorBase
from .pregen_evaluator_types import PregenRule


class ComponentChecker(RuleEvaluatorBase):
    def __init__(self) -> None:
        super().__init__(PregenRule.COMPONENT_CHECK.value)

    def evaluate(self, data_wrapper):
        self.data_wrapper = data_wrapper

        if not self.data_wrapper.text_gen_components:
            return {
                "label": "FAIL",
                "comment": "No components are selected.",
                "message": "No components are selected.",
            }
        message = (
            f"{len(self.data_wrapper.text_gen_components)} components are selected"
            if len(self.data_wrapper.text_gen_components) > 1
            else "1 component is selected"
        )
        return {
            "label": "PASS",
            "comment": message,
            "message": message,
        }
