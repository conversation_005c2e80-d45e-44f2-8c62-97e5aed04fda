import hashlib
import logging

from django.core.cache import cache

from ..evaluator_base import LLMEvaluatorBase
from .pregen_evaluator_types import PregenRule


class LLMEvaluatorEmailPersonalized(LLMEvaluatorBase):
    def __init__(self) -> None:
        super().__init__(PregenRule.EMAIL_PERSONALIZED.value)

        self.prompt = """I need you to evaluate the email template.
Here is the rule we want to check:
The generated content shall be personalized, including but not limited to:
- It may include one object as the email audience, the audience shall not be generic but have to be specific.
- For example, the template mentioning "As Costco the retailer industry needs to improve the customer experience" is considered as personalized.
- For example, the template only talking about "You need to improve the company efficiency" is considered as generic.

Here is the instruction you shall follow:
- Please generate only the evaluation result, starting with "PASS" or "FAIL" and following some explanations. PASS means it passes the rule, FAIL means it fails the rule.
- The output shall be constructured as a JSON object two keys: label, which be either "PASS" or "FAIL", and comment, which is a string for the explanation.Please return in exact json format that I could parse directly. Please don't include any leading sentences like "here is"
- For example: "label": "FAIL", "comment": "The template provided target generic audience but not a concrete company or persona"


Here is the template you shall evaluate against:
{template}"""

    @property
    def label_type(self):
        return "boolean"

    @property
    def dataset_name(self):
        return "email_personalized"

    def get_cache_key(self, template):
        md5_hash = hashlib.md5()
        # Encode the input string and update the hash object
        md5_hash.update(str(template).encode("utf-8"))
        template_key = md5_hash.hexdigest()
        return f"autoeval_{self._name}_{template_key}"

    def evaluate(self, data_wrapper):
        self._data_wrapper = data_wrapper
        template = self._data_wrapper.template

        cache_key = self.get_cache_key(template)
        # check if cache_key exist in redis cache
        existing_result = cache.get(cache_key)
        if existing_result:
            logging.info(f"Cache hit for {self._name} for {cache_key}")
            return existing_result

        result = super().evaluate({"template": str(template)})
        if "label" not in result:
            result["label"] = "PASS"

        if not "message" in result:
            result["message"] = (
                "Great job adding personalized sentences."
                if result["label"] == "PASS"
                else "Adding personalized sentences will help Tofu follow the style you want for the rest of your targets."
            )
        cache.set(cache_key, result, None)
        return result

    def add_to_dataset(self, unit):
        # self._langsmith_client.create_example(
        #     inputs={
        #         "generation": unit.generation,
        #         "target_context": unit._features["target_context"],
        #     },
        #     outputs=unit._results[self._name],
        #     metadata=unit.meta,
        #     dataset_id=self.get_dataset().id,
        # )
        pass
