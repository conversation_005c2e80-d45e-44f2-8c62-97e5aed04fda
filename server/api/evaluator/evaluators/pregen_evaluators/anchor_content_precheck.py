import json
import logging
from typing import Dict, List, Optional, Set

from langchain_core.messages import HumanMessage

from ....models import AssetInfo, AssetInfoGroup
from ....prompt.prompt_library.anchor_content_precheck_prompt import (
    ANCHOR_CONTENT_PRECHECK_PROMPT,
)
from ....utils import fix_claude_outputs
from ..evaluator_base import LLMEvaluatorBase
from .asset_eval_builder import AssetEvalBuilder
from .pregen_evaluator_types import PregenRule


class AssetCollector:
    """Helper class to collect and manage asset IDs from various sources."""

    @staticmethod
    def collect_all_asset_ids(
        asset_ids: List[int], asset_group_ids: Optional[List[str]] = None
    ) -> List[int]:
        """Collect all asset IDs from individual assets and asset groups."""
        all_asset_ids: Set[int] = set(asset_ids)  # use a set to dedupe

        if asset_group_ids:
            group_asset_ids = AssetCollector._get_asset_ids_from_groups(asset_group_ids)
            all_asset_ids.update(group_asset_ids)

        return list(all_asset_ids)

    @staticmethod
    def _get_asset_ids_from_groups(asset_group_ids: List[str]) -> List[int]:
        """Get asset IDs from asset groups."""
        asset_groups = {
            group.asset_info_group_key: group
            for group in AssetInfoGroup.objects.filter(
                asset_info_group_key__in=asset_group_ids
            )
        }

        asset_ids = []
        for asset_group_id in asset_group_ids:
            asset_group = asset_groups.get(asset_group_id)
            if asset_group:
                assets = AssetInfo.objects.filter(asset_info_group=asset_group)
                asset_ids.extend(asset.id for asset in assets)

        return asset_ids

    @staticmethod
    def get_asset_map(asset_ids: List[int]) -> Dict[int, AssetInfo]:
        """Get a mapping of asset IDs to AssetInfo objects."""
        return {asset.id: asset for asset in AssetInfo.objects.filter(id__in=asset_ids)}


class AnchorContentPrecheckEvaluator(LLMEvaluatorBase):
    """Evaluator for checking anchor content before processing."""

    def __init__(self, foundation_model="us.anthropic.claude-3-5-sonnet-20240620-v1:0"):
        super().__init__(
            name=PregenRule.ANCHOR_CONTENT_PRECHECK.value, eval_model=foundation_model
        )

    # Status Management Methods
    def get_eval_status(
        self, asset_ids: List[int], asset_group_ids: Optional[List[str]] = None
    ) -> str:
        """Get the evaluation status for the given assets."""
        all_asset_ids = AssetCollector.collect_all_asset_ids(asset_ids, asset_group_ids)
        assets = AssetInfo.objects.filter(id__in=all_asset_ids)

        # Check if any assets are still running
        if self._has_running_assets(assets):
            return "RUNNING"

        # Check if all assets are done
        if self._all_assets_done(assets):
            return "DONE"

        return "READY"

    def _has_running_assets(self, assets) -> bool:
        """Check if any assets are currently running."""
        for asset in assets:
            asset_builder = AssetEvalBuilder(asset)
            status = asset_builder.get_anchor_precheck_status()
            if status in ["RUNNING", "QUEUED"]:
                return True
        return False

    def _all_assets_done(self, assets) -> bool:
        """Check if all assets have completed precheck."""
        for asset in assets:
            asset_builder = AssetEvalBuilder(asset)
            status = asset_builder.get_anchor_precheck_status()
            if status != "DONE":
                return False
        return True

    def get_eval_results(
        self, asset_ids: List[int], asset_group_ids: Optional[List[str]] = None
    ) -> Dict[int, Dict]:
        """Get the evaluation results for the given assets."""
        all_asset_ids = AssetCollector.collect_all_asset_ids(asset_ids, asset_group_ids)
        asset_info_map = AssetCollector.get_asset_map(all_asset_ids)

        check_results = {}
        for asset_id in all_asset_ids:
            asset_object = asset_info_map.get(asset_id)
            if asset_object:
                asset_builder = AssetEvalBuilder(asset_object)
                cached_result = asset_builder.get_cached_precheck_result()
                if cached_result:
                    check_results[asset_id] = cached_result
                else:
                    logging.warning(
                        f"Anchor content precheck result not found for asset {asset_id}"
                    )
                    pass

        return check_results

    def set_assets_anchor_precheck_status(
        self,
        asset_ids: List[int],
        asset_group_ids: Optional[List[str]] = None,
        status: str = "QUEUED",
    ) -> None:
        """Set the anchor precheck status for the given assets."""
        all_asset_ids = AssetCollector.collect_all_asset_ids(asset_ids, asset_group_ids)
        asset_info_map = AssetCollector.get_asset_map(all_asset_ids)

        for asset_id in all_asset_ids:
            asset_object = asset_info_map.get(asset_id)
            if asset_object:
                asset_builder = AssetEvalBuilder(asset_object)
                asset_builder.set_anchor_precheck_status(status)

    # Evaluation Methods
    def evaluate_asset_ids(
        self, asset_ids: List[int], asset_group_ids: Optional[List[str]] = None
    ) -> Dict[int, Dict]:
        """Evaluate the given asset IDs and return check results."""
        all_asset_ids = AssetCollector.collect_all_asset_ids(asset_ids, asset_group_ids)
        asset_info_map = AssetCollector.get_asset_map(all_asset_ids)

        # Set status to RUNNING for all assets
        for asset_id in all_asset_ids:
            asset_object = asset_info_map.get(asset_id)
            if asset_object:
                asset_builder = AssetEvalBuilder(asset_object)
                asset_builder.set_anchor_precheck_status("RUNNING")

        # Process individual assets
        check_results = {}
        for asset_id in all_asset_ids:
            asset_object = asset_info_map.get(asset_id)
            if asset_object:
                check_results[asset_id] = AssetEvalBuilder(
                    asset_object, llm_evaluator_callback=self._llm_check_content
                ).check_asset_info()

        return check_results

    def check_asset_info(self, asset_object: AssetInfo) -> Dict[str, str]:
        """
        Perform anchor content precheck on a single asset.
        Returns: {"label": "PASS" or "FAIL", "comment": reason if "FAIL"}
        """
        asset_builder = AssetEvalBuilder(
            asset_object, llm_evaluator_callback=self._llm_check_content
        )
        return asset_builder.check_asset_info()

    # LLM Evaluation Methods
    def _llm_check_content(self, anchor_content: str) -> Dict[str, str]:
        """Use LLM to check anchor content quality."""
        msg = ANCHOR_CONTENT_PRECHECK_PROMPT.format(
            anchor_content=anchor_content,
            json_format='{"label": "PASS" or "FAIL", "comment": <only give a reason for a FAIL result>}',
        )

        llm_inputs = [HumanMessage(content=msg)]
        result = self.model_caller.get_results_with_fallback(
            llm_inputs, json_output=True
        )

        if not isinstance(result, list) or not result:
            raise ValueError(f"Expected a list of results, got {result}")

        if len(result) > 1:
            logging.error(f"Expected 1 output, got {len(result)}")

        text = result[0].text
        if not text:
            raise ValueError(f"Expected a text output, got {text}")

        if "claude" in self.model_caller.model_name:
            text = fix_claude_outputs([text])[0]

        return self._parse_llm_response(text)

    def _parse_llm_response(self, text: str) -> Dict[str, str]:
        """Parse and validate LLM response."""
        try:
            output_json = json.loads(text)
        except json.decoder.JSONDecodeError as e:
            logging.error(f"Failed to parse output as JSON: {e}")
            return {"label": "PASS", "comment": None}

        label = output_json.get("label")
        if label not in ["PASS", "FAIL"]:
            logging.error(f"Expected 'label' to be 'PASS' or 'FAIL', got {label}")
            return {"label": "PASS", "comment": None}

        if label == "FAIL" and "comment" not in output_json:
            logging.error(f"Expected 'comment' key in output, got {output_json}")
            return {"label": "FAIL", "comment": None}

        return {"label": label, "comment": output_json.get("comment")}

    def check(self, anchor_content: str) -> Dict[str, str]:
        """Legacy method for compatibility - use _llm_check_content instead."""
        return self._llm_check_content(anchor_content)

    def add_to_dataset(self, unit):
        """Required by base class - not implemented."""
        pass

    def evaluate(self, data_wrapper) -> Dict[str, Dict]:
        """Evaluate assets based on data wrapper (legacy interface)."""
        self.data_wrapper = data_wrapper
        check_results = {}

        asset_params = self.data_wrapper.aggregated_asset_params

        for asset_param in asset_params:
            if asset_param.get("meta") == "repurpose_anchor_content":
                if not asset_param.get("assets"):
                    continue

                asset_map = asset_param.get("assets")
                if isinstance(asset_map, list):
                    asset_map = asset_map[0]

                for asset_info_group_key, asset_key in asset_map.items():
                    asset_info = self._find_asset_info(asset_info_group_key, asset_key)

                    if not asset_info:
                        check_results[asset_key] = {
                            "label": "FAIL",
                            "comment": f"Your anchor content {asset_key} was either renamed or deleted. Please delete and re-add the anchor content.",
                        }
                        continue

                    check_results[asset_key] = AssetEvalBuilder(
                        asset_info, llm_evaluator_callback=self._llm_check_content
                    ).check_asset_info()

        return check_results

    def _find_asset_info(
        self, asset_info_group_key: str, asset_key: str
    ) -> Optional[AssetInfo]:
        """Find asset info by group key and asset key."""
        return AssetInfo.objects.filter(
            asset_info_group__playbook_id=self.data_wrapper.playbook_instance.id,
            asset_info_group__asset_info_group_key=asset_info_group_key,
            asset_key=asset_key,
        ).first()
