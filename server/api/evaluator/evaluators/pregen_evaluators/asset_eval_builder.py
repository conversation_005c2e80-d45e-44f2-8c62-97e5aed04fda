import logging
from typing import Dict, Optional

from ....data_loaders.s3_video_file_loader import TofuS3AudioVideoFileLoader
from ....data_loaders.url_loader_utils import try_parse_google_url
from ....models import AssetInfo
from ....playbook_build.object_builder import ObjectBuilder
from ....playbook_build.object_builder_asset import extract_asset_raw_text

# Constants for evaluation
MINIMUM_WORD_COUNT_FOR_AUTO_PASS = 200

GOOGLE_DRIVE_PERMISSION_ERROR_MSG = (
    "Looks like we don't have access to your file. Please make sure the link is "
    "public or share <NAME_EMAIL> and then come back here to try again."
)
URL_CRAWL_ERROR_MSG = (
    "We couldn't crawl the URL {url}. Remove the URL and paste the content as "
    "text instead in order to generate content."
)
FILE_PROCESSING_ERROR_MSG = (
    "We couldn't get enough information from the item {filename}. Try another "
    "format or paste the content as text instead."
)
TRANSCRIPT_PROCESSING_MSG = (
    "The transcript for {filename} is still being processed. You'll need to wait "
    "for the transcription to finish to use it. You can check the transcription "
    "status in playbook or refresh this page."
)
GENERIC_PROCESSING_ERROR_MSG = (
    "We couldn't get enough information from the item. Try another format or "
    "paste the content as text instead."
)


class DocumentValidator:
    """Handles validation of individual documents within assets."""

    def __init__(self, asset_object: AssetInfo):
        self.asset_object = asset_object

    def validate_all_documents(self) -> Dict[str, Dict[str, str]]:
        """Validate all documents in the asset and return results for non-passing documents."""
        results = {}

        for doc_key, data_entry in self.asset_object.docs.items():
            result = self.validate_document(doc_key, data_entry)
            if result["label"] != "PASS":
                results[doc_key] = result

        return results

    def validate_document(self, doc_key: str, data_entry: Dict) -> Dict[str, str]:
        """
        Validate a single document.
        Returns: {"label": "PASS"|"FAIL"|"RUNNING", "comment": str|None}
        """
        data_type = data_entry.get("type", "")
        data_value = data_entry.get("value", "")

        # Check build status first
        build_status = self._get_build_status_for_doc(doc_key)
        if build_status == "error":
            return self._create_fail_result(
                data_type, data_value, "Build error occurred"
            )

        # For video/audio files, check transcription status
        if self._is_video_audio_file(data_type, data_value):
            return self._validate_video_document(data_value)

        # For other document types, try to extract content
        return self._validate_regular_document(data_type, data_value)

    def _validate_video_document(self, data_value) -> Dict[str, str]:
        """Validate a video/audio document by checking transcription status."""
        if not isinstance(data_value, dict):
            return self._create_fail_result("file", data_value, "Invalid file data")

        filename = data_value.get("original_filename", "unknown file")

        try:
            video_loader = TofuS3AudioVideoFileLoader(data_value)

            # Check if transcript already exists and is ready
            if video_loader._check_transcript_exist():
                return {"label": "PASS", "comment": None}

            # Check transcription job status
            try:
                job_status, error = video_loader._check_job_status()

                if job_status == "COMPLETED":
                    # Job completed but transcript not ready yet (postprocessing)
                    return {
                        "label": "RUNNING",
                        "comment": TRANSCRIPT_PROCESSING_MSG.format(filename=filename),
                    }
                elif job_status == "IN_PROGRESS":
                    return {
                        "label": "RUNNING",
                        "comment": TRANSCRIPT_PROCESSING_MSG.format(filename=filename),
                    }
                elif job_status == "FAILED":
                    return {
                        "label": "FAIL",
                        "comment": FILE_PROCESSING_ERROR_MSG.format(filename=filename),
                    }
                else:
                    # Unknown status
                    return {
                        "label": "FAIL",
                        "comment": FILE_PROCESSING_ERROR_MSG.format(filename=filename),
                    }

            except (
                video_loader.transcribe.exceptions.NotFoundException,
                video_loader.transcribe.exceptions.BadRequestException,
            ):
                # No transcription job exists yet
                return {
                    "label": "FAIL",
                    "comment": TRANSCRIPT_PROCESSING_MSG.format(filename=filename),
                }

        except Exception as e:
            logging.error(f"Error validating video document {filename}: {e}")
            return {
                "label": "FAIL",
                "comment": FILE_PROCESSING_ERROR_MSG.format(filename=filename),
            }

    def _validate_regular_document(self, data_type: str, data_value) -> Dict[str, str]:
        """Validate non-video documents by trying to extract content."""
        try:
            doc_loader = ObjectBuilder.get_builder(self.asset_object).doc_loader
            extracted_doc = doc_loader.extract_single_doc_from_loader(
                data_type, data_value, fast_return=True
            )

            if extracted_doc:
                # Check word count - fail fast if content is too short
                total_words = sum(
                    len(doc.page_content.split()) for doc in extracted_doc
                )
                if total_words < 10:
                    return {
                        "label": "FAIL",
                        "comment": "Insufficient content (less than 10 words)",
                    }
                return {"label": "PASS", "comment": None}
            else:
                return self._create_fail_result(
                    data_type, data_value, "Could not extract content"
                )

        except Exception as e:
            # Check for Google Drive permission errors
            if data_type == "url" and self._is_google_drive_permission_error(
                data_value, str(e)
            ):
                return {"label": "FAIL", "comment": GOOGLE_DRIVE_PERMISSION_ERROR_MSG}

            return self._create_fail_result(
                data_type, data_value, f"Extraction failed: {str(e)}"
            )

    def _create_fail_result(
        self, data_type: str, data_value, error_detail: str = None
    ) -> Dict[str, str]:
        """Create a standardized fail result based on data type."""
        # If we have a specific error detail for extraction failures, use it
        if error_detail and "Could not extract content" in error_detail:
            return {"label": "FAIL", "comment": "Could not extract content"}

        if data_type == "url":
            return {
                "label": "FAIL",
                "comment": URL_CRAWL_ERROR_MSG.format(url=data_value),
            }
        elif data_type == "file":
            if isinstance(data_value, dict):
                filename = data_value.get("original_filename", "unknown file")
                return {
                    "label": "FAIL",
                    "comment": FILE_PROCESSING_ERROR_MSG.format(filename=filename),
                }
            else:
                return {"label": "FAIL", "comment": GENERIC_PROCESSING_ERROR_MSG}
        else:
            return {"label": "FAIL", "comment": GENERIC_PROCESSING_ERROR_MSG}

    def _get_build_status_for_doc(self, doc_key: str) -> str:
        """Get the build status for a specific document."""
        if not self.asset_object.docs_build_status:
            return ""

        docs_status = self.asset_object.docs_build_status.get("docs", {})
        doc_status = docs_status.get(doc_key, {})
        return doc_status.get("status", "")

    def _is_google_drive_permission_error(
        self, data_value: str, error_str: str
    ) -> bool:
        """Check if the error is a Google Drive permission error."""
        google_drive_url = try_parse_google_url(data_value)
        return google_drive_url and (
            "The caller does not have permission" in error_str
            or "File not found" in error_str
        )

    @staticmethod
    def _is_video_audio_file(data_type: str, data_value) -> bool:
        """Check if the file is a video or audio file."""
        if not isinstance(data_value, dict):
            return False
        return (
            data_type == "file"
            and data_value.get("mime_file_type", "")
            in TofuS3AudioVideoFileLoader.MIME_TYPE_TO_FORMAT.keys()
        )


class AssetEvalBuilder:
    """Builder for asset evaluation database operations and field manipulations."""

    def __init__(self, asset_object: AssetInfo, llm_evaluator_callback=None):
        self.asset_object = asset_object
        self.llm_evaluator_callback = llm_evaluator_callback

        # refresh the asset object
        self.asset_object.refresh_from_db()

    def get_anchor_precheck_status(self) -> Optional[str]:
        """Get the anchor precheck status for the asset."""
        if not self.asset_object.docs_build_status:
            return None
        return self.asset_object.docs_build_status.get("anchor_precheck_status")

    def set_anchor_precheck_status(self, status: str) -> None:
        """Set the anchor precheck status for the asset."""
        if not self.asset_object.docs_build_status:
            self.asset_object.docs_build_status = {}
        self.asset_object.docs_build_status["anchor_precheck_status"] = status
        self.asset_object.save(update_fields=["docs_build_status"])

    def get_cached_precheck_result(self) -> Optional[Dict]:
        """Get cached precheck result from additional_info."""
        additional_info = self.asset_object.additional_info
        if (
            additional_info
            and "anchor_content_precheck" in additional_info
            and additional_info["anchor_content_precheck"]
        ):
            return additional_info["anchor_content_precheck"]
        return None

    def save_precheck_result(self, result_dict: Dict) -> None:
        """Save the precheck result to additional_info."""
        additional_info = self.asset_object.additional_info or {}
        additional_info["anchor_content_precheck"] = result_dict
        self.asset_object.additional_info = additional_info
        self.asset_object.save(update_fields=["additional_info"])

    def clear_stale_precheck_cache(self) -> None:
        """Clear cached precheck results if docs have changed."""
        if self.asset_object.docs != self.asset_object.docs_last_build:
            additional_info = self.asset_object.additional_info
            if additional_info and "anchor_content_precheck" in additional_info:
                del additional_info["anchor_content_precheck"]
                self.asset_object.additional_info = additional_info
                self.asset_object.save(update_fields=["additional_info"])

    def has_docs_changed(self) -> bool:
        """Check if docs have changed since last build."""
        return self.asset_object.docs != self.asset_object.docs_last_build

    def get_docs_build_status(self) -> Dict:
        """Get the docs build status from the asset."""
        if self.asset_object.docs_build_status:
            return self.asset_object.docs_build_status.get("docs", {})
        return {}

    def get_docs(self) -> Dict:
        """Get the docs from the asset."""
        return self.asset_object.docs or {}

    def check_asset_info(self) -> Dict[str, str]:
        """
        Perform anchor content precheck on this asset.
        Returns: {"label": "PASS" or "FAIL", "comment": reason if "FAIL"}
        """
        # Clear cached results if docs have changed
        self.clear_stale_precheck_cache()

        # Set status to RUNNING
        self.set_anchor_precheck_status("RUNNING")

        # Check for existing cached result
        cached_result = self.get_cached_precheck_result()
        if cached_result:
            result = cached_result
        else:
            # Perform the actual evaluation
            result = self._perform_asset_evaluation()

        # Save result and set status to DONE (or keep RUNNING if needed)
        self.save_precheck_result(result)

        # Check if result indicates still running
        has_running_status = self._has_running_status(result)
        if has_running_status:
            self.set_anchor_precheck_status("RUNNING")
        else:
            self.set_anchor_precheck_status("DONE")

        return result

    def _perform_asset_evaluation(self) -> Dict[str, str]:
        """
        Perform the actual asset evaluation logic.
        This includes document validation and content evaluation.
        """
        # First validate all documents in the asset
        document_errors = self._validate_asset_documents()
        if document_errors:
            return document_errors

        # Then evaluate the actual content
        return self._evaluate_anchor_content()

    def _validate_asset_documents(self) -> Optional[Dict]:
        """Validate all documents in the asset and return errors if any."""

        validator = DocumentValidator(self.asset_object)
        validation_results = validator.validate_all_documents()

        # If any documents have validation issues, return them
        if validation_results:
            return validation_results

        return None

    def _evaluate_anchor_content(self) -> Dict[str, str]:
        """Evaluate the anchor content quality."""
        try:
            anchor_content = extract_asset_raw_text(self.asset_object)

            # Auto-pass if content is long enough
            if len(anchor_content.split()) >= MINIMUM_WORD_COUNT_FOR_AUTO_PASS:
                return {"label": "PASS", "comment": None}

            # For shorter content, use LLM evaluation if callback is provided
            if self.llm_evaluator_callback:
                return self.llm_evaluator_callback(anchor_content)
            else:
                # Auto-pass for tests when no LLM callback is provided
                return {"label": "PASS", "comment": None}

        except Exception as e:
            logging.error(
                f"Error evaluating anchor content for asset {self.asset_object.id}: {e}"
            )
            return {"label": "FAIL", "comment": "Failed to evaluate content"}

    def _has_running_status(self, result_dict: Dict) -> bool:
        """Check if the result indicates a running status."""
        if isinstance(result_dict, dict):
            # Check if it's a document validation result (dict of dicts)
            if any(
                isinstance(v, dict) and v.get("label") == "RUNNING"
                for v in result_dict.values()
            ):
                return True
            # Check if it's a single result dict
            elif result_dict.get("label") == "RUNNING":
                return True
        return False
