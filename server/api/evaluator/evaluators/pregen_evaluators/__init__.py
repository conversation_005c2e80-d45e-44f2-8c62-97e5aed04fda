from .anchor_content_precheck import Anchor<PERSON>ontentPrecheckEvaluator
from .evaluator_email_no_greeting_selected import LLMEvaluatorEmailNoGreetingSignature
from .evaluator_email_personalized import LLMEvaluatorEmailPersonalized
from .evaluator_no_stat_selected import LLMEvaluator<PERSON>oStatSelected
from .rule_asset_checker import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .rule_components_checker import <PERSON>mpo<PERSON><PERSON><PERSON><PERSON>

all_predefined_pregen_rules = [
    AnchorContentPrecheckEvaluator(),
    LLMEvaluatorEmailPersonalized(),
    LLMEvaluatorEmailNoGreetingSignature(),
    LLMEvaluatorNoStatSelected(),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(),
    ComponentChecker(),
]

all_predefined_pregen_rule_map = {
    rule._name: rule for rule in all_predefined_pregen_rules
}


class PregenRules:
    def __init__(self) -> None:
        pass

    def get_all_predefined_pregen_rules(self):
        return all_predefined_pregen_rules

    def get_rules_by_name(self, rule_names):
        return [
            rule for rule in all_predefined_pregen_rules if rule._name in rule_names
        ]


pregen_rules = PregenRules()
