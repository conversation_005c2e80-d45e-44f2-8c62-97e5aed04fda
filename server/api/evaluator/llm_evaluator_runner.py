import concurrent.futures
import copy
import logging
import uuid

import pandas as pd
from langsmith import traceable

# from typing import Self
from typing_extensions import Self

from ..content import ContentGenerator
from ..models import Content, ContentGroup, TofuUser
from ..playbook import PlaybookHandler
from ..release_tests.llm_inout_check.llm_inout_report_html_util import (
    upload_report_html_to_s3,
)
from ..thread_locals import set_current_user
from .evaluators import predefined_evaluators
from .evaluators.evaluator_base import LLMGenEvaluatorBase
from .evaluators.evaluator_combined import LLMEvaluatorCombined

list_content_groups = [
    34314,  # whitepaper
    16167,  # Seomonitor LP
]

list_content_ids_json_test = [
    322649,
    322652,
    322653,
    322718,
    322719,
    322720,
    322721,
    322722,
]


class LLMPromptEvaluationResultUnit:
    def __init__(self) -> None:
        self._generation = None
        self._results = {}
        self._meta = {}
        self._features = {}
        self._original = None
        self._is_combined = False
        self._is_annotation = False

    def add_generation(self, generation, features, original):
        copied_features = copy.deepcopy(features)
        self._generation = generation["text"]
        self._features = copied_features
        self._original = original
        self._features["original_text"] = self._original

    def evaluate(self, rule):
        if self._is_annotation:
            rule.set_annotation()

        result = rule.evaluate(self._generation, self._features)

        if self._is_combined:
            self._results.update(result)
        else:
            self._results[rule.name] = result

    def get_result(self, rule_name):
        return self._results.get(rule_name, {})

    def set_meta(self, **kwargs):
        self._meta.update(kwargs)

    def set_combined(self) -> None:
        self._is_combined = True

    def set_annotation(self):
        self._is_annotation = True

    @property
    def generation(self):
        return self._generation

    @property
    def original(self):
        return self._original

    @property
    def content_id(self):
        return self._meta.get("content_id")

    @property
    def component_id(self):
        return self._meta.get("component_id")

    @property
    def meta(self):
        return self._meta


class LLMPromptEvaluationResult:
    def __init__(self) -> None:
        self._units = {}
        self._rules = []
        self._evaluators = []
        self._cnt_succ = 0
        self._cnt_fail = 0
        self._failure_cases = []
        self._is_annotation = False

    def set_rules(self, rules):
        self._rules = rules

    def set_evaluators(self, evaluators: list) -> None:
        self._evaluators = evaluators

    def set_metadata(self, metadata: dict) -> None:
        self._metadata = metadata

    def set_annotation(self):
        self._is_annotation = True

    def combined_check(self) -> bool:
        return self._evaluators == ["combined"]

    def add_generation(self, content_id, idx_of_round, variations, features):
        self._cnt_succ += 1
        if content_id not in self._units:
            self._units[content_id] = {}

        for component_id in variations.keys():
            if component_id not in self._units[content_id]:
                self._units[content_id][component_id] = []

        for component_id, component_generation in variations.items():
            original = component_generation.get("text", "")
            for idx, generation in enumerate(
                component_generation.get("meta", {}).get("variations", [])
            ):
                unit = LLMPromptEvaluationResultUnit()
                unit.set_meta(
                    content_id=content_id,
                    idx_of_round=idx_of_round,
                    component_id=component_id,
                    variation_order=idx,
                )
                unit.add_generation(generation, features, original)
                self._units[content_id][component_id].append(unit)

    def add_generation_failure(self, content_id, idx_of_round, error):
        self._cnt_fail += 1
        self._failure_cases.append(
            {"content_id": content_id, "idx_of_round": idx_of_round, "error": error}
        )

    def unit_iterator(self):
        for content_id, content_data in self._units.items():
            for component_id, component_data in content_data.items():
                for unit in component_data:
                    yield unit

    def evaluate(self):
        # for rule in self._rules:
        #     for unit in self.unit_iterator():
        #         unit.evaluate(rule)
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for unit in self.unit_iterator():
                if self.combined_check():
                    unit.set_combined()
                    futures.append(
                        executor.submit(
                            unit.evaluate,
                            LLMEvaluatorCombined([rule.name for rule in self._rules]),
                        )
                    )
                else:
                    if self._is_annotation:
                        unit.set_annotation()
                    for rule in self._rules:
                        futures.append(executor.submit(unit.evaluate, rule))

            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # Optionally handle return value or exceptions
                except Exception as e:
                    print(f"Error evaluating unit: {e}")

    def _add_to_dataset(self):
        for rule in self._rules:
            for unit in self.unit_iterator():
                rule.add_to_dataset(unit)

    def gen_report(self):
        # generate a html page
        df = self.convert_success_cases_to_df()
        failure_df = self.convert_failure_cases_to_df()
        logging.error(f"failure_df: {failure_df}")
        html_report = self.gen_html_report(df, failure_df)

        report_id = str(uuid.uuid4())
        link = upload_report_html_to_s3(
            html_report,
            f"llm_prompt_evaluation/llm_prompt_evaluation_report_{report_id}.html",
        )
        return link

    def convert_success_cases_to_df(self):
        # Extract the data into a DataFrame
        rows = []

        for unit in self.unit_iterator():
            row_data = {
                "content_id": unit.content_id,
                "component_id": unit.component_id,
                "original": unit.original,
                "generation": unit.generation,
                "targets": unit._features.get("targets", []),
            }
            for rule in self._rules:
                rule_name = rule.name
                result = unit.get_result(rule_name)

                if not result:
                    logging.error(f"rule {rule_name} not evaluated")
                    row_data[f"{rule_name}_label"] = "N/A"
                    row_data[f"{rule_name}_comment"] = "N/A"
                else:
                    label = result["label"]
                    comment = result["comment"]
                    row_data[f"{rule_name}_label"] = label
                    row_data[f"{rule_name}_comment"] = comment

            rows.append(row_data)
        summarize_row = {
            "content_id": "Summary",
        }
        for rule in self._rules:
            rule_name = rule.name
            pass_count = 0
            fail_count = 0
            for unit in self.unit_iterator():
                result = unit.get_result(rule_name)
                if not result:
                    continue
                label = result["label"]
                if label == "PASS":
                    pass_count += 1
                else:
                    fail_count += 1
            summarize_row[f"{rule_name}_label"] = (
                f"{pass_count}/{pass_count+fail_count}"
            )
        rows.append(summarize_row)

        df = pd.DataFrame(rows)
        return df

    def convert_failure_cases_to_df(self):
        rows = []
        for case in self._failure_cases:
            row_data = {
                "content_id": case["content_id"],
                "error": case["error"],
            }
            rows.append(row_data)
        df = pd.DataFrame(rows)
        return df

    def gen_html_report(self, df, failure_df):
        summary = {
            "total": len(df) - 1,
            "failures": sum(
                any(row[f"{rule.name}_label"] == "FAIL" for rule in self._rules)
                for _, row in df.iterrows()
            ),
        }

        # Section: Metadata
        metadata_html = "<div class='section'>"
        metadata_html += "<h3>Metadata</h3>"
        for key, value in self._metadata.items():
            metadata_html += f"<p><strong>{key}:</strong> {value}</p>"
        metadata_html += "</div>"

        # Section: Job Success/Failure Counts
        job_counts_html = "<div class='section'>"
        job_counts_html += "<h3>Job Success/Failure Counts</h3>"
        total_cases = self._cnt_fail + self._cnt_succ
        job_counts_html += (
            f"<p><strong>Total Content generated:</strong> {total_cases}</p>"
        )
        job_counts_html += f"<p><strong>Success Cases:</strong> {self._cnt_succ}</p>"
        job_counts_html += f"<p><strong>Failure Cases:</strong> {self._cnt_fail}</p>"
        job_counts_html += "</div>"

        # Section: Metrics for Rule Pass/Fail Ratios
        metrics_html = "<div class='section'>"
        metrics_html += "<h3>Metrics for Rule Pass/Fail Ratios</h3>"
        for rule in self._rules:
            rule_name = rule.name
            pass_count = 0
            fail_count = 0
            for unit in self.unit_iterator():
                result = unit.get_result(rule_name)
                if not result:
                    continue
                label = result["label"]
                if label == "PASS":
                    pass_count += 1
                else:
                    fail_count += 1
            total_count = pass_count + fail_count
            pass_percentage = (pass_count / total_count) * 100 if total_count > 0 else 0
            metrics_html += f"<p><strong>{rule_name}:</strong> {pass_percentage:.2f}% ({pass_count}/{total_count})</p>"
        metrics_html += "</div>"

        # Combine all sections
        metadata_html = (
            f'<div class="bg-white shadow-md rounded-lg p-4 mb-4">'
            f"{metadata_html}{job_counts_html}{metrics_html}"
            f"</div>"
        )
        css = """
        <style>
            .container {
                font-family: Arial, sans-serif;
                line-height: 1.6;
            }
            .section {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 16px;
            }
            .section h3 {
                margin-top: 0;
            }
            .section p {
                margin: 4px 0;
            }
        </style>
        """

        # Final HTML output
        metadata_html = f"{css}{metadata_html}"

        # Generate the HTML for failure cases
        failure_html = ""
        if len(failure_df) > 0:
            failure_html = f"""
            <div class="bg-white shadow-md rounded-lg p-4 mb-4">
                <h3 class="text-lg font-bold mb-2">Failure Cases</h3>
                <table class="min-w-full leading-normal">
                    <thead>
                        <tr>
                            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">content_id</th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">error</th>
                        </tr>
                    </thead>
                    <tbody>
            """
            for _, row in failure_df.iterrows():
                failure_html += f"""
                <tr>
                    <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{row['content_id']}</td>
                    <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{row['error']}</td>
                </tr>
                """
            failure_html += """
                    </tbody>
                </table>
            </div>
            """

        # Generate the HTML report with collapsible content and a summary row
        html_report = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>HTML Report</title>
            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        </head>
        <body class="bg-gray-100">

        <div class="container mx-auto my-10">
            <h2 class="text-2xl font-bold mb-5">HTML Report</h2>

            {metadata_html}
            <button id="toggleButton" class="mb-4 px-4 py-2 bg-blue-500 text-white rounded">Show Fail Cases Only</button>
            {failure_html}

            <div class="bg-white shadow-md rounded-lg overflow-x-auto">
                <table class="min-w-full leading-normal" id="reportTable">
                    <thead>
                        <tr>
                            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">content_id</th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">component_id</th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">original</th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">generation</th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">targets</th>
        """

        # Add rule headers dynamically
        for rule in self._rules:
            rule_name = rule.name
            html_report += f'<th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{rule_name}_label</th>'
            html_report += f'<th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{rule_name}_comment</th>'

        html_report += """
                        </tr>
                    </thead>
                    <tbody>
        """

        for _, row in df.iterrows():
            has_failure = any(
                row[f"{rule.name}_label"] == "FAIL" for rule in self._rules
            )
            row_class = "failure" if has_failure else "success"

            original = str(row["original"])
            original_part = ""
            if len(original) > 200:
                original_part = f"""
                    <button class="collapsible px-3 py-1 bg-blue-500 text-white rounded">Expand</button>
                    <div class="content hidden mt-2">
                        <p>{original}</p>
                    </div>
                """
            else:
                original_part = f"""
                    <p>{original}</p>
                """

            generation = str(row["generation"])
            # TODO: why there's nan?
            generation_part = ""
            if len(generation) > 200:
                generation_part = f"""
                    <button class="collapsible px-3 py-1 bg-blue-500 text-white rounded">Expand</button>
                    <div class="content hidden mt-2">
                        <p>{generation}</p>
                    </div>
                """
            else:
                generation_part = f"""
                    <p>{generation}</p>
                """
            html_report += f"""
            <tr class="{row_class}">
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{row['content_id']}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{row['component_id']}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                    {original_part}
                </td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                    {generation_part}
                </td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{row['targets']}</td>
            """

            # Add rule columns dynamically
            for rule in self._rules:
                rule_name = rule.name
                html_report += f'<td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{row[f"{rule_name}_label"]}</td>'
                html_report += f'<td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{row[f"{rule_name}_comment"]}</td>'

            html_report += "</tr>"

        # Add summary row
        html_report += f"""
            <tr class="summary">
                <td colspan="{3 + len(self._rules) * 2}" class="px-5 py-5 border-t border-gray-200 bg-gray-100 text-sm font-bold">
                    Total: {summary["total"]} | Failures: {summary["failures"]}
                </td>
            </tr>
        """

        html_report += """
                    </tbody>
                </table>
            </div>
        </div>

        <script>
            document.getElementById('toggleButton').addEventListener('click', function() {
                var table = document.getElementById('reportTable');
                var rows = table.getElementsByTagName('tr');
                for (var i = 1; i < rows.length; i++) { // Skip the header row
                    if (rows[i].classList.contains('summary')) continue;
                    if (this.textContent === 'Show Fail Cases Only') {
                        if (!rows[i].classList.contains('failure')) {
                            rows[i].style.display = 'none';
                        }
                    } else {
                        rows[i].style.display = '';
                    }
                }
                if (this.textContent === 'Show Fail Cases Only') {
                    this.textContent = 'Show All Cases';
                } else {
                    this.textContent = 'Show Fail Cases Only';
                }
            });

            var coll = document.getElementsByClassName("collapsible");
            for (var i = 0; i < coll.length; i++) {
                coll[i].addEventListener("click", function() {
                    this.classList.toggle("active");
                    var content = this.nextElementSibling;
                    if (content.classList.contains("hidden")) {
                        content.classList.remove("hidden");
                    } else {
                        content.classList.add("hidden");
                    }
                });
            }
        </script>

        </body>
        </html>
        """

        return html_report


class LLMEvaluatorRunner:
    def __init__(self) -> None:
        self.list_content_groups = []
        self.list_contents = []
        self.contents_to_evaluate = []

        self.num_of_variations = None
        self.foundation_model = None
        self.num_of_rounds = 1
        self.rules = None
        self.add_to_dataset = False
        self._no_evaluation = False
        self._is_annotation = False
        self._result = LLMPromptEvaluationResult()

    def set_content_groups(self, content_groups) -> Self:
        self.list_content_groups.extend(content_groups)
        return self

    def set_contents(self, contents) -> Self:
        self.list_contents.extend(contents)
        return self

    def _resolve_contents(self):
        if self.list_content_groups:
            content_groups = ContentGroup.objects.filter(
                id__in=self.list_content_groups
            )
            contents = Content.objects.filter(content_group__in=content_groups)
            self.contents_to_evaluate.extend(contents)

        if self.list_contents:
            contents = Content.objects.filter(id__in=self.list_contents)
            self.contents_to_evaluate.extend(contents)

    def set_add_to_dataset(self, add_to_dataset) -> Self:
        self.add_to_dataset = add_to_dataset
        return self

    def filter_tests(self, campaign_goal=None, content_type=None) -> Self:
        raise NotImplementedError
        global list_content_groups
        self.list_content_groups = list_content_groups
        if campaign_goal:
            list_content_groups = [
                content_group
                for content_group in list_content_groups
                if content_group["campaign_goal"] == campaign_goal
            ]
        if content_type:
            list_content_groups = [
                content_group
                for content_group in list_content_groups
                if content_group["content_type"] == content_type
            ]
        self.list_content_groups = list_content_groups
        return self

    def set_settings(
        self, num_of_variations, foundation_model, num_of_rounds=1
    ) -> Self:
        self.num_of_variations = num_of_variations
        self.foundation_model = foundation_model
        self.num_of_rounds = num_of_rounds
        return self

    def set_no_evaluation(self) -> Self:
        self.rules = []
        self._no_evaluation = True
        return self

    def set_annotation(self) -> Self:
        self._is_annotation = True
        return self

    def evaluate_rules(
        self, combined=False, predefined_rules=None, new_rules=None
    ) -> Self:
        self.rules = []
        rules = []

        if predefined_rules:
            for rule_name in predefined_rules:
                rule = predefined_evaluators.get_predefined_rule(rule_name)
                if not rule:
                    raise Exception(f"Rule {rule_name} is not defined")
                self.rules.append(rule)
                rules.append(rule.name)

        if new_rules:
            for rule in new_rules:
                new_rule_name = uuid.uuid4()
                new_rule = LLMGenEvaluatorBase(new_rule_name)
                new_rule.set_prompt(rule)
                self.rules.append(new_rule)
                rules.append(new_rule_name)

        if not predefined_rules and not new_rules:
            self.rules = predefined_evaluators.get_all_predefined_rules()
            rules = predefined_evaluators.get_all_predefined_rule_names()

        if combined:
            self._result.set_evaluators(["combined"])
        else:
            self._result.set_evaluators(rules)

        self._result.set_rules(self.rules)

        return self

    def custom_prompts(self, custom_prompts=None) -> Self:
        self.custom_prompts = custom_prompts
        return self

    def _generate(self):
        if not self.foundation_model:
            logging.error("Foundation model not set")
            return

        def process_content(
            test_user_id,
            content_id,
            num_of_variations,
            foundation_model,
            result,
            num_of_rounds=1,
        ):
            test_user = TofuUser.objects.get(id=test_user_id)
            content = Content.objects.get(id=content_id)
            set_current_user(test_user)
            content_generator = ContentGenerator(
                PlaybookHandler(content.playbook), content
            )
            content_generator.set_settings(
                num_of_variations=num_of_variations,
                foundation_model=foundation_model,
                joint_generation=True,
                skip_num_of_variations_check=True,
                no_retry=True,
                save_variations=False,
            )
            for i in range(num_of_rounds):
                try:
                    variations = content_generator.gen()
                    features = content_generator._features
                    features["targets"] = content.content_params.get("targets", [])
                    result.add_generation(content.id, i, variations, features)
                except Exception as e:
                    logging.error(f"Error generating content: {e}")
                    result.add_generation_failure(content.id, i, str(e))

        test_user_id = 800  # tofuadmin-test-eval

        foundation_model = self.foundation_model
        num_of_variations = self.num_of_variations

        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = []

            for content in self.contents_to_evaluate:
                futures.append(
                    executor.submit(
                        process_content,
                        test_user_id,
                        content.id,
                        num_of_variations,
                        foundation_model,
                        self._result,
                        self.num_of_rounds,
                    )
                )

            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # Optionally handle return value or exceptions
                except Exception as e:
                    print(f"Error processing content: {e}")

    @traceable
    def evaluate(self) -> str:
        if not self.list_content_groups and not self.list_contents:
            return "No content groups to evaluate"
        if not self.num_of_variations:
            return "Number of variations not set"
        if not self.rules and not self._no_evaluation:
            self.rules = predefined_evaluators.get_all_predefined_rules()
            self._result.set_rules(self.rules)

            if not self.rules:
                return "No rules to evaluate"

        self._resolve_contents()
        self._result.set_metadata(
            {
                "content_groups": self.list_content_groups,
                "contents": self.list_contents,
                "num_of_variations": self.num_of_variations,
                "num_of_rounds": self.num_of_rounds,
                "foundation_model": self.foundation_model,
            }
        )

        # Create a logger
        root_logger = logging.getLogger()
        # Set the logging level to ERROR
        root_logger.setLevel(logging.ERROR)

        self._generate()
        if not self._no_evaluation:
            if self._is_annotation:
                self._result.set_annotation()

            self._result.evaluate()

            if self.add_to_dataset:
                self._result._add_to_dataset()
        report = self._result.gen_report()
        logging.error(f"report: {report}")
        return report
