import copy
import logging
import traceback
import uuid
from abc import ABC

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...gdrive_utils import get_gslides_text_contents
from ...llms import model_is_json_enabled
from ...models import (
    AssetInfo,
    Campaign,
    Content,
    ContentGroup,
    ContentVariation,
    Playbook,
)
from ...playbook_build.doc_loader import get_template
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionCategory
from ...shared_types import ContentType
from ...slides.template import (
    contains_placeholders,
    get_pptx_text_contents,
    get_slides_template_content,
    validate_slides_template_source,
)
from ...thread_locals import get_current_user
from ...utils import (
    contains_words_substring,
    extract_slate_editor_text,
    is_google_slides_url,
    is_pptx_s3_file,
    is_s3_url,
    parse_s3_presigned_url,
    try_parse_int,
)
from ...validator.campaign_validator import CampaignGoal
from .data_wrapper_campaign_v3 import get_campaign_v3_content_group_asset_params


class BaseContentWrapper(ABC):
    def __init__(self) -> None:
        pass

    @staticmethod
    def from_data_instance(data_instance):
        if isinstance(data_instance, Playbook):
            return PlaybookWrapper(data_instance)
        elif isinstance(data_instance, Campaign):
            return CampaignWrapper(data_instance)
        elif isinstance(data_instance, ContentGroup):
            return ContentGroupWrapper(data_instance)
        elif isinstance(data_instance, Content):
            return ContentWrapper(data_instance)
        elif isinstance(data_instance, dict):
            return ComponentWrapper(data_instance)
        else:
            raise ValueError(
                f"Unexpected data_instance for DataWrapperHolder: {data_instance}"
            )

    @staticmethod
    def from_content_component(content_instance, component_key, component=None):
        if not isinstance(content_instance, Content):
            raise ValueError(
                f"Unexpected content_instance for DataWrapperHolder: {content_instance}"
            )
        # if (
        #     not isinstance(component_key, str)
        #     or component_key not in content_instance.components
        # ):
        #     raise ValueError(
        #         f"Unexpected component_key for DataWrapperHolder: {component_key} for content_instance: {content_instance}"
        #     )
        # TODO: put checkers
        return ComponentWrapper(content_instance, component_key, component)

    @staticmethod
    def from_data_instance_and_unsaved_data(data_instance, unsaved_data):
        if not isinstance(data_instance, ContentGroup):
            raise ValueError(
                f"Unsaved data is only expected for ContentGroup but given: {data_instance}"
            )
        return ContentGroupUnsavedDataWrapper(data_instance, unsaved_data)

    def _convert_asset_params_to_list(self, asset_params):
        if not asset_params:
            return []
        if isinstance(asset_params, list):
            return asset_params
        elif isinstance(asset_params, dict):
            asset_params_list = []
            for key, value in asset_params.items():
                if value is None:
                    # asset list case, parse the assets of the asset group.
                    asset_infos = AssetInfo.objects.filter(
                        asset_info_group__asset_info_group_key=key,
                        asset_info_group__playbook=self.playbook_instance,
                    )
                    for asset_info in asset_infos:
                        asset_params_list.append(
                            {
                                "assets": {key: asset_info.asset_key},
                                "instruction": None,
                                "meta": "repurpose_anchor_content",
                            }
                        )
                elif isinstance(value, list):
                    for k2 in value:
                        asset_params_list.append(
                            {
                                "assets": {key: k2},
                                "instruction": None,
                                "meta": "repurpose_anchor_content",
                            }
                        )
                else:
                    asset_params_list.append(
                        {
                            "assets": {key: value},
                            "instruction": None,
                            "meta": "repurpose_anchor_content",
                        }
                    )
            return asset_params_list
        else:
            logging.error(
                f"Unexpected asset_params for DataWrapperHolder: {asset_params}"
            )
        return []

    @property
    def content_group_instance(self):
        return None

    @property
    def content_instance(self):
        return None

    @property
    def has_components(self):
        return False

    @property
    def target_params(self):
        logging.error(f"target_params is not expected to be queried for {self}")
        return {}

    @property
    def content_type(self):
        logging.error(f"content_type is not expected to be queried for {self}")
        return ""

    @property
    def components(self):
        logging.error(f"components is not expected to be queried for {self}")
        return {}


class PlaybookWrapper(BaseContentWrapper):
    def __init__(self, playbook) -> None:
        super().__init__()
        self.playbook = playbook

    @property
    def playbook_instance(self):
        return self.playbook

    @property
    def company_name(self):
        company_name = None

        company_object = self.playbook.company_object
        for doc in company_object.docs.values():
            if doc.get("meta", {}).get("field_name") == "Company Name":
                company_name = doc.get("value", "")
                break
        if not company_name:
            logging.error(f"Company name not found for playbook: {self.playbook.id}")
        return company_name or "[MyCompany]"

    @property
    def aggregated_asset_params(self):
        return []

    @property
    def aggregated_custom_instructions(self):
        return []

    @property
    def target_params(self):
        return {}

    @property
    def content_type(self):
        return ""

    @property
    def campaign_instance(self):
        return None

    @property
    def campaign_goal(self):
        return None


class CampaignWrapper(PlaybookWrapper):
    def __init__(self, campaign) -> None:
        super().__init__(
            campaign.playbook,
        )
        self._campaign_instance = campaign

    @property
    def campaign_instance(self):
        return self._campaign_instance

    @property
    def campaign_goal(self):
        return self.campaign_instance.campaign_params.get(
            "campaign_goal", "Personalization"
        )

    @property
    def content_goal(self):
        return self.campaign_goal

    @property
    def is_repurpose(self):
        return self.campaign_goal == CampaignGoal.Repurposing

    @property
    def _campaign_asset_params(self):
        return self._convert_asset_params_to_list(
            self.campaign_instance.campaign_params.get("assets", {})
        )

    @property
    def _campaign_custom_instructions(self):
        return self.campaign_instance.campaign_params.get("custom_instructions", [])

    @property
    def campaign_foundation_model(self):
        if not self.campaign_instance.campaign_params:
            return ""
        foundation_model = self.campaign_instance.campaign_params.get(
            "foundation_model", ""
        )
        return foundation_model

    @property
    def aggregated_asset_params(self):
        return self._campaign_asset_params

    @property
    def aggregated_custom_instructions(self):
        return self._campaign_custom_instructions

    @property
    def num_of_variations(self):
        return self.campaign_instance.campaign_params.get("num_of_variations", 3)

    @property
    def is_campaign_v3(self):
        return self.campaign_instance.campaign_params.get("is_campaign_v3", False)


# TODO: resolve the duplicated code for ContentGroupWrapper
class ContentGroupUnsavedDataWrapper(CampaignWrapper):
    def __init__(self, content_group, unsaved_data) -> None:
        super().__init__(
            content_group.campaign,
        )
        self._content_group_instance = content_group
        self._unsaved_data = unsaved_data

    @property
    def has_components(self):
        return len(self.components) > 0

    @property
    def content_group_instance(self):
        return self._content_group_instance

    @property
    def content_group_params(self):
        return self.content_group_instance.content_group_params or {}

    @property
    def components(self):
        result = self._unsaved_data.get("components", {})
        return result

    @property
    def content_type(self):
        return self.content_group_params.get("content_type", "")

    @property
    def content_source_format(self):
        return self.content_group_params.get("content_source_format", "")

    @property
    def content_source_copy(self):
        return self._unsaved_data.get("content_source_copy", "")

    @property
    def is_email(self):
        return self.content_type in (ContentType.EmailMarketing, ContentType.EmailSDR)

    @property
    def is_linkedin_ads_v2(self):
        return self.content_type == ContentType.AdCampaignLinkedin

    @property
    def content_group_foundation_model(self):
        foundation_model = self.content_group_params.get("foundation_model", "")
        return foundation_model

    @property
    def _content_group_custom_instructions(self):
        result = self.content_group_params.get("custom_instructions", []) or []
        result += self._unsaved_data.get("custom_instructions", []) or []
        return result

    @property
    def _content_group_asset_params(self):
        if self.content_group_params is None:
            return []
        asset_params = self.content_group_params.get("assets", {}) or {}
        asset_params = self._convert_asset_params_to_list(asset_params)
        if self.is_campaign_v3:
            asset_params.extend(
                get_campaign_v3_content_group_asset_params(self.content_group_instance)
            )

        if self.is_in_content_group_collection:
            content_collection_instructions = (
                self.content_group_params.get("content_collection", {}) or {}
            ).get("content_collection_instructions", []) or []
            for instruction in content_collection_instructions:
                instruction["meta"] = "content_collection_instructions"

            return asset_params + content_collection_instructions
        return asset_params

    @property
    def content_collection_instructions(self):
        custom_instructions = (
            self.content_group_params.get("content_collection", {}) or {}
        ).get("custom_instructions", []) or []
        return custom_instructions

    @property
    def aggregated_asset_params(self):
        campaign_asset_params = self._campaign_asset_params or []
        content_group_asset_params = self._content_group_asset_params or []

        return campaign_asset_params + content_group_asset_params

    @property
    def aggregated_custom_instructions(self):
        campaign_custom_instructions = self._campaign_custom_instructions or []
        content_group_custom_instructions = (
            self._content_group_custom_instructions or []
        )

        return campaign_custom_instructions + content_group_custom_instructions

    @property
    def template_settings(self):
        template_settings_data = (
            self.content_group_params.get("template_settings", {}) or {}
        )
        if not isinstance(template_settings_data, dict):
            logging.error(
                f"Template settings is not a dictionary: {template_settings_data}"
            )
            return {}
        return template_settings_data

    @property
    def tone_reference_v2(self):
        action_instance = self.action_instance
        if not action_instance:
            return None
        template_data = action_instance.inputs.get("template", {})
        if not template_data:
            return None

        try:
            template_tofu_data = TofuDataListHandler.get_template(template_data)
        except Exception as e:
            return None
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(
            template_tofu_data.tone_reference_v2.tone_assets
        )
        if not asset_ids and not asset_group_ids:
            return None
        return template_tofu_data.tone_reference_v2

    @property
    def content_word_count(self):
        if self.is_campaign_v3:
            action_instance = self.content_group_instance.action
            if not action_instance:
                logging.error(
                    f"Action instance not found for content group: {self.content_group_instance.id}"
                )
                return 0
            if not action_instance.inputs.get("content_word_count", {}):
                logging.info(
                    f"Content word count not found for action instance: {action_instance.id}"
                )
                return 0
            content_word_count = TofuDataListHandler.get_int_value(
                action_instance.inputs.get("content_word_count", {})
            )
            return content_word_count
        else:
            return self.content_group_params.get("content_word_count", 0)

    @property
    def text_gen_components(self) -> dict:
        return {
            k: v
            for k, v in self.components.items()
            if v.get("meta", {}).get("type", "text") == "text"
            and v.get("meta", {}).get("component_type", "") != "edited"
        }

    @property
    def template(self):
        return self._unsaved_data.get("template", "")

    @property
    def is_in_content_group_collection(self):
        return bool(self.content_group_params.get("content_collection", {}) or {})

    @property
    def is_tofu_lite(self):
        return self.content_group_params.get("tofu_lite", False)

    @property
    def content_goal(self):
        if self.is_campaign_v3:
            return self.content_group_params.get("content_goal", "")
        else:
            return self.campaign_goal

    # TODO: remove this property later
    @property
    def is_repurpose(self):
        return self.content_goal == CampaignGoal.Repurposing

    @property
    def target_params(self):
        if self.content_goal == CampaignGoal.SeqPersonalizeTemplate:
            result = self.content_group_params.get("selected_targets", {}) or {}
            if not result or not isinstance(result, dict) or len(result) != 1:
                logging.error(
                    f"Invalid selected targets found for content group: {self.content_group_instance.id}: {result}"
                )
                return {}
            first_key = list(result.keys())[0]
            values = result[first_key]
            if not values:
                logging.error(
                    f"Invalid selected targets found for content group: {self.content_group_instance.id}: {result}"
                )
                return {}
            if isinstance(values, list):
                return {first_key: values[0]}
            else:
                if not isinstance(values, str):
                    logging.error(
                        f"Invalid selected targets found for content group: {self.content_group_instance.id}: {result}"
                    )
                    return {}
                return {first_key: values}
        return super().target_params


class ContentGroupWrapper(CampaignWrapper):
    def __init__(self, content_group) -> None:
        super().__init__(
            content_group.campaign,
        )
        self._content_group_instance = content_group

    @property
    def has_components(self):
        return True

    @property
    def content_group_instance(self):
        return self._content_group_instance

    @property
    def content_group_params(self):
        return self.content_group_instance.content_group_params or {}

    @property
    def action_instance(self):
        return self.content_group_instance.action

    @property
    def campaign_goal(self):
        if self.is_campaign_v3:
            action_instance = self.action_instance
            if not action_instance:
                logging.error(
                    f"Action instance not found for content group: {self.content_group_instance.id}"
                )
                return "Personalization"
            return (
                "Personalization"
                if action_instance.action_category
                == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_PERSONALIZE)
                else "Repurpose Content"
            )
        else:
            return self.campaign_instance.campaign_params.get(
                "campaign_goal", "Personalization"
            )

    @property
    def is_in_content_group_collection(self):
        return bool(self.content_group_params.get("content_collection", {}) or {})

    @property
    def components(self):
        return self.content_group_instance.components

    @property
    def content_type(self):
        return self.content_group_params.get("content_type", "")

    @property
    def content_source_format(self):
        return self.content_group_params.get("content_source_format", "")

    @property
    def content_source_copy(self):
        return self.content_group_params.get("content_source_copy", "")

    @property
    def is_email(self):
        return self.content_type in (ContentType.EmailMarketing, ContentType.EmailSDR)

    @property
    def is_linkedin_ads_v2(self):
        return self.content_type == ContentType.AdCampaignLinkedin

    @property
    def content_group_foundation_model(self):
        foundation_model = self.content_group_params.get("foundation_model", "")
        return foundation_model

    # @property
    # def target_params(
    #     self,
    # ):  # TODO: revisit this since this is only used for template generation
    #     return self.content_group_params.get("targets", {})

    @property
    def _content_group_custom_instructions(self):
        return self.content_group_params.get("custom_instructions", []) or []

    @property
    def _content_group_asset_params(self):
        if not self.content_group_params:
            return []
        asset_params = self.content_group_params.get("assets", {}) or {}
        asset_params = self._convert_asset_params_to_list(asset_params)
        if self.is_campaign_v3:
            asset_params.extend(
                get_campaign_v3_content_group_asset_params(self.content_group_instance)
            )

        if self.is_in_content_group_collection:
            content_collection_instructions = (
                self.content_group_params.get("content_collection", {}) or {}
            ).get("content_collection_instructions", []) or []
            for instruction in content_collection_instructions:
                instruction["meta"] = "content_collection_instructions"

            return asset_params + content_collection_instructions
        return asset_params

    @property
    def content_collection_instructions(self):
        content_collection_data = (
            self.content_group_params.get("content_collection", {}) or {}
        )
        if not content_collection_data:
            return []
        custom_instructions = (
            content_collection_data.get("custom_instructions", []) or []
        )
        if not custom_instructions:
            return []
        return custom_instructions

    @property
    def template_instructions(self):
        return self.content_group_params.get("template_instructions", [])

    @property
    def aggregated_asset_params(self):
        campaign_asset_params = self._campaign_asset_params or []
        content_group_asset_params = self._content_group_asset_params or []

        return campaign_asset_params + content_group_asset_params

    @property
    def aggregated_custom_instructions(self):
        campaign_custom_instructions = self._campaign_custom_instructions or []
        content_group_custom_instructions = (
            self._content_group_custom_instructions or []
        )

        return campaign_custom_instructions + content_group_custom_instructions

    @property
    def template_settings(self):
        template_settings_data = (
            self.content_group_params.get("template_settings", {}) or {}
        )
        if not isinstance(template_settings_data, dict):
            logging.error(
                f"Template settings is not a dictionary: {template_settings_data}"
            )
            return {}
        return template_settings_data

    @property
    def tone_reference_v2(self):
        action_instance = self.action_instance
        if not action_instance:
            return None
        template_data = action_instance.inputs.get("template", {})
        if not template_data:
            return None

        try:
            template_tofu_data = TofuDataListHandler.get_template(template_data)
        except Exception as e:
            return None
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(
            template_tofu_data.tone_reference_v2.tone_assets
        )
        if not asset_ids and not asset_group_ids:
            return None
        return template_tofu_data.tone_reference_v2

    @property
    def content_word_count(self):
        if self.is_campaign_v3:
            action_instance = self.content_group_instance.action
            if not action_instance:
                logging.error(
                    f"Action instance not found for content group: {self.content_group_instance.id}"
                )
                return 0
            if not action_instance.inputs.get("content_word_count", {}):
                logging.info(
                    f"Content word count not found for action instance: {action_instance.id}"
                )
                return 0
            content_word_count = TofuDataListHandler.get_int_value(
                action_instance.inputs.get("content_word_count", {})
            )
            return content_word_count

        else:
            return self.content_group_params.get("content_word_count", 0)

    @property
    def text_gen_components(self) -> dict:
        return {
            k: v
            for k, v in self.components.items()
            if v.get("meta", {}).get("type", "text") == "text"
            and v.get("meta", {}).get("component_type", "") != "edited"
        }

    @property
    def personalization_template(self):
        template_content_source_copy = self.content_group_params.get(
            "template_content_source_copy", None
        )
        if not template_content_source_copy:
            return {}
        personalization_template = get_template(template_content_source_copy)
        return personalization_template or {}

    @property
    def should_use_slides_template(self):
        repurpose_template_content_source_copy = self.content_group_params.get(
            "repurpose_template_content_source_copy", None
        )
        if not repurpose_template_content_source_copy:
            return False
        if (
            not self.content_type == ContentType.SalesDeck
            and not self.content_type == ContentType.SlideDeck
        ):
            return False

        is_valid, _, _ = validate_slides_template_source(
            repurpose_template_content_source_copy
        )
        return is_valid

    @property
    def uses_google_slides_template(self):
        repurpose_template_content_source_copy = self.content_group_params.get(
            "repurpose_template_content_source_copy", None
        )
        if not repurpose_template_content_source_copy:
            return False
        return is_google_slides_url(repurpose_template_content_source_copy)

    @property
    def uses_pptx_template(self):
        """Check if the repurpose template is a PPTX file on S3.
        Returns:
            bool: True if template is a PPTX file on S3, False otherwise
        """
        repurpose_template_content_source_copy = self.content_group_params.get(
            "repurpose_template_content_source_copy", None
        )
        if not repurpose_template_content_source_copy:
            return False
        if is_s3_url(repurpose_template_content_source_copy):
            source_file_name, _, _ = parse_s3_presigned_url(
                repurpose_template_content_source_copy
            )
            return is_pptx_s3_file(source_file_name)
        return False

    @property
    def should_use_manual_placeholder(self):
        if not self.should_use_slides_template:
            return False
        try:
            repurpose_template_content_source_copy = self.content_group_params.get(
                "repurpose_template_content_source_copy", None
            )
            if not repurpose_template_content_source_copy:
                return False
            if is_google_slides_url(repurpose_template_content_source_copy):
                return contains_placeholders(
                    get_gslides_text_contents(repurpose_template_content_source_copy)
                )
            elif is_s3_url(repurpose_template_content_source_copy):
                return contains_placeholders(
                    get_pptx_text_contents(repurpose_template_content_source_copy)
                )
            else:
                return False
        except Exception as e:
            logging.error(f"Error checking if should use manual placeholder: {e}")
            return False

    @property
    def repurpose_template(self):
        repurpose_template_content_source_copy = self.content_group_params.get(
            "repurpose_template_content_source_copy", None
        )
        if not repurpose_template_content_source_copy:
            return {}

        if self.should_use_slides_template:
            try:
                repurpose_template = get_slides_template_content(
                    repurpose_template_content_source_copy
                )
            except Exception as e:
                logging.error(f"Error fetching repurpose template: {e}")
                return {}
            return repurpose_template or {}
        else:
            try:
                repurpose_template = get_template(
                    repurpose_template_content_source_copy
                )
            except Exception as e:
                logging.error(f"Error fetching repurpose template: {e}")
                return {}
        if self.is_email:
            subject_line_only_template = {}
            subject_line_only_content_source_copy = self.content_group_params.get(
                "subject_line_only_content_source_copy", None
            )
            if subject_line_only_content_source_copy:
                subject_line_only_template = get_template(
                    subject_line_only_content_source_copy
                )
                if isinstance(subject_line_only_template, list):
                    try:
                        subject_template = None
                        for item in subject_line_only_template:
                            if item.get("type", "") == "subject-line-only":
                                extracted_text = extract_slate_editor_text(item)
                                subject_template = {"subjectLine": extracted_text}
                                break
                        if subject_template:
                            subject_line_only_template = subject_template
                        else:
                            subject_line_only_template = {}
                    except Exception as e:
                        logging.error(
                            f"Error extracting subject line only template: {e}"
                        )
                        subject_line_only_template = {}
                elif isinstance(subject_line_only_template, dict):
                    pass
                else:
                    subject_line_only_template = {}

            return {
                "subjectLine": subject_line_only_template.get("subjectLine", "")
                or repurpose_template.get("subjectLine", ""),
                "body": repurpose_template.get("text", "")
                or repurpose_template.get("body", ""),
            }
        else:
            return repurpose_template or {}

    @property
    def is_tofu_lite(self):
        return self.content_group_params.get("tofu_lite", False)

    @property
    def content_goal(self):
        if self.is_campaign_v3:
            return self.content_group_params.get("content_goal", "")
        else:
            return self.campaign_goal

    @property
    def is_repurpose(self):
        return self.content_goal == CampaignGoal.Repurposing

    @property
    def target_params(self):
        if self.content_goal == CampaignGoal.SeqPersonalizeTemplate:
            result = self.content_group_params.get("selected_targets", {}) or {}
            if not result or not isinstance(result, dict) or len(result) != 1:
                logging.error(
                    f"Invalid selected targets found for content group: {self.content_group_instance.id}: {result}"
                )
                return {}
            first_key = list(result.keys())[0]
            values = result[first_key]
            if not values:
                logging.error(
                    f"Invalid selected targets found for content group: {self.content_group_instance.id}: {result}"
                )
                return {}
            if isinstance(values, list):
                return {first_key: values[0]}
            else:
                if not isinstance(values, str):
                    logging.error(
                        f"Invalid selected targets found for content group: {self.content_group_instance.id}: {result}"
                    )
                    return {}
                return {first_key: values}
        return super().target_params


class ContentWrapper(ContentGroupWrapper):
    def __init__(self, content) -> None:
        if not content.content_group:
            raise ValueError(f"Unexpected content for DataWrapperHolder: {content}")
        super().__init__(
            content.content_group,
        )
        self.content = content

    @property
    def content_instance(self):
        return self.content

    @property
    def target_params(self):
        return self.content.content_params.get("targets", {})

    def get_component_current_value(self, component_key, idx=0):
        content_variation = ContentVariation.objects.get(content=self.content)
        if not content_variation:
            logging.error(
                f"Content variation not found for content: {self.content.id} when fetching component current value."
            )
            return ""
        if idx == 0:
            cur_value = (
                (
                    (content_variation.variations.get(component_key, {}) or {}).get(
                        "meta", {}
                    )
                    or {}
                ).get("current_version", {})
                or {}
            ).get("text", "")
        else:
            variations = (
                (content_variation.variations.get(component_key, {}) or {}).get(
                    "meta", {}
                )
                or {}
            ).get("variations", []) or []
            if not variations:
                logging.error(
                    f"Component {component_key} does not have variations {content_variation.variations}"
                )
                return ""
            if len(variations) < idx:
                logging.error(
                    f"Component {component_key} does not have variation {idx}"
                )
                return ""
            cur_value = variations[idx - 1].get("text", "")
        if not cur_value:
            logging.error(
                f"Component {component_key} does not have value {content_variation.variations}"
            )
        return cur_value


class ComponentWrapper(ContentWrapper):
    def __init__(self, content, component_key, component=None):
        super().__init__(content)
        self.component_key = component_key
        self.component = component
        if self.component_key not in self.components and not self.component:
            raise ValueError(
                f"Unexpected component for DataWrapperHolder: {self.component_key}"
            )

    @property
    def has_components(self):
        return False

    @property
    def content_group_component_data(self):
        if self.component_key not in self.content_group_instance.components:
            return {}
            # raise ValueError(
            #     f"Unexpected component for DataWrapperHolder: {self.component_key}"
            # )
        return self.content_group_instance.components[self.component_key]

    @property
    def content_group_component_asset_params(self):
        return self._convert_asset_params_to_list(
            self.content_group_component_data.get("meta", {})
            .get("component_params", {})
            .get("assets", {})
        )

    @property
    def aggregated_asset_params(self):
        content_group_asset_params = super().aggregated_asset_params or []
        component_asset_params = self.content_group_component_asset_params or []

        return content_group_asset_params + component_asset_params

    @property
    def content_group_component_custom_instructions(self):
        return (
            (self.content_group_component_data.get("meta", {}) or {}).get(
                "component_params", {}
            )
            or {}
        ).get("custom_instructions", []) or []

    @property
    def aggregated_custom_instructions(self):
        content_custom_instructions = super().aggregated_custom_instructions or []
        component_custom_instructions = (
            self.content_group_component_custom_instructions or []
        )
        return content_custom_instructions + component_custom_instructions


class DummyGenSettings:
    def __init__(self) -> None:
        pass


class DefaultGenSettings:
    def __init__(self, data_wrapper) -> None:
        self._data_wrapper = data_wrapper
        self._enable_custom = False
        self._content_collection_plan_gen = False

    @property
    def enable_custom(self):
        return self._enable_custom

    @property
    def content_collection_plan_gen(self):
        return self._content_collection_plan_gen

    @property
    def session_id(self):
        return None

    @property
    def config_map(self):
        return {}


class ContentGenSettings:
    def __init__(
        self,
        data_wrapper,
        num_of_variations=None,
        foundation_model=None,
        enable_custom=True,
        full_page_html_gen=False,
        joint_generation=False,
        template_generation=False,
        free_gen=False,
        content_collection_plan_gen=False,
        components=None,
        session_id=None,
        session_tag=None,
        skip_num_of_variations_check=False,
        no_retry=False,
        is_long_form_generation=False,
        is_template_personalization=False,
        template_length=None,
        template_purpose=None,
        save_variations=None,
        is_json_output=False,
    ):
        self._data_wrapper = data_wrapper

        self._request_id = str(uuid.uuid4())

        self._num_of_variations = num_of_variations
        self._skip_num_of_variations_check = skip_num_of_variations_check
        self._foundation_model = foundation_model
        self._enable_custom = enable_custom
        self._full_page_html_gen = full_page_html_gen
        self._joint_generation = joint_generation
        self._template_generation = template_generation
        self._free_gen = free_gen
        self._content_collection_plan_gen = content_collection_plan_gen
        self._components = components
        self._session_id = session_id
        self._session_tag = session_tag
        self._no_retry = no_retry
        self._is_long_form_generation = is_long_form_generation
        self._long_form_output_length = 0
        self._is_template_personalization = is_template_personalization
        self._template_length = template_length
        self._template_purpose = template_purpose
        if save_variations is None:
            raise Exception("save_variations must be provided")
        self._save_variations = save_variations
        self._is_json_output = is_json_output
        self._resolve()

    def set(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, f"_{key}"):
                setattr(self, f"_{key}", value)
            else:
                logging.error(f"Unexpected key {key} for ContentGenSettings.set.")
        self._resolve()

    # TODO: refactor the components add logic after migration, perhaps to this wrapper
    def update_components(self, components):
        self._components = components
        self._resolve_components()
        self._resolve_joint_generation()

    @property
    def config_map(self):
        config = {
            "num_of_variations": self._num_of_variations,
            "foundation_model": self._foundation_model,
            "enable_custom": self._enable_custom,
            "full_page_html_gen": self._full_page_html_gen,
            "joint_generation": self._joint_generation,
            "template_generation": self._template_generation,
            "free_gen": self._free_gen,
            "content_collection_plan_gen": self._content_collection_plan_gen,
            "components": self._components,
            "is_template_personalization": self._is_template_personalization,
        }
        if self._session_id:
            config["session_id"] = self._session_id
            config["session_tag"] = self._session_tag
        return config

    @property
    def request_id(self):
        return self._request_id

    @property
    def enable_custom(self):
        return self._enable_custom

    @property
    def foundation_model(self):
        return self._foundation_model

    @property
    def full_page_html_gen(self):
        return self._full_page_html_gen

    @property
    def num_of_variations(self):
        return self._num_of_variations

    @property
    def joint_generation(self):
        return self._joint_generation

    @property
    def template_generation(self):
        return self._template_generation

    @property
    def free_gen(self):
        return self._free_gen

    @property
    def session_id(self):
        return self._session_id

    @property
    def session_tag(self):
        return self._session_tag

    @property
    def content_collection_plan_gen(self):
        return self._content_collection_plan_gen

    @property
    def no_retry(self):
        return self._no_retry

    @property
    def is_long_form_generation(self):
        return self._is_long_form_generation

    @property
    def long_form_output_length(self):
        return self._long_form_output_length

    @property
    def is_template_personalization(self):
        return self._is_template_personalization

    @property
    def template_length(self):
        return self._template_length

    @property
    def template_purpose(self):
        return self._template_purpose

    @property
    def content_gen_components(self):
        return self._content_gen_components

    @property
    def save_variations(self):
        return self._save_variations

    @property
    def is_json_output(self):
        return self._is_json_output

    def _resolve(self):
        try:
            # order matters
            self._resolve_components()
            self._resolve_foundation_model()
            self._resolve_joint_generation()
            self._resolve_long_form_generation()
            self._resolve_num_of_variations()
            self._resolve_user_settings()
            self._resolve_full_page_html_gen()
            self._resolve_is_json_output()
            self._validate()
        except Exception as e:
            logging.error(
                f"Error resolving ContentGenSettings: {e}. {traceback.format_exc()}"
            )

    def _validate(self):
        self._validate_foundation_model()
        self._validate_num_of_variations()

    def _validate_foundation_model(self):
        valid_model_names = (
            "gpt",
            "claude",
            "gemini",
            "mock",
            "o1",
            "deepseek",
            "o3",
            "o4",
        )
        if not any(
            model_name in self._foundation_model for model_name in valid_model_names
        ):
            raise Exception(
                f"Invalid foundation model: {self._foundation_model}. Must be one of {valid_model_names}."
            )

    def _validate_num_of_variations(self):
        if not isinstance(self._num_of_variations, int) or self._num_of_variations < 1:
            raise Exception(
                f"Invalid number of variations: {self._num_of_variations}. Must be an integer greater than 0."
            )

    def _resolve_components(self):
        if not self._components and self._data_wrapper.has_components:
            self._components = self._data_wrapper.components
        self._content_gen_components = {}
        if self._components:

            def is_component_for_generation(component: dict) -> bool:
                component_meta = component.get("meta", {})
                return (
                    component_meta.get("type", "text") == "text"
                    and component_meta.get("component_type", "") != "edited"
                )

            for component_id, component in self._components.items():
                if is_component_for_generation(component):
                    self._content_gen_components[component_id] = component

    def _resolve_foundation_model(self):
        # foundation_model
        # TODO: use context manager
        current_user = get_current_user()
        if current_user and current_user.is_eligible_for_mock():
            self._foundation_model = "mock"
            return
        # assumption: possible sources: passed by caller, content, content_group, campaign, or set at user level
        # priority: caller > content_group > content >  campaign > user setting
        if not self._foundation_model and self._data_wrapper.content_group_instance:
            self._foundation_model = self._data_wrapper.content_group_foundation_model
        if not self._foundation_model and self._data_wrapper.campaign_instance:
            self._foundation_model = self._data_wrapper.campaign_foundation_model

        if not self._foundation_model:
            creator = self._data_wrapper.content_instance.creator
            # if repurposing or template_gen, use the repurpose model
            if (
                self._data_wrapper.campaign_goal == "Repurpose Content"
                or self.template_generation
            ):
                self._foundation_model = (getattr(creator, "context", None) or {}).get(
                    "model_for_repurpose", ""
                )

            if not self._foundation_model:
                self._foundation_model = (getattr(creator, "context", None) or {}).get(
                    "model", ""
                )

        # TODO: double check what default model we may need
        if not self._foundation_model:
            self._foundation_model = "gpt-4o-2024-11-20"

        if (
            self._template_generation
            and self._data_wrapper.is_email
            and not model_is_json_enabled(self._foundation_model)
        ):
            self._foundation_model = "gpt-4o-2024-11-20"

    def _resolve_joint_generation(self):
        if self.template_generation:
            self._joint_generation = False
            return
        if self._data_wrapper.campaign_goal == "Repurpose Content" and not (
            self.free_gen or self.content_collection_plan_gen
        ):
            if (
                self._data_wrapper.content_type
                in (
                    ContentType.EmailMarketing,
                    ContentType.EmailSDR,
                )
                or self._data_wrapper.is_linkedin_ads_v2
            ):
                self._joint_generation = True
                return
            else:
                self._joint_generation = False
                return

        if not self._components:
            self._joint_generation = True  # TODO
            return
        self._joint_generation = self._resolve_component_joint_generation()

    def _resolve_component_joint_generation(self):

        num_text_all_components = len(self._data_wrapper.text_gen_components)
        return (
            model_is_json_enabled(self._foundation_model)
            and num_text_all_components > 1
            and (
                self._data_wrapper.campaign_goal == "Personalization"
                or self._joint_generation
            )
        )

    def _resolve_num_of_variations(self):
        # number_of_variations
        # assumption: possible sources: caller, campaign, content.
        # priority: caller > campaign > content
        if not self._num_of_variations:
            if self._data_wrapper.campaign_instance:
                self._num_of_variations = (
                    self._data_wrapper.campaign_instance.campaign_params.get(
                        "num_of_variations", 2
                    )
                )
        if not self._num_of_variations:
            self._num_of_variations = 2

        if self._is_long_form_generation:
            self._num_of_variations = 1

    def _resolve_long_form_generation(self):
        _LONG_FORM_MIN_SIZE = 2500
        _LONG_FORM_MAX_SIZE = 10000

        # only allow long form generation for repurposing blog post, whitepaper, or ebook.
        if self._data_wrapper.campaign_goal != "Repurpose Content":
            return
        if self._data_wrapper.content_type not in (
            ContentType.BlogPost,
            ContentType.Whitepaper,
            ContentType.EBook,
            ContentType.CaseStudy,
            ContentType.Other,
        ):
            return

        content_word_count, success = try_parse_int(
            self._data_wrapper.content_word_count
        )
        if success and content_word_count > 0:
            content_word_count = min(content_word_count, _LONG_FORM_MAX_SIZE)
            self._is_long_form_generation = True
            self._long_form_output_length = content_word_count
            return

        word_count_custom_prompts = [
            prompt.get("instruction", "") or ""
            for prompt in self._data_wrapper.aggregated_custom_instructions
            if not prompt.get("assets")
            and contains_words_substring(
                prompt.get("instruction", "") or "",
                _LONG_FORM_MIN_SIZE,
                _LONG_FORM_MAX_SIZE,
            )
        ]
        if not word_count_custom_prompts:
            return
        if len(word_count_custom_prompts) > 1:
            logging.error(
                f"Multiple long form custom prompts found: {word_count_custom_prompts}. Using the first one."
            )
        long_form_prompt = word_count_custom_prompts[0] or ""
        match_str = contains_words_substring(
            long_form_prompt, _LONG_FORM_MIN_SIZE, _LONG_FORM_MAX_SIZE
        )
        if match_str:
            # should be of the form "xx words"
            self._is_long_form_generation = True
            self._long_form_output_length = int(match_str.split(" ")[0])

    def _resolve_user_settings(self):
        # TODO: use context manager
        self.user = str(get_current_user())
        use_company_summary_users = [
            "selectstar",
        ]
        self.use_company_summary = self.user in use_company_summary_users

    def _resolve_full_page_html_gen(self):
        if (
            not self._data_wrapper.content_group_instance
            or not self._data_wrapper.content_source_format
            or not self._data_wrapper.campaign_goal
            or not self._components
        ):
            self._full_page_html_gen = False
            return

        self._full_page_html_gen = (
            self._data_wrapper.content_source_format == "Html"
            and self._data_wrapper.campaign_goal == "Repurpose Content"
            and (self._components and len(self._components) > 1)
        )

    def _resolve_is_json_output(self):
        self._is_json_output = (
            self.template_generation
            and self._data_wrapper.content_type
            in (ContentType.EmailMarketing, ContentType.EmailSDR)
        ) or (
            self._data_wrapper.campaign_goal != "Repurpose Content"
            and self.joint_generation
            and (self._components and len(self._components) > 1)
        )


class GenerateEnv:
    def __init__(self, data_wrapper, gen_settings) -> None:
        self._data_wrapper = data_wrapper
        self._gen_settings = gen_settings

    def copy(self):
        settings = copy.deepcopy(self._gen_settings)
        return GenerateEnv(self._data_wrapper, settings)


class DataWrapperHolder:
    def __init__(self, gen_env) -> None:
        self._gen_env = gen_env
        self._data_wrapper = gen_env._data_wrapper
        self._gen_settings = gen_env._gen_settings
        self._features = {}

    # def __getattr__(self, name):
    #     """Delegate attribute lookup to data_wrapper instance."""
    #     try:
    #         return getattr(self._data_wrapper, name)
    #     except AttributeError:
    #         raise AttributeError(
    #             f"'{type(self).__name__}' object has no attribute '{name}'"
    #         )

    @property
    def aggregated_custom_instructions(self):
        if not self._gen_settings.enable_custom:
            return []
        if self._gen_settings.content_collection_plan_gen:
            return []

        custom_instructions = self._data_wrapper.aggregated_custom_instructions

        if self._gen_env._gen_settings.template_generation:
            custom_instructions.extend(self._data_wrapper.template_instructions)

        # we commented it out since it may be used for other places
        # if self._data_wrapper.content_group_instance:
        #     content_collection_instructions = (
        #         self._data_wrapper.content_collection_instructions
        #     )
        #     custom_instructions.extend(content_collection_instructions)

        dict_custom_instructions = [
            inst_dict
            for inst_dict in custom_instructions
            if isinstance(inst_dict, dict) and inst_dict
        ]
        str_custom_instructions = [
            inst_str
            for inst_str in custom_instructions
            if isinstance(inst_str, str) and inst_str
        ]
        try:

            def sort_key(d):
                items = {k: str(v) for k, v in d.items()}
                sorted_items = sorted(items.items())
                return sorted_items

            dict_custom_instructions = sorted(dict_custom_instructions, key=sort_key)

            str_custom_instructions = sorted(list(set(str_custom_instructions)))

            return dict_custom_instructions + str_custom_instructions
        except Exception as e:
            logging.error(f"Error sorting custom instructions: {e}")
            return dict_custom_instructions + str_custom_instructions

    @property
    def aggregated_asset_params(self):
        asset_params = self._data_wrapper.aggregated_asset_params
        custom_instructions = self.aggregated_custom_instructions
        # add component level custom instructions
        if self._data_wrapper.has_components and self._data_wrapper.components:
            components_custom_instructions = []
            for _, component_data in self._data_wrapper.components.items():
                component_custom_instructions = (
                    component_data.get("meta", {})
                    .get("component_params", {})
                    .get("custom_instructions", [])
                )
                components_custom_instructions.extend(component_custom_instructions)
            custom_instructions = custom_instructions + components_custom_instructions
        for custom_instruction in custom_instructions:
            custom_instruction_assets = custom_instruction.get("assets", None)
            instruction = custom_instruction.get("instruction", None)
            if custom_instruction_assets:
                if isinstance(custom_instruction_assets, list):
                    if len(custom_instruction_assets) != 1:
                        raise Exception("Only support one asset for custom instruction")
                    custom_instruction_assets = custom_instruction_assets[0]
                # parse assets as dict
                if not isinstance(custom_instruction_assets, dict):
                    raise Exception("assets must be a dict")
                for key, value in custom_instruction_assets.items():
                    if value is None:
                        # asset group case
                        asset_infos = AssetInfo.objects.filter(
                            asset_info_group__asset_info_group_key=key,
                            asset_info_group__playbook=self.playbook_instance,
                        )
                        for asset_info in asset_infos:
                            asset_params.append(
                                {
                                    "assets": {key: asset_info.asset_key},
                                    "instruction": instruction,
                                }
                            )
                    elif isinstance(value, list):
                        for k2 in value:
                            asset_params.append(
                                {"assets": {key: k2}, "instruction": instruction}
                            )
                    else:
                        asset_params.append(
                            {"assets": {key: value}, "instruction": instruction}
                        )
        if not self._gen_settings.enable_custom:
            asset_only_params = []
            for asset_param in asset_params:
                if not asset_param.get("instruction"):
                    asset_only_params.append(asset_param)
            asset_params = asset_only_params
        # remove old content_collection instructions if this is a content_collection_plan_gen
        if self._gen_settings.content_collection_plan_gen:
            asset_params = [
                ap
                for ap in asset_params
                if ap.get("meta") != "content_collection_instructions"
            ]
        # only for compatible
        if self._gen_settings.content_collection_plan_gen:
            for asset_param in asset_params:
                if "meta" in asset_param:
                    del asset_param["meta"]
        # we only support up to 30 asset params.
        if len(asset_params) > 30:
            if self._data_wrapper.content_group_instance:
                logging.error(
                    f"ContentGroup {self._data_wrapper.content_group_instance.id} has {len(asset_params)} asset params. Truncating to 30...."
                )
            else:
                logging.error(
                    f"Detected {len(asset_params)} asset params. Truncating to 30...."
                )
            asset_params = asset_params[:30]
        return asset_params

    @property
    def playbook_instance(self):
        return self._data_wrapper.playbook_instance

    @property
    def campaign_instance(self):
        return self._data_wrapper.campaign_instance

    @property
    def campaign_goal(self):
        return self._data_wrapper.campaign_goal

    @property
    def content_group_instance(self):
        return self._data_wrapper.content_group_instance

    @property
    def content_instance(self):
        return self._data_wrapper.content_instance

    @property
    def content_type(self):
        return self._data_wrapper.content_type

    @property
    def content_group_params(self):
        return self._data_wrapper.content_group_params

    @property
    def content_source_format(self):
        return self._data_wrapper.content_source_format

    @property
    def foundation_model(self):
        return self._gen_settings.foundation_model

    @property
    def num_of_variations(self):
        return self._gen_settings.num_of_variations

    @property
    def joint_generation(self):
        return self._gen_settings.joint_generation

    @property
    def template_generation(self):
        return self._gen_settings.template_generation

    @property
    def free_gen(self):
        return self._gen_settings.free_gen

    @property
    def enable_custom(self):
        return self._gen_settings.enable_custom

    @property
    def full_page_html_gen(self):
        return self._gen_settings.full_page_html_gen

    @property
    def repurpose_template(self):
        return self._data_wrapper.repurpose_template

    @property
    def template_settings(self):
        return self._data_wrapper.template_settings

    @property
    def tone_reference_v2(self):
        return self._data_wrapper.tone_reference_v2

    @property
    def content_word_count(self):
        return self._data_wrapper.content_word_count

    @property
    def use_company_summary(self):
        return self._gen_settings.use_company_summary

    @property
    def is_template_personalization(self):
        return self._gen_settings.is_template_personalization

    @property
    def personalization_template(self):
        return self._data_wrapper.personalization_template

    @property
    def template_length(self):
        return self._gen_settings.template_length

    @property
    def template_purpose(self):
        return self._gen_settings.template_purpose

    @property
    def content_gen_components(self):
        return self._gen_settings.content_gen_components

    @property
    def is_json_output(self):
        return self._gen_settings.is_json_output

    @property
    def is_in_content_group_collection(self):
        return self._data_wrapper.is_in_content_group_collection

    def set_features(self, features):
        self._features = features
