from ...actions.action_data_wrapper import ActionDataWrapper
from ...actions.legacy_converter.legacy_custom_instruction_converter import (
    AssetsV3ToV2Converter,
)
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionCategory


def get_campaign_v3_content_group_asset_params(content_group_instance):
    """
    returns a list of asset params of the form
    [{
        "assets": {asset_group_key: asset_key},
        "instruction": None,
        "meta": "repurpose_anchor_content",
    }, ...]
    """

    action_instance = content_group_instance.action
    if not action_instance:
        raise Exception("Action instance not found")
    if action_instance.action_category != ActionCategory.Name(
        ActionCategory.ACTION_CATEGORY_REPURPOSE
    ):
        return []
    action_data_wrapper = ActionDataWrapper(action_instance)
    assets = action_data_wrapper.get_input_by_name("anchor_content")
    if not assets:
        return []

    # should look like:
    # {"data": [{"asset": {"asset_id": "1228"}}, {"asset": {"asset_id": "1229"}}]}

    # use AssetsV3ToV2Converter
    assets_v3_to_v2_converter = AssetsV3ToV2Converter(assets)
    assets_v2 = assets_v3_to_v2_converter.convert_assets_v3_to_v2()

    asset_params = []
    for asset_group_key, asset_keys in assets_v2.items():
        for asset_key in asset_keys:
            asset_params.append(
                {
                    "assets": {asset_group_key: asset_key},
                    "instruction": None,
                    "meta": "repurpose_anchor_content",
                }
            )

    return asset_params
