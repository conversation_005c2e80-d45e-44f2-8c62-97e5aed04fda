from .base_feature_builder import BaseFeatureBuilder


class ToneAnalysisFeatureBuilder(BaseFeatureBuilder):
    def __init__(self, object_docs, gen_env) -> None:
        super().__init__(gen_env)
        self.object_docs = object_docs

    def extract_tone_example(self):
        return self.object_docs

    def register_feature_extractors(self):
        self.register_feature_extractor("tone_example", self.extract_tone_example)
