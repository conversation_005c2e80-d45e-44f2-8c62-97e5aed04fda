import logging

from ...models import TargetInfo
from ...utils import (
    clean_personalization_token,
    get_curr_selected_variation_single_component,
    replace_variation,
)
from .base_feature_builder import BaseFeatureBuilder


class ComponentFeatureBuilder(BaseFeatureBuilder):
    def __init__(
        self,
        example_content,
        prev_gen_variations,
        gen_env,
    ):
        super().__init__(gen_env=gen_env)
        self.example_content = example_content
        self.prev_gen_variations = prev_gen_variations

    def extract_example_text(self):
        return self.example_content.get("text", "")

    def extract_num_of_words(self):
        return len(self.example_content.get("text", "").split())

    def extract_preceding_element(self):
        metadata = self.example_content.get("meta", {})
        precedingContent = (
            metadata.get("precedingContent", metadata.get("preceding_element", ""))
            or ""
        )
        # Remove {{}} p13n tokens from precedingContent
        precedingContent = clean_personalization_token(preceding<PERSON>ontent)
        return precedingContent

    def extract_preceding_variation(self):
        metadata = self.example_content.get("meta", {})
        precedingContent = (
            metadata.get("precedingContent", metadata.get("preceding_element", ""))
            or ""
        )
        precedingVariation = replace_variation(
            precedingContent, self.prev_gen_variations
        )
        return precedingVariation

    def extract_succeeding_element(self):
        metadata = self.example_content.get("meta", {})
        succeedingContent = (
            metadata.get("succeedingContent", metadata.get("succeeding_element", ""))
            or ""
        )
        # Remove {{}} p13n tokens from succeedingContent
        succeedingContent = clean_personalization_token(succeedingContent)
        return succeedingContent

    def extract_succeeding_variation(self):
        metadata = self.example_content.get("meta", {})
        succeedingContent = metadata.get(
            "succeedingContent", metadata.get("succeeding_element", "")
        )
        succeedingVariation = replace_variation(
            succeedingContent, self.prev_gen_variations
        )
        return succeedingVariation

    def extract_html_tag(self):
        metadata = self.example_content.get("meta", {})
        html_tag = metadata.get("html_tag", "")
        return html_tag

    def extract_page_num(self):
        metadata = self.example_content.get("meta", {})
        pageNum = metadata.get("pageNum", "")
        return pageNum

    def extract_component_type(self):
        metadata = self.example_content.get("meta", {})
        component_type = metadata.get("component_type", "message")
        if not component_type:
            component_type = "message"
        return component_type

    def extract_component_type_section(self):
        metadata = self.example_content.get("meta", {})
        component_type = metadata.get("component_type", "message")
        if component_type == "email body":
            return "section in the email body"
        elif component_type == "email subject":
            return "section in the email subject"
        if not component_type:
            component_type = "message"
        return component_type

    def extract_previous_generation(self):
        text = self.example_content.get("text", "")
        prev_variation = replace_variation(text, self.prev_gen_variations)
        return prev_variation

    def extract_repurpose_template_content(self):
        if not self.repurpose_template:
            return None
        metadata = self.example_content.get("meta", {})
        component_type = metadata.get("component_type", "message")
        template_content = None
        is_email_text_template = (
            "subjectLine" in self.repurpose_template
            or "body" in self.repurpose_template
        )
        is_linkedin_text_template = (
            "introductory-text" in self.repurpose_template
            or "headline" in self.repurpose_template
            or "description" in self.repurpose_template
            or "ad-copy" in self.repurpose_template
        )  # TODO: make this consolidated
        if is_email_text_template:
            if component_type == "email subject":
                template_content = self.repurpose_template.get("subjectLine", None)
            elif component_type == "email body":
                template_content = self.repurpose_template.get("body", None)
        elif is_linkedin_text_template:
            if component_type == "introductory-text":
                template_content = self.repurpose_template.get(
                    "introductory-text", None
                )
            elif component_type == "headline":
                template_content = self.repurpose_template.get("headline", None)
            elif component_type == "description":
                template_content = self.repurpose_template.get("description", None)
            elif component_type == "ad-copy":
                template_content = self.repurpose_template.get("ad-copy", None)
        elif "text" in self.repurpose_template:
            template_content = self.repurpose_template.get("text", None)
        else:
            logging.error(
                f"Expected repurpose template contain fields 'subjectLine' and 'body' OR 'introductory-text' and 'headline' and 'description' and 'ad-copy' OR 'text' but got {self.repurpose_template}"
            )
        return template_content

    def extract_component_instructions(self):
        # for free gen now and only plain text
        custom_instructions = self.example_content.get("meta", {}).get(
            "custom_instructions", []
        )
        instructions = [
            custom_instruction.get("instruction", "")
            for custom_instruction in custom_instructions
        ]
        instructions = [instruction for instruction in instructions if instruction]
        return "\n".join(instructions) if instructions else ""

    def extract_parent_component_current_value(self):
        parent_component_id = self.example_content.get("meta", {}).get(
            "parent_component_id", ""
        )
        if not parent_component_id:
            logging.error(f"Parent component id is not found in {self.example_content}")
            return ""

        cur_idx = self.example_content.get("meta", {}).get(
            "parent_component_variation_index", ""
        )
        cur_idx = int(cur_idx) if cur_idx else 0
        cur_value = self._data_wrapper.get_component_current_value(
            parent_component_id, cur_idx
        )
        return cur_value

    def extract_component_reviewed_contents_string(self):
        content_group = self.content_group_instance
        reviewed_contents = content_group.content_group_params.get(
            "reviewed_content_list", []
        )
        if not reviewed_contents:
            return None
        else:
            content_examples = []
            component_id = self._data_wrapper.component_key
            if not component_id:
                logging.error(f"Component id is not found in {self._data_wrapper}")
                return None
            # sort by reviewed_time and get the latest 2 reviewed content
            reviewed_contents = sorted(
                reviewed_contents, key=lambda x: x["reviewed_time"], reverse=True
            )
            for reviewed_content in reviewed_contents:
                content_id = reviewed_content.get("content_id")
                # skip if content_id is the same as the current content
                if content_id == self.content_instance.id:
                    continue
                curr_var = get_curr_selected_variation_single_component(
                    content_id, component_id
                )
                if curr_var:
                    content_examples.append(
                        "\n<example>\n" + str(curr_var) + "\n</example>\n"
                    )
                # only get 2 reviewed content
                if len(content_examples) >= 2:
                    break
            if not content_examples:
                return None
            return "\n".join(content_examples)

    def register_feature_extractors(self):
        self.register_feature_extractor("example_text", self.extract_example_text)
        self.register_feature_extractor("num_of_words", self.extract_num_of_words)
        self.register_feature_extractor(
            "preceding_element", self.extract_preceding_element
        )
        self.register_feature_extractor(
            "preceding_variation", self.extract_preceding_variation
        )
        self.register_feature_extractor(
            "succeeding_element", self.extract_succeeding_element
        )
        self.register_feature_extractor(
            "succeeding_variation", self.extract_succeeding_variation
        )
        self.register_feature_extractor("html_tag", self.extract_html_tag)
        self.register_feature_extractor("pageNum", self.extract_page_num)
        self.register_feature_extractor("component_type", self.extract_component_type)
        self.register_feature_extractor(
            "component_type_section", self.extract_component_type_section
        )
        self.register_feature_extractor(
            "previous_generation", self.extract_previous_generation
        )
        self.register_feature_extractor(
            "repurpose_template_content", self.extract_repurpose_template_content
        )
        self.register_feature_extractor(
            "component_instructions", self.extract_component_instructions
        )
        self.register_feature_extractor(
            "parent_component_current_value",
            self.extract_parent_component_current_value,
        )
        self.register_feature_extractor(
            "component_reviewed_contents_string",
            self.extract_component_reviewed_contents_string,
        )
