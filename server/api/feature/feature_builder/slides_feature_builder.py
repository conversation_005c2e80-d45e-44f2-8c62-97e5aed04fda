from ...slides.template import get_placeholder_mapping
from ...utils import is_google_slides_url, is_s3_url
from .base_feature_builder import BaseFeatureBuilder


class SlidesFeatureBuilder(BaseFeatureBuilder):

    def __init__(self, gen_env):
        super().__init__(gen_env)

    def extract_slides_placeholder_mapping(self):
        repurpose_template_content_source_copy = self.content_group_params.get(
            "repurpose_template_content_source_copy", None
        )
        if not repurpose_template_content_source_copy:
            return {}

        # validate is google slides or s3 pptx

        if not is_google_slides_url(
            repurpose_template_content_source_copy
        ) and not is_s3_url(repurpose_template_content_source_copy):
            raise ValueError(
                "Repurpose template content source copy is not a google slides url or s3 pptx file"
            )
        return get_placeholder_mapping(repurpose_template_content_source_copy)

    def register_feature_extractors(self):
        self.register_feature_extractor(
            "slides_placeholder_mapping", self.extract_slides_placeholder_mapping
        )
