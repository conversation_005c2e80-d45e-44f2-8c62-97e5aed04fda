import logging
from abc import ABC, abstractmethod
from typing import Dict, List

from ..data_wrapper.data_wrapper import DataWrapperHolder


class BaseFeatureBuilder(ABC, DataWrapperHolder):
    def __init__(self, gen_env) -> None:
        DataWrapperHolder.__init__(self, gen_env=gen_env)

        self.feature_extractions = {}
        self.budgets = None

    def set_budgets(self, budgets: Dict[str, int]):
        if self.budgets is not None:
            raise Exception("Budgets already set")
        self.budgets = budgets

    def get_budgets(self) -> Dict[str, int]:
        if self.budgets is None:
            logging.error("Budgets not set but queried")
            return {}
        return self.budgets

    def get_budget(self, variable_name: str) -> int:
        # TODO: if we need an error to avoid unexpected query?
        if variable_name not in self.budgets:
            logging.error(
                f"Variable {variable_name} not in budgets and this query is unexpected"
            )
        return self.budgets.get(variable_name, 0)

    def get_feature_names(self) -> List[str]:
        return self.feature_extractions.keys()

    @abstractmethod
    def register_feature_extractors(self):
        pass

    def register_feature_extractor(self, feature_name, feature_extraction):
        self.feature_extractions[feature_name] = feature_extraction

    def build(self, feature_names) -> Dict[str, str]:
        self.register_feature_extractors()
        results = {}
        for feature_name in feature_names:
            if feature_name in self.feature_extractions:
                feature_value = self.feature_extractions[feature_name]()
                if feature_value is not None:
                    results[feature_name] = feature_value
        return results
