import json
import logging

from ...models import AssetInfo
from ...prompt.prompt_library.prompt_rules import (
    EMAIL_BODY_RULES_PERSONALIZE,
    EMAIL_LENGTH_RULES,
    EMAIL_SUBJECT_RULES,
    EMAIL_TONE_RULES,
    LINKEDIN_ADS_AD_COPY_RULES,
    LINKEDIN_ADS_DESCRIPTION_RULES,
    LINKEDIN_ADS_HEADLINE_RULES,
    LINKEDIN_ADS_INTRODUCTORY_TEXT_RULES,
)
from ...prompt.prompt_library.templates import (
    get_linkedin_ads_template,
    get_linkedin_ads_template_w_template,
)
from ...utils import (
    clean_personalization_token,
    get_curr_selected_variation_json,
    to_camel_case,
)
from .base_feature_builder import BaseFeatureBuilder


class JointComponentFeatureBuilder(BaseFeatureBuilder):

    def __init__(
        self,
        all_components,
        prev_gen_variations,
        gen_env,
    ):
        super().__init__(gen_env=gen_env)
        self.components = all_components
        self.prev_gen_variations = prev_gen_variations

    def extract_contents_xml(self):
        xml_string = ""
        keys = list(self.components.keys())
        for key in keys:
            text = self.components[key]["text"]
            component_type = self.components[key]["meta"].get("component_type", "")
            xml_string += f'<component id="{key}">\n<text>{text}</text>\n'

            component_meta = self.components[key]["meta"]
            meta = self.get_meta(component_type, component_meta)
            meta["word count"] = len(text.split())
            meta_xml_string = ""
            for key in meta:
                value = meta[key]
                if value:
                    if isinstance(value, list):
                        value = " ".join(value)
                    xml_key = to_camel_case(key)
                    meta_xml_string += f"<{xml_key}>\n{value}\n</{xml_key}>\n"

            xml_string += f"<meta>\n{meta_xml_string}</meta>\n"

            xml_string += "</component>\n"
        return xml_string

    def extract_repurpose_template_content(self):
        if not self.repurpose_template:
            return None
        if not isinstance(self.repurpose_template, dict):
            raise Exception(
                f"Repurpose template is not a dictionary: {self.repurpose_template}"
            )
        is_email_text_template = (
            "subjectLine" in self.repurpose_template
            or "body" in self.repurpose_template
        )
        is_linkedin_ads_text_template = self._data_wrapper.is_linkedin_ads_v2

        # some validation check
        if self._data_wrapper.is_email and not is_email_text_template:
            raise Exception(
                f"Repurpose template does not match email since it has the keys: {list(self.repurpose_template.keys())}, not 'subjectLine' or 'body'"
            )
        if not self._data_wrapper.is_email and is_email_text_template:
            raise Exception(
                f"Expected other repurpose template, got email template with keys: {list(self.repurpose_template.keys())}"
            )
        if is_linkedin_ads_text_template and not (
            "introductory-text" in self.repurpose_template
            or "headline" in self.repurpose_template
            or "description" in self.repurpose_template
            or "ad-copy" in self.repurpose_template
        ):
            raise Exception(
                f"Repurpose template does not match linkedin ads since it has the keys: {list(self.repurpose_template.keys())}, not 'introductory-text' or 'headline' or 'description' or 'ad-copy'"
            )

        if is_email_text_template:
            subjectLine = self.repurpose_template.get("subjectLine")
            body = self.repurpose_template.get("body")
            repurpose_template_content = (
                f"subject line: {subjectLine} \n\n body: {body}"
            )
        elif is_linkedin_ads_text_template:
            introductory_text = self.repurpose_template.get("introductory-text")
            headline = self.repurpose_template.get("headline")
            description = self.repurpose_template.get("description")
            ad_copy = self.repurpose_template.get("ad-copy")
            repurpose_template_content = f"introductory-text: {introductory_text} \n\n headline: {headline} \n\n description: {description} \n\n ad-copy: {ad_copy}"
        else:
            repurpose_template_content = self.repurpose_template.get("text")
        if not repurpose_template_content:
            raise Exception(
                f"Expect repurpose_template_content to have 'subjectLine' and 'body' OR 'text'. Got: {repurpose_template_content}"
            )
        return repurpose_template_content

    def extract_email_template_instructions(self):
        email_template_component_level_custom_prompts = ""
        for key in self.components:
            component_type = self.components[key]["meta"].get("component_type", "")
            if component_type and component_type == "email subject":
                if "component_params" in self.components[key]["meta"]:
                    component_params = self.components[key]["meta"]["component_params"]
                    custom_instructions = component_params.get(
                        "custom_instructions", []
                    )
                    instruct_prompts = [
                        instruct.get("instruction", "")
                        for instruct in custom_instructions
                    ]

                    if instruct_prompts:
                        email_template_component_level_custom_prompts += (
                            "Email subject instructions: " + "\n".join(instruct_prompts)
                        )
            if component_type and component_type == "email body":
                if "component_params" in self.components[key]["meta"]:
                    component_params = self.components[key]["meta"]["component_params"]
                    custom_instructions = component_params.get(
                        "custom_instructions", []
                    )
                    instruct_prompts = [
                        instruct.get("instruction", "")
                        for instruct in custom_instructions
                    ]
                    if instruct_prompts:
                        email_template_component_level_custom_prompts += (
                            "Email body instructions: " + "\n".join(instruct_prompts)
                        )
        return email_template_component_level_custom_prompts

    def extract_component_level_custom_prompts(self):
        if self.repurpose_template:
            return self.extract_email_template_instructions()
        contents_json = {}

        keys = list(self.components.keys())

        if not self.enable_custom:
            return None

        for key in keys:
            if "component_params" in self.components[key]["meta"]:
                component_params = self.components[key]["meta"]["component_params"]
                custom_instructions = component_params.get("custom_instructions", [])
                # looks like [{{'assets': {{'Smart replace url list': None}}, 'instruction': 'Replace the statistic reference with one from the list that is most relevant to the target.'}}]
                if custom_instructions:
                    contents_json[key] = self.build_component_specific_instructions(
                        custom_instructions
                    )

        if len(contents_json) == 0:
            return None

        component_custom_prompts_xml_string = ""
        for key in contents_json:
            component_type = self.components[key]["meta"].get("component_type", "")
            instructions = contents_json[key]
            if component_type and component_type in [
                "email subject",
                "email body",
                "introductory-text",
                "headline",
                "description",
                "ad-copy",
            ]:
                component_custom_prompts_xml_string += f"<component id='{component_type} {key}'>\n<componentSpecificInstruction>\n{instructions}\n</componentSpecificInstruction>\n</component>\n"
            else:
                component_custom_prompts_xml_string += f"<component id='{key}'>\n<componentSpecificInstruction>\n{instructions}\n</componentSpecificInstruction>\n</component>\n"
        return component_custom_prompts_xml_string

    def build_component_specific_instructions(self, instructions):
        custom_prompts = []
        for custom_instruction in instructions:
            assets = custom_instruction.get("assets", {})
            instruction = custom_instruction.get("instruction", "")

            if not assets:
                if instruction:
                    custom_prompts.append(
                        "<instruction>\n" + instruction + "\n</instruction>\n"
                    )
            else:
                instruction_string = (
                    (
                        "<instruction (using reference content)>\n"
                        + instruction
                        + "\n</instruction>\n"
                    )
                    if instruction
                    else ""
                )
                assets_prompts = []
                for asset_key, asset_value in assets.items():
                    if asset_value is None:
                        # asset list case. Expand the list into multiple assets.
                        asset_infos = AssetInfo.objects.filter(
                            asset_info_group__asset_info_group_key=asset_key,
                            asset_info_group__playbook=self.playbook_instance,
                        )
                        for asset_info in asset_infos:
                            assets_prompts.append(
                                f"{{{asset_key}:{asset_info.asset_key}}}"
                            )
                    else:
                        assets_prompts.append(f"{{{asset_key}:{asset_value}}}")

                assets_string = "\n".join(assets_prompts)
                custom_prompts.append(
                    f"<referenceContent>\n<assets>\n{assets_string}\n</assets>\n{instruction_string}</referenceContent>"
                )

        return "\n".join(custom_prompts)

    def extract_json_format(self):
        json_format = '\\{<component id>: \\{text: <generated message variation>, word count: <output word count>\\}\\}.\nFor the generated message in text, make sure to escape quotes using \\".'
        return json_format

    def extract_email_joint_format(self):
        return "<generation>\n<subject>\n[subject line]\n</subject> \n\n <body>\n[body]\n</body>\n</generation>"

    def extract_linkedin_ads_joint_format(self):
        if not self.repurpose_template:
            linkedin_ads_joint_format = get_linkedin_ads_template()
        else:
            linkedin_ads_joint_format = get_linkedin_ads_template_w_template()
        return linkedin_ads_joint_format

    def extract_generic_joint_format(self):
        if self._data_wrapper.is_email:
            return self.extract_email_joint_format()
        if self._data_wrapper.is_linkedin_ads_v2:
            return self.extract_linkedin_ads_joint_format()
        return self.extract_json_format

    def extract_reviewed_contents_string(self):
        content_group = self.content_group_instance
        reviewed_contents = content_group.content_group_params.get(
            "reviewed_content_list", []
        )
        if not reviewed_contents:
            return ""

        content_json_examples = []

        for reviewed_content in reviewed_contents:
            content_id = reviewed_content.get("content_id")
            # skip if content_id is the same as the current content
            if content_id == self.content_instance.id:
                continue

            positive_example = reviewed_content.get("positive_example", False)
            if not positive_example:
                continue

            curr_var_json = get_curr_selected_variation_json(content_id)
            if curr_var_json:
                content_json_examples.append(
                    "\n<example>\n" + json.dumps(curr_var_json) + "\n</example>\n"
                )
            # only get 2 reviewed content
            if len(content_json_examples) >= 2:
                break
        if not content_json_examples:
            return None
        return "\n".join(content_json_examples)

    def get_joint_generation_rules(self, component_type):
        rules = []

        email_subject_rules = EMAIL_SUBJECT_RULES
        email_body_rules = EMAIL_BODY_RULES_PERSONALIZE
        email_tone_rules = EMAIL_TONE_RULES
        email_length_rules = EMAIL_LENGTH_RULES

        linkedin_ads_introductory_text_rules = LINKEDIN_ADS_INTRODUCTORY_TEXT_RULES
        linkedin_ads_headline_rules = LINKEDIN_ADS_HEADLINE_RULES
        linkedin_ads_description_rules = LINKEDIN_ADS_DESCRIPTION_RULES
        linkedin_ads_ad_copy_rules = LINKEDIN_ADS_AD_COPY_RULES

        if component_type:
            if component_type == "email subject":
                rules.append(email_subject_rules)
            elif component_type == "email body":
                rules.append(email_body_rules)
                if self.template_settings.get("follow_tone", True):
                    rules.append(email_tone_rules)
                if self.template_settings.get("follow_length", True):
                    rules.append(email_length_rules)
            elif component_type == "introductory-text":
                rules.append(linkedin_ads_introductory_text_rules)
            elif component_type == "headline":
                rules.append(linkedin_ads_headline_rules)
            elif component_type == "description":
                rules.append(linkedin_ads_description_rules)
            elif component_type == "ad-copy":
                rules.append(linkedin_ads_ad_copy_rules)

        return rules

    def get_meta(self, component_type, component_meta):
        meta = {}
        if (not component_type) or (
            component_type
            and "email" not in component_type
            and component_type
            not in [
                "email subject",
                "email body",
                "introductory-text",
                "headline",
                "description",
                "ad-copy",
            ]
        ):
            if (
                "preceding_element" in component_meta
                and component_meta["preceding_element"]
            ):
                meta["preceding_element"] = clean_personalization_token(
                    component_meta["preceding_element"]
                )
            if (
                "succeeding_element" in component_meta
                and component_meta["succeeding_element"]
            ):
                meta["succeeding_element"] = clean_personalization_token(
                    component_meta["succeeding_element"]
                )
            if (
                "precedingContent" in component_meta
                and component_meta["precedingContent"]
            ):
                meta["precedingContent"] = clean_personalization_token(
                    component_meta["precedingContent"]
                )
            if (
                "succeedingContent" in component_meta
                and component_meta["succeedingContent"]
            ):
                meta["succeedingContent"] = clean_personalization_token(
                    component_meta["succeedingContent"]
                )
        if "html_tag" in component_meta:
            meta["html_tag"] = component_meta["html_tag"]
        if "pageNum" in component_meta:
            meta["pageNum"] = component_meta["pageNum"]
        if "component_type" in component_meta:
            meta["component_type"] = component_meta["component_type"]

        meta["rules"] = self.get_joint_generation_rules(component_type)
        return meta

    def register_feature_extractors(self):
        self.register_feature_extractor("contents_xml", self.extract_contents_xml)
        self.register_feature_extractor("json_format", self.extract_json_format)
        self.register_feature_extractor(
            "component_level_custom_prompts",
            self.extract_component_level_custom_prompts,
        )
        self.register_feature_extractor(
            "repurpose_template_content", self.extract_repurpose_template_content
        )
        self.register_feature_extractor(
            "generic_joint_format", self.extract_generic_joint_format
        )
        self.register_feature_extractor(
            "reviewed_contents_string", self.extract_reviewed_contents_string
        )
