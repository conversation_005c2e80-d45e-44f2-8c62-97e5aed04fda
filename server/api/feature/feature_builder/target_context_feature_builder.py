import logging
import os
import traceback

import pinecone
from langchain_pinecone import PineconeVectorStore

from ...llms import get_llm_for_embeddings
from ...models import TargetInfo, TargetInfoGroup
from ...playbook_build.doc_loader import DocLoader
from ...playbook_build.object_builder import ObjectBuilder
from ...prompt.prompt_library.instruct_prompt.default_instruct import (
    STATIC_CONTEXT_QUERY,
)
from ...utils import fix_xml_context_tags, get_display_l2_key, get_token_count
from .base_feature_builder import BaseFeatureBuilder

_INDEX_CHUNK_SIZE = 250  # tokens


class TargetContextBuilder(BaseFeatureBuilder):
    def __init__(
        self,
        gen_env,
        use_mrmr=True,
    ):
        super().__init__(
            gen_env=gen_env,
        )
        self.use_mrmr = use_mrmr
        self.pinecone_index = pinecone.Pinecone().Index(
            host=os.environ.get("PINECONE_INDEX_HOST")
        )

    @property
    def target_params(self):
        return self._data_wrapper.target_params

    @property
    def content_type(self):
        return self._data_wrapper.content_type

    @property
    def playbook_instance(self):
        return self._data_wrapper.playbook_instance

    def extract_targets_string(self):
        targets = []
        # get the playbook target_info
        try:
            for l1_key, l2_key in self.target_params.items():
                field_name = ""
                target_info = TargetInfo.objects.get(
                    target_key=l2_key,
                    target_info_group__target_info_group_key=l1_key,
                    target_info_group__playbook=self.playbook_instance,
                )
                field_name = target_info.meta.get("field_name", "")
                remove_l2_key_suffix = target_info.meta.get("duplicate", False)

                if field_name:
                    targets.append(
                        f"{field_name} - {get_display_l2_key(l2_key, remove_l2_key_suffix)}"
                    )
                else:
                    targets.append(
                        f"{get_display_l2_key(l2_key, remove_l2_key_suffix)}"
                    )
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e}"
            )
            raise
        targets_string = "\n".join(targets)
        return targets_string

    def register_feature_extractors(self):
        self.register_feature_extractor("target_context", self.extract_target_context)
        self.register_feature_extractor("has_value_prop", self.extract_has_value_prop)
        self.register_feature_extractor(
            "target_meta_types", self.extract_target_meta_types
        )
        self.register_feature_extractor("targets", self.extract_targets_string)

    def extract_target_context(self):
        # build target context
        budget = self.get_budget("target_context")

        target_context = ""
        num_of_targets = len(self.target_params)
        for l1_key, l2_key in self.target_params.items():
            max_token_size = budget // num_of_targets
            context = self.get_single_target_context(
                l1_key, l2_key, max_token_size=max_token_size
            )
            budget -= get_token_count(
                context
            )  # calculate what's left for target context budget
            num_of_targets -= 1
            target_context += context + "\n"
        return target_context

    def extract_has_value_prop(self):
        # Concatenate value props
        logging.info("Target object consumption is enabled")
        for l1_key, l2_key in self.target_params.items():
            try:
                # TODO: we may not need a query since prefetch. Check later
                target = TargetInfo.objects.get(
                    target_key=l2_key,
                    target_info_group__target_info_group_key=l1_key,
                    target_info_group__playbook=self.playbook_instance,
                )
                value_prop = target.value_prop
                if value_prop:  # can be either empty str or null
                    return True
            except TargetInfo.DoesNotExist as e:
                logging.error(
                    f"TargetInfo does not exist: {l1_key} {l2_key} in context feature builder"
                )
                raise Exception(
                    f"TargetInfo does not exist: {l1_key} {l2_key} in context feature builder"
                ) from e
            except Exception as e:
                logging.error(
                    f"Error in context feature builder for playbook {self.playbook_instance.id}: {e} {traceback.format_exc()}"
                )
                raise
        return False

    def extract_target_meta_types(self):
        meta_types = []
        logging.info("Target object consumption is enabled")
        try:
            for target_info_group_key in self.target_params.keys():
                try:
                    target_info_group = self.playbook_instance.target_info_groups.get(
                        target_info_group_key=target_info_group_key
                    )
                except TargetInfoGroup.DoesNotExist:
                    raise Exception(
                        f"playbook: {self.playbook_instance.id} target_info_group not found for target_info_group_key: {target_info_group_key}"
                    )
                meta_type = target_info_group.meta.get("type", "")
                if meta_type != "":
                    meta_types.append(meta_type)
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e} {traceback.format_exc()}"
            )
            raise
        return meta_types

    def get_single_target_context(self, l1_key, l2_key, max_token_size=1200):
        try:
            logging.info("Target object consumption is enabled")
            try:
                target_object = self.playbook_instance.target_info_groups.get(
                    target_info_group_key=l1_key
                ).targets.get(target_key=l2_key)
            except (TargetInfoGroup.DoesNotExist, TargetInfo.DoesNotExist):
                raise Exception(
                    f"playbook: {self.playbook_instance.id} target_info_group or target not found for target_info_group_key: {l1_key}, target_key: {l2_key}"
                )
            value_prop = target_object.value_prop or ""
            summary = target_object.summary or ""
            index = target_object.index or {}
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e} {traceback.format_exc()}"
            )
            raise

        value_prop = value_prop[
            : int(max_token_size * 4)
        ]  # make sure value_prop is under the limit

        try:
            docs = ObjectBuilder.get_builder(target_object).extract_docs()
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e} {traceback.format_exc()}"
            )
            docs = []
        docs = DocLoader.optimize_docs(docs)
        raw_text = "\n".join([doc.page_content for doc in docs])

        additional_info = target_object.additional_info or {}
        tofu_research_items = additional_info.get("tofu_research", {})
        tofu_research_results = [
            item["result"] for item in tofu_research_items.values() if "result" in item
        ]
        tofu_research_result = "\n".join(tofu_research_results)

        # when there is no summary or index, fall back to grabbing all the data
        if not summary or not index:
            capacity = max(0, 4 * max_token_size - len(value_prop))
            tofu_insights = f"<context>\n{value_prop}\n</context>"
            if tofu_research_result:
                tofu_insights += f"<context><tofu_research>\n{tofu_research_result}\n</tofu_research>\n</context>"
            return (
                f"{tofu_insights}\n{fix_xml_context_tags(raw_text[:int(capacity)])}\n"
            )

        summary_token_size = get_token_count(summary)
        value_prop_token_size = get_token_count(value_prop)

        # if summary + value_prop + raw text fits within the budget, return it
        if (
            summary_token_size + value_prop_token_size + get_token_count(raw_text)
            <= max_token_size
        ):
            tofu_insights = f"<context>\n<summary>\n{summary}\n</summary>\n</context>\n<context>\n{value_prop}\n</context>"
            if tofu_research_result:
                tofu_insights += f"<context><tofu_research>\n{tofu_research_result}\n</tofu_research>\n</context>"
            return f"{tofu_insights}\n{raw_text}"

        idx_token_size = max(
            int(0.1 * max_token_size),  # at least 10% of max_token_size
            max_token_size - summary_token_size - value_prop_token_size,
        )
        revised_summary_token_size = max(
            0, max_token_size - idx_token_size - value_prop_token_size
        )

        # when summary is too long, truncate it
        if summary_token_size > revised_summary_token_size:
            summary = summary[: int(revised_summary_token_size * 4)]

        DYNAMIC_RETRIEVAL_BUDGET = idx_token_size // _INDEX_CHUNK_SIZE  # vectors

        MODEL_BUDGET, llm = get_llm_for_embeddings()
        pinecone_index = PineconeVectorStore(
            index=self.pinecone_index,
            embedding=llm,
            text_key="text",
            namespace=index["namespace"],
        )
        dynamic_docs_string = self.get_dynamic_docs_string(
            DYNAMIC_RETRIEVAL_BUDGET, index, pinecone_index, l1_key, l2_key
        )
        tofu_insights = f"<context>\n<summary>\n{summary}\n</summary>\n</context>\n<context>\n{value_prop}\n</context>"
        if tofu_research_result:
            tofu_insights += f"<context><tofu_research>\n{tofu_research_result}\n</tofu_research>\n</context>"
        return f"{tofu_insights}\n{dynamic_docs_string}"

    def get_static_index_query(self):
        return STATIC_CONTEXT_QUERY

    # TODO: this is duplicated across all context builders
    def get_dynamic_index_query(self):
        context_query = self.content_type
        for l1_key, l2_key in self.target_params.items():
            context_query += f"\n{l1_key}\n{l2_key}"
        for item in self.aggregated_custom_instructions:
            context_query += "\n" + str(item)
        if not context_query:
            context_query = self.get_static_index_query()
        return context_query

    def get_dynamic_docs_string(self, budget, index, pinecone_index, l1_key, l2_key):
        dynamic_index_query = self.get_dynamic_index_query()
        dynamic_docs = []
        if budget > 0:
            try:
                if self.use_mrmr:
                    dynamic_docs = pinecone_index.max_marginal_relevance_search(
                        query=dynamic_index_query,
                        k=budget,
                        fetch_k=5 * budget,
                        namespace=index["namespace"],
                        filter={
                            "$and": [{"column_id": "target_info"}],
                            "$and": [{"key_ids": l1_key}, {"key_ids": l2_key}],
                        },
                    )
                else:
                    dynamic_docs = pinecone_index.similarity_search(
                        query=dynamic_index_query,
                        k=budget,
                        namespace=index["namespace"],
                        filter={
                            "$and": [{"column_id": "target_info"}],
                            "$and": [{"key_ids": l1_key}, {"key_ids": l2_key}],
                        },
                    )
            except Exception as e:
                logging.error("Failed to retrieve dynamic docs: %s", e)
                return ""
        dynamic_docs_string = "\n".join([doc.page_content for doc in dynamic_docs])
        return dynamic_docs_string
