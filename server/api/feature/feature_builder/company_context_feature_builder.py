import logging
import os

import pinecone
from langchain_pinecone import PineconeVectorStore

from ...llms import get_llm_for_embeddings
from ...logger import log_data_wrapper_mismatch
from ...models import CompanyInfo, TargetInfo
from ...playbook_build.doc_loader import <PERSON><PERSON>oa<PERSON>
from ...playbook_build.object_builder import ObjectBuilder
from ...prompt.prompt_library.instruct_prompt.default_instruct import (
    STATIC_CONTEXT_QUERY,
)
from ...utils import fix_xml_context_tags, get_token_count
from ..data_wrapper.data_wrapper import CampaignWrapper
from .base_feature_builder import BaseFeatureBuilder


class CompanyContextFeatureBuilder(BaseFeatureBuilder):
    def __init__(
        self,
        gen_env,
        use_mrmr=False,
    ):
        super().__init__(gen_env=gen_env)
        self.use_mrmr = use_mrmr
        self.pinecone_index = pinecone.Pinecone().Index(
            host=os.environ.get("PINECONE_INDEX_HOST")
        )

    @property
    def target_params(self):
        if isinstance(self._data_wrapper, CampaignWrapper):
            return {}
        return self._data_wrapper.target_params

    @property
    def content_type(self):
        if isinstance(self._data_wrapper, CampaignWrapper):
            return ""
        return self._data_wrapper.content_type

    @property
    def playbook_instance(self):
        return self._data_wrapper.playbook_instance

    def get_static_index_query(self):
        return STATIC_CONTEXT_QUERY

    def get_dynamic_index_query(self):
        context_query = self.content_type
        for item in self.aggregated_custom_instructions:
            context_query += "\n" + str(item)
        for l1_key, l2_key in self.target_params.items():
            context_query += f"\n{l1_key}\n{l2_key}"

            try:
                # TODO: we may not need a query since prefetch. Check later
                target = TargetInfo.objects.get(
                    target_key=l2_key,
                    target_info_group__target_info_group_key=l1_key,
                    target_info_group__playbook=self.playbook_instance,
                )
                value_prop = target.value_prop
                summary = target.summary

                additional_info = target.additional_info or {}
                tofu_research_items = additional_info.get("tofu_research", {})
                tofu_research_results = [
                    item["result"]
                    for item in tofu_research_items.values()
                    if "result" in item
                ]
                tofu_research_result = "\n".join(tofu_research_results)

            except TargetInfo.DoesNotExist:
                logging.error(
                    f"TargetInfo does not exist: {l1_key} {l2_key} in context feature builder"
                )
                return context_query
            except Exception as e:
                logging.error(
                    f"Error in context feature builder for playbook {self.playbook_instance.id}: {e}"
                )
                return context_query

            context_query += f"\n{summary}\n{value_prop}"
            if tofu_research_result:
                context_query += f"\n{tofu_research_result}"
        if not context_query:
            context_query = self.get_static_index_query()
        return context_query

    def extract_company_context(self):
        budget = self.get_budget("company_context")  # in tokens

        try:
            docs = ObjectBuilder.get_builder(
                self.playbook_instance.company_object
            ).extract_docs()
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e}"
            )
            return ""

        docs = DocLoader.optimize_docs(docs)
        raw_text = "\n".join([doc.page_content for doc in docs])
        # check if docs will fit in budget. If so just return.
        if get_token_count(raw_text) <= budget:
            return raw_text

        try:
            company_info = CompanyInfo.objects.get(playbook=self.playbook_instance)
            summary = company_info.summary
            index = company_info.index
        except CompanyInfo.DoesNotExist:
            logging.error(
                f"CompanyInfo does not exist in context feature builder for playbook {self.playbook_instance.id}"
            )
            return fix_xml_context_tags(raw_text[: 4 * budget])

        # when there is no summary or index, fall back to grabbing all the data
        # TODO: check if summary exist but not index?
        if not summary or not index:
            return fix_xml_context_tags(raw_text[: 4 * budget])

        summary_token_size = get_token_count(summary)
        # if summary + raw text fits in budget, return it.
        if summary_token_size + get_token_count(raw_text) <= budget:
            return (
                f"<context>\n<summary>\n{summary}\n</summary>\n</context>\n{raw_text}"
            )
        INDEX_CHUNK_SIZE = 250  # tokens
        STATIC_RETRIEVAL_BUDGET = (budget - summary_token_size) // (
            2 * INDEX_CHUNK_SIZE
        )  # vectors
        DYNAMIC_RETRIEVAL_BUDGET = (budget - summary_token_size) // (
            2 * INDEX_CHUNK_SIZE
        )  # vectors

        MODEL_BUDGET, llm = get_llm_for_embeddings()
        pinecone_index = PineconeVectorStore(
            index=self.pinecone_index,
            embedding=llm,
            text_key="text",
            namespace=index["namespace"],
        )

        static_docs = self.get_static_docs(
            STATIC_RETRIEVAL_BUDGET, index, pinecone_index
        )
        # we overfetch dynamic docs to account for deduplication and filtering
        dynamic_docs = self.get_dynamic_docs(
            2 * DYNAMIC_RETRIEVAL_BUDGET, index, pinecone_index
        )
        dynamic_docs_deduped = [doc for doc in dynamic_docs if doc not in static_docs]
        static_docs_string = "\n".join(static_docs)
        dynamic_docs_string = "\n".join(dynamic_docs_deduped)
        return f"<context>\n<summary>\n{summary}\n</summary>\n</context>\n{static_docs_string}\n\n{dynamic_docs_string}"

    def get_static_docs(self, budget, index, pinecone_index):
        static_context_query = self.get_static_index_query()

        static_docs = []
        if budget > 0:
            try:
                if self.use_mrmr:
                    static_docs = pinecone_index.max_marginal_relevance_search(
                        query=static_context_query,
                        k=budget,
                        fetch_k=5 * budget,
                        namespace=index["namespace"],
                        filter={"column_id": {"$eq": "company_info"}},
                    )
                else:
                    static_docs = pinecone_index.similarity_search(
                        query=static_context_query,
                        k=budget,
                        namespace=index["namespace"],
                        filter={"column_id": {"$eq": "company_info"}},
                    )
            except Exception as e:
                logging.error("Failed to retrieve static docs: %s", e)
                return []
        static_docs = [doc.page_content for doc in static_docs]
        return static_docs

    def get_dynamic_docs(self, budget, index, pinecone_index):
        dynamic_index_query = self.get_dynamic_index_query()
        dynamic_docs = []
        if budget > 0:
            try:
                if self.use_mrmr:
                    dynamic_docs = pinecone_index.max_marginal_relevance_search(
                        query=dynamic_index_query,
                        k=budget,
                        fetch_k=5 * budget,
                        namespace=index["namespace"],
                        filter={"column_id": {"$eq": "company_info"}},
                    )
                else:
                    dynamic_docs = pinecone_index.similarity_search(
                        query=dynamic_index_query,
                        k=budget,
                        namespace=index["namespace"],
                        filter={"column_id": {"$eq": "company_info"}},
                    )
            except Exception as e:
                logging.error("Failed to retrieve dynamic docs: %s", e)
                return []
        dynamic_docs = [doc.page_content for doc in dynamic_docs if doc.page_content]
        return dynamic_docs

    def register_feature_extractors(self):
        self.register_feature_extractor("company_context", self.extract_company_context)
