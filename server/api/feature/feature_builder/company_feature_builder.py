import logging

from ...models import CompanyInfo
from ...playbook_build.doc_loader import DocLoader
from .base_feature_builder import BaseFeatureBuilder


class CompanyFeatureBuilder(BaseFeatureBuilder):
    def __init__(self, gen_env):
        super().__init__(gen_env=gen_env)

    def extract_company_name(self):
        return self._data_wrapper.company_name

    @property
    def playbook_instance(self):
        return self._data_wrapper.playbook_instance

    def extract_company_summary(self):
        try:
            company_info = CompanyInfo.objects.get(playbook=self.playbook_instance)
            company_summary = company_info.summary
            if company_summary:
                return company_summary

            budget = self.get_budget("company_context")

            if "brief" in company_info.meta:
                brief = company_info.meta.get("brief", "")
                if brief:
                    brief = brief[: int(4 * budget)]
                    return brief

            (docs_build_processed, _docs_extract_errors, _docs_build_status) = (
                DocLoader({}).extract_docs(company_info.docs)
            )
            optimized_docs = docs_build_processed
            summary = "\n".join([doc.page_content for doc in optimized_docs])
            return summary[: int(4 * budget)]

        except CompanyInfo.DoesNotExist:
            logging.error(
                f"CompanyInfo does not exist in context feature builder for playbook {self.playbook_instance.id}"
            )
            return ""
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e}"
            )
            return ""

    def register_feature_extractors(self):
        self.register_feature_extractor("company_name", self.extract_company_name)
        self.register_feature_extractor("company_summary", self.extract_company_summary)
