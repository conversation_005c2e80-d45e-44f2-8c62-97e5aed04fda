import logging

from ...logger import log_data_wrapper_mismatch
from ...models import TargetInfo
from ...shared_types import ContentType, ContentTypeDetails
from ...utils import get_display_l2_key
from .base_feature_builder import BaseFeatureBuilder


class ContentFeatureBuilder(BaseFeatureBuilder):
    def __init__(
        self,
        gen_env,
    ):
        super().__init__(gen_env=gen_env)

    @property
    def target_params(self):
        return self._data_wrapper.target_params

    def extract_custom_prompts_string(self):
        if not self.aggregated_custom_instructions:
            return ""

        global_custom_prompts = [
            prompt.get("instruction", "")
            for prompt in self.aggregated_custom_instructions
            if not prompt.get("assets") and prompt.get("instruction")
        ]
        if not global_custom_prompts:
            return ""
        # dedupe
        global_custom_prompts = list(set(global_custom_prompts))
        global_custom_prompts.sort()
        custom_prompts_string = "\n----\n".join(
            [str(prompt) for prompt in global_custom_prompts]
        )
        return "\n----\n" + custom_prompts_string + "\n----\n"

    def extract_targets_string(self):
        targets = []
        # get the playbook target_info
        try:
            for l1_key, l2_key in self.target_params.items():
                field_name = ""
                target_info = TargetInfo.objects.get(
                    target_key=l2_key,
                    target_info_group__target_info_group_key=l1_key,
                    target_info_group__playbook=self.playbook_instance,
                )
                field_name = target_info.meta.get("field_name", "")
                remove_l2_key_suffix = target_info.meta.get("duplicate", False)

                if field_name:
                    targets.append(
                        f"{l1_key} - {field_name} - {get_display_l2_key(l2_key, remove_l2_key_suffix)}"
                    )
                else:
                    targets.append(
                        f"{l1_key} - {get_display_l2_key(l2_key, remove_l2_key_suffix)}"
                    )
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e}"
            )
            return ""
        targets_string = "\n".join(targets)
        return targets_string

    def extract_content_type(self):
        return self.content_type

    def extract_content_type_name(self):
        return ContentTypeDetails.get(self.content_type, {}).get(
            "name", "marketing message"
        )

    def extract_content_type_plural_name(self):
        return ContentTypeDetails.get(self.content_type, {}).get(
            "plural_name", "marketing messages"
        )

    def register_feature_extractors(self):
        self.register_feature_extractor(
            "custom_prompts_string", self.extract_custom_prompts_string
        )
        self.register_feature_extractor("targets", self.extract_targets_string)
        self.register_feature_extractor("content_type", self.extract_content_type)
        self.register_feature_extractor(
            "content_type_name", self.extract_content_type_name
        )
        self.register_feature_extractor(
            "content_type_plural_name", self.extract_content_type_plural_name
        )
