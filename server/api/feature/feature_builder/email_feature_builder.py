from .base_feature_builder import BaseFeatureBuilder


class EmailFeatureBuilder(BaseFeatureBuilder):
    def __init__(
        self,
        gen_env,
    ):
        super().__init__(gen_env=gen_env)

    def register_feature_extractors(self):
        self.register_feature_extractor(
            "email_template_content", self.extract_email_template_content
        )
        self.register_feature_extractor(
            "email_template_json_format", self.extract_email_template_json_format
        )

    def extract_email_template_content(self):
        if not self.personalization_template:
            raise Exception("Email template is not set")
        is_email_text_template = (
            "subjectLine" in self.personalization_template
            or "body" in self.personalization_template
        )
        if not is_email_text_template:
            raise Exception(
                f"Expect email_template to have 'subjectLine' and 'body'. Got: {self.personalization_template}"
            )
        subjectLine = self.personalization_template.get("subjectLine")
        body = self.personalization_template.get("body")
        if not subjectLine and not body:
            raise Exception(
                f"Expect email_template to have 'subjectLine' and 'body'. Got: {self.personalization_template}"
            )
        email_template_content = f"subject line: {subjectLine} \n\n body: {body}"
        return email_template_content

    def extract_email_template_json_format(self):
        return '{"email_subject": <subject>, "email_body": <body>}'
