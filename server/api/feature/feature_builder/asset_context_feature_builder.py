import logging
import os

import pinecone
from langchain_pinecone import PineconeVectorStore

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...llms import get_llm_for_embeddings
from ...logger import log_data_wrapper_mismatch
from ...models import AssetInfo, AssetInfoGroup, TargetInfo
from ...playbook_build.doc_loader import Doc<PERSON>oa<PERSON>
from ...playbook_build.object_builder import ObjectBuilder
from ...playbook_build.object_builder_asset import extract_asset_raw_text
from ...prompt.prompt_library.instruct_prompt.default_instruct import (
    STATIC_CONTEXT_QUERY,
)
from ...utils import fix_xml_context_tags, get_token_count
from ...validator.campaign_validator import CampaignGoal
from ..data_wrapper.data_wrapper import CampaignWrapper
from .base_feature_builder import BaseFeatureBuilder

_INDEX_CHUNK_SIZE = 250  # tokens


class AssetContextFeatureBuilder(BaseFeatureBuilder):
    def __init__(
        self,
        gen_env,
        use_mrmr=True,
        tone_reference=None,
        tone_reference_v2=None,
    ):
        super().__init__(gen_env=gen_env)
        self.use_mrmr = use_mrmr
        self.pinecone_index = pinecone.Pinecone().Index(
            host=os.environ.get("PINECONE_INDEX_HOST")
        )
        self.tone_reference = tone_reference

    @property
    def target_params(self):
        if isinstance(self._data_wrapper, CampaignWrapper):
            return {}
        return self._data_wrapper.target_params

    @property
    def content_type(self):
        if isinstance(self._data_wrapper, CampaignWrapper):
            return ""
        return self._data_wrapper.content_type

    @property
    def playbook_instance(self):
        return self._data_wrapper.playbook_instance

    def register_feature_extractors(self):
        self.register_feature_extractor("asset_context", self.extract_asset_context)
        self.register_feature_extractor(
            "asset_custom_prompts_string", self.extract_asset_custom_prompts_string
        )
        self.register_feature_extractor("tone_reference", self.extract_tone_reference)
        self.register_feature_extractor(
            "brand_guidelines", self.extract_brand_guidelines
        )

    def extract_brand_guidelines(self):
        brand_guidelines_text = self.get_content_type_brand_guidelines(self._gen_env)
        return brand_guidelines_text

    def get_content_type_brand_guidelines(self, gen_env):
        content_type = gen_env._data_wrapper.content_type
        playbook_instance = gen_env._data_wrapper.playbook_instance
        if not content_type:
            return None
        l1_key = "[TOFU Internal] Brand Guideline"  # hardcoded
        brand_guideline_l2_key = "Brand Guideline"  # hardcoded

        # Check if the asset exists
        brand_guideline_asset_group = AssetInfoGroup.objects.filter(
            asset_info_group_key=l1_key, playbook=playbook_instance
        ).first()
        if not brand_guideline_asset_group:
            return None
        brand_guideline_asset_info = AssetInfo.objects.filter(
            asset_info_group=brand_guideline_asset_group,
            asset_key=brand_guideline_l2_key,
        ).first()

        if not brand_guideline_asset_info:
            return None

        # First check if raw text of the guideline is under budget, if so just return it.
        brand_guideline_raw_text = extract_asset_raw_text(brand_guideline_asset_info)
        if get_token_count(brand_guideline_raw_text) < self.get_budget(
            "brand_guidelines"
        ):
            return brand_guideline_raw_text

        additional_info = brand_guideline_asset_info.additional_info
        if not additional_info:
            return None
        if content_type not in additional_info.get("guideline", {}):
            # fallback to extracting raw text.
            # doesn't use <context> XML tags.
            return brand_guideline_raw_text[: 4 * self.get_budget("brand_guidelines")]
        return additional_info.get("guideline", {}).get(content_type, "")

    def extract_tone_reference(self):
        if self.tone_reference_v2:
            return self.extract_tone_reference_v2()

        if self.tone_reference:
            tone_reference_string = ""
            num_of_refs = len(self.tone_reference)
            for asset_param in self.tone_reference:
                assets = asset_param.get("assets", {})
                if isinstance(assets, list):
                    if not assets:
                        raise ValueError("assets list cannot be empty")
                    assets = assets[0]
                if not isinstance(assets, dict):
                    raise Exception("assets must be a dict")
                for l1_key, l2_keys in assets.items():
                    if not isinstance(l2_keys, (list, tuple)):
                        raise ValueError(f"l2_keys for {l1_key} must be a list")
                    for l2_key in l2_keys:
                        asset_context = self.get_asset_writing_sample(
                            l1_key,
                            l2_key,
                            max_token_size=int(
                                self.get_budget("tone_reference") / num_of_refs
                            ),
                            include_asset_name=False,
                        )
                        tone_reference_string += f"{asset_context}"
            return tone_reference_string
        else:
            return None

    def extract_tone_reference_v2(self):
        if not self.tone_reference_v2:
            logging.error("Tone reference v2 is not set")
            return None
        tone_reference_string = ""

        # Extract asset_ids correctly from the TofuToneReference structure
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(
            self.tone_reference_v2.tone_assets
        )
        if asset_group_ids:
            logging.error(
                f"Debug: asset group as tone reference is not supported for {self.content_group_instance.id}"
            )
        if not asset_ids:
            logging.error(
                f"Debug: no asset ids found for {self.content_group_instance.id}"
            )
            return ""

        asset_infos = AssetInfo.objects.filter(id__in=asset_ids)
        if not asset_infos:
            return ""

        tone_analysis = self.tone_reference_v2.tone_analysis.tone_analysis
        tone_summary = self.tone_reference_v2.tone_analysis.tone_analysis_summary
        budget_left = (
            self.get_budget("tone_reference")
            - get_token_count(tone_analysis)
            - get_token_count(tone_summary)
        )
        budget_left = max(budget_left, 0)

        num_of_refs = len(asset_infos)
        budget_per_ref = budget_left / num_of_refs
        for asset_info in asset_infos:
            asset_context = self.get_asset_writing_sample_v2(
                asset_info,
                max_token_size=int(budget_per_ref),
                include_asset_name=False,
            )
            tone_reference_string += f"{asset_context}"

        # add tone_analysis and tone_summary to the tone_reference_string
        if tone_analysis:
            tone_reference_string += f"<toneAnalysis>\n{tone_analysis}\n</toneAnalysis>"
        if tone_summary:
            tone_reference_string += f"<toneSummary>\n{tone_summary}\n</toneSummary>"
        return tone_reference_string

    def extract_asset_custom_prompts_string(self):
        if not self.aggregated_custom_instructions:  # no custom prompts
            return None

        asset_custom_prompts = [
            f"{prompt.get('assets')}: {prompt.get('instruction', '')}"
            for prompt in self.aggregated_custom_instructions
            if prompt.get("assets") and prompt.get("instruction")
        ]
        if not asset_custom_prompts:
            return None

        custom_prompts_string = "\n----\n".join(
            [str(prompt) for prompt in asset_custom_prompts]
        )
        return "\n----\n" + custom_prompts_string + "\n----\n"

    def extract_asset_context(self):
        asset_context_string = ""
        if (
            self.aggregated_asset_params is not None
            and len(self.aggregated_asset_params) > 0
        ):
            num_of_assets = len(self.aggregated_asset_params)
            if num_of_assets > 30:
                raise Exception("Too many assets: %s", num_of_assets)
            self.budgets["asset_context"] = max(
                self.budgets["asset_context"], num_of_assets * 1000
            )
            for asset_param in self.aggregated_asset_params:
                asset_param_instruct = asset_param.get("instruction", "")
                assets = asset_param.get("assets", {})
                if isinstance(assets, list):
                    assets = assets[0]
                if not isinstance(assets, dict):
                    raise Exception("assets must be a dict")
                for l1_key, l2_key in assets.items():
                    asset_context = self.get_asset_context(
                        l1_key,
                        l2_key,
                        max_token_size=int(
                            self.get_budget("asset_context") / num_of_assets
                        ),
                    )
                    asset_context_string += f"{asset_context}\n"
                if asset_param_instruct:
                    asset_context_string += f"\n<customInstruction>\n{asset_param_instruct}\n</customInstruction>"

        # check for content collection level assets
        if (
            self._data_wrapper.content_goal == CampaignGoal.SeqPersonalizeTemplate
            and self._gen_settings.content_collection_plan_gen
        ):
            content_collection_instructions = (
                self.content_group_params.get("content_collection", {}) or {}
            ).get("custom_instructions", [])

            for custom_instruction in content_collection_instructions:
                assets = custom_instruction.get("assets", {})
                if not assets:
                    continue
                instruction = custom_instruction.get("instruction", "")
                if isinstance(assets, list):
                    assets = assets[0]
                if not isinstance(assets, dict):
                    raise Exception("assets must be a dict")
                for l1_key, l2_key in assets.items():
                    if isinstance(l2_key, list):
                        l2_key = l2_key[0]
                    if not isinstance(l2_key, str):
                        raise Exception("l2_key must be a str, got %s", type(l2_key))
                    asset_context = self.get_asset_context(
                        l1_key,
                        l2_key,
                        max_token_size=int(self.get_budget("asset_context")),
                    )
                    asset_context_string += f"{asset_context}\n"
                    asset_context_string += (
                        f"\n<customInstruction>\n{instruction}\n</customInstruction>"
                    )

        return asset_context_string

    def get_static_index_query(self):
        return STATIC_CONTEXT_QUERY

    def get_dynamic_index_query(self, l1_key, l2_key):
        context_query = self.content_type
        # If there are asset specific prompt params, use those instead.
        asset_custom_instructions = []
        for asset_param in self.aggregated_asset_params:
            if not asset_param.get("instruction"):
                continue
            assets = asset_param.get("assets", {})
            if isinstance(assets, list):
                assets = assets[0]
            if not isinstance(assets, dict):
                raise Exception("assets must be a dict")
            if l1_key in assets and l2_key in assets.values():
                asset_custom_instructions.append(asset_param.get("instruction"))
        if asset_custom_instructions:
            context_query += "\n" + "\n".join(asset_custom_instructions)
            return context_query

        try:
            for l1_key, l2_key in self.target_params.items():
                context_query += f"\n{l1_key}\n{l2_key}"
                target_info = TargetInfo.objects.get(
                    target_key=l2_key,
                    target_info_group__target_info_group_key=l1_key,
                    target_info_group__playbook=self.playbook_instance,
                )
                value_prop = target_info.value_prop
                summary = target_info.summary

                additional_info = target_info.additional_info or {}
                tofu_research_items = additional_info.get("tofu_research", {})
                tofu_research_results = [
                    item["result"]
                    for item in tofu_research_items.values()
                    if "result" in item
                ]
                tofu_research_result = "\n".join(tofu_research_results)

                context_query += f"\n{summary}\n{value_prop}"

                if tofu_research_result:
                    context_query += f"\n{tofu_research_result}"
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e}"
            )
            return context_query

        if not self.target_params:
            # repurposing content case
            context_query += "\n" + self.get_static_index_query()
        for item in self.aggregated_custom_instructions:
            context_query += "\n" + str(item)
        if not context_query:
            context_query = self.get_static_index_query()
        return context_query

    def get_asset_writing_sample(
        self, l1_key, l2_key, max_token_size=2000, include_asset_name=True
    ):
        # hardcode max_token_size to max of 3000 per asset.
        max_token_size = min(max_token_size, 3000)
        try:
            asset_info = AssetInfo.objects.get(
                asset_key=l2_key,
                asset_info_group__asset_info_group_key=l1_key,
                asset_info_group__playbook=self.playbook_instance,
            )
            docs = ObjectBuilder.get_builder(asset_info).extract_docs()
        except AssetInfo.DoesNotExist:
            logging.error(f"Asset {l1_key} - {l2_key} does not exist")
            return f"## {l1_key} - {l2_key}"
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e} for asset {l1_key} - {l2_key}"
            )

            # fallback logic
            return f"## {l1_key} - {l2_key}"

        # grab the first x tokens of the document.
        docs = [doc.page_content for doc in docs]
        raw_text = "\n------\n".join(docs)
        if asset_info.additional_info:
            tone_analysis = str(asset_info.additional_info.get("tone_analysis", ""))
        else:
            tone_analysis = None
        raw_context = fix_xml_context_tags(
            raw_text[: 4 * max_token_size], supress_error=True
        )
        tone_context = ""
        if tone_analysis:
            tone_context = f"<toneAnalysis>\n{tone_analysis}\n</toneAnalysis>"
        if include_asset_name:
            return f"""<asset name="{l1_key} - {l2_key}">\n{raw_context}\n{tone_context}\n</asset>"""
        else:
            return f"""<asset>\n{raw_context}\n{tone_context}\n</asset>"""

    def get_asset_writing_sample_v2(
        self, asset_info, max_token_size=2000, include_asset_name=True
    ):
        # hardcode max_token_size to max of 3000 per asset.
        max_token_size = min(max_token_size, 3000)
        l2_key = asset_info.asset_key
        l1_key = asset_info.asset_info_group.asset_info_group_key
        try:
            docs = ObjectBuilder.get_builder(asset_info).extract_docs()
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e} for asset {l1_key} - {l2_key}"
            )
            return f"## {l1_key} - {l2_key}"

        # grab the first x tokens of the document.
        docs = [doc.page_content for doc in docs]
        raw_text = "\n------\n".join(docs)
        raw_context = fix_xml_context_tags(
            raw_text[: 4 * max_token_size], supress_error=True
        )
        if include_asset_name:
            return f"""<asset name="{l1_key} - {l2_key}">\n{raw_context}\n</asset>"""
        else:
            return f"""<asset>\n{raw_context}\n</asset>"""

    def get_asset_context(
        self, l1_key, l2_key, max_token_size=1200, additional_dynamic_index_query=None
    ):
        try:
            try:
                asset_info = AssetInfo.objects.get(
                    asset_key=l2_key,
                    asset_info_group__asset_info_group_key=l1_key,
                    asset_info_group__playbook=self.playbook_instance,
                )
            except AssetInfo.DoesNotExist:
                # try again by trimming the spaces of the asset keys
                asset_info = AssetInfo.objects.get(
                    asset_key=l2_key.strip(),
                    asset_info_group__asset_info_group_key=l1_key.strip(),
                    asset_info_group__playbook=self.playbook_instance,
                )

            # If we get here, one of the gets succeeded
            summary = asset_info.summary
            index = asset_info.index
            docs = ObjectBuilder.get_builder(asset_info).extract_docs()

        except AssetInfo.DoesNotExist as e:
            # Both attempts failed
            logging.error(f"Asset {l1_key} - {l2_key} does not exist")
            raise Exception(f"Asset {l1_key} - {l2_key} does not exist") from e
        except Exception as e:
            logging.error(
                f"Error in context feature builder for playbook {self.playbook_instance.id}: {e} for asset {l1_key} - {l2_key}"
            )
            # fallback logic
            return f"## {l1_key} - {l2_key}"

        docs = DocLoader.optimize_docs(docs)
        raw_text = "\n------\n".join([doc.page_content for doc in docs])
        # when there is no summary or index, fall back to grabbing all the data
        if not summary or not index:
            return f"""<asset name="{l1_key} - {l2_key}">\n{fix_xml_context_tags(raw_text[: 4 * max_token_size])}\n</asset>"""

        summary_token_size = get_token_count(summary)

        # if summary + raw text size fits in max_token_size, return it
        if summary_token_size + get_token_count(raw_text) <= max_token_size:
            return f"""<asset name="{l1_key} - {l2_key}">\n<context>\n<summary>\n{summary}\n</summary>\n</context>\n{raw_text}\n</asset>"""
        idx_token_size = max(
            int(0.1 * max_token_size),  # at least 10% of max_token_size
            max_token_size - summary_token_size,
        )
        revised_summary_token_size = max(0, max_token_size - idx_token_size)

        # when summary is too long, truncate it
        if summary_token_size > revised_summary_token_size:
            summary = summary[: int(revised_summary_token_size * 4)]

        DYNAMIC_RETRIEVAL_BUDGET = idx_token_size // _INDEX_CHUNK_SIZE  # vectors

        MODEL_BUDGET, llm = get_llm_for_embeddings()
        pinecone_index = PineconeVectorStore(
            index=self.pinecone_index,
            embedding=llm,
            text_key="text",
            namespace=index["namespace"],
        )
        dynamic_docs_string = self.get_dynamic_docs_string(
            DYNAMIC_RETRIEVAL_BUDGET,
            index,
            pinecone_index,
            l1_key,
            l2_key,
            additional_dynamic_index_query,
        )
        return f"""<asset name="{l1_key} - {l2_key}">\n<context>\n<summary>\n{summary}\n</summary>\n</context>\n{dynamic_docs_string}\n</asset>"""

    def get_dynamic_docs_string(
        self,
        budget,
        index,
        pinecone_index,
        l1_key,
        l2_key,
        additional_dynamic_index_query=None,
    ):
        dynamic_index_query = self.get_dynamic_index_query(l1_key, l2_key)
        if additional_dynamic_index_query:
            dynamic_index_query += "\n" + additional_dynamic_index_query
        dynamic_docs = []
        if budget > 0:
            try:
                if self.use_mrmr:
                    dynamic_docs = pinecone_index.max_marginal_relevance_search(
                        query=dynamic_index_query,
                        k=budget,
                        fetch_k=5 * budget,
                        namespace=index["namespace"],
                        filter={
                            "$and": [{"column_id": "assets"}],
                            "$and": [{"key_ids": l1_key}, {"key_ids": l2_key}],
                        },
                    )
                else:
                    dynamic_docs = pinecone_index.similarity_search(
                        query=dynamic_index_query,
                        k=budget,
                        namespace=index["namespace"],
                        filter={
                            "$and": [{"column_id": "assets"}],
                            "$and": [{"key_ids": l1_key}, {"key_ids": l2_key}],
                        },
                    )
            except Exception as e:
                logging.error("Failed to retrieve dynamic docs: %s", e)
                return ""

        dynamic_docs_string = "\n".join([doc.page_content for doc in dynamic_docs])
        return dynamic_docs_string
