from ...models import ContentGroup
from ...shared_types import ContentTypeDetails
from ...utils import find_common_values
from .base_feature_builder import BaseFeatureBuilder


class ContentCollectionFeatureBuilder(BaseFeatureBuilder):
    def __init__(self, gen_env, content_collection) -> None:
        super().__init__(gen_env)
        self.content_collection = content_collection

    def extract_content_collection_graph_desc(self):
        # Assume self.content_collection is of the form {content_group_id: content_group_params.content_collection}
        statements = []

        # Find the root of the graph with "prev": null
        root_nodes = [
            content_group_id
            for content_group_id, content_collection in self.content_collection.content_collection_map.items()
            if content_collection.get("prev", None) is None
        ]
        if len(root_nodes) == 0:
            raise ValueError("No root node found in content collection graph")

        working_stack = []

        # Ensure that content_collection_map is initialized and not None
        if self.content_collection.content_collection_map is None:
            raise ValueError("No content collection map found")

        for content_group_id in root_nodes:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_group_name = content_group.content_group_name
            content_type = content_group.content_group_params.get("content_type", None)
            statements.append(
                f"Start with {content_group_name}, a {content_type} with no dependencies"
            )

            content_collection_params = (
                self.content_collection.content_collection_map.get(content_group_id, {})
            )
            if content_collection_params:
                next_ids = content_collection_params.get("next", [])
                if next_ids:
                    for id in next_ids:
                        if id is not None:
                            working_stack.append(id)

        while working_stack:
            content_group_id = working_stack.pop()
            content_collection = self.content_collection.content_collection_map.get(
                content_group_id, {}
            )
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_group_name = content_group.content_group_name
            content_type = content_group.content_group_params.get("content_type", None)
            content_group_prev_ids = content_collection.get("prev", [])
            if content_group_prev_ids:
                content_group_prev_names = []
                for content_group_prev_id in content_group_prev_ids:
                    content_group_prev = ContentGroup.objects.get(
                        id=content_group_prev_id
                    )
                    content_group_prev_names.append(
                        content_group_prev.content_group_name
                    )
                content_group_prev_name = ",".join(content_group_prev_names)
                statements.append(
                    f"Then, {content_group_name}, a {content_type}, is dependent on {content_group_prev_name}"
                )
            content_group_next_ids = content_collection.get("next", [])
            if content_group_next_ids:
                working_stack.extend(content_group_next_ids)

        return "\n".join(statements)

    def extract_custom_prompts_string(self):
        global_custom_prompts = self.get_content_collection_custom_prompts()
        if not global_custom_prompts:
            return ""

        custom_prompts_string = "\n----\n".join(
            [str(prompt) for prompt in global_custom_prompts]
        )
        return "\n----\n" + custom_prompts_string + "\n----\n"

    def get_content_collection_custom_prompts(self):
        custom_prompts = []
        individual_content_group_custom_prompts = {}
        campaign_custom_prompts = []

        # get content collection level custom instructions.
        for (
            content_group_id,
            content_collection,
        ) in self.content_collection.content_collection_map.items():
            content_collection_instructions = content_collection.get(
                "custom_instructions", []
            )
            for instruct in content_collection_instructions:
                instruction = instruct.get("instruction", "")
                if instruction:
                    content_collection_instruction = (
                        f"From content collection: {instruction}"
                    )
                    if content_collection_instruction not in custom_prompts:
                        custom_prompts.append(content_collection_instruction)

        # get campaign and content group level custom instructions.
        for content_group_id in self.content_collection.content_collection_map:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_group_params = content_group.content_group_params
            content_group_custom_prompts = content_group_params.get(
                "custom_instructions", []
            )
            for prompt in content_group_custom_prompts:
                instruction = prompt.get("instruction", "")
                if instruction:
                    if content_group_id not in individual_content_group_custom_prompts:
                        individual_content_group_custom_prompts[content_group_id] = []
                    individual_content_group_custom_prompts[content_group_id].append(
                        instruction
                    )

            campaign = content_group.campaign
            if campaign:
                campaign_prompt_params = campaign.campaign_params.get(
                    "custom_instructions", []
                )
                for prompt in campaign_prompt_params:
                    instruction = prompt.get("instruction", "")
                    if instruction:
                        campaign_custom_prompts.append(
                            f"General instructions: {instruction}"
                        )

        custom_prompts.extend(list(set(campaign_custom_prompts)))

        if not individual_content_group_custom_prompts:
            return custom_prompts

        # Add the remaining custom prompts to the global custom prompts
        for content_group_id in individual_content_group_custom_prompts:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_group_name = content_group.content_group_name
            for prompt in individual_content_group_custom_prompts[content_group_id]:
                if prompt:
                    custom_prompts.append(f"From {content_group_name}: {prompt}")

        return custom_prompts

    def extract_content_type(self):
        # only if every content group in this content collection has the same content type.
        content_types = []
        for content_group_id in self.content_collection.content_collection_map:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_type = content_group.content_group_params.get("content_type", "")
            content_types.append(content_type)

        if len(set(content_types)) == 1:
            return content_types[0]
        return ""

    def extract_content_type_name(self):
        return ContentTypeDetails.get(self.content_type, {}).get(
            "name", "marketing message"
        )

    def extract_content_type_plural_name(self):
        return ContentTypeDetails.get(self.content_type, {}).get(
            "plural_name", "marketing messages"
        )

    def register_feature_extractors(self):
        self.register_feature_extractor(
            "content_collection_graph_desc", self.extract_content_collection_graph_desc
        )
        self.register_feature_extractor(
            "custom_prompts_string", self.extract_custom_prompts_string
        )
        self.register_feature_extractor("content_type", self.extract_content_type)
        self.register_feature_extractor(
            "content_type_name", self.extract_content_type_name
        )
        self.register_feature_extractor(
            "content_type_plural_name", self.extract_content_type_plural_name
        )
