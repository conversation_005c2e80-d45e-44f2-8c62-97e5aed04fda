from .base_feature_builder import BaseFeatureBuilder


class TemplateGenerationFeatureBuilder(BaseFeatureBuilder):
    def __init__(self, gen_env) -> None:
        super().__init__(gen_env)

    def extract_email_json_format(self):
        return '\{ "subject": "", "body": "" \}'

    def extract_linkedin_ads_json_format(self):
        return '\{ "introductory-text": "", "headline": "", "description": "", "ad-copy":"" \}'

    def extract_template_length_instructions(self):
        if not self.template_length:
            return ""
        return self.template_length

    def extract_template_purpose_instructions(self):
        if not self.template_purpose:
            return ""
        return self.template_purpose

    def register_feature_extractors(self):
        self.register_feature_extractor(
            "email_json_format", self.extract_email_json_format
        )
        self.register_feature_extractor(
            "linkedin_ads_json_format", self.extract_linkedin_ads_json_format
        )
        self.register_feature_extractor(
            "template_length_instructions", self.extract_template_length_instructions
        )
        self.register_feature_extractor(
            "template_purpose_instructions", self.extract_template_purpose_instructions
        )
