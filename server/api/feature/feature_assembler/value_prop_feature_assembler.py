import logging

from ..feature_builder.company_feature_builder import CompanyFeatureBuilder
from .base_feature_assembler import BaseFeatureAssembler


class ValuePropFeatureAssembler(BaseFeatureAssembler):
    def __init__(
        self,
        model_budget,
        gen_env,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.model_budget = model_budget

    def get_features_needed(self):
        contexts = [
            "company_summary",
            "company_name",
        ]
        return contexts

    def get_budgets(self, features_needed):
        budgets = {}
        budgets["company_context"] = int(self.model_budget * 0.4)
        budgets["target_context"] = int(self.model_budget * 0.3)
        return budgets

    def register_builders(self):
        self.register_builder(CompanyFeatureBuilder(gen_env=self._gen_env))
