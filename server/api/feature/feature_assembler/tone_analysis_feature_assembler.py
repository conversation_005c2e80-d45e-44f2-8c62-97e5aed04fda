import logging

from ..feature_builder.tone_analysis_feature_builder import ToneAnalysisFeatureBuilder
from .base_feature_assembler import BaseFeatureAssembler


class ToneAnalysisFeatureAssembler(BaseFeatureAssembler):
    def __init__(
        self,
        object_docs,
        gen_env,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.object_docs = object_docs

    def get_features_needed(self):
        features = [
            "tone_example",
        ]
        return features

    def register_builders(self):
        self.register_builder(
            ToneAnalysisFeatureBuilder(
                object_docs=self.object_docs, gen_env=self._gen_env
            )
        )
