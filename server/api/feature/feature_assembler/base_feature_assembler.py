import logging
from abc import ABC, abstractmethod

from ..data_wrapper.data_wrapper import DataWrapperHolder
from ..feature_builder.base_feature_builder import BaseFeatureBuilder


class BaseFeatureAssembler(ABC, DataWrapperHolder):
    def __init__(self, gen_env) -> None:
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.builders = []
        self.has_built = False
        self.features = {}

    def register_builder(self, builder: BaseFeatureBuilder):
        self.builders.append(builder)

    @abstractmethod
    def register_builders(self):
        pass

    @abstractmethod
    def get_features_needed(self):
        pass

    def get_features(self):
        if not self.has_built:
            raise Exception("Features have not been built yet")
        return self.features

    # By default we don't set budgets for any builders
    def get_budgets(self, features_needed):
        return {}

    def extract(self, features_needed, budgets):
        self.register_builders()

        results = {}
        for builder in self.builders:
            builder.set_budgets(budgets)
            results.update(builder.build(features_needed))
        return results

    def build(self):
        if self.has_built:
            return self.features

        features_needed = self.get_features_needed()
        budgets = self.get_budgets(features_needed)
        self.features = self.extract(features_needed, budgets)
        self.has_built = True

        # TODO add check for missing features
        return self.features
