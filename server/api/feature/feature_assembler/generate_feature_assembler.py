import logging

from ...logger import log_data_wrapper_mismatch
from ...shared_types import ContentType
from ...validator.campaign_validator import CampaignGoal
from ..feature_builder.asset_context_feature_builder import AssetContextFeatureBuilder
from ..feature_builder.company_context_feature_builder import (
    CompanyContextFeatureBuilder,
)
from ..feature_builder.company_feature_builder import CompanyFeatureBuilder
from ..feature_builder.component_feature_builder import ComponentFeatureBuilder
from ..feature_builder.content_collection_feature_builder import (
    ContentCollectionFeatureBuilder,
)
from ..feature_builder.content_feature_builder import ContentFeatureBuilder
from ..feature_builder.email_feature_builder import EmailFeatureBuilder
from ..feature_builder.joint_component_feature_builder import (
    JointComponentFeatureBuilder,
)
from ..feature_builder.slides_feature_builder import SlidesFeatureBuilder
from ..feature_builder.target_context_feature_builder import TargetContextBuilder
from ..feature_builder.template_gen_feature_builder import (
    TemplateGenerationFeatureBuilder,
)
from .base_feature_assembler import BaseFeatureAssembler


class GenerateFeatureAssembler(BaseFeatureAssembler):
    def __init__(
        self,
        model_budget,
        gen_env,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.model_budget = model_budget

    @property
    def content_type(self):
        return self._data_wrapper.content_type

    @property
    def content_source_format(self):
        return self._data_wrapper.content_source_format

    @property
    def campaign_goal(self):
        return self._data_wrapper.content_goal

    @property
    def target_params(self):
        return self._data_wrapper.target_params

    def get_features_needed(self):
        features = [
            "company_context",
            "company_summary",
            "target_context",
            "company_name",
            "targets",
            "has_value_prop",
            "target_meta_types",
        ]
        if self._gen_settings.is_template_personalization:
            features += ["email_template_content", "email_template_json_format"]
        return features

    def get_budgets(self, features_needed):
        budgets = {}
        # TODO: we move asset builder to component level but budget has dependency
        # this should be resolved in the future
        if self.campaign_goal == "Repurpose Content":
            budgets["company_context"] = int(self.model_budget * 0.25)
            budgets["target_context"] = 0
        else:
            if (
                self.aggregated_asset_params is not None
                and len(self.aggregated_asset_params) > 0
            ):
                budgets["company_context"] = int(self.model_budget * 0.25)
                budgets["target_context"] = int(self.model_budget * 0.25)
            else:
                budgets["company_context"] = int(self.model_budget * 0.3)
                budgets["target_context"] = int(self.model_budget * 0.25)
        return budgets

    def register_builders(self):
        self.register_builder(
            TargetContextBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            CompanyFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            CompanyContextFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            EmailFeatureBuilder(
                gen_env=self._gen_env,
            )
        )


class ComponentGenerateFeatureAssembler(BaseFeatureAssembler):
    def __init__(
        self,
        model_budget,
        example_content,
        prev_gen_variations,
        gen_env,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.model_budget = model_budget
        self.example_content = example_content
        self.prev_gen_variations = prev_gen_variations

    def get_budgets(self, features_needed):
        budgets = {}
        # TODO: we move asset builder to component level but budget has dependency
        # this should be resolved in the future
        if self.campaign_goal == "Repurpose Content":
            budgets["asset_context"] = int(self.model_budget * 0.3)
        elif "asset_context" in features_needed:
            budgets["asset_context"] = int(self.model_budget * 0.15)
        if "tone_reference" in features_needed:
            budgets["tone_reference"] = int(self.model_budget * 0.1)
        budgets["brand_guidelines"] = min(4500, int(self.model_budget * 0.15))
        return budgets

    def get_features_needed(self):
        features = [
            "custom_prompts_string",
            "example_text",
            "num_of_words",
            "targets",
            "component_type",
            "component_type_section",
            "content_type",
            "content_type_name",
            "content_type_plural_name",
            "brand_guidelines",
        ]
        if (
            self.template_settings and "tone_reference" in self.template_settings
        ) or self.tone_reference_v2:
            features += ["tone_reference"]

        if (
            self.aggregated_asset_params is not None
            and len(self.aggregated_asset_params) > 0
        ):
            features += ["asset_context", "asset_custom_prompts_string"]

        if self.campaign_goal == "Repurpose Content":
            features += ["repurpose_template_content"]

        elif self.campaign_goal == "Personalization":
            features += ["component_reviewed_contents_string"]
        if (
            self.content_source_format in ("Html", "PDF", "Text")
            and "Email" not in self.content_type
        ):
            features += ["preceding_element", "succeeding_element"]
            features += ["preceding_variation", "succeeding_variation"]
        if self.content_source_format == "Html":
            features += ["html_tag"]
        elif self.content_source_format == "PDF":
            features += ["pageNum"]
        if self.prev_gen_variations:
            features += ["previous_generation"]
        if (
            self.campaign_goal == "Repurpose Content"
            and (
                self.content_type == ContentType.SalesDeck
                or self.content_type == ContentType.SlideDeck
            )
            and self._data_wrapper.should_use_slides_template
        ):
            features += ["slides_placeholder_mapping"]
        return features

    def register_builders(self):
        self.register_builder(
            ComponentFeatureBuilder(
                self.example_content,
                self.prev_gen_variations,
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            ContentFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            AssetContextFeatureBuilder(
                gen_env=self._gen_env,
                tone_reference=self.template_settings.get("tone_reference", None),
                tone_reference_v2=self.tone_reference_v2,
            )
        )
        self.register_builder(SlidesFeatureBuilder(gen_env=self._gen_env))


class ComponentJointGenerateFeatureAssembler(BaseFeatureAssembler):

    def __init__(
        self,
        model_budget,
        all_components,
        prev_gen_variations,
        gen_env,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.model_budget = model_budget
        self.all_components = all_components
        self.prev_gen_variations = prev_gen_variations

    def get_budgets(self, features_needed):
        budgets = {}
        # TODO: we move asset builder to component level but budget has dependency
        # this should be resolved in the future
        if "asset_context" in features_needed:
            budgets["asset_context"] = int(self.model_budget * 0.15)
            if self.campaign_goal == "Repurpose Content":
                budgets["asset_context"] = int(self.model_budget * 0.3)
        if "tone_reference" in features_needed:
            budgets["tone_reference"] = int(self.model_budget * 0.1)
        budgets["brand_guidelines"] = min(4500, int(self.model_budget * 0.15))
        return budgets

    def get_features_needed(self):
        features = [
            "custom_prompts_string",
            "targets",
            "content_type",
            "content_type_name",
            "content_type_plural_name",
            "json_format",
            "component_level_custom_prompts",
            "brand_guidelines",
        ]
        if self.campaign_goal == "Personalization":
            features += ["contents_xml", "reviewed_contents_string"]

        elif self.campaign_goal == "Repurpose Content":
            features += ["repurpose_template_content", "generic_joint_format"]

        if (
            self.aggregated_asset_params is not None
            and len(self.aggregated_asset_params) > 0
        ):
            features += ["asset_context", "asset_custom_prompts_string"]

        if (
            self.template_settings and "tone_reference" in self.template_settings
        ) or self.tone_reference_v2:
            features += ["tone_reference"]

        return features

    def register_builders(self):
        self.register_builder(
            ContentFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            JointComponentFeatureBuilder(
                self.all_components,
                self.prev_gen_variations,
                gen_env=self._gen_env,
            )
        )

        self.register_builder(
            AssetContextFeatureBuilder(
                gen_env=self._gen_env,
                tone_reference=self.template_settings.get("tone_reference", None),
                tone_reference_v2=self.tone_reference_v2,
            )
        )


class TemplateGenerationFeatureAssembler(BaseFeatureAssembler):
    def __init__(
        self,
        gen_env,
        model_budget=8000,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.model_budget = model_budget

    def get_budgets(self, features_needed):
        budgets = {}
        # TODO: we move asset builder to component level but budget has dependency
        # this should be resolved in the future
        if "asset_context" in features_needed:
            budgets["asset_context"] = int(self.model_budget * 0.15)
        budgets["brand_guidelines"] = min(4500, int(self.model_budget * 0.15))
        return budgets

    def get_features_needed(self):
        features = [
            "targets",
            "custom_prompts_string",
            "content_type",
            "content_type_name",
            "brand_guidelines",
            "template_length_instructions",
            "template_purpose_instructions",
        ]
        if self._data_wrapper.is_email:
            features += ["email_json_format"]
        elif self._data_wrapper.is_linkedin_ads_v2:
            features += ["linkedin_ads_json_format"]
        if (
            self.aggregated_asset_params is not None
            and len(self.aggregated_asset_params) > 0
        ):
            features += ["asset_context", "asset_custom_prompts_string"]
        return features

    def register_builders(self):
        self.register_builder(
            ContentFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(TemplateGenerationFeatureBuilder(gen_env=self._gen_env))
        self.register_builder(
            AssetContextFeatureBuilder(
                gen_env=self._gen_env,
            )
        )


class ContentCollectionFeatureAssembler(BaseFeatureAssembler):

    def __init__(
        self,
        playbook_handler,
        gen_env,
        content_collection,
        model_budget=8000,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.playbook_handler = playbook_handler
        self.model_budget = model_budget
        self.content_collection = content_collection
        self.content_goal = self._data_wrapper.content_goal

    def get_budgets(self, features_needed):
        budgets = {}
        budgets["company_context"] = int(self.model_budget * 0.4)
        # TODO: we move asset builder to component level but budget has dependency
        # this should be resolved in the future
        if "asset_context" in features_needed and "target_context" in features_needed:
            # When both contexts are needed, split budget between them
            budgets["asset_context"] = int(self.model_budget * 0.3)
            budgets["target_context"] = int(self.model_budget * 0.1)
        else:
            # When only one context is needed, allocate full budget to it
            if "asset_context" in features_needed:
                budgets["asset_context"] = int(self.model_budget * 0.4)
            if "target_context" in features_needed:
                budgets["target_context"] = int(self.model_budget * 0.4)
        return budgets

    def get_features_needed(self):
        features = [
            "company_context",
            "content_type_name",
            "company_name",
            "custom_prompts_string",
            "content_collection_graph_desc",
            "content_type",
        ]
        if (
            self.aggregated_asset_params is not None
            and len(self.aggregated_asset_params) > 0
        ) or self.content_goal == CampaignGoal.SeqPersonalizeTemplate:
            features += ["asset_context", "asset_custom_prompts_string"]
        if self.content_goal == CampaignGoal.SeqPersonalizeTemplate:
            features += ["target_context", "targets"]
        return features

    def register_builders(self):
        self.register_builder(
            ContentCollectionFeatureBuilder(
                gen_env=self._gen_env, content_collection=self.content_collection
            )
        )
        self.register_builder(
            CompanyContextFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            CompanyFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            AssetContextFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
        self.register_builder(
            TargetContextBuilder(
                gen_env=self._gen_env,
            )
        )
