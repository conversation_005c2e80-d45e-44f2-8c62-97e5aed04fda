import logging

from ..feature_builder.company_context_feature_builder import (
    CompanyContextFeatureBuilder,
)
from ..feature_builder.company_feature_builder import CompanyFeatureBuilder
from .generate_feature_assembler import ComponentGenerateFeatureAssembler


class FreeGenFeatureAssembler(ComponentGenerateFeatureAssembler):
    def __init__(
        self,
        gen_env,
        component,
        model_budget=8000,
    ) -> None:
        # Initialize ComponentGenerateFeatureAssembler with the component as example_content
        super().__init__(
            model_budget=model_budget,
            example_content=component,
            prev_gen_variations={},  # Free_gen doesn't use previous variations
            gen_env=gen_env,
        )

    def get_budgets(self, features_needed):
        # Get component-level budgets from parent
        budgets = super().get_budgets(features_needed)

        # Add budget for company_context since it's used in repurpose generation
        # but ComponentGenerateFeatureAssembler doesn't allocate budget for it
        if "company_context" in features_needed:
            budgets["company_context"] = int(self.model_budget * 0.25)

        return budgets

    def get_features_needed(self):
        # Get all the standard repurpose features from parent
        features = super().get_features_needed()

        # Add company features (not included in ComponentGenerateFeatureAssembler)
        features.extend(
            [
                "company_name",
                "company_context",
                "company_summary",
            ]
        )

        # Add free_gen specific features
        features.extend(
            [
                "component_instructions",
                "parent_component_current_value",
            ]
        )

        return features

    def register_builders(self):
        # Register all the component-level builders from parent
        super().register_builders()

        # Add company feature builders (not included in ComponentGenerateFeatureAssembler)
        self.register_builder(
            CompanyFeatureBuilder(
                gen_env=self._gen_env,
            )
        )

        self.register_builder(
            CompanyContextFeatureBuilder(
                gen_env=self._gen_env,
            )
        )
