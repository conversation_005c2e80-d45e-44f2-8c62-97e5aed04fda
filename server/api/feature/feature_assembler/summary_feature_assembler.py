import logging

from ...utils import get_token_count
from ..feature_builder.company_feature_builder import CompanyFeatureBuilder
from .base_feature_assembler import BaseFeatureAssembler


class SummaryFeatureAssembler(BaseFeatureAssembler):
    def __init__(self) -> None:
        super().__init__()

    def get_features_needed(self):
        contexts = [
            "company_name",
        ]
        return contexts

    def register_builders(self):
        self.register_builder(CompanyFeatureBuilder())
