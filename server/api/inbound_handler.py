import logging
import os
import traceback
from typing import Optional

import requests
from django.core.cache import cache
from django.db.models import Q

from .constants.industry_list import (
    INBOUND_INDUSTRY_LIST,
    INBOUND_INDUSTRY_MAPPING,
    INBOUND_INDUSTRY_SKIP_MAPPING,
)
from .models import (
    Content,
    DebugLogEntry,
    InboundTypeChoices,
    PublicContent,
    TargetInfo,
)


class InboundProvider:
    class CachedResponse:
        def __init__(self, status_code, data):
            self.status_code = status_code
            self._data = data

        def json(self):
            return self._data

    @staticmethod
    def cache_key_happierleads(ip_address: str):
        return f"inbound_result_cache_raw:happierleads:{ip_address}"

    @staticmethod
    def cache_key_leadfeeder(ip_address: str):
        return f"inbound_result_cache_raw:leadfeeder:{ip_address}"

    @staticmethod
    def resolve_identification_happierleads(ip_address: str):
        cache_key = InboundProvider.cache_key_happierleads(ip_address)
        try:
            cached_data = cache.get(cache_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get happierleads cached data for {ip_address}: {e}"
            )
            cached_data = None
        if cached_data:
            return InboundProvider.CachedResponse(
                status_code=cached_data["status_code"], data=cached_data["data"]
            )

        # doc: https://api-docs.happierleads.com/#realtime-ip-reveal
        HAPPIERLEADS_PRIVATE_KEY = os.getenv("HAPPIERLEADS_PRIVATE_KEY")
        if not HAPPIERLEADS_PRIVATE_KEY:
            raise Exception("HAPPIERLEADS_PRIVATE_KEY is not set")

        url = f"https://rest-admin.happierleads.com/v3/reveal?ip={ip_address}&privateKey={HAPPIERLEADS_PRIVATE_KEY}"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 404 or response.status_code == 200:
            cache.set(
                cache_key,
                {"status_code": response.status_code, "data": response.json()},
                timeout=60 * 60 * 24 * 30,
            )
        else:
            logging.error(
                f"debug only: Failed to resolve identification from happierleads for {ip_address}: {response.status_code} {response.text}"
            )
        return response

    @staticmethod
    def resolve_identification_leadfeeder(ip_address: str):
        cache_key = InboundProvider.cache_key_leadfeeder(ip_address)
        try:
            cached_data = cache.get(cache_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get leadfeeder cached data for {ip_address}: {e}"
            )
            cached_data = None
        if cached_data:
            return InboundProvider.CachedResponse(
                status_code=cached_data["status_code"], data=cached_data["data"]
            )

        # doc: https://docs.leadfeeder.com/connectors/ip-enrich-api/#leadfeeder-ip-enrich-api

        LEADFEEDER_PRIVATE_KEY = os.getenv("LEADFEEDER_PRIVATE_KEY")
        if not LEADFEEDER_PRIVATE_KEY:
            raise Exception("LEADFEEDER_PRIVATE_KEY is not set")

        url = f"https://api.lf-discover.com/companies?ip={ip_address}"
        headers = {
            "Accept": "application/json",
            "X-API-KEY": LEADFEEDER_PRIVATE_KEY,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 404 or response.status_code == 200:
            cache.set(
                cache_key,
                {"status_code": response.status_code, "data": response.json()},
                timeout=60 * 60 * 24 * 30,
            )
        else:
            logging.error(
                f"debug only: Failed to resolve identification from leadfeeder for {ip_address}: {response.status_code} {response.text}"
            )
        return response

    @staticmethod
    def test_all_providers(ip_address: str):
        response_happierleads = InboundProvider.resolve_identification_happierleads(
            ip_address
        ).json()
        response_leadfeeder = InboundProvider.resolve_identification_leadfeeder(
            ip_address
        ).json()
        return response_happierleads, response_leadfeeder


class InboundMatcher:
    def __init__(
        self,
        passed_tofu_content_id: str,
        ip_address: str,
        cookie_id: str,
        url: str,
    ) -> None:
        self._passed_tofu_content_id = passed_tofu_content_id
        self._ip_address = ip_address
        self._cookie_id = cookie_id
        self._url = url

    def _match_public_content(self, company_domain, industry):
        matching_criteria = []
        if company_domain:
            matching_criteria.append(f"company_domain:{company_domain}")
        if False and industry and industry not in INBOUND_INDUSTRY_SKIP_MAPPING:
            mapped_industry = None
            if industry in INBOUND_INDUSTRY_LIST:
                mapped_industry = industry
            elif industry in INBOUND_INDUSTRY_MAPPING:
                mapped_industry = INBOUND_INDUSTRY_MAPPING[industry]
            if not mapped_industry:
                logging.error(f"Industry fails to resolve: {industry}")
            else:
                matching_criteria.append(f"industry:{mapped_industry}")
        if not matching_criteria:
            return None

        matching_criteria = [x.lower() for x in matching_criteria]

        # Start with an empty Q object
        query = Q()
        # Loop through the matching criteria and add each one as an OR condition
        for criterion in matching_criteria:
            query |= Q(matching_criteria__contains=[criterion])

        # Apply the OR conditions along with the tofu_content_id filter
        public_content_entries = PublicContent.objects.filter(
            Q(tofu_content_id=self._passed_tofu_content_id)
            & Q(inbound_type=InboundTypeChoices.PARTIAL_INBOUND)
            & query
        )
        if not public_content_entries:
            return None
        if public_content_entries.count() > 1:
            logging.error(
                f"Found multiple tofu data for company data: {company_domain}, {industry}"
            )
        public_content_entry = public_content_entries.first()
        return public_content_entry

    @property
    def cache_key_happierleads(self):
        return f"inbound_result_cache:happierleads:{self._ip_address}"

    @property
    def cache_key_leadfeeder(self):
        return f"inbound_result_cache:leadfeeder:{self._ip_address}"

    def _resolve_identification_happierleads(self):
        # check cache first
        try:
            cached_data = cache.get(self.cache_key_happierleads)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get happierleads cached data for {self._ip_address}: {e}"
            )
            cached_data = None
        if cached_data:
            company_data = cached_data.get("company_data")
        else:
            response = InboundProvider.resolve_identification_happierleads(
                self._ip_address
            )
            if response.status_code != 200:
                return None
            company_data = response.json()
            cache.set(
                self.cache_key_happierleads, company_data, timeout=60 * 60 * 24 * 30
            )
        if not company_data:
            logging.error(
                f"debug only: inbound happierleads result for {self._ip_address} has return code 200 but no data: {company_data}"
            )
            return None

        if company_data.get("type") != "business":
            return None
        domain = company_data.get("domain", "")
        industry = company_data.get("industry", "")
        return (
            self._match_public_content(
                company_domain=domain,
                industry=industry,
            ),
            company_data,
        )

    def _resolve_identification_leadfeeder(self):
        # check cache first
        try:
            cached_data = cache.get(self.cache_key_leadfeeder)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get leadfeeder cached data for {self._ip_address}: {e}"
            )
            cached_data = None
        if cached_data:
            company_data = cached_data.get("company_data")
        else:
            # doc: https://docs.leadfeeder.com/connectors/ip-enrich-api/#leadfeeder-ip-enrich-api
            response = InboundProvider.resolve_identification_leadfeeder(
                self._ip_address
            )

            # Check the response status code
            if response.status_code != 200:
                return None

            company_data = response.json()
            cache.set(
                self.cache_key_leadfeeder, company_data, timeout=60 * 60 * 24 * 30
            )
        if not company_data:
            logging.error(
                f"debug only: inbound leadfeeder result for {self._ip_address} has return code 200 but no data: {company_data}"
            )
            return None

        domain = company_data.get("company", {}).get("domain", "")
        industry = company_data.get("company", {}).get("industries", {}).get("name", "")
        return (
            self._match_public_content(
                company_domain=domain,
                industry=industry,
            ),
            company_data,
        )

    def resolve_identification(self):
        try:
            run_with_happierleads = True
            run_with_leadfeeder = True

            if not run_with_happierleads and not run_with_leadfeeder:
                raise Exception("No identification service is enabled")

            public_content_entry_happierleads = None
            matched_raw_data_happierleads = None

            public_content_entry_leadfeeder = None
            matched_raw_data_leadfeeder = None

            if run_with_happierleads:
                try:
                    match_result = self._resolve_identification_happierleads()
                    if match_result:
                        (
                            public_content_entry_happierleads,
                            matched_raw_data_happierleads,
                        ) = match_result
                except Exception as e:
                    logging.error(
                        f"Failed to resolve identification from happierleads: {e}\n{traceback.format_exc()}"
                    )
                    public_content_entry_happierleads = None

            if run_with_leadfeeder:
                try:
                    match_result = self._resolve_identification_leadfeeder()
                    if match_result:
                        (
                            public_content_entry_leadfeeder,
                            matched_raw_data_leadfeeder,
                        ) = match_result
                except Exception as e:
                    logging.exception(
                        f"Failed to resolve identification from leadfeeder: {e}"
                    )
                    public_content_entry_leadfeeder = None

            # debugging
            if (
                run_with_leadfeeder
                and run_with_happierleads
                and public_content_entry_happierleads != public_content_entry_leadfeeder
            ):
                logging.error(
                    f"debug only: Public content entries from two services are different: {public_content_entry_happierleads}, {public_content_entry_leadfeeder} based on happierleads return {matched_raw_data_happierleads} and leadfeeder return {matched_raw_data_leadfeeder}"
                )

            try:
                DebugLogEntry.objects.create(
                    entry_type="inbound_vendor_result",
                    payload={
                        "passed_tofu_content_id": self._passed_tofu_content_id,
                        "ip_address": self._ip_address,
                        "cookie_id": self._cookie_id,
                        "happierleads_response": matched_raw_data_happierleads,
                        "happierleads_match": (
                            {
                                "tofu_content_id": public_content_entry_happierleads.tofu_content_id,
                                "tofu_slug": public_content_entry_happierleads.tofu_slug,
                            }
                            if public_content_entry_happierleads
                            else None
                        ),
                        "leadfeeder_response": matched_raw_data_leadfeeder,
                        "leadfeeder_match": (
                            {
                                "tofu_content_id": public_content_entry_leadfeeder.tofu_content_id,
                                "tofu_slug": public_content_entry_leadfeeder.tofu_slug,
                            }
                            if public_content_entry_leadfeeder
                            else None
                        ),
                    },
                )
            except Exception as e:
                logging.error(
                    f"Failed to log debug entry for inbound vendor result: {e}"
                )

            public_content_entry = (
                public_content_entry_leadfeeder or public_content_entry_happierleads
            )  # prefer leadfeeder for higher precision

        except Exception as e:
            logging.error(
                f"Failed to resolve identification: {e}\n{traceback.format_exc()}"
            )
            public_content_entry = None

        return public_content_entry


class InboundHandler:
    def __init__(
        self,
        ip_address: str,
        cookie_id: str,
        passed_tofu_content_id: Optional[str] = None,
        url: str = "",
    ) -> None:
        self._passed_tofu_content_id = passed_tofu_content_id  # passed
        self._ip_address = ip_address
        self._cookie_id = cookie_id
        self._url = url

    @property
    def cache_key_ip(self):
        return f"inbound_cache:{self._passed_tofu_content_id}:{self._ip_address}"

    @property
    def cache_key_cookie(self):
        return f"inbound_cache:{self._passed_tofu_content_id}:{self._cookie_id}"

    def _check_cached_data(self):
        try:
            cached_data = cache.get(self.cache_key_ip) or cache.get(
                self.cache_key_cookie
            )
        except Exception as e:
            logging.exception(
                f"debug: Failed to get inbound cached data for {self._ip_address}: {e}"
            )
            cached_data = None
        if not cached_data:
            return None

        tofu_content_id = cached_data.get("tofu_content_id")
        tofu_slug = cached_data.get("tofu_slug")
        variations = cached_data.get("variations")
        return (tofu_content_id, tofu_slug, variations)

    def _save_to_cache(self, tofu_content_id, tofu_slug, variations):
        cached_data = {
            "tofu_content_id": tofu_content_id,
            "tofu_slug": tofu_slug,
            "variations": variations,
        }
        expire_time = 60 * 60 * 24 * 30  # 30 days

        cache.set(
            self.cache_key_ip,
            cached_data,
            timeout=expire_time,
        )
        cache.set(
            self.cache_key_cookie,
            cached_data,
            timeout=expire_time,
        )

    def is_eligible_for_partial_inbound(self):
        return PublicContent.objects.filter(
            tofu_content_id=self._passed_tofu_content_id,
            inbound_type=InboundTypeChoices.PARTIAL_INBOUND,
        ).exists()

    def get_inbound_components(self) -> dict:
        # check cache first
        cached_data = self._check_cached_data()
        if cached_data:
            tofu_content_id, tofu_slug, variations = cached_data
            logging.info(
                f"inbound cache hit for {self._passed_tofu_content_id}, {self._ip_address}, {self._cookie_id}"
            )
            return (tofu_content_id, tofu_slug, variations)

        content_entry = InboundMatcher(
            self._passed_tofu_content_id, self._ip_address, self._cookie_id, self._url
        ).resolve_identification()
        if not content_entry:
            logging.info(
                f"no content entry found for {self._passed_tofu_content_id}, {self._ip_address}, {self._cookie_id}, {self._url}"
            )
            return None

        # save to cache
        self._save_to_cache(
            content_entry.tofu_content_id,
            content_entry.tofu_slug,
            content_entry.variations,
        )
        return (
            content_entry.tofu_content_id,
            content_entry.tofu_slug,
            content_entry.variations,
        )


def get_matching_criteria(content_instance: Content):
    matching_criteria = set()
    targets = content_instance.content_params.get("targets", {})

    # Batch query for all target objects
    target_objects = TargetInfo.objects.filter(
        target_key__in=targets.values(),
        target_info_group__target_info_group_key__in=targets.keys(),
        target_info_group__playbook=content_instance.playbook,
    ).select_related("target_info_group")

    # Create a dictionary for faster lookups
    target_dict = {
        (obj.target_info_group.target_info_group_key, obj.target_key): obj
        for obj in target_objects
    }

    for target_group_key, target_key in targets.items():
        target_object = target_dict.get((target_group_key, target_key))

        if not target_object:
            logging.error(
                f"Target object not found for {target_group_key}-{target_key}"
            )
            continue

        additional_info = target_object.additional_info or {}

        if company_domain := additional_info.get("company_domain"):
            matching_criteria.add(f"company_domain:{company_domain.lower()}")

        for industry in additional_info.get("industries", []):
            matching_criteria.add(f"industry:{industry.lower()}")

    return list(matching_criteria)
