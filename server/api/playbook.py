import copy
import csv
import hashlib
import json
import logging
import re
import tempfile
from typing import Dict

import markdown2
from django.apps import apps
from django.db import transaction
from django.db.models import <PERSON>teger<PERSON>ield, Sum, Value
from django.db.models.expressions import RawSQL
from django.db.models.functions import Coalesce

from .models import AssetInfo, AssetInfoGroup, EventLogs, TargetInfo, TargetInfoGroup
from .playbook_build.external_info_builder import ExternalInfoBuilder
from .playbook_build.object_builder_asset import extract_asset_raw_text
from .s3_utils import check_file_exists, upload_file
from .utils import create_presigned_url


class PlaybookHandler:
    def __init__(self, playbook_instance):
        self.playbook_instance = playbook_instance

    @classmethod
    def load_from_db(cls, playbook_id):
        model = cls.get_model()
        playbook_instance = (
            model.objects.filter(pk=playbook_id)
            .prefetch_related("target_info_groups__targets")
            .get()
        )
        if not playbook_instance:
            raise Exception(f"Playbook with id {playbook_id} does not exist")
        playbook_handler = cls(playbook_instance)
        return playbook_handler

    @classmethod
    def load_from_db_instance(cls, playbook_instance):
        playbook_handler = cls(playbook_instance)

        return playbook_handler

    @classmethod
    def get_model(cls):
        return apps.get_model("api", "Playbook")

    def get_company_name(self):
        data = self.playbook_instance.company_info.get("Company Name", {}).get(
            "data", []
        )
        company_name = ""
        if len(data) > 0:
            company_name = data[0].get("value", "")
        if company_name == "":
            return "[MyCompany]"
        return company_name

    def copy_from_playbook(self, src_playbook):
        with transaction.atomic():
            # info
            self.playbook_instance.company_info = src_playbook.company_info
            self.playbook_instance.target_info = src_playbook.target_info
            self.playbook_instance.assets = src_playbook.assets
            self.playbook_instance.custom_instructions = copy.copy(
                src_playbook.custom_instructions
            )
            # copy objects
            #   company info

            # If CompanyInfo exists, create a copy of it
            if src_playbook.company_object:
                new_company_info = copy.copy(src_playbook.company_object)
                new_company_info.id = None
                new_company_info.save()
            else:
                new_company_info = None

            self.playbook_instance.company_object = (
                new_company_info  # Set the new CompanyInfo
            )
            self.playbook_instance.save()

            # Targets

            # Copy related TargetInfoGroups and their nested TargetInfo objects
            for target_info_group in src_playbook.target_info_groups.all():
                original_target_info_group = copy.copy(target_info_group)
                original_target_info_group.id = None
                original_target_info_group.playbook = self.playbook_instance
                original_target_info_group.save()

                # Copy TargetInfo objects
                for target_info in target_info_group.targets.all():
                    new_target_info = copy.copy(target_info)
                    new_target_info.id = None
                    new_target_info.target_info_group = original_target_info_group
                    new_target_info.save()

            # Assets
            for asset_info_group in src_playbook.asset_info_groups.all():
                original_asset_info_group = copy.copy(asset_info_group)
                original_asset_info_group.id = None
                original_asset_info_group.playbook = self.playbook_instance
                original_asset_info_group.save()

                # Copy AssetInfo objects
                for asset_info in asset_info_group.assets.all():
                    new_asset_info = copy.copy(asset_info)
                    new_asset_info.id = None
                    new_asset_info.asset_info_group = original_asset_info_group
                    new_asset_info.save()

    def get_metric_tiles(self):
        metric_tiles = {}

        # campaign created
        total_campaigns = EventLogs.objects.filter(
            event_type="campaign_creation", playbook_id=self.playbook_instance.id
        ).count()
        metric_tiles["campaign_created"] = {
            "total": total_campaigns,
        }

        # content created
        total_contents = EventLogs.objects.filter(
            event_type="content_creation", playbook_id=self.playbook_instance.id
        ).count()
        metric_tiles["content_created"] = {"total": total_contents}

        # token usage
        event_logs = EventLogs.objects.filter(
            event_type="token_usage", playbook_id=self.playbook_instance.id
        )

        token_usage = (
            event_logs.annotate(
                model_name=RawSQL("(payload->>%s)", ("model_name",)),
                completion_tokens=Coalesce(
                    RawSQL("((payload->>%s)::integer)", ("completion_tokens",)),
                    Value(0),
                    output_field=IntegerField(),  # Explicitly setting the output field
                ),
            )
            .values("model_name")
            .annotate(total_sum=Sum("completion_tokens"))
            .order_by("model_name")
        )

        metric_tiles["token_usage"] = list(token_usage)

        return metric_tiles

    def web_search(
        self,
        keyword,
        num_links,
        include_sitelinks,
    ):
        # Deprecated 02/03/2025.
        pass
        # return ExternalInfoBuilder().build(keyword, num_links, include_sitelinks)

    def format_insight_as_html(self, text):
        """
        Format insight text as HTML with proper line breaks.

        Args:
            text (str): The markdown text to format

        Returns:
            str: HTML formatted text
        """
        if not text:
            return ""

        # Convert markdown to HTML and ensure proper line breaks
        return markdown2.markdown(text)

    def _get_target_integration_ids(self, target) -> Dict[str, str]:
        """
        Extracts integration IDs from a target's meta data.

        Args:
            target (TargetInfo): Target object

        Returns:
            Dict[str, str]: Mapping from *display name* to integration ID.
        """
        # Initialize integration IDs with empty values
        integration_ids = {}

        if not target.meta:
            return integration_ids

        # Extract Hubspot ID
        if "hubspot_record_id" in target.meta:
            integration_ids["Hubspot ID"] = target.meta.get("hubspot_record_id", "")

        # Extract Salesforce ID
        salesforce_data = target.meta.get("salesforce", {})
        if salesforce_data and "Id" in salesforce_data:
            integration_ids["Salesforce ID"] = salesforce_data.get("Id", "")

        # Extract Marketo ID
        marketo_data = target.meta.get("marketo", {})
        if marketo_data and "lead_id" in marketo_data:
            integration_ids["Marketo ID"] = marketo_data.get("lead_id", "")

        return integration_ids

    def _get_target_doc_fields_data(self, target):
        """
        Extracts dynamic field data (field_name: value) from a target's docs.

        Args:
            target (TargetInfo): Target object

        Returns:
            dict: Dictionary mapping field_name to value.
        """
        target_docs_data = {}
        if target.docs and isinstance(target.docs, dict):
            for field_data in target.docs.values():
                meta = field_data.get("meta", {})
                # we use label if it exists, otherwise we use field_name
                field_name = meta.get("label", meta.get("field_name", ""))
                value = field_data.get("value", "")
                if field_name:
                    target_docs_data[field_name] = value
        return target_docs_data

    def get_target_insights_data(self, target):
        """
        Get insights data (excluding docs fields) for a target.

        Args:
            target (TargetInfo): Target object

        Returns:
            dict: Dictionary with target insights data containing:
                - Basic info (target_name, target_id)
                - Summary and value proposition (both raw and HTML)
                - Tofu research (both raw and HTML)
                - Ordered lists of tofu research results (latest to earliest)
        """

        target_name = target.target_key
        summary = target.summary or ""
        value_prop = target.value_prop or ""

        try:  # in case format has some issues
            tofu_research = (
                target.additional_info.get("tofu_research", {})
                if target.additional_info
                else {}
            )
            tofu_research_items = sorted(
                list(tofu_research.values()),
                key=lambda x: x.get("updated_at", ""),
                reverse=True,
            )
            tofu_research_results = [
                item.get("result", "")
                for item in tofu_research_items
                if item.get("result")
            ]
            # we use the latest tofu research result for export
            latest_tofu_research_str = (
                tofu_research_results[0] if tofu_research_results else ""
            )
        except Exception as e:
            logging.exception(f"Error getting tofu research for {target_name}: {e}")
            latest_tofu_research_str = ""
            tofu_research_results = []

        # Format as HTML
        summary_html = self.format_insight_as_html(summary)
        value_prop_html = self.format_insight_as_html(value_prop)
        tofu_research_html = self.format_insight_as_html(latest_tofu_research_str)

        # Create ordered lists (latest to earliest) for CSV display
        ordered_results = tofu_research_results.copy()
        ordered_html = [
            self.format_insight_as_html(result) for result in ordered_results
        ]

        return {
            "target_name": target_name,
            "target_id": target.id,
            "summary": summary,
            "value_prop": value_prop,
            "tofu_research": latest_tofu_research_str,
            "summary_html": summary_html,
            "value_prop_html": value_prop_html,
            "tofu_research_html": tofu_research_html,
            "ordered_tofu_research": ordered_results,
            "ordered_tofu_research_html": ordered_html,
        }

    def get_insight_csv_url(self, target_group_name):
        """
        Generate a CSV with insights for all targets in a group and return a URL to download it.
        Includes:
        - Dynamic fields from target.docs using field_name
        - Integration IDs (Hubspot, Salesforce, Marketo) if present in target.meta
        - Each tofu research result in a separate column, ordered from latest to earliest
        - HTML versions of all content placed at the end of the CSV
        """

        # Get the target group
        target_info_group = TargetInfoGroup.objects.filter(
            playbook=self.playbook_instance, target_info_group_key=target_group_name
        ).first()

        if not target_info_group:
            logging.error(
                f"Target group '{target_group_name}' not found in playbook '{self.playbook_instance.id}'"
            )
            return None

        targets = TargetInfo.objects.filter(target_info_group=target_info_group)

        targets_data = []
        target_doc_fields = {}
        integration_id_fields = {}
        max_tofu_insights_results = 0

        for target in targets:
            insights_data = self.get_target_insights_data(target)
            docs_data = self._get_target_doc_fields_data(target)
            integration_ids = self._get_target_integration_ids(target)

            combined_data = {
                **insights_data,
                "target_docs_data": docs_data,
                "integration_ids": integration_ids,
            }
            targets_data.append(combined_data)

            max_tofu_insights_results = max(
                max_tofu_insights_results, len(insights_data["ordered_tofu_research"])
            )

            for field_name in docs_data.keys():
                target_doc_fields[field_name] = None

            for field_name in integration_ids.keys():
                integration_id_fields[field_name] = None

        target_doc_field_names = list(target_doc_fields.keys())
        target_integration_id_field_names = list(integration_id_fields.keys())

        # Generate the CSV header using the collected field names
        header = self._generate_insight_csv_header(
            max_tofu_insights_results,
            target_doc_field_names,
            target_integration_id_field_names,
        )

        csv_data = [header]

        # Create rows using the stored combined data
        for data in targets_data:
            row = [
                data["target_name"],
                data["target_id"],
            ]

            # Add integration IDs
            integration_ids = data.get("integration_ids", {})
            for field_name in target_integration_id_field_names:
                row.append(integration_ids.get(field_name, ""))

            # Add dynamic field values using the ordered list of names
            current_docs_data = data.get("target_docs_data", {})
            for field_name in target_doc_field_names:
                row.append(current_docs_data.get(field_name, ""))

            # Add standard fields
            row.extend(
                [
                    data["summary"],
                    data["value_prop"],
                ]
            )

            # Add raw text tofu insights results
            research_list = data["ordered_tofu_research"]
            for i in range(max_tofu_insights_results):
                result = research_list[i] if i < len(research_list) else ""
                row.append(result)

            # Add HTML versions at the end
            row.extend([data["summary_html"], data["value_prop_html"]])

            # Add HTML versions of tofu research results
            research_html_list = data["ordered_tofu_research_html"]
            for i in range(max_tofu_insights_results):
                result_html = (
                    research_html_list[i] if i < len(research_html_list) else ""
                )
                row.append(result_html)

            csv_data.append(row)

        # Hashing, S3 check, upload, and URL generation (unchanged)
        data_hash = hashlib.md5(json.dumps(csv_data).encode("utf-8")).hexdigest()
        s3_bucket = "tofu-uploaded-files"
        file_name = f"{self.playbook_instance.id}-{target_group_name}-{data_hash}-tofu-insights.csv"

        if check_file_exists(s3_bucket, file_name):
            return create_presigned_url(s3_bucket, file_name)

        with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
            with open(temp_file.name, mode="w", newline="") as file:
                writer = csv.writer(file)
                writer.writerows(csv_data)

            upload_file(temp_file.name, "text/csv", s3_bucket, file_name)
            return create_presigned_url(s3_bucket, file_name)

    def _generate_insight_csv_header(
        self, max_tofu_results, target_doc_field_names, integration_id_field_names
    ):
        # Create CSV header dynamically based on the max number of tofu research results
        header = ["Target Name", "Target ID"]

        # Add integration ID fields if provided
        if integration_id_field_names:
            header.extend(integration_id_field_names)

        header.extend(target_doc_field_names)
        header.extend(["Summary", "Value Props"])

        # Add columns for each tofu research result (raw text only)
        for i in range(max_tofu_results):
            # If it's the first research (latest), mark it as latest
            if i == 0:
                header.append(f"Tofu Research {i+1}/{max_tofu_results} - Latest")
            else:
                header.append(f"Tofu Research {i+1}/{max_tofu_results}")

        # Add all HTML versions at the end
        header.extend(["Summary (HTML)", "Value Props (HTML)"])
        for i in range(max_tofu_results):
            # If it's the first research (latest), mark it as latest
            if i == 0:
                header.append(f"Tofu Research {i+1}/{max_tofu_results} - Latest (HTML)")
            else:
                header.append(f"Tofu Research {i+1}/{max_tofu_results} (HTML)")
        return header

    def get_brand_guidelines_text(self):
        l1_key = "[TOFU Internal] Brand Guideline"  # hardcoded
        brand_guideline_l2_key = "Brand Guideline"  # hardcoded

        brand_guideline_asset_group = AssetInfoGroup.objects.filter(
            asset_info_group_key=l1_key, playbook=self.playbook_instance
        ).first()
        if not brand_guideline_asset_group:
            return None
        brand_guideline_asset_info = AssetInfo.objects.filter(
            asset_info_group=brand_guideline_asset_group,
            asset_key=brand_guideline_l2_key,
        ).first()

        if not brand_guideline_asset_info:
            return None

        brand_guideline_raw_text = extract_asset_raw_text(brand_guideline_asset_info)

        # TODO: Handle budgeting for brand guidelines.
        return brand_guideline_raw_text
