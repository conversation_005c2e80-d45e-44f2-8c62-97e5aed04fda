# Google Drive Utilities - Import Hub
# This file maintains backwards compatibility by re-exporting functions from focused modules

# Re-export Google Drive file operations
from .gdrive_file_ops import (
    copy_google_drive_file,
    copy_google_drive_file_with_retry,
    copy_permissions_from_template_to_slides,
    set_google_drive_permissions_flashdocs,
    set_google_drive_permissions_public,
    upload_cloudfront_pptx_file_to_google_drive,
)

# Re-export Google Sheets utilities
from .gsheets_utils import create_and_save_gsheet, create_gsheet, save_to_google_drive

# Re-export Google Slides utilities
from .gslides_utils import (
    check_text_overflow,
    get_gslides_text_contents,
    reset_google_slides_text_box_fonts,
    resize_google_slides_text_boxes,
)

# Re-export retry utilities
from .retry_utils import retry_operation, retry_with_exponential_backoff

# Re-export unit conversion utilities
from .unit_conversions import (
    BATCH_SIZE,
    DEFAULT_DPI,
    DEFAULT_FONT_FAMILY,
    DEFAULT_FONT_SIZE_PT,
    EMU_PER_PT,
    emu_to_pt,
    emu_to_px,
    pt_to_emu,
    pt_to_px,
    px_to_emu,
    px_to_pt,
)

# All exports for backwards compatibility
__all__ = [
    # Retry utilities
    "retry_operation",
    "retry_with_exponential_backoff",
    # Unit conversions
    "BATCH_SIZE",
    "DEFAULT_DPI",
    "DEFAULT_FONT_FAMILY",
    "DEFAULT_FONT_SIZE_PT",
    "EMU_PER_PT",
    "emu_to_pt",
    "emu_to_px",
    "pt_to_emu",
    "pt_to_px",
    "px_to_emu",
    "px_to_pt",
    # Google Drive file operations
    "copy_google_drive_file",
    "copy_google_drive_file_with_retry",
    "copy_permissions_from_template_to_slides",
    "set_google_drive_permissions_flashdocs",
    "set_google_drive_permissions_public",
    "upload_cloudfront_pptx_file_to_google_drive",
    # Google Slides utilities
    "check_text_overflow",
    "get_gslides_text_contents",
    "reset_google_slides_text_box_fonts",
    "resize_google_slides_text_boxes",
    # Google Sheets utilities
    "create_and_save_gsheet",
    "create_gsheet",
    "save_to_google_drive",
]
