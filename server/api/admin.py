import logging
import uuid

import boto3
from django import forms
from django.contrib import admin, messages
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import AdminPasswordChangeForm
from django.core.cache import cache
from django.db.models import JSONField
from django.forms.models import BaseInlineFormSet
from django.shortcuts import redirect, render
from django.urls import path, reverse
from django.utils.html import format_html, mark_safe
from django_svelte_jsoneditor.widgets import SvelteJSONEditorWidget

from .models import (
    Action,
    ActionEdge,
    AssetInfo,
    AssetInfoGroup,
    AutopilotRun,
    Campaign,
    ChatHistory,
    CompanyDomain,
    CompanyInfo,
    Content,
    ContentGroup,
    ContentTemplate,
    ContentVariation,
    EventLogs,
    FeatureAnnouncement,
    OffsiteEventLogs,
    Playbook,
    PlaybookUser,
    PublicContent,
    Status,
    Tag,
    TargetInfo,
    TargetInfoGroup,
    TofuUser,
    UserCreditAdjustment,
)
from .shared_definitions.protobuf.gen.action_define_pb2 import ActionCategory


class LimitedFormset(BaseInlineFormSet):
    def get_queryset(self):
        qs = super().get_queryset().order_by("-created_at")
        return qs[:3]


class TofuTabularInline(admin.TabularInline):
    """Base tabular inline class that applies SvelteJSONEditorWidget to all JSONFields"""

    formfield_overrides = {
        JSONField: {
            "widget": SvelteJSONEditorWidget,
        }
    }


class TofuStackedInline(admin.StackedInline):
    """Base stacked inline class that applies SvelteJSONEditorWidget to all JSONFields"""

    formfield_overrides = {
        JSONField: {
            "widget": SvelteJSONEditorWidget,
        }
    }


class LimitedTabularInline(TofuTabularInline):
    formset = LimitedFormset
    extra = 0


class LimitedStackedInline(TofuStackedInline):
    formset = LimitedFormset
    extra = 0


class StatusInline(TofuTabularInline):
    model = Status
    extra = 0


class PlaybookUserInline(TofuTabularInline):
    model = PlaybookUser
    extra = 1


class TargetInfoGroupInline(LimitedStackedInline):
    model = TargetInfoGroup


class TargetInfoInline(LimitedStackedInline):
    model = TargetInfo


class AssetInfoGroupInline(LimitedStackedInline):
    model = AssetInfoGroup


class AssetInfoInline(LimitedStackedInline):
    model = AssetInfo


class ContentInline(LimitedStackedInline):
    model = Content


class CampaignInline(LimitedStackedInline):
    model = Campaign


class ContentGroupInline(LimitedStackedInline):
    model = ContentGroup


class PublicContentInline(LimitedStackedInline):
    model = PublicContent


class ContentVariationInline(TofuTabularInline):
    model = ContentVariation


class ChangePasswordWidget(forms.TextInput):
    def __init__(self, instance=None, *args, **kwargs):
        self.instance = instance
        super().__init__(*args, **kwargs)

    def render(self, name, value, attrs=None, renderer=None):
        if self.instance and self.instance.pk:
            change_url = reverse(
                "admin:auth_user_password_change", args=[self.instance.pk]
            )
            return format_html(
                '<input type="text" name="{}" value="{}" class="vTextField">'
                '<a href="{}" class="button" style="margin-left: 10px;">Change</a>',
                name,
                value or "",
                change_url,
            )
        return super().render(name, value, attrs, renderer)


class TofuModelAdmin(admin.ModelAdmin):
    """Base admin class for Tofu models that applies SvelteJSONEditorWidget to all JSONFields
    and shows created/updated timestamps"""

    formfield_overrides = {
        JSONField: {
            "widget": SvelteJSONEditorWidget,
        }
    }

    def get_list_display(self, request):
        """Add created_at and updated_at to list_display if they exist on the model"""
        list_display = list(super().get_list_display(request))
        model_fields = [field.name for field in self.model._meta.fields]

        # Add timestamps to end of list if they exist
        if "updated_at" in model_fields and "updated_at" not in list_display:
            list_display.append("updated_at")
        if "created_at" in model_fields and "created_at" not in list_display:
            list_display.append("created_at")

        return list_display

    def get_readonly_fields(self, request, obj=None):
        """Add created_at and updated_at to readonly_fields if they exist"""
        readonly_fields = list(super().get_readonly_fields(request, obj))
        model_fields = [field.name for field in self.model._meta.fields]

        # Add timestamps to readonly fields if they exist
        if "created_at" in model_fields and "created_at" not in readonly_fields:
            readonly_fields.append("created_at")
        if "updated_at" in model_fields and "updated_at" not in readonly_fields:
            readonly_fields.append("updated_at")

        return readonly_fields


class TofuUserForm(forms.ModelForm):
    MODEL_CHOICES = [
        ("gpt-4o-2024-11-20", "gpt-4o-2024-11-20"),
        ("claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20240620"),
        ("claude-3-7-sonnet-20250219", "claude-3-7-sonnet-20250219"),
        ("claude-3-7-sonnet-20250219-thinking", "claude-3-7-sonnet-20250219-thinking"),
        (
            "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
            "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        ),
        (
            "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0",
            "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0",
        ),
        (
            "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
            "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        ),
        (
            "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
            "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        ),
        ("deepseek-chat", "deepseek-chat"),
        ("deepseek-reasoner", "deepseek-reasoner"),
        ("gemini-2.0-flash-exp", "gemini-2.0-flash-exp"),
        ("o1-2024-12-17", "o1-2024-12-17"),
        ("o3-mini-2025-01-31", "o3-mini-2025-01-31"),
        ("gpt-4.1-2025-04-14", "gpt-4.1-2025-04-14"),
        ("o3-2025-04-16", "o3-2025-04-16"),
        ("o4-mini-2025-04-16", "o4-mini-2025-04-16"),
        ("claude-sonnet-4-20250514", "claude-sonnet-4-20250514"),
        ("claude-opus-4-20250514", "claude-opus-4-20250514"),
        (
            "us.anthropic.claude-sonnet-4-20250514-v1:0",
            "us.anthropic.claude-sonnet-4-20250514-v1:0",
        ),
        (
            "us.anthropic.claude-opus-4-20250514-v1:0",
            "us.anthropic.claude-opus-4-20250514-v1:0",
        ),
    ]

    MODEL_CHOICES_FOR_REPURPOSE = [
        (None, "Same as user level model"),
        ("gpt-4o-2024-11-20", "gpt-4o-2024-11-20"),
        ("claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20240620"),
        ("claude-3-7-sonnet-20250219", "claude-3-7-sonnet-20250219"),
        ("claude-3-7-sonnet-20250219-thinking", "claude-3-7-sonnet-20250219-thinking"),
        (
            "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
            "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        ),
        (
            "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0",
            "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0",
        ),
        (
            "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
            "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        ),
        (
            "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
            "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        ),
        ("deepseek-chat", "deepseek-chat"),
        ("deepseek-reasoner", "deepseek-reasoner"),
        ("gemini-2.0-flash-exp", "gemini-2.0-flash-exp"),
        ("o1-2024-12-17", "o1-2024-12-17"),
        ("o3-mini-2025-01-31", "o3-mini-2025-01-31"),
        ("gpt-4.5-preview", "gpt-4.5-preview"),
        ("gpt-4.1-2025-04-14", "gpt-4.1-2025-04-14"),
        ("o3-2025-04-16", "o3-2025-04-16"),
        ("o4-mini-2025-04-16", "o4-mini-2025-04-16"),
        ("claude-sonnet-4-20250514", "claude-sonnet-4-20250514"),
        ("claude-opus-4-20250514", "claude-opus-4-20250514"),
        (
            "us.anthropic.claude-sonnet-4-20250514-v1:0",
            "us.anthropic.claude-sonnet-4-20250514-v1:0",
        ),
        (
            "us.anthropic.claude-opus-4-20250514-v1:0",
            "us.anthropic.claude-opus-4-20250514-v1:0",
        ),
    ]

    SCRAPER_CHOICES = [
        ("simple-scraper", "🏠 Simple Scraper"),
        ("scraping-bee", "🐝 Scraping Bee"),
        ("tofu-scraper", "🚀 Tofu Scraper"),
    ]

    verified_user_context = forms.BooleanField(required=False, label="Verified User")
    model_context = forms.ChoiceField(
        required=False, label="Model", choices=MODEL_CHOICES
    )
    model_context_for_repurpose = forms.ChoiceField(
        required=False, label="Model for repurpose", choices=MODEL_CHOICES_FOR_REPURPOSE
    )
    scraper_context = forms.ChoiceField(
        required=False, label="Scraper", choices=SCRAPER_CHOICES
    )
    internal_features_context = forms.BooleanField(
        required=False, label="Internal Features"
    )
    inboundLandingPageEnabled = forms.BooleanField(
        required=False, label="Inbound Landing Page"
    )
    campaignV3Enabled = forms.BooleanField(required=False, label="Campaign V3")
    salesDeckEnabled = forms.BooleanField(required=False, label="Sales Deck")
    richTextV2Enabled = forms.BooleanField(required=False, label="Rich Text V2")
    mySavedResourcesEnabled = forms.BooleanField(
        required=False, label="My Saved Resources"
    )
    enable_model_mock = forms.BooleanField(required=False, label="Enable Model Mock")
    enable_auto_select_components = forms.BooleanField(
        required=False, label="Enable Auto Select Components"
    )
    enable_model_llm_cache = forms.BooleanField(
        required=False, label="Enable Model LLM Cache"
    )
    context = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)

    class Meta:
        model = TofuUser
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.context:
            self.fields["verified_user_context"].initial = self.instance.context.get(
                "verified_user", True
            )
            self.fields["model_context"].initial = self.instance.context.get(
                "model", None
            )
            self.fields["model_context_for_repurpose"].initial = (
                self.instance.context.get("model_for_repurpose", None)
            )
            self.fields["scraper_context"].initial = self.instance.context.get(
                "webOverrides", {}
            ).get("scraper", None)
            self.fields["internal_features_context"].initial = (
                self.instance.context.get("internalFeatures", None)
            )
            self.fields["inboundLandingPageEnabled"].initial = (
                self.instance.context.get("inboundLandingPageEnabled", None)
            )
            self.fields["campaignV3Enabled"].initial = self.instance.context.get(
                "campaignV3Enabled", None
            )
            self.fields["salesDeckEnabled"].initial = self.instance.context.get(
                "salesDeckEnabled", None
            )
            self.fields["richTextV2Enabled"].initial = self.instance.context.get(
                "richTextV2Enabled", None
            )
            self.fields["mySavedResourcesEnabled"].initial = self.instance.context.get(
                "mySavedResourcesEnabled", None
            )
            self.fields["enable_model_mock"].initial = self.instance.context.get(
                "enable_model_mock", None
            )
            self.fields["enable_auto_select_components"].initial = (
                self.instance.context.get("enable_auto_select_components", None)
            )
            self.fields["enable_model_llm_cache"].initial = self.instance.context.get(
                "enable_model_llm_cache", None
            )
            self.fields["password"].widget = ChangePasswordWidget(
                instance=self.instance
            )


class UserPlaybookInline(admin.TabularInline):
    model = PlaybookUser
    extra = 1  # You can adjust this to control how many empty forms are shown.


class NoValidationPasswordChangeForm(AdminPasswordChangeForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["password1"].help_text = ""
        self.fields["password2"].help_text = (
            "Enter the same password as before, for verification."
        )

    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("The two password fields didn't match.")
        return password2


class TofuUserAdmin(UserAdmin):
    form = TofuUserForm
    change_password_form = NoValidationPasswordChangeForm
    inlines = [UserPlaybookInline]

    list_display = (
        "id",
        "username",
        "full_name",
        "email",
        "get_playbooks",
        "get_verified_user",
        "get_model",
        "get_model_for_repurpose",
        "get_scraper",
        "get_internal_features",
        "get_inbound_landing_page_enabled",
        "get_campaign_v3_enabled",
        "get_enable_model_mock",
        "get_enable_auto_select_components",
        "get_enable_model_llm_cache",
        "get_rich_text_v2_enabled",
        "is_superuser",
        "is_staff",
        "is_active",
        "date_joined",
        "customer_type",
        "credits_available",
        "credits_last_updated",
    )
    list_filter = ("is_superuser", "is_staff", "is_active")
    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (
            "Personal info",
            {"fields": ("full_name", "first_name", "last_name", "email")},
        ),
        (
            "Context",
            {
                "fields": (
                    "verified_user_context",
                    "model_context",
                    "model_context_for_repurpose",
                    "scraper_context",
                    "internal_features_context",
                    "inboundLandingPageEnabled",
                    "campaignV3Enabled",
                    "salesDeckEnabled",
                    "richTextV2Enabled",
                    "mySavedResourcesEnabled",
                    "enable_model_mock",
                    "enable_auto_select_components",
                    "enable_model_llm_cache",
                    "context",
                )
            },
        ),
        ("Permissions", {"fields": ("is_staff", "is_active")}),
        ("Important dates", {"fields": ("last_login", "date_joined")}),
        (
            "Tofu Lite",
            {
                "fields": (
                    "customer_type",
                    "credits_available",
                    "credits_last_updated",
                    "stripe_checkout_session_id",
                    "stripe_customer_id",
                    "stripe_payment_id",
                    "tofu_lite_subscription_tier",
                )
            },
        ),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "username",
                    "email",
                    "password1",
                    "password2",
                    "is_staff",
                    "is_active",
                ),
            },
        ),
    )
    search_fields = ("id", "username", "email", "full_name")
    ordering = ("-id",)

    def get_playbooks(self, obj):
        links = []
        for playbook in obj.playbook_set.all():
            url = reverse("admin:api_playbook_change", args=[playbook.id])
            links.append(
                format_html(
                    '<a href="{}">{} ({})</a>',
                    url,
                    playbook.id,
                    playbook.company_domain,
                )
            )
        return format_html(", ".join(links))

    get_playbooks.short_description = "Playbooks"

    def get_verified_user(self, obj):
        if not obj.context:
            return True
        return obj.context.get("verified_user", True)

    get_verified_user.short_description = "Verified User"
    get_verified_user.boolean = True

    def get_model(self, obj):
        if not obj.context:
            return ""
        return obj.context.get("model", None)

    get_model.short_description = "Model"

    def get_model_for_repurpose(self, obj):
        if not obj.context:
            return ""
        return obj.context.get("model_for_repurpose", None)

    get_model_for_repurpose.short_description = "Model for repurpose"

    def get_scraper(self, obj):
        if not obj.context:
            return ""
        return obj.context.get("webOverrides", {}).get("scraper", None)

    get_scraper.short_description = "Scraper"

    def get_internal_features(self, obj):
        if not obj.context:
            return False
        return obj.context.get("internalFeatures", False)

    get_internal_features.short_description = "Internal Features"
    get_internal_features.boolean = True

    def get_inbound_landing_page_enabled(self, obj):
        if not obj.context:
            return False
        return obj.context.get("inboundLandingPageEnabled", False)

    get_inbound_landing_page_enabled.short_description = "Inbound Landing Page Enabled"
    get_inbound_landing_page_enabled.boolean = True

    def get_campaign_v3_enabled(self, obj):
        if not obj.context:
            return False
        return obj.context.get("campaignV3Enabled", False)

    def get_rich_text_v2_enabled(self, obj):
        if not obj.context:
            return False
        return obj.context.get("richTextV2Enabled", False)

    get_rich_text_v2_enabled.short_description = "Rich Text V2 Enabled"
    get_rich_text_v2_enabled.boolean = True

    get_campaign_v3_enabled.short_description = "Campaign V3 Enabled"
    get_campaign_v3_enabled.boolean = True

    def get_sales_deck_enabled(self, obj):
        if not obj.context:
            return False
        return obj.context.get("salesDeckEnabled", False)

    get_sales_deck_enabled.short_description = "Sales Deck Enabled"
    get_sales_deck_enabled.boolean = True

    def get_enable_model_mock(self, obj):
        if not obj.context:
            return False
        return obj.context.get("enable_model_mock", False)

    get_enable_model_mock.short_description = "Enable Model Mock"
    get_enable_model_mock.boolean = True

    def get_enable_auto_select_components(self, obj):
        if not obj.context:
            return False
        return obj.context.get("enable_auto_select_components", False)

    get_enable_auto_select_components.short_description = (
        "Enable Auto Select Components"
    )
    get_enable_auto_select_components.boolean = True

    def get_enable_model_llm_cache(self, obj):
        if not obj.context:
            return False
        return obj.context.get("enable_model_llm_cache", False)

    get_enable_model_llm_cache.short_description = "Enable Model LLM Cache"
    get_enable_model_llm_cache.boolean = True

    def save_model(self, request, obj, form, change):
        verified_user_context = form.cleaned_data.get("verified_user_context")
        model_context = form.cleaned_data.get("model_context")
        model_context_for_repurpose = form.cleaned_data.get(
            "model_context_for_repurpose"
        )
        scraper_context = form.cleaned_data.get("scraper_context")
        internal_features_context = form.cleaned_data.get("internal_features_context")
        inboundLandingPageEnabled = form.cleaned_data.get("inboundLandingPageEnabled")
        campaignV3Enabled = form.cleaned_data.get("campaignV3Enabled")
        salesDeckEnabled = form.cleaned_data.get("salesDeckEnabled")
        richTextV2Enabled = form.cleaned_data.get("richTextV2Enabled")
        mySavedResourcesEnabled = form.cleaned_data.get("mySavedResourcesEnabled")
        enable_model_mock = form.cleaned_data.get("enable_model_mock")
        enable_auto_select_components = form.cleaned_data.get(
            "enable_auto_select_components"
        )
        enable_model_llm_cache = form.cleaned_data.get("enable_model_llm_cache")

        if obj.context is None:
            obj.context = {}
        if verified_user_context is not None:
            obj.context["verified_user"] = verified_user_context
        if model_context is not None:
            obj.context["model"] = model_context
        if model_context_for_repurpose is not None:
            obj.context["model_for_repurpose"] = model_context_for_repurpose
        if scraper_context is not None:
            if obj.context.get("webOverrides") is None:
                obj.context["webOverrides"] = {}
            obj.context["webOverrides"]["scraper"] = scraper_context
        if internal_features_context is not None:
            obj.context["internalFeatures"] = internal_features_context
        if inboundLandingPageEnabled is not None:
            obj.context["inboundLandingPageEnabled"] = inboundLandingPageEnabled
        if campaignV3Enabled is not None:
            obj.context["campaignV3Enabled"] = campaignV3Enabled
        if salesDeckEnabled is not None:
            obj.context["salesDeckEnabled"] = salesDeckEnabled
        if richTextV2Enabled is not None:
            obj.context["richTextV2Enabled"] = richTextV2Enabled
        if mySavedResourcesEnabled is not None:
            obj.context["mySavedResourcesEnabled"] = mySavedResourcesEnabled
        if enable_model_mock is not None:
            obj.context["enable_model_mock"] = enable_model_mock
        if enable_auto_select_components is not None:
            obj.context["enable_auto_select_components"] = enable_auto_select_components
        if enable_model_llm_cache is not None:
            obj.context["enable_model_llm_cache"] = enable_model_llm_cache

        super().save_model(request, obj, form, change)

    formfield_overrides = {
        JSONField: {
            "widget": SvelteJSONEditorWidget,
        }
    }


class TargetInfoGroupForm(forms.ModelForm):
    autopilot_duration = forms.IntegerField(
        required=False,
        label="Autopilot Duration (minutes)",
        initial=20,
        help_text="Duration in minutes for autopilot mode",
        min_value=1,
        max_value=1440,  # 24 hours
    )
    meta = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)
    status = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)

    class Meta:
        model = TargetInfoGroup
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.meta:
            try:
                duration = int(self.instance.meta.get("autopilot_duration", 20))
                self.fields["autopilot_duration"].initial = max(1, min(duration, 1440))
            except (ValueError, TypeError):
                self.fields["autopilot_duration"].initial = 20


def validate_autopilot_duration(duration):
    if not isinstance(duration, int):
        logging.error(f"Invalid autopilot duration: {duration}")
        return 20
    if duration < 1 or duration > 1440:
        logging.error(f"Invalid autopilot duration: {duration}")
        return min(max(duration, 1), 1440)
    return duration


class TargetInfoGroupAdmin(TofuModelAdmin):
    form = TargetInfoGroupForm
    list_display = (
        "id",
        "target_info_group_key",
        "playbook",
        "updated_at",
        "created_at",
    )
    inlines = [TargetInfoInline]
    list_filter = ("updated_at", "created_at")
    search_fields = ("target_info_group_key", "id")

    def get_autopilot_duration(self, obj):
        if not obj.meta:
            return 20
        return validate_autopilot_duration(obj.meta.get("autopilot_duration", 20))

    get_autopilot_duration.short_description = "Autopilot Duration"

    def save_model(self, request, obj, form, change):
        autopilot_duration = form.cleaned_data.get("autopilot_duration")

        if obj.meta is None:
            obj.meta = {}
        obj.meta["autopilot_duration"] = validate_autopilot_duration(autopilot_duration)

        super().save_model(request, obj, form, change)


class TargetInfoAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "target_key",
        "get_target_info_group_key",
        "get_playbook",
        "updated_at",
        "created_at",
    )
    search_fields = ("target_key", "id")
    list_filter = ("updated_at", "created_at")

    # Keep field order without fieldsets
    fields = (
        "target_info_group",
        "target_key",
        "value_prop",
        "docs",
        "meta",
        "summary",
        "index",
        "additional_info",
        "docs_last_build",
        "docs_build_status",
        "created_at",
        "updated_at",
    )

    def get_target_info_group_key(self, obj):
        return obj.target_info_group.target_info_group_key

    get_target_info_group_key.short_description = "Target Group Key"

    def get_playbook(self, obj):
        return obj.target_info_group.playbook

    get_playbook.short_description = "Playbook"


class AssetInfoGroupAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "asset_info_group_key",
        "playbook",
        "updated_at",
        "created_at",
    )
    search_fields = (
        "id",
        "asset_info_group_key",
    )
    inlines = [AssetInfoInline]
    list_filter = ("updated_at", "created_at")
    list_select_related = ("playbook",)


class AssetInfoAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "asset_key",
        "get_asset_info_group_key",
        "get_playbook",
        "updated_at",
        "created_at",
    )
    search_fields = (
        "id",
        "asset_key",
    )
    list_filter = ("updated_at", "created_at")

    # Keep field order without fieldsets
    fields = (
        "asset_info_group",
        "asset_key",
        "docs",
        "meta",
        "summary",
        "index",
        "additional_info",
        "docs_last_build",
        "docs_build_status",
        "created_at",
        "updated_at",
    )

    def get_asset_info_group_key(self, obj):
        return obj.asset_info_group.asset_info_group_key

    get_asset_info_group_key.short_description = "Asset Group Key"

    def get_playbook(self, obj):
        return obj.asset_info_group.playbook

    get_playbook.short_description = "Playbook"


class PlaybookForm(forms.ModelForm):
    enable_data_structure_v2 = forms.BooleanField(
        required=False, label="Enable data structure v2"
    )
    disable_playbook_post_processing = forms.BooleanField(
        required=False, label="Disable Playbook Post Processing"
    )
    enable_fake_metrics = forms.BooleanField(
        required=False, label="Enable Fake Metrics"
    )
    enable_feature_auto_sync = forms.BooleanField(
        required=False, label="Enable Feature Auto Sync"
    )
    enable_crustdata_for_linkedin_profile = forms.BooleanField(
        required=False, label="Enable Crustdata for LinkedIn Profile", initial=True
    )
    settings = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)

    class Meta:
        model = Playbook
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.settings:
            self.fields["enable_data_structure_v2"].initial = (
                self.instance.settings.get("enableDataStructureV2", False)
            )
            self.fields["disable_playbook_post_processing"].initial = (
                self.instance.settings.get("disablePlaybookPostProcessing", False)
            )
            self.fields["enable_fake_metrics"].initial = self.instance.settings.get(
                "enableFakeMetrics", False
            )
            self.fields["enable_feature_auto_sync"].initial = (
                self.instance.settings.get("enableFeatureAutoSync", False)
            )
            self.fields["enable_crustdata_for_linkedin_profile"].initial = (
                self.instance.settings.get("enableCrustdataForLinkedInProfile", True)
            )


class PlaybookAdmin(TofuModelAdmin):
    form = PlaybookForm

    list_display = ("id", "name", "get_user", "updated_at", "created_at")
    inlines = [
        StatusInline,
        PlaybookUserInline,
        TargetInfoGroupInline,
        AssetInfoGroupInline,
        CampaignInline,
    ]
    search_fields = (
        "id",
        "company_info__Company Name__text",
    )
    ordering = ("-id",)

    def get_user(self, obj):
        user = obj.users.first()
        return user.username if user else None

    get_user.admin_order_field = "users__username"
    get_user.short_description = "User"

    def get_enable_data_structure_v2(self, obj):
        if not obj.settings:
            return False
        return obj.settings.get("enableDataStructureV2", False)

    get_enable_data_structure_v2.short_description = "Enable Target Object"
    get_enable_data_structure_v2.boolean = True

    def get_disable_playbook_post_processing(self, obj):
        if not obj.settings:
            return False
        return obj.settings.get("disablePlaybookPostProcessing", False)

    get_disable_playbook_post_processing.short_description = (
        "Disable Playbook Post Processing"
    )
    get_disable_playbook_post_processing.boolean = True

    def get_enable_fake_metrics(self, obj):
        if not obj.settings:
            return False
        return obj.settings.get("enableFakeMetrics", False)

    get_enable_fake_metrics.short_description = "Enable Fake Metrics"
    get_enable_fake_metrics.boolean = True

    def get_enable_feature_auto_sync(self, obj):
        if not obj.settings:
            return False
        return obj.settings.get("enableFeatureAutoSync", False)

    get_enable_feature_auto_sync.short_description = "Enable Feature Auto Sync"
    get_enable_feature_auto_sync.boolean = True

    def get_enable_crustdata_for_linkedin_profile(self, obj):
        if not obj.settings:
            return True
        return obj.settings.get("enableCrustdataForLinkedInProfile", True)

    get_enable_crustdata_for_linkedin_profile.short_description = (
        "Enable Crustdata for LinkedIn Profile"
    )
    get_enable_crustdata_for_linkedin_profile.boolean = True

    def save_model(self, request, obj, form, change):
        enable_data_structure_v2 = form.cleaned_data.get("enable_data_structure_v2")
        disable_playbook_post_processing = form.cleaned_data.get(
            "disable_playbook_post_processing"
        )
        enable_fake_metrics = form.cleaned_data.get("enable_fake_metrics")
        enable_feature_auto_sync = form.cleaned_data.get("enable_feature_auto_sync")
        enable_crustdata_for_linkedin_profile = form.cleaned_data.get(
            "enable_crustdata_for_linkedin_profile"
        )

        if obj.settings is None:
            obj.settings = {}
        if enable_data_structure_v2 is not None:
            obj.settings["enableDataStructureV2"] = enable_data_structure_v2
        if disable_playbook_post_processing is not None:
            obj.settings["disablePlaybookPostProcessing"] = (
                disable_playbook_post_processing
            )
        if enable_fake_metrics is not None:
            obj.settings["enableFakeMetrics"] = enable_fake_metrics
        if enable_feature_auto_sync is not None:
            obj.settings["enableFeatureAutoSync"] = enable_feature_auto_sync
        if enable_crustdata_for_linkedin_profile is not None:
            obj.settings["enableCrustdataForLinkedInProfile"] = (
                enable_crustdata_for_linkedin_profile
            )

        super().save_model(request, obj, form, change)


class CampaignTemplateForm(forms.Form):
    category = forms.CharField(
        required=True,
        help_text="Enter the campaign category (e.g., ABM, General)",
        widget=forms.TextInput(attrs={"placeholder": "e.g., ABM"}),
    )

    is_active = forms.BooleanField(
        required=False, initial=True, help_text="Whether this template is active"
    )

    deliverables = forms.CharField(
        widget=forms.Textarea(
            attrs={
                "rows": 3,
                "placeholder": "Email sequence\nLanding page\nAds-General",
            }
        ),
        help_text="Enter deliverables, one per line",
    )

    image_entry_point = forms.URLField(
        required=False,
        label="Image entry point",
        help_text="Enter URL or upload a new image for campaign entry point",
        widget=forms.URLInput(
            attrs={
                "placeholder": "https://tofu-public-files.s3.us-east-2.amazonaws.com/...",
                "style": "width: 90%; margin-bottom: 10px;",
            }
        ),
    )

    image_entry_point_file = forms.ImageField(
        required=False,
        label="",
        widget=forms.ClearableFileInput(attrs={"style": "margin-top: 5px;"}),
    )

    image_template_modal = forms.URLField(
        required=False,
        label="Image template modal",
        help_text="Enter URL or upload a new image for template preview",
        widget=forms.URLInput(
            attrs={
                "placeholder": "https://tofu-public-files.s3.us-east-2.amazonaws.com/...",
                "style": "width: 90%; margin-bottom: 10px;",
            }
        ),
    )

    image_template_modal_file = forms.ImageField(
        required=False,
        label="",
        widget=forms.ClearableFileInput(attrs={"style": "margin-top: 5px;"}),
    )

    anchor_description = forms.CharField(
        widget=forms.Textarea(
            attrs={
                "rows": 4,
                "placeholder": "Content you want to repurpose as anchor content...",
            }
        ),
        help_text="Description of the anchor content requirements",
    )

    template_description = forms.CharField(
        widget=forms.Textarea(
            attrs={
                "rows": 3,
                "placeholder": "Build trust and guide leads through the sales funnel...",
            }
        ),
        help_text="Overall description of the template",
    )

    def clean_image_entry_point(self):
        url = self.cleaned_data.get("image_entry_point")
        if url and not url.startswith(
            "https://tofu-public-files.s3.us-east-2.amazonaws.com/"
        ):
            raise forms.ValidationError(
                "URL must start with 'https://tofu-public-files.s3.us-east-2.amazonaws.com/'"
            )
        return url

    def clean_image_template_modal(self):
        url = self.cleaned_data.get("image_template_modal")
        if url and not url.startswith(
            "https://tofu-public-files.s3.us-east-2.amazonaws.com/"
        ):
            raise forms.ValidationError(
                "URL must start with 'https://tofu-public-files.s3.us-east-2.amazonaws.com/'"
            )
        return url

    def save_image_to_s3(self, image_file, prefix):
        if not image_file:
            return None
        try:
            s3 = boto3.client("s3")
            bucket = "tofu-public-files"
            file_extension = image_file.name.split(".")[-1]
            file_name = (
                f"campaign_template_image/{prefix}_{uuid.uuid4()}.{file_extension}"
            )

            s3.upload_fileobj(
                image_file,
                bucket,
                file_name,
                ExtraArgs={"ContentType": image_file.content_type},
            )
        except boto3.exceptions.Boto3Error as e:
            raise forms.ValidationError(f"S3 upload failed: {e}")

        # Updated URL format to match the required pattern
        return f"https://{bucket}.s3.us-east-2.amazonaws.com/{file_name}"


class CampaignForm(forms.ModelForm):
    suggested_campaign = forms.BooleanField(required=False, widget=forms.CheckboxInput)
    suggested_campaign_desc = forms.CharField(required=False, widget=forms.Textarea)
    campaign_params = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)
    campaign_status = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)

    class Meta:
        model = Campaign
        fields = [
            "creator",
            "playbook",
            "campaign_name",
            "campaign_params",
            "campaign_status",
            # config fields
            "suggested_campaign",
            "suggested_campaign_desc",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        initial_data = (
            self.instance.campaign_params
            if self.instance and self.instance.campaign_params
            else {}
        )
        suggested_campaign = initial_data.get("suggested_campaign", False)
        self.fields["suggested_campaign"].initial = suggested_campaign

        suggested_campaign_desc = initial_data.get("suggested_campaign_desc", "")
        self.fields["suggested_campaign_desc"].initial = suggested_campaign_desc

    def clean(self):
        cleaned_data = super().clean()

        suggested_campaign = cleaned_data.get("suggested_campaign")
        suggested_campaign_desc = cleaned_data.get("suggested_campaign_desc")
        if "campaign_params" in self.cleaned_data:
            self.cleaned_data["campaign_params"][
                "suggested_campaign"
            ] = suggested_campaign
            self.cleaned_data["campaign_params"][
                "suggested_campaign_desc"
            ] = suggested_campaign_desc

        return self.cleaned_data


class CampaignAdmin(TofuModelAdmin):
    change_form_template = "admin/api/campaign/change_form.html"

    list_display = (
        "id",
        "creator",
        "campaign_name",
        "playbook",
        "tags_list",
        "updated_at",
        "created_at",
    )
    inlines = [ContentGroupInline]
    search_fields = [
        "id",
        "campaign_name",
        "creator__username",
        "playbook__id",
        "campaignTags__name",
    ]
    list_filter = ("updated_at", "created_at")
    form = CampaignForm

    def tags_list(self, obj):
        return ", ".join([tag.name for tag in obj.campaignTags.all()])

    tags_list.short_description = "Tags"

    def save_model(self, request, obj, form, change):
        obj.campaign_params["suggested_campaign"] = form.cleaned_data[
            "suggested_campaign"
        ]
        obj.campaign_params["suggested_campaign_desc"] = form.cleaned_data[
            "suggested_campaign_desc"
        ]
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("campaignTags")

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "<path:object_id>/edit-template/",
                self.admin_site.admin_view(self.edit_campaign_template),
                name="api_campaign_edit_template",
            ),
        ]
        return custom_urls + urls

    def edit_campaign_template(self, request, object_id):
        campaign = Campaign.objects.get(id=object_id)

        if request.method == "POST":
            form = CampaignTemplateForm(request.POST, request.FILES)
            if form.is_valid():
                # Handle image uploads
                image_entry_point = form.cleaned_data["image_entry_point"]
                image_template_modal = form.cleaned_data["image_template_modal"]

                # Upload new images if provided
                if form.cleaned_data["image_entry_point_file"]:
                    image_entry_point = form.save_image_to_s3(
                        form.cleaned_data["image_entry_point_file"], "entry_point"
                    )

                if form.cleaned_data["image_template_modal_file"]:
                    image_template_modal = form.save_image_to_s3(
                        form.cleaned_data["image_template_modal_file"], "template_modal"
                    )

                # Update campaign_template in campaign_params
                params = campaign.campaign_params or {}
                template_data = {
                    "category": form.cleaned_data["category"],
                    "is_active": form.cleaned_data["is_active"],
                    "deliverables": form.cleaned_data["deliverables"],
                    "image_entry_point": image_entry_point,
                    "anchor_description": form.cleaned_data["anchor_description"],
                    "image_template_modal": image_template_modal,
                    "template_description": form.cleaned_data["template_description"],
                }
                params["campaign_template"] = template_data
                campaign.campaign_params = params
                campaign.save()
                # invalidate cache
                try:
                    cache.delete(f"campaign_templates_v3")
                except Exception as e:
                    logging.exception(
                        f"debug: Failed to delete campaign_templates_v3: {e}"
                    )
                messages.success(request, "Campaign template updated successfully.")
                return redirect("admin:api_campaign_change", object_id)
        else:
            # Pre-populate form with existing data
            initial_data = {}
            if (
                campaign.campaign_params
                and "campaign_template" in campaign.campaign_params
            ):
                template = campaign.campaign_params["campaign_template"]
                initial_data = {
                    "category": template.get("category", "ABM"),
                    "is_active": template.get("is_active", True),
                    "deliverables": template.get("deliverables", ""),
                    "image_entry_point": template.get("image_entry_point", ""),
                    "anchor_description": template.get("anchor_description", ""),
                    "image_template_modal": template.get("image_template_modal", ""),
                    "template_description": template.get("template_description", ""),
                }
            form = CampaignTemplateForm(initial=initial_data)

        context = {
            "form": form,
            "campaign": campaign,
            "opts": self.model._meta,
            "title": f"Edit Campaign Template for Campaign {campaign.id}",
            "has_file_field": True,
        }
        return render(
            request, "admin/api/campaign/campaign_template_form.html", context
        )


class ActionEdgeInline(TofuTabularInline):
    model = ActionEdge
    fk_name = "to_action"
    extra = 1
    fields = ("from_action", "config")


class ActionForm(forms.ModelForm):
    # Generate choices from proto enum, mapping values to themselves
    ACTION_CATEGORY_CHOICES = [
        (name, name)
        for name, number in ActionCategory.items()
        if name != "ACTION_CATEGORY_UNSPECIFIED"  # Exclude unspecified
    ]

    action_category = forms.ChoiceField(
        choices=ACTION_CATEGORY_CHOICES,
        required=True,
    )
    inputs = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)
    outputs = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)
    status = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)
    meta = forms.JSONField(widget=SvelteJSONEditorWidget(), required=False)

    class Meta:
        model = Action
        fields = "__all__"


class ActionAdmin(TofuModelAdmin):
    form = ActionForm
    list_display = (
        "id",
        "action_name",
        "action_category",
        "creator",
        "playbook",
        "campaign",
        "updated_at",
        "created_at",
    )
    list_filter = ("action_category", "updated_at", "created_at")
    search_fields = (
        "id",
        "action_name",
        "creator__username",
        "playbook__id",
        "campaign__id",
    )
    inlines = [ActionEdgeInline]
    readonly_fields = ("created_at", "updated_at", "get_content_groups")

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "creator",
                    "playbook",
                    "campaign",
                    "action_name",
                    "action_category",
                    "get_content_groups",
                )
            },
        ),
        ("Action Configuration", {"fields": ("inputs", "outputs", "status", "meta")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def get_content_groups(self, obj):
        content_groups = ContentGroup.objects.filter(action=obj)
        if not content_groups:
            return "-"
        links = []
        for cg in content_groups:
            url = reverse("admin:api_contentgroup_change", args=[cg.id])
            links.append(
                format_html(
                    '<a href="{}">{} (ID: {})</a>',
                    url,
                    cg.content_group_name or f"Content Group #{cg.id}",
                    cg.id,
                )
            )
        return format_html(", ".join(links))

    get_content_groups.short_description = "Content Groups"


class ActionEdgeAdmin(TofuModelAdmin):
    list_display = ("id", "from_action", "to_action", "updated_at", "created_at")
    list_filter = ("updated_at", "created_at")
    search_fields = ("id", "from_action__action_name", "to_action__action_name")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (None, {"fields": ("from_action", "to_action", "config")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if not obj:  # Only set defaults for new objects
            form.base_fields["config"].initial = {}
        return form


class ContentGroupAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "creator",
        "content_group_name",
        "campaign",
        "updated_at",
        "created_at",
    )
    inlines = [ContentInline]
    search_fields = [
        "id",
        "content_group_name",
        "creator__username",
        "campaign__campaign_name",
        "campaign__id",
    ]
    list_filter = ("updated_at", "created_at")

    def get_fieldsets(self, request, obj=None):
        fieldsets = (
            (
                None,
                {
                    "fields": (
                        "creator",
                        "campaign",
                        "action",
                        "content_group_name",
                        "content_group_params",
                        "content_group_status",
                        "components",
                    )
                },
            ),
        )
        return fieldsets


class ContentAdmin(TofuModelAdmin):
    inlines = [ContentVariationInline]
    list_display = (
        "id",
        "creator",
        "playbook",
        "content_group",
        "content_name",
        "updated_at",
        "created_at",
    )
    search_fields = (
        "id",
        "content_name",
        "creator__username",
        "playbook__id",
        "content_group__id",
    )
    list_filter = ("updated_at", "created_at")

    def campaign_link(self, obj):
        if obj.content_group_id and obj.content_group.campaign_id:
            url = reverse(
                "admin:api_campaign_change", args=[obj.content_group.campaign_id]
            )
            return mark_safe(
                f'<a href="{url}">Campaign {obj.content_group.campaign_id}</a>'
            )
        return "N/A"

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)
        return ["campaign_link"] + list(readonly_fields)

    def change_view(self, request, object_id, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context["campaign_link"] = self.campaign_link(
            Content.objects.get(id=object_id)
        )
        return super().change_view(
            request,
            object_id,
            form_url,
            extra_context=extra_context,
        )


class ContentVariationAdmin(TofuModelAdmin):
    inlines = [PublicContentInline]
    list_display = ("id", "content", "updated_at", "created_at")
    search_fields = ("id",)
    list_filter = ("updated_at", "created_at")

    def campaign_link(self, obj):
        if obj.content.content_group_id and obj.content.content_group.campaign_id:
            url = reverse(
                "admin:api_campaign_change",
                args=[obj.content.content_group.campaign_id],
            )
            return mark_safe(
                f'<a href="{url}">Campaign {obj.content.content_group.campaign_id}</a>'
            )
        return "N/A"

    def content_group_link(self, obj):
        if obj.content.content_group_id:
            url = reverse(
                "admin:api_contentgroup_change", args=[obj.content.content_group_id]
            )
            return mark_safe(
                f'<a href="{url}">Content Group {obj.content.content_group_id}</a>'
            )
        return "N/A"

    def content_link(self, obj):
        if obj.content_id:
            url = reverse("admin:api_content_change", args=[obj.content_id])
            return mark_safe(f'<a href="{url}">Content {obj.content_id}</a>')
        return "N/A"

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)
        return ["campaign_link", "content_group_link", "content_link"] + list(
            readonly_fields
        )

    def change_view(self, request, object_id, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context["campaign_link"] = self.campaign_link(
            ContentVariation.objects.get(id=object_id)
        )
        extra_context["content_group_link"] = self.content_group_link(
            ContentVariation.objects.get(id=object_id)
        )
        extra_context["content_link"] = self.content_link(
            ContentVariation.objects.get(id=object_id)
        )
        return super().change_view(
            request,
            object_id,
            form_url,
            extra_context=extra_context,
        )


class PublicContentAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "source_content_variation",
        "tofu_content_id",
        "tofu_slug",
        "updated_at",
        "created_at",
    )
    search_fields = ("id", "tofu_content_id", "tofu_slug")
    list_filter = ("updated_at", "created_at")


class CompanyDomainAdmin(TofuModelAdmin):
    list_display = ("id", "link_playbook_domain", "allow_register_domain")
    search_fields = ("link_playbook_domain", "allow_register_domain")


class EventLogsAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "event_type",
        "user_id",
        "playbook_id",
        "campaign_id",
        "created_at",
    )
    list_filter = ("event_type", "created_at")
    search_fields = ("event_type", "user_id", "playbook_id", "campaign_id")
    readonly_fields = ("created_at",)

    def has_add_permission(self, request, obj=None):
        return True

    def has_change_permission(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return True


class OffsiteEventLogsAdmin(TofuModelAdmin):
    list_display = (
        "session_id",
        "event_type",
        "playbook_id",
        "campaign_id",
        "content_id",
        "content_variation_index",
        "tofu_slug",
        "ip_address",
        "user_agent",
        "created_at",
    )
    list_filter = ("event_type", "created_at")
    search_fields = (
        "event_type",
        "playbook_id",
        "campaign_id",
        "content_id",
        "tofu_slug",
        "ip_address",
    )
    readonly_fields = ("created_at",)

    def has_add_permission(self, request, obj=None):
        return True

    def has_change_permission(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return True


class ChatHistoryAdmin(TofuModelAdmin):
    list_display = (
        "key",
        "creator",
        "model",
        "updated_at",
        "created_at",
    )
    search_fields = ("key",)


class ContentTemplateAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "creator",
        "playbook",
        "name",
        "updated_at",
        "created_at",
    )
    search_fields = ("name",)


class FeatureAnnouncementForm(forms.ModelForm):
    type = forms.ChoiceField(
        choices=FeatureAnnouncement.AnnouncementType.choices,
        widget=forms.Select,
        required=True,
    )

    class Meta:
        model = FeatureAnnouncement
        fields = "__all__"
        widgets = {
            "description": forms.Textarea(attrs={"rows": 4}),
        }


class FeatureAnnouncementAdmin(TofuModelAdmin):
    form = FeatureAnnouncementForm
    list_display = (
        "id",
        "headline",
        "type",
        "is_shown",
        "position",
        "tag",
        "updated_at",
        "created_at",
    )
    list_filter = ("type", "is_shown")
    search_fields = ("id", "headline", "description", "tag")
    ordering = ("-id",)
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "type",
                    "is_shown",
                    "position",
                    "tag",
                    "headline",
                    "description",
                )
            },
        ),
        ("Links", {"fields": ("link", "image_url", "video_url")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ("created_at",)
        return self.readonly_fields


class TagAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "name",
        "creator",
        "campaign_count",
        "updated_at",
        "created_at",
    )
    list_filter = ("creator__username",)
    search_fields = ("id", "name", "description", "creator__username")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (None, {"fields": ("name", "creator", "description", "color")}),
        (
            "Linked Campaigns",
            {
                "fields": ("campaigns",),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("campaigns")

    def campaign_count(self, obj):
        return obj.campaigns.count()

    campaign_count.short_description = "Number of Campaigns"

    def formfield_for_manytomany(self, db_field, request, **kwargs):
        if db_field.name == "campaigns":
            kwargs["widget"] = admin.widgets.FilteredSelectMultiple(
                "Campaigns", is_stacked=False
            )
        return super().formfield_for_manytomany(db_field, request, **kwargs)


class UserCreditAdjustmentAdmin(TofuModelAdmin):
    list_display = ("id", "user", "credit_adjustment", "created_at")
    list_filter = ("created_at",)
    search_fields = ("user__username",)
    readonly_fields = ("created_at",)


class AutopilotRunAdmin(TofuModelAdmin):
    list_display = (
        "id",
        "playbook",
        "target_info_group",
        "campaign",
        "session_id",
        "autopilot_action_type",
        "created_at",
    )
    list_filter = ("created_at",)
    search_fields = ("playbook__name", "campaign__id", "session_id")
    readonly_fields = ("created_at",)


class CompanyInfoAdmin(TofuModelAdmin):
    list_display = ("id", "playbook", "updated_at", "created_at")
    list_filter = ("updated_at", "created_at")
    readonly_fields = ("get_playbook",)  # Make get_playbook readonly

    # Keep field order without fieldsets - removed playbook field since it's not a real field
    fields = (
        "get_playbook",  # Include the custom method that displays the playbook
        "docs",
        "meta",
        "summary",
        "index",
        "additional_info",
        "docs_last_build",
        "docs_build_status",
        "created_at",
        "updated_at",
    )

    def get_playbook(self, obj):
        # Try to get the related Playbook instance through the reverse relation
        try:
            playbook = (
                obj.playbook
            )  # This works because of the OneToOneField reverse relation
            url = reverse("admin:api_playbook_change", args=(playbook.id,))
            return format_html(
                '<a href="{}">Playbook #{} (Click to view)</a>', url, playbook.id
            )
        except Playbook.DoesNotExist:
            return "None"

    get_playbook.short_description = "Playbook"


admin.site.register(TofuUser, TofuUserAdmin)
admin.site.register(CompanyInfo, CompanyInfoAdmin)
admin.site.register(TargetInfoGroup, TargetInfoGroupAdmin)
admin.site.register(TargetInfo, TargetInfoAdmin)
admin.site.register(AssetInfoGroup, AssetInfoGroupAdmin)
admin.site.register(AssetInfo, AssetInfoAdmin)
admin.site.register(Playbook, PlaybookAdmin)
admin.site.register(Campaign, CampaignAdmin)
admin.site.register(Action, ActionAdmin)
admin.site.register(ActionEdge, ActionEdgeAdmin)
admin.site.register(ContentGroup, ContentGroupAdmin)
admin.site.register(Content, ContentAdmin)
admin.site.register(ContentVariation, ContentVariationAdmin)
admin.site.register(CompanyDomain, CompanyDomainAdmin)
admin.site.register(PublicContent, PublicContentAdmin)
admin.site.register(EventLogs, EventLogsAdmin)
admin.site.register(OffsiteEventLogs, OffsiteEventLogsAdmin)
admin.site.register(ChatHistory, ChatHistoryAdmin)
admin.site.register(ContentTemplate, ContentTemplateAdmin)
admin.site.register(FeatureAnnouncement, FeatureAnnouncementAdmin)
admin.site.register(Tag, TagAdmin)
admin.site.register(UserCreditAdjustment, UserCreditAdjustmentAdmin)
admin.site.register(AutopilotRun, AutopilotRunAdmin)
