from abc import ABC, abstractmethod

from langsmith import traceable


class BaseTracableClass:
    def get_metadata(self):
        return {}

    def get_tags(self):
        return []


def dynamic_traceable(*dargs, **dkwargs):
    """
    A decorator factory that returns a decorator to dynamically apply the `traceable` decorator
    with parameters that are determined at runtime, along with additional static parameters.
    """

    def decorator(method):
        def wrapper(self, *args, **kwargs):
            # Dynamically get tags and metadata
            dynamic_tags = self.get_tags()  # Assuming you have a method `get_tags`
            dynamic_metadata = self.get_metadata()  # Your existing method

            # Combine dynamic parameters with the static ones provided to the decorator
            final_tags = dynamic_tags + dkwargs.get("tags", [])
            final_metadata = {**dynamic_metadata, **dkwargs.get("metadata", {})}
            name = dkwargs.get("name", method.__name__)

            # Apply the traceable decorator dynamically
            traceable_method = traceable(
                tags=final_tags, metadata=final_metadata, name=name
            )(method)

            # Call the now traceable method
            return traceable_method(self, *args, **kwargs)

        return wrapper

    # If dynamic_traceable is used without parentheses, e.g., @dynamic_traceable directly above a function
    # dargs[0] will be the function itself, so we return decorator(dargs[0])
    if dargs and callable(dargs[0]):
        return decorator(dargs[0])

    return decorator
