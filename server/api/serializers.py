import copy
import json
import logging
import traceback
from datetime import datetime

from celery.result import AsyncResult
from django.db import transaction
from django.db.models import Q
from django.forms.models import model_to_dict
from django.utils import timezone
from google.protobuf.json_format import MessageToDict, ParseDict
from pydantic import ValidationError as PydanticValidationError
from rest_framework import serializers
from server.celery import app as celery_app

from .actions.action_creator import ActionCreator
from .actions.action_data_wrapper import (
    ActionDataWrapper,
    convert_campaign_targets_v2_to_v3,
)
from .actions.action_handler import ActionHandler
from .actions.tofu_data_wrapper import TofuDataListHandler
from .content_group import ContentGroupHandler
from .logger import log_campaign_creation, log_content_creation
from .models import (
    Action,
    ActionEdge,
    AssetInfo,
    AssetInfoGroup,
    Campaign,
    ChatHistory,
    CompanyInfo,
    Content,
    ContentGroup,
    ContentTemplate,
    ContentVariation,
    FeatureAnnouncement,
    Playbook,
    PlaybookUser,
    Status,
    Tag,
    TargetInfo,
    TargetInfoGroup,
    TofuUser,
)
from .playbook_build.data_sync import (
    deserialize_asset_info,
    deserialize_target_info,
    playbook_data_compare,
    playbook_sync,
    target_info_groups_to_representation_for_comparison,
)
from .shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuDataList,
    TofuTemplate,
)
from .validator.campaign_validator import CampaignValidation
from .validator.content_group_validator import ContentGroupValidation
from .validator.content_validator import ContentValidation
from .validator.playbook_validator import PlaybookValidation


class TofuUserSerializer(serializers.ModelSerializer):
    playbook = serializers.SerializerMethodField()

    class Meta:
        model = TofuUser
        fields = [
            "id",
            "username",
            "full_name",
            "first_name",
            "last_name",
            "email",
            "context",
            "last_login",
            "date_joined",
            "playbook",
            "customer_type",
            "credits_available",
            "credits_last_updated",
            "tofu_lite_subscription_tier",
        ]
        read_only_fields = ["id", "last_login", "date_joined"]

    def create(self, validated_data):
        return TofuUser.objects.create_user(**validated_data)

    def update(self, instance, validated_data):
        # Merge the context field changes rather than overwriting it
        if "context" in validated_data:
            # Get existing context or empty dict if None
            existing_context = instance.context or {}
            # Get new context from validated data
            new_context = validated_data.pop("context", {})
            # Merge the contexts
            existing_context.update(new_context)
            # Put merged context back into validated_data
            validated_data["context"] = existing_context

        # Update the instance with all other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance

    def get_playbook(self, obj):
        playbook_user = PlaybookUser.objects.filter(
            Q(user=obj, type="user") | Q(user=obj, type="creator")
        ).first()
        if playbook_user:
            return {
                "playbook_id": playbook_user.playbook.id,
                "company_domain": playbook_user.playbook.company_domain,
            }
        else:
            return {"playbook_id": None, "company_domain": None}


class CompanyInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyInfo
        fields = "__all__"


class TargetInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = TargetInfo
        fields = [
            "id",
            "docs",
            "meta",
            "summary",
            "index",
            "docs_last_build",
            "docs_build_status",
            "target_key",
            "value_prop",
            "additional_info",
        ]


class TargetInfoGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = TargetInfoGroup
        fields = "__all__"

    def to_representation(self, instance):
        is_legacy = self.context.get("legacy", False)
        if is_legacy:
            ret = target_info_groups_to_representation_for_comparison(instance)
        else:
            ret = super(TargetInfoGroupSerializer, self).to_representation(instance)
            target_info_group_id = self.context.get("target_info_group_id", None)
            if target_info_group_id:
                ret["target_infos"] = {}
                for k in instance.targets.all():
                    target_data = TargetInfoSerializer(k).data
                    target_data.pop("summary", None)
                    target_data.pop("index", None)
                    target_data.pop("docs_last_build", None)
                    target_data.pop("value_prop", None)
                    if "meta" in target_data:
                        target_data["meta"].pop("brief", None)
                    ret["target_infos"][k.id] = target_data
        return ret

    def to_representation_for_comparison(self, instance):
        res_dict = {
            "meta": instance.meta,
            "status": instance.status,
            "target_info_group_key": instance.target_info_group_key,
            "targets": {},
        }
        for target in instance.targets.all():
            data_list = sorted(target.docs.values(), key=lambda x: x["position"])
            data_list = [
                {k: v for k, v in item.items() if k != "position"} for item in data_list
            ]
            res_dict["targets"][target.target_key] = {
                # "meta": target.meta,
                "data": data_list,
            }
            # TODO: this is for comparison. We shall always set meta even it's empty dict
            if target.meta and (len(target.meta) > 1 or "brief" not in target.meta):
                res_dict["targets"][target.target_key]["meta"] = target.meta
        return res_dict


class AssetInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssetInfo
        fields = "__all__"


class AssetInfoGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssetInfoGroup
        fields = "__all__"

    def to_representation(self, instance):
        ret = super(AssetInfoGroupSerializer, self).to_representation(instance)
        ret["asset_infos"] = {
            k.id: AssetInfoSerializer(k).data for k in instance.assets.all()
        }
        return ret

    def to_representation_for_comparison(self, instance):
        res_dict = {
            "meta": instance.meta,
            "asset_info_group_key": instance.asset_info_group_key,
            "assets": {},
        }
        for asset in instance.assets.all():
            data_list = sorted(asset.docs.values(), key=lambda x: x["position"])
            data_list = [
                {k: v for k, v in item.items() if k != "position"} for item in data_list
            ]
            res_dict["assets"][asset.asset_key] = {
                # "meta": asset.meta,
                "data": data_list,
            }
            # TODO: this is for comparison. We shall always set meta even it's empty dict
            if asset.meta and (len(asset.meta) > 1 or "brief" not in asset.meta):
                res_dict["assets"][asset.asset_key]["meta"] = asset.meta
        return res_dict


class PlaybookSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()
    company_object = serializers.SerializerMethodField()
    target_info_groups = serializers.SerializerMethodField()
    asset_info_groups = serializers.SerializerMethodField()

    class Meta:
        model = Playbook
        fields = [
            "id",
            "users",
            "name",
            "company_info",
            "target_info",
            "assets",
            "company_object",
            "target_info_groups",
            "asset_info_groups",
            "updated_at",
            "created_at",
            "status",
            "custom_instructions",
            "settings",
        ]
        read_only_fields = [
            "id",
            "updated_at",
            "created_at",
        ]

    def get_status(self, obj):
        status_objs = obj.status_set.all()
        status_dict = {}
        for status_obj in status_objs:
            status_dict[status_obj.type] = status_obj.status
        return status_dict

    def get_company_object(self, obj):
        if not obj.company_object:
            return {}
        return CompanyInfoSerializer(obj.company_object).data

    def get_target_info_groups(self, obj):
        target_info_groups = obj.target_info_groups.all()
        target_info_groups_serialized = TargetInfoGroupSerializer(
            target_info_groups, many=True, context=self.context
        ).data
        return sorted(
            target_info_groups_serialized, key=lambda x: x["created_at"], reverse=True
        )

    def get_asset_info_groups(self, obj):
        asset_info_groups = obj.asset_info_groups.all()
        return AssetInfoGroupSerializer(
            asset_info_groups, many=True, context=self.context
        ).data

    def to_representation(self, instance):
        """
        Remove the expanded fields from the representation to make the response smaller
        """
        ret = super().to_representation(instance)
        is_lite = self.context.get("lite", False)

        if ret.get("company_info", {}).get("meta", {}).get("changed"):
            ret["company_info"]["meta"].pop("changed", {})
        if "meta" in ret.get("company_info", {}):
            ret["company_info"]["meta"].pop("brief", None)
        if ret.get("assets", {}).get("meta", {}).get("changed"):
            ret["assets"]["meta"].pop("changed", {})

        ret["assets"] = deserialize_asset_info(instance, is_lite=is_lite)
        instance.assets = ret["assets"]
        ret["target_info"] = deserialize_target_info(instance, is_lite=is_lite)
        instance.target_info = ret["target_info"]

        return ret

    def create(self, validated_data):
        # Validate using Pydantic
        try:
            PlaybookValidation(**validated_data)
        except PydanticValidationError as e:
            logging.warning(
                f"Validation error: playbook creation with error: {str(e.errors())}"
            )

        if "company_object" not in validated_data:
            validated_data["company_object"] = CompanyInfo.objects.create()

        if validated_data.get("target_info"):
            target_data = validated_data["target_info"]
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Get the list of changed fields
        # changed_fields = {k: v for k, v in validated_data.items() if v != getattr(instance, k)}
        changed_fields = validated_data  # we are doing this since we lock the post-processing for the whole playbook

        # For each field that can contain a JSON object, compute the deltas and
        # update the "meta" field in the JSON object with the deltas.
        for field in ("company_info", "target_info", "assets"):
            if field in changed_fields:
                new_value = changed_fields[field]
                # Update the "meta" field with the deltas.
                if "meta" not in new_value:
                    new_value["meta"] = {}
                changed_fields[field] = new_value

        # Update the instance with the validated data
        update_fields = list(changed_fields.keys())
        for attr, value in changed_fields.items():
            setattr(instance, attr, value)

        with transaction.atomic():
            try:
                # update playbook objects with update data
                playbook_sync(instance, validated_data)
            except Exception as e:
                logging.error(
                    f"debug: playbook {instance.id} failed to sync new object data structure: {e}\n{traceback.format_exc()}"
                )

            # Save the instance with the update_fields argument
            instance.save(update_fields=update_fields)

        # Validate using Pydantic
        try:
            PlaybookValidation(**model_to_dict(instance))
        except PydanticValidationError as e:
            logging.warning(
                f"Validation error: campaign {instance.id} update with error: {str(e.errors())}"
            )

        return instance

    def check_data_changes(self, old_data_list, new_data_list, deltas, path):
        old_data = {item["id"]: item for item in old_data_list}
        new_data = {item["id"]: item for item in new_data_list}

        for id_key, old_item in old_data.items():
            if id_key not in new_data:
                deltas["data_deleted"].append(path + [id_key])
            elif "value" in old_item and old_item["value"] != new_data[id_key]["value"]:
                deltas["data_modified"].append(path + [id_key])

        for id_key, new_item in new_data.items():
            if id_key not in old_data:
                deltas["data_added"].append(path + [id_key])

    def merge_deltas(self, d1, d2):
        for key, value in d2.items():
            d1[key].extend(value)
        return d1


class CampaignSerializer(serializers.ModelSerializer):
    content_groups = serializers.SerializerMethodField()
    tags = serializers.SerializerMethodField()

    class Meta:
        model = Campaign
        fields = (
            "id",
            "creator",
            "playbook",
            "campaign_name",
            "campaign_params",
            "campaign_status",
            "content_groups",
            "tags",
            "updated_at",
            "created_at",
        )
        read_only_fields = ["id", "updated_at", "created_at"]

    def get_content_groups(self, obj):
        # Check if the Campaign object has the _prefetched_objects_cache attribute
        # Check if the related objects are already prefetched
        if (
            hasattr(obj, "_prefetched_objects_cache")
            and "contentgroup_set" in obj._prefetched_objects_cache
        ):
            content_groups = obj._prefetched_objects_cache["contentgroup_set"]
        else:
            # If _prefetched_objects_cache is not present, manually fetch the related objects
            content_groups = ContentGroup.objects.filter(campaign=obj)

        return ContentGroupSerializer(
            content_groups, many=True, context=self.context
        ).data

    def get_tags(self, obj):
        return TagSerializer(obj.campaignTags.all(), many=True).data

    def create(self, validated_data):
        # Validate using Pydantic
        try:
            CampaignValidation(**validated_data)
        except PydanticValidationError as e:
            logging.error(
                f"Validation error: campaign creation with error: {str(e.errors())}"
            )

        # If validation passes, create the new instance
        campaign_instance = super().create(validated_data)
        log_campaign_creation(campaign=campaign_instance)
        return campaign_instance

    def update(self, instance, validated_data):
        update_fields = {}

        need_update_targets = False
        # Support incremental updates to campaign_params
        if "campaign_params" in validated_data:
            # Get the existing campaign_params
            campaign_params = instance.campaign_params or {}

            # Get the new campaign_params from the request
            new_campaign_params = validated_data.get("campaign_params", {})

            if "targets" in new_campaign_params and new_campaign_params[
                "targets"
            ] != campaign_params.get("targets", {}):

                new_campaign_targets = new_campaign_params.get("targets", {})
                if not new_campaign_targets:
                    logging.error(
                        f"Campaign targets is empty in update for campaign {instance.id}"
                    )
                    campaign_targets_tofu_data = TofuDataList()
                else:
                    campaign_targets_tofu_data = convert_campaign_targets_v2_to_v3(
                        new_campaign_targets, instance.playbook_id
                    )
                if not isinstance(campaign_targets_tofu_data, TofuDataList):
                    logging.error(
                        f"Invalid campaign targets type: {type(campaign_targets_tofu_data)}"
                    )
                else:
                    # save data to campaign_params
                    instance.campaign_params["tofu_target_data"] = MessageToDict(
                        campaign_targets_tofu_data
                    )
                    instance.save(update_fields=["campaign_params"])
                need_update_targets = True

            # Merge the new campaign_params into the existing campaign_params
            campaign_params.update(new_campaign_params)

            # Add the updated campaign_params to the update_fields dictionary
            update_fields["campaign_params"] = campaign_params

        # Add other fields that are provided in the request data to the update_fields dictionary
        for attr, value in validated_data.items():
            if attr != "campaign_params":
                update_fields[attr] = value

        # Update only the fields in the update_fields dictionary
        Campaign.objects.filter(pk=instance.pk).update(**update_fields)

        # Refresh the instance to reflect the changes in the database
        instance.refresh_from_db()

        # Validate using Pydantic
        try:
            CampaignValidation(**model_to_dict(instance))
        except PydanticValidationError as e:
            logging.warning(
                f"Validation error: campaign {instance.id} update with error: {str(e.errors())}"
            )

        if need_update_targets:
            actions = Action.objects.filter(campaign=instance)
            for action in actions:
                action_handler = ActionHandler(action)
                action_handler.update_targets()

        return instance

    def to_representation(self, instance):
        # Check and update campaign status before serialization
        self.check_clone_status(instance)
        return super().to_representation(instance)

    def check_clone_status(self, campaign):
        if not campaign.campaign_status:
            return
        clone_status = campaign.campaign_status.get("clone_status", {})
        if not clone_status:
            return

        # Check if the job is running for more than 30 minutes
        if "update_time" in clone_status and clone_status.get("status") in (
            "IN_PROGRESS",
            "SUBMITTED",
        ):
            update_time = datetime.fromisoformat(clone_status["update_time"])
            minute_diff = (timezone.now() - update_time).seconds // 60
            if minute_diff > 30:
                logging.error(
                    f"campaign {campaign.id} is taking too long ({minute_diff} minutes)"
                )
                task_id = clone_status.get("task_id")
                if task_id:
                    # revoke for task_id
                    celery_app.control.revoke(task_id)
                campaign.campaign_status["clone_status"].update(
                    {
                        "status": "ERROR",
                        "update_time": timezone.now().isoformat(),
                        "message": f"Job is taking too long ({minute_diff} minutes)",
                    }
                )
                campaign.save(update_fields=["campaign_status"])
            else:
                # Check if the task has been revoked or failed
                task_id = clone_status.get("task_id")
                if task_id:
                    result = AsyncResult(task_id)
                    if result.failed() or result.status == "REVOKED":
                        logging.error(
                            f"campaign {campaign.id} failed or was revoked for task {task_id}"
                        )
                        campaign.campaign_status["clone_status"].update(
                            {
                                "status": "ERROR",
                                "update_time": timezone.now().isoformat(),
                                "message": f"Job {task_id} failed or was revoked",
                            }
                        )
                        campaign.save(update_fields=["campaign_status"])


class ActionEdgeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActionEdge
        fields = (
            "id",
            "from_action",
            "to_action",
            "config",
            "created_at",
            "updated_at",
        )
        read_only_fields = ["id", "created_at", "updated_at"]


class ActionSerializer(serializers.ModelSerializer):
    incoming_edges = serializers.ListField(
        child=serializers.DictField(), write_only=True, required=False, default=list
    )
    # Add this new field for reading incoming actions
    incoming_actions_data = serializers.SerializerMethodField()

    class Meta:
        model = Action
        fields = (
            "id",
            "creator",
            "playbook",
            "campaign",
            "action_name",
            "action_category",
            "inputs",
            "outputs",
            "status",
            "meta",
            "incoming_edges",
            "incoming_actions_data",  # Add the new field here
            "created_at",
            "updated_at",
        )
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_incoming_actions_data(self, obj):
        """
        Get information about incoming actions and their edges
        """
        # Get all incoming edges for this action
        incoming_edges = ActionEdge.objects.filter(to_action=obj).select_related(
            "from_action"
        )

        return [
            {
                "edge": {
                    "id": edge.id,
                    "from_action": edge.from_action.id,
                    "config": edge.config,
                    "created_at": edge.created_at,
                    "updated_at": edge.updated_at,
                }
            }
            for edge in incoming_edges
        ]

    @staticmethod
    def get_campaign_actions(campaign):
        if not campaign:
            return []
        # Get all actions for this campaign, excluding the current action
        campaign_actions = Action.objects.filter(campaign=campaign).order_by("id")
        # serialize
        return ActionSerializer(campaign_actions, many=True).data

    def create(self, validated_data):
        try:
            new_instance = ActionCreator(
                self.context["request"].user, validated_data
            ).create()
            # Return just the new instance instead of all campaign actions
            return new_instance
        except serializers.ValidationError as e:
            logging.exception(f"debug: Failed to create action: {e}")
            raise e
        except Exception as e:
            logging.exception(f"Failed to create action: {e}")
            raise serializers.ValidationError(str(e))

    def update(self, instance, validated_data):
        raise NotImplementedError(
            "Action update from Serializer is not supposed to be used"
        )

    def to_representation(self, instance):
        try:
            # Get base representation
            ret = super().to_representation(instance)

            # Get and validate inputs
            instance_inputs = ActionDataWrapper(instance).get_all_user_inputs()
            if not isinstance(instance_inputs, dict):
                logging.warning(
                    f"Invalid inputs type for action {instance.id}: {type(instance_inputs)}"
                )
                ret["inputs"] = {}
                return ret

            # Convert inputs safely
            converted_inputs = {}
            for key, value in instance_inputs.items():
                try:
                    converted_inputs[key] = (
                        TofuDataListHandler.convert_tofu_data_to_json(value)
                    )
                except Exception as e:
                    logging.error(
                        f"Failed to convert input {key} for action {instance.id}: {str(e)}"
                    )
                    converted_inputs[key] = None
                    continue

            ret["inputs"] = converted_inputs
            return ret

        except Exception as e:
            logging.exception(
                f"Error in action serializer to_representation for action {getattr(instance, 'id', 'unknown')}: {str(e)}"
            )
            # Re-raise as DRF validation error to ensure proper API response
            raise serializers.ValidationError("Failed to serialize action data")


class ContentGroupSerializer(serializers.ModelSerializer):
    contents = serializers.SerializerMethodField()

    class Meta:
        model = ContentGroup
        fields = (
            "id",
            "creator",
            "campaign",
            "action",
            "content_group_name",
            "content_group_params",
            "content_group_status",
            "components",
            "contents",
            "updated_at",
            "created_at",
        )
        read_only_fields = ["id", "updated_at", "created_at"]

    def get_contents(self, obj):
        content_group_id = self.context.get("content_group_id", None)
        campaign_id = self.context.get("campaign_id", None)
        if not content_group_id and not campaign_id:
            return []
        contents = Content.objects.filter(content_group=obj).order_by("-created_at")
        return ContentSerializer(contents, many=True, context=self.context).data

    def create(self, validated_data):
        # Validate using Pydantic
        try:
            ContentGroupValidation(**validated_data)
        except PydanticValidationError as e:
            logging.error(
                f"Validation error: content_group with error: {str(e.errors())}"
            )

        # If validation passes, create and return the new instance
        return super().create(validated_data)

    def update(self, instance, validated_data):
        update_fields = {}

        # Support incremental updates to content_group_params
        if "content_group_params" in validated_data:
            # Get the existing content_group_params
            content_group_params = instance.content_group_params or {}

            # Get the new content_group_params from the request
            new_content_group_params = validated_data.get("content_group_params", {})

            # Merge the new content_group_params into the existing content_group_params
            content_group_params.update(new_content_group_params)

            # Add the updated content_group_params to the update_fields dictionary
            update_fields["content_group_params"] = content_group_params

        # Add other fields that are provided in the request data to the update_fields dictionary
        for attr, value in validated_data.items():
            if attr != "content_group_params":
                update_fields[attr] = value

        # Update only the fields in the update_fields dictionary
        ContentGroup.objects.filter(pk=instance.pk).update(**update_fields)

        # Refresh the instance to reflect the changes in the database
        instance.refresh_from_db()

        # Check if content_group_name has changed and call bulk_rename
        # TODO: this is not triggered by django admin
        if (
            "content_group_name" in validated_data
            and validated_data["content_group_name"] != instance.content_group_name
        ):
            ContentGroupHandler(instance).bulk_rename()

        # Validate using Pydantic
        try:
            ContentGroupValidation(**model_to_dict(instance))
        except PydanticValidationError as e:
            logging.warning(
                f"Validation error: content_group {instance.id} with error: {str(e.errors())}"
            )

        return instance

    def to_representation(self, instance):
        ContentGroupHandler(instance).update_status()
        ret = super().to_representation(instance)
        return ret


class ContentSerializer(serializers.ModelSerializer):
    results = serializers.SerializerMethodField()
    campaign = serializers.SerializerMethodField()

    class Meta:
        model = Content
        fields = (
            "id",
            "creator",
            "playbook",
            "campaign",
            "content_group",
            "content_name",
            "content_params",
            "components",
            "updated_at",
            "created_at",
            "results",
        )
        read_only_fields = ["id", "updated_at", "created_at"]

    def get_campaign(self, obj):
        content_group_id = self.context.get("content_group_id", None)
        campaign_id = self.context.get("campaign_id", None)
        # Don't even query the foreign keys (which is slow) if the request is coming from campaign or content_group
        if content_group_id or campaign_id:
            return None
        content_group = obj.content_group
        if content_group:
            if not content_group.campaign:
                logging.error(
                    f"Content group {content_group.id} does not have a campaign"
                )
                return None
            return content_group.campaign.id
        return None

    def get_results(self, obj):
        content_id = self.context.get("content_id", None)
        if not content_id:
            return []
        results = ContentVariation.objects.filter(content=obj)
        return ContentVariationSerializer(results, many=True).data

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        content_group_id = self.context.get("content_group_id", None)
        campaign_id = self.context.get("campaign_id", None)
        # Remove the unnecessary fields from the representation if the request is coming from campaign or content_group
        if content_group_id or campaign_id:
            ret.pop("creator", None)
            ret.pop("playbook", None)
            ret.pop("campaign", None)
            ret.pop("content_group", None)
            ret.pop("components", None)
            ret.pop("results", None)
            ret.pop("updated_at", None)
            ret.pop("created_at", None)
            updated_content_params = {}
            if ret.get("content_params", {}).get("targets"):
                updated_content_params["targets"] = ret.get("content_params", {}).get(
                    "targets"
                )
            ret["content_params"] = updated_content_params

        return ret

    def create(self, validated_data):
        # Validate using Pydantic
        try:
            ContentValidation(**validated_data)
        except PydanticValidationError as e:
            logging.error(
                f"Validation error: content creation with error: {str(e.errors())}"
            )

        # If validation passes, create the new instance
        content_instance = super().create(validated_data)
        log_content_creation(
            content=content_instance,
        )
        return content_instance

    def update(self, instance, validated_data):
        # This is a bit unconventional than what we normally do for updates.
        # The reason we are doing this is because Content instances are receiving a lot of concurrent updates (tied to the control panel on UI).
        # So it's easy for race conditions to happen, which will cause some updates to be lost.
        # To solve this, we are doing two things:
        # 1. we are updating each column independently, so they don't interfere with each other
        # 2. for content_params json blob, we are doing a merge instead of a replace to support incremental updates

        update_fields = {}

        # Support incremental updates to content_params
        if "content_params" in validated_data:
            # Get the existing content_params
            content_params = instance.content_params or {}

            # Get the new content_params from the request
            new_content_params = validated_data.get("content_params", {})

            # Merge the new content_params into the existing content_params
            content_params.update(new_content_params)

            # Add the updated content_params to the update_fields dictionary
            update_fields["content_params"] = content_params

        # Add other fields that are provided in the request data to the update_fields dictionary
        for attr, value in validated_data.items():
            if attr != "content_params":
                update_fields[attr] = value

        # Update only the fields in the update_fields dictionary
        Content.objects.filter(pk=instance.pk).update(**update_fields)

        # Refresh the instance to reflect the changes in the database
        instance.refresh_from_db()

        # Validate using Pydantic
        try:
            ContentValidation(**model_to_dict(instance))
        except PydanticValidationError as e:
            logging.warning(
                f"Validation error: content {instance.id} with error: {str(e.errors())}"
            )

        return instance


class ContentVariationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentVariation
        fields = "__all__"
        read_only_fields = ["id", "updated_at", "created_at"]


class EmptySerializer(serializers.Serializer):
    pass


class ChatHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatHistory
        fields = (
            "creator",
            "key",
            "model",
            "json",
            "updated_at",
            "created_at",
        )
        read_only_fields = ["key", "creator", "updated_at", "created_at"]

    def to_representation(self, instance):

        ret = super().to_representation(instance)
        messages = instance.json.get("previous_messages", [])
        if messages:
            # filter out message with "keep_hidden" flag
            messages = [m for m in messages if not m.get("keep_hidden", False)]
            ret["json"]["previous_messages"] = messages
        return ret


class LiteChatHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatHistory
        fields = (
            "creator",
            "key",
            "model",
            "json",
            "updated_at",
            "created_at",
        )
        read_only_fields = ["key", "creator", "updated_at", "created_at"]

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        messages = instance.json.get("previous_messages", [])
        if messages:
            # filter out message with "keep_hidden" flag
            messages = [m for m in messages if not m.get("keep_hidden", False)]
            # only keep first message
            ret["json"]["previous_messages"] = messages[:1]
        return ret


class ContentTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentTemplate
        fields = "__all__"
        read_only_fields = ["id", "updated_at", "created_at"]

    def validate(self, attrs):
        template_data = attrs.get("template_data", {})
        is_template_v2 = template_data.get("is_template_v2", False)
        is_template_v3 = not is_template_v2
        if is_template_v3:  # v3 templates
            try:
                template_data = ParseDict(template_data, TofuTemplate())
            except Exception as e:
                # raise serializers.ValidationError({"invalid template_data": str(e)})
                # bypass the validation error for now
                logging.exception(
                    f"invalid template_data: {str(e)} for template_data: {template_data}"
                )
                return attrs

        return attrs


class FeatureAnnouncementSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeatureAnnouncement
        fields = "__all__"
        read_only_fields = ["id", "updated_at", "created_at"]


class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = "__all__"
        read_only_fields = ["id", "campaigns", "creator", "updated_at", "created_at"]
