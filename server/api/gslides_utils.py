import logging

import matplotlib.font_manager as fm
from django.core.cache import cache
from google.oauth2 import service_account
from googleapiclient.discovery import build
from PIL import Image, ImageDraw, ImageFont

from .unit_conversions import (
    BATCH_SIZE,
    DEFAULT_DPI,
    DEFAULT_FONT_FAMILY,
    DEFAULT_FONT_SIZE_PT,
    emu_to_pt,
    emu_to_px,
    pt_to_px,
    px_to_pt,
)


def resize_google_slides_text_boxes(
    url: str,
    default_font_size_pt: float = DEFAULT_FONT_SIZE_PT,
    dpi: int = DEFAULT_DPI,
) -> bool:
    """
    Resize every text box in a Slides deck so its height just fits its text.

    Args:
      url: Google Slides URL
      default_font_size_pt: fallback if no fontSize is found
      dpi: screen DPI (for PT→PX conversion)

    Returns:
      True on success, False on error.
    """
    try:
        if "/d/" not in url:
            logging.error("Invalid Slides URL: %s", url)
            return False
        pres_id = url.split("/d/")[1].split("/")[0]

        # init Slides API
        creds = service_account.Credentials.from_service_account_file(
            ".credentials/google_service_account_key.json",
            scopes=["https://www.googleapis.com/auth/presentations"],
        )
        svc = build("slides", "v1", credentials=creds)
        pres = svc.presentations().get(presentationId=pres_id).execute()

        dummy = Image.new("RGB", (1, 1))
        draw = ImageDraw.Draw(dummy)

        requests = []

        for slide in pres.get("slides", []):
            for el in slide.get("pageElements", []):
                shape = el.get("shape")
                if not shape or "text" not in shape:
                    continue

                oid = el["objectId"]
                xf = el.get("transform", {})
                orig_tx_emu = xf.get("translateX", 0)
                orig_ty_emu = xf.get("translateY", 0)
                orig_sx = float(xf.get("scaleX", 1.0))
                orig_sy = float(xf.get("scaleY", 1.0))
                shearX = xf.get("shearX", 0)
                shearY = xf.get("shearY", 0)

                # original size in EMU → PT
                size = el["size"]
                orig_w_emu = size["width"]["magnitude"]
                orig_h_emu = size["height"]["magnitude"]
                orig_w_pt = emu_to_pt(orig_w_emu)
                orig_h_pt = emu_to_pt(orig_h_emu)

                # display width in PT & PX
                disp_w_pt = orig_w_pt * orig_sx
                disp_w_px = pt_to_px(disp_w_pt, dpi)

                # reconstruct text with real line-breaks
                elems = shape["text"]["textElements"]
                raw = ""
                for te in elems:
                    if "textRun" in te and te["textRun"].get("content"):
                        raw += te["textRun"]["content"]
                    elif te.get("paragraphMarker"):
                        raw += "\n"
                raw = raw.rstrip("\n")

                # pull the first-specified fontFamily & fontSize
                font_family = None
                font_size_pt = None
                for te in elems:
                    if "textRun" not in te:
                        continue
                    style = te["textRun"].get("style", {})
                    if not font_family and style.get("fontFamily"):
                        font_family = style["fontFamily"]
                    if not font_size_pt and style.get("fontSize"):
                        font_size_pt = style["fontSize"]["magnitude"]
                    if font_family and font_size_pt:
                        break
                font_family = font_family or DEFAULT_FONT_FAMILY
                font_size_pt = font_size_pt or default_font_size_pt

                # locate system .ttf for that family
                prop = fm.FontProperties(family=font_family)
                font_path = fm.findfont(prop)
                font_px = int(pt_to_px(font_size_pt, dpi))
                font = ImageFont.truetype(font_path, font_px)

                # wrap each paragraph, measuring with textbbox()
                lines = []
                for para in raw.split("\n"):
                    if not para:
                        continue
                    cur = ""
                    for word in para.split():
                        test = (cur + " " + word).strip()
                        bbox = draw.textbbox((0, 0), test, font=font)
                        w_px = bbox[2] - bbox[0]
                        if w_px <= disp_w_px:
                            cur = test
                        else:
                            # finish current line, start new one
                            if cur:
                                lines.append(cur)
                            cur = word
                    lines.append(cur)

                # Get font metrics for consistent line height
                ascent, descent = font.getmetrics()
                # Use a more generous line height calculation that includes some padding
                line_height_px = (ascent + descent) * 1.2  # Add 20% to basic metrics

                # Calculate total height needed
                # Add 30% spacing between lines, but not after the last line
                total_height_px = line_height_px * len(lines) * 1.3 - (
                    line_height_px * 0.3
                )

                # Add a small buffer to ensure text fits
                total_height_px += 4  # Add 4 pixels of buffer

                # Convert to points
                needed_h_pt = px_to_pt(total_height_px, dpi)

                # new scaleY (won't shrink)
                target_sy = needed_h_pt / orig_h_pt if orig_h_pt else 1.0
                new_sy = max(orig_sy, target_sy)

                # Calculate if the box would exceed page height
                slide_h_emu = pres["pageSize"]["height"]["magnitude"]
                final_h_emu = orig_h_emu * new_sy
                bottom_edge = orig_ty_emu + final_h_emu

                # If bottom would exceed page, reduce scale to fit
                if bottom_edge > slide_h_emu:
                    new_sy = (slide_h_emu - orig_ty_emu) / orig_h_emu

                # Keep original translateY - don't adjust position
                new_ty = orig_ty_emu

                requests.append(
                    {
                        "updatePageElementTransform": {
                            "objectId": oid,
                            "applyMode": "ABSOLUTE",
                            "transform": {
                                "scaleX": orig_sx,
                                "scaleY": new_sy,
                                "translateX": orig_tx_emu,
                                "translateY": new_ty,
                                "shearX": shearX,
                                "shearY": shearY,
                                "unit": "EMU",
                            },
                        }
                    }
                )

        # send in batches
        if requests:
            for i in range(0, len(requests), BATCH_SIZE):
                svc.presentations().batchUpdate(
                    presentationId=pres_id,
                    body={"requests": requests[i : i + BATCH_SIZE]},
                ).execute()

        logging.info("Resized %d text boxes in %s", len(requests), pres_id)
        return True

    except Exception as e:
        logging.error("Error resizing: %s", e)
        return False


def reset_google_slides_text_box_fonts(url: str) -> bool:
    """Reset Google Slides text box fonts to maintain consistency."""
    try:
        # Extract presentation ID from URL
        if "/d/" not in url:
            logging.error("Bad URL.")
            return False
        pres_id = url.split("/d/")[1].split("/")[0]

        # Initialize API client
        creds = service_account.Credentials.from_service_account_file(
            ".credentials/google_service_account_key.json",
            scopes=["https://www.googleapis.com/auth/presentations"],
        )
        service = build("slides", "v1", credentials=creds)

        # Fetch the presentation
        pres = service.presentations().get(presentationId=pres_id).execute()

        requests = []
        for slide in pres.get("slides", []):
            for elem in slide.get("pageElements", []):
                shape = elem.get("shape")
                if not shape:
                    continue

                # Get the text box's base style
                text_style = shape.get("text", {}).get("style", {})

                # Scan for the first TextRun with a fontFamily
                text_elems = shape.get("text", {}).get("textElements", [])
                font = None
                for te in text_elems:
                    tr = te.get("textRun")
                    if tr:
                        font = tr.get("style", {}).get("fontFamily")
                        if font:
                            break

                if not font:
                    # no explicit font on any run → skip
                    continue

                # Calculate weighted average font size and check for formatting
                total_length = 0
                weighted_size = 0
                has_bold = False
                has_italic = False
                has_underline = False
                has_strikethrough = False
                has_small_caps = False
                foreground_color = None
                background_color = None

                for te in text_elems:
                    tr = te.get("textRun")
                    if not tr:
                        continue

                    content = tr.get("content", "")
                    if not content:
                        continue

                    style = tr.get("style", {})
                    if not style:
                        continue

                    # Check for various formatting
                    if style.get("bold", False):
                        has_bold = True
                    if style.get("italic", False):
                        has_italic = True
                    if style.get("underline", False):
                        has_underline = True
                    if style.get("strikethrough", False):
                        has_strikethrough = True
                    if style.get("smallCaps", False):
                        has_small_caps = True

                    # Preserve the first non-null color we find
                    if not foreground_color and "foregroundColor" in style:
                        foreground_color = style["foregroundColor"]
                    if not background_color and "backgroundColor" in style:
                        background_color = style["backgroundColor"]

                    font_size = style.get("fontSize", {}).get(
                        "magnitude", 12
                    )  # default to 12pt
                    content_length = len(content)

                    total_length += content_length
                    weighted_size += font_size * content_length

                # Calculate average font size (default to 12pt if no text)
                avg_font_size = weighted_size / total_length if total_length > 0 else 12

                # Start with the text box's base style
                new_style = text_style.copy() if text_style else {}

                # Update with our calculated values
                new_style.update(
                    {
                        "fontFamily": font,
                        "fontSize": {"magnitude": avg_font_size, "unit": "PT"},
                        "bold": has_bold,
                        "italic": has_italic,
                        "underline": has_underline,
                        "strikethrough": has_strikethrough,
                        "smallCaps": has_small_caps,
                    }
                )

                # Add colors if they exist
                if foreground_color:
                    new_style["foregroundColor"] = foreground_color
                if background_color:
                    new_style["backgroundColor"] = background_color

                # Update the text style for the entire box
                requests.append(
                    {
                        "updateTextStyle": {
                            "objectId": elem["objectId"],
                            "textRange": {"type": "ALL"},
                            "style": new_style,
                            "fields": "fontFamily,bold,italic,underline,strikethrough,smallCaps,foregroundColor,backgroundColor,link,fontSize",
                        }
                    }
                )

        # Send all updates in one batch
        if requests:
            MAX = 450  # give headroom
            for i in range(0, len(requests), MAX):
                service.presentations().batchUpdate(
                    presentationId=pres_id,
                    body={"requests": requests[i : i + MAX]},
                ).execute()

        logging.info(f"Reset text box fonts for {pres_id}")
        return True

    except Exception as e:
        logging.error(f"Error resetting fonts: {e}")
        return False


def check_text_overflow(
    slides_url: str,
    default_font_size_pt: float = DEFAULT_FONT_SIZE_PT,
    dpi: int = DEFAULT_DPI,
) -> list[dict]:
    """
    Check each text box in a Slides presentation for overflow,
    correctly converting EMU or PT → pixels.

    Args:
        slides_url: full URL of the Google Slides presentation.
        default_font_size_pt: fallback font size (in pt).
        dpi: screen DPI (pixels per inch).

    Returns:
        A list of dicts, each containing:
            - objectId: the pageElement ID
            - slide_index: zero-based slide index
            - overflow: True if text exceeds box height
            - box_px: (width_px, height_px)
            - needed_px: total height_px needed to fit wrapped text
            - line_count: number of wrapped lines (including manual breaks)
            - font_used: the fontFamily applied
    """
    # 1. Extract the presentation ID
    if "/d/" in slides_url:
        pres_id = slides_url.split("/d/")[1].split("/")[0]
    else:
        raise ValueError(f"Invalid Slides URL: {slides_url}")

    # 2. Initialize the Slides API client
    creds = service_account.Credentials.from_service_account_file(
        ".credentials/google_service_account_key.json",
        scopes=["https://www.googleapis.com/auth/presentations.readonly"],
    )
    service = build("slides", "v1", credentials=creds)

    # 3. Fetch only the fields we need
    presentation = (
        service.presentations()
        .get(
            presentationId=pres_id,
            fields="slides(pageElements(objectId,size,transform,shape(text(textElements))))",
        )
        .execute()
    )
    # dummy context for measuring text
    dummy = Image.new("RGB", (1, 1))
    draw = ImageDraw.Draw(dummy)

    def extract_text_with_breaks(text_elements):
        """Reconstruct text, inserting '\n' at each paragraphMarker."""
        txt = ""
        for e in text_elements:
            if "textRun" in e and e["textRun"].get("content"):
                txt += e["textRun"]["content"]
            elif e.get("paragraphMarker"):
                txt += "\n"
        return txt.rstrip("\n")

    results = []
    for slide_idx, slide in enumerate(presentation.get("slides", [])):
        for elem in slide.get("pageElements", []):
            shape = elem.get("shape")
            if not shape or "text" not in shape:
                continue

            obj_id = elem["objectId"]
            raw = extract_text_with_breaks(shape["text"]["textElements"])

            xf = elem.get("transform", {})
            sx = float(xf.get("scaleX", 1.0))
            sy = float(xf.get("scaleY", 1.0))

            w_emu = elem["size"]["width"]["magnitude"] * sx
            h_emu = elem["size"]["height"]["magnitude"] * sy
            w_px = int(emu_to_px(w_emu, dpi))
            h_px = int(emu_to_px(h_emu, dpi))

            # pull fontFamily & fontSize
            font_family = None
            font_size_pt = None
            for run in shape["text"]["textElements"]:
                style = run.get("textRun", {}).get("style", {})
                if not font_family and style.get("fontFamily"):
                    font_family = style["fontFamily"]
                if not font_size_pt and style.get("fontSize"):
                    font_size_pt = style["fontSize"]["magnitude"]
                if font_family and font_size_pt:
                    break

            font_family = font_family or DEFAULT_FONT_FAMILY
            font_size_pt = font_size_pt or default_font_size_pt

            # find matching TTF
            prop = fm.FontProperties(family=font_family)
            font_path = fm.findfont(prop)
            font_px = int(pt_to_px(font_size_pt, dpi))
            font = ImageFont.truetype(font_path, font_px)

            # wrap each paragraph
            lines = []
            for para in raw.split("\n"):
                if not para:
                    lines.append("")
                    continue
                cur = ""
                for word in para.split():
                    candidate = (cur + " " + word).strip()
                    bbox = draw.textbbox((0, 0), candidate, font=font)
                    text_w = bbox[2] - bbox[0]
                    if text_w <= w_px:
                        cur = candidate
                    else:
                        lines.append(cur)
                        cur = word
                lines.append(cur)

            # compute needed height
            ascent, descent = font.getmetrics()
            line_h = ascent + descent
            needed_px = line_h * len(lines)

            results.append(
                {
                    "objectId": obj_id,
                    "slide_index": slide_idx,
                    "overflow": needed_px > h_px,
                    "box_px": (w_px, h_px),
                    "needed_px": needed_px,
                    "line_count": len(lines),
                    "font_used": font_family,
                }
            )

    return results


def get_gslides_text_contents(slides_url: str) -> list[dict]:
    """
    Extracts text content from a Google Slides presentation.
    Returns a list of dicts, each containing:
        - slide_index: zero-based index of the slide
        - text: text content of the slide, ordered by vertical and horizontal position

    Args:
        slides_url: URL of the Google Slides presentation

    Returns:
        list[dict]: List of dictionaries containing slide index and text content
    """
    cache_key = f"gslides_text_contents_{slides_url}"
    cached_result = cache.get(cache_key)
    if cached_result:
        return cached_result
    try:
        # Extract presentation ID from URL
        if "/d/" not in slides_url:
            raise ValueError(f"Invalid Slides URL: {slides_url}")
        pres_id = slides_url.split("/d/")[1].split("/")[0]

        # Initialize the Slides API client
        creds = service_account.Credentials.from_service_account_file(
            ".credentials/google_service_account_key.json",
            scopes=["https://www.googleapis.com/auth/presentations.readonly"],
        )
        service = build("slides", "v1", credentials=creds)

        # Fetch the presentation with transform information
        presentation = (
            service.presentations()
            .get(
                presentationId=pres_id,
                fields="slides(pageElements(objectId,transform,shape(text(textElements))))",
            )
            .execute()
        )

        results = []
        for slide_idx, slide in enumerate(presentation.get("slides", [])):
            # Store text elements with their positions
            text_elements_with_pos = []

            # Process each element in the slide
            for elem in slide.get("pageElements", []):
                shape = elem.get("shape")
                if not shape or "text" not in shape:
                    continue

                # Get the position from transform
                transform = elem.get("transform", {})
                y_pos = transform.get("translateY", 0)
                x_pos = transform.get("translateX", 0)

                # Extract text from text elements
                text_elements = shape["text"].get("textElements", [])
                current_text = []
                for text_elem in text_elements:
                    if "textRun" in text_elem and text_elem["textRun"].get("content"):
                        current_text.append(text_elem["textRun"]["content"])
                    elif text_elem.get("paragraphMarker"):
                        current_text.append("\n")

                if current_text:
                    text_elements_with_pos.append(
                        {"y_pos": y_pos, "x_pos": x_pos, "text": "".join(current_text)}
                    )
            # Sort text elements by y-position first, then x-position
            text_elements_with_pos.sort(key=lambda x: (x["y_pos"], x["x_pos"]))

            # Join all text elements in order
            text = "".join(elem["text"] for elem in text_elements_with_pos).strip()
            if text:  # Only add slides that have text content
                results.append({"slide_index": slide_idx, "text": text})
        # cache for 1 minute
        cache.set(cache_key, results, 60)
        return results

    except Exception as e:
        logging.error(f"Error extracting text from Google Slides: {str(e)}")
        raise
