import logging
import uuid
from datetime import datetime

from django.db import close_old_connections, connection

from ..models import Playbook, TargetInfo, TargetInfoGroup
from ..playbook_build.object_builder import ObjectBuilder
from ..playbook_build.playbook_builder import PlaybookBuilder
from ..thread_locals import set_current_user_as_tofuadmin_test


class PlaybookPostprocessTest:
    TEST_PLAYBOOK_ID = 552

    def __init__(self) -> None:
        try:
            close_old_connections()
            set_current_user_as_tofuadmin_test()
            self._playbook_instance = Playbook.objects.get(id=self.TEST_PLAYBOOK_ID)
        except Exception as e:
            logging.error(f"Error getting playbook {self.TEST_PLAYBOOK_ID}: {e}")
            raise e

        self._targets = []
        self._errors = []

    @property
    def errors(self):
        return self._errors

    def get_random_target_group_name(self):
        short_id = str(uuid.uuid4())[:8]
        date_str = datetime.now().strftime("%Y-%m-%d")

        target_list_name = f"test_target_group_{date_str}_{short_id}"
        return target_list_name

    def create_targets(self, target_list):
        target_1 = TargetInfo.objects.create(
            target_info_group=target_list,
            target_key="tofu",
            meta={
                "position": 0,
            },
            docs={
                "f32a3875": {
                    "id": "f32a3875",
                    "meta": {"field_name": "website"},
                    "type": "url",
                    "value": "https://www.tofuhq.com/",
                    "position": 0,
                }
            },
        )
        self._targets = [target_1]

    def check_postprocess_result(self):
        # check healthiness of the targets
        try:
            for object in self._targets:
                object.refresh_from_db()
                object_healthiness, object_errors = ObjectBuilder.get_builder(
                    object
                ).check_healthiness(rebuild=False)
                if not object_healthiness:
                    self._errors += object_errors
        except Exception as e:
            self._errors.append(f"Exception occurred: {e}")

    def run(self):
        try:
            # Ensure connection is open
            if connection.connection is None:
                connection.connect()

            # step 1: create a target list
            target_group_name = self.get_random_target_group_name()
            new_target_list = TargetInfoGroup.objects.create(
                playbook=self._playbook_instance,
                target_info_group_key=target_group_name,
                meta={
                    "type": "Company",
                },
            )

            # step 2: create a few targets
            self.create_targets(new_target_list)

            playbook_builder = PlaybookBuilder(self._playbook_instance)
            playbook_builder.update_context()

            # No need to sleep since tasks run synchronously in test mode
            self.check_postprocess_result()

            if not self._errors:
                # clean up when no errors otherwise we need to debug
                new_target_list.delete()

            return not self._errors
        finally:
            # Ensure connection is closed properly
            if connection.connection is not None:
                connection.close()
