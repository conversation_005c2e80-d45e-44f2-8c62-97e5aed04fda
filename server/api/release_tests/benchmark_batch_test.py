import concurrent.futures
import copy
import datetime
import json
import logging
import traceback

from ..eval import (
    Benchmark,
    benchmark_review_personalization_campaigns,
    benchmark_review_personalization_campaigns_small_set,
    benchmark_review_repurpose_campaigns,
    benchmark_review_repurpose_campaigns_small_set,
    benchmark_review_template_campaigns,
    benchmark_review_template_campaigns_small_set,
)
from ..thread_locals import set_current_user_as_tofuadmin_test, set_enable_crawl_api


class BenchmarkBatchTest:
    def get_tags(self):
        return ["benchmark", "batch_test"]

    def get_metadata(self):
        return {
            "tags": self.get_tags(),
            "description": "Run benchmark batch test",
        }

    def __init__(self, quickRun=True, session_id=None, session_tag=None) -> None:
        self.quickRun = quickRun

        self.benchmark_instance = Benchmark.get_benchmark_instance(
            session_id=session_id,
            session_tag=session_tag,
            test_only=True,
        )

        self.benchmark_prefix = f"benchmark-batch-test-{datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S')}"

        # initialize the env
        set_current_user_as_tofuadmin_test()

        self.tests_repurpose = (
            benchmark_review_repurpose_campaigns
            if not self.quickRun
            else benchmark_review_repurpose_campaigns_small_set
        )
        self.tests_p13n = (
            benchmark_review_personalization_campaigns
            if not self.quickRun
            else benchmark_review_personalization_campaigns_small_set
        )
        self.tests_template = (
            benchmark_review_template_campaigns
            if not self.quickRun
            else benchmark_review_template_campaigns_small_set
        )

    @property
    def models(self):
        return ["gpt-4o-2024-11-20", "us.anthropic.claude-3-5-sonnet-20240620-v1:0"]

    def _run_p13n_benchmark_single(
        self, prefix, model_name, campaign_ids, joint_generation
    ):
        set_current_user_as_tofuadmin_test()
        set_enable_crawl_api(True)

        num_of_variations = 1

        # TODO: failure handles
        result_data = self.benchmark_instance.gen_review(
            prefix=prefix,
            campaign_ids=campaign_ids,
            content_ids=[],
            model_name=model_name,
            num_of_variations=num_of_variations,
            enable_custom=True,
            generate_all_targets=[],
            joint_generation=joint_generation,
        )
        return result_data

    def _run_p13n_benchmark(self):
        set_current_user_as_tofuadmin_test()
        campaign_ids = self.tests_p13n

        joint_generation = True
        for campaign_id in campaign_ids:
            for model_name in self.models:
                prefix = f"{self.benchmark_prefix}_p13n_{model_name}_joint_{joint_generation}"
                job = (
                    self._run_p13n_benchmark_single,
                    {
                        "prefix": prefix,
                        "model_name": model_name,
                        "campaign_ids": [campaign_id],
                        "joint_generation": joint_generation,
                    },
                )
                yield job

        if campaign_ids:
            joint_generation = False
            for model_name in self.models:
                non_joint_generation_job = (
                    self._run_p13n_benchmark_single,
                    {
                        "prefix": f"{self.benchmark_prefix}_p13n_{model_name}_joint_{joint_generation}",
                        "model_name": model_name,
                        "campaign_ids": [campaign_ids[0]],
                        "joint_generation": joint_generation,
                    },
                )
                yield non_joint_generation_job

    def _run_repurposing_benchmark_single(
        self, prefix, model_name, campaign_ids, joint_generation
    ):

        set_current_user_as_tofuadmin_test()
        set_enable_crawl_api(True)
        num_of_variations = 1

        result_data = self.benchmark_instance.gen_review_repurpose(
            prefix=prefix,
            campaign_ids=campaign_ids,
            content_ids=[],
            model_name=model_name,
            num_of_variations=num_of_variations,
            enable_custom=True,
            generate_all_targets=[],
            joint_generation=joint_generation,
        )
        return result_data

    def _run_repurposing_benchmark(self):
        set_current_user_as_tofuadmin_test()
        campaign_ids = self.tests_repurpose

        joint_generation = True
        for campaign_id in campaign_ids:
            for model_name in self.models:
                prefix = f"{self.benchmark_prefix}_repurpose_{model_name}_joint_{joint_generation}"
                job = (
                    self._run_repurposing_benchmark_single,
                    {
                        "prefix": prefix,
                        "model_name": model_name,
                        "campaign_ids": [campaign_id],
                        "joint_generation": joint_generation,
                    },
                )
                yield job

        if campaign_ids:
            joint_generation = False
            for model_name in self.models:
                non_joint_generation_job = (
                    self._run_repurposing_benchmark_single,
                    {
                        "prefix": f"{self.benchmark_prefix}_repurpose_{model_name}_joint_{joint_generation}",
                        "model_name": model_name,
                        "campaign_ids": [campaign_ids[0]],
                        "joint_generation": joint_generation,
                    },
                )
                yield non_joint_generation_job

    def _run_template_benchmark_single(self, prefix, model_name, campaign_ids):

        set_current_user_as_tofuadmin_test()
        set_enable_crawl_api(True)

        num_of_variations = 1

        result_data = self.benchmark_instance.gen_review(
            prefix=prefix,
            campaign_ids=campaign_ids,
            content_ids=[],
            model_name=model_name,
            num_of_variations=num_of_variations,
            enable_custom=True,
            generate_all_targets=[],
            template_generation=True,
        )
        return result_data

    def _run_template_benchmark(self):
        campaign_ids = self.tests_template

        for model_name in self.models:
            prefix = f"{self.benchmark_prefix}_template_{model_name}"

            job = (
                self._run_template_benchmark_single,
                {
                    "prefix": prefix,
                    "model_name": model_name,
                    "campaign_ids": campaign_ids,
                },
            )
            yield job

    @property
    def benchmark_jobs(self):
        for flows in [
            self._run_p13n_benchmark,
            self._run_repurposing_benchmark,
            self._run_template_benchmark,
        ]:
            for job in flows():
                yield job

    def _execute_jobs_sequentially(self, jobs):
        results = []
        for job_func, kwargs in jobs:
            result = job_func(**kwargs)  # Unpack kwargs into the function call
            results.append(result)
        return results

    def _execute_jobs_in_parallel(self, jobs):
        logging.info(f"number of jobs to run for batch test: {len(jobs)}")
        results = []
        max_workers = 10
        timeout = 60 * 10  # 10 minutes
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(job[0], **job[1]) for job in jobs
            ]  # Unpack kwargs for each job
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result(timeout=timeout)
                    results.append(result)
                except concurrent.futures.TimeoutError as e:
                    logging.error("Job TimeoutError: {future} with error: {e}")
                except Exception as e:
                    logging.error(
                        f"Job generated an exception: {e}\n{traceback.format_exc()}"
                    )
        return results

    # following methods are generic and usually no need to change
    def run(self, parallel=True):
        try:
            jobs = self.benchmark_jobs
            jobs = list(jobs)
            if parallel:
                self._execute_jobs_in_parallel(list(jobs))
            else:
                self._execute_jobs_sequentially(jobs)
        except Exception as e:
            logging.error(
                f"Fail to run benchmark batch test: {e}\n{traceback.format_exc()}"
            )
            return False
        return True
