import logging
import os
import sys
import traceback
from abc import ABC, abstractmethod

from django.db import close_old_connections, connection
from server.celery import app as celery_app


class ErrorFilteringHandler(logging.Handler):
    def __init__(self, known_prefix, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.known_prefix = known_prefix
        self.unknown_errors = []

    # TODO: implement a more sophisticated error filtering mechanism
    def is_known_error(self, error_message):
        if any(error_message.startswith(prefix) for prefix in self.known_prefix):
            return True
        return False

    def emit(self, record):
        error_message = record.getMessage()
        if not self.is_known_error(error_message):
            error_stack = str(traceback.format_stack())
            self.unknown_errors.append(f"{error_message}\n{error_stack}")


class ReleaseBaseTest(ABC):
    def __init__(self) -> None:
        self._errors = []

    @property
    def known_error_prefix(self):
        return []

    @abstractmethod
    def run(self):
        raise NotImplementedError("run method is not implemented")

    @property
    def errors(self):
        return self._errors


class ReleaseTestIntf:
    def __init__(self, print_all=False) -> None:
        self._print_all = print_all

        # init env
        celery_app.conf.update(
            task_always_eager=True,
            task_eager_propagates=True,
        )

        self.original_level = logging.getLogger().level
        self.original_handlers = logging.getLogger().handlers[:]

        self.root_logger = self._override_logger()

        self.errors = []

    def _override_logger(self):
        root_logger = logging.getLogger()
        if not self._print_all:
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)

            # Set the root logger level directly
            root_logger.setLevel(logging.ERROR)

            all_tests = self.all_tests
            known_prefix = []
            for test in all_tests.values():
                known_prefix.extend(test.known_error_prefix)

            # Add your custom handler
            root_logger.addHandler(ErrorFilteringHandler(known_prefix=known_prefix))
        return root_logger

    def __del__(self):
        # Reset the logger to its original state
        root_logger = logging.getLogger()
        root_logger.setLevel(self.original_level)
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        for handler in self.original_handlers:
            root_logger.addHandler(handler)

    def run_release_tests(self, test_name=None):
        # benchmark batch test
        all_success = True
        try:
            tests = self.get_tests(test_name)
            for test in tests:
                success = test.run()
                all_success = all_success and success and not test.errors
                self.errors.extend(test.errors)
                close_old_connections()

        except Exception as e:
            logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
            print(f"Exception occurred: {e}\n{traceback.format_exc()}")
            self.errors.append(f"Exception occurred: {e}")
        finally:
            close_old_connections()

        for handler in self.root_logger.handlers:
            if isinstance(handler, ErrorFilteringHandler) and handler.unknown_errors:
                self.errors.extend(handler.unknown_errors)

        all_success = all_success and not self.errors
        return all_success

    def get_errors(self):
        return self.errors

    def get_tests(self, test_name=None):
        all_tests = self.all_tests
        if not test_name:
            return all_tests.values()
        return [test for name, test in all_tests.items() if name.find(test_name) != -1]

    @property
    def all_tests(self):
        return {
            "benchmark": ReleaseBenchmarkQuickTest(),
            "playbook_post_process": ReleasePlaybookPostprocessTest(),
        }


class ReleaseBenchmarkQuickTest(ReleaseBaseTest):
    def __init__(self) -> None:
        self._errors = []

    @property
    def known_error_prefix(self):
        return [
            "Generated content is too short.",
            "generation does not contain key",
            "Generated content exceeds length",
            "Failed to parse joint generation.",
            "Key of original component",
            "generation does not contain text for key",
            "Error in _async_agenerate_wrapper_claude due to ThrottlingException: Error raised by bedrock service:",
        ]

    def run(self):
        try:
            from ..release_tests.benchmark_batch_test import BenchmarkBatchTest

            return BenchmarkBatchTest(quickRun=True).run()
        except Exception as e:
            logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
            print(f"Exception occurred: {e}\n{traceback.format_exc()}")

            self._errors.append(f"Exception occurred: {e}")
            return False


class ReleasePlaybookPostprocessTest(ReleaseBaseTest):
    def __init__(self) -> None:
        self._errors = []

    @property
    def known_error_prefix(self):
        return []

    def run(self):
        try:
            # Ensure connection is open
            if connection.connection is None:
                connection.connect()

            from .playbook_postprocess_test import PlaybookPostprocessTest

            test = PlaybookPostprocessTest()
            res = test.run()
            self._errors.extend(test.errors)

            # Ensure connection is properly closed
            if connection.connection is not None:
                connection.close()

            return res

        except Exception as e:
            logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
            print(f"Exception occurred: {e}\n{traceback.format_exc()}")

            self._errors.append(f"Exception occurred: {e}")

            # Ensure connection is properly closed even on error
            if connection.connection is not None:
                connection.close()

            return False
        finally:
            # Ensure connection is closed properly
            if connection.connection is not None:
                connection.close()
