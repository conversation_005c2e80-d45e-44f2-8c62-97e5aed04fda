import argparse
import logging
import os
import sys
import traceback

import django


def run_inout_checks(args):
    # init env
    # Django setup
    notebook_dir = os.path.dirname(os.path.abspath("__file__"))
    tofu_dir = os.path.dirname(notebook_dir)
    sys.path.append(tofu_dir + "/server")
    os.environ["DJANGO_SETTINGS_MODULE"] = "server.settings"
    os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
    django.setup()

    from server.celery import app as celery_app

    celery_app.conf.update(
        CELERY_ALWAYS_EAGER=True
    )  # we don't use celery but just be safe

    from api.integrations.slack import send_slack_message
    from api.release_tests.llm_inout_check.llm_check_runner import LLMInOutCheckRunner

    if args.list:
        try:
            LLMInOutCheckRunner.list_tests(args.list)
            return
        except Exception as e:
            logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
            sys.exit(0)

    if args.update_golden_set:
        try:
            LLMInOutCheckRunner.update_golden_set()
            return
        except Exception as e:
            logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
            sys.exit(1)

    try:
        test_passed, report = LLMInOutCheckRunner.test_with_golden_set()
        logging.info(f"Test passed: {test_passed}")
        logging.info(f"Report:\n{report}")
        if args.report:
            with open(args.report, "w") as f:
                f.write(report)

        send_slack_message("#bot_tests", report)

        if not test_passed:
            logging.error(report)
            sys.exit(1)  # tmp mark not blocking and revisit later
        else:
            logging.info(report)
            sys.exit(0)

    except Exception as e:
        logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Run release tests from llm inout comparison."
    )
    parser.add_argument(
        "--report",
        type=str,
        help="Provide a file name to save the report.",
    )
    parser.add_argument(
        "--update-golden-set",
        action="store_true",
        help="Update golden set.",
    )
    parser.add_argument(
        "--list",
        type=str,
        help="List runs given the session_id.",
    )

    args = parser.parse_args()
    run_inout_checks(args)
