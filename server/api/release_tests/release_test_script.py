import argparse
import logging
import os
import sys
import traceback

import django


def run_release_tests_local(args):
    try:
        # Django setup
        notebook_dir = os.path.dirname(os.path.abspath("__file__"))
        tofu_dir = os.path.dirname(notebook_dir)
        sys.path.append(tofu_dir + "/server")
        os.environ["DJANGO_SETTINGS_MODULE"] = "server.settings"
        os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
        django.setup()

        def run():
            from api.release_tests.release_test_intf import ReleaseTestIntf

            print_all = bool(args.print_all)

            test_intf = ReleaseTestIntf(print_all=print_all)

            if args.list_tests:
                tests = test_intf.all_tests.keys()
                for test in tests:
                    print(test)
                return

            test_name = None
            if args.test:
                test_name = args.test

            run_sucess = test_intf.run_release_tests(test_name=test_name)
            if not run_sucess:
                logging.error(f"Errors: {test_intf.get_errors()}")
                for error in test_intf.get_errors():
                    print(f"\n-------\n {error} \n-------\n")
                sys.exit(1)

        run()
    except Exception as e:
        logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
        print(f"Exception occurred: {e}\n{traceback.format_exc()}")
        sys.exit(1)


def run_release_tests_api(url):
    raise NotImplementedError("This function is not implemented yet.")


def run_release_tests_dev():
    url = "https://dev.api.tofuhq.com/api/eval/test_benchmark/"
    run_release_tests_api(url)


def run_release_tests_prod():
    url = "https://api.tofuhq.com/api/eval/test_benchmark/"
    run_release_tests_api(url)


def run_release_tests(args):

    run_env = (
        "local"  # force override the value since other environments are not available
    )
    if run_env == "local":
        run_release_tests_local(args)
    elif run_env == "prod":
        run_release_tests_prod()
    elif run_env == "dev":
        run_release_tests_dev()
    else:
        raise ValueError(f"Invalid environment: {run_env}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Run release tests for a specific environment."
    )
    parser.add_argument(
        "--env",
        type=str,
        default="local",
        choices=["dev", "prod", "local"],
        help="Specifies the environment to run tests against. Options are: dev, prod, local.",
    )
    parser.add_argument(
        "--list-tests",
        action="store_true",
        help="List all available tests to run.",
    )
    parser.add_argument(
        "--test",
        type=str,
        help="Run a specific test.",
    )
    parser.add_argument(
        "--print-all",
        action="store_true",
        help="Print all logs.",
    )

    args = parser.parse_args()

    run_release_tests(args)
