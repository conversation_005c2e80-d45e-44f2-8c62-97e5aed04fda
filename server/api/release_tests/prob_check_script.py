import argparse
import logging
import os
import sys
import traceback

import django


class Prob<PERSON>heck<PERSON>unner:
    def __init__(self) -> None:
        pass

    def run(self, content_ids, prompt_changes=None):
        pass

    def run_with_filter(self):
        pass


def run_prob_check(args):
    try:
        # Django setup
        notebook_dir = os.path.dirname(os.path.abspath("__file__"))
        tofu_dir = os.path.dirname(notebook_dir)
        sys.path.append(tofu_dir + "/server")
        os.environ["DJANGO_SETTINGS_MODULE"] = "server.settings"
        os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
        django.setup()

        def run():
            from api.content import ContentGenerator
            from api.models import Content, Playbook
            from api.playbook import PlaybookHandler

            # resolve args
            content_id = args.content_id
            num_of_variations = args.num_of_variations

            content = Content.objects.get(id=content_id)
            if not content:
                logging.error(f"Content with id {content_id} not found.")
                print(f"Content with id {content_id} not found.")
                sys.exit(1)

            playbook = content.playbook
            playbook_handler = PlaybookHandler(playbook)

            generator = ContentGenerator(
                playbook_handler=playbook_handler, content=content
            )
            generator.set_settings(
                num_of_variations=num_of_variations,
                save_variations=False,
            )

            variations = generator.gen()
            print(variations)

        run()
    except Exception as e:
        logging.error(f"Exception occurred: {e}\n{traceback.format_exc()}")
        print(f"Exception occurred: {e}\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test variations for a given content.")
    parser.add_argument(
        "--content_id",
        type=str,
        help="Provide a content to re-run.",
    )
    parser.add_argument(
        "--num_of_variations",
        type=int,
        default=5,
        help="Number of variations to generate.",
    )
    args = parser.parse_args()

    run_prob_check(args)
