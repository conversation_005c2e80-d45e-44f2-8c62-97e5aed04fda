import difflib
import html
import json
import logging
import textwrap
import uuid

import boto3
from langsmith import Client

MAX_LINE_LENGTH = 80  # Adjust this value as needed


def wrap_text(text):
    wrapped_lines = []
    for line in text.splitlines():
        wrapped_lines.extend(textwrap.wrap(line, MAX_LINE_LENGTH))
    return "<br>".join(wrapped_lines)


def generate_diff_html(metadata, messages1, messages2):

    html_content = """
    <html>
    <head>
        <title>Message Comparison</title>
        <style>
            .metadata {{
                margin-bottom: 20px;
            }}
            .comparison {{
                display: flex;
            }}
            .column {{
                flex: 1;
                padding: 10px;
            }}
            .message {{
                margin-bottom: 10px;
                padding: 10px;
                border: 1px solid #ccc;
            }}
            .message-type {{
                font-weight: bold;
            }}
            .diff_add {{
                background-color: #c1ffc1;
            }}
            .diff_chg {{
                background-color: #fffacd;
            }}
            .diff_sub {{
                background-color: #ffc1c1;
            }}
        </style>
    </head>
    <body>
        <div class="metadata">
            {metadata}
        </div>
        <div class="comparison">
            <div class="column">
                {messages_html}
            </div>
        </div>
    </body>
    </html>
    """

    metadata_html = "<br>".join([f"{key}: {value}" for key, value in metadata.items()])

    messages_html = ""

    for i in range(max(len(messages1), len(messages2))):
        if i < len(messages1):
            message1 = messages1[i]
            message_type1 = message1.get("type", "")
            message_content1 = message1.get("content", "")
        else:
            message_type1 = ""
            message_content1 = ""

        if i < len(messages2):
            message2 = messages2[i]
            message_content2 = message2.get("content", "")
        else:
            message_content2 = ""

        if message_content1 != message_content2:
            diff = difflib.HtmlDiff(wrapcolumn=80).make_file(
                message_content1.splitlines(),
                message_content2.splitlines(),
                context=True,
                numlines=1,
            )
            # Remove the legends and adjust the HTML structure
            diff_lines = diff.split("\n")
            diff_lines = [
                line
                for line in diff_lines
                if not line.startswith("<tr>")
                or not line.startswith('<td class="diff_next">')
            ]
            diff_lines = [
                line
                for line in diff_lines
                if not line.startswith('<td class="legend">')
            ]
            diff_lines = [
                line
                for line in diff_lines
                if not line.startswith('<table class="diff" summary="Legends">')
            ]
            diff_lines = [
                line
                for line in diff_lines
                if not line.startswith('<tr> <th colspan="2"> Legends </th> </tr>')
            ]
            diff_lines = [
                line
                for line in diff_lines
                if not line.startswith('<tr> <td> <table border="" summary="Colors">')
            ]
            diff_lines = [
                line
                for line in diff_lines
                if not line.startswith('<tr> <td> <table border="" summary="Links">')
            ]
            diff = "\n".join(diff_lines)
            message_content1 = diff
        else:
            message_content1 = wrap_text(message_content1)

        messages_html += f'<div class="message"><span class="message-type">{message_type1}</span><br>{message_content1}</div>'

    res = html_content.format(
        metadata=metadata_html,
        messages_html=messages_html,
    )

    return res


def upload_report_html_to_s3(html_content, target_file):
    tmp_html_file = f"/tmp/{uuid.uuid4()}.html"
    with open(tmp_html_file, "w") as f:
        f.write(html_content)

    s3 = boto3.client("s3")
    bucket = "tofu-public-files"
    extra_args = {"ContentType": "text/html"}

    s3.upload_file(tmp_html_file, bucket, target_file, ExtraArgs=extra_args)

    s3_link = f"https://{bucket}.s3.amazonaws.com/{target_file}"
    return s3_link


def dump_llm_input_messages(test_messages, golden_messages, metadata, target_file):
    html_content = generate_diff_html(metadata, test_messages, golden_messages)

    s3_link = upload_report_html_to_s3(html_content, target_file)
    logging.info(f"link to the report: {s3_link}")
    return s3_link


def convert_run_id_to_link(run_id):
    url = f"https://smith.langchain.com/o/44ba7596-5fd3-5a13-9c7f-68a1dd91b341/projects/p/d6ccf463-ce35-4590-904d-9683895ba5bf?peek={run_id}"
    return f'<a href="{url}">{run_id}</a>'


# for debugging
def dump_llm_inputs(test_run, golden_run, metadata, report_prefix):
    logging.info(
        f"compare llm inputs between test_run: {test_run.id} vs golden_run: {golden_run.id}"
    )
    if "messages" not in test_run.inputs:
        logging.error(
            f"test_run: {test_run.id} has no messages: {test_run.inputs.keys()}"
        )
        return
    if "messages" not in golden_run.inputs:
        logging.error(
            f"golden_run: {golden_run.id} has no messages: {golden_run.inputs.keys()}"
        )
        return
    test_inputs = test_run.inputs["messages"]
    golden_inputs = golden_run.inputs["messages"]

    metadata.update(
        {
            "test_run_id": test_run.id,
            "golden_run_id": golden_run.id,
        }
    )

    def processed_messages(inputs):
        messages = []
        for input in inputs:
            messages.append(
                {
                    "type": input["id"][-1],
                    "content": input["kwargs"]["content"],
                }
            )
        return messages

    test_messages = processed_messages(test_inputs)
    golden_messages = processed_messages(golden_inputs)

    if test_messages == golden_messages:
        return None

    report_file = f"{report_prefix}_{test_run.id}.html"
    return dump_llm_input_messages(
        test_messages, golden_messages, metadata, report_file
    )


# for debugging
def dump_llm_inputs_by_id(test_run_id, golden_run_id):
    client = Client()
    test_run = client.list_runs(id=[test_run_id])
    test_run = list(test_run)[0]

    golden_run = client.list_runs(id=[golden_run_id])
    golden_run = list(golden_run)[0]

    report_prefix = "llm_inputs_diff_v2"
    dumped_report = dump_llm_inputs(test_run, golden_run, {}, report_prefix)
    return dumped_report


def dump_llm_output_diff(test_run_output, golden_run_output, metadata, target_file):

    # Convert JSON objects to formatted strings
    json_str1 = json.dumps(test_run_output, indent=2)
    json_str2 = json.dumps(golden_run_output, indent=2)

    # Split the JSON strings into lines
    lines1 = json_str1.splitlines()
    lines2 = json_str2.splitlines()

    # Generate the diff using difflib
    differ = difflib.HtmlDiff()
    diff_table = differ.make_table(lines1, lines2, fromdesc="", todesc="")

    # Escape the diff table to handle special characters
    # diff_table = html.escape(diff_table)

    # Create the HTML page with the diff view
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>JSON Diff View</title>
        <style>
            table {{
                border-collapse: collapse;
                width: 100%;
            }}
            th, td {{
                border: 1px solid black;
                padding: 8px;
                text-align: left;
            }}
            .diff_add {{
                background-color: #a6f3a6;
            }}
            .diff_chg {{
                background-color: #ffff99;
            }}
            .diff_sub {{
                background-color: #f8cbcb;
            }}
        </style>
    </head>
    <body>
        <h1>JSON Diff View</h1>
        {diff_table}
    </body>
    </html>
    """

    s3_link = upload_report_html_to_s3(html_content, target_file)
    logging.info(f"link to the report: {s3_link}")
    return s3_link


def dump_report(columns, report_data, other_errors, report_file):
    # Start the HTML with a table, including headers for metadata keys
    html_content = [
        "<html><head><script src='https://www.kryogenix.org/code/browser/sorttable/sorttable.js'></script></head><body>"
    ]

    for key, value in other_errors.items():
        if not value:
            continue
        html_content.append(f"<h2>{key} ({len(value)} cases)</h2>")
        html_content.append('<table border="1" class="sortable">')
        keys = list(value[0].keys())

        def custom_sort(key):
            if key in ("test_run_id", "golden_run_id"):
                return (0, key)  # Prioritize these keys by returning 0
            return (1, key)  # Other keys come later

        # Sort the keys using the custom sorting function
        keys = sorted(keys, key=custom_sort)

        # dump columns
        html_content.append(
            "<tr>" + "".join(f"<th>{key}</th>" for key in keys) + "</tr>"
        )
        # dump data from value one by one
        for record in value:
            cells = []
            for key in keys:
                value = record.get(key, "")
                cells.append(
                    f"<td>{convert_run_id_to_link(value) if key in ('test_run_id', 'golden_run_id') else value}</td>"
                )
            html_content.append("<tr>" + "".join(cells) + "</tr>")
        html_content.append("</table>")

    def add_data(report_data, title):
        html_content.append(f"<h2>{title} ({len(report_data)} cases)</h2>")
        html_content.append('<table border="1" class="sortable">')
        html_content.append(
            "<tr>" + "".join(f"<th>{key}</th>" for key in columns) + "</tr>"
        )

        for record in report_data:
            cells = []
            for key in columns:
                value = record.get(key, "")

                if isinstance(value, dict):
                    vvalue = value.get("value", "")
                    if value.get("type") == "link":
                        cells.append(f'<td><a href="{vvalue}">{key}</a></td>')
                    else:
                        severity = value.get("severity_level", "")

                        color = (
                            "ccffcc"
                            if severity == "info"
                            else "ffffcc" if severity == "warning" else "ffcccc"
                        )
                        cells.append(
                            f'<td style="background-color: #{color};">{vvalue}</td>'
                        )
                else:
                    cells.append(
                        f"<td>{convert_run_id_to_link(value) if key in ('test_run_id', 'golden_run_id') else value}</td>"
                    )
            html_content.append("<tr>" + "".join(cells) + "</tr>")

        # Finish the table
        html_content.append("</table>")

    add_data(report_data["error"], "error")
    add_data(report_data["warning"], "warning")
    add_data(report_data["info"], "info")

    html_content.append("</body></html>")

    html_content = "".join(html_content)
    s3_link = upload_report_html_to_s3(html_content, report_file)
    logging.info(f"link to the report: {s3_link}")
    return s3_link
