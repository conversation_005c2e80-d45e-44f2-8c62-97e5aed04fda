import logging
import os
import traceback

from dotenv import load_dotenv
from langsmith import Client

# Load environment variables from .env file
load_dotenv()


class LangsmithRunWrapper:
    def __init__(self, run=None, run_id=None):
        self.run = run
        self.run_id = run_id
        # Get the API key from environment variable
        api_key = os.getenv("LANGCHAIN_API_KEY")
        if not api_key:
            raise ValueError("LANGCHAIN_API_KEY not found in environment variables")

        if self.run is None and self.run_id is not None:
            # Use the API key when creating the Client
            client = Client(api_key=api_key)
            search_runs = client.list_runs(id=[run_id])
            if not search_runs:
                raise Exception(f"Run {run_id} not found")
            self.run = list(search_runs)[0]

    def __getattr__(self, item):
        return getattr(self.run, item)

    def get_llm_input(self):
        return self.run.inputs

    def get_llm_output(self):
        return self.run.outputs


class LangsmithRunDiffWrapper:
    def __init__(self, run_wrapper_1, run_wrapper_2):
        self.run_wrapper_1 = run_wrapper_1
        self.run_wrapper_2 = run_wrapper_2

    def diff_llm_input(self):
        return self.run_wrapper_1.get_llm_input() == self.run_wrapper_2.get_llm_input()

    def diff_llm_output(self):
        return (
            self.run_wrapper_1.get_llm_output() == self.run_wrapper_2.get_llm_output()
        )


# from .llm
class LLMInputChecker:
    def __init__(self, reporter, test_run, golden_run=None) -> None:
        self.reporter = reporter
        self.test_run = test_run
        self.golden_run = golden_run

    @property
    def test_run_messages(self):
        return self._get_messages(self.test_run)

    @property
    def golden_run_messages(self):
        return self._get_messages(self.golden_run) if self.golden_run else []

    @staticmethod
    def _get_message(message):
        message_content = message["kwargs"]["content"]
        # strip source lines
        message_content = "\n".join(
            [
                message_line
                for message_line in message_content.split("\n")
                if message_line.find("/var/folders") == -1
                and message_line.find("/tmp") == -1
                and message_line.find("Content Collection Plans") == -1
            ]
        )
        # Handle the case where message["id"] might be a string, list, or other type
        message_type = message["id"]
        if isinstance(message["id"], list):
            message_type = message["id"][-1]
        elif isinstance(message["id"], str):
            message_type = message["id"].split(".")[-1]
        else:
            message_type = str(message["id"])

        return {
            "type": message_type,
            "content": message_content,
        }

    @staticmethod
    def _get_messages_for_format_v1(run):
        if "messages" not in run.inputs:
            return []

        messages = []
        for message in run.inputs["messages"]:
            try:
                message_data = LLMInputChecker._get_message(message)
                messages.append(message_data)
            except Exception as e:
                logging.error(
                    f'Error processing message: {e} for message "{message}"\n{traceback.format_exc()}'
                )
        return messages

    @staticmethod
    def _get_messages_for_format_v2(run):
        if (
            "messages" not in run.inputs
            or not isinstance(run.inputs["messages"], list)
            or len(run.inputs["messages"]) == 0
        ):
            return []
        if not isinstance(run.inputs["messages"][0], list):
            return []

        messages = []
        for message in run.inputs["messages"][0]:
            try:
                message_data = LLMInputChecker._get_message(message)
                messages.append(message_data)
            except Exception as e:
                logging.error(
                    f'Error processing message: {e} for message "{message}"\n{traceback.format_exc()}'
                )
        return messages

    @staticmethod
    def _get_messages(run):
        # try v2 first
        messages = LLMInputChecker._get_messages_for_format_v2(run)
        if len(messages) > 0:
            return messages

        # try v1
        messages = LLMInputChecker._get_messages_for_format_v1(run)
        if len(messages) > 0:
            return messages

        logging.error(f"No messages found for run {run.id} for {run.inputs}")
        return []

    def check(self):

        self.reporter.add_llm_input_diff(
            self.test_run,
            self.test_run_messages,
            self.golden_run_messages,
        )

        # always dump inputs
        exact_equal = self._exact_equal_check()
        if exact_equal:
            self.reporter.add_test_result(
                self.test_run,
                "llm_input_exact_match",
                "info",
                "Pass: exact match",
                "text",
            )
        else:
            self.reporter.add_test_result(
                self.test_run,
                "llm_input_exact_match",
                "warning",
                "Fail: exact match",
                "text",
            )

        if exact_equal or self._fuzzy_equal_check():
            self.reporter.add_test_result(
                self.test_run,
                "llm_input_fuzzy_match",
                "info",
                "Pass: fuzzy match",
                "text",
            )
        else:
            self.reporter.add_test_result(
                self.test_run,
                "llm_input_fuzzy_match",
                "error",
                "Fail: fuzzy match failed",
                "text",
            )

    def _exact_equal_check(self):
        if len(self.test_run_messages) != len(self.golden_run_messages):
            return False

        if self.test_run_messages == self.golden_run_messages:
            return True

        for test_run_message, golden_run_message in zip(
            self.test_run_messages, self.golden_run_messages
        ):
            if (
                test_run_message["content"].lower()
                == golden_run_message["content"].lower()
            ):
                continue

            return False
        return True

    def _fuzzy_equal_check(self):
        if len(self.test_run_messages) != len(self.golden_run_messages):
            return False

        def get_sorted_lines(message):
            lines = message["content"].lower().split("\n")
            lines.sort()
            return lines

        for test_run_message, golden_run_message in zip(
            self.test_run_messages, self.golden_run_messages
        ):
            if get_sorted_lines(test_run_message) == get_sorted_lines(
                golden_run_message
            ):
                continue

            # TODO: compare based on message with LLM models
            return False
        return True


def main():
    import sys

    if len(sys.argv) not in [2, 3]:
        print("Usage: python llm_input_check.py <run_id> [<run_id2>]")
        sys.exit(1)

    try:
        if len(sys.argv) == 2:
            run_id = sys.argv[1]
            wrapper = LangsmithRunWrapper(run_id=run_id)

            print("LLM Input:")
            print(wrapper.get_llm_input())
            print("\nLLM Output:")
            print(wrapper.get_llm_output())
        else:
            run_id1, run_id2 = sys.argv[1], sys.argv[2]
            wrapper1 = LangsmithRunWrapper(run_id=run_id1)
            wrapper2 = LangsmithRunWrapper(run_id=run_id2)
            diff_wrapper = LangsmithRunDiffWrapper(wrapper1, wrapper2)

            print(f"Comparing runs: {run_id1} and {run_id2}")
            print("\nLLM Input Comparison:")
            if diff_wrapper.diff_llm_input():
                print("Inputs are identical.")
            else:
                print("Inputs differ:")
                print(f"Run {run_id1} input:")
                print(wrapper1.get_llm_input())
                print(f"\nRun {run_id2} input:")
                print(wrapper2.get_llm_input())

            print("\nLLM Output Comparison:")
            if diff_wrapper.diff_llm_output():
                print("Outputs are identical.")
            else:
                print("Outputs differ:")
                print(f"Run {run_id1} output:")
                print(wrapper1.get_llm_output())
                print(f"\nRun {run_id2} output:")
                print(wrapper2.get_llm_output())
    except ValueError as e:
        print(f"Error: {e}")
        print(
            "Make sure you have set the LANGCHAIN_API_KEY in your .env file or environment variables."
        )
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
