import logging
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>cut<PERSON>, as_completed
from difflib import <PERSON>quence<PERSON>atch<PERSON>

from langsmith import Client

from .llm_inout_report import <PERSON><PERSON><PERSON>ort<PERSON>
from .llm_input_check import <PERSON><PERSON>n<PERSON><PERSON>he<PERSON>
from .llm_output_check import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def convert_key_to_dict(key):
    metadata = {}
    for pair in key.split("|"):
        k, v = pair.split("=")
        metadata[k] = v
    return metadata


class LLMInOutChecker:
    keys_to_check_with_default = {
        "orig_campaign_id": "None",
        "orig_content_group_id": "None",
        "targets": "None",
        "foundation_model": "None",
        "enable_custom": "None",
        "template_generation": "None",
        "content_collection_plan_gen": "None",
        "component_id": "None",
        "campaign_goal": "None",
        "is_campaign_v3": "None",
        "content_type": "None",
    }

    def __init__(self, session_id, test_runs, golden_runs) -> None:
        self._fetch_all_runs(test_runs, golden_runs)

        self._errors = []
        self._warnings = []
        self._reporter = LLMReporter(session_id=session_id)

    # this is to reduce the number of queries which may fail
    def _fetch_all_runs(self, test_runs, golden_runs):
        test_run_ids = [run.id for run in test_runs]
        golden_run_ids = [run.id for run in golden_runs]
        ignored_names = ["content_postprocess", "brand_guideline_rewriting"]

        def query_runs(all_runs):
            run_map = {}

            all_runs = [run for run in all_runs if run.name not in ignored_names]

            all_child_run_ids = []
            for run in all_runs:
                if run.child_run_ids:
                    all_child_run_ids += run.child_run_ids
                run_map[run.id] = run
            all_child_run_ids = list(set(all_child_run_ids))
            all_child_run_ids = [
                run_id for run_id in all_child_run_ids if run_id not in run_map
            ]

            client = Client()

            def batch_query(run_ids):
                trunk_size = 100
                results = []
                for i in range(0, len(run_ids), trunk_size):
                    results += client.list_runs(id=run_ids[i : i + trunk_size])
                return results

            while all_child_run_ids:
                next_child_run_ids = []
                logging.info(f"list_runs: {len(all_child_run_ids)}")
                all_child_runs = batch_query(
                    all_child_run_ids
                )  # client.list_runs(id=all_child_run_ids)
                all_child_runs = [
                    run for run in all_child_runs if run.name not in ignored_names
                ]
                for child_run in all_child_runs:
                    if child_run.id in run_map:
                        logging.error(f"duplicate run: {child_run.id}")
                    run_map[child_run.id] = child_run

                    if child_run.child_run_ids:
                        next_child_run_ids += child_run.child_run_ids

                next_child_run_ids = list(set(next_child_run_ids))
                next_child_run_ids = [
                    run_id for run_id in next_child_run_ids if run_id not in run_map
                ]
                all_child_run_ids = next_child_run_ids

            return run_map

        all_runs = test_runs + golden_runs
        run_map = query_runs(all_runs)

        self.test_llm_runs = {}
        self.golden_llm_runs = {}

        client = Client()

        for run in run_map.values():
            if not self.is_llm_job(run):
                continue

            parent_run_id = run.parent_run_id
            if parent_run_id not in run_map:
                parent_run = client.list_runs(id=[parent_run_id])
                parent_run = list(parent_run)
                if not parent_run:
                    logging.error(f"parent run: {parent_run_id} not found")
                    continue
                parent_run = parent_run[0]
                if parent_run.name not in ignored_names:
                    logging.error(
                        f"parent run: {parent_run_id} not found and not in {ignored_names}"
                    )
                    continue
                continue
            parent_run = run_map[parent_run_id]
            if self._skip_checks(parent_run):
                continue
            key = self._convert_to_hashkey(parent_run)

            if parent_run_id in test_run_ids:
                if key not in self.test_llm_runs:
                    self.test_llm_runs[key] = []
                self.test_llm_runs[key].append(run)
            elif parent_run_id in golden_run_ids:
                if key not in self.golden_llm_runs:
                    self.golden_llm_runs[key] = []
                self.golden_llm_runs[key].append(run)
            else:
                logging.error(f"run: {run.id} is not in test or golden")

        logging.info(
            f"number of test_llm_runs: {len(self.test_llm_runs)}, golden_llm_runs: {len(self.golden_llm_runs)}"
        )

    @staticmethod
    def is_llm_job(run):
        if run.name in (
            "ChatAnthropicMessages",
            "ChatOpenAI",
            "ChatBedrock",
        ) or run.name.startswith("Chat"):
            return True
        return False

    @staticmethod
    def _convert_to_hashkey(run):
        hash_elements = []
        for key in sorted(LLMInOutChecker.keys_to_check_with_default.keys()):
            try:
                # Get the value from metadata for the key, defaulting to a string 'None' if not found
                value = str(
                    run.extra["metadata"].get(
                        key, LLMInOutChecker.keys_to_check_with_default[key]
                    )
                )
            except KeyError:
                logging.error(f"run: {run.id} has no metadata: {run.extra.keys()}")
                value = "None"
            value = f"{key}={value}"
            hash_elements.append(value)

        # Join the values using a separator to ensure uniqueness
        hashkey = "|".join(hash_elements)
        return hashkey

    @staticmethod
    def _skip_checks(run):
        # skip content collection for campaign 2740 and check only plan gen
        if (
            str(run.extra["metadata"].get("orig_campaign_id", "None")) == "2740"
            and str(run.extra["metadata"].get("content_collection_plan_gen", "False"))
            == "False"
        ):
            return True
        return False

    def check_result(self):
        # check if all golden exists in test
        self._check_golden_in_test()

        def find_closest_golden_llm_runs(test_key):
            test_metadata = convert_key_to_dict(test_key)
            test_campaign_id = test_metadata.get("orig_campaign_id", "None")
            test_content_group_id = test_metadata.get("orig_content_group_id", "None")
            test_component_id = test_metadata.get("component_id", "None")

            candidates = []
            for golden_key in self.golden_llm_runs:
                golden_metadata = convert_key_to_dict(golden_key)
                golden_campaign_id = golden_metadata.get("orig_campaign_id", "None")
                golden_content_group_id = golden_metadata.get(
                    "orig_content_group_id", "None"
                )
                golden_component_id = golden_metadata.get("component_id", "None")

                if (
                    test_campaign_id == golden_campaign_id
                    and test_content_group_id == golden_content_group_id
                    and test_component_id == golden_component_id
                ):
                    candidates.append(golden_key)

            return candidates

        def process_key(key, test_runs, golden_llm_runs, check_llm_runs_func):
            if key not in golden_llm_runs:
                try:
                    golden_candidates = find_closest_golden_llm_runs(key)
                    logging.error(
                        f"missing golden for test: {key} and candidates are: {golden_candidates}"
                    )
                except Exception as e:
                    logging.error(
                        f"missing golden for test: {key} and error: {e} when finding candidates"
                    )
                return

            golden_runs = golden_llm_runs[key]
            test_run_ids = [run.id for run in test_runs]
            golden_run_ids = [run.id for run in golden_runs]

            if len(test_run_ids) != len(set(test_run_ids)):
                logging.error(f"duplicate test runs: {test_run_ids}")
            if len(golden_run_ids) != len(set(golden_run_ids)):
                logging.error(f"duplicate golden runs: {golden_run_ids}")

            def find_most_close_golden_run(test_run, golden_runs):
                test_run_inputs = str(test_run.inputs)
                first_golden_run = golden_runs[0]
                first_golden_inputs = str(first_golden_run.inputs)

                most_similar_golden_run = first_golden_run
                highest_similarity = SequenceMatcher(
                    None, test_run_inputs, first_golden_inputs
                ).ratio()

                for golden_run in golden_runs[1:]:  # Start from second run
                    golden_run_inputs = str(golden_run.inputs)
                    similarity = SequenceMatcher(
                        None, test_run_inputs, golden_run_inputs
                    ).ratio()

                    if similarity > highest_similarity:
                        highest_similarity = similarity
                        most_similar_golden_run = golden_run

                return most_similar_golden_run

            for test_run in test_runs:
                try:
                    most_close_golden_run = find_most_close_golden_run(
                        test_run, golden_runs
                    )
                except Exception as e:
                    logging.exception(
                        f"error finding most close golden run for test: {test_run.id} and error: {e}"
                    )
                    most_close_golden_run = golden_runs[0]

                if not most_close_golden_run:
                    logging.error(f"no most close golden run for test: {test_run.id}")
                    continue

                check_llm_runs_func(test_run, most_close_golden_run, key)

        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(
                    process_key,
                    key,
                    test_runs,
                    self.golden_llm_runs,
                    self._check_llm_runs,
                )
                for key, test_runs in self.test_llm_runs.items()
            ]

            for future in as_completed(futures):
                try:
                    future.result()  # Check for exceptions
                except Exception as exc:
                    logging.error(f"Generated an exception: {exc}")

        all_pass, report = self._gen_reports()
        return all_pass, report

    def _check_golden_in_test(self):
        for key, golden_runs in self.golden_llm_runs.items():
            if key not in self.test_llm_runs:
                self._reporter.add_missing_test(key, golden_runs[0])
            else:
                test_runs = self.test_llm_runs[key]
                if len(test_runs) != len(golden_runs):
                    self._reporter.add_mismatch_runs(key, test_runs, golden_runs)
        for key, test_runs in self.test_llm_runs.items():
            if key not in self.golden_llm_runs:
                self._reporter.add_missing_golden(key, test_runs[0])
                # self._append_warnings(f"Golden missing for test: {key}")

    def _check_llm_runs(self, test_run, golden_run, key):
        if not self.is_llm_job(test_run) or not self.is_llm_job(golden_run):
            logging.error(
                f"invalid llm job comparison: test {test_run.name} vs golden {golden_run.name}"
            )
            return

        metadata = convert_key_to_dict(key)
        self._reporter.init_test(test_run, golden_run, metadata)

        input_checker = LLMInputChecker(
            reporter=self._reporter,
            test_run=test_run,
            golden_run=golden_run,
        )
        input_checker.check()

        output_checker = LLMOutputChecker(
            reporter=self._reporter,
            test_run=test_run,
            golden_run=golden_run,
        )
        output_checker.check()

    def _gen_reports(self):
        succ, final_report_brief = self._reporter.final_report_page()
        logging.info(f"final_report_brief: {final_report_brief}")

        return succ, final_report_brief
