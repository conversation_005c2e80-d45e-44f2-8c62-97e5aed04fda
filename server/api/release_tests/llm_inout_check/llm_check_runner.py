import logging
import os
import time
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor, as_completed

import requests
from langsmith import Client

from ...eval import (
    benchmark_review_personalization_campaigns_small_set,
    benchmark_review_repurpose_campaigns_small_set_llm_tests,
    benchmark_review_template_campaigns_small_set,
)
from ...integrations.slack import send_slack_message
from ...models import Campaign
from ...playbook_build.doc_loader import <PERSON><PERSON>oader
from ...playbook_build.playbook_builder import PlaybookBuilder
from ...release_tests.benchmark_batch_test import BenchmarkBatchTest
from ...thread_locals import set_enable_crawl_api
from .llm_inout_check import LLMInOutChecker


class LLMInOutCheckRunner:
    def __init__(self) -> None:
        pass

    @staticmethod
    def check_env():
        if not os.environ.get("TOFU_ADMIN_TOKEN"):
            raise Exception("TOFU_ADMIN_TOKEN is not set.")

    @staticmethod
    def set_crawl_api_env():
        set_enable_crawl_api(True)

    @staticmethod
    def reset_crawl_api_env(old_env):
        set_enable_crawl_api(False)

    @staticmethod
    def get_latest_golden_tag_api():
        lastest_golden_tag_key = "latest_golden_tag"
        url = f"https://dev.api.tofuhq.com/api/data/get_cache/?key={lastest_golden_tag_key}"
        headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {os.environ.get('TOFU_ADMIN_TOKEN')}",
            "Content-Type": "application/json",
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            response_data = response.json()
            tag = response_data.get("value")
            if tag:
                return tag
        raise Exception(f"Failed to get latest golden tag: {response.text}")

    @staticmethod
    def get_latest_golden_tag():
        # first try to call API
        try:
            latest_golden_tag = LLMInOutCheckRunner.get_latest_golden_tag_api()
            return latest_golden_tag
        except Exception as e:
            logging.error(
                f"Failed to get latest golden tag: {e}, fallback to search runs."
            )

        # fallback
        # search recent runs with golden tag
        client = Client()
        golden_runs = client.list_runs(
            project_name="tofu",
            filter="and(eq(metadata_key, 'session_tag'), eq(metadata_value, 'goldenset'))",
        )
        golden_runs = list(golden_runs)

        session_ids = [run.extra["metadata"]["session_id"] for run in golden_runs]
        session_ids = [session_id for session_id in session_ids if len(session_id) > 12]

        last_golden_run = max(session_ids)
        return last_golden_run

    @staticmethod
    def set_lastest_golden_tag(tag):
        lastest_golden_tag_key = "latest_golden_tag"

        # URL for the API endpoint
        url = "https://dev.api.tofuhq.com/api/data/save_cache/"

        # Headers including Basic Auth, Content-Type, CSRF token, and Accept
        headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {os.environ.get('TOFU_ADMIN_TOKEN')}",
            "Content-Type": "application/json",
        }
        # Data payload for the POST request
        payload = {"key": lastest_golden_tag_key, "value": tag, "timeout": 168}

        # Make the POST request
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            # Parse the response data
            return
        else:
            logging.error(f"Failed to set latest golden tag: {response.text}")
        raise Exception("Failed to set latest golden tag")

    @staticmethod
    def update_golden_set():
        LLMInOutCheckRunner.check_env()

        version = int(time.time())
        session_id = f"goldenset-{version}"
        session_tag = "goldenset"

        old_env = LLMInOutCheckRunner.set_crawl_api_env()

        instance = BenchmarkBatchTest(
            quickRun=True, session_id=session_id, session_tag=session_tag
        )
        instance.tests_repurpose = (
            benchmark_review_repurpose_campaigns_small_set_llm_tests
        )
        instance.run()

        LLMInOutCheckRunner.reset_crawl_api_env(old_env)

        golden_runs = LLMInOutCheckRunner.list_tests(session_id)

        message = (
            f"Updated golden set for session {session_id} with {len(golden_runs)} runs."
        )
        try:
            LLMInOutCheckRunner.set_lastest_golden_tag(session_id)
        except Exception as e:
            raise e

        send_slack_message("#bot_tests", message)
        return session_id

    @staticmethod
    def test_with_golden_set():
        LLMInOutCheckRunner.check_env()

        version = str(uuid.uuid4())
        test_session_id = f"test-{version}"
        test_session_tag = "test"
        logging.info(f"test_session_id: {test_session_id}")

        old_env = LLMInOutCheckRunner.set_crawl_api_env()

        instance = BenchmarkBatchTest(
            quickRun=True, session_id=test_session_id, session_tag=test_session_tag
        )
        instance.tests_repurpose = (
            benchmark_review_repurpose_campaigns_small_set_llm_tests
        )
        instance.run()

        LLMInOutCheckRunner.reset_crawl_api_env(old_env)

        return LLMInOutCheckRunner.check_test_result(test_session_id)

    @staticmethod
    def check_test_result(test_tag):
        logging.info(f"Checking test result for LLM inout: {test_tag}")
        golden_tag = LLMInOutCheckRunner.get_latest_golden_tag()

        client = Client()

        test_runs = client.list_runs(
            project_name="tofu",
            filter=f"and(eq(metadata_key, 'session_id'), eq(metadata_value, '{test_tag}'))",
        )
        test_runs = list(test_runs)

        golden_runs = client.list_runs(
            project_name="tofu",
            filter=f"and(eq(metadata_key, 'session_id'), eq(metadata_value, '{golden_tag}'))",
        )
        golden_runs = list(golden_runs)

        checker = LLMInOutChecker(
            session_id=test_tag, test_runs=test_runs, golden_runs=golden_runs
        )
        return checker.check_result()

    @staticmethod
    def list_tests(session_id):

        client = Client()

        runs = client.list_runs(
            project_name="tofu",
            filter=f"and(eq(metadata_key, 'session_id'), eq(metadata_value, '{session_id}'))",
        )
        runs = list(runs)
        return runs
