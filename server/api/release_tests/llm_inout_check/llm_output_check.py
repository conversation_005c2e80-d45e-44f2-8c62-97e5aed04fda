import json
import logging
from typing import Any, Dict, List

from langsmith import Client

from ...shared_types import ContentType


# from .llm
class LLMOutputChecker:
    def __init__(self, reporter, test_run, golden_run=None) -> None:
        self.reporter = reporter
        self.test_run = test_run
        self.golden_run = golden_run

        self._resolve_metadata()

    @property
    def test_run_outputs(self):
        return self.test_run.outputs

    def _resolve_metadata(self):
        parent_run_id = self.test_run.parent_run_id
        if parent_run_id:
            client = Client()
            parent_run = client.list_runs(id=[parent_run_id])
            parent_run = list(parent_run)[0]
            self.parent_run = parent_run

            self.metadata = self.parent_run.extra["metadata"]
        else:
            self.parent_run = None
            self.metadata = {}

    def get_metadata(self, key):
        return self.metadata.get(key, "")

    @staticmethod
    def _get_generations(run: Any) -> List[str]:
        outputs = run.outputs or {}
        generations = outputs.get("generations", [])

        if not generations or not isinstance(generations, list):
            logging.error(f"No generations found for run: {run.id}")
            return []

        if isinstance(generations[0], list):
            generations = generations[0]

        results = LLMOutputChecker._extract_text_from_generations(generations)
        if not results:
            logging.error(f"No results found for generations: {generations}")
        return results

    @staticmethod
    def _extract_text_from_generations(generations: List[Dict[str, Any]]) -> List[str]:
        results = []
        for generation in generations:
            if isinstance(generation, dict):
                text = generation.get("text", "")
            elif isinstance(generation, str):
                text = generation
            else:
                text = ""

            if text:
                results.append(text)
        return results

    @property
    def test_run_generations(self):
        return self._get_generations(self.test_run)

    @property
    def golden_run_generations(self):
        return self._get_generations(self.golden_run) if self.golden_run else []

    def check(self):
        self.reporter.add_llm_output_diff(
            self.test_run,
            self.test_run.outputs,
            self.golden_run.outputs,
        )

        succ, error = self._generation_check()
        if not succ:
            if not error:
                logging.error(f"error not found")
                error = "error not found in output test"
            self.reporter.add_test_result(
                self.test_run,
                "llm_output_generation_check",
                "warning",
                error,
                "text",
            )
            return True  # not set failure for now
        else:
            self.reporter.add_test_result(
                self.test_run,
                "llm_output_generation_check",
                "info",
                "Pass: generation check",
                "text",
            )

        return True  # all are under warnings now

    def _generation_check(self):
        test_generations = self.test_run_generations
        golden_generations = self.golden_run_generations

        if not test_generations:
            return False, "test_generations is empty"

        if not golden_generations:
            return False, "golden_generations is empty"

        num_of_variations = self.get_metadata("num_of_variations") or None
        if not num_of_variations:
            logging.error(f"fail to get num_of_variations")
        elif len(test_generations) < num_of_variations:
            return False, f"test_generations length is less than {num_of_variations}"

        # check only first variations
        first_test_generation = test_generations[0]
        first_golden_generation = golden_generations[0]

        joint_generation = self.get_metadata("joint_generation")
        if (
            joint_generation == "true"
            or joint_generation == "True"
            or joint_generation == True
        ):
            return self._generation_check_joint_generation(
                first_test_generation, first_golden_generation
            )
        else:
            if len(first_test_generation) == 0:
                return False, "test generation is empty"
            if not self._length_check(first_test_generation, first_golden_generation):
                self.reporter.add_test_result(
                    self.test_run,
                    "llm_output_length_check",
                    "warning",
                    f"Fail: length mismatch: {len(first_test_generation)} vs {len(first_golden_generation)}",
                    "text",
                )
            else:
                self.reporter.add_test_result(
                    self.test_run,
                    "llm_output_length_check",
                    "info",
                    f"Pass: length match: {len(first_test_generation)} vs {len(first_golden_generation)}",
                    "text",
                )
        return True, ""

    def _generation_check_joint_generation(
        self, first_test_generation, first_golden_generation
    ):
        try:
            # TODO: move to a util function
            def str_to_map(input_str, keys):
                values = []
                start_index = 0

                for key in keys:
                    key_index = input_str.find(key + ":", start_index)
                    if key_index != -1:
                        start_index = key_index + len(key) + 1
                        next_key_index = (
                            input_str.find(keys[keys.index(key) + 1] + ":")
                            if keys.index(key) < len(keys) - 1
                            else len(input_str)
                        )
                        value = input_str[start_index:next_key_index].strip()
                        values.append(value)
                    else:
                        values.append("")

                return dict(zip(keys, values))

            content_type = self.get_metadata("content_type")
            if content_type in (
                ContentType.EmailMarketing,
                ContentType.EmailSDR,
            ):
                test_data = str_to_map(first_test_generation, ["subject line", "body"])
                golden_data = str_to_map(
                    first_golden_generation, ["subject line", "body"]
                )
            else:
                test_data = json.loads(first_test_generation)
                golden_data = json.loads(first_golden_generation)
        except json.JSONDecodeError:
            return False, "json decode error"
        except Exception as e:
            return False, f"error: {e}"

        # components check
        components = self.get_metadata("components")
        if not components:
            return False, "components is empty"

        for component in components:
            if component not in test_data:
                return False, f"component not found in test: {component}"
            if component not in golden_data:
                # shall be checked at golden update
                return False, f"component not found in golden: {component}"

            length_mismatch = []
            # length check
            if not self._length_check(test_data[component], golden_data[component]):
                length_mismatch.append(
                    (
                        component,
                        len(test_data[component]),
                        len(golden_data[component]),
                    )
                )
            if length_mismatch:
                message = ", ".join(
                    [
                        f"{component}: {test_len} vs {golden_len}"
                        for component, test_len, golden_len in length_mismatch
                    ]
                )
                self.reporter.add_test_result(
                    self.test_run,
                    "llm_output_length_check",
                    "warning",
                    f"Fail: length mismatch: {message}",
                    "text",
                )
            else:
                self.reporter.add_test_result(
                    self.test_run,
                    "llm_output_length_check",
                    "info",
                    f"Pass: length match",
                    "text",
                )
        return True, ""

    def _length_check(self, test_component_generation, golden_component_generation):
        len_test = len(test_component_generation)
        len_golden = len(golden_component_generation)

        if len_golden == 0:
            # shall check for golden update
            logging.error(f"golden_component_generation is empty")
            return False

        if len_golden == 1:
            return len_test <= 3
        # upper bound
        if len_test > len_golden * 2.5:
            return False
        if len_test < len_golden * 0.8:
            return False
        return True
