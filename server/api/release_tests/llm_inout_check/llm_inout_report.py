import copy
import logging

from .llm_inout_report_html_util import (
    dump_llm_input_messages,
    dump_llm_output_diff,
    dump_report,
)


def convert_key_to_dict(key):
    metadata = {}
    for pair in key.split("|"):
        k, v = pair.split("=")
        metadata[k] = v
    return metadata


def convert_other_error(other_error_value, run_id, run_type):
    record = convert_key_to_dict(other_error_value)
    record[run_type] = run_id
    return record


class LLMReporter:
    base_columns = [
        "severity_level",
        "failed_cases",
        "llm_input_exact_match",
        "llm_input_fuzzy_match",
        "llm_output_generation_check",
        "llm_output_length_check",
    ]
    metadata_columns = [
        "test_run_id",
        "golden_run_id",
        "llm_input_comparison",
        "llm_output_comparison",
        "content_type",
        "campaign_goal",
        "is_campaign_v3",
        "orig_campaign_id",
        "orig_content_group_id",
        "targets",
        "foundation_model",
        "enable_custom",
        "template_generation",
        "content_collection_plan_gen",
        "component_id",
    ]

    def __init__(self, session_id) -> None:
        self.session_id = session_id
        self.tests = {}
        self.other_errors = {}

    @property
    def render_columns(self):
        return self.metadata_columns[:4] + self.base_columns + self.metadata_columns[4:]

    def init_test(self, test_run, golden_run, metadata):
        test_data = {
            "meta": {
                "test_run_id": test_run.id,
                "golden_run_id": golden_run.id,
            },
            "tests": {},
        }
        for meta_key in self.metadata_columns:
            if meta_key in metadata:
                test_data["meta"][meta_key] = metadata[meta_key]
        self.tests[test_run.id] = test_data

    def add_llm_input_diff(
        self,
        test_run,
        test_run_messages,
        golden_run_messages,
    ):
        if test_run.id not in self.tests:
            logging.error(
                f"test_run_id: {test_run.id} is not initialized in LLMReporter"
            )
            return

        metadata = self.tests[test_run.id]["meta"]

        target_file = (
            f"llm_inouts_diff/testrun_{self.session_id}/inputs/{test_run.id}.html"
        )

        report = dump_llm_input_messages(
            test_run_messages, golden_run_messages, metadata, target_file
        )

        self.tests[test_run.id]["meta"]["llm_input_comparison"] = {
            "type": "link",
            "value": report,
        }

    def add_llm_output_diff(self, test_run, test_run_output, golden_run_output):
        if test_run.id not in self.tests:
            logging.error(
                f"test_run_id: {test_run.id} is not initialized in LLMReporter"
            )
            return

        metadata = self.tests[test_run.id]["meta"]
        target_file = (
            f"llm_inouts_diff/testrun_{self.session_id}/outputs/{test_run.id}.html"
        )
        report = dump_llm_output_diff(
            test_run_output, golden_run_output, metadata, target_file
        )

        self.tests[test_run.id]["meta"]["llm_output_comparison"] = {
            "type": "link",
            "value": report,
        }

    def add_test_result(
        self, test_run, test_name, severity_level, test_report, test_report_type
    ):
        if test_run.id not in self.tests:
            logging.error(
                f"test_run_id: {test_run.id} is not initialized in LLMReporter"
            )
            return

        self.tests[test_run.id]["tests"][test_name] = {
            "severity_level": severity_level,
            "value": test_report,
            "type": test_report_type,
        }

    def add_missing_test(self, key, gold_run):
        if "missing_test" not in self.other_errors:
            self.other_errors["missing_test"] = []
        record = convert_key_to_dict(key)
        record["golden_run_id"] = gold_run.id
        self.other_errors["missing_test"].append(record)

    def add_missing_golden(self, key, test_run):
        if "missing_golden" not in self.other_errors:
            self.other_errors["missing_golden"] = []
        record = convert_key_to_dict(key)
        record["test_run_id"] = test_run.id
        self.other_errors["missing_golden"].append(record)

    def add_mismatch_runs(self, key, test_runs, golden_runs):
        return
        if "mismatch_runs" not in self.other_errors:
            self.other_errors["mismatch_runs"] = []
        self.other_errors["mismatch_runs"].append(
            f"{key}: test {len(test_runs)} vs golden {len(golden_runs)}"
        )

    def final_report_page(self):
        records_errors = []
        records_warnings = []
        records_info = []

        for test_id, test_data in self.tests.items():
            tests = test_data["tests"]

            top_severity = "info"
            error_cases = []
            warning_cases = []
            for test_name, test_result in tests.items():
                severity_level = test_result["severity_level"]
                if severity_level == "error":
                    # add test_data to records
                    top_severity = "error"
                    error_cases.append(test_name)
                    break
                elif severity_level == "warning":
                    warning_cases.append(test_name)
                    top_severity = "warning" if top_severity != "error" else "error"

            record = {
                "severity_level": f"{top_severity}",
                "failed_cases": f"{error_cases if error_cases else warning_cases}",
            }

            for key in self.metadata_columns:
                if key in test_data["meta"]:
                    record[key] = test_data["meta"][key]

            for key in self.base_columns:
                if key in tests:
                    record[key] = tests[key]

            record.update(
                {
                    key: test_data["meta"].get(key, "None")
                    for key in self.metadata_columns
                }
            )
            if top_severity == "error":
                records_errors.append(record)
            elif top_severity == "warning":
                records_warnings.append(record)
            else:
                records_info.append(record)

        columns = self.render_columns

        final_report_file = f"llm_inouts_diff/testrun_{self.session_id}/llm_report.html"

        # validation check
        report_link = dump_report(
            columns=columns,
            report_data={
                "error": records_errors,
                "warning": records_warnings,
                "info": records_info,
            },
            other_errors=self.other_errors,
            report_file=final_report_file,
        )

        other_errors = []
        for key, value in self.other_errors.items():
            if key == "missing_test":
                other_errors.append(f"Missing test: {len(value)}")
            elif key == "missing_golden":
                other_errors.append(f"Missing golden: {len(value)}")
            elif key == "mismatch_runs":
                other_errors.append(f"Mismatch runs: {len(value)}")
            else:
                other_errors.append(f"Unknown error {key}: {len(value)}")
        other_errors = "\n".join(other_errors)
        if other_errors:
            other_errors = f"{other_errors}\n"

        brief = f"Error cases: {len(records_errors)}, Warning cases: {len(records_warnings)}\n{other_errors}Link to the report: {report_link}"
        return len(records_errors) == 0, brief
