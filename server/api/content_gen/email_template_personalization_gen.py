import copy
import json
import logging
import time

from ..utils import fix_claude_outputs, strip_for_json
from .base_content_generator import BaseContentGenerator
from .component_utils import create_predefined_components
from .email_template_input_builder import EmailTemplateInputBuilder


class EmailTemplatePersonalizationGenerator(BaseContentGenerator):

    def __init__(self, gen_env):

        super().__init__(
            gen_env=gen_env,
        )

    def gen(self):
        self.validate()

        components = self._build_components()
        self.email_subject_key = None
        self.email_body_key = None
        if not components:
            raise Exception(
                f"components should not be empty for content {self.content_instance.id}"
            )

        variations = copy.deepcopy(components)
        keys = list(components.keys())
        # component should have email_subject and email_body.
        for key, component in components.items():
            if component.get("meta", {}).get("isEmailSubject", False):
                self.email_subject_key = key
            else:
                self.email_body_key = key
        if not (self.email_body_key and self.email_subject_key):
            raise Exception(
                f"email components should have subject and body. Found: {components}"
            )

        logging.info(f"Start generation with model {self.foundation_model}...")

        start_time = time.perf_counter()
        email_template_input_builder = EmailTemplateInputBuilder(
            model_budget=self.model_budget,
            gen_env=self._gen_env,
        )
        llm_inputs = email_template_input_builder.create_llm_inputs()

        generations = self.get_results(llm_inputs)

        end_time = time.perf_counter()
        runtime = end_time - start_time
        logging.info(f"Generation time: {runtime}")

        start_time_postprocess = time.perf_counter()
        outputs = [generation.text for generation in generations]

        generated_variations = {self.email_body_key: [], self.email_subject_key: []}
        for output in outputs:
            # check the output for json format {"email_subject": "subject", "email_body": "body"}
            try:
                try:
                    json_object = json.loads(strip_for_json(output))
                except Exception as e:
                    output = strip_for_json(output)
                    output = output.replace("\n", "\\n")
                    json_object = json.loads(output)
            except Exception as e:
                logging.error(f"Error parsing output to json: {e}")
                return None

            if "email_subject" not in json_object or "email_body" not in json_object:
                logging.error("Output does not contain email_subject or email_body")
                return None
            generated_variations[self.email_subject_key].append(
                json_object["email_subject"]
            )
            generated_variations[self.email_body_key].append(json_object["email_body"])

        end_time_postprocess = time.perf_counter()
        postprocess_runtime = end_time_postprocess - start_time_postprocess
        total_runtime = runtime + postprocess_runtime

        for i in range(len(keys)):
            component_id = keys[i]
            component_variations = [
                {
                    "text": text,
                    "meta": {
                        "runtime_seconds": total_runtime,
                        "gen_runtime": runtime,
                        "postprocess_runtime": postprocess_runtime,
                        "request_id": self._gen_env._gen_settings.request_id,
                    },
                }
                for text in generated_variations[component_id]
            ]
            variations[component_id]["meta"]["variations"] = component_variations
            variations[component_id]["meta"]["current_variation_index"] = 0
            variations[component_id]["meta"]["current_version"] = {
                "text": component_variations[0]["text"],
                "request_id": component_variations[0]["meta"]["request_id"],
            }

        return variations

    def _build_components(self):
        components = create_predefined_components(self._data_wrapper)
        self._gen_settings.update_components(components)
        return components

    def validate(self):
        if (
            self._gen_settings.is_template_personalization
            and not self._data_wrapper.personalization_template
        ):
            raise Exception(
                f"Personalization template is not set for content {self.content_instance.id}"
            )
