import logging

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..langsmith_integration import BaseTracableClass
from ..logger import log_content_gen
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..models import ContentVariation
from ..task_registry import GenerationGoal
from ..utils import user_has_internal_features
from ..validation_utils import check_valid_url_anchor_contents
from ..validator.campaign_validator import CampaignGoal
from .component_utils import create_predefined_components


class BaseContentGenerator(DataWrapperHolder, BaseTracableClass):
    def __init__(
        self,
        gen_env,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        BaseTracableClass.__init__(self)
        self.generation_goal = (
            GenerationGoal.GENERATION_REPURPOSING
            if self.campaign_goal == "Repurpose Content"
            else GenerationGoal.GENERATION
        )
        self.model_config = ModelConfigResolver.resolve(
            self.generation_goal,
            foundation_model=self.foundation_model,
            n=self.num_of_variations,
        )
        self.model_budget = self.model_config.model_budget
        self.model_caller = ModelCaller(self.model_config)
        if self.content_group_instance:
            self.prev_gen_variations = self.get_prev_gen_variations()

        self.user = self.playbook_instance.users.first()

    def get_results(self, llm_inputs, json_output=None):
        if json_output is None:
            json_output = self.is_json_output

        return self.model_caller.get_results_with_fallback(
            llm_inputs, json_output=json_output
        )

    def get_prev_gen_variations(self):
        components = self.content_group_instance.components
        if not components:
            return {}

        components = {
            component_id: component
            for component_id, component in components.items()
            if component.get("meta", {}).get("type", "text") == "text"
        }

        # we assume there is only one variation object per content instance
        if not self.content_instance:
            return {}
        content_variations = ContentVariation.objects.filter(
            content=self.content_instance.id
        ).first()
        if not content_variations:
            return {}
        else:
            content_variations = content_variations.variations

        variation_dict = {}
        for component_id in components.keys():
            orig_text = components[component_id]["text"]
            current_variation_index = (
                content_variations.get(component_id, {})
                .get("meta", {})
                .get("current_variation_index", 0)
            )
            text_variation = (
                content_variations.get(component_id, {})
                .get("meta", {})
                .get("variations", [{}])[current_variation_index]
                .get("text", "")
            )
            if text_variation:
                variation_dict[component_id] = {
                    "orig_text": orig_text,
                    "text_variation": text_variation,
                }
        return variation_dict

    def update_prev_gen_variations(self, component_id, text_variation):
        if component_id not in self.content_group_instance.components:
            return
        self.prev_gen_variations[component_id] = {
            "orig_text": self.content_group_instance.components[component_id]["text"],
            "text_variation": text_variation,
        }

    def save_components(self, components):
        self.content_group_instance.components = components
        self.content_group_instance.save()

    def validate(self):
        # Check all assets are valid
        check_valid_url_anchor_contents(self._data_wrapper)

    def _build_components(self, components=None):
        if (
            self._data_wrapper.content_goal == CampaignGoal.Repurposing
            or self._data_wrapper.content_goal == CampaignGoal.SeqPersonalizeTemplate
        ) and not components:
            components = create_predefined_components(self._data_wrapper)
            self.save_components(components)

        if not components:
            content_id = (
                self.content_instance.id if self.content_instance else "unknown"
            )
            raise Exception(f"Components should not be empty for content {content_id}")
        self._gen_settings.update_components(components)
        return components

    def _sort_component_keys(self, components):
        keys = list(components.keys())
        keys.sort(
            key=lambda x: (
                not components[x].get("meta", {}).get("isEmailSubject", False),
                components[x].get("meta", {}).get("order", 0),
                components[x].get("meta", {}).get("time_added", 0),
            )
        )
        return keys

    def _log_generation_stats(self, postprocessor):
        total_gen_count = postprocessor.total_gen_count
        rewrite_count = postprocessor.rewrite_count
        logging.info(f"Total generations: {total_gen_count}")
        logging.info(f"Total rewrites: {rewrite_count}")
        if total_gen_count < 1:
            logging.error("No generations found")
            return
        if rewrite_count / total_gen_count > 0.2:
            logging.warning(
                f"Rewrites too high. Total rewrites: {rewrite_count}, Total generations: {total_gen_count}"
            )
        log_content_gen(content=self.content_instance)
