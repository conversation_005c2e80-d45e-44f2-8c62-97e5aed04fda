from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..feature.feature_assembler.generate_feature_assembler import (
    GenerateFeatureAssembler,
    TemplateGenerationFeatureAssembler,
)
from ..prompt.prompt_assembler.email_template_personalize_assembler import (
    EmailTemplatePersonalizeAssembler,
)


class EmailTemplateInputBuilder(DataWrapperHolder):

    def __init__(
        self,
        model_budget,
        gen_env=None,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.model_budget = model_budget

    def create_llm_inputs(self):
        generate_feature_assembler = GenerateFeatureAssembler(
            model_budget=self.model_budget, gen_env=self._gen_env
        )
        component_feature_assembler = TemplateGenerationFeatureAssembler(
            gen_env=self._gen_env, model_budget=self.model_budget
        )
        all_features = component_feature_assembler.build()
        all_features.update(generate_feature_assembler.build())

        self._features = all_features

        prompt_assembler = EmailTemplatePersonalizeAssembler(gen_env=self._gen_env)
        return prompt_assembler.build(all_features)
