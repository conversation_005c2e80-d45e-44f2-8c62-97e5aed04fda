import copy
import logging

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..feature.feature_assembler.generate_feature_assembler import (
    ContentCollectionFeatureAssembler,
)
from ..prompt.prompt_assembler.generate_content_collection_plan_prompt_assembler import (
    GenerateContentCollectionPlanAssembler,
)


class ContentCollectionPlanInputBuilder(DataWrapperHolder):

    def __init__(
        self,
        playbook_handler,
        content_collection,
        model_budget=8000,
        gen_env=None,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.playbook_handler = playbook_handler
        self.content_collection = content_collection
        self.model_budget = model_budget

    def create_llm_inputs(
        self,
    ):

        content_collection_feature_assembler = ContentCollectionFeatureAssembler(
            self.playbook_handler,
            self._gen_env,
            self.content_collection,
            model_budget=self.model_budget,
        )

        self.all_features = content_collection_feature_assembler.build()

        prompt_assembler = GenerateContentCollectionPlanAssembler(
            gen_env=self._gen_env,
        )

        return prompt_assembler.build(self.all_features)
