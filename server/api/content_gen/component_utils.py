import time

import nanoid

from ..shared_types import ContentType


def create_predefined_components(data_wrapper):
    if data_wrapper is None:
        raise ValueError("data_wrapper cannot be None")

    if not hasattr(data_wrapper, "content_type"):
        raise AttributeError("data_wrapper must have a content_type attribute")

    if data_wrapper.content_type in (
        ContentType.EmailMarketing,
        ContentType.EmailSDR,
    ):
        return create_email_components()
    elif getattr(data_wrapper, "is_linkedin_ads_v2", False) or getattr(
        data_wrapper, "is_linkedin_ads", False
    ):
        return create_linkedin_ads_components()
    elif data_wrapper.content_type not in ContentType.__members__.values():
        raise ValueError(f"Unexpected content type: {data_wrapper.content_type}")
    else:
        return create_generic_component()


def create_email_components(
    body_text="Repurpose Content", subject_text="Email Subject"
):
    components = {}
    body_uuid = str(nanoid.generate(size=16))
    components[body_uuid] = {
        "text": body_text,
        "meta": {
            "precedingContent": "",
            "succeedingContent": "",
            "time_added": int(time.time()),
            "isEmailSubject": False,
            "component_type": "email body",
        },
    }
    subject_uuid = str(nanoid.generate(size=16))
    components[subject_uuid] = {
        "text": subject_text,
        "meta": {
            "precedingContent": "",
            "succeedingContent": "",
            "time_added": int(time.time()),
            "isEmailSubject": True,
            "component_type": "email subject",
        },
    }
    return components


def create_linkedin_ads_components():
    components = {}
    for order, key in enumerate(
        ["introductory-text", "headline", "description", "ad-copy"]
    ):
        uuid_str = str(nanoid.generate(size=16))
        components[uuid_str] = {
            "text": key,
            "meta": {
                "precedingContent": "",
                "succeedingContent": "",
                "time_added": int(time.time()),
                "template_field_name": key,
                "component_type": key,
                "order": order,
            },
        }
    return components


def create_generic_component(text="Repurpose Content"):
    uuid_str = str(nanoid.generate(size=16))
    return {
        uuid_str: {
            "text": text,
            "meta": {
                "precedingContent": "",
                "succeedingContent": "",
                "time_added": int(time.time()),
            },
        }
    }
