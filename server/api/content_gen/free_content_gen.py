import copy
import logging
import time

from django.core.cache import cache
from django.core.exceptions import ValidationError

from ..content_gen.base_content_generator import BaseContentGenerator
from ..feature.feature_assembler.free_gen_feature_assembler import (
    FreeGenFeatureAssembler,
)
from ..logger import log_content_gen
from ..utils import fix_claude_outputs
from .free_gen_message_builder import FreeGenMessageBuilder


class FreeContentGenerator(BaseContentGenerator):
    def __init__(
        self,
        gen_env,
    ):
        super().__init__(gen_env=gen_env)

    def gen(self):
        components = self.content_gen_components
        if not components or len(components) != 1:
            raise ValidationError(
                f"components should have exactly one component for content {self.content_instance.id}"
            )
        component = list(components.values())[0]
        component_id = list(components.keys())[0]

        self.gen_feature_assembler = FreeGenFeatureAssembler(
            gen_env=self._gen_env,
            component=component,
        )
        self.gen_feature_assembler.build()

        message_input_builder = FreeGenMessageBuilder(
            gen_env=self._gen_env,
            gen_feature_assembler=self.gen_feature_assembler,
        )

        start_time = time.perf_counter()

        llm_inputs = message_input_builder.create_llm_inputs()

        generations = self.get_results(llm_inputs)

        end_time = time.perf_counter()
        runtime = end_time - start_time
        logging.info(f"Generation time: {runtime}")

        outputs = [generation.text for generation in generations]
        start_time_postprocess = time.perf_counter()
        all_generated_variations = self.postprocess_variation(
            components,
            outputs,
        )
        end_time_postprocess = time.perf_counter()
        postprocess_runtime = end_time_postprocess - start_time_postprocess
        total_runtime = runtime + postprocess_runtime

        variations = copy.deepcopy(components)

        keys = components.keys()
        for component_id in keys:
            generated_variations = all_generated_variations[component_id]
            generated_variations = [
                {
                    "text": text,
                    "meta": {
                        "runtime_seconds": total_runtime,
                        "gen_runtime": runtime,
                        "postprocess_runtime": postprocess_runtime,
                        "request_id": self._gen_env._gen_settings.request_id,
                    },
                }
                for text in generated_variations
            ]
            variations[component_id]["meta"]["variations"] = generated_variations
            variations[component_id]["meta"]["current_variation_index"] = 0
            variations[component_id]["meta"]["current_version"] = {
                "text": generated_variations[0]["text"],
                "request_id": generated_variations[0]["meta"]["request_id"],
            }

        log_content_gen(content=self.content_instance)
        return variations

    def postprocess_variation(self, components, outputs):
        all_generated_variations = {}
        for component_id, component in components.items():
            all_generated_variations[component_id] = outputs
        return all_generated_variations
