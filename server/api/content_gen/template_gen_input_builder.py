import logging

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..feature.feature_assembler.generate_feature_assembler import (
    TemplateGenerationFeatureAssembler,
)
from ..logger import log_data_wrapper_mismatch
from ..prompt.prompt_assembler.generate_template_prompt_assembler import (
    GenerateTemplatePromptAssembler,
)


class TemplateGenerationInputBuilder(DataWrapperHolder):
    def __init__(
        self,
        gen_feature_assembler=None,
        model_budget=8000,
        gen_env=None,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.gen_feature_assembler = gen_feature_assembler
        self.model_budget = model_budget

    def create_llm_inputs(self):
        if self.gen_feature_assembler is None:
            logging.error(f"gen_feature_assembler is None.")
            return []

        component_feature_assembler = TemplateGenerationFeatureAssembler(
            gen_env=self._gen_env,
            model_budget=self.model_budget,
        )
        all_features = component_feature_assembler.build()
        all_features.update(self.gen_feature_assembler.get_features())

        prompt_assembler = GenerateTemplatePromptAssembler(
            gen_env=self._gen_env,
        )
        return prompt_assembler.build(all_features)
