import json
import logging
import re
import traceback

import sentry_sdk

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..logger import log_data_wrapper_mismatch
from ..utils import fuzzy_match_json_key, strip_for_json


class JointGenerationPostprocessor(DataWrapperHolder):
    def __init__(self, gen_env):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.total_gen_count = 0
        self.rewrite_count = 0
        self._generation_output_pattern = (
            r"(?<=<generation>)\s*(.*?)\s*(?=</generation>)"
        )
        try:
            pass
        except Exception as e:
            logging.error(f"Error when checking wrapper equivalence: {e}")

    @staticmethod
    def get_postprocessor(gen_env):
        if gen_env._data_wrapper.is_email:
            return EmailJointGenerationPostprocessor(gen_env)
        elif gen_env._data_wrapper.is_linkedin_ads_v2:
            return LinkedInAdsJointGenerationPostprocessor(gen_env)
        else:
            return JointGenerationPostprocessor(gen_env)

    def postprocess_joint(
        self,
        components,
        outputs,
    ):
        if self.campaign_goal == "Repurpose Content":
            return self.postprocess_joint_repurpose(components, outputs)
        else:
            return self.postprocess_joint_personalization(components, outputs)

    def check_generations_valid(self, generations):
        logging.error(
            f"Unsupported combinations for: {self._data_wrapper.content_type} {self.campaign_goal}"
        )
        return True

    def postprocess_joint_repurpose(self, components, outputs):
        logging.error(
            f"Unsupported combinations for: {self._data_wrapper.content_type} {self.campaign_goal}"
        )
        return {}

    def postprocess_joint_personalization(
        self,
        components,
        outputs,
    ):
        keys = list(components.keys())
        generated_variations = {}
        for output in outputs:
            try:
                try:
                    json_object = json.loads(strip_for_json(output))
                except Exception as e:
                    output = strip_for_json(output)
                    output = output.replace("\n", "\\n")
                    json_object = json.loads(output)

                for key in keys:
                    json_key = None
                    if key not in json_object:
                        json_key = fuzzy_match_json_key(key, json_object)
                        if json_key:
                            logging.warning(
                                f"generation does not contain key {key}. Using closest key {json_key}"
                            )
                        else:
                            logging.warning(
                                f"generation does not contain key {key}: {output}"
                            )
                            sentry_sdk.metrics.distribution(
                                "json_missing_key",
                                value=1,
                                tags={
                                    "foundation_model": self.foundation_model,
                                    "campaign_id": self.campaign_instance.id,
                                    "content_id": self.content_instance.id,
                                    "user": self.playbook_instance.users.first(),
                                },
                            )
                            continue
                    else:
                        sentry_sdk.metrics.distribution(
                            "json_missing_key",
                            value=0,
                            tags={
                                "foundation_model": self.foundation_model,
                                "campaign_id": self.campaign_instance.id,
                                "content_id": self.content_instance.id,
                                "user": self.playbook_instance.users.first(),
                            },
                        )
                        json_key = key
                    if not key in generated_variations:
                        generated_variations[key] = []
                    generated_text = json_object[json_key].get("text", "")
                    if not generated_text:
                        logging.error(
                            f"generation does not contain text for key {key}: {output}"
                        )
                    else:
                        generated_variations[key].append(generated_text)
                    self.total_gen_count += 1

            except Exception as e:
                logging.error(
                    f"In joint generation is not a valid json: {output} with error: {e}\n{traceback.format_exc()}"
                )
                return generated_variations

        return generated_variations


class EmailJointGenerationPostprocessor(JointGenerationPostprocessor):
    def __init__(self, gen_env):
        super().__init__(gen_env)
        self._subject_line_pattern = r"(?<=<subject>)\s*(.*?)\s*(?=</subject>)"
        self._body_pattern = r"(?<=<body>)\s*(.*?)\s*(?=</body>)"

    def postprocess_joint_repurpose(self, components, outputs):
        generated_variations = {}
        keys = list(components.keys())
        subject_key = None
        body_key = None
        for key in keys:
            if components[key]["meta"]["component_type"] == "email subject":
                subject_key = key
            elif components[key]["meta"]["component_type"] == "email body":
                body_key = key

        for output in outputs:
            self.total_gen_count += 1
            generation_output_match = re.search(
                self._generation_output_pattern, output, re.DOTALL | re.IGNORECASE
            )
            if not generation_output_match:
                logging.error(f"generation does not contain generation: {output}")
                continue
            generation_output = generation_output_match.group(1)

            subject_line_match = re.search(
                self._subject_line_pattern, generation_output, re.DOTALL | re.IGNORECASE
            )
            body_match = re.search(
                self._body_pattern, generation_output, re.DOTALL | re.IGNORECASE
            )

            subject_line = subject_line_match.group(1) if subject_line_match else None
            body = body_match.group(1) if body_match else None
            if subject_line and subject_key:
                if not subject_key in generated_variations:
                    generated_variations[subject_key] = []
                generated_variations[subject_key].append(subject_line)
            if body and body_key:
                if not body_key in generated_variations:
                    generated_variations[body_key] = []
                generated_variations[body_key].append(body)
            if not subject_line_match:
                logging.error(f"generation does not contain subject line: {output}")
            if not body_match:
                logging.error(f"generation does not contain body: {output}")

            if not body_match or not subject_line_match:
                raise Exception(
                    f"Failed to extract subject line or body from generation: {output}"
                )

        return generated_variations

    def check_generations_valid(self, generations):
        outputs = [generation.text for generation in generations]
        for output in outputs:
            subject_line_match = re.search(
                self._subject_line_pattern, output, re.DOTALL | re.IGNORECASE
            )
            body_match = re.search(
                self._body_pattern, output, re.DOTALL | re.IGNORECASE
            )
            if not subject_line_match or not body_match:
                return False
        return True


class LinkedInAdsJointGenerationPostprocessor(JointGenerationPostprocessor):
    def __init__(self, gen_env):
        super().__init__(gen_env)

        self._introductory_pattern = (
            r"(?<=<introductory-text>)\s*(.*?)\s*(?=</introductory-text>)"
        )
        self._headline_pattern = r"(?<=<headline>)\s*(.*?)\s*(?=</headline>)"
        self._description_pattern = r"(?<=<description>)\s*(.*?)\s*(?=</description>)"
        self._ad_copy_pattern = r"(?<=<ad-copy>)\s*(.*?)\s*(?=</ad-copy>)"

    def postprocess_joint_repurpose(self, components, outputs):
        generated_variations = {}
        keys = list(components.keys())
        introductory_key = None
        headline_key = None
        description_key = None
        ad_copy_key = None

        for key in keys:
            if components[key]["meta"]["component_type"] == "introductory-text":
                introductory_key = key
            elif components[key]["meta"]["component_type"] == "headline":
                headline_key = key
            elif components[key]["meta"]["component_type"] == "description":
                description_key = key
            elif components[key]["meta"]["component_type"] == "ad-copy":
                ad_copy_key = key

        for output in outputs:
            self.total_gen_count += 1
            generation_output_match = re.search(
                self._generation_output_pattern, output, re.DOTALL | re.IGNORECASE
            )
            if not generation_output_match:
                logging.error(f"generation does not contain generation: {output}")
                continue
            generation_output = generation_output_match.group(1)

            introductory_match = re.search(
                self._introductory_pattern, generation_output, re.DOTALL | re.IGNORECASE
            )
            headline_match = re.search(
                self._headline_pattern, generation_output, re.DOTALL | re.IGNORECASE
            )
            description_match = re.search(
                self._description_pattern, generation_output, re.DOTALL | re.IGNORECASE
            )
            ad_copy_match = re.search(
                self._ad_copy_pattern, generation_output, re.DOTALL | re.IGNORECASE
            )

            introductory = introductory_match.group(1) if introductory_match else None
            headline = headline_match.group(1) if headline_match else None
            description = description_match.group(1) if description_match else None
            ad_copy = ad_copy_match.group(1) if ad_copy_match else None
            if introductory and introductory_key:
                if not introductory_key in generated_variations:
                    generated_variations[introductory_key] = []
                generated_variations[introductory_key].append(introductory)
            if headline and headline_key:
                if not headline_key in generated_variations:
                    generated_variations[headline_key] = []
                generated_variations[headline_key].append(headline)
            if description_key:  # description is optional
                if not description:
                    description = ""
                if not description_key in generated_variations:
                    generated_variations[description_key] = []
                generated_variations[description_key].append(description)
            if ad_copy and ad_copy_key:
                if not ad_copy_key in generated_variations:
                    generated_variations[ad_copy_key] = []
                generated_variations[ad_copy_key].append(ad_copy)

            if not introductory_match:
                logging.error(
                    f"generation does not contain introductory-text: {output}"
                )
            if not headline_match:
                logging.error(f"generation does not contain headline: {output}")
            if not description_match:
                logging.error(f"generation does not contain description: {output}")
            if not ad_copy_match:
                logging.error(f"generation does not contain ad-copy: {output}")

            if not introductory_match or not headline_match:
                raise Exception(
                    f"Failed to extract introductory-text, headline or description from generation: {output}"
                )

        return generated_variations

    def check_generations_valid(self, generations):
        outputs = [generation.text for generation in generations]

        for output in outputs:
            introductory_match = re.search(
                self._introductory_pattern, output, re.DOTALL | re.IGNORECASE
            )
            headline_match = re.search(
                self._headline_pattern, output, re.DOTALL | re.IGNORECASE
            )
            description_match = re.search(
                self._description_pattern, output, re.DOTALL | re.IGNORECASE
            )
            ad_copy_match = re.search(
                self._ad_copy_pattern, output, re.DOTALL | re.IGNORECASE
            )
            if (
                not introductory_match
                or not headline_match
                # or not description_match  # description is optional
                or not ad_copy_match
            ):
                return False
        return True
