import asyncio
import contextvars
import logging
import os
import re
from typing import Dict, List

from bs4 import BeautifulSoup
from langchain_core.messages import HumanMessage

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..langsmith_integration import BaseTracableClass, dynamic_traceable, traceable
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..prompt.prompt_library.instruct_prompt.brand_guideline_rewriting_instruct import (
    BRAND_GUIDELINE_CUSTOM_INSTRUCTIONS,
    BRAND_GUIDELINE_INSTRUCT,
    BRAND_GUIDELINE_PROMPT,
    PREV_GEN_CONTENT_PROMPT,
)


class BrandGuidelineRewriter(DataWrapperHolder, BaseTracableClass):
    def __init__(
        self,
        gen_env,
        model_caller: ModelCaller,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        BaseTracableClass.__init__(self)
        self.model_caller = model_caller
        self.generation_goal = self.model_caller.model_config.generation_goal
        foundation_model = self.model_caller.model_config.model_params_list[
            0
        ].model_name
        brand_guideline_model_config = ModelConfigResolver.resolve(
            self.generation_goal,
            foundation_model=foundation_model,
            n=1,
        )
        self.brand_guideline_model_caller = ModelCaller(brand_guideline_model_config)

    def get_metadata(self):
        return {
            "content_id": self.content_instance.id if self.content_instance else None,
            "campaign_id": (
                self.campaign_instance.id if self.campaign_instance else None
            ),
            "content_type": self.content_type,
            "campaign_goal": self.campaign_goal,
            "environment": os.environ.get("TOFU_ENV", default="unknown"),
            "foundation_model": self.foundation_model,
            "generation_goal": self.generation_goal,
        }

    def rewrite_with_brand_guideline(
        self,
        generations: List[str],
        brand_guideline_text: str,
        features: Dict[str, str] = None,
    ):
        try:
            # Try to get existing event loop first
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                # If no event loop exists or it's closed, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                should_close_loop = True
            else:
                should_close_loop = False

            # Create a list of tasks for parallel processing
            tasks = []
            for generated_content in generations:
                messages = self.get_messages_for_brand_guideline_rewriting(
                    generated_content, brand_guideline_text, features
                )

                # Create async task for each generation
                task = loop.create_task(
                    self._process_single_generation_async(
                        self.brand_guideline_model_caller,
                        messages,
                        generated_content,
                    )
                )
                tasks.append(task)

            try:
                rewritten_outputs = loop.run_until_complete(asyncio.gather(*tasks))
            finally:
                if should_close_loop:
                    loop.close()

            return rewritten_outputs
        except Exception as e:
            logging.exception(
                f"Error in async brand guideline rewriting, falling back to sync mode: {e}"
            )
            return self._sync_rewrite_with_brand_guideline(
                generations, brand_guideline_text, features
            )

    def _sync_rewrite_with_brand_guideline(
        self,
        generations: List[str],
        brand_guideline_text: str,
        features: Dict[str, str] = None,
    ):
        rewritten_outputs = []
        for generated_content in generations:
            try:
                messages = self.get_messages_for_brand_guideline_rewriting(
                    generated_content, brand_guideline_text, features
                )
                rewritten_content = self._process_single_generation(
                    self.brand_guideline_model_caller, messages, generated_content
                )
                rewritten_outputs.append(rewritten_content)
            except Exception as e:
                logging.error(f"Failed to process generation in sync mode: {e}")
                rewritten_outputs.append(generated_content)
        return rewritten_outputs

    def _process_single_generation(
        self,
        model_caller: ModelCaller,
        messages: List[HumanMessage],
        original_content: str,
    ) -> str:
        try:
            response = model_caller.get_results_with_fallback(messages)
            if not isinstance(response, list):
                raise Exception("Response is not a list")
            if not response:
                raise Exception("Response is empty")
            text_response = response[0].text
            if not text_response:
                raise Exception("Generated text is empty")
            return self.parse_text_response_content(text_response)
        except Exception as e:
            logging.error(f"Failed to process generation: {e}")
            return original_content

    def parse_text_response_content(self, text_response: str) -> str:
        # we expect the text response to be in the format of:
        # <thinking>...</thinking> <content>...</content>
        # we want to return the content part
        match = re.search(r"<content>(.*?)</content>", text_response, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            raise Exception(f"No <content> tag found in text response: {text_response}")

    def get_messages_for_brand_guideline_rewriting(
        self, generated_content, brand_guideline_text, features
    ):
        if not isinstance(generated_content, str) or not isinstance(
            brand_guideline_text, str
        ):
            raise TypeError(
                "Both generated_content and brand_guideline_text must be strings"
            )

        MAX_BUDGET = self.model_caller.model_config.model_budget
        max_words = MAX_BUDGET * 0.75  # about 0.75 words per token
        # Sanitize inputs to prevent format string injection
        generated_content = self._sanitize_input(generated_content)
        brand_guideline_text = self._sanitize_input(brand_guideline_text)

        generated_content_length = len(generated_content.split())
        brand_guideline_length = len(brand_guideline_text.split())
        if generated_content_length > max_words:
            raise ValueError(
                f"Generated content is too long. Max words: {max_words}, Actual: {generated_content_length}"
            )

        if brand_guideline_length + 2 * generated_content_length > max_words:
            logging.error(
                f"Length of brand guidelines + 2*generated content is {brand_guideline_length + 2*generated_content_length}, longer than the max words budget of {max_words}. Truncating brand guideline..."
            )
            # Truncate by words instead of characters
            words = brand_guideline_text.split()
            buffer_word_count = 100
            truncated_words = words[
                : int(max_words - 2 * generated_content_length - buffer_word_count)
            ]
            brand_guideline_text = " ".join(truncated_words)

        custom_prompts_string = (
            features.get("custom_prompts_string") if features else None
        )
        messages = [
            HumanMessage(
                content=PREV_GEN_CONTENT_PROMPT.format(
                    generated_content=generated_content
                )
            ),
            HumanMessage(
                content=BRAND_GUIDELINE_PROMPT.format(
                    brand_guideline_text=brand_guideline_text
                )
            ),
        ]
        if custom_prompts_string:
            messages.append(
                HumanMessage(
                    content=BRAND_GUIDELINE_CUSTOM_INSTRUCTIONS.format(
                        custom_prompts_string=custom_prompts_string
                    )
                )
            )
        messages.append(HumanMessage(content=BRAND_GUIDELINE_INSTRUCT))
        return messages

    def _sanitize_input(self, text: str) -> str:
        """Sanitize input to prevent format string injection."""
        return text.replace("{", "{{").replace("}", "}}")

    async def _process_single_generation_async(
        self,
        model_caller: ModelCaller,
        messages: List[Dict[str, str]],
        original_content: str,
    ) -> str:
        try:
            # Propagate tracing context into the thread pool
            ctx = contextvars.copy_context()
            loop = asyncio.get_running_loop()
            results = await loop.run_in_executor(
                None, lambda: ctx.run(model_caller.get_results_with_fallback, messages)
            )
            if results and len(results) > 0:
                return self.parse_text_response_content(results[0].text)
            return original_content
        except Exception as e:
            logging.error(f"Error processing single generation: {e}")
            return original_content
