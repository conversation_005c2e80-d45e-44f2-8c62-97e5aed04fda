import copy
import logging

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..feature.feature_assembler.generate_feature_assembler import (
    ComponentJointGenerateFeatureAssembler,
)
from ..logger import log_data_wrapper_mismatch
from ..prompt.prompt_assembler.generate_joint_personalization_prompt_assembler import (
    GenerateJointPersonalizePromptAssembler,
)
from ..prompt.prompt_assembler.generate_repurpose_prompt_assembler import (
    GenerateJointRepurposePromptAssembler,
)


class JointGenerationMessageBuilder(DataWrapperHolder):
    def __init__(
        self,
        gen_feature_assembler=None,
        model_budget=8000,
        prev_gen_variations={},
        gen_env=None,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.gen_feature_assembler = gen_feature_assembler
        self.model_budget = model_budget
        self.prev_gen_variations = prev_gen_variations

    def create_llm_inputs(
        self,
        all_components,
    ):
        if self.gen_feature_assembler is None:
            logging.error(f"gen_feature_assembler is None.")
            return []

        component_joint_feature_assembler = ComponentJointGenerateFeatureAssembler(
            model_budget=self.model_budget,
            all_components=all_components,
            prev_gen_variations=self.prev_gen_variations,
            gen_env=self._gen_env,
        )
        all_features = component_joint_feature_assembler.build()
        all_features.update(self.gen_feature_assembler.get_features())

        self._features = all_features

        if self.campaign_goal == "Repurpose Content":
            prompt_assembler = GenerateJointRepurposePromptAssembler(
                gen_env=self._gen_env,
            )
            return prompt_assembler.build(all_features)
        else:
            prompt_assembler = GenerateJointPersonalizePromptAssembler(
                gen_env=self._gen_env,
            )
            return prompt_assembler.build(all_features)
