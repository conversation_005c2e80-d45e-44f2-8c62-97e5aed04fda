import copy
import logging

from langchain_core.messages import AIMessage, HumanMessage

from ..prompt.prompt_assembler.generate_repurpose_prompt_assembler import (
    GenerateRepurposePromptAssembler,
)
from ..prompt.prompt_library.instruct_prompt.repurpose_instruct import (
    FREE_GEN_ASK_ORIGINAL_CONTENT,
    FREE_GEN_ASK_PART_TO_REWRITE,
    FREE_GEN_MSG,
    FREE_GEN_MSG_WITH_INSTRUCTIONS,
    FREE_GEN_PARENT_COMPONENT_CURRENT_VALUE_MSG,
)


class FreeGenMessageBuilder(GenerateRepurposePromptAssembler):
    def __init__(
        self,
        gen_env,
        gen_feature_assembler,
        features=None,
    ):
        super().__init__(gen_env=gen_env)
        self.gen_feature_assembler = gen_feature_assembler
        self.features = features

    def create_llm_inputs(self):
        if not self.gen_feature_assembler:
            logging.error("Gen feature assembler is not initialized")
            return None

        # Get features from the assembler or use passed features
        if self.features:
            all_features = self.features
        else:
            all_features = self.gen_feature_assembler.build()

        # Use the overridden build method with free_gen additions
        return self.build(all_features)

    def build(self, features):
        """Override to add free_gen specific incremental generation features"""
        # Follow the repurpose flow
        super().build(features)

        # Add free_gen specific features for incremental generation
        parent_component_current_value = features.get("parent_component_current_value")
        if parent_component_current_value:
            # AI asks about the original generation
            self.append_message(
                FREE_GEN_ASK_ORIGINAL_CONTENT,
                AIMessage,
            )

            # Human provides what was generated before
            self.append_message(
                FREE_GEN_PARENT_COMPONENT_CURRENT_VALUE_MSG,
                HumanMessage,
            )

            # AI asks which part to rewrite
            self.append_message(
                FREE_GEN_ASK_PART_TO_REWRITE,
                AIMessage,
            )

        # Human provides the part to rewrite with instructions (consolidated)
        component_instructions = features.get("component_instructions")
        if component_instructions:
            self.append_message(
                FREE_GEN_MSG_WITH_INSTRUCTIONS,
                HumanMessage,
            )
        else:
            self.append_message(FREE_GEN_MSG, HumanMessage)

        return self.fill(features)
