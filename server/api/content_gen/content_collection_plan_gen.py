import logging
import os
import time

from ..content_gen.base_content_generator import <PERSON><PERSON>ontentGenerator
from ..langsmith_integration import dynamic_traceable
from ..prompt.prompt_library.instruct_prompt.content_collection_instruct import (
    CONTENT_COLLECTION_CUSTOM_INSTRUCT_PROMPT_TEMPLATE,
)
from ..thread_locals import get_current_user
from .content_collection_plan_input_builder import ContentCollectionPlanInputBuilder


class ContentCollectionPlanGen(BaseContentGenerator):
    def __init__(
        self,
        playbook_handler,
        content_collection,
        gen_env=None,
    ):
        super().__init__(
            gen_env=gen_env,
        )
        self.playbook_handler = playbook_handler
        self.content_collection = content_collection

    def get_metadata(self):
        campaign = self._gen_env._data_wrapper.campaign_instance
        user = campaign.creator if campaign else None
        if not user:
            logging.warning(
                "No user found for content collection plan gen, use current user"
            )
            user = get_current_user()

        metadata = {
            "campaign_id": campaign.id if campaign else None,
            "username": user.username if user else None,
            "request_id": self._gen_settings.request_id,
            "campaign_goal": self._data_wrapper.campaign_goal,
            "environment": os.environ.get("TOFU_ENV", default="unknown"),
            "content_collection_id": self.content_collection.content_collection_id,
            "is_campaign_v3": self._data_wrapper.is_campaign_v3,
        }

        metadata.update(self._gen_settings.config_map)
        return metadata

    @dynamic_traceable(name="content_collection_plan_gen")
    def gen(self):
        logging.info(f"Start generation with model {self.foundation_model}...")

        message_input_builder = ContentCollectionPlanInputBuilder(
            self.playbook_handler,
            self.content_collection,
            self.model_budget,
            gen_env=self._gen_env,
        )

        start_time = time.perf_counter()
        llm_inputs = message_input_builder.create_llm_inputs()

        generations = self.get_results(llm_inputs)

        end_time = time.perf_counter()
        runtime = end_time - start_time
        logging.info(f"Generation time: {runtime}")

        outputs = [generation.text for generation in generations]
        text_output = "\n".join(outputs)

        content_collection_instructions_prompt = (
            self.get_content_collection_instructions_prompt(message_input_builder)
        )
        text_output += "\n\n" + content_collection_instructions_prompt

        return text_output

    def get_content_collection_instructions_prompt(self, message_input_builder):
        if not message_input_builder.all_features:
            return ""
        if "custom_prompts_string" not in message_input_builder.all_features:
            return ""
        custom_prompts_string = message_input_builder.all_features[
            "custom_prompts_string"
        ]
        if not custom_prompts_string:
            return ""
        return CONTENT_COLLECTION_CUSTOM_INSTRUCT_PROMPT_TEMPLATE.format(
            custom_prompts_string=custom_prompts_string
        )
