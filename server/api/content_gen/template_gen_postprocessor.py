import json
import logging
import traceback
from abc import ABC, abstractmethod

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..logger import log_data_wrapper_mismatch
from ..shared_types import ContentType
from ..utils import strip_for_json


class TemplateGenerationPostprocessor(DataWrapperHolder):
    def __init__(
        self,
        gen_env,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.total_gen_count = 0
        self.rewrite_count = 0

    @staticmethod
    def get_postprocessor(gen_env):
        content_type = gen_env._data_wrapper.content_type
        if content_type in (ContentType.EmailMarketing, ContentType.EmailSDR):
            return EmailTemplateGenerationPostprocessor(gen_env)
        elif gen_env._data_wrapper.is_linkedin_ads_v2:
            return LinkedInTemplateGenerationPostprocessor(gen_env)
        else:
            return GenericTemplateGenerationPostprocessor(gen_env)

    def initialize(self, components):
        self.generated_variations = {key: [] for key in components.keys()}
        return True

    def postprocess(
        self,
        components,
        generations,
    ):
        self.initialize(components)

        for generated_content in generations:
            self.total_gen_count += 1
            try:
                self.process_single_generated_content(
                    generated_content,
                )
            except Exception as e:
                logging.error(e)
                return self.generated_variations
        return self.generated_variations

    @abstractmethod
    def process_single_generated_content(self, components, generated_content):
        pass


class GenericTemplateGenerationPostprocessor(TemplateGenerationPostprocessor):

    def __init__(self, gen_env):
        super().__init__(gen_env)

    def initialize(self, components):
        if len(components) != 1:
            logging.error(
                f"generic content should have 1 component, not {len(components)}: {components}"
            )
            return False
        self.key = list(components.keys())[0]
        return super().initialize(components)

    def process_single_generated_content(self, generated_content):
        self.generated_variations[self.key] = [generated_content]


class EmailTemplateGenerationPostprocessor(TemplateGenerationPostprocessor):

    def __init__(self, gen_env):
        super().__init__(gen_env)

        self.email_subject_key = None
        self.email_body_key = None

    def initialize(
        self,
        components,
    ):
        if len(components) != 2:
            logging.error(
                f"email content should have 2 components, not {len(components)}: {components}"
            )
            raise Exception(
                f"email content should have 2 components, not {len(components)}: {components}"
            )
        for key, component in components.items():
            if component.get("meta", {}).get("isEmailSubject", False):
                self.email_subject_key = key
            else:
                self.email_body_key = key
        if not (self.email_body_key and self.email_subject_key):
            logging.error(
                f"email content should have one component with isEmailSubject=True: {components}"
            )
            raise Exception(
                f"email content should have one component with isEmailSubject=True: {components}"
            )
        return super().initialize(components)

    def process_single_generated_content(self, generated_content):
        # parse email json
        try:
            generated_json = json.loads(strip_for_json(generated_content))
            if "subject" in generated_json and generated_json["subject"]:
                self.generated_variations[self.email_subject_key].append(
                    generated_json["subject"]
                )
            else:
                logging.error(f"subject is not in generated_json: {generated_json}")
                return self.generated_variations
            if "body" in generated_json and generated_json["body"]:
                if not self.email_body_key:
                    logging.error(f"email_body_key is not found: {self.email_body_key}")
                    return self.generated_variations
                self.generated_variations[self.email_body_key].append(
                    generated_json["body"]
                )
            else:
                logging.error(f"body is not in generated_json: {generated_json}")

        except Exception as e:
            logging.error(
                f"generation is not a valid json: {generated_content} with error: {e}\n{traceback.format_exc()}"
            )


class LinkedInTemplateGenerationPostprocessor(TemplateGenerationPostprocessor):

    def __init__(self, gen_env):
        super().__init__(gen_env)

        self.field_keys = {
            key: None
            for key in ["introductory-text", "headline", "description", "ad-copy"]
        }

    def initialize(self, components):
        if len(components) != 4:
            logging.error(
                f"linkedin ads content should have 4 components, not {len(components)}: {components}"
            )
            return False

        for key, component in components.items():
            field_name = component.get("meta", {}).get("template_field_name")
            if not field_name or field_name not in self.field_keys:
                logging.error(
                    f"linkedin ads content should have template_field_name: {component}"
                )
                return False
            if self.field_keys[field_name]:
                logging.error(
                    f"linkedin ads content should have unique template_field_name: {component}"
                )
                return False
            self.field_keys[field_name] = key

        if not self.field_keys["introductory-text"] or not self.field_keys["headline"]:
            logging.error(
                f"linkedin ads content should have introductory-text and headline fields required: {components}"
            )
            return False
        return super().initialize(components)

    def process_single_generated_content(self, generated_content):
        # parse email json
        try:
            generated_json = json.loads(strip_for_json(generated_content))

            for key, field_key in self.field_keys.items():
                if key in generated_json and generated_json[key]:
                    self.generated_variations[field_key].append(generated_json[key])

        except Exception as e:
            logging.error(
                f"generation is not a valid json: {generated_content} with error: {e}\n{traceback.format_exc()}"
            )
