import copy
import logging

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..feature.feature_assembler.generate_feature_assembler import (
    ComponentGenerateFeatureAssembler,
)
from ..logger import log_data_wrapper_mismatch
from ..prompt.prompt_assembler.generate_personalization_prompt_assembler import (
    GeneratePersonalizePromptAssembler,
)
from ..prompt.prompt_assembler.generate_repurpose_prompt_assembler import (
    GenerateRepurposePromptAssembler,
    GenerateRepurposeSalesDeckPromptAssembler,
    GenerateRepurposeSingleComponentPromptAssembler,
)
from ..shared_types import ContentType


class DefaultMessageInputBuilder(DataWrapperHolder):
    def __init__(
        self,
        gen_feature_assembler=None,
        model_budget=8000,
        prev_gen_variations={},
        gen_env=None,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)

        self.gen_feature_assembler = gen_feature_assembler
        self.model_budget = model_budget
        self.prev_gen_variations = prev_gen_variations

    def create_llm_inputs(self, example_content):
        if self.gen_feature_assembler is None:
            logging.error(f"gen_feature_assembler is None.")
            return []

        component_feature_assembler = ComponentGenerateFeatureAssembler(
            self.model_budget,
            example_content,
            self.prev_gen_variations,
            gen_env=self._gen_env,
        )
        all_features = component_feature_assembler.build()
        all_features.update(self.gen_feature_assembler.get_features())

        self._features = all_features

        if self.campaign_goal == "Repurpose Content":
            if (
                self.content_type == ContentType.SalesDeck
                or self.content_type == ContentType.SlideDeck
            ):
                prompt_assembler = GenerateRepurposeSalesDeckPromptAssembler(
                    gen_env=self._gen_env,
                )
            else:
                prompt_assembler = GenerateRepurposePromptAssembler(
                    gen_env=self._gen_env,
                )
        else:
            prompt_assembler = GeneratePersonalizePromptAssembler(
                gen_env=self._gen_env,
            )
        return prompt_assembler.build(all_features)
