import copy
import logging
import os
import re
import time

from django.core.cache import cache
from django.core.exceptions import ValidationError
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.outputs.generation import Generation

from ..content_gen.base_content_generator import BaseContentGenerator
from ..content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from ..content_gen.default_message_input_builder import DefaultMessageInputBuilder
from ..feature.data_wrapper.data_wrapper import BaseContentWrapper, GenerateEnv
from ..feature.feature_assembler.generate_feature_assembler import (
    GenerateFeatureAssembler,
)
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..logger import log_content_gen
from ..prompt.prompt_library.long_form_generation_prompt import (
    content_generation_first,
    content_generation_last,
    content_generation_next,
    plan_generation_prompt,
)
from ..utils import ceildiv, fix_claude_outputs, get_token_count

_LONG_FORM_GEN_CHUNK_SIZE = 1000


class LongFormComponentGenerator(BaseContentGenerator, BaseTracableClass):
    def __init__(
        self,
        component,
        component_id,
        gen_feature_assembler,
        gen_env=None,
    ):
        super().__init__(
            gen_env=gen_env,
        )

        self.component = component
        self.component_id = component_id
        self.gen_feature_assembler = gen_feature_assembler  # todo: reformat this

    def get_metadata(self):
        metadata = {
            "content_id": self.content_instance.id,
            "campaign_id": (
                self.campaign_instance.id if self.campaign_instance else None
            ),
            "content_type": self.content_type,
            "component_id": self.component_id,
            "campaign_goal": self.campaign_goal,
            "environment": os.environ.get("TOFU_ENV", default="unknown"),
        }
        metadata.update(self._gen_settings.config_map)
        if self._gen_settings.session_id:  # for test purpose
            match = re.search(r"-copy\((\d+)\)", self.campaign_instance.campaign_name)
            if match:
                extracted_number = match.group(1)
                metadata["orig_campaign_id"] = extracted_number
            else:
                logging.error(
                    f"Error extracting orig_campaign_id from {self.campaign_instance.campaign_name}"
                )

            def dict_to_str(d):
                # Sort the dictionary by key to ensure consistent order
                sorted_items = sorted(d.items())
                # Join the key-value pairs with ':' and each pair with '_'
                return "_".join(f"{key}:{value}" for key, value in sorted_items)

            metadata["targets"] = dict_to_str(
                self.content_instance.content_params.get("targets", {})
            )
        return metadata

    @dynamic_traceable(name="component_gen")
    def component_gen(self, content_gen_postprocessor, num_of_elements):
        start_time = time.perf_counter()

        component_data_wrapper = BaseContentWrapper.from_content_component(
            self.content_instance, self.component_id, self.component
        )
        gen_env = GenerateEnv(
            data_wrapper=component_data_wrapper,
            gen_settings=self._gen_env._gen_settings,
        )
        message_input_builder = DefaultMessageInputBuilder(
            gen_feature_assembler=self.gen_feature_assembler,
            model_budget=self.model_budget,
            prev_gen_variations=self.prev_gen_variations,
            gen_env=gen_env,
        )

        llm_inputs = message_input_builder.create_llm_inputs(self.component)

        self._features = message_input_builder._features

        try:
            cache_key = f"llm_messages_{self._data_wrapper.content_instance.id}_{self.component_id}"
            cache.set(
                cache_key, llm_inputs, timeout=60 * 60 * 24 * 30
            )  # save for 30 days
        except Exception as e:
            logging.error(f"Failed to cache llm messages: {e}")

        generations = self.get_generations(llm_inputs, self._features)

        end_time = time.perf_counter()
        runtime = end_time - start_time
        logging.info(f"Generation time: {runtime}")

        outputs = [generation.text for generation in generations]
        if self.full_page_html_gen:
            return [
                {
                    "text": text,
                    "meta": {
                        "runtime_seconds": runtime,
                        "gen_runtime": runtime,
                        "postprocess_runtime": 0,
                        "request_id": self._gen_env._gen_settings.request_id,
                    },
                }
                for text in outputs
            ]
        start_time_postprocess = time.perf_counter()
        generated_variations = content_gen_postprocessor.postprocess(
            self.component,
            outputs,
            llm_inputs,
            follow_length=self.template_settings.get("follow_length", True),
            component_id=self.component_id,
        )
        end_time_postprocess = time.perf_counter()
        postprocess_runtime = end_time_postprocess - start_time_postprocess
        total_runtime = runtime + postprocess_runtime

        generated_variations = [
            {
                "text": text,
                "meta": {
                    "runtime_seconds": total_runtime,
                    "gen_runtime": runtime,
                    "postprocess_runtime": postprocess_runtime,
                    "request_id": self._gen_env._gen_settings.request_id,
                },
            }
            for text in generated_variations
        ]
        variation_meta = {
            "variations": generated_variations,
            "current_variation_index": 0,
            "current_version": {
                "text": generated_variations[0]["text"],
                "request_id": generated_variations[0]["meta"]["request_id"],
            },
        }
        self.update_prev_gen_variations(
            self.component_id, generated_variations[0]["text"]
        )
        return variation_meta

    def get_generations(self, llm_inputs, all_features):
        long_form_output_length = self._gen_settings.long_form_output_length

        # First generate the plan
        content_type_name = all_features.get("content_type_name", "content")
        generation_plan_prompt = plan_generation_prompt.format(
            total_word_count=long_form_output_length,
            content_type_name=content_type_name,
        )
        llm_inputs = llm_inputs + [HumanMessage(content=generation_plan_prompt)]
        generations = self.get_results(llm_inputs)
        output_generations = []
        for generation in generations:
            self._gen_settings._num_of_variations = 1
            final_outputs = []
            # We need to maintain multiple threads of generations for different outputs.
            plan_text = generation.text
            ai_plan_message = AIMessage(content=plan_text)
            thread_llm_inputs = llm_inputs + [ai_plan_message]
            remaining_words = long_form_output_length
            for i in range(ceildiv(long_form_output_length, _LONG_FORM_GEN_CHUNK_SIZE)):
                if i == 0:
                    content_generation = content_generation_first.format(
                        content_type_name=content_type_name,
                    )
                elif (
                    i == ceildiv(long_form_output_length, _LONG_FORM_GEN_CHUNK_SIZE) - 1
                ):
                    content_generation = content_generation_last
                    content_generation = content_generation.format(
                        generation_word_count=generation_word_count,
                        remaining_words=remaining_words,
                        content_type_name=content_type_name,
                    )
                else:
                    content_generation = content_generation_next
                    content_generation = content_generation.format(
                        generation_word_count=generation_word_count,
                        content_type_name=content_type_name,
                    )
                thread_llm_inputs = thread_llm_inputs + [
                    HumanMessage(content=content_generation)
                ]
                output = self.get_results(thread_llm_inputs)
                # Should be only one output
                if len(output) != 1:
                    logging.error(
                        f"Expected 1 output but got {len(output)} outputs: {output}"
                    )
                output = output[0].text
                # output should be a string
                if not isinstance(output, str):
                    logging.error(f"Expected output to be a string but got {output}")
                    continue
                generation_word_count = len(output.split())
                thread_llm_inputs = thread_llm_inputs + [AIMessage(content=output)]
                remaining_words -= generation_word_count
                final_outputs.append(output)

            final_output = "\n".join(final_outputs)
            output_generations.append(Generation(text=final_output))
        return output_generations


class LongFormContentGenerator(BaseContentGenerator, BaseTracableClass):

    def __init__(
        self,
        gen_env=None,
    ):
        super().__init__(
            gen_env=gen_env,
        )

    def gen(self):
        self.validate()
        components = self._build_components(self.content_gen_components)
        if not components:
            raise ValidationError(
                f"components should not be empty for content {self.content_instance.id}"
            )

        logging.info(f"Start generation with model {self.foundation_model}...")
        variations = copy.deepcopy(components)
        num_of_elements = len(components)
        keys = list(components.keys())

        # sort component keys by time_added and isEmailSubject and order
        keys.sort(
            key=lambda x: (
                not components[x]
                .get("meta", {})
                .get("isEmailSubject", False),  # False < True
                components[x].get("meta", {}).get("order", 0),
                components[x].get("meta", {}).get("time_added", 0),
            )
        )

        self.gen_feature_assembler = GenerateFeatureAssembler(
            model_budget=self.model_budget,
            gen_env=self._gen_env,
        )
        self.gen_feature_assembler.build()

        content_gen_postprocessor = DefaultContentGenPostprocessor(
            gen_env=self._gen_env, model_caller=self.model_caller
        )

        for i in range(num_of_elements):
            component_generator = LongFormComponentGenerator(
                component=components[keys[i]],
                component_id=keys[i],
                gen_feature_assembler=self.gen_feature_assembler,
                gen_env=self._gen_env,
            )

            variation_meta = component_generator.component_gen(
                content_gen_postprocessor, num_of_elements
            )
            self._features = component_generator._features
            variations[keys[i]]["meta"] = {
                **variations[keys[i]]["meta"],
                **variation_meta,
            }

        total_gen_count = content_gen_postprocessor.total_gen_count
        rewrite_count = content_gen_postprocessor.rewrite_count
        logging.info("Total generations: " + str(total_gen_count))
        logging.info("Total rewrites: " + str(rewrite_count))
        if total_gen_count < 1:
            logging.error("No generations found")
            return variations
        if rewrite_count / total_gen_count > 0.2:
            logging.warning(
                "Rewrites too high. Total rewrites: "
                + str(rewrite_count)
                + ", Total generations: "
                + str(total_gen_count)
            )

        log_content_gen(content=self.content_instance)
        return variations
