import copy
import logging
import time

from django.core.exceptions import ValidationError

from ..content_gen.base_content_generator import BaseContentGenerator
from ..content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from ..content_gen.joint_gen_message_builder import JointGenerationMessageBuilder
from ..content_gen.joint_gen_postprocessor import JointGenerationPostprocessor
from ..feature.feature_assembler.generate_feature_assembler import (
    GenerateFeatureAssembler,
)
from ..utils import check_generations_are_valid_json, fix_claude_outputs
from .disjoint_content_gen import DisjointContentGenerator


class JointContentGenerator(BaseContentGenerator):
    def __init__(self, gen_env):
        super().__init__(gen_env=gen_env)

    def gen(self):
        self.validate()
        components = self._build_components(self.content_gen_components)
        if not components:
            raise ValidationError(
                f"components should not be empty for content {self.content_instance.id}"
            )

        logging.info(f"Start generation with model {self.foundation_model}...")
        keys = self._sort_component_keys(components)

        self.gen_feature_assembler = GenerateFeatureAssembler(
            model_budget=self.model_budget,
            gen_env=self._gen_env,
        )
        self.gen_feature_assembler.build()

        self.message_input_builder = JointGenerationMessageBuilder(
            self.gen_feature_assembler,
            self.model_budget,
            self.prev_gen_variations,
            gen_env=self._gen_env,
        )

        self.joint_content_gen_postprocessor = (
            JointGenerationPostprocessor.get_postprocessor(
                gen_env=self._gen_env,
            )
        )
        start_time = time.perf_counter()
        self.llm_inputs = self.message_input_builder.create_llm_inputs(
            self._data_wrapper.text_gen_components
        )

        generations = self.get_results(self.llm_inputs)
        self._features = self.message_input_builder._features

        if (
            self.campaign_goal != "Repurpose Content"
            and not check_generations_are_valid_json(generations, self.foundation_model)
        ):
            return self._handle_invalid_generation(
                "Failed to parse joint generation.", generations
            )

        if (
            self.campaign_goal == "Repurpose Content"
            and not self.joint_content_gen_postprocessor.check_generations_valid(
                generations
            )
        ):
            return self._handle_invalid_generation(
                f"Failed to parse repurpose {self.content_type} joint generation.",
                generations,
            )
        end_time = time.perf_counter()
        self.runtime = end_time - start_time
        logging.info(f"Generation time: {self.runtime}")

        outputs = [generation.text for generation in generations]

        start_time = time.perf_counter()
        try:
            all_generated_variations = self._postprocess_generations(
                components, outputs, keys
            )
        except ValueError as e:
            return self._handle_invalid_generation(
                f"Failed to parse joint generation.", generations
            )
        end_time = time.perf_counter()
        self.postprocess_runtime = end_time - start_time
        logging.info(f"Postprocess time: {self.postprocess_runtime}")

        variations = self._update_variations(components, all_generated_variations, keys)

        return variations

    def _handle_invalid_generation(self, message, generations):
        logging.error(
            f"{message} Retrying with disjoint content generation.\n Original generations: {generations}"
        )
        if self._gen_settings.no_retry:
            raise Exception(message)
        return self.retry_using_disjoint_content_gen()

    def _postprocess_generations(self, components, outputs, keys):
        all_generated_variations = (
            self.joint_content_gen_postprocessor.postprocess_joint(components, outputs)
        )

        for key in keys:
            if (
                key not in all_generated_variations
                or len(all_generated_variations[key]) < self.num_of_variations
            ):
                raise ValueError(f"Failed to parse joint generation")

        return all_generated_variations

    def _update_variations(self, components, all_generated_variations, keys):
        default_content_gen_postprocessor = DefaultContentGenPostprocessor(
            gen_env=self._gen_env, model_caller=self.model_caller
        )

        variations = copy.deepcopy(components)
        for component_id in keys:
            generated_variations = default_content_gen_postprocessor.postprocess(
                components[component_id],
                all_generated_variations[component_id],
                self.llm_inputs,
                follow_length=self.template_settings.get("follow_length", True),
                component_id=component_id,
            )
            variations[component_id] = self._update_component_variation(
                components[component_id], component_id, generated_variations
            )

        self._log_generation_stats(default_content_gen_postprocessor)
        return variations

    def _update_component_variation(
        self, component, component_id, generated_variations
    ):
        if not generated_variations:
            logging.error(f"No generated variations for component {component_id}")
            return component
        total_runtime = self.runtime + self.postprocess_runtime
        meta_variations = [
            {
                "text": text,
                "meta": {
                    "runtime_seconds": total_runtime,
                    "gen_runtime": self.runtime,
                    "postprocess_runtime": self.postprocess_runtime,
                    "request_id": self._gen_env._gen_settings.request_id,
                },
            }
            for text in generated_variations
        ]
        component["meta"]["variations"] = meta_variations
        component["meta"]["current_variation_index"] = 0
        component["meta"]["current_version"] = {
            "text": meta_variations[0]["text"],
            "request_id": meta_variations[0]["meta"]["request_id"],
        }
        self.update_prev_gen_variations(component_id, meta_variations[0]["text"])
        return component

    def retry_using_disjoint_content_gen(self):
        disjoint_content_gen = DisjointContentGenerator(
            gen_env=self._gen_env,
        )
        return disjoint_content_gen.gen()
