import copy
import logging
import os
import re
import time

from django.core.cache import cache

from ..feature.data_wrapper.data_wrapper import Base<PERSON><PERSON>ntWrapper, GenerateEnv
from ..feature.feature_assembler.generate_feature_assembler import (
    GenerateFeatureAssembler,
)
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..playbook import Play<PERSON><PERSON>andler
from ..shared_types import ContentType
from ..slides.core import SlidesDeliverableGenerator
from .base_content_generator import BaseContentGenerator
from .default_content_gen_postprocessor import DefaultContentGenPostprocessor
from .default_message_input_builder import DefaultMessageInputBuilder


class ComponentGenerator(BaseContentGenerator, BaseTracableClass):
    def __init__(
        self,
        component,
        component_id,
        gen_feature_assembler,
        gen_env=None,
    ):
        super().__init__(
            gen_env=gen_env,
        )

        self.component = component
        self.component_id = component_id
        self.gen_feature_assembler = gen_feature_assembler  # todo: reformat this

    def get_metadata(self):
        metadata = {
            "content_id": self.content_instance.id,
            "campaign_id": (
                self.campaign_instance.id if self.campaign_instance else None
            ),
            "content_type": self.content_type,
            "component_id": self.component_id,
            "campaign_goal": self.campaign_goal,
            "environment": os.environ.get("TOFU_ENV", default="unknown"),
        }
        metadata.update(self._gen_settings.config_map)
        if self._gen_settings.session_id:  # for test purpose
            match = re.search(r"-copy\((\d+)\)", self.campaign_instance.campaign_name)
            if match:
                extracted_number = match.group(1)
                metadata["orig_campaign_id"] = extracted_number
            else:
                logging.error(
                    f"Error extracting orig_campaign_id from {self.campaign_instance.campaign_name}"
                )

            def dict_to_str(d):
                # Sort the dictionary by key to ensure consistent order
                sorted_items = sorted(d.items())
                # Join the key-value pairs with ':' and each pair with '_'
                return "_".join(f"{key}:{value}" for key, value in sorted_items)

            metadata["targets"] = dict_to_str(
                self.content_instance.content_params.get("targets", {})
            )
        return metadata

    @dynamic_traceable(name="component_gen")
    def component_gen(self, content_gen_postprocessor):
        start_time = time.perf_counter()

        component_data_wrapper = BaseContentWrapper.from_content_component(
            self.content_instance, self.component_id, self.component
        )
        gen_env = GenerateEnv(
            data_wrapper=component_data_wrapper,
            gen_settings=self._gen_env._gen_settings,
        )
        message_input_builder = DefaultMessageInputBuilder(
            gen_feature_assembler=self.gen_feature_assembler,
            model_budget=self.model_budget,
            prev_gen_variations=self.prev_gen_variations,
            gen_env=gen_env,
        )

        llm_inputs = message_input_builder.create_llm_inputs(self.component)

        self._features = message_input_builder._features

        try:
            cache_key = f"llm_messages_{self._data_wrapper.content_instance.id}_{self.component_id}"
            cache.set(
                cache_key, llm_inputs, timeout=60 * 60 * 24 * 30
            )  # save for 30 days
        except Exception as e:
            logging.error(f"Failed to cache llm messages: {e}")

        generations = self.get_results(llm_inputs, json_output=False)

        end_time = time.perf_counter()
        runtime = end_time - start_time
        logging.info(f"Generation time: {runtime}")

        outputs = [generation.text for generation in generations]

        if self.full_page_html_gen:
            return [
                {
                    "text": text,
                    "meta": {
                        "runtime_seconds": runtime,
                        "gen_runtime": runtime,
                        "postprocess_runtime": 0,
                        "request_id": self._gen_env._gen_settings.request_id,
                    },
                }
                for text in outputs
            ]
        start_time_postprocess = time.perf_counter()
        # only apply brand guidelines if the campaign is repurposing and the content is not quotes & highlights or sales deck.
        if self.campaign_goal == "Repurpose Content":
            try:
                brand_guidelines_text = PlaybookHandler(
                    self.playbook_instance
                ).get_brand_guidelines_text()
                if (
                    brand_guidelines_text
                    and self.content_type != ContentType.QuotesHighlights
                    and self.content_type != ContentType.SalesDeck
                ):
                    outputs = content_gen_postprocessor.rewrite_with_brand_guideline(
                        outputs, brand_guidelines_text, features=self._features
                    )
            except Exception as e:
                logging.error(f"Failed to apply brand guidelines: {e}")
        generated_variations = content_gen_postprocessor.postprocess(
            self.component,
            outputs,
            llm_inputs,
            follow_length=self.template_settings.get("follow_length", True),
            component_id=self.component_id,
        )
        end_time_postprocess = time.perf_counter()
        postprocess_runtime = end_time_postprocess - start_time_postprocess
        total_runtime = runtime + postprocess_runtime

        generated_variations = [
            {
                "text": text,
                "meta": {
                    "runtime_seconds": total_runtime,
                    "gen_runtime": runtime,
                    "postprocess_runtime": postprocess_runtime,
                    "request_id": self._gen_env._gen_settings.request_id,
                },
            }
            for text in generated_variations
        ]
        variation_meta = {
            "variations": generated_variations,
            "current_variation_index": 0,
            "current_version": {
                "text": generated_variations[0]["text"],
                "request_id": generated_variations[0]["meta"]["request_id"],
            },
        }
        self.update_prev_gen_variations(
            self.component_id, generated_variations[0]["text"]
        )

        if (
            self.content_type == ContentType.SalesDeck
            or self.content_type == ContentType.SlideDeck
        ) and self.campaign_goal == "Repurpose Content":
            variation_meta = SlidesDeliverableGenerator(
                self._gen_env
            ).update_slides_variations(variation_meta)

        return variation_meta


class DisjointContentGenerator(BaseContentGenerator):
    def __init__(
        self,
        gen_env=None,
    ):
        super().__init__(
            gen_env=gen_env,
        )

    def gen(self):
        self.validate()
        components = self._build_components(self.content_gen_components)

        logging.info(f"Start generation with model {self.foundation_model}...")
        variations = copy.deepcopy(components)
        keys = self._sort_component_keys(components)

        self._setup_generation()

        for key in keys:
            variation_meta = self._generate_component(key, components)
            variations[key]["meta"].update(variation_meta)

        self._log_generation_stats(self.content_gen_postprocessor)
        return variations

    def _setup_generation(self):
        self.gen_feature_assembler = GenerateFeatureAssembler(
            model_budget=self.model_budget,
            gen_env=self._gen_env,
        )
        self.gen_feature_assembler.build()
        self.content_gen_postprocessor = DefaultContentGenPostprocessor(
            gen_env=self._gen_env, model_caller=self.model_caller
        )

    def _generate_component(self, component_id, components):
        component = components[component_id]
        component_generator = ComponentGenerator(
            component=component,
            component_id=component_id,
            gen_feature_assembler=self.gen_feature_assembler,
            gen_env=self._gen_env,
        )
        variation_meta = component_generator.component_gen(
            self.content_gen_postprocessor
        )
        self._features = component_generator._features
        return variation_meta
