import copy
import logging
import time

from django.core.exceptions import ValidationError

from ..content_gen.base_content_generator import BaseContentGenerator
from ..content_gen.template_gen_input_builder import TemplateGenerationInputBuilder
from ..content_gen.template_gen_postprocessor import TemplateGenerationPostprocessor
from ..feature.feature_assembler.generate_feature_assembler import (
    GenerateFeatureAssembler,
)
from ..logger import log_content_gen
from ..utils import fix_claude_outputs, get_token_count
from .component_utils import create_predefined_components


class TemplateGenerator(BaseContentGenerator):
    def __init__(
        self,
        gen_env=None,
    ):
        super().__init__(
            gen_env=gen_env,
        )

    def gen(self):
        self.validate_template_generation()
        components = self._build_components()
        if not components:
            raise ValidationError(
                f"components should not be empty for content {self.content_instance.id}"
            )

        logging.info(f"Start generation with model {self.foundation_model}...")
        variations = copy.deepcopy(components)
        keys = list(components.keys())

        # sort component keys by time_added and isEmailSubject and order
        keys.sort(
            key=lambda x: (
                not components[x]
                .get("meta", {})
                .get("isEmailSubject", False),  # False < True
                components[x].get("meta", {}).get("order", 0),
                components[x].get("meta", {}).get("time_added", 0),
            )
        )

        self.gen_feature_assembler = GenerateFeatureAssembler(
            model_budget=self.model_budget,
            gen_env=self._gen_env,
        )
        self.gen_feature_assembler.build()
        message_input_builder = TemplateGenerationInputBuilder(
            self.gen_feature_assembler,
            self.model_budget,
            gen_env=self._gen_env,
        )
        content_gen_postprocessor = TemplateGenerationPostprocessor.get_postprocessor(
            gen_env=self._gen_env,
        )
        start_time = time.perf_counter()
        llm_inputs = message_input_builder.create_llm_inputs()

        generations = self.get_results(llm_inputs)

        end_time = time.perf_counter()
        runtime = end_time - start_time
        logging.info(f"Generation time: {runtime}")

        outputs = [generation.text for generation in generations]

        start_time_postprocess = time.perf_counter()
        generated_variations = content_gen_postprocessor.postprocess(
            components,
            outputs,
        )
        end_time_postprocess = time.perf_counter()
        postprocess_runtime = end_time_postprocess - start_time_postprocess
        total_runtime = runtime + postprocess_runtime
        for i in range(len(keys)):
            component_id = keys[i]
            if component_id not in generated_variations:
                logging.error(
                    f"Component ID {component_id} not found in generated variations"
                )
                continue
            component_variations = [
                {
                    "text": text,
                    "meta": {
                        "runtime_seconds": total_runtime,
                        "gen_runtime": runtime,
                        "postprocess_runtime": postprocess_runtime,
                        "request_id": self._gen_env._gen_settings.request_id,
                    },
                }
                for text in generated_variations[component_id]
            ]
            variations[component_id]["meta"]["variations"] = component_variations
            variations[component_id]["meta"]["current_variation_index"] = 0
            variations[component_id]["meta"]["current_version"] = {
                "text": component_variations[0]["text"],
                "request_id": component_variations[0]["meta"]["request_id"],
            }

        total_gen_count = content_gen_postprocessor.total_gen_count
        rewrite_count = content_gen_postprocessor.rewrite_count
        logging.info("Total generations: " + str(total_gen_count))
        logging.info("Total rewrites: " + str(rewrite_count))
        if total_gen_count < 1:
            logging.error("No generations found")
            return variations
        if rewrite_count / total_gen_count > 0.2:
            logging.warning(
                "Rewrites too high. Total rewrites: "
                + str(rewrite_count)
                + ", Total generations: "
                + str(total_gen_count)
            )

        log_content_gen(content=self.content_instance)
        return variations

    def validate_template_generation(self):
        # Template generation requires targets.
        if (
            self._gen_settings.template_generation
            and not self._data_wrapper.target_params
        ):
            raise Exception(
                f"Template generation requires targets for content {self.content_instance.id}"
            )

        # Template generation is only for text content source format.
        if (
            self._gen_settings.template_generation
            and self._data_wrapper.content_source_format != "Text"
        ):
            raise Exception(
                f"Template generation is only for text content source format, not {self._data_wrapper.content_source_format}"
            )

    def _build_components(self):
        components = create_predefined_components(self._data_wrapper)
        self._gen_settings.update_components(components)
        return components
