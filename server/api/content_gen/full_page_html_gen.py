from ..content_gen.base_content_generator import BaseContentGenerator
from ..utils import get_s3_file, postprocess_repurpose_html, process_html
from .disjoint_content_gen import DisjointContentGenerator


class FullPageHtmlGenerator(BaseContentGenerator):
    def __init__(self, gen_env=None):
        super().__init__(gen_env=gen_env)

    def gen(self):
        components, mapping = self.process_full_page_html()
        self._gen_settings.update_components(components)
        variations = DisjointContentGenerator(self._gen_env).gen()
        variations = postprocess_repurpose_html(
            variations,
            mapping,
            self._data_wrapper.components,
            self._gen_settings.request_id,
        )
        return variations

    def process_full_page_html(self):
        # Get HTML from s3 file bucket and process by replacing html tags.
        html_s3_source = self._data_wrapper.content_source_copy
        input_html = get_s3_file(html_s3_source)
        processed_html_doc, mapping = process_html(input_html)
        components = {"0": {"text": processed_html_doc}}
        return components, mapping
