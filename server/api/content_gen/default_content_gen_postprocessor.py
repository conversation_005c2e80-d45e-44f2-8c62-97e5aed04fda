import json
import logging
import os
import re
from typing import Dict, List

from bs4 import BeautifulSoup
from langchain_core.messages import BaseMessage

from ..feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..model_caller import ModelCaller
from ..shared_types import ContentSourceFormat, ContentType
from ..utils import (
    CloudWatchMetrics,
    fix_content_html_tags,
    fuzzy_match_json_key,
    rewrite_length_limit,
    strip_for_json,
)
from .brand_guideline_rewriter import BrandGuidelineRewriter


class DefaultContentGenPostprocessor(DataWrapperHolder, BaseTracableClass):

    def __init__(
        self,
        gen_env,
        model_caller: ModelCaller,
    ):
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.total_gen_count = 0
        self.rewrite_count = 0
        self.model_caller = model_caller

    def get_metadata(self):
        metadata = {
            "content_id": self.content_instance.id if self.content_instance else None,
            "campaign_id": (
                self.campaign_instance.id if self.campaign_instance else None
            ),
            "content_type": self.content_type,
            "campaign_goal": self.campaign_goal,
            "environment": os.environ.get("TOFU_ENV", default="unknown"),
        }
        metadata.update(self._gen_settings.config_map)
        if self._gen_settings.session_id:  # for test purpose
            match = re.search(r"-copy\((\d+)\)", self.campaign_instance.campaign_name)
            if match:
                extracted_number = match.group(1)
                metadata["orig_campaign_id"] = extracted_number
            else:
                logging.error(
                    f"Error extracting orig_campaign_id from {self.campaign_instance.campaign_name}"
                )

            def dict_to_str(d):
                # Sort the dictionary by key to ensure consistent order
                sorted_items = sorted(d.items())
                # Join the key-value pairs with ':' and each pair with '_'
                return "_".join(f"{key}:{value}" for key, value in sorted_items)

            metadata["targets"] = dict_to_str(
                self.content_instance.content_params.get("targets", {})
            )
        metadata["brand_guideline_rewriting"] = True
        return metadata

    @dynamic_traceable(name="brand_guideline_rewriting")
    def rewrite_with_brand_guideline(
        self,
        generations: List[str],
        brand_guideline_text: str,
        features: Dict[str, str] = None,
    ):
        self.brand_guideline_rewriter = BrandGuidelineRewriter(
            self._gen_env, self.model_caller
        )
        try:
            return self.brand_guideline_rewriter.rewrite_with_brand_guideline(
                generations, brand_guideline_text, features
            )
        except Exception as e:
            logging.error(f"Failed to rewrite with brand guidelines: {e}")
            return generations

    @dynamic_traceable(name="content_postprocess")
    def postprocess(
        self,
        example_content: Dict[str, str],
        generations: List[str],
        original_llm_inputs: List[BaseMessage],
        follow_length=True,
        clean_ending_period=True,
        clean_quotation=True,
        clean_word_count=True,
        clean_leading_word_count=True,
        clean_leading_message=True,
        clean_new_line=True,
        clean_back_slash=True,
        clean_bullet_points=False,
        fix_html_tags=True,
        component_id=None,
    ):
        # only clean html tags if the content source format is not html and campaign goal is not repurpose content
        clean_html_tags = self.content_source_format != ContentSourceFormat.Html

        formatted_outputs = []
        for generated_content in generations:
            self.total_gen_count += 1
            source_content = example_content["text"]
            if self.campaign_goal != "Repurpose Content":
                expected_length = len(source_content.split())

                def exceeds_lower_limit(generated_content, expected_length):
                    return len(generated_content.split()) < 0.8 * expected_length

                def exceeds_upper_limit(generated_content, expected_length):
                    content_length = len(generated_content.split())
                    if self.content_source_format == "PDF":
                        return content_length > 1.2 * expected_length
                    else:
                        if expected_length == 1:
                            # for 1 and 2 word prompts we only rewrite if the output is greater than 4 words.
                            return content_length > 4
                        return content_length > 3 * expected_length

                if exceeds_lower_limit(generated_content, expected_length):
                    # Currently LLM is bad at increasing content length, so just log it for now.
                    logging.warning(
                        f"Generated content is too short. Component id: {component_id}, Expected: {expected_length}, Actual: {len(generated_content.split())}. Generated content: {generated_content}"
                    )
                if follow_length and exceeds_upper_limit(
                    generated_content, expected_length
                ):
                    logging.warning(
                        f"Generated content exceeds length. Component id: {component_id}, Expected: {expected_length}, Actual: {len(generated_content.split())}. Generated content: {generated_content}"
                    )
                    generated_content = rewrite_length_limit(
                        self.model_caller,
                        original_llm_inputs,
                        generated_content,
                        expected_length,
                    )
                    self.rewrite_count += 1

                    # Use the utility class
                    CloudWatchMetrics.put_metric(
                        metric_name="PostprocessRewrite",
                        value=1,
                        dimensions=[
                            {"Name": "FoundationModel", "Value": self.foundation_model},
                            {
                                "Name": "CampaignId",
                                "Value": str(self.campaign_instance.id),
                            },
                            {
                                "Name": "User",
                                "Value": (
                                    str(self.playbook_instance.users.first())
                                    if self.playbook_instance
                                    and self.playbook_instance.users.exists()
                                    else "none"
                                ),
                            },
                        ],
                    )

                    logging.warning(
                        f"Generated content after rewrite: {generated_content}\nLength: {len(generated_content.split())}"
                    )
                    if "claude" in self.foundation_model:
                        generated_content = self.try_resolve_rewrite_json(
                            generated_content, source_content, component_id
                        )
                else:
                    # Use the utility class
                    CloudWatchMetrics.put_metric(
                        metric_name="PostprocessRewrite",
                        value=0,
                        dimensions=[
                            {"Name": "FoundationModel", "Value": self.foundation_model},
                            {
                                "Name": "CampaignId",
                                "Value": str(self.campaign_instance.id),
                            },
                            {
                                "Name": "User",
                                "Value": (
                                    str(self.playbook_instance.users.first())
                                    if self.playbook_instance
                                    and self.playbook_instance.users.exists()
                                    else "none"
                                ),
                            },
                        ],
                    )

                if clean_ending_period:
                    # clean ending period
                    if not re.search(
                        r"[.!?]$", source_content.strip()
                    ) and generated_content.endswith("."):
                        generated_content = generated_content[:-1]

            if self.content_type in (ContentType.EmailMarketing, ContentType.EmailSDR):
                if generated_content.startswith("Subject:"):
                    generated_content = generated_content[len("Subject:") :]
            if self.campaign_goal != "Repurpose Content":
                if clean_html_tags:
                    # Clean html tags if any
                    soup = BeautifulSoup(generated_content, "html.parser")
                    generated_content = soup.get_text(separator="")
                else:  # HTML case, turn \n into <br>
                    generated_content = generated_content.replace("\n", "<br>")
                    # Turn leading and trailing spaces into &nbsp;
                    generated_content = re.sub(r"^\s+", "&nbsp;", generated_content)
                    generated_content = re.sub(r"\s+$", "&nbsp;", generated_content)
                    if fix_html_tags:
                        generated_content = fix_content_html_tags(generated_content)

            if clean_quotation:
                # clean quotation
                if not (
                    source_content.startswith(("'", '"'))
                    and source_content.endswith(("'", '"'))
                ):
                    if generated_content.startswith(
                        ("'", '"')
                    ) and generated_content.endswith(("'", '"')):
                        generated_content = generated_content[1:-1]

            if clean_word_count:
                # clean "(xx words)"
                generated_content = re.sub(
                    r"\s*(\(\d+ words\)|\[\d+ words\])", "", generated_content
                )

            if clean_leading_word_count:
                # clean what Claude generates with prefix like "Here is a 55 word personalized message tailored for the CEO persona:\n\n"
                generated_content = re.sub(
                    r"\s*(Here is [^\n]*word[^\n]* message[^\n]*\n\n)",
                    "",
                    generated_content,
                )
                if isinstance(
                    self.foundation_model, str
                ) and self.foundation_model.startswith("llama-2"):
                    # clean what llama-2 generates with prefix like 34 words or shorter new message for Stampli's targeting customers with Healthcare Subverticals - Psychology:
                    generated_content = re.sub(
                        r"\s*([^\n]*word[^\n]* message[^\n]*:[\n]+)",
                        "",
                        generated_content,
                    )
                    # llama-2 also have: Sure, here is the rewritten text in 70 words or less:
                    generated_content = re.sub(
                        r"\s*(Sure[^\n]*ere is[^\n]*[\n]+)", "", generated_content
                    )

            if clean_leading_message:
                if generated_content.startswith("Transformed message: "):
                    generated_content = generated_content[
                        len("Transformed message: ") :
                    ]

            if clean_new_line:
                # Clean \\n to \n
                generated_content = generated_content.replace("\\n", "\n")

            if clean_back_slash:
                # Clean \ in string.
                generated_content = generated_content.replace("\\", "")

            if clean_bullet_points:
                # We set for False since we think the model can do a good job at this.
                # If this becomes a problem in the future, we can set it to True for Slides.
                # Normalize unicode bullets
                generated_content = generated_content.replace("\u2022", "-").replace(
                    "•", "-"
                )
                # Replace only leading "* " at the start of lines (bullet markers)
                generated_content = re.sub(r"(?m)^[ \t]*\* ", "- ", generated_content)
            formatted_outputs.append(generated_content)

        return formatted_outputs

    # context: for claude model, the generated content is possibly in json format with component_id as key
    def try_resolve_rewrite_json(self, generated_content, source_content, component_id):
        if generated_content.find("{") == -1 or generated_content.find("}") == -1:
            # not json format
            return generated_content

        try:
            generated_json_data = json.loads(strip_for_json(generated_content))
        except Exception as e:
            logging.warning(
                f"Failed to load generated json data: {e}, fallback to original content"
            )
            return source_content

        if not component_id:
            logging.error(
                f"component id is not passed for joint generation post-processing, fallback to original content"
            )
            return source_content

        if component_id in generated_json_data:
            generated_content = generated_json_data[component_id]["text"]
        else:
            # try fuzzy match
            closest_key = fuzzy_match_json_key(component_id, generated_json_data)
            if closest_key:
                generated_content = generated_json_data[closest_key]["text"]
            else:
                logging.warning(
                    f"Failed to find key {component_id} in generated json data between {generated_json_data.keys()}, fallback to original content"
                )
                return source_content
        return generated_content
