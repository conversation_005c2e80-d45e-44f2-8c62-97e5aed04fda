{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrastyle %}
{{ block.super }}
<style>
    .form-row {
        padding: 10px 0;
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
    }
    .field-label {
        width: 200px;
        padding-right: 20px;
        text-align: right;
        padding-top: 8px;
        font-weight: bold;
    }
    .field-content {
        flex: 1;
    }
    .preview-image {
        max-width: 300px;
        margin: 10px 0;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    .help {
        color: #666;
        font-style: italic;
        margin-top: 4px;
        font-size: 0.9em;
    }
    input[type="text"], 
    input[type="url"], 
    textarea {
        width: 90%;
        padding: 6px 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    textarea {
        min-height: 80px;
    }
    .submit-row {
        margin-top: 20px;
        padding: 20px;
        text-align: right;
    }
    .submit-row input[type="submit"] {
        padding: 10px 15px;
        background: #417690;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .submit-row .closelink {
        margin-left: 10px;
    }
    .image-upload-container {
        margin-top: 10px;
    }
    
    .image-preview {
        margin: 10px 0;
    }
    
    .image-preview img {
        max-width: 300px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    
    .current-url {
        word-break: break-all;
        font-size: 0.9em;
        color: #666;
        margin: 5px 0;
    }
    
    .file-upload {
        margin-top: 10px;
    }
    
    .file-upload input[type="file"] {
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div id="content-main">
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <fieldset class="module aligned">
            {% for field in form %}
            <div class="form-row">
                <div class="field-label">
                    {% if not field.label == '' %}
                        {{ field.label_tag }}
                    {% endif %}
                </div>
                <div class="field-content">
                    {% if field.name == 'image_entry_point' or field.name == 'image_template_modal' %}
                        {{ field }}
                        {% if field.value %}
                            <div class="image-preview">
                                <img src="{{ field.value }}" class="preview-image" onerror="this.style.display='none'" />
                            </div>
                        {% endif %}
                        {% if field.name == 'image_entry_point' %}
                            {{ form.image_entry_point_file }}
                        {% elif field.name == 'image_template_modal' %}
                            {{ form.image_template_modal_file }}
                        {% endif %}
                        <div class="help">{{ field.help_text }}</div>
                    {% elif not field.name == 'image_entry_point_file' and not field.name == 'image_template_modal_file' %}
                        {{ field }}
                        {% if field.help_text %}
                            <div class="help">{{ field.help_text }}</div>
                        {% endif %}
                    {% endif %}
                    {{ field.errors }}
                </div>
            </div>
            {% endfor %}
        </fieldset>
        <div class="submit-row">
            <input type="submit" value="Save" class="default" />
            <a href="{% url 'admin:api_campaign_change' campaign.id %}" class="closelink">Cancel</a>
        </div>
    </form>
</div>
{% endblock %} 