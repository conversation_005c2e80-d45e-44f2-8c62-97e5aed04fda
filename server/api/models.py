import logging
import uuid
from datetime import datetime

from django.contrib.auth.models import (
    AbstractBase<PERSON>ser,
    BaseUserManager,
    PermissionsMixin,
)
from django.contrib.postgres.fields import ArrayField
from django.contrib.postgres.indexes import GinIndex
from django.contrib.postgres.search import Search<PERSON><PERSON><PERSON>ield
from django.core.validators import validate_email
from django.db import IntegrityError, models, transaction
from django.db.models import F, Func, Value
from django.db.models.signals import m2m_changed, post_delete, post_save
from django.dispatch import receiver
from django.utils import timezone
from sqlalchemy import Column, Computed, Index, Integer, Sequence, String
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy_utils import TSVectorType

from .integrations.slack import send_slack_message


class TofuUserManager(BaseUserManager):
    # try to extract company domain from username
    # return None if failed or the domain is not registered in our system
    def extract_company_domain(self, username):
        try:
            validate_email(username)
        except:
            return None

        company_domain = username.split("@")[1]
        # check whether we want to auto link playbook for the domain or not
        if CompanyDomain.objects.filter(link_playbook_domain=company_domain).exists():
            return company_domain
        return None

    # check whether the company domain is allowed to register
    def is_company_domain_allowed_to_register(self, company_domain):
        if not company_domain:
            return False
        return CompanyDomain.objects.filter(
            allow_register_domain=company_domain
        ).exists()

    def create_user(self, username, password=None, playbook=None, **extra_fields):
        if not username:
            raise ValueError("The username field must be set")

        user = self.model(username=username, **extra_fields)
        user.set_password(password)
        user.credits_available = 100

        user.context = user.context or {}
        company_domain = self.extract_company_domain(username)

        user.context.update(
            {
                "verified_user": user.username.startswith("tofuadmin")
                or self.is_company_domain_allowed_to_register(company_domain)
                or self.is_tofu_lite(user),
                "internalFeatures": user.username.startswith("tofuadmin"),
                "campaignV3Enabled": True,
                "richTextV2Enabled": False,
                "model": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                "model_for_repurpose": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
            }
        )
        if user.username.startswith("tofuadmin"):
            user.context["model_for_repurpose"] = (
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
            )
            user.context["model"] = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"

        try:
            if (
                user.context.get("verified_user")
                and not user.username.startswith("tofuadmin")
                and not self.is_tofu_lite(user)
            ):
                send_slack_message(
                    "#customer-success",
                    f"New user {user.username} ({user.full_name}) is auto verified",
                )
            if (
                user.context.get("verified_user")
                and user.customer_type == TofuUser.CustomerType.LITE
            ):
                send_slack_message(
                    "#customer-success-tofu-lite",
                    f"New tofu lite user {user.username} ({user.full_name}) is auto verified",
                )
        except Exception as e:
            logging.error(f"Failed to send slack message: {e}")

        user.save(using=self._db)

        if playbook is None and company_domain:
            playbook = Playbook.objects.filter(company_domain=company_domain).first()
            logging.info(f"Auto linking playbook {playbook} for user {username}")

        if playbook is None:
            logging.info(f"Creating playbook for user {username}")
            company_info = CompanyInfo.objects.create()
            playbook = Playbook.objects.create(
                company_domain=company_domain, company_object=company_info
            )
            playbook_user_type = "creator"
        else:
            playbook_user_type = "user"

        PlaybookUser.objects.create(
            playbook=playbook, user=user, type=playbook_user_type
        )

        return user

    def create_superuser(self, username, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        return self.create_user(username, password, **extra_fields)

    def is_tofu_lite(self, user):
        return user.customer_type == TofuUser.CustomerType.LITE


class TofuUser(AbstractBaseUser, PermissionsMixin):
    class CustomerType(models.TextChoices):
        ADMIN = "admin", "Admin"
        NORMAL = "normal", "Normal"
        LITE = "lite", "Lite"

    username = models.CharField(max_length=150, unique=True)
    full_name = models.CharField(max_length=150, blank=True)
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    email = models.EmailField(blank=True)
    context = models.JSONField(blank=True, null=True, default=dict)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(default=timezone.now)
    customer_type = models.CharField(
        max_length=150, choices=CustomerType.choices, default=CustomerType.NORMAL
    )
    credits_available = models.IntegerField(default=0)
    credits_last_updated = models.DateTimeField(null=True, blank=True)
    stripe_checkout_session_id = models.CharField(max_length=255, blank=True, null=True)
    stripe_customer_id = models.CharField(
        max_length=255, blank=True, null=True, unique=True
    )
    stripe_payment_id = models.CharField(max_length=255, blank=True, null=True)

    class TofuLiteSubscriptionTier(models.TextChoices):
        FREE_TIER = "free_tier", "Free Tier"
        TIER_1 = "tier_1", "Tier 1"
        TIER_2 = "tier_2", "Tier 2"

    tofu_lite_subscription_tier = models.CharField(
        max_length=150,
        choices=TofuLiteSubscriptionTier.choices,
        default=TofuLiteSubscriptionTier.FREE_TIER,
    )

    objects = TofuUserManager()

    EMAIL_FIELD = "email"
    USERNAME_FIELD = "username"
    REQUIRED_FIELDS = []

    def clean(self):
        super().clean()
        self.email = self.__class__.objects.normalize_email(self.email)

    def get_full_name(self):
        """
        Return the first_name plus the last_name, with a space in between.
        """
        full_name = "%s %s" % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        """Return the short name for the user."""
        return self.first_name

    def is_eligible_for_mock(self):
        return self.context.get("enable_model_mock", False)

    def is_eligible_for_llm_cache(self):
        if self.is_eligible_for_mock():
            return False
        if self.context.get("enable_model_llm_cache", False):
            return True
        return self.username in [
            "tofuadmin-export-e2e-3",
            "tofuadmin-test-be",
            "e2etest-homepage-newuser",
        ] or any(
            self.username.startswith(x)
            for x in ["tofuadmin-e2etest", "e2etest", "loadtest"]
        )

    def is_eligible_for_internal_features(self):
        return self.username == "tofuadmin" or self.username.startswith("tofuadmin-")

    def is_eligible_for_auto_select_components(self):
        return self.context.get("enable_auto_select_components", False)


class ObjectInfo(models.Model):
    docs = models.JSONField(blank=True, null=True)
    meta = models.JSONField(blank=True, null=True)

    summary = models.TextField(blank=True, null=True)
    index = models.JSONField(blank=True, null=True)
    additional_info = models.JSONField(blank=True, null=True)

    docs_last_build = models.JSONField(blank=True, null=True)
    docs_build_status = models.JSONField(blank=True, null=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    class Meta:
        abstract = True


class CompanyInfo(ObjectInfo):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self.docs:
            company_name_id = str(uuid.uuid4())
            company_website_id = str(uuid.uuid4())
            company_description_id = str(uuid.uuid4())

            self.docs = {
                company_name_id: {
                    "id": company_name_id,
                    "meta": {"field_name": "Company Name"},
                    "type": "text",
                    "value": "",
                    "position": 1,
                    "immutableField": True,
                },
                company_website_id: {
                    "id": company_website_id,
                    "meta": {"field_name": "Company Website"},
                    "type": "url",
                    "value": "",
                    "position": 2,
                    "immutableField": True,
                },
                company_description_id: {
                    "id": company_description_id,
                    "meta": {"field_name": "Company Description"},
                    "type": "text",
                    "value": "",
                    "position": 3,
                    "immutableField": True,
                },
            }
            self.meta = {
                "position": 1,
            }


class Playbook(models.Model):
    users = models.ManyToManyField(
        TofuUser, through="PlaybookUser", through_fields=("playbook", "user")
    )
    name = models.CharField(max_length=255, blank=True, default="My Playbook")
    company_domain = models.CharField(max_length=255, blank=True, null=True)
    company_info = models.JSONField(blank=True, null=True)
    company_info_expanded = models.JSONField(blank=True, null=True)
    target_info = models.JSONField(blank=True, null=True)
    target_info_expanded = models.JSONField(blank=True, null=True)
    assets = models.JSONField(blank=True, null=True)
    assets_expanded = models.JSONField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    company_object = models.OneToOneField(CompanyInfo, on_delete=models.CASCADE)
    custom_instructions = models.JSONField(blank=True, null=True)

    settings = models.JSONField(blank=True, null=True, default=dict)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self.company_info:

            def convert_company_object_to_info(company_object):
                company_info = {}
                company_info["meta"] = company_object.meta
                for k, v in company_object.docs.items():
                    field_name = v.get("meta", {}).get("field_name")
                    if not field_name:
                        logging.error(
                            f"Field name is missing for {k} in init value of company object: {company_object}"
                        )
                        continue

                    company_info[field_name] = {
                        "meta": {"position": v.get("position")},
                        "data": [
                            {
                                "id": k,
                                "type": v.get("type"),
                                "value": v.get("value"),
                                "immutableField": v.get("immutableField", False),
                            }
                        ],
                    }
                return company_info

            self.company_info = convert_company_object_to_info(self.company_object)
        if not self.target_info:
            self.target_info = {
                "meta": {"position": 2},
            }
        if not self.assets:
            self.assets = {
                "meta": {"position": 3},
            }

    def get_owner_or_first_user(self):
        try:
            owner = (
                self.playbookuser_set.filter(type="owner")
                .select_related("user")
                .first()
            )
            if owner:
                return owner.user
            else:
                first_user = self.playbookuser_set.select_related("user").first()
                if first_user:
                    return first_user.user
        except PlaybookUser.DoesNotExist:
            return None
        return None


class TargetInfoGroup(models.Model):
    playbook = models.ForeignKey(
        Playbook, on_delete=models.CASCADE, related_name="target_info_groups"
    )
    target_info_group_key = models.CharField(max_length=255)
    meta = models.JSONField(blank=True, null=True)
    status = models.JSONField(default=dict(), blank=True, null=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["playbook", "target_info_group_key"],
                name="unique_target_info_group_key_for_playbook",
            )
        ]


class AssetInfoGroup(models.Model):
    playbook = models.ForeignKey(
        Playbook, on_delete=models.CASCADE, related_name="asset_info_groups"
    )
    asset_info_group_key = models.CharField(max_length=255)
    meta = models.JSONField(blank=True, null=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["playbook", "asset_info_group_key"],
                name="unique_asset_info_group_key_for_playbook",
            )
        ]


class TargetInfo(ObjectInfo):
    target_info_group = models.ForeignKey(
        TargetInfoGroup, on_delete=models.CASCADE, related_name="targets"
    )
    target_key = models.CharField(max_length=255)

    value_prop = models.TextField(blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["target_info_group", "target_key"],
                name="unique_target_key_for_group",
            )
        ]


class AssetInfo(ObjectInfo):
    asset_info_group = models.ForeignKey(
        AssetInfoGroup, on_delete=models.CASCADE, related_name="assets"
    )
    asset_key = models.CharField(max_length=255)

    brand_audit = models.JSONField(blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["asset_info_group", "asset_key"],
                name="unique_asset_key_for_group",
            )
        ]


class PlaybookUser(models.Model):
    playbook = models.ForeignKey(Playbook, on_delete=models.CASCADE)
    user = models.ForeignKey(TofuUser, on_delete=models.CASCADE)
    type = models.CharField(max_length=64)


class Campaign(models.Model):
    creator = models.ForeignKey(TofuUser, on_delete=models.SET_NULL, null=True)
    playbook = models.ForeignKey(Playbook, on_delete=models.CASCADE, null=True)
    campaign_name = models.CharField(max_length=255, blank=True)
    campaign_params = models.JSONField(blank=True, null=True)
    campaign_status = models.JSONField(default=dict, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)


class Action(models.Model):
    """
    Represents an action in the workflow system.
    Actions can be connected to form a directed graph of operations.
    """

    creator = models.ForeignKey(TofuUser, on_delete=models.CASCADE)
    playbook = models.ForeignKey(Playbook, on_delete=models.CASCADE)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE)

    action_name = models.CharField(max_length=255)
    action_category = models.CharField(max_length=255)

    incoming_actions = models.ManyToManyField(
        "self",
        through="ActionEdge",
        through_fields=("from_action", "to_action"),
        symmetrical=False,
        related_name="outgoing_actions",
    )

    # TODO: put a data schema in validator or protobuf
    inputs = models.JSONField(blank=True)
    outputs = models.JSONField(blank=True)
    status = models.JSONField(blank=True, default=dict())

    meta = models.JSONField(blank=True, null=True, default=dict())

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    class Meta:
        indexes = [
            models.Index(fields=["campaign"]),
        ]
        ordering = ["-created_at"]


class ActionEdge(models.Model):
    """
    Represents a connection between two actions in the workflow.
    Defines how data flows from one action to another.
    """

    from_action = models.ForeignKey(
        "Action", on_delete=models.CASCADE, related_name="outgoing_edges"
    )
    to_action = models.ForeignKey(
        "Action", on_delete=models.CASCADE, related_name="incoming_edges"
    )
    # TODO: put a data schema in validator or protobuf
    config = models.JSONField(
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [["from_action", "to_action"]]
        indexes = [
            models.Index(fields=["from_action"]),
            models.Index(fields=["to_action"]),
        ]

    def __str__(self):
        return f"{self.from_action} → {self.to_action}"


class ContentGroup(models.Model):
    creator = models.ForeignKey(TofuUser, on_delete=models.SET_NULL, null=True)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE)
    action = models.ForeignKey(Action, on_delete=models.CASCADE, blank=True, null=True)
    content_group_name = models.CharField(max_length=255, blank=True)
    content_group_params = models.JSONField(blank=True, null=True)
    content_group_status = models.JSONField(default=dict, blank=True)
    components = models.JSONField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)


class Content(models.Model):
    creator = models.ForeignKey(TofuUser, on_delete=models.SET_NULL, null=True)
    playbook = models.ForeignKey(Playbook, on_delete=models.SET_NULL, null=True)
    content_group = models.ForeignKey(
        ContentGroup, on_delete=models.CASCADE, blank=True, null=True
    )
    content_name = models.CharField(max_length=255, blank=True)
    content_params = models.JSONField(blank=True, null=True)
    content_status = models.JSONField(default=dict, blank=True)
    components = models.JSONField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["content_group", "content_name"],
                name="unique_content_name_per_group",
            )
        ]


class ContentVariation(models.Model):
    content = models.ForeignKey(Content, on_delete=models.CASCADE, unique=True)
    params = models.JSONField(blank=True, null=True)
    variations = models.JSONField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)


class InboundTypeChoices(models.TextChoices):
    OUTBOUND = "outbound", "Outbound"
    PARTIAL_INBOUND = "partial_inbound", "Partial Inbound"
    FULL_INBOUND = "full_inbound", "Full Inbound"


class PublicContent(models.Model):
    creator = models.ForeignKey(TofuUser, on_delete=models.SET_NULL, null=True)
    source_content_variation = models.ForeignKey(
        ContentVariation, on_delete=models.SET_NULL, null=True
    )
    tofu_content_id = models.CharField(max_length=255, blank=True)
    tofu_slug = models.CharField(max_length=255, blank=True)
    content_id = models.IntegerField(blank=True, null=True)
    inbound_type = models.CharField(
        max_length=255,
        choices=InboundTypeChoices.choices,
        blank=True,
        null=True,
    )
    matching_criteria = ArrayField(
        models.CharField(max_length=1023),
        blank=True,
        default=list,
        null=True,
    )
    variations = models.JSONField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)
    is_tofu_lite = models.BooleanField(default=False)
    last_tofu_lite_deduction_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        unique_together = (
            "tofu_content_id",
            "tofu_slug",
        )


class Status(models.Model):
    playbook = models.ForeignKey(
        Playbook, on_delete=models.CASCADE, blank=True, null=True
    )
    type = models.CharField(max_length=64)
    status = models.CharField(max_length=64)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)


class CompanyDomain(models.Model):
    link_playbook_domain = models.CharField(max_length=255, blank=True, null=True)
    allow_register_domain = models.CharField(max_length=255, blank=True, null=True)


class LongText(models.Model):
    text = models.TextField(blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)


class ChatHistory(models.Model):
    creator = models.ForeignKey(TofuUser, on_delete=models.CASCADE)
    key = models.CharField(max_length=255, primary_key=True)
    model = models.CharField(max_length=255, blank=True)
    json = models.JSONField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)


class ContentTemplate(models.Model):
    creator = models.ForeignKey(TofuUser, on_delete=models.SET_NULL, null=True)
    playbook = models.ForeignKey(Playbook, on_delete=models.CASCADE, null=True)
    name = models.CharField(max_length=255, blank=True)
    template_data = models.JSONField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    # fields for v3 templates
    content_types = ArrayField(
        models.CharField(max_length=100), blank=True, default=list
    )
    default_content_types = ArrayField(
        models.CharField(max_length=100), blank=True, default=list
    )


class EventLogs(models.Model):
    event_type = models.CharField(max_length=255)

    user_id = models.IntegerField(blank=True, null=True)
    playbook_id = models.IntegerField(blank=True, null=True)
    campaign_id = models.IntegerField(blank=True, null=True)

    payload = models.JSONField(blank=True, null=True)

    created_at = models.DateTimeField(default=timezone.now, editable=False)


class OffsiteEventLogs(models.Model):
    session_id = models.CharField(max_length=255, primary_key=True)
    event_type = models.CharField(max_length=255)

    playbook_id = models.IntegerField(blank=True, null=True)
    campaign_id = models.IntegerField(blank=True, null=True)
    content_group_id = models.IntegerField(blank=True, null=True)
    content_id = models.IntegerField(null=True)
    content_variation_index = models.IntegerField(blank=True, null=True)
    tofu_slug = models.CharField(max_length=255, blank=True, null=True)

    ip_address = models.CharField(max_length=255, blank=True, null=True)
    user_agent = models.CharField(max_length=511, blank=True)

    payload = models.JSONField(blank=True, null=True)

    created_at = models.DateTimeField(default=timezone.now, editable=False)


class FulltextLLMCache(models.Model):
    """Django model for fulltext-indexed LLM Cache with prompt as JSONField."""

    prompt = models.JSONField()  # Storing structured JSON data
    llm = models.TextField()
    idx = models.IntegerField(null=True, blank=True)
    response = models.TextField(null=True, blank=True)
    # For searching, you might still maintain a SearchVectorField if you plan to index concatenated text for full-text search.
    prompt_tsv = SearchVectorField(null=True)

    created_at = models.DateTimeField(auto_now_add=True, editable=False)

    class Meta:
        indexes = [GinIndex(fields=["prompt_tsv"], name="idx_fulltext_prompt_tsv")]


AlchemyBase = declarative_base()


class FulltextLLMCacheAlchemy(AlchemyBase):
    """Postgres table for fulltext-indexed LLM Cache"""

    __tablename__ = "api_fulltextllmcache"
    id = Column(Integer, Sequence("cache_id"), primary_key=True)
    prompt = Column(String, nullable=False)
    llm = Column(String, nullable=False)
    idx = Column(Integer)
    response = Column(String)
    prompt_tsv = Column(
        TSVectorType(),
        Computed("to_tsvector('english', llm || ' ' || prompt)", persisted=True),
    )
    __table_args__ = (
        Index("idx_fulltext_prompt_tsv", prompt_tsv, postgresql_using="gin"),
    )


class UrlRecord(models.Model):
    source_url = models.CharField(max_length=2047, unique=True, blank=True)
    redirect_urls = models.JSONField(
        default=list, blank=True, null=True
    )  # Start with only one now

    class ExtractResult(models.TextChoices):
        ALWAYS_FAILS = "AF", "Always Fails"
        ALWAYS_SUCCEEDS = "AS", "Always Succeeds"
        SUCCEEDS_SOMETIMES = "SS", "Succeeds Sometimes"
        NO_RECORD = "NR", "No Record"

    crawling_status = models.CharField(
        max_length=16,
        choices=ExtractResult.choices,
        default=ExtractResult.NO_RECORD,
    )
    errors = models.JSONField(default=list, blank=True, null=True)  # List of errors
    docs = models.JSONField(blank=True, null=True)

    def __str__(self):
        return self.source_url

    @classmethod
    def get_or_create_url_data(cls, url):
        try:
            with transaction.atomic():
                url_handler, created = cls.objects.get_or_create(source_url=url)
                return url_handler
        except IntegrityError:
            # Handle race condition by fetching the existing object
            return cls.objects.get(source_url=url)


class DebugLogEntry(models.Model):
    entry_type = models.CharField(max_length=255)
    payload = models.JSONField()

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class FeatureAnnouncement(models.Model):
    class AnnouncementType(models.TextChoices):
        NEW_FEATURES = "NF", "New Features"
        TIPS_TUTORIALS = "TT", "Tips and Tutorials"
        WHATS_NEW = "WN", "Whats New"
        INCIDENT = "IC", "Incident"

    type = models.CharField(
        max_length=16,
        choices=AnnouncementType.choices,
        default=AnnouncementType.NEW_FEATURES,
    )
    is_shown = models.BooleanField(default=False)
    position = models.IntegerField(default=0)
    tag = models.CharField(max_length=255, blank=True, null=True)
    headline = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    link = models.URLField(blank=True, null=True)
    image_url = models.URLField(blank=True, null=True)
    video_url = models.URLField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)


class Tag(models.Model):
    creator = models.ForeignKey(TofuUser, on_delete=models.CASCADE)
    campaigns = models.ManyToManyField(Campaign, related_name="campaignTags")
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    color = models.CharField(max_length=255, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now, editable=False)

    class Meta:
        unique_together = (
            "creator",
            "name",
        )


class UserCreditAdjustment(models.Model):
    user = models.ForeignKey(TofuUser, on_delete=models.CASCADE)
    credit_adjustment = models.IntegerField(default=0)
    created_at = models.DateTimeField(default=timezone.now, editable=False)
    payment_info = models.JSONField(blank=True, null=True)
    additional_info = models.JSONField(blank=True, null=True)


class AutopilotRun(models.Model):
    playbook = models.ForeignKey(Playbook, on_delete=models.CASCADE)
    campaign = models.ForeignKey(
        Campaign, on_delete=models.SET_NULL, blank=True, null=True
    )
    target_info_group = models.ForeignKey(
        TargetInfoGroup, on_delete=models.SET_NULL, blank=True, null=True
    )
    created_at = models.DateTimeField(default=timezone.now, editable=False)
    updated_at = models.DateTimeField(auto_now=True)
    session_id = models.CharField(max_length=255, blank=True, null=True)

    class AutopilotActionType(models.TextChoices):
        CRM_SYNC = "crm_sync", "CRM Sync"
        TARGET_CREATION = "target_creation", "Target Creation"
        ALL_TARGETS_CREATION = "all_targets_creation", "All Targets Creation"
        CONTENT_GENERATION = "content_generation", "Content Generation"
        EXPORT = "export", "Export"

    autopilot_action_type = models.CharField(
        max_length=255, choices=AutopilotActionType.choices
    )
    status = models.JSONField(default=dict, blank=True, null=True)
    additional_info = models.JSONField(default=dict, blank=True, null=True)

    class Meta:
        indexes = [
            models.Index(fields=["campaign", "session_id"]),
        ]
        ordering = ["-created_at"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.status is None:
            self.status = {}
        if self.additional_info is None:
            self.additional_info = {}


@receiver(post_save, sender=ContentTemplate)
def ensure_unique_default_content_types(sender, instance, **kwargs):
    """
    Ensures that each default content type is only associated with one template per playbook.
    When a template is saved with default content types, those types are removed from
    other templates in the same playbook.
    """
    if not instance.playbook or not instance.default_content_types:
        return

    # Find templates in the same playbook that have any of the default content types
    # that this template has, but exclude this template itself
    templates_to_update = []

    # Get all affected templates in a single query
    affected_templates = ContentTemplate.objects.filter(
        playbook=instance.playbook,  # Same playbook
        default_content_types__overlap=instance.default_content_types,  # Has at least one overlapping content type
    ).exclude(
        id=instance.id  # Not the current template
    )

    # Prepare bulk update
    for template in affected_templates:
        # Remove any content types that are in the saved template's default_content_types
        template.default_content_types = [
            ct
            for ct in template.default_content_types
            if ct not in instance.default_content_types
        ]
        templates_to_update.append(template)

    # Perform a bulk update if there are templates to update
    if templates_to_update:
        ContentTemplate.objects.bulk_update(
            templates_to_update, ["default_content_types"]
        )


@receiver(post_delete, sender=ChatHistory)
def cleanup_thread_assets(sender, instance, **kwargs):
    """
    Clean up assets associated with a thread when it is deleted.
    """
    if not instance or not instance.json:
        return

    previous_messages = instance.json.get("previous_messages", [])
    if not isinstance(previous_messages, list):
        logging.error(f"Invalid previous_messages format in ChatHistory {instance.key}")
        return
    for message in previous_messages:
        thread_asset_ids = message.get("thread_asset_ids", [])
        if not isinstance(thread_asset_ids, list):
            logging.error(
                f"Invalid thread_asset_ids format in ChatHistory {instance.key}"
            )
            continue
        logging.info(f"Cleaning up thread assets {thread_asset_ids}")
        try:
            with transaction.atomic():
                # Filter out any invalid asset IDs
                valid_asset_ids = [
                    str(asset_id) for asset_id in thread_asset_ids if asset_id
                ]
                if valid_asset_ids:
                    # Only delete assets that exist
                    existing_assets = AssetInfo.objects.filter(id__in=valid_asset_ids)
                    if existing_assets.exists():
                        existing_assets.delete()
                        logging.info(
                            f"Deleted {existing_assets.count()} assets for chat {instance.key}"
                        )
        except Exception as e:
            logging.error(
                f"Error cleaning up thread assets for chat {instance.key}: {e}"
            )
            # Re-raise the exception to ensure the transaction is rolled back
            raise
