import concurrent.futures
import logging
import os
from enum import Enum

from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.utils import IntegrityError

from .feature.data_wrapper.data_wrapper import BaseContentWrapper
from .gen_status import Gen<PERSON>tatusUpdater
from .logger import log_content_creation
from .models import AssetInfo, Content, ContentGroup, ContentVariation, TofuUser
from .playbook_build.object_builder import ObjectBuilder
from .shared_definitions.protobuf.gen.action_define_pb2 import ActionCategory
from .shared_types import ContentType
from .utils import dict_to_tuple, iter_campaign_target_params, single_dict_to_tuple
from .validator.campaign_validator import CampaignGoal


class ContentGroupStatus(Enum):
    NOT_GENERATED = "NOT_GENERATED"
    GENERATE_ERROR = "GENERATE_ERROR"
    READY_TO_EXPORT = "READY_TO_EXPORT"
    GENERATED = "GENERATED"  # for repurposing only
    EXPORTED = "EXPORTED"


class ContentGroupHandler:
    def __init__(self, content_group) -> None:
        self.content_group = content_group
        self._is_targets_concat = self.content_group.campaign.campaign_params.get(
            "targets_concat", False
        )

    def is_p13n_content_group(self) -> bool:
        if self.content_group.action:
            return self.content_group.action.action_category == ActionCategory.Name(
                ActionCategory.ACTION_CATEGORY_PERSONALIZE
            )
        else:
            return (
                self.content_group.campaign.campaign_params.get("campaign_goal")
                == CampaignGoal.Personalization
            )

    def is_repurpose_content_group(self) -> bool:
        if self.content_group.action:
            return self.content_group.action.action_category == ActionCategory.Name(
                ActionCategory.ACTION_CATEGORY_REPURPOSE
            )
        else:
            return (
                self.content_group.campaign.campaign_params.get("campaign_goal")
                == CampaignGoal.Repurposing
            )

    def is_seq_personalize_template_content_group(self) -> bool:
        return (
            self.content_group.content_group_params.get("content_goal", None)
            == CampaignGoal.SeqPersonalizeTemplate
        )

    def is_eligible_for_autopilot(self) -> bool:
        if self.is_repurpose_content_group():
            return False
        # if any generated
        gen_status = GenStatusUpdater().get_content_group_gen_status_v3(
            self.content_group
        )
        stats = gen_status.get("stats")
        if not stats:
            return False
        if stats.cnts_succ == 0:
            return False
        return True

    def get_content_name_postfix(self, target_list):
        if self._is_targets_concat:
            if len(target_list) != 1:
                raise ValueError(
                    f"Target list shall has only one key and the value is a list with one element: {target_list}"
                )

            key, value = next(iter(target_list.items()))
            name_postfix = f"{key}_{value}"
        else:
            name_postfix = "-".join(value for _, value in sorted(target_list.items()))
        return name_postfix

    def get_content_name(self, target_list):
        name_postfix = self.get_content_name_postfix(target_list)
        content_name = f"{self.content_group.content_group_name}_{name_postfix}"
        return content_name

    def bulk_rename(self):
        with transaction.atomic():
            # Lock the content group first
            content_group = ContentGroup.objects.select_for_update().get(
                id=self.content_group.id
            )
            all_contents = Content.objects.filter(content_group=content_group)

            # First pass: collect all new names and identify conflicts
            name_mapping = {}  # content -> new_name
            contents_by_name = {}  # new_name -> list of contents

            # Map contents to their new names
            for content in all_contents:
                if not content.content_params:
                    logging.error(
                        f"Content {content.id} has no content_params inside bulk_rename"
                    )
                    continue

                target_list = content.content_params.get("targets", {})
                new_name = self.get_content_name(target_list)
                name_mapping[content] = new_name

                if new_name not in contents_by_name:
                    contents_by_name[new_name] = []
                contents_by_name[new_name].append(content)

            # Process contents based on naming conflicts
            contents_to_update = []
            contents_to_delete = []

            for new_name, contents in contents_by_name.items():
                if len(contents) > 1:
                    # Keep the first content, delete the rest
                    contents_to_update.append(contents[0])
                    contents_to_delete.extend(contents[1:])
                else:
                    # No conflict for this name
                    contents_to_update.extend(contents)

            # Perform updates and deletions
            try:
                # Delete conflicting contents first
                if contents_to_delete:
                    delete_ids = [content.id for content in contents_to_delete]
                    Content.objects.filter(id__in=delete_ids).delete()
                    logging.info(
                        f"Deleted {len(contents_to_delete)} conflicting contents during bulk_rename"
                    )

                # Update remaining contents with their new names
                for content in contents_to_update:
                    content.content_name = name_mapping[content]
                Content.objects.bulk_update(contents_to_update, ["content_name"])
                logging.info(
                    f"Successfully updated {len(contents_to_update)} content names"
                )

            except Exception as e:
                logging.exception(f"Error during bulk_rename operations: {str(e)}")
                raise

    def create_content_from_group(self, target_dict):
        return self.create_content(
            tofu_user=self.content_group.creator,
            playbook=self.content_group.campaign.playbook,
            content_group=self.content_group,
            content_type=self.content_group.content_group_params.get("content_type"),
            content_source_format=self.content_group.content_group_params.get(
                "content_source_format"
            ),
            content_source=self.content_group.content_group_params.get(
                "content_source"
            ),
            content_source_copy=self.content_group.content_group_params.get(
                "content_source_copy"
            ),
            content_source_upload_method=self.content_group.content_group_params.get(
                "content_source_upload_method"
            ),
            components=self.content_group.components,
            target_list=target_dict,
        )

    def create_content(
        self,
        tofu_user,
        playbook,
        content_group,
        content_type,
        content_source_format,
        content_source,
        content_source_copy,
        content_source_upload_method,
        components,
        target_list,
    ):
        try:
            with transaction.atomic():
                if not target_list:
                    content_name = self.content_group.content_group_name
                else:
                    content_name = self.get_content_name(target_list)

                content_params = {
                    "content_type": content_type,
                    "content_source_format": content_source_format,
                    "content_source": content_source,
                    "content_source_copy": content_source_copy,
                    "content_source_upload_method": content_source_upload_method,
                    "targets": target_list,
                }
                if not target_list:
                    content_params.pop("targets")
                content_status = {
                    "gen_status": {"status": "NOT_STARTED"},
                }
                new_content = Content.objects.create(
                    creator=tofu_user,
                    playbook=playbook,
                    content_group=content_group,
                    content_name=content_name,
                    content_params=content_params,
                    content_status=content_status,
                    components=components,
                )
                log_content_creation(content=new_content, user_id=tofu_user.id)
                return new_content
        except IntegrityError:
            logging.warning(f"Content already exists: {target_list}")
        except Exception as e:
            logging.exception(
                f"Error creating content for target: {target_list}, content group: {content_group.id} due to {str(e)}"
            )

    def _create_contents_sequentially(
        self,
        all_target_flattened,
        tofu_user,
        playbook,
        content_type,
        content_source_format,
        content_source,
        content_source_copy,
        content_source_upload_method,
        components,
    ):
        new_contents = []

        for target_list in all_target_flattened:
            if not isinstance(target_list, dict):
                logging.error(f"Invalid target set, dict is expected: {target_list}")
                continue

            new_content = self.create_content(
                tofu_user,
                playbook,
                self.content_group,
                content_type,
                content_source_format,
                content_source,
                content_source_copy,
                content_source_upload_method,
                components,
                target_list,
            )
            if new_content:
                new_contents.append(new_content)

        return new_contents

    def _create_contents_parallel(
        self,
        all_target_flattened,
        tofu_user,
        playbook,
        content_type,
        content_source_format,
        content_source,
        content_source_copy,
        content_source_upload_method,
        components,
    ):
        def create_content(args):
            (
                tofu_user,
                playbook,
                content_group,
                content_type,
                content_source_format,
                content_source,
                content_source_copy,
                content_source_upload_method,
                components,
                target_list,
            ) = args
            return self.create_content(
                tofu_user,
                playbook,
                content_group,
                content_type,
                content_source_format,
                content_source,
                content_source_copy,
                content_source_upload_method,
                components,
                target_list,
            )

        new_contents = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for target_list in all_target_flattened:
                if not isinstance(target_list, dict):
                    logging.error(
                        f"Invalid target set, dict is expected: {target_list}"
                    )
                    continue

                args = (
                    tofu_user,
                    playbook,
                    self.content_group,
                    content_type,
                    content_source_format,
                    content_source,
                    content_source_copy,
                    content_source_upload_method,
                    components,
                    target_list,
                )
                futures.append(executor.submit(create_content, args))

            for future in concurrent.futures.as_completed(futures):
                new_content = future.result()
                if new_content:
                    new_contents.append(new_content)

        return new_contents

    # Main function
    def _create_contents_batch(
        self,
        targets,
        is_targets_concat,
        tofu_user,
        playbook,
        content_type,
        content_source_format,
        content_source,
        content_source_copy,
        content_source_upload_method,
        components,
    ):

        all_target_flattened = list(
            iter_campaign_target_params(targets, is_targets_concat)
        )
        # env or number
        create_sequentially = (
            os.environ.get("TOFU_ENV") == "unit_test" or len(all_target_flattened) < 10
        )
        if create_sequentially:
            new_contents = self._create_contents_sequentially(
                all_target_flattened,
                tofu_user,
                playbook,
                content_type,
                content_source_format,
                content_source,
                content_source_copy,
                content_source_upload_method,
                components,
            )
        else:
            new_contents = self._create_contents_parallel(
                all_target_flattened,
                tofu_user,
                playbook,
                content_type,
                content_source_format,
                content_source,
                content_source_copy,
                content_source_upload_method,
                components,
            )
        return new_contents

    def _bulk_create_content_for_purpose(
        self,
        tofu_user,
        playbook,
        content_type,
        content_source_format,
        content_source,
        content_source_copy,
        content_source_upload_method,
        components,
    ):
        new_contents = []
        # only create new content if the content_group has none for repurposing.
        if Content.objects.filter(content_group=self.content_group).exists():
            return new_contents
        new_content = self.create_content(
            tofu_user,
            playbook,
            self.content_group,
            content_type,
            content_source_format,
            content_source,
            content_source_copy,
            content_source_upload_method,
            components,
            {},
        )
        if new_content:
            new_contents.append(new_content)

        return new_contents

    def _bulk_create_content_for_seq_personalize_template(
        self,
        tofu_user,
        playbook,
        content_type,
        content_source_format,
        content_source,
        content_source_copy,
        content_source_upload_method,
        components,
    ):
        def get_seq_personalize_template_targets():
            targets = self.content_group.content_group_params.get("selected_targets")
            if targets:
                return [targets]

            def get_first_target(targets):
                if not targets:
                    return None

                def get_all_valid_pairs():
                    pairs = []
                    for target_dict in targets:
                        for key, value in target_dict.items():
                            if value:  # Only include non-empty values
                                if not isinstance(value, list):
                                    raise ValueError(
                                        f"Value for key {key} is not a list: {value}"
                                    )
                                else:
                                    for v in value:
                                        pairs.append((key, v))
                    return pairs

                pairs = get_all_valid_pairs()
                if not pairs:
                    return None

                # Find smallest key-value pair lexicographically
                first_key, first_value = min(pairs)
                return {first_key: [first_value]}

            first_target = get_first_target(
                self.content_group.campaign.campaign_params.get("targets")
            )
            targets = [first_target] if first_target else []
            return targets

        targets = get_seq_personalize_template_targets()
        if not targets:
            return []
        first_target = targets
        if isinstance(first_target, list):
            first_target = first_target[0]
        if not isinstance(first_target, dict):
            raise ValueError(f"Invalid targets: {targets}")
        first_key = next(iter(first_target.keys()))
        first_value = first_target[first_key]
        if isinstance(first_value, list):
            if not first_value:
                raise ValueError(f"Invalid targets: {targets}")
            first_value = first_value[0]
        flatterned_targets = {first_key: first_value}
        # save the flatterned targets to the content group
        all_existing_contents = Content.objects.filter(content_group=self.content_group)
        if (
            len(all_existing_contents) == 1
            and all_existing_contents[0].content_params.get("targets")
            == flatterned_targets
        ):
            return []
        else:
            with transaction.atomic():
                self.content_group.content_group_params["selected_targets"] = (
                    flatterned_targets
                )
                self.content_group.save(update_fields=["content_group_params"])
                # delete all existing contents
                for content in all_existing_contents:
                    content.delete()
                # Use sequential version instead of parallel
                return self._create_contents_batch(
                    targets,
                    False,
                    tofu_user,
                    playbook,
                    content_type,
                    content_source_format,
                    content_source,
                    content_source_copy,
                    content_source_upload_method,
                    components,
                )

    def _bulk_create_content_for_personalize(
        self,
        targets,
        tofu_user,
        playbook,
        content_type,
        content_source_format,
        content_source,
        content_source_copy,
        content_source_upload_method,
        components,
    ):
        has_targets_passed = bool(targets)
        if not targets:
            targets = self.content_group.campaign.campaign_params.get("targets")

        # this is the flag to distinguish between the two use cases mentioned above the function's definition
        is_targets_concat = self.content_group.campaign.campaign_params.get(
            "targets_concat", False
        )
        existing_contents = Content.objects.filter(content_group=self.content_group)
        existing_targets = set(
            [
                single_dict_to_tuple(c.content_params.get("targets", {}))
                for c in existing_contents
            ]
        )

        all_targets = list(iter_campaign_target_params(targets, is_targets_concat))
        all_targets_tuples = set([single_dict_to_tuple(t) for t in all_targets])
        # deletion for non-existing targets
        if not has_targets_passed:
            # Get existing reviewed content list
            reviewed_content_list = self.content_group.content_group_params.get(
                "reviewed_content_list", []
            )
            deleted_content_ids = set()
            # Delete contents that are not in the campaign targets.
            # This is only for the case where no targets are passed
            for content in existing_contents:
                if (
                    single_dict_to_tuple(content.content_params.get("targets", {}))
                    not in all_targets_tuples
                ):
                    deleted_content_ids.add(content.id)
                    content.delete()
            if reviewed_content_list and deleted_content_ids:
                reviewed_content_list = [
                    reviewed_content
                    for reviewed_content in reviewed_content_list
                    if reviewed_content.get("content_id") not in deleted_content_ids
                ]
                self.content_group.content_group_params["reviewed_content_list"] = (
                    reviewed_content_list
                )
                self.content_group.save(update_fields=["content_group_params"])
        # Find targets that don't exist yet using set difference
        new_targets_to_create = list(all_targets_tuples - existing_targets)
        logging.info(
            f"new_targets_to_create for content group {self.content_group.id}: {new_targets_to_create}"
        )
        new_contents = self._create_contents_batch(
            new_targets_to_create,
            is_targets_concat,
            tofu_user,
            playbook,
            content_type,
            content_source_format,
            content_source,
            content_source_copy,
            content_source_upload_method,
            components,
        )
        logging.info(
            f"number of new contents created for content group {self.content_group.id}: {len(new_contents)}"
        )
        return new_contents

    def bulk_create_content(self, targets=[]):
        self.content_group.refresh_from_db()

        tofu_user = self.content_group.creator
        playbook = self.content_group.campaign.playbook

        content_type = self.content_group.content_group_params.get("content_type")
        if not content_type:
            raise ValidationError(
                f"No content type set in content group creation: {self.content_group.id}"
            )
        content_source_format = self.content_group.content_group_params.get(
            "content_source_format"
        )
        content_source = self.content_group.content_group_params.get("content_source")
        content_source_copy = self.content_group.content_group_params.get(
            "content_source_copy"
        )
        content_source_upload_method = self.content_group.content_group_params.get(
            "content_source_upload_method"
        )
        components = self.content_group.components

        new_contents = []
        try:
            if self.is_repurpose_content_group():
                new_contents = self._bulk_create_content_for_purpose(
                    tofu_user=tofu_user,
                    playbook=playbook,
                    content_type=content_type,
                    content_source_format=content_source_format,
                    content_source=content_source,
                    content_source_copy=content_source_copy,
                    content_source_upload_method=content_source_upload_method,
                    components=components,
                )
            elif self.is_seq_personalize_template_content_group():
                new_contents = self._bulk_create_content_for_seq_personalize_template(
                    tofu_user=tofu_user,
                    playbook=playbook,
                    content_type=content_type,
                    content_source_format=content_source_format,
                    content_source=content_source,
                    content_source_copy=content_source_copy,
                    content_source_upload_method=content_source_upload_method,
                    components=components,
                )
            else:
                new_contents = self._bulk_create_content_for_personalize(
                    targets=targets,
                    tofu_user=tofu_user,
                    playbook=playbook,
                    content_type=content_type,
                    content_source_format=content_source_format,
                    content_source=content_source,
                    content_source_copy=content_source_copy,
                    content_source_upload_method=content_source_upload_method,
                    components=components,
                )
        except Exception as e:
            logging.exception(
                f"Failed to run bulk_create_content for content group {self.content_group.id}: {e}"
            )

        logging.info(f"Num of Contents created in bulk creation: {len(new_contents)}")
        return new_contents

    def bulk_copy_generations(self, src_content_group):
        src_contents = Content.objects.filter(content_group=src_content_group)
        dest_contents = Content.objects.filter(content_group=self.content_group)
        mapping_src_contents = {
            self.get_content_name_postfix(
                content.content_params.get("targets", {})
            ): content
            for content in src_contents
        }

        with transaction.atomic():
            all_contents_to_update = []
            for dest_content in dest_contents:
                dest_content_name_postfix = self.get_content_name_postfix(
                    dest_content.content_params.get("targets", {})
                )
                src_content = mapping_src_contents.get(dest_content_name_postfix)
                if not src_content:
                    logging.error(
                        f"No source content found for {dest_content_name_postfix}"
                    )
                    continue

                try:
                    src_variation = ContentVariation.objects.get(content=src_content)
                    ContentVariation.objects.update_or_create(
                        content=dest_content,
                        defaults={
                            "params": src_variation.params,
                            "variations": src_variation.variations,
                        },
                    )
                    # we also need to update the content status
                    dest_content.content_status = src_content.content_status
                    all_contents_to_update.append(dest_content)
                except ContentVariation.DoesNotExist:
                    logging.warning(
                        f"No ContentVariation found for source content {src_content.id}"
                    )
                except Exception as e:
                    logging.error(f"Error copying content variation: {str(e)}")

            Content.objects.bulk_update(all_contents_to_update, ["content_status"])

    def set_edits(self, content_ids, components, variations):
        apply_to_all = not content_ids
        if apply_to_all:
            contents = Content.objects.filter(content_group=self.content_group)
        else:
            contents = Content.objects.filter(
                content_group=self.content_group, id__in=content_ids
            )
        if not contents:
            raise ValidationError(
                f"No content found in content group {self.content_group.id} for the given list {content_ids}"
            )

        # validation check
        for component_id in variations:
            if (
                component_id not in self.content_group.components
                and component_id not in components
            ):
                raise ValidationError(
                    f"Component {component_id} in variation is not defined in content_group {self.content_group.id}"
                )

        # updae components definition
        if components:
            self.content_group.components.update(components)
            # save components field to database
            self.content_group.save(update_fields=["components"])

        content_variations = ContentVariation.objects.filter(content__in=contents)
        content_vairation_dict = {cv.content_id: cv for cv in content_variations}
        for content in contents:
            if content.id in content_vairation_dict:
                content_variation = content_vairation_dict[content.id]
                content_variation.variations.update(variations)
                content_variation.save(update_fields=["variations"])
            else:
                variation_params = {
                    "targets": (
                        content.content_params.get("targets", {})
                        if content.content_params
                        else {}
                    ),
                }
                content_variation = ContentVariation.objects.create(
                    content=content,
                    variations=variations,
                    params=variation_params,
                )
                content_variation.save()

        return self.content_group

    def delete_components(self, content_ids, component_ids):
        apply_to_all = not content_ids
        if apply_to_all:
            contents = Content.objects.filter(content_group=self.content_group)
        else:
            contents = Content.objects.filter(
                content_group=self.content_group, id__in=content_ids
            )

        with transaction.atomic():
            if apply_to_all:
                # Remove components from content group
                for component_id in component_ids:
                    self.content_group.components.pop(component_id, None)
                # Save components field to database
                self.content_group.save(update_fields=["components"])

            content_variations = ContentVariation.objects.filter(content__in=contents)
            updated_content_variations = []
            for content_variation in content_variations:
                updated = False
                for component_id in component_ids:
                    if content_variation.variations.pop(component_id, None) is not None:
                        updated = True
                if updated:
                    updated_content_variations.append(content_variation)

            ContentVariation.objects.bulk_update(
                updated_content_variations, ["variations"]
            )

        return self.content_group

    def update_status(self):
        # exported
        status = ContentGroupStatus.NOT_GENERATED  # default value
        campaign_goal = self.content_group.campaign.campaign_params.get("campaign_goal")
        gen_status = self.content_group.content_group_status.get("gen_status", {}).get(
            "status"
        )

        def has_exported(export_settings):
            def has_any_target_exported(targets_setting):
                for target_setting in targets_setting:
                    if target_setting.get("exportStatus") == "Completed":
                        return True
                return False

            def check_platform_exports(platform_settings):
                for (
                    _export_content_type,
                    export_content_type_settings,
                ) in platform_settings.items():
                    if not isinstance(export_content_type_settings, dict):
                        continue
                    for (
                        _export_method,
                        export_method_settings,
                    ) in export_content_type_settings.items():
                        if not isinstance(export_method_settings, dict):
                            continue
                        if has_any_target_exported(
                            export_method_settings.get("targetsSetting", [])
                        ):
                            return True
                return False

            export_destination = export_settings.get("exportDestination", None)
            if export_destination:
                platform_settings = export_settings.get(export_destination, None)
                if platform_settings:
                    return check_platform_exports(platform_settings)
            return False

        if campaign_goal == "Repurpose Content":
            if gen_status == "ERROR":
                status = ContentGroupStatus.GENERATE_ERROR
            elif gen_status == "FINISHED":
                status = ContentGroupStatus.GENERATED
        else:
            is_exported = (
                has_exported(
                    self.content_group.content_group_params.get("export_settings", {})
                )
                if self.content_group.content_group_params
                else False
            )
            if is_exported:
                status = ContentGroupStatus.EXPORTED
            else:
                if gen_status == "ERROR":
                    status = ContentGroupStatus.GENERATE_ERROR
                elif gen_status == "NOT_STARTED":
                    status = ContentGroupStatus.NOT_GENERATED
                else:
                    status = (
                        ContentGroupStatus.READY_TO_EXPORT
                        if campaign_goal == "Personalization"
                        else ContentGroupStatus.GENERATED
                    )
        if not self.content_group.content_group_status:
            self.content_group.content_group_status = {}
        if status.value == self.content_group.content_group_status.get("status"):
            return

        self.content_group.content_group_status["status"] = status.value
        self.content_group.save()

    def delete_results(self):
        with transaction.atomic():
            if self.content_group.components:
                component_ids_to_delete_result = [
                    component_id
                    for component_id, component_data in self.content_group.components.items()
                    if component_data.get("meta", {}).get("component_type", "")
                    != "edited"
                ]
                # Directly filter ContentVariation based on the related Content's content_group
                content_variations = ContentVariation.objects.filter(
                    content__content_group=self.content_group
                )
                for content_variation in content_variations:
                    for component_id in component_ids_to_delete_result:
                        content_variation.variations.pop(component_id, None)

                ContentVariation.objects.bulk_update(content_variations, ["variations"])

            # remove review contents
            self.content_group.content_group_params.pop("reviewed_content_list", None)
            # remove export response and settings
            self.content_group.content_group_params.pop("export_response", None)
            self.content_group.content_group_params.pop("export_settings", None)

            # update status
            self.content_group.content_group_status["status"] = (
                ContentGroupStatus.NOT_GENERATED.value
            )

            self.content_group.save(
                update_fields=["content_group_params", "content_group_status"]
            )
            # reset gen status
            GenStatusUpdater().reset_content_group_gen_status(self.content_group)
