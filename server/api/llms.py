import os

import boto3
from botocore.config import Config
from langchain_anthropic import ChatAnthropic
from langchain_aws import ChatBedrock
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_openai.chat_models.base import BaseChatOpenAI

from .llm.chat_mock import Chat<PERSON>ock


def model_is_json_enabled(model_name):
    if "gpt-4" in model_name and ("turbo" in model_name or "preview" in model_name):
        return True
    if "claude" in model_name:
        return True
    if "gpt-4o" in model_name:
        return True
    if "o1" in model_name:
        return True
    if "o3" in model_name:
        return True
    if "o4" in model_name:
        return True
    if "mock" in model_name:
        return True
    if "deepseek" in model_name:
        return True
    if "gemini" in model_name:
        return True
    return False


def get_llm_for_embeddings(model_name="text-embedding-3-large", **kwargs):
    kwargs.update(
        {
            "model": model_name,
        }
    )
    return (8000, OpenAIEmbeddings(**kwargs))


def get_llm_for_model_name(model_name, json_output=False, **kwargs):

    supported_models = [
        "gpt-4o-mini-2024-07-18",
        "gpt-4o-2024-11-20",
        "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        "claude-3-5-sonnet-20240620",
        "claude-3-5-sonnet-20241022",
        "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        "claude-3-7-sonnet-20250219",
        "claude-3-7-sonnet-20250219-thinking",
        "gemini-2.0-flash-exp",
        "o1-2024-12-17",
        "o3-2025-04-16",
        "o3-mini-2025-01-31",
        "mock",
        "deepseek-chat",
        "deepseek-reasoner",
        "gpt-4.5-preview",
        "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0",
        "gemini-2.5-pro-preview-03-25",
        "gemini-2.0-flash-exp-image-generation",
        "gpt-image-1",
        "gpt-4.1-2025-04-14",
        "gpt-4.1-mini-2025-04-14",
        "o4-mini-2025-04-16",
        "us.anthropic.claude-3-5-haiku-20241022-v1:0",
        "claude-sonnet-4-20250514",
        "claude-opus-4-20250514",
        "us.anthropic.claude-sonnet-4-20250514-v1:0",
        "us.anthropic.claude-opus-4-20250514-v1:0",
    ]
    if model_name not in supported_models:
        raise ValueError(f"Model {model_name} is not supported")

    if model_is_json_enabled(model_name) and "gpt" in model_name and json_output:
        # Update kwargs with json_object response format.
        kwargs.update({"model_kwargs": {"response_format": {"type": "json_object"}}})

    if "mock" in model_name:
        return ChatMock(**kwargs)
    elif model_name.startswith("anthropic.claude") or model_name.startswith(
        "us.anthropic"
    ):
        # bedrock fails if we pass max_tokens_to_sample
        if "max_tokens_to_sample" in kwargs:
            raise ValueError(
                "max_tokens_to_sample must not be specified for Bedrock model"
            )
        # check that kwargs has max_tokens
        if "max_tokens" not in kwargs:
            raise ValueError("max_tokens must be specified for Bedrock model")
        # check that kwargs does not have n or max_retries
        if "n" in kwargs or "max_retries" in kwargs:
            raise ValueError(
                "n and max_retries must not be specified for Bedrock model"
            )

        config = Config(
            connect_timeout=60 * 5,
            read_timeout=60 * 5,
        )
        bedrock_client = boto3.client(
            service_name="bedrock-runtime", region_name="us-west-2", config=config
        )
        kwargs.pop("model_name", None)
        return ChatBedrock(
            client=bedrock_client, model_id=model_name, model_kwargs=kwargs
        )

    elif "claude" in model_name:
        # check that kwargs has max_tokens_to_sample and max_tokens
        if "max_tokens_to_sample" not in kwargs or "max_tokens" not in kwargs:
            raise ValueError(
                "max_tokens_to_sample and max_tokens must be specified for Claude models"
            )
        # check that kwargs does not have n or max_retries
        if "n" in kwargs or "max_retries" in kwargs:
            raise ValueError(
                "n and max_retries must not be specified for Claude models"
            )
        return ChatAnthropic(model_name=model_name, **kwargs)
    elif "deepseek" in model_name:
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            raise ValueError("DEEPSEEK_API_KEY environment variable is not set")
        return BaseChatOpenAI(
            model_name=model_name,
            openai_api_key=api_key,
            openai_api_base="https://api.deepseek.com",
            **kwargs,
        )
    elif "gemini" in model_name:
        return ChatGoogleGenerativeAI(model=model_name, **kwargs)
    else:
        if "n" not in kwargs or "max_retries" not in kwargs:
            raise ValueError("n and max_retries must be specified")
        return ChatOpenAI(model_name=model_name, **kwargs)
