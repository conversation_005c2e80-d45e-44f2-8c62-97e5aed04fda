import hashlib
import unittest
from datetime import datetime
from unittest.mock import MagicMock, patch

from django.core.cache import cache

from ..playbook_build.object_builder_target import TargetObjectBuilder


class TestTargetObjectBuilder(unittest.TestCase):

    def setUp(self):
        self.mock_object = MagicMock()
        self.mock_object.id = "test_id"
        self.mock_object.target_key = "Test Company"
        self.mock_object.additional_info = {}
        self.builder = TargetObjectBuilder(self.mock_object)

    def test_tofu_research_full_query(self):
        query = "What are the latest products?"
        result = self.builder._tofu_research_full_query(query)

        # Check each line separately
        expected_lines = [
            "What are the latest products?",
            "Search for: Test Company",
            "Additional info about the entity:",
        ]

        result_lines = result.split("\n")

        # Check the first 3 lines exactly
        for expected, actual in zip(expected_lines, result_lines[:3]):
            self.assertEqual(expected, actual)

        # Check that the last line contains the mock object without checking the specific ID
        self.assertIn("MagicMock name='mock.meta.get()'", result_lines[3])
        self.assertIn("Test Company", result_lines[3])

    def test_tofu_research_impl(self):
        with patch("requests.request") as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "Test research result"}}],
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 20,
                    "total_tokens": 30,
                },
            }
            mock_request.return_value = mock_response

            result = self.builder._tofu_research_impl("Test query")
            self.assertEqual(result, "Test research result")

    def test_tofu_research(self):
        with patch(
            "api.playbook_build.object_builder_target.TargetObjectBuilder._tofu_research_impl"
        ) as mock_research_impl:
            mock_research_impl.return_value = "Cached research result"

            # Generate the cache key in the same way as the TargetObjectBuilder class
            full_query = self.builder._tofu_research_full_query("Test query")
            full_query_key = hashlib.md5(full_query.encode()).hexdigest()
            cache_key = f"tofu_research:{self.mock_object.id}:{full_query_key}"

            cache.set(cache_key, "Cached research result")

            result = self.builder.tofu_research("query1", "Test query")
            self.assertEqual(result, "Cached research result")
            self.assertIn("tofu_research", self.mock_object.additional_info)
            self.assertIn("query1", self.mock_object.additional_info["tofu_research"])

    def test_delete_tofu_research(self):
        # Set up initial state
        self.mock_object.additional_info = {
            "tofu_research": {"query1": {"result": "Test result"}}
        }

        # Set up cache
        query = "Test query"
        full_query = self.builder._tofu_research_full_query(query)
        full_query_key = hashlib.md5(full_query.encode()).hexdigest()
        cache_key = f"tofu_research:{self.mock_object.id}:{full_query_key}"
        cache.set(cache_key, "Cached result")

        # Test deletion of existing query
        result = self.builder.delete_tofu_research("query1")
        self.assertTrue(result)
        self.assertNotIn("query1", self.mock_object.additional_info["tofu_research"])

        # Test deletion of non-existent query
        result = self.builder.delete_tofu_research("non_existent_query")
        self.assertFalse(result)


if __name__ == "__main__":
    unittest.main()
