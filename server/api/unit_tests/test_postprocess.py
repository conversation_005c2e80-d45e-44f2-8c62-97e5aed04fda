from unittest.mock import Mock, patch

import pytest

from ..content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from ..shared_types import ContentSourceFormat, ContentType
from ..utils import fix_content_html_tags


def test_pure_text():
    input_text = "This is pure text"
    assert fix_content_html_tags(input_text) == input_text


def test_valid_html():
    input_html = "<b>Bold text</b>"
    assert fix_content_html_tags(input_html) == input_html


def test_wrong_html_tag():
    input_html = "<b/>Unclosed bold</b>"
    expected_output = "<b>Unclosed bold</b>"
    assert fix_content_html_tags(input_html) == expected_output


def test_mixed_tags():
    input_html = "test<b/>test2</b>test3"
    expected_output = "test<b>test2</b>test3"
    assert fix_content_html_tags(input_html) == expected_output


def test_single_slash():
    input_html = "</>"
    expected_output = "</>"
    assert fix_content_html_tags(input_html) == expected_output


def test_strong_tag():
    input_html = "<strong/>test</strong>"
    expected_output = "<strong>test</strong>"
    assert fix_content_html_tags(input_html) == expected_output


@pytest.fixture
def postprocessor():
    gen_env = Mock()
    gen_env._data_wrapper.content_source_format = ContentSourceFormat.Text
    gen_env._data_wrapper.campaign_goal = "Personalization"
    gen_env._gen_settings.foundation_model = "gpt-4o-2024-11-20"
    gen_env._data_wrapper.content_type = ContentType.BlogPost

    # Update campaign_instance mock to include campaign_name
    campaign_instance_mock = Mock()
    campaign_instance_mock.id = "campaign_id"
    campaign_instance_mock.campaign_name = "Test Campaign"
    gen_env._data_wrapper.campaign_instance = campaign_instance_mock

    gen_env._data_wrapper.content_instance = Mock(id="content_id")
    gen_env._data_wrapper.playbook_instance = Mock(
        users=Mock(first=Mock(return_value="user_id"))
    )

    # Create a proper Mock for gen_settings with a dictionary for config_map
    gen_settings_mock = Mock()
    gen_settings_mock.config_map = {}  # or any specific test data you need
    gen_settings_mock.foundation_model = "gpt-4o-2024-11-20"
    gen_settings_mock.session_id = None
    gen_env._gen_settings = gen_settings_mock

    return DefaultContentGenPostprocessor(gen_env, Mock())


def test_postprocessor_properties(postprocessor):
    assert postprocessor.content_source_format == ContentSourceFormat.Text
    assert postprocessor.campaign_goal == "Personalization"
    assert postprocessor.foundation_model == "gpt-4o-2024-11-20"
    assert postprocessor.content_type == ContentType.BlogPost
    assert postprocessor.campaign_instance.id == "campaign_id"
    assert postprocessor.content_instance.id == "content_id"
    assert postprocessor.playbook_instance.users.first() == "user_id"


def test_postprocess_basic(postprocessor):
    example_content = {"text": "This is a test."}
    generations = ["This is a generated test."]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a generated test."]


def test_postprocess_clean_html_tags(postprocessor):
    example_content = {"text": "This is a test."}
    generations = ["<p>This is a <b>generated</b> test.</p>"]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a generated test."]


def test_postprocess_preserve_html(postprocessor):
    postprocessor._gen_env._data_wrapper.content_source_format = (
        ContentSourceFormat.Html
    )
    example_content = {"text": "<p>This is a test.</p>"}
    generations = ["<p>This is a <b>generated</b> test.</p>"]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["<p>This is a <b>generated</b> test.</p>"]


def test_postprocess_clean_quotation(postprocessor):
    example_content = {"text": "This is a test."}
    generations = ['"This is a generated test."']
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a generated test."]


def test_postprocess_preserve_quotation(postprocessor):
    example_content = {"text": '"This is a test."'}
    generations = ['"This is a generated test."']
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ['"This is a generated test."']


def test_postprocess_clean_word_count(postprocessor):
    example_content = {"text": "This is a test."}
    generations = ["This is a generated test. (10 words)"]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a generated test."]


def test_postprocess_clean_leading_message(postprocessor):
    example_content = {"text": "This is a test."}
    generations = ["Transformed message: This is a generated test."]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a generated test."]


def test_postprocess_clean_new_line(postprocessor):
    example_content = {"text": "This is a test."}
    generations = ["This is a\\ngenerated test."]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a\ngenerated test."]


def test_postprocess_clean_back_slash(postprocessor):
    example_content = {"text": "This is a test."}
    generations = ["This is a \\ generated test."]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a  generated test."]


@patch("api.content_gen.default_content_gen_postprocessor.rewrite_length_limit")
def test_postprocess_exceeds_upper_limit(mock_rewrite, postprocessor):
    example_content = {"text": "Short test."}
    generations = ["This is a very long generated test that exceeds the upper limit."]
    mock_rewrite.return_value = "Rewritten shorter test."
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["Rewritten shorter test."]
    mock_rewrite.assert_called_once()


@patch("api.content_gen.default_content_gen_postprocessor.logging.warning")
def test_postprocess_exceeds_lower_limit(mock_logging, postprocessor):
    example_content = {
        "text": "This is a long test that should not be shortened too much."
    }
    generations = ["Short test."]
    postprocessor.postprocess(example_content, generations, [])
    mock_logging.assert_called_once()


def test_postprocess_email_subject(postprocessor):
    postprocessor._gen_env._data_wrapper.content_type = ContentType.EmailMarketing
    example_content = {"text": "Email subject"}
    generations = ["Subject: This is an email subject"]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == [" This is an email subject"]


def test_postprocess_clean_ending_period(postprocessor):
    example_content = {"text": "This is a test"}
    generations = ["This is a generated test."]
    result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["This is a generated test"]


def test_postprocess_html_content_fix_tags(postprocessor):
    postprocessor._gen_env._data_wrapper.content_source_format = (
        ContentSourceFormat.Html
    )
    example_content = {"text": "<p>HTML content</p>"}
    generations = ["<p>Generated HTML content with <b>unclosed tag</p>"]
    with patch(
        "api.content_gen.default_content_gen_postprocessor.fix_content_html_tags"
    ) as mock_fix:
        mock_fix.return_value = "<p>Fixed HTML content</p>"
        result = postprocessor.postprocess(example_content, generations, [])
    assert result == ["<p>Fixed HTML content</p>"]
    mock_fix.assert_called_once()


def test_postprocess_clean_bullet_points(postprocessor):
    """Test that bullet point cleaning only affects true bullet markers, not inline asterisks."""
    example_content = {"text": "* Test bullet with emphasis"}

    # Test content with various asterisk usages
    test_generation = """* Test bullet
* Another *emphasis* bullet"""

    expected_result = """- Test bullet
- Another *emphasis* bullet"""

    generations = [test_generation]
    result = postprocessor.postprocess(
        example_content, generations, [], clean_bullet_points=True
    )
    assert result == [expected_result]


def test_postprocess_bullet_points_comprehensive(postprocessor):
    """Test comprehensive bullet point scenarios without triggering length limits."""
    example_content = {"text": "Content with mixed asterisks"}

    # Test content with various asterisk usages - keep it short to avoid length rewriting
    test_generation = """This is *bold* text.
Math: 2 * 3 = 6
* Real bullet point
Check*this*out
• Unicode bullet"""

    expected_result = """This is *bold* text.
Math: 2 * 3 = 6
- Real bullet point
Check*this*out
- Unicode bullet"""

    generations = [test_generation]
    result = postprocessor.postprocess(
        example_content, generations, [], follow_length=False, clean_bullet_points=True
    )
    assert result == [expected_result]


def test_postprocess_bullet_points_edge_cases(postprocessor):
    """Test edge cases for bullet point cleaning."""
    example_content = {"text": "Sample content"}

    # Test cases that should NOT be converted
    test_cases = [
        # Asterisk without space after - not a bullet
        ("*not-a-bullet", "*not-a-bullet"),
        # Asterisk in middle of line - not a bullet
        ("Text with * asterisk in middle", "Text with * asterisk in middle"),
        # Multiple asterisks for emphasis
        ("**bold text**", "**bold text**"),
        # Mixed with actual bullets
        ("* Real bullet\n**Not a bullet**", "- Real bullet\n**Not a bullet**"),
    ]

    for input_text, expected_output in test_cases:
        generations = [input_text]
        result = postprocessor.postprocess(
            example_content, generations, [], clean_bullet_points=True
        )
        assert result == [expected_output], f"Failed for input: {input_text}"


def test_postprocess_bullet_points_disabled(postprocessor):
    """Test that bullet point cleaning can be disabled."""
    example_content = {"text": "Sample content"}
    generations = ["* This should remain unchanged\n• This too"]

    # Disable bullet point cleaning
    result = postprocessor.postprocess(
        example_content, generations, [], clean_bullet_points=False, follow_length=False
    )
    assert result == ["* This should remain unchanged\n• This too"]


# Repurpose Content tests
@pytest.fixture
def repurpose_postprocessor():
    gen_env = Mock()
    gen_env._data_wrapper.content_source_format = ContentSourceFormat.Text
    gen_env._data_wrapper.campaign_goal = "Repurpose Content"
    gen_env._data_wrapper.content_type = ContentType.BlogPost

    # Add campaign instance mock
    campaign_instance_mock = Mock()
    campaign_instance_mock.id = "campaign_id"
    campaign_instance_mock.campaign_name = "Test Campaign"
    gen_env._data_wrapper.campaign_instance = campaign_instance_mock

    # Add content instance mock
    gen_env._data_wrapper.content_instance = Mock(id="content_id")

    # Add playbook instance mock
    gen_env._data_wrapper.playbook_instance = Mock(
        users=Mock(first=Mock(return_value="user_id"))
    )

    # Create gen_settings mock with proper config_map
    gen_settings_mock = Mock()
    gen_settings_mock.config_map = {}  # or any specific test data you need
    gen_settings_mock.foundation_model = "gpt-4o-2024-11-20"
    gen_settings_mock.session_id = None
    gen_env._gen_settings = gen_settings_mock

    return DefaultContentGenPostprocessor(gen_env, Mock())


def test_repurpose_content_no_length_check(repurpose_postprocessor):
    example_content = {"text": "Short original content."}
    generations = [
        "This is a much longer repurposed content that would normally exceed the length limit."
    ]
    result = repurpose_postprocessor.postprocess(example_content, generations, [])
    assert result == generations  # Content should not be rewritten


def test_repurpose_content_preserve_html(repurpose_postprocessor):
    repurpose_postprocessor._gen_env._data_wrapper.content_source_format = (
        ContentSourceFormat.Html
    )
    example_content = {"text": "<p>Original HTML content</p>"}
    generations = ["<p>Repurposed <strong>HTML</strong> content</p>"]
    result = repurpose_postprocessor.postprocess(example_content, generations, [])
    assert result == [
        "<p>Repurposed <strong>HTML</strong> content</p>"
    ]  # HTML should be preserved
