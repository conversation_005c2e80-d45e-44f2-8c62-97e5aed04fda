from django.test import TransactionTestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from ..models import Campaign, Tag, TofuUser
from ..serializers import TagSerializer


class TagViewSetTestCase(TransactionTestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = TofuUser.objects.create_user(
            username="testuser", password="testpass"
        )
        self.other_user = TofuUser.objects.create_user(
            username="otheruser", password="otherpass"
        )
        self.client.force_authenticate(user=self.user)

        self.tag1 = Tag.objects.create(name="Tag 1", creator=self.user)
        self.tag2 = Tag.objects.create(name="Tag 2", creator=self.user)
        self.other_user_tag = Tag.objects.create(
            name="Other User Tag", creator=self.other_user
        )

    def test_list_tags(self):
        url = reverse("tag-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Only tags created by the user

    def test_create_tag(self):
        url = reverse("tag-list")
        data = {"name": "New Tag"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Tag.objects.count(), 4)
        self.assertEqual(Tag.objects.filter(creator=self.user).count(), 3)

    def test_retrieve_tag(self):
        url = reverse("tag-detail", kwargs={"tag_id": self.tag1.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], "Tag 1")

    def test_update_tag(self):
        url = reverse("tag-detail", kwargs={"tag_id": self.tag1.id})
        data = {"name": "Updated Tag"}
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.tag1.refresh_from_db()
        self.assertEqual(self.tag1.name, "Updated Tag")

    def test_delete_tag(self):
        url = reverse("tag-detail", kwargs={"tag_id": self.tag1.id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Tag.objects.filter(id=self.tag1.id).count(), 0)

    def test_cannot_access_other_user_tag(self):
        url = reverse("tag-detail", kwargs={"tag_id": self.other_user_tag.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_manage_campaign_tags(self):
        campaign1 = Campaign.objects.create(
            creator=self.user, campaign_name="Campaign 1"
        )
        campaign2 = Campaign.objects.create(
            creator=self.user, campaign_name="Campaign 2"
        )

        url = reverse("tag-manage-campaign-tags")
        data = {
            "campaign_ids": [campaign1.id, campaign2.id],
            "tag_ids": [self.tag1.id, self.tag2.id],
            "action": "add",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        campaign1.refresh_from_db()
        campaign2.refresh_from_db()
        self.assertEqual(campaign1.campaignTags.count(), 2)
        self.assertEqual(campaign2.campaignTags.count(), 2)

        # Test removing tags
        data["action"] = "remove"
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        campaign1.refresh_from_db()
        campaign2.refresh_from_db()
        self.assertEqual(campaign1.campaignTags.count(), 0)
        self.assertEqual(campaign2.campaignTags.count(), 0)

    def test_manage_campaign_tags_invalid_action(self):
        campaign = Campaign.objects.create(
            creator=self.user, campaign_name="Campaign 1"
        )

        url = reverse("tag-manage-campaign-tags")
        data = {
            "campaign_ids": [campaign.id],
            "tag_ids": [self.tag1.id],
            "action": "invalid",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_duplicate_tag(self):
        url = reverse("tag-list")
        data = {"name": "Tag 1"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {"error": "A tag with this name already exists for your account."},
        )
        self.assertEqual(Tag.objects.filter(creator=self.user).count(), 2)
