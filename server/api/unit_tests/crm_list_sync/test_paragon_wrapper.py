import re
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from api.paragon_wrapper import HubspotAgent


class DummyPlaybook:
    pass


@pytest.fixture
def agent():
    return HubspotAgent(project_id="dummy_project", playbook=DummyPlaybook())


def test_create_company_list_from_contact_uses_desired_name(agent):
    # Simulate successful creation on first try
    agent.get_request_url = MagicMock(return_value="dummy_url")
    agent.post_request = MagicMock(
        return_value=MagicMock(
            status_code=201,
            json=lambda: {
                "output": {"list": {"name": "My List (converted using Tofu)"}}
            },
        )
    )
    result = agent.create_company_list_from_contact(
        name="My List",
        object_type_id="0-2",
        processing_type="STATIC",
        original_list_id="123",
    )
    assert result["name"] == "My List (converted using Tofu)"
    agent.post_request.assert_called_once()


def test_create_company_list_from_contact_appends_timestamp_on_duplicate(agent):
    # Simulate duplicate error on first try, success on second
    def post_request_side_effect(url, headers, body):
        if body["name"] == "My List (converted using Tofu)":
            # Simulate duplicate error
            mock_resp = MagicMock()
            mock_resp.status_code = 400
            mock_resp.json.return_value = {
                "output": {"subCategory": "ILS.DUPLICATE_LIST_NAMES"}
            }
            return mock_resp
        else:
            # Simulate success
            mock_resp = MagicMock()
            mock_resp.status_code = 201
            mock_resp.json.return_value = {"output": {"list": {"name": body["name"]}}}
            return mock_resp

    agent.get_request_url = MagicMock(return_value="dummy_url")
    agent.post_request = MagicMock(side_effect=post_request_side_effect)

    with patch("api.paragon_wrapper.datetime") as mock_datetime:
        mock_datetime.now.return_value = datetime(2024, 6, 1, 12, 34, 56)
        mock_datetime.strftime = datetime.strftime
        result = agent.create_company_list_from_contact(
            name="My List",
            object_type_id="0-2",
            processing_type="STATIC",
            original_list_id="123",
        )
    # Should have called post_request twice
    assert agent.post_request.call_count == 2
    # The second name should have the timestamp
    expected_pattern = r"My List \(converted using Tofu\) - 20240601-123456"
    assert re.match(expected_pattern, result["name"])
