from unittest.mock import MagicMock, patch

from api.models import Campaign, CompanyInfo, Playbook, TargetInfoGroup
from api.shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from api.sync.crm_list_sync.base_crm_list_sync import RecordType
from api.sync.crm_list_sync.crm_list_sync_hubspot import (
    CRMListSyncHubspot,
)
from api.sync.crm_list_sync.crm_list_sync_workflow import (
    CRMListSyncWorkflow,
    ImportListAsTargetInfoGroupTaskInput,
)
from django.test import TestCase


class TestCRMListSyncWorkflow(TestCase):
    def setUp(self):
        self.company_info = CompanyInfo.objects.create()
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company_info
        )
        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            playbook=self.playbook,
            campaign_params={
                "targets": [{"target1": ["value1"]}],
                "foundation_model": "gpt-4o-2024-11-20",
                "campaign_goal": "Personalization",
            },
            campaign_status={},
        )
        self.list_id = "list123"
        self.list_name = "Test List"
        self.object_type_id = "0-2"
        self.campaign_id = self.campaign.id
        self.task_input = ImportListAsTargetInfoGroupTaskInput(
            playbook_id=self.playbook.id,
            list_id=self.list_id,
            source=PlatformType.PLATFORM_TYPE_HUBSPOT,
            record_type=RecordType.CONTACT,
            table_data=None,
        )

    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.submit_build_targets_task",
        return_value="mock_task_id",
    )
    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.create_target_data_for_new_records",
        return_value=[
            MagicMock(id=1, name="HP Inc."),
            MagicMock(id=2, name="Dell Inc."),
        ],
    )
    @patch("api.sync.crm_list_sync.crm_list_sync_workflow.SyncListRecordFetcher")
    @patch(
        "api.sync.crm_list_sync.base_crm_list_sync.ParagonWrapper",
    )
    def test_sync_import_list_as_target_info_group_creates_group(
        self,
        mock_paragon_wrapper,
        mock_record_fetcher,
        mock_bulk_create,
        mock_submit_task,
    ):
        mock_agent = MagicMock()
        mock_agent.get_list_records.return_value = [
            {"id": "hs_object_id", "email": "<EMAIL>"},
            {"id": "hs_object_id", "email": "<EMAIL>"},
        ]
        mock_agent.get_list_by_id.return_value = {
            "objectTypeId": self.object_type_id,
            "name": self.list_name,
        }
        mock_paragon_wrapper.return_value.get_hubspot_agent.return_value = mock_agent

        mock_record_fetcher_instance = MagicMock()
        mock_record_fetcher.return_value = mock_record_fetcher_instance
        mock_record_fetcher_instance.get_new_records_data.return_value = [
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "HP Inc.",
                "website": "https://www.hp.com",
            },
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "Dell Inc.",
                "website": "https://www.dell.com",
            },
        ]

        workflow = CRMListSyncWorkflow(
            self.playbook.id, CRMListSyncHubspot(self.playbook.id)
        )
        workflow.sync_import_list_as_target_info_group(
            self.list_id,
            RecordType.COMPANY,
            None,
        )
        target_info_group = TargetInfoGroup.objects.get(
            playbook=self.playbook, target_info_group_key=self.list_name
        )
        self.assertEqual(
            target_info_group.meta["importListSettings"]["syncFrom"], "hubspot"
        )
        self.assertEqual(
            target_info_group.meta["importListSettings"]["listId"], self.list_id
        )
        mock_bulk_create.assert_called_once()
        args, kwargs = mock_bulk_create.call_args
        created_targets = args[0]
        self.assertEqual(len(created_targets), 2)
        target_names = {target["name"] for target in created_targets}
        self.assertIn("HP Inc.", target_names)
        self.assertIn("Dell Inc.", target_names)
        mock_submit_task.assert_called_once_with([1, 2])

    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.submit_build_targets_task",
        return_value="mock_task_id",
    )
    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.create_target_data_for_new_records",
        return_value=[
            MagicMock(id=1, name="HP Inc."),
            MagicMock(id=2, name="Dell Inc."),
        ],
    )
    @patch("api.sync.crm_list_sync.crm_list_sync_workflow.SyncListRecordFetcher")
    def test_sync_list_to_target_info_group(
        self, mock_record_fetcher, mock_bulk_create, mock_submit_task
    ):
        group = TargetInfoGroup.objects.create(
            playbook=self.playbook,
            target_info_group_key=self.list_name,
            meta={
                "type": "Company",
                "importListSettings": {
                    "syncFrom": "hubspot",
                    "listId": self.list_id,
                    "campaignId": self.campaign_id,
                    "tableData": [
                        {"columnName": "name", "dataType": "subtarget_name"},
                        {
                            "columnName": "hs_object_id",
                            "dataType": "hubspot_identifier",
                        },
                        {"columnName": "website", "dataType": "url"},
                    ],
                },
            },
        )
        mock_record_fetcher_instance = MagicMock()
        mock_record_fetcher.return_value = mock_record_fetcher_instance
        mock_record_fetcher_instance.get_new_records_data.return_value = [
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "HP Inc.",
                "website": "https://www.hp.com",
            },
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "Dell Inc.",
                "website": "https://www.dell.com",
            },
        ]

        workflow = CRMListSyncWorkflow(
            self.playbook.id, CRMListSyncHubspot(self.playbook.id)
        )
        workflow.sync_list_to_target_info_group(group.id)
        group = TargetInfoGroup.objects.get(id=group.id)
        self.assertEqual(group.meta["importListSettings"]["syncFrom"], "hubspot")
        self.assertEqual(group.meta["importListSettings"]["listId"], self.list_id)
        self.assertEqual(
            group.meta["importListSettings"]["campaignId"], self.campaign_id
        )
        mock_bulk_create.assert_called_once()
        args, kwargs = mock_bulk_create.call_args
        created_targets = args[0]
        self.assertEqual(len(created_targets), 2)
        target_names = {target["name"] for target in created_targets}
        self.assertIn("HP Inc.", target_names)
        self.assertIn("Dell Inc.", target_names)
        mock_submit_task.assert_called_once_with([1, 2])

    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.submit_build_targets_task",
        return_value="mock_task_id",
    )
    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.create_target_data_for_new_records",
        return_value=[],
    )
    @patch("api.sync.crm_list_sync.crm_list_sync_workflow.SyncListRecordFetcher")
    def test_sync_list_to_target_info_group_no_new_records(
        self, mock_record_fetcher, mock_create_targets, mock_submit_task
    ):
        group = TargetInfoGroup.objects.create(
            playbook=self.playbook,
            target_info_group_key=self.list_name,
            meta={
                "type": "Company",
                "importListSettings": {
                    "syncFrom": "hubspot",
                    "listId": self.list_id,
                    "campaignId": self.campaign_id,
                    "tableData": [
                        {"columnName": "name", "dataType": "subtarget_name"},
                        {
                            "columnName": "hs_object_id",
                            "dataType": "hubspot_identifier",
                        },
                        {"columnName": "website", "dataType": "url"},
                    ],
                },
            },
        )
        mock_record_fetcher_instance = MagicMock()
        mock_record_fetcher.return_value = mock_record_fetcher_instance
        mock_record_fetcher_instance.get_new_records_data.return_value = []

        workflow = CRMListSyncWorkflow(
            self.playbook.id, CRMListSyncHubspot(self.playbook.id)
        )
        result = workflow.sync_list_to_target_info_group(group.id)
        # Should return None or not call submit_build_targets_task
        mock_submit_task.assert_not_called()
        self.assertEqual(result, {})

    def test_init_playbook_does_not_exist(self):
        with self.assertRaises(ValueError):
            CRMListSyncWorkflow(99999, CRMListSyncHubspot(99999))

    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.submit_build_targets_task",
        return_value="mock_task_id",
    )
    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.create_target_data_for_new_records",
        return_value=[
            MagicMock(id=1, name="HP Inc."),
            MagicMock(id=2, name="Dell Inc."),
        ],
    )
    @patch("api.sync.crm_list_sync.crm_list_sync_workflow.SyncListRecordFetcher")
    @patch(
        "api.sync.crm_list_sync.base_crm_list_sync.ParagonWrapper",
    )
    def test_sync_import_list_as_target_info_group_creates_group_exist_list(
        self,
        mock_paragon_wrapper,
        mock_record_fetcher,
        mock_bulk_create,
        mock_submit_task,
    ):
        mock_agent = MagicMock()
        mock_agent.get_list_records.return_value = [
            {"id": "hs_object_id", "email": "<EMAIL>"},
            {"id": "hs_object_id", "email": "<EMAIL>"},
        ]
        mock_agent.get_list_by_id.return_value = {
            "objectTypeId": self.object_type_id,
            "name": self.list_name,
        }
        mock_paragon_wrapper.return_value.get_hubspot_agent.return_value = mock_agent

        mock_record_fetcher_instance = MagicMock()
        mock_record_fetcher.return_value = mock_record_fetcher_instance
        mock_record_fetcher_instance.get_new_records_data.return_value = [
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "HP Inc.",
                "website": "https://www.hp.com",
            },
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "Dell Inc.",
                "website": "https://www.dell.com",
            },
        ]

        workflow = CRMListSyncWorkflow(
            self.playbook.id, CRMListSyncHubspot(self.playbook.id)
        )
        workflow.sync_import_list_as_target_info_group(
            self.list_id,
            RecordType.COMPANY,
            None,
        )
        target_info_group = TargetInfoGroup.objects.get(
            playbook=self.playbook, target_info_group_key=self.list_name
        )
        self.assertEqual(
            target_info_group.meta["importListSettings"]["syncFrom"], "hubspot"
        )
        self.assertEqual(
            target_info_group.meta["importListSettings"]["listId"], self.list_id
        )
        mock_bulk_create.assert_called_once()
        args, kwargs = mock_bulk_create.call_args
        created_targets = args[0]
        self.assertEqual(len(created_targets), 2)
        target_names = {target["name"] for target in created_targets}
        self.assertIn("HP Inc.", target_names)
        self.assertIn("Dell Inc.", target_names)
        mock_submit_task.assert_called_with([1, 2])

        workflow.sync_import_list_as_target_info_group(
            self.list_id,
            RecordType.COMPANY,
            None,
        )

        target_info_group2 = TargetInfoGroup.objects.get(
            playbook=self.playbook, target_info_group_key=f"{self.list_name} 2"
        )
        self.assertEqual(
            target_info_group2.meta["importListSettings"]["syncFrom"], "hubspot"
        )
        self.assertEqual(
            target_info_group2.meta["importListSettings"]["listId"], self.list_id
        )
        self.assertGreaterEqual(mock_submit_task.call_count, 2)

    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.submit_build_targets_task",
        return_value="mock_task_id",
    )
    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.create_target_data_for_new_records",
        return_value=[
            MagicMock(id=1, name="HP Inc."),
            MagicMock(id=2, name="Dell Inc."),
        ],
    )
    @patch("api.sync.crm_list_sync.crm_list_sync_workflow.SyncListRecordFetcher")
    @patch(
        "api.sync.crm_list_sync.base_crm_list_sync.ParagonWrapper",
    )
    def test_sync_import_list_as_target_info_group_creates_company_from_contact(
        self,
        mock_paragon_wrapper,
        mock_record_fetcher,
        mock_bulk_create,
        mock_submit_task,
    ):
        # Simulate the list is a contact list, but we want to import as company
        contact_list_id = "contact_list_123"
        contact_list_name = "Contact List Name"
        company_list_id = "company_list_456"
        company_list_name = "Company List Name (converted using Tofu)"
        # The initial get_list_by_id returns a contact type
        mock_agent = MagicMock()
        mock_agent.get_list_by_id.return_value = {
            "objectTypeId": "0-1",  # CONTACT
            "name": contact_list_name,
        }
        # The create_company_list_from_contact returns a new company list
        mock_agent.create_company_list_from_contact.return_value = {
            "listId": company_list_id,
            "name": company_list_name,
            "objectTypeId": "0-2",  # COMPANY
        }
        mock_paragon_wrapper.return_value.get_hubspot_agent.return_value = mock_agent

        mock_record_fetcher_instance = MagicMock()
        mock_record_fetcher.return_value = mock_record_fetcher_instance
        mock_record_fetcher_instance.get_new_records_data.return_value = [
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "HP Inc.",
                "website": "https://www.hp.com",
            },
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "Dell Inc.",
                "website": "https://www.dell.com",
            },
        ]

        workflow = CRMListSyncWorkflow(
            self.playbook.id, CRMListSyncHubspot(self.playbook.id)
        )
        workflow.sync_import_list_as_target_info_group(
            contact_list_id,
            RecordType.COMPANY,
            None,
        )
        # The group name should be based on the new company list name
        target_info_group = TargetInfoGroup.objects.get(
            playbook=self.playbook, target_info_group_key=company_list_name
        )
        self.assertEqual(
            target_info_group.meta["importListSettings"]["syncFrom"], "hubspot"
        )
        self.assertEqual(
            target_info_group.meta["importListSettings"]["listId"], company_list_id
        )
        self.assertEqual(
            target_info_group.meta["importListSettings"]["objectTypeId"], "0-2"
        )
        mock_agent.create_company_list_from_contact.assert_called_once_with(
            contact_list_name, "0-2", "DYNAMIC", contact_list_id
        )
        mock_bulk_create.assert_called_once()
        args, kwargs = mock_bulk_create.call_args
        created_targets = args[0]
        self.assertEqual(len(created_targets), 2)
        target_names = {target["name"] for target in created_targets}
        self.assertIn("HP Inc.", target_names)
        self.assertIn("Dell Inc.", target_names)
        mock_submit_task.assert_called_once_with([1, 2])

    @patch(
        "api.sync.crm_list_sync.crm_list_sync_hubspot.CRMListSyncHubspot._create_target_info_group_from_list"
    )
    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.create_target_data"
    )
    @patch("api.sync.crm_list_sync.crm_list_sync_workflow.SyncListRecordFetcher")
    def test_fetch_list_data(
        self, mock_record_fetcher, mock_create_target_data, mock_create_group
    ):
        # Mock the group info
        expected_group_name = "Test List"
        expected_group_meta = {"meta_key": "meta_value"}
        mock_create_group.return_value = (
            None,
            expected_group_name,
            expected_group_meta,
        )
        # Mock the record fetcher to return new records data
        mock_record_fetcher_instance = MagicMock()
        mock_record_fetcher.return_value = mock_record_fetcher_instance
        mock_record_fetcher_instance.get_new_records_data.return_value = [
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "HP Inc.",
                "website": "https://www.hp.com",
            },
            {
                "about_us": None,
                "createdate": "2025-05-06T20:30:46.807Z",
                "hs_keywords": None,
                "hs_lastmodifieddate": "2025-05-06T20:35:54.563Z",
                "hs_object_id": "33085425759",
                "name": "Dell Inc.",
                "website": "https://www.dell.com",
            },
        ]
        # Mock the target creator to return target keys, objects, and failed records
        expected_target_keys = ["HP Inc.", "Dell Inc."]
        expected_target_objects_data = [
            {"target_key": "HP Inc.", "meta": {"foo": "bar"}, "docs": {"doc": 1}},
            {"target_key": "Dell Inc.", "meta": {"foo": "baz"}, "docs": {"doc": 2}},
        ]
        expected_failed_record_data = []
        mock_create_target_data.return_value = (
            expected_target_keys,
            expected_target_objects_data,
            expected_failed_record_data,
        )

        workflow = CRMListSyncWorkflow(
            self.playbook.id, CRMListSyncHubspot(self.playbook.id)
        )
        result = workflow.fetch_list_data(
            self.list_id,
            RecordType.COMPANY,
            None,
        )
        self.assertEqual(result["target_info_group_name"], expected_group_name)
        self.assertEqual(result["target_info_group_meta"], expected_group_meta)
        self.assertEqual(
            result["new_target_objects_data"], expected_target_objects_data
        )
        mock_record_fetcher.assert_called()
        mock_create_target_data.assert_called()
        mock_create_group.assert_called()

    @patch(
        "api.sync.crm_list_sync.crm_list_sync_hubspot.CRMListSyncHubspot._create_target_info_group_from_list"
    )
    @patch(
        "api.sync.crm_list_sync.crm_list_sync_workflow.SyncTargetCreator.create_target_data"
    )
    @patch("api.sync.crm_list_sync.crm_list_sync_workflow.SyncListRecordFetcher")
    def test_fetch_list_data_no_new_records(
        self, mock_record_fetcher, mock_create_target_data, mock_create_group
    ):
        # Mock the group info
        expected_group_name = "Test List"
        expected_group_meta = {"meta_key": "meta_value"}
        mock_create_group.return_value = (
            None,
            expected_group_name,
            expected_group_meta,
        )
        # Mock the record fetcher to return no new records data
        mock_record_fetcher_instance = MagicMock()
        mock_record_fetcher.return_value = mock_record_fetcher_instance
        mock_record_fetcher_instance.get_new_records_data.return_value = []

        workflow = CRMListSyncWorkflow(
            self.playbook.id, CRMListSyncHubspot(self.playbook.id)
        )
        result = workflow.fetch_list_data(
            self.list_id,
            RecordType.COMPANY,
            None,
        )
        self.assertEqual(result["new_target_objects_data"], [])
        mock_record_fetcher.assert_called()
        mock_create_target_data.assert_not_called()
        mock_create_group.assert_called()

    def test_field_mapping_save_and_read_from_settings(self):
        """Test that custom field mappings are saved to and read from playbook settings"""
        # Create a HubSpot sync instance
        hubspot_sync = CRMListSyncHubspot(self.playbook.id)

        # Custom field mapping
        custom_mapping = [
            {"columnName": "email", "dataType": "subtarget_name"},
            {"columnName": "hs_object_id", "dataType": "hubspot_identifier"},
            {"columnName": "firstname", "dataType": "text"},
            {"columnName": "custom_field", "dataType": "text"},
        ]

        # Test saving field mapping
        hubspot_sync._save_field_mapping_to_settings("Contact", custom_mapping)

        # Refresh from DB
        self.playbook.refresh_from_db()

        # Verify it's saved correctly
        saved_mapping = (
            self.playbook.settings.get("hubspotImportSettings", {})
            .get("hubspotFieldMappings", {})
            .get("Contact", None)
        )
        self.assertEqual(saved_mapping, custom_mapping)

        # Test reading field mapping
        retrieved_mapping = hubspot_sync._get_field_mapping_from_settings("Contact")
        self.assertEqual(retrieved_mapping, custom_mapping)

        # Test effective field mapping - should use saved mapping when no table_data provided
        effective_mapping = hubspot_sync._get_effective_field_mapping(
            "Contact", None, hubspot_sync.DEFAULT_TABLE_VALUES_MAP["Contact"]
        )
        self.assertEqual(effective_mapping, custom_mapping)

        # Test effective field mapping - should use provided mapping when provided
        new_mapping = [{"columnName": "email", "dataType": "subtarget_name"}]
        effective_mapping = hubspot_sync._get_effective_field_mapping(
            "Contact", new_mapping, hubspot_sync.DEFAULT_TABLE_VALUES_MAP["Contact"]
        )
        self.assertEqual(effective_mapping, new_mapping)

        # Explicitly save the new mapping to test the save functionality
        hubspot_sync._save_field_mapping_to_settings("Contact", new_mapping)

        # Verify the new mapping was saved
        self.playbook.refresh_from_db()
        saved_new_mapping = (
            self.playbook.settings.get("hubspotImportSettings", {})
            .get("hubspotFieldMappings", {})
            .get("Contact", None)
        )
        self.assertEqual(saved_new_mapping, new_mapping)
