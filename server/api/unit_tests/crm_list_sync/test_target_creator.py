from unittest.mock import patch

import pytest
from api.models import CompanyInfo, Playbook, TargetInfo, TargetInfoGroup
from api.status import Status<PERSON>andler
from api.sync.worker.target_creator import async_build_targets


@pytest.mark.django_db
def test_async_build_targets_sets_status_and_calls_parallel_build_docs():
    # Create required objects
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    group = TargetInfoGroup.objects.create(
        playbook=playbook, target_info_group_key="test-group", meta={}
    )
    target1 = TargetInfo.objects.create(
        target_info_group=group, target_key="target1", meta={}, docs={}
    )
    target2 = TargetInfo.objects.create(
        target_info_group=group, target_key="target2", meta={}, docs={}
    )
    target_ids = [target1.id, target2.id]

    with patch(
        "api.sync.worker.target_creator.parallel_build_docs"
    ) as mock_parallel_build_docs:
        async_build_targets(group.id, target_ids)
        called_targets = mock_parallel_build_docs.call_args[0][0]
        assert set(t.id for t in called_targets) == set(target_ids)
        mock_parallel_build_docs.assert_called_once_with(
            called_targets, rebuild=True, check_and_rebuild=False
        )

    # Check final status in DB
    status_qs = StatusHandler.get_playbook_status_queryset(playbook).filter(
        type="info_expansion"
    )
    assert status_qs.count() == 1
    assert status_qs.first().status == "success"


@pytest.mark.django_db
def test_async_build_targets_sets_status_error_on_failure():
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    group = TargetInfoGroup.objects.create(
        playbook=playbook, target_info_group_key="test-group", meta={}
    )
    target1 = TargetInfo.objects.create(
        target_info_group=group, target_key="target1", meta={}, docs={}
    )
    target2 = TargetInfo.objects.create(
        target_info_group=group, target_key="target2", meta={}, docs={}
    )
    target_ids = [target1.id, target2.id]

    with patch(
        "api.sync.worker.target_creator.parallel_build_docs",
        side_effect=Exception("fail!"),
    ) as mock_parallel_build_docs:
        with pytest.raises(Exception, match="fail!"):
            async_build_targets(group.id, target_ids)

    # Check final status in DB
    status_qs = StatusHandler.get_playbook_status_queryset(playbook).filter(
        type="info_expansion"
    )
    assert status_qs.count() == 1
    assert status_qs.first().status == "error"
