import logging
from unittest.mock import MagicMock

import pytest

from ..feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    ContentGenSettings,
    GenerateEnv,
)
from ..feature.feature_assembler.generate_feature_assembler import (
    ComponentGenerateFeatureAssembler,
    ComponentJointGenerateFeatureAssembler,
    ContentCollectionFeatureAssembler,
    GenerateFeatureAssembler,
    TemplateGenerationFeatureAssembler,
)
from ..feature.feature_builder.company_context_feature_builder import (
    CompanyContextFeatureBuilder,
)
from ..feature.feature_builder.company_feature_builder import CompanyFeatureBuilder
from ..feature.feature_builder.email_feature_builder import EmailFeatureBuilder
from ..feature.feature_builder.target_context_feature_builder import (
    TargetContextBuilder,
)
from ..models import Campaign, ContentGroup

"""
Mock content group with components for testing.
The campaign goal is Personalization, and the campaign has 1 custom instruction.
The content group has 2 components, both of which are text.
The content group has a foundation model of gpt-4o-2024-11-20, an asset of Asset 1 with instruction Test2, and a custom instruction of Test3.
"""


@pytest.fixture
def mock_content_group_with_components():
    campaign = MagicMock(spec=Campaign)
    campaign.campaign_params = {
        "campaign_goal": "Personalization",
        "custom_instructions": [{"instruction": "Test1"}],
    }
    content_group = MagicMock(spec=ContentGroup)
    content_group.components = {
        "component1": {"meta": {"type": "text"}},
        "component2": {"meta": {"type": "text"}},
    }
    content_group.content_group_params = {
        "foundation_model": "gpt-4o-2024-11-20",
        "custom_instructions": [
            {"instruction": "Test2", "asset": "Asset1"},
            {"instruction": "Test3"},
        ],
    }
    content_group.campaign = campaign
    return content_group


"""
Test the get_budgets method for the joint component feature assembler.
For personalization, the budgets should be distributed as follows:
- asset_context: 15
- tone_reference: 10
- brand_guidelines: 15
For repurpose content, the budgets should be distributed as follows:
- asset_context: 30
- brand_guidelines: 15
- tone_reference: 10
"""


@pytest.mark.parametrize(
    "features_needed, campaign_goal, model_budget, expected_budgets",
    [
        (
            ["asset_context", "tone_reference"],
            "Personalization",
            100,
            {"asset_context": 15, "tone_reference": 10, "brand_guidelines": 15},
        ),
        (
            ["asset_context"],
            "Repurpose Content",
            100,
            {"asset_context": 30, "brand_guidelines": 15},
        ),
        (
            ["tone_reference", "asset_context"],
            "Repurpose Content",
            100,
            {"tone_reference": 10, "asset_context": 30, "brand_guidelines": 15},
        ),
        (
            ["tone_reference"],
            "Personalization",
            100,
            {"tone_reference": 10, "brand_guidelines": 15},
        ),
        ([], "Personalization", 100, {"brand_guidelines": 15}),
    ],
)
def test_get_budgets_joint_components(
    mock_content_group_with_components,
    features_needed,
    campaign_goal,
    model_budget,
    expected_budgets,
):
    all_components = mock_content_group_with_components.components
    mock_content_group_with_components.campaign.campaign_params["campaign_goal"] = (
        campaign_goal
    )
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = ContentGenSettings(data_wrapper, save_variations=False)
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    assembler = ComponentJointGenerateFeatureAssembler(
        model_budget,
        all_components,
        prev_gen_variations=MagicMock(),
        gen_env=gen_env,
    )
    result = assembler.get_budgets(features_needed)
    assert result == expected_budgets


"""
Test the get_budgets method for the component-level generate feature assembler.
For personalization, the budgets should be distributed as follows:
- asset_context: 15
- tone_reference: 10
- brand_guidelines: 15
For repurpose content, the budgets should be distributed as follows:
- asset_context: 30
- brand_guidelines: 15
- tone_reference: 10
"""


@pytest.mark.parametrize(
    "features_needed, campaign_goal, model_budget, expected_budgets",
    [
        (
            ["asset_context", "tone_reference"],
            "Personalization",
            100,
            {"asset_context": 15, "tone_reference": 10, "brand_guidelines": 15},
        ),
        (
            ["asset_context"],
            "Repurpose Content",
            100,
            {"asset_context": 30, "brand_guidelines": 15},
        ),
        (
            ["tone_reference"],
            "Personalization",
            100,
            {"tone_reference": 10, "brand_guidelines": 15},
        ),
        ([], "Personalization", 100, {"brand_guidelines": 15}),
    ],
)
def test_component_generate_feature_assembler_get_budgets(
    mock_content_group_with_components,
    features_needed,
    campaign_goal,
    model_budget,
    expected_budgets,
):
    mock_content_group_with_components.campaign.campaign_params["campaign_goal"] = (
        campaign_goal
    )
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = ContentGenSettings(data_wrapper, save_variations=False)
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    assembler = ComponentGenerateFeatureAssembler(
        model_budget,
        example_content=MagicMock(),
        prev_gen_variations=MagicMock(),
        gen_env=gen_env,
    )
    result = assembler.get_budgets(features_needed)
    assert result == expected_budgets


"""
Test the get_budgets method for the template generation feature assembler.
The budgets should be distributed as follows:
- asset_context: 15 
- brand_guidelines: 15
"""


@pytest.mark.parametrize(
    "features_needed, model_budget, expected_budgets",
    [
        (
            ["asset_context"],
            100,
            {"asset_context": 15, "brand_guidelines": 15},
        ),
        (
            [],
            100,
            {"brand_guidelines": 15},
        ),
    ],
)
def test_template_generation_feature_assembler_get_budgets(
    mock_content_group_with_components,
    features_needed,
    model_budget,
    expected_budgets,
):
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = ContentGenSettings(data_wrapper, save_variations=False)
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    assembler = TemplateGenerationFeatureAssembler(
        gen_env=gen_env,
        model_budget=model_budget,
    )
    result = assembler.get_budgets(features_needed)
    assert result == expected_budgets


"""
Test the get_budgets method for the content collection feature assembler.
For personalization, the budgets should be distributed as follows:
- company_context: 40
- asset_context: 40
"""


@pytest.mark.parametrize(
    "features_needed, model_budget, expected_budgets",
    [
        (
            ["asset_context"],
            100,
            {"company_context": 40, "asset_context": 40},
        ),
        (
            [],
            100,
            {"company_context": 40},
        ),
    ],
)
def test_content_collection_feature_assembler_get_budgets(
    mock_content_group_with_components,
    features_needed,
    model_budget,
    expected_budgets,
):
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = ContentGenSettings(data_wrapper, save_variations=False)
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    assembler = ContentCollectionFeatureAssembler(
        playbook_handler=MagicMock(),
        gen_env=gen_env,
        content_collection=MagicMock(),
        model_budget=model_budget,
    )
    result = assembler.get_budgets(features_needed)
    assert result == expected_budgets


"""
Test the get_budgets method for the generate feature assembler.
For personalization, the budgets should be distributed as follows:
- company_context: 25
- target_context: 25
For personalization with assets, the budgets should be distributed as follows:
- company_context: 30
- target_context: 25
For repurpose content, the budgets should be distributed as follows:
- company_context: 25
- target_context: 0
"""


@pytest.mark.parametrize(
    "campaign_goal, model_budget, has_assets, expected_budgets",
    [
        (
            "Personalization",
            100,
            True,
            {"company_context": 25, "target_context": 25},
        ),
        (
            "Personalization",
            100,
            False,
            {"company_context": 30, "target_context": 25},
        ),
        (
            "Repurpose Content",
            100,
            True,
            {"company_context": 25, "target_context": 0},
        ),
    ],
)
def test_generate_feature_assembler_get_budgets(
    mock_content_group_with_components,
    campaign_goal,
    model_budget,
    has_assets,
    expected_budgets,
):
    mock_content_group_with_components.campaign.campaign_params["campaign_goal"] = (
        campaign_goal
    )
    if has_assets:
        mock_content_group_with_components.campaign.campaign_params["assets"] = [
            {"asset": "Asset1", "instruction": "Instruction1"},
        ]
    else:
        mock_content_group_with_components.campaign.campaign_params["assets"] = []

    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = ContentGenSettings(data_wrapper, save_variations=False)
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    assembler = GenerateFeatureAssembler(
        model_budget,
        gen_env=gen_env,
    )
    result = assembler.get_budgets(None)
    logging.info(result)
    assert result == expected_budgets


@pytest.fixture
def mock_generate_feature_assembler(mock_content_group_with_components):
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = ContentGenSettings(data_wrapper, save_variations=False)
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    return GenerateFeatureAssembler(10000, gen_env=gen_env)


def test_generate_feature_assembler_get_features_needed(
    mock_generate_feature_assembler,
):
    expected_features = [
        "company_context",
        "company_summary",
        "target_context",
        "company_name",
        "targets",
        "has_value_prop",
        "target_meta_types",
    ]
    assert set(mock_generate_feature_assembler.get_features_needed()) == set(
        expected_features
    )


def test_generate_feature_assembler_get_features_needed_with_template(
    mock_content_group_with_components,
):
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = ContentGenSettings(
        data_wrapper, is_template_personalization=True, save_variations=False
    )
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    generate_feature_assembler = GenerateFeatureAssembler(10000, gen_env=gen_env)
    expected_features = [
        "company_context",
        "company_summary",
        "target_context",
        "company_name",
        "targets",
        "has_value_prop",
        "target_meta_types",
        "email_template_content",
        "email_template_json_format",
    ]
    assert set(generate_feature_assembler.get_features_needed()) == set(
        expected_features
    )


def test_generate_feature_assembler_register_builders(mock_generate_feature_assembler):
    mock_generate_feature_assembler.register_builders()
    assert len(mock_generate_feature_assembler.builders) == 4
    assert any(
        isinstance(builder, TargetContextBuilder)
        for builder in mock_generate_feature_assembler.builders
    )
    assert any(
        isinstance(builder, CompanyFeatureBuilder)
        for builder in mock_generate_feature_assembler.builders
    )
    assert any(
        isinstance(builder, CompanyContextFeatureBuilder)
        for builder in mock_generate_feature_assembler.builders
    )
    assert any(
        isinstance(builder, EmailFeatureBuilder)
        for builder in mock_generate_feature_assembler.builders
    )
