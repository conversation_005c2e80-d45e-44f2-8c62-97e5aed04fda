from unittest.mock import MagicMock, patch

import pytest
from django.test import TestCase

from ..content_gen.content_collection_plan_gen import ContentCollectionPlanGen
from ..feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    ContentGenSettings,
    GenerateEnv,
)
from ..models import Campaign, CompanyInfo, ContentGroup, Playbook, TofuUser


class TestContentCollectionPlanGen(TestCase):
    def setUp(self):
        self.user = TofuUser.objects.create(username="testuser")
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=CompanyInfo.objects.create(),
        )
        self.playbook.users.set([self.user])
        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            creator=self.user,
            playbook=self.playbook,
            campaign_params={
                "foundation_model": "gpt-4o-2024-11-20",
                "campaign_goal": "Repurpose Content",
            },
            campaign_status={},
        )
        # Create content groups in a collection
        self.content_group_1 = ContentGroup.objects.create(
            campaign=self.campaign,
            creator=self.user,
            components={"comp1": {"text": "text1", "meta": {}}},
            content_group_params={
                "content_collection": {"id": "collection1", "prev": None, "next": ["2"]}
            },
            content_group_status={},
        )
        self.content_group_2 = ContentGroup.objects.create(
            campaign=self.campaign,
            creator=self.user,
            components={"comp2": {"text": "text2", "meta": {}}},
            content_group_params={
                "content_collection": {
                    "id": "collection1",
                    "prev": ["1"],
                    "next": None,
                }
            },
            content_group_status={},
        )

    @pytest.mark.django_db
    def test_content_collection_plan_gen_initialization(self):
        collection_data_wrapper = BaseContentWrapper.from_data_instance(self.campaign)
        collection_gen_settings = ContentGenSettings(
            collection_data_wrapper,
            foundation_model="gpt-4o-2024-11-20",
            content_collection_plan_gen=True,
            save_variations=False,
        )
        collection_gen_env = GenerateEnv(
            collection_data_wrapper, collection_gen_settings
        )

        mock_playbook_handler = MagicMock()
        mock_content_collection = MagicMock()

        generator = ContentCollectionPlanGen(
            mock_playbook_handler,
            mock_content_collection,
            gen_env=collection_gen_env,
        )

        assert generator.playbook_handler == mock_playbook_handler
        assert generator.content_collection == mock_content_collection
        assert generator._gen_env == collection_gen_env

    @pytest.mark.django_db
    @patch(
        "api.content_gen.content_collection_plan_gen.ContentCollectionPlanInputBuilder"
    )
    def test_content_collection_plan_generation(self, mock_input_builder):
        collection_data_wrapper = BaseContentWrapper.from_data_instance(self.campaign)
        collection_gen_settings = ContentGenSettings(
            collection_data_wrapper,
            foundation_model="gpt-4o-2024-11-20",
            content_collection_plan_gen=True,
            save_variations=False,
        )
        collection_gen_env = GenerateEnv(
            collection_data_wrapper, collection_gen_settings
        )

        # Mock the input builder
        mock_input_builder_instance = MagicMock()
        mock_input_builder.return_value = mock_input_builder_instance
        mock_input_builder_instance.create_llm_inputs.return_value = {
            "mocked": "llm_inputs"
        }
        mock_input_builder_instance.all_features = {
            "custom_prompts_string": "mocked custom prompts"
        }

        # Create the generator
        mock_playbook_handler = MagicMock()
        mock_content_collection = MagicMock()
        generator = ContentCollectionPlanGen(
            mock_playbook_handler,
            mock_content_collection,
            gen_env=collection_gen_env,
        )

        # Mock the get_results method
        mock_get_results = [MagicMock(text="Generated plan content")]
        with patch.object(
            ContentCollectionPlanGen, "get_results", return_value=mock_get_results
        ):
            result = generator.gen()

        # Assertions
        assert "Generated plan content" in result
        assert "mocked custom prompts" in result
        mock_input_builder_instance.create_llm_inputs.assert_called_once()

    @pytest.mark.django_db
    def test_get_content_collection_instructions_prompt(self):
        collection_data_wrapper = BaseContentWrapper.from_data_instance(self.campaign)
        collection_gen_settings = ContentGenSettings(
            collection_data_wrapper,
            foundation_model="gpt-4o-2024-11-20",
            content_collection_plan_gen=True,
            save_variations=False,
        )
        collection_gen_env = GenerateEnv(
            collection_data_wrapper, collection_gen_settings
        )

        mock_playbook_handler = MagicMock()
        mock_content_collection = MagicMock()
        generator = ContentCollectionPlanGen(
            mock_playbook_handler,
            mock_content_collection,
            gen_env=collection_gen_env,
        )

        # Test with no features
        mock_input_builder = MagicMock()
        mock_input_builder.all_features = {}
        result = generator.get_content_collection_instructions_prompt(
            mock_input_builder
        )
        assert result == ""

        # Test with features but no custom_prompts_string
        mock_input_builder.all_features = {"other_feature": "value"}
        result = generator.get_content_collection_instructions_prompt(
            mock_input_builder
        )
        assert result == ""

        # Test with custom_prompts_string
        mock_input_builder.all_features = {"custom_prompts_string": "test prompts"}
        result = generator.get_content_collection_instructions_prompt(
            mock_input_builder
        )
        assert "test prompts" in result
