import unittest
from unittest.mock import Mock, patch

from ..s3_utils import download_url_content_to_s3


class TestS3Utils(unittest.TestCase):
    @patch("api.s3_utils.boto3")
    @patch("api.s3_utils.requests.get")
    @patch("api.s3_utils.check_file_exists")
    def test_download_url_content_to_s3(
        self, mock_check_file, mock_requests_get, mock_boto3
    ):
        # Setup mocks
        mock_check_file.return_value = False

        # Mock response from requests
        mock_response = Mock()
        mock_response.iter_content.return_value = [b"test content"]
        mock_requests_get.return_value = mock_response

        # Mock S3 client
        mock_s3_client = Mock()
        mock_boto3.client.return_value = mock_s3_client

        # Test parameters
        url = "http://example.com/test.pdf"
        bucket = "test-bucket"
        filename = "test.pdf"
        content_type = "application/pdf"

        # Execute
        download_url_content_to_s3(url, bucket, filename, content_type)

        # Verify
        mock_requests_get.assert_called_once_with(url, stream=True, timeout=90)

        # Verify S3 upload was called
        mock_s3_client.upload_file.assert_called_once()
        args = mock_s3_client.upload_file.call_args[0]  # Positional arguments
        kwargs = mock_s3_client.upload_file.call_args[1]  # Keyword arguments

        # Verify the temp file was uploaded to correct bucket and filename
        self.assertEqual(args[1], bucket)  # Second arg is bucket
        self.assertEqual(args[2], filename)  # Third arg is key/filename
        self.assertEqual(kwargs.get("ExtraArgs"), {"ContentType": "application/pdf"})

    @patch("api.s3_utils.check_file_exists")
    @patch("api.s3_utils.requests.get")
    def test_download_url_content_to_s3_file_exists(
        self, mock_requests_get, mock_check_file
    ):
        # Setup mock to indicate file already exists
        mock_check_file.return_value = True

        # Test parameters
        url = "http://example.com/test.pdf"
        bucket = "test-bucket"
        filename = "test.pdf"

        # Execute
        download_url_content_to_s3(url, bucket, filename)

        # Verify that requests.get was never called since file exists
        mock_requests_get.assert_not_called()


if __name__ == "__main__":
    unittest.main()
