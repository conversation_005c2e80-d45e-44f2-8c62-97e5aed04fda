from unittest.mock import MagicMock, patch

from django.test import TestCase

from ..gdrive_utils import copy_google_drive_file, set_google_drive_permissions_public


class TestGDriveUtils(TestCase):
    def setUp(self):
        self.test_google_slides_url = "https://docs.google.com/presentation/d/test/edit"
        self.test_cloudfront_url = (
            "https://d2uf75zdrg71z4.cloudfront.net/tofuhq.com/creations/test.pptx"
        )

    def _mock_credentials(self):
        # Create the base credentials mock
        mock_creds = MagicMock()
        mock_creds.universe_domain = "googleapis.com"

        # Create the authorized credentials mock
        mock_auth_creds = MagicMock()
        mock_auth_creds.universe_domain = "googleapis.com"
        mock_auth_creds.credentials = mock_auth_creds

        # Create the final credentials mock that will be returned by authorize
        mock_final_creds = MagicMock()
        mock_final_creds.universe_domain = "googleapis.com"
        mock_final_creds.credentials = mock_final_creds

        # Set up the chain
        mock_creds.create_scoped.return_value = mock_creds
        mock_creds.authorize.return_value = mock_final_creds

        return mock_creds

    def _mock_http(self):
        mock_http = MagicMock()
        mock_response = MagicMock()
        mock_response.status = 200
        mock_http.request.return_value = (mock_response, b'{"data": {}}')
        return mock_http

    @patch("api.gdrive_file_ops.service_account.Credentials.from_service_account_file")
    @patch("api.gdrive_file_ops.googleapiclient.discovery.build")
    @patch("api.gdrive_file_ops.extract_google_drive_file_id")
    def test_set_google_drive_permissions_public(
        self, mock_extract_id, mock_build, mock_credentials
    ):
        # Setup
        mock_extract_id.return_value = "test_file_id"
        mock_creds = self._mock_credentials()
        mock_credentials.return_value = mock_creds

        # Create the mock drive service
        mock_drive = MagicMock()
        mock_build.return_value = mock_drive

        # Mock the permissions chain properly
        mock_permissions = MagicMock()
        mock_create = MagicMock()
        mock_create_execute = MagicMock()
        mock_create_execute.return_value = {"id": "permission_id"}
        mock_create.execute = mock_create_execute
        mock_permissions.create.return_value = mock_create

        # Set up permissions() as a method that returns the mock_permissions object
        mock_drive.permissions.return_value = mock_permissions

        # Execute
        result = set_google_drive_permissions_public(self.test_google_slides_url)

        # Assert
        self.assertTrue(result)
        mock_extract_id.assert_called_once_with(self.test_google_slides_url)

    @patch("api.gdrive_file_ops.service_account.Credentials.from_service_account_file")
    @patch("api.gdrive_file_ops.googleapiclient.discovery.build")
    @patch("api.gdrive_file_ops.extract_google_drive_file_id")
    @patch("api.gdrive_file_ops._copy_file_only")
    @patch("api.gdrive_file_ops._apply_permissions_to_file")
    def test_copy_google_drive_file(
        self,
        mock_apply_permissions,
        mock_copy_file_only,
        mock_extract_id,
        mock_build,
        mock_credentials,
    ):
        # Setup
        mock_extract_id.return_value = "test_file_id"
        mock_creds = self._mock_credentials()
        mock_credentials.return_value = mock_creds

        # Create a mock drive service
        mock_drive = MagicMock()
        mock_build.return_value = mock_drive

        # Create a mock files service
        mock_files = MagicMock()
        mock_drive.files.return_value = mock_files

        # Set up the mock chain for files().get(fileId=file_id, fields="name").execute()
        mock_get_request = MagicMock()
        mock_get_request.execute.return_value = {"name": "Original Presentation"}
        mock_files.get.return_value = mock_get_request

        # Mock the helper functions
        mock_copy_file_only.return_value = "new_file_id"
        mock_apply_permissions.return_value = None  # Successful permission application

        # Execute
        result = copy_google_drive_file(self.test_google_slides_url)

        # Assert
        self.assertEqual(
            result, "https://docs.google.com/presentation/d/new_file_id/edit"
        )
        mock_extract_id.assert_called_once_with(self.test_google_slides_url)
        mock_copy_file_only.assert_called_once_with(
            "test_file_id", "(COPY) Original Presentation", mock_drive
        )
        mock_apply_permissions.assert_called_once_with(
            "test_file_id", "new_file_id", mock_drive
        )

    @patch("api.gdrive_file_ops.service_account.Credentials.from_service_account_file")
    @patch("api.gdrive_file_ops.googleapiclient.discovery.build")
    @patch("api.gdrive_file_ops.extract_google_drive_file_id")
    @patch("api.gdrive_file_ops._copy_file_only")
    @patch("api.gdrive_file_ops._apply_permissions_to_file")
    @patch("api.gdrive_file_ops._delete_google_drive_file")
    def test_copy_google_drive_file_cleanup_on_permission_failure(
        self,
        mock_delete_file,
        mock_apply_permissions,
        mock_copy_file_only,
        mock_extract_id,
        mock_build,
        mock_credentials,
    ):
        """Test that orphaned files are cleaned up when permission application fails."""
        # Setup
        mock_extract_id.return_value = "test_file_id"
        mock_creds = self._mock_credentials()
        mock_credentials.return_value = mock_creds

        # Create a mock drive service
        mock_drive = MagicMock()
        mock_build.return_value = mock_drive

        # Create a mock files service
        mock_files = MagicMock()
        mock_drive.files.return_value = mock_files

        # Set up the mock chain for files().get(fileId=file_id, fields="name").execute()
        mock_get_request = MagicMock()
        mock_get_request.execute.return_value = {"name": "Original Presentation"}
        mock_files.get.return_value = mock_get_request

        # Mock the helper functions - copy succeeds but permissions fail
        mock_copy_file_only.return_value = "new_file_id"
        mock_apply_permissions.side_effect = Exception("Permission application failed")

        # Execute and expect exception
        with self.assertRaises(Exception) as context:
            copy_google_drive_file(self.test_google_slides_url)

        self.assertEqual(str(context.exception), "Permission application failed")

        # Assert that cleanup was called
        mock_delete_file.assert_called_once_with("new_file_id", mock_drive)

    def test_should_skip_permission_filters(self):
        """Test the permission filtering logic."""
        from ..gdrive_file_ops import _should_skip_permission

        # Test owner permission - should be skipped
        owner_permission = {"role": "owner", "emailAddress": "<EMAIL>"}
        self.assertTrue(_should_skip_permission(owner_permission))

        # Test service account permission - should be skipped
        service_permission = {
            "role": "writer",
            "emailAddress": "<EMAIL>",
        }
        self.assertTrue(_should_skip_permission(service_permission))

        # Test FlashDocs permission - should be skipped
        flashdocs_permission = {"role": "writer", "emailAddress": "<EMAIL>"}
        self.assertTrue(_should_skip_permission(flashdocs_permission))

        # Test regular user permission - should not be skipped
        user_permission = {"role": "writer", "emailAddress": "<EMAIL>"}
        self.assertFalse(_should_skip_permission(user_permission))

        # Test permission without email - should not be skipped (edge case)
        anonymous_permission = {"role": "reader", "type": "anyone"}
        self.assertFalse(_should_skip_permission(anonymous_permission))

    @patch("api.gdrive_file_ops.service_account.Credentials.from_service_account_file")
    @patch("api.gdrive_file_ops.googleapiclient.discovery.build")
    @patch("api.gdrive_file_ops.extract_google_drive_file_id")
    @patch("api.gdrive_file_ops._should_skip_permission")
    def test_apply_permissions_to_file_filtering(
        self, mock_should_skip, mock_extract_id, mock_build, mock_credentials
    ):
        """Test that _apply_permissions_to_file properly filters permissions."""
        from ..gdrive_file_ops import _apply_permissions_to_file

        # Setup
        mock_creds = self._mock_credentials()
        mock_credentials.return_value = mock_creds
        mock_drive = MagicMock()
        mock_build.return_value = mock_drive

        # Mock permissions response
        mock_permissions = MagicMock()
        mock_drive.permissions.return_value = mock_permissions

        mock_list_request = MagicMock()
        test_permissions = [
            {"id": "1", "role": "writer", "emailAddress": "<EMAIL>"},
            {
                "id": "2",
                "role": "owner",
                "emailAddress": "<EMAIL>",
            },  # Now included (not skipped)
            {"id": "3", "role": "writer", "emailAddress": "<EMAIL>"},
        ]
        mock_list_request.execute.return_value = {"permissions": test_permissions}
        mock_permissions.list.return_value = mock_list_request

        # Mock create permission
        mock_create_request = MagicMock()
        mock_create_request.execute.return_value = {"id": "new_permission"}
        mock_permissions.create.return_value = mock_create_request

        # Configure skip logic - don't skip any permissions now
        mock_should_skip.return_value = False

        # Execute
        _apply_permissions_to_file("source_id", "target_id", mock_drive)

        # Assert - should create all 3 permissions (including owner)
        self.assertEqual(mock_permissions.create.call_count, 3)

        # Verify all permissions were processed
        create_calls = mock_permissions.create.call_args_list
        created_emails = [call[1]["body"]["emailAddress"] for call in create_calls]
        self.assertIn("<EMAIL>", created_emails)
        self.assertIn("<EMAIL>", created_emails)
        self.assertIn("<EMAIL>", created_emails)
