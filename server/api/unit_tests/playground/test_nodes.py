import time
from unittest.mock import Mock, patch

import pytest
from api.model_caller import Model<PERSON>aller
from api.playground.chatbot_status import (
    ChatbotStepStatusUpdator,
    PlaygroundChatbotStep,
)
from api.playground.langgraph.models import UserIntent
from api.playground.langgraph.nodes.base_node import BaseNode
from api.playground.langgraph.nodes.reflection_node import ReflectionNode
from api.playground.langgraph.nodes.tool_selection_node import ToolSelectionNode
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, ToolMessage
from langchain_core.tools import BaseTool
from pydantic import BaseModel


class TestBaseNode(BaseNode):
    """Concrete implementation of BaseNode for testing."""

    node_name = "test_base_node"
    model_name = "gpt-4o-2024-11-20"  # Update model name

    def process(self, state):
        return state


class MockTimeoutNode(BaseNode):
    """Mock node for timeout behavior testing"""

    def __init__(self):
        self.status_updator = Mock()

    @property
    def node_name(self) -> str:
        return "test_timeout_node"

    def process(self, state):
        return state


@pytest.fixture
def mock_model_config():
    class ModelParam:
        def __init__(self, params):
            self.model_params = params

    mock = Mock()
    mock.model_params_list = [
        ModelParam(
            {"model_name": "gpt-4o-2024-11-20", "temperature": 0.7, "max_tokens": 1000}
        )
    ]
    return mock


@pytest.fixture
def mock_status_updator():
    return Mock(spec=ChatbotStepStatusUpdator)


@pytest.fixture
def mock_model_caller(mock_model_config):
    mock = Mock(spec=ModelCaller)
    mock.model_config = mock_model_config
    return mock


@pytest.fixture
def base_state():
    return {
        "task_id": "test_task",
        "playbook_id": "test_playbook",
        "messages": [],
        "intent": UserIntent.MARKETING_QUESTION,
        "synthesis_content": "",
        "reflection_output": "",
        "final_response": "",
        "current_token_count": 0,
        "raw_tool_results": [],
        "tool_result": "",
        "needs_more_info": False,
        "tool_round": 0,
    }


def test_get_latest_message(mock_model_caller, base_state, mock_status_updator):
    node = TestBaseNode(
        model_name="gpt-4o-2024-11-20", status_updator=mock_status_updator
    )  # Update model name

    # Test with no messages
    with pytest.raises(ValueError, match="No messages found in state"):
        node.get_latest_message(base_state)

    # Test with messages
    base_state["messages"] = [
        HumanMessage(content="Hello"),
        AIMessage(content="Hi there"),
    ]
    latest_message = node.get_latest_message(base_state)
    assert latest_message == base_state["messages"][-1]


def test_get_latest_ai_response(mock_model_caller, base_state, mock_status_updator):
    node = TestBaseNode(
        model_name="gpt-4o-2024-11-20", status_updator=mock_status_updator
    )  # Update model name

    # Test with no messages
    result = {"messages": []}
    with pytest.raises(ValueError, match="No AI response found in result"):
        node.get_latest_ai_response(result)

    # Test with no AI message
    result = {"messages": [HumanMessage(content="Hello")]}
    with pytest.raises(ValueError, match="No AI response found in result"):
        node.get_latest_ai_response(result)

    # Test with AI message
    result = {
        "messages": [HumanMessage(content="Hello"), AIMessage(content="Hi there")]
    }
    assert node.get_latest_ai_response(result) == "Hi there"


def test_get_user_original_query(mock_model_caller, base_state, mock_status_updator):
    node = TestBaseNode(
        model_name="gpt-4o-2024-11-20", status_updator=mock_status_updator
    )  # Update model name

    # Test with no messages
    with pytest.raises(ValueError, match="No messages found in state"):
        node.get_user_original_query(base_state)

    # Test with no user message
    base_state["messages"] = [AIMessage(content="Hi there")]
    with pytest.raises(ValueError, match="No user message found in message history"):
        node.get_user_original_query(base_state)

    # Test with user message
    base_state["messages"] = [
        HumanMessage(content="Hello"),
        AIMessage(content="Hi there"),
    ]
    assert node.get_user_original_query(base_state) == "Hello"


class TestToolArgs(BaseModel):
    query: str


class TestTool(BaseTool):
    name: str = "test_tool"
    description: str = "A test tool"
    args_schema: type[BaseModel] = TestToolArgs

    def _run(self, query: str):
        return f"Result for {query}"


@patch("api.playground.langgraph.nodes.tool_selection_node.ToolSelectionNode.get_llm")
def test_tool_selection_node(mock_get_llm, mock_model_caller, base_state):
    # Mock the LLM
    mock_llm = Mock()
    mock_get_llm.return_value = mock_llm

    # Create a proper tool with schema
    test_tool = TestTool()

    status_updator = ChatbotStepStatusUpdator(task_id="test_task")

    # Create the node with just the tools parameter
    node = ToolSelectionNode(tools=[test_tool], status_updator=status_updator)

    # Mock the tool executor response
    mock_result = {
        "messages": [
            HumanMessage(content="Test question"),
            ToolMessage(
                tool_call_id="123", name="test_tool", content="Tool execution result"
            ),
            AIMessage(content="Final response"),
        ]
    }

    # Mock the tool_executor
    node.tool_executor = Mock(return_value=mock_result)

    # Test with a basic state
    base_state["messages"] = [HumanMessage(content="Test question")]
    result = node.process(base_state)

    # Verify tool_executor was called
    node.tool_executor.assert_called_once_with(base_state)

    # Verify tool_result is set in state
    assert "tool_result" in result
    assert result["tool_result"] == "Final response"

    # Verify raw_tool_results is set in state
    assert "raw_tool_results" in result
    assert len(result["raw_tool_results"]) == 1
    assert result["raw_tool_results"][0]["tool_call_id"] == "123"
    assert result["raw_tool_results"][0]["name"] == "test_tool"
    assert result["raw_tool_results"][0]["content"] == "Tool execution result"


@patch("api.playground.langgraph.nodes.tool_selection_node.ToolSelectionNode.get_llm")
def test_tool_selection_node_step_limit_zero(
    mock_get_llm, mock_model_caller, base_state
):
    mock_llm = Mock()
    mock_get_llm.return_value = mock_llm
    test_tool = TestTool()
    status_updator = ChatbotStepStatusUpdator(task_id="test_task")
    node = ToolSelectionNode(tools=[test_tool], status_updator=status_updator)
    state = base_state.copy()
    state["messages"] = [HumanMessage(content="Test question")]
    state["remaining_steps"] = 0
    result = node.process(state)
    assert any(
        isinstance(m, AIMessage) and "Step limit reached" in m.content
        for m in result["messages"]
    )
    assert result["tool_result"] == "Step limit reached. Returning best results so far."


@patch("api.playground.langgraph.nodes.tool_selection_node.ToolSelectionNode.get_llm")
def test_tool_selection_node_step_limit_negative(
    mock_get_llm, mock_model_caller, base_state
):
    mock_llm = Mock()
    mock_get_llm.return_value = mock_llm
    test_tool = TestTool()
    status_updator = ChatbotStepStatusUpdator(task_id="test_task")
    node = ToolSelectionNode(tools=[test_tool], status_updator=status_updator)
    state = base_state.copy()
    state["messages"] = [HumanMessage(content="Test question")]
    state["remaining_steps"] = -1
    result = node.process(state)
    assert any(
        isinstance(m, AIMessage) and "Step limit reached" in m.content
        for m in result["messages"]
    )
    assert result["tool_result"] == "Step limit reached. Returning best results so far."


@patch("api.playground.langgraph.nodes.tool_selection_node.ToolSelectionNode.get_llm")
def test_tool_selection_node_step_limit_positive(
    mock_get_llm, mock_model_caller, base_state
):
    mock_llm = Mock()
    mock_get_llm.return_value = mock_llm
    test_tool = TestTool()
    status_updator = ChatbotStepStatusUpdator(task_id="test_task")
    node = ToolSelectionNode(tools=[test_tool], status_updator=status_updator)
    # Should call tool_executor
    node.tool_executor = Mock(
        return_value={"messages": [AIMessage(content="Normal response")]}
    )
    state = base_state.copy()
    state["messages"] = [HumanMessage(content="Test question")]
    state["remaining_steps"] = 2
    with patch.object(node, "get_latest_ai_response", return_value="Normal response"):
        result = node.process(state)
        node.tool_executor.assert_called_once_with(state)
        assert result["tool_result"] == "Normal response"


@patch("api.playground.langgraph.nodes.tool_selection_node.ToolSelectionNode.get_llm")
def test_tool_selection_node_prompt_includes_reflection(
    mock_get_llm, mock_model_caller, base_state
):
    mock_llm = Mock()
    mock_get_llm.return_value = mock_llm
    test_tool = TestTool()
    status_updator = ChatbotStepStatusUpdator(task_id="test_task")
    # Copy the tool_call_prompt logic from ToolSelectionNode
    from langchain_core.messages import BaseMessage, SystemMessage

    def tool_call_prompt(state):
        messages = list(state.get("messages", []))
        remaining_steps = state.get("remaining_steps")
        system_additions = [
            "======================",
            "Tool Calling Rules: ",
            f"You have {remaining_steps} steps left to call tools. If the remaining steps is 0, stop and return the best results you can.",
        ]
        reflection_result = state.get("reflection_output")
        if reflection_result:
            system_additions.append(
                f"""Previous reflection result: {reflection_result}. 
                                        For the next tool call, please only focus on the missing information from the previous reflection result.
                                        If you believe we can't get more information from the previous reflection result, stop calling tools and return the best results you can.
                                        """
            )
        if messages and isinstance(messages[0], SystemMessage):
            original_content = messages[0].content
            merged_content = original_content + "\n" + "\n".join(system_additions)
            messages[0] = SystemMessage(content=merged_content)
        else:
            merged_content = "\n".join(system_additions)
            messages.insert(0, SystemMessage(content=merged_content))
        return messages

    state = base_state.copy()
    state["messages"] = [
        SystemMessage(content="System info"),
        HumanMessage(content="Test question"),
    ]
    state["remaining_steps"] = 5
    state["reflection_output"] = "Some reflection here"
    prompt = tool_call_prompt(state)
    assert any(
        isinstance(m, SystemMessage) and "Some reflection here" in m.content
        for m in prompt
    )


@pytest.fixture
def mock_model_caller():
    mock_caller = Mock(spec=ModelCaller)
    mock_caller.model_config = Mock()
    mock_caller.get_llm_dict_response.return_value = {
        "needs_more_info": True,
        "reflection_output": "Test reflection output",
    }
    mock_caller.model_config.model_budget = 1000
    return mock_caller


@pytest.fixture
def reflection_node(mock_model_caller):
    status_updator = Mock(spec=ChatbotStepStatusUpdator)
    node = ReflectionNode(status_updator)
    node.model_caller = mock_model_caller
    return node


@pytest.fixture
def reflection_state():
    return {
        "task_id": "test_task",
        "playbook_id": "test_playbook",
        "messages": [HumanMessage(content="Test question")],
        "current_token_count": 0,
        "final_response": "",
        "intent": None,
        "tool_round": 0,
        "needs_more_info": False,
        "reflection_output": "",
        "synthesis_content": "some dummy content",
        "raw_tool_results": [],
        "tool_result": "Some dummy results",
    }


def test_reflection_node_successful_reflection(
    reflection_node, reflection_state, mock_model_caller
):
    # Mock the model caller response
    mock_model_caller.get_llm_dict_response.return_value = {
        "needs_more_info": True,
        "reflection_output": "Test reflection output",
    }

    # Process the state
    result = reflection_node.process(reflection_state)

    # Verify state updates
    assert result["needs_more_info"] is True
    assert result["reflection_output"] == "Test reflection output"
    assert result["tool_round"] == 1


def test_reflection_node_no_more_info_needed(
    reflection_node, reflection_state, mock_model_caller
):
    # Mock the model caller response
    mock_model_caller.get_llm_dict_response.return_value = {
        "needs_more_info": False,
        "reflection_output": "Test reflection output",
    }

    # Process the state
    result = reflection_node.process(reflection_state)

    # Verify state updates
    assert result["needs_more_info"] is False
    assert result["reflection_output"] == "Test reflection output"
    assert result["tool_round"] == 0


def test_reflection_node_max_rounds_reached(
    reflection_node, reflection_state, mock_model_caller
):
    # Set max rounds reached
    reflection_state["tool_round"] = 5

    # Process the state
    result = reflection_node.process(reflection_state)

    # Verify state updates
    assert result["needs_more_info"] is False
    assert result["reflection_output"] == "Maximum research rounds reached"
    assert result["tool_round"] == 5


def test_reflection_node_missing_data(
    reflection_node, reflection_state, mock_model_caller
):
    # Remove required data
    reflection_state.pop("messages")

    # Process the state
    result = reflection_node.process(reflection_state)

    # Verify state updates
    assert result["needs_more_info"] is False
    assert (
        result["reflection_output"]
        == "Error during reflection: No messages found in state"
    )
    assert result["tool_round"] == 0


def test_get_previous_messages_str(mock_model_caller, base_state, mock_status_updator):
    node = TestBaseNode(
        model_name="gpt-4o-2024-11-20", status_updator=mock_status_updator
    )

    # Test case 1: No previous messages (less than 3 messages)
    base_state["messages"] = [
        HumanMessage(content="Hello"),
        AIMessage(content="Hi there"),
    ]
    result = node._get_previous_messages_str(base_state, token_budget=1000)
    assert result == ""

    # Test case 2: Messages within token budget
    base_state["messages"] = [
        HumanMessage(content="First message"),
        AIMessage(content="First response"),
        HumanMessage(content="Second message"),
        AIMessage(content="Second response"),
    ]
    result = node._get_previous_messages_str(base_state, token_budget=1000)
    assert result == "First message\nFirst response\nSecond message\nSecond response"

    # Test case 3: Messages exceeding token budget
    base_state["messages"] = [
        HumanMessage(content="Very long message that exceeds token budget" * 10),
        AIMessage(content="Very long response that exceeds token budget" * 10),
        HumanMessage(content="Second message"),
        AIMessage(content="Second response"),
    ]
    result = node._get_previous_messages_str(base_state, token_budget=100)
    # Should trim from start until it fits within budget
    assert result == "Second message\nSecond response"

    # Test case 4: Invalid token budget
    with pytest.raises(ValueError, match="Token budget must be greater than 0"):
        node._get_previous_messages_str(base_state, token_budget=-1)

    # Test case 5: All messages exceed token budget, should return whatever remains
    base_state["messages"] = [
        HumanMessage(content="First message"),
        AIMessage(content="First response"),
        HumanMessage(content="Second message"),
        AIMessage(content="Second response"),
    ]
    result = node._get_previous_messages_str(base_state, token_budget=1)
    assert result == "Second message\nSecond response"


# Timeout Shortcircuit Tests
class TestTimeoutShortCircuit:

    def setUp(self):
        self.test_node = MockTimeoutNode()
        self.mock_status_updator = Mock()

    def test_timeout_not_approaching(self):
        """Test that timeout check returns False when under threshold"""
        test_node = MockTimeoutNode()
        state = {"execution_start_time": time.time(), "timeout_seconds": 480}

        result = test_node.is_timeout_approaching(state, threshold_percentage=0.7)
        assert result is False

    def test_timeout_approaching(self):
        """Test that timeout check returns True when over threshold"""
        test_node = MockTimeoutNode()
        # Simulate 400 seconds ago (83% of 480 seconds)
        state = {"execution_start_time": time.time() - 400, "timeout_seconds": 480}

        result = test_node.is_timeout_approaching(state, threshold_percentage=0.7)
        assert result is True

    @patch(
        "api.playground.langgraph.nodes.reflection_node.BaseNode.get_model_caller_from_model_name"
    )
    def test_reflection_node_timeout_shortcircuit(self, mock_model_caller):
        """Test reflection node skips when timeout approaching"""
        # Mock the model caller
        mock_model_caller.return_value = Mock()

        mock_status_updator = Mock()
        reflection_node = ReflectionNode(
            status_updator=mock_status_updator,
            model_name="us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        )

        state = {
            "execution_start_time": time.time() - 400,  # 400 seconds ago
            "timeout_seconds": 480,
            "tool_round": 1,  # Not at max rounds
            "remaining_steps": 10,  # Plenty of steps
            "synthesis_content": "Some content",
            "tool_result": "Some tool result",
            "messages": [Mock()],  # Add messages to avoid errors
        }

        result = reflection_node.process(state)

        # Should skip reflection and set needs_more_info to False
        assert result["needs_more_info"] is False
        assert "Timeout threshold reached" in result["reflection_output"]

    @patch(
        "api.playground.langgraph.nodes.tool_selection_node.BaseNode.get_model_caller_from_model_name"
    )
    @patch(
        "api.playground.langgraph.nodes.tool_selection_node.ToolSelectionNode.get_llm"
    )
    @patch(
        "api.playground.langgraph.nodes.tool_selection_node.ToolSelectionNode.custom_agent_executor"
    )
    def test_tool_selection_node_timeout_shortcircuit(
        self, mock_executor, mock_llm, mock_model_caller
    ):
        """Test tool selection node skips when timeout approaching"""
        # Mock the dependencies
        mock_model_caller.return_value = Mock()
        mock_llm.return_value = Mock()
        mock_executor.return_value = lambda state: state

        mock_status_updator = Mock()
        mock_tools = []
        tool_node = ToolSelectionNode(
            status_updator=mock_status_updator,
            tools=mock_tools,
            model_name="us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        )

        state = {
            "execution_start_time": time.time() - 400,  # 400 seconds ago
            "timeout_seconds": 480,
            "intent": "marketing_question",
            "messages": [],
        }

        result = tool_node.process(state)

        # Should skip tool execution
        assert "Tool execution skipped" in result["tool_result"]
        assert result["raw_tool_results"] == []
