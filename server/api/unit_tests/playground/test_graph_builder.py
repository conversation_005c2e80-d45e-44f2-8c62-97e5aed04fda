from unittest.mock import Mock, patch

import pytest
from api.feature.data_wrapper.data_wrapper import GenerateEnv
from api.playground.chatbot_status import ChatbotStepStatusUpdator
from api.playground.langgraph.graph_builder import Graph<PERSON>uilder, get_or_create_graph
from api.playground.playground_chat_generator import GenerateChatRequest
from langgraph.graph import StateGraph
from langgraph.graph.state import CompiledStateGraph


@pytest.fixture
def mock_gen_env():
    mock = Mock(spec=GenerateEnv)
    mock._data_wrapper = Mock()
    mock._gen_settings = Mock()
    mock._data_wrapper.playbook_instance = Mock()
    mock._data_wrapper.playbook_instance.id = "test-playbook-id"
    return mock


@patch("api.playground.langgraph.graph_builder.create_tools")
@patch("api.playground.langgraph.graph_builder.create_final_response_tools")
@patch("api.playground.langgraph.graph_builder.IntentIdentificationNode")
@patch("api.playground.langgraph.graph_builder.ToolSelectionNode")
@patch("api.playground.langgraph.graph_builder.ResultSynthesisNode")
@patch("api.playground.langgraph.graph_builder.ReflectionNode")
@patch("api.playground.langgraph.graph_builder.ResponseGenerationNode")
def test_get_or_create_graph(
    mock_response_node,
    mock_reflection_node,
    mock_synthesis_node,
    mock_tool_node,
    mock_intent_node,
    mock_create_final_tools,
    mock_create_tools,
    mock_gen_env,
):
    # Setup mocks
    mock_create_tools.return_value = {}
    mock_create_final_tools.return_value = {}

    # Mock node instances with proper node_name properties
    intent_node = Mock()
    intent_node.node_name = "intent_identification"
    mock_intent_node.return_value = intent_node

    tool_node = Mock()
    tool_node.node_name = "tool_selection"
    mock_tool_node.return_value = tool_node

    synthesis_node = Mock()
    synthesis_node.node_name = "result_synthesis"
    mock_synthesis_node.return_value = synthesis_node

    reflection_node = Mock()
    reflection_node.node_name = "reflection"
    mock_reflection_node.return_value = reflection_node

    response_node = Mock()
    response_node.node_name = "response_generation"
    mock_response_node.return_value = response_node

    status_updator = Mock(spec=ChatbotStepStatusUpdator)

    thread_id_1 = "test_thread_1"
    chat_model_name = "gpt-4o-2024-11-20"
    chat_request = GenerateChatRequest(
        task_id="test_task_id",
        model=chat_model_name,
        previous_messages=[],
        new_message="test_message",
        use_company_info=True,
        use_brand_guidelines=True,
        targets=None,
        assets=None,
    )

    # First call should create a new graph
    graph_1 = get_or_create_graph(
        thread_id_1, chat_request, mock_gen_env, status_updator
    )
    assert isinstance(graph_1, (StateGraph, CompiledStateGraph))

    same_graph = get_or_create_graph(
        thread_id_1, chat_request, mock_gen_env, status_updator
    )
    assert graph_1 != same_graph
    assert id(graph_1) != id(same_graph)

    thread_id_2 = "test_thread_2"
    graph_2 = get_or_create_graph(
        thread_id_2, chat_request, mock_gen_env, status_updator
    )
    assert isinstance(graph_2, (StateGraph, CompiledStateGraph))
    assert graph_1 != graph_2
    assert id(graph_1) != id(graph_2)
