from unittest.mock import Mock, patch

import pytest
from api.model_caller import <PERSON><PERSON><PERSON><PERSON>
from api.playground.chatbot_status import Cha<PERSON>botStepStatusUpdator
from api.playground.langgraph.nodes.response_generation_node import (
    ResponseGenerationNode,
)
from langchain_core.messages import HumanMessage, SystemMessage


@pytest.fixture
def mock_model_caller():
    mock_caller = Mock(spec=ModelCaller)
    mock_caller.model_config = Mock()
    mock_caller.model_config.model_budget = 1000
    return mock_caller


@pytest.fixture
def mock_status_updator():
    return Mock(spec=ChatbotStepStatusUpdator)


@pytest.fixture
def response_generation_node(mock_model_caller, mock_status_updator):
    node = ResponseGenerationNode(
        model_name="gpt-4o-2024-11-20", tools=[], status_updator=mock_status_updator
    )
    node.model_caller = mock_model_caller
    return node


@pytest.fixture
def base_state():
    return {
        "task_id": "test_task",
        "playbook_id": "test_playbook",
        "messages": [
            SystemMessage(content="System message"),
            HumanMessage(content="Test question"),
        ],
        "synthesis_content": "Test synthesis",
        "tool_result": "Test tool result",
        "tool_results": "Test tool results",
        "reflection_output": "Test reflection",
        "intent": "Test intent",
    }


def test_update_user_prompt(response_generation_node, base_state):
    updated_state = response_generation_node._update_user_prompt(base_state)

    # Get the final message content
    final_message = updated_state["messages"][-1].content

    # Check that all expected sections are present in the correct order
    expected_sections = [
        "User's Request:\nTest question",
        "Tool Results:\nTest tool result",
        "Reflection:\nTest reflection",
        "Intent:\nTest intent",
        "Based on the above information, please proceed accordingly.",
    ]

    # Verify each section is present in the correct order
    for section in expected_sections:
        assert section in final_message, f"Expected section not found: {section}"

    # Verify the sections are properly separated by double newlines
    assert "\n\n" in final_message

    # Verify the message is a HumanMessage
    assert isinstance(updated_state["messages"][-1], HumanMessage)

    # Verify the original message was removed
    assert len(updated_state["messages"]) == 2  # System message + new Human message
