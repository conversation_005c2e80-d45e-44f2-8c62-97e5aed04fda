from unittest.mock import MagicMock, patch

from api.playground.playground_utils import crawl_url, crawl_urls, remove_image_messages
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage


def test_remove_image_messages():
    # Test case 1: Basic image message with previous human message
    human_msg = HumanMessage(content="Show me an image")
    image_msg = AIMessage(
        content="", additional_kwargs={"image_urls": ["https://example.com/image.png"]}
    )
    system_msg = SystemMessage(content="System message")

    messages = [system_msg, human_msg, image_msg]
    filtered = remove_image_messages(messages)
    assert filtered == [system_msg]  # Both human and image messages should be removed

    # Test case 2: Image message without previous human message
    messages = [system_msg, image_msg]
    filtered = remove_image_messages(messages)
    assert filtered == [system_msg]  # Only image message should be removed

    # Test case 3: Image message with previous non-human message
    messages = [system_msg, system_msg, image_msg]
    filtered = remove_image_messages(messages)
    assert filtered == [
        system_msg,
        system_msg,
    ]  # Previous system message should be kept

    # Test case 4: Multiple image messages
    messages = [
        system_msg,
        HumanMessage(content="Show image 1"),
        AIMessage(
            content="",
            additional_kwargs={"image_urls": ["https://example.com/image1.png"]},
        ),
        HumanMessage(content="Show image 2"),
        AIMessage(
            content="",
            additional_kwargs={"image_urls": ["https://example.com/image2.png"]},
        ),
    ]
    filtered = remove_image_messages(messages)
    assert filtered == [system_msg]  # All human and image messages should be removed

    # Test case 5: Regular conversation without images
    messages = [
        system_msg,
        HumanMessage(content="Hello"),
        AIMessage(content="Hi there"),
        HumanMessage(content="How are you?"),
    ]
    filtered = remove_image_messages(messages)
    assert filtered == messages  # No messages should be removed

    # Test case 6: Human message with image URLs
    human_with_image = HumanMessage(
        content="Look at this image",
        additional_kwargs={"image_urls": ["https://example.com/uploaded.png"]},
    )
    ai_response = AIMessage(content="That's a nice image!")
    messages = [system_msg, human_with_image, ai_response]
    filtered = remove_image_messages(messages)
    assert filtered == [
        system_msg
    ]  # Both human with image and AI response should be removed

    # Test case 7: Human message with image URLs without AI response
    messages = [system_msg, human_with_image]
    filtered = remove_image_messages(messages)
    assert filtered == [system_msg]  # Only human with image should be removed

    # Test case 8: Mixed cases - both AI generated images and human uploaded images
    messages = [
        system_msg,
        HumanMessage(content="Show me an image"),
        AIMessage(
            content="",
            additional_kwargs={"image_urls": ["https://example.com/generated.png"]},
        ),
        HumanMessage(
            content="Here's my image",
            additional_kwargs={"image_urls": ["https://example.com/uploaded.png"]},
        ),
        AIMessage(content="Thanks for sharing!"),
    ]
    filtered = remove_image_messages(messages)
    assert filtered == [system_msg]  # All image-related messages should be removed


@patch("api.playground.playground_utils.TofuURLLoader")
def test_crawl_url(mock_tofu_loader):
    # Setup mock
    mock_loader_instance = MagicMock()
    mock_tofu_loader.return_value = mock_loader_instance
    mock_doc1 = MagicMock(page_content="Content 1")
    mock_doc2 = MagicMock(page_content="Content 2")
    mock_loader_instance.load.return_value = [mock_doc1, mock_doc2]

    # Test crawling
    result = crawl_url("https://example.com")

    # Verify
    mock_tofu_loader.assert_called_once_with("https://example.com")
    mock_loader_instance.load.assert_called_once()
    assert result == "Content 1\nContent 2"

    # Test truncation
    long_content = "x" * 6000
    mock_doc3 = MagicMock(page_content=long_content)
    mock_loader_instance.load.return_value = [mock_doc3]

    result = crawl_url("https://example.com")
    assert len(result) == 5000


@patch("api.playground.playground_utils.crawl_url")
def test_crawl_urls(mock_crawl_url):
    # Setup
    mock_crawl_url.side_effect = lambda url: f"Content for {url}"

    # Test with multiple URLs
    urls = ["https://example.com", "https://test.com"]
    result = crawl_urls(urls)

    # Verify
    assert result == {
        "https://example.com": "Content for https://example.com",
        "https://test.com": "Content for https://test.com",
    }
    assert mock_crawl_url.call_count == 2
