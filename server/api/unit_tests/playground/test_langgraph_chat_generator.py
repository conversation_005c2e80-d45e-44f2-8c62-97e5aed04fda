import time
import unittest
from unittest.mock import MagicMock, patch

import django.core.cache
from api.playground.chatbot_status import Playground<PERSON><PERSON>botStep, TaskState
from api.playground.langgraph.langgraph_chat_generator import (
    GenerateChatRequest,
    LangGraphAgentChatGenerator,
)
from api.playground.playground_utils import serialize_message_history
from langchain.schema import AIMessage, HumanMessage, SystemMessage


class TestLangGraphAgentChatGenerator(unittest.TestCase):
    def setUp(self):
        # Mock user and chat history
        self.mock_user = MagicMock()
        self.mock_chat_history = MagicMock()
        self.mock_chat_history.json = {"previous_messages": []}
        self.mock_chat_history.model = "test-model"

        # Mock graph
        self.mock_graph = MagicMock()

        # Create mock gen_env
        mock_gen_env = MagicMock()
        mock_gen_env._data_wrapper = MagicMock()
        mock_gen_env._data_wrapper.playbook_instance = MagicMock()
        mock_gen_env._data_wrapper.playbook_instance.id = "test-playbook-id"

        # Mock parent class initialization to set up gen_env
        def mock_parent_init(self, user, chat_history, thread_id):
            self.user = user
            self.chat_history = chat_history
            self.thread_id = thread_id
            self.gen_env = mock_gen_env

        self.parent_patcher = patch(
            "api.playground.langgraph.langgraph_chat_generator.BasePlaygroundChatGenerator.__init__",
            mock_parent_init,
        )
        self.mock_parent_init = self.parent_patcher.start()

        # Mock graph creation
        self.graph_patcher = patch(
            "api.playground.langgraph.langgraph_chat_generator.get_or_create_graph"
        )
        self.mock_get_graph = self.graph_patcher.start()
        self.mock_get_graph.return_value = self.mock_graph

        # Mock cache
        self.cache_patcher = patch("django.core.cache.cache")
        self.mock_cache = self.cache_patcher.start()

        # Mock message templates
        self.company_context_patcher = patch(
            "api.playground.langgraph.langgraph_chat_generator.company_context_message",
            "Company Context: {company_context}",
        )
        self.system_assets_patcher = patch(
            "api.playground.langgraph.langgraph_chat_generator.SYSTEM_ASSETS_PROMPT",
            "Assets: {assets}",
        )
        self.system_targets_patcher = patch(
            "api.playground.langgraph.langgraph_chat_generator.SYSTEM_TARGETS_PROMPT",
            "Targets: {targets}",
        )

        self.company_context_patcher.start()
        self.system_assets_patcher.start()
        self.system_targets_patcher.start()

        generate_chat_request = GenerateChatRequest(
            task_id="test-task",
            model="test-model",
            previous_messages=[],
            new_message="test-message",
            use_company_info=True,
            use_brand_guidelines=True,
            assets={"test": ["asset1", "asset2"]},
            targets={"test": ["target1", "target2"]},
        )

        # Initialize the generator
        self.generator = LangGraphAgentChatGenerator(
            user=self.mock_user,
            chat_history=self.mock_chat_history,
            thread_id="test-thread",
            chat_request=generate_chat_request,
        )

    def tearDown(self):
        self.parent_patcher.stop()
        self.graph_patcher.stop()
        self.cache_patcher.stop()
        self.company_context_patcher.stop()
        self.system_assets_patcher.stop()
        self.system_targets_patcher.stop()

    def test_timeout_state_initialization_includes_timing_fields(self):
        """Test that _initialize_state includes the new timeout tracking fields"""
        task_id = "test-task"
        playbook_id = "test-playbook"

        # Mock system context
        system_context = {
            "company_name": "Test Company",
            "company_context": "Test company context",
            "brand_guidelines": "Test brand guidelines",
        }

        with patch.object(
            self.generator, "get_system_context", return_value=system_context
        ):
            chat_input = GenerateChatRequest(
                task_id=task_id,
                new_message=HumanMessage(content="Hello"),
                previous_messages=[],
                model="test-model",
                use_company_info=False,
                use_brand_guidelines=False,
                assets={},
                targets={},
            )

            start_time = time.time()
            state = self.generator._initialize_state(chat_input, playbook_id)
            end_time = time.time()

            # Verify timeout tracking fields are included
            self.assertIn("execution_start_time", state)
            self.assertIn("timeout_seconds", state)

            # Verify values are reasonable
            self.assertGreaterEqual(state["execution_start_time"], start_time)
            self.assertLessEqual(state["execution_start_time"], end_time)
            self.assertEqual(
                state["timeout_seconds"], self.generator.timeout_in_seconds
            )

    def test_generate_chat_response_error(self):
        # Setup
        task_id = "test-task"
        model = "test-model"
        previous_messages = []
        new_message = HumanMessage(content="Hello")

        # Mock the _chat method to raise an exception
        with patch.object(
            self.generator, "_chat", side_effect=ValueError("Test error")
        ):
            with self.assertRaises(ValueError):
                generate_chat_request = GenerateChatRequest(
                    task_id=task_id,
                    model=model,
                    previous_messages=previous_messages,
                    new_message=new_message,
                )
                self.generator.generate_chat_response(generate_chat_request)

    def test_chat_method(self):
        # Setup
        task_id = "test-task"
        message = HumanMessage(content="Hello")
        playbook_id = "test-playbook"
        expected_response = "Test response"

        # Mock _run_graph_with_timeout instead of graph.invoke
        async def mock_run_graph_with_timeout(state):
            return {"final_response": expected_response}

        # Mock _initialize_state
        with (
            patch.object(
                self.generator, "_initialize_state", return_value={"messages": []}
            ),
            patch.object(
                self.generator,
                "_run_graph_with_timeout",
                side_effect=mock_run_graph_with_timeout,
            ),
        ):
            chat_input = GenerateChatRequest(
                task_id=task_id,
                new_message=message,
                previous_messages=[],
                model="test-model",
                use_company_info=True,
                use_brand_guidelines=True,
            )
            response = self.generator._chat(
                chat_input,
                playbook_id,
            )

            # Verify
            self.assertEqual(response, expected_response)
            self.generator._run_graph_with_timeout.assert_called_once()

    def test_initialize_state_with_all_features(self):
        # Setup
        task_id = "test-task"
        playbook_id = "test-playbook"

        # Mock system context with all required keys
        system_context = {
            "company_name": "Test Company",
            "company_context": "Test company context",
            "brand_guidelines": "Test brand guidelines",
        }

        with patch.object(
            self.generator, "get_system_context", return_value=system_context
        ):
            chat_input = GenerateChatRequest(
                task_id=task_id,
                new_message=HumanMessage(content="Hello"),
                previous_messages=[],
                model="test-model",
                use_company_info=True,
                use_brand_guidelines=True,
                assets={"test": ["asset1", "asset2"]},
                targets={"test": ["target1", "target2"]},
            )
            state = self.generator._initialize_state(
                chat_input,
                playbook_id,
            )

            # Verify
            self.assertEqual(state["task_id"], task_id)
            self.assertEqual(state["playbook_id"], playbook_id)
            self.assertEqual(state["assets"], {"test": ["asset1", "asset2"]})
            self.assertEqual(state["targets"], {"test": ["target1", "target2"]})
            self.assertTrue(isinstance(state["messages"][0], SystemMessage))
            self.assertTrue(
                "Company Context: Test company context" in state["messages"][0].content
            )
            self.assertTrue(
                "<Brand Guidelines Context>" in state["messages"][0].content
            )
            self.assertEqual(state["remaining_steps"], 20)
            self.assertFalse(state["is_last_step"])

    def test_initialize_state_without_company_info(self):
        # Setup
        task_id = "test-task"
        playbook_id = "test-playbook"

        # Mock system context with all required keys
        system_context = {
            "company_name": "Test Company",
            "company_context": "Test company context",
            "brand_guidelines": "Test brand guidelines",
        }

        with patch.object(
            self.generator, "get_system_context", return_value=system_context
        ):
            chat_input = GenerateChatRequest(
                task_id=task_id,
                new_message=HumanMessage(content="Hello"),
                previous_messages=[],
                model="test-model",
                use_company_info=False,
                use_brand_guidelines=True,
                assets={},
                targets={},
            )
            state = self.generator._initialize_state(
                chat_input,
                playbook_id,
            )

            # Verify
            self.assertEqual(state["task_id"], task_id)
            self.assertEqual(state["playbook_id"], playbook_id)
            self.assertEqual(state["assets"], {})
            self.assertEqual(state["targets"], {})
            self.assertTrue(isinstance(state["messages"][0], SystemMessage))
            self.assertFalse(
                "Company Context: Test company context" in state["messages"][0].content
            )
            self.assertTrue(
                "<Brand Guidelines Context>" in state["messages"][0].content
            )
            self.assertEqual(state["remaining_steps"], 20)
            self.assertFalse(state["is_last_step"])

    def test_initialize_state_without_brand_guidelines(self):
        # Setup
        task_id = "test-task"
        playbook_id = "test-playbook"

        # Mock system context with all required keys
        system_context = {
            "company_name": "Test Company",
            "company_context": "Test company context",
            "brand_guidelines": "Test brand guidelines",
        }

        with patch.object(
            self.generator, "get_system_context", return_value=system_context
        ):
            chat_input = GenerateChatRequest(
                task_id=task_id,
                new_message=HumanMessage(content="Hello"),
                previous_messages=[],
                model="test-model",
                use_company_info=True,
                use_brand_guidelines=False,
                assets={},
                targets={},
            )
            state = self.generator._initialize_state(
                chat_input,
                playbook_id,
            )

            # Verify
            self.assertEqual(state["task_id"], task_id)
            self.assertEqual(state["playbook_id"], playbook_id)
            self.assertEqual(state["assets"], {})
            self.assertEqual(state["targets"], {})
            self.assertTrue(isinstance(state["messages"][0], SystemMessage))
            self.assertTrue(
                "Company Context: Test company context" in state["messages"][0].content
            )
            self.assertFalse(
                "<Brand Guidelines Context>" in state["messages"][0].content
            )
            self.assertEqual(state["remaining_steps"], 20)
            self.assertFalse(state["is_last_step"])

    def test_initialize_state_without_features(self):
        # Setup
        task_id = "test-task"
        playbook_id = "test-playbook"

        # Mock system context with all required keys
        system_context = {
            "company_name": "Test Company",
            "company_context": "Test company context",
            "brand_guidelines": "Test brand guidelines",
        }

        with patch.object(
            self.generator, "get_system_context", return_value=system_context
        ):
            chat_input = GenerateChatRequest(
                task_id=task_id,
                new_message=HumanMessage(content="Hello"),
                previous_messages=[],
                model="test-model",
                use_company_info=False,
                use_brand_guidelines=False,
                assets={},
                targets={},
            )
            state = self.generator._initialize_state(
                chat_input,
                playbook_id,
            )

            # Verify
            self.assertEqual(state["task_id"], task_id)
            self.assertEqual(state["playbook_id"], playbook_id)
            self.assertEqual(state["assets"], {})
            self.assertEqual(state["targets"], {})
            self.assertTrue(isinstance(state["messages"][0], SystemMessage))
            self.assertFalse(
                "Company Context: Test company context" in state["messages"][0].content
            )
            self.assertFalse(
                "Brand Guidelines: Test brand guidelines"
                in state["messages"][0].content
            )
            self.assertEqual(state["remaining_steps"], 20)
            self.assertFalse(state["is_last_step"])

    def test_store_chat_history(self):
        # Prepare messages
        messages = [
            HumanMessage(content="Hello"),
            AIMessage(content="Hi there!"),
        ]
        chat_request = GenerateChatRequest(
            task_id="test-task",
            model="test-model-2",
            previous_messages=[],
            new_message="irrelevant for this test",
        )

        # Reset mock call history
        self.mock_chat_history.save.reset_mock()
        self.mock_chat_history.refresh_from_db.reset_mock()

        # Call the method
        self.generator._store_chat_history(messages, chat_request)

        # Check that json["previous_messages"] is correct
        expected_serialized = serialize_message_history(messages)
        self.assertEqual(
            self.mock_chat_history.json["previous_messages"], expected_serialized
        )
        # Check that model is updated
        self.assertEqual(self.mock_chat_history.model, "test-model-2")
        # Check that save and refresh_from_db were called
        self.mock_chat_history.save.assert_called_once()
        self.mock_chat_history.refresh_from_db.assert_called_once()

    def test_store_chat_history_called_twice_with_correct_parameters(self):
        # Prepare input
        previous_messages = [HumanMessage(content="Hello")]  # could be empty or not
        new_message = HumanMessage(content="How are you?")
        chat_request = GenerateChatRequest(
            task_id="test-task",
            model="test-model-2",
            previous_messages=previous_messages.copy(),
            new_message=new_message,
        )

        # Patch _store_chat_history and _chat
        with (
            patch.object(self.generator, "_store_chat_history") as mock_store,
            patch.object(
                self.generator, "_chat", return_value="I am fine!"
            ) as mock_chat,
        ):
            self.generator.generate_chat_response(chat_request)

            # Should be called twice
            self.assertEqual(mock_store.call_count, 2)

            # First call: previous_messages + new_message
            first_call_args, first_call_kwargs = mock_store.call_args_list[0]
            self.assertEqual(
                [m.content for m in first_call_args[0]],
                [m.content for m in previous_messages + [new_message]],
            )
            self.assertIs(first_call_args[1], chat_request)

            # Second call: previous_messages + new_message + AIMessage
            second_call_args, second_call_kwargs = mock_store.call_args_list[1]
            self.assertEqual(
                [m.content for m in second_call_args[0][:-1]],
                [m.content for m in previous_messages + [new_message]],
            )
            self.assertIsInstance(second_call_args[0][-1], AIMessage)
            self.assertEqual(second_call_args[0][-1].content, "I am fine!")
            self.assertIs(second_call_args[1], chat_request)

    def test_generate_chat_response_timeout_sets_failed_status(self):
        # Patch _chat to raise TimeoutError and check update_chatbot_failed is NOT called directly
        with patch.object(
            self.generator, "_chat", side_effect=TimeoutError("Timeout!")
        ):
            with patch.object(
                self.generator.status_updator, "update_chatbot_failed"
            ) as mock_failed:
                generate_chat_request = GenerateChatRequest(
                    task_id="test-task",
                    model="test-model",
                    previous_messages=[],
                    new_message=HumanMessage(content="Hello"),
                )
                with self.assertRaises(TimeoutError):
                    self.generator.generate_chat_response(generate_chat_request)
                mock_failed.assert_not_called()  # generate_chat_response does not call it directly

        # Patch _run_graph_with_timeout to raise TimeoutError in _chat, and check update_chatbot_failed is called
        with patch.object(self.generator, "_initialize_state", return_value={}):
            with patch.object(
                self.generator,
                "_run_graph_with_timeout",
                side_effect=TimeoutError("Timeout!"),
            ):
                with patch.object(
                    self.generator.status_updator, "update_chatbot_failed"
                ) as mock_failed:
                    chat_input = GenerateChatRequest(
                        task_id="test-task",
                        new_message=HumanMessage(content="Hello"),
                        previous_messages=[],
                        model="test-model",
                        use_company_info=True,
                        use_brand_guidelines=True,
                    )
                    with self.assertRaises(TimeoutError):
                        self.generator._chat(chat_input, "test-playbook")
                    mock_failed.assert_called_once()
                    # Check the error message
                    error_msg = mock_failed.call_args[0][0]
                    self.assertIn("timed out", error_msg)

    def test_generate_chat_response_generic_exception_sets_failed_status(self):
        # Patch _run_graph_with_timeout to raise a generic Exception in _chat, and check update_chatbot_failed is called
        with patch.object(self.generator, "_initialize_state", return_value={}):
            with patch.object(
                self.generator,
                "_run_graph_with_timeout",
                side_effect=Exception("Something went wrong!"),
            ):
                with patch.object(
                    self.generator.status_updator, "update_chatbot_failed"
                ) as mock_failed:
                    chat_input = GenerateChatRequest(
                        task_id="test-task",
                        new_message=HumanMessage(content="Hello"),
                        previous_messages=[],
                        model="test-model",
                        use_company_info=True,
                        use_brand_guidelines=True,
                    )
                    with self.assertRaises(Exception):
                        self.generator._chat(chat_input, "test-playbook")
                    mock_failed.assert_called_once()
                    # Check the error message
                    error_msg = mock_failed.call_args[0][0]
                    self.assertIn("please contact support", error_msg)
