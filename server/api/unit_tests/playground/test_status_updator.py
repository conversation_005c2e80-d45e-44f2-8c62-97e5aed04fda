import pytest
from api.playground.chatbot_status import (
    ChatbotStepStatusUpdator,
    PlaygroundChatbotStep,
    PlaygroundChatbotSubStep,
    TaskState,
)
from django.core.cache import cache


@pytest.fixture
def status_updator():
    # Clear cache before each test
    cache.clear()
    return ChatbotStepStatusUpdator("test_task_id")


def test_update_chatbot_step(status_updator):
    # Test updating a single step
    step = PlaygroundChatbotStep.THINKING
    output = "Analyzing the problem..."

    status_updator.update_chatbot_step(step, output)

    # Verify the status was stored in cache
    status = cache.get("test_task_id")
    assert status is not None
    assert status["status"] == TaskState.RUNNING
    assert status["display_message"] == output
    assert len(status["steps"]) == 1
    assert status["steps"][0]["name"] == step
    assert status["steps"][0]["output"] == output
    assert status["steps"][0]["is_current"] is True


def test_update_multiple_steps(status_updator):
    # Test updating multiple steps
    steps = [
        (PlaygroundChatbotStep.THINKING, "Thinking about the problem..."),
        (PlaygroundChatbotStep.WEB_SEARCH, "Searching the web..."),
    ]

    for step, output in steps:
        status_updator.update_chatbot_step(step, output)

    # Verify all steps were stored
    status = cache.get("test_task_id")
    assert len(status["steps"]) == 2
    assert status["steps"][0]["name"] == steps[0][0]
    assert status["steps"][1]["name"] == steps[1][0]
    assert status["steps"][1]["is_current"] is True  # Last step should be current


def test_update_chatbot_final_result(status_updator):
    # Test updating final result
    final_message = "Here's your final answer!"

    status_updator.update_chatbot_final_result(final_message)

    # Verify the final status
    status = cache.get("test_task_id")
    assert status["status"] == TaskState.COMPLETED
    assert status["message"] == final_message
    assert len(status["steps"]) == 1
    assert status["steps"][0]["name"] == PlaygroundChatbotStep.RESPONSE_GENERATION


def test_update_chatbot_final_result_with_brand_guideline(status_updator):
    # Test updating final result
    final_message = "Here's your final answer!"

    # Add a brand guideline step
    status_updator.update_chatbot_step(
        PlaygroundChatbotStep.REWRITING_CONTENT, "Rewriting content..."
    )
    status_updator.update_chatbot_step(
        PlaygroundChatbotStep.REWRITING_CONTENT,
        "Rewriting content Finished",
        step_finished=True,
    )
    status_updator.update_chatbot_final_result(final_message)

    # Verify the final status
    status = cache.get("test_task_id")
    assert status["status"] == TaskState.COMPLETED
    assert status["message"] == final_message
    assert len(status["steps"]) == 2
    assert status["steps"][1]["name"] == PlaygroundChatbotStep.FINAL_RESULT


def test_get_current_running_steps(status_updator):
    # Test getting current running steps
    # First add some steps
    status_updator.update_chatbot_step(PlaygroundChatbotStep.THINKING, "Thinking...")
    status_updator.update_chatbot_step(PlaygroundChatbotStep.WEB_SEARCH, "Searching...")

    current_steps = status_updator.get_current_running_steps()
    assert len(current_steps) == 2
    assert current_steps[1] == PlaygroundChatbotStep.WEB_SEARCH
    assert current_steps[0] == PlaygroundChatbotStep.THINKING


def test_step_output_accumulation(status_updator):
    # Test that step outputs accumulate
    step = PlaygroundChatbotStep.THINKING
    outputs = ["First thought", "Second thought", "Third thought"]

    for output in outputs:
        status_updator.update_chatbot_step(step, output)

    status = cache.get("test_task_id")
    assert len(status["steps"]) == 1
    expected_output = "\n\n".join(outputs)
    assert status["steps"][0]["output"] == expected_output


def test_update_substep_invalid_step(status_updator):
    # Test updating sub-step for a step that doesn't support sub-steps
    step = PlaygroundChatbotStep.THINKING
    sub_step = PlaygroundChatbotSubStep.PLAYBOOK_SEARCH
    output = "Trying to update sub-step"

    status_updator.update_substep(step, sub_step, output)

    # Verify no changes were made
    status = cache.get("test_task_id")
    assert status is None


def test_update_substep_invalid_substep(status_updator):
    # Test updating with an invalid sub-step
    step = PlaygroundChatbotStep.READING_PLAYBOOK
    sub_step = "invalid_substep"
    output = "Trying to update invalid sub-step"

    status_updator.update_substep(step, sub_step, output)

    # Verify no changes were made
    status = cache.get("test_task_id")
    assert status is None


def test_update_substep_create_new(status_updator):
    # Test creating a new sub-step
    step = PlaygroundChatbotStep.READING_PLAYBOOK
    sub_step = PlaygroundChatbotSubStep.PLAYBOOK_SEARCH
    output = "Searching for playbook..."

    status_updator.update_substep(step, sub_step, output)

    # Verify the sub-step was created
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"]) == 1
    assert status["steps"][0]["name"] == step
    assert len(status["steps"][0]["sub_steps"]) == 1
    assert status["steps"][0]["sub_steps"][0]["name"] == sub_step
    assert status["steps"][0]["sub_steps"][0]["is_running"] == True
    assert status["steps"][0]["sub_steps"][0]["output"] == output


def test_update_substep_update_existing(status_updator):
    # Test updating an existing sub-step
    step = PlaygroundChatbotStep.READING_PLAYBOOK
    sub_step = PlaygroundChatbotSubStep.PLAYBOOK_SEARCH
    initial_output = "Initial search..."
    updated_output = "Updated search..."

    # Create initial sub-step
    status_updator.update_substep(step, sub_step, initial_output)
    # Update the sub-step
    status_updator.update_substep(step, sub_step, updated_output)

    # Verify the sub-step was updated
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"][0]["sub_steps"]) == 1
    assert status["steps"][0]["sub_steps"][0]["output"] == updated_output


def test_update_substep_complete(status_updator):
    # Test completing a sub-step
    step = PlaygroundChatbotStep.READING_PLAYBOOK
    sub_step = PlaygroundChatbotSubStep.PLAYBOOK_SEARCH
    output = "Search completed"

    status_updator.update_substep(step, sub_step, output, is_finished=False)
    status_updator.update_substep(step, sub_step, output, is_finished=True)

    # Verify the sub-step was marked as completed
    status = cache.get("test_task_id")
    assert status is not None
    assert status["steps"][0]["sub_steps"][0]["is_running"] == False


def test_update_substep_all_completed(status_updator):
    # Test when all sub-steps are completed
    step = PlaygroundChatbotStep.READING_PLAYBOOK
    sub_steps = [
        (PlaygroundChatbotSubStep.PLAYBOOK_SEARCH, "Playbook search completed"),
        (PlaygroundChatbotSubStep.ASSET_READ, "Asset read completed"),
        (PlaygroundChatbotSubStep.TARGET_READ, "Target read completed"),
    ]

    # Start all sub-steps
    for sub_step, output in sub_steps:
        status_updator.update_substep(step, sub_step, output, is_finished=False)

    # Complete all sub-steps
    for sub_step, output in sub_steps:
        status_updator.update_substep(step, sub_step, output, is_finished=True)

    # Verify all sub-steps are completed and main step is marked as finished
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"][0]["sub_steps"]) == 3
    assert all(
        substep["is_running"] == False for substep in status["steps"][0]["sub_steps"]
    )
    assert not status["steps"][0][
        "is_current"
    ]  # Main step should be marked as finished


def test_update_substep_partial_completion(status_updator):
    # Test when only some sub-steps are completed
    step = PlaygroundChatbotStep.READING_PLAYBOOK
    sub_steps = [
        (PlaygroundChatbotSubStep.PLAYBOOK_SEARCH, "Playbook search completed", False),
        (PlaygroundChatbotSubStep.ASSET_READ, "Asset read in progress", False),
        (PlaygroundChatbotSubStep.TARGET_READ, "Target read not started", False),
    ]

    # Update sub-steps with different completion statuses
    for sub_step, output, is_finished in sub_steps:
        status_updator.update_substep(step, sub_step, output, is_finished)

    sub_steps_updated = [
        (PlaygroundChatbotSubStep.PLAYBOOK_SEARCH, "Playbook search completed", True),
        (PlaygroundChatbotSubStep.ASSET_READ, "Asset read in progress", False),
        (PlaygroundChatbotSubStep.TARGET_READ, "Target read not started", False),
    ]

    # Update sub-steps with different completion statuses
    for sub_step, output, is_finished in sub_steps_updated:
        status_updator.update_substep(step, sub_step, output, is_finished)

    # Verify the status of each sub-step
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"][0]["sub_steps"]) == 3
    assert status["steps"][0]["sub_steps"][0]["is_running"] == False
    assert status["steps"][0]["sub_steps"][1]["is_running"] == True
    assert status["steps"][0]["sub_steps"][2]["is_running"] == True
    assert status["steps"][0]["is_current"]  # Main step should still be current

    sub_steps_finished = [
        (PlaygroundChatbotSubStep.ASSET_READ, "Asset read in progress", True),
        (PlaygroundChatbotSubStep.TARGET_READ, "Target read not started", True),
    ]

    # Update sub-steps with different completion statuses
    for sub_step, output, is_finished in sub_steps_finished:
        status_updator.update_substep(step, sub_step, output, is_finished)
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"][0]["sub_steps"]) == 3
    assert status["steps"][0]["sub_steps"][0]["is_running"] == False
    assert status["steps"][0]["sub_steps"][1]["is_running"] == False
    assert status["steps"][0]["sub_steps"][2]["is_running"] == False
    assert (
        status["steps"][0]["is_current"] == False
    )  # Main step should still be current


def test_multiple_steps_running_at_the_same_time(status_updator):
    # Test when multiple steps are running at the same time
    thinking_step = PlaygroundChatbotStep.THINKING
    web_search_step = PlaygroundChatbotStep.WEB_SEARCH
    reading_playbook_step = PlaygroundChatbotStep.READING_PLAYBOOK

    # Thinking step
    status_updator.update_chatbot_step(thinking_step, "Thinking...")
    status_updator.update_chatbot_step(
        thinking_step, "Thinking...finished", step_finished=True
    )
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"]) == 1
    assert status["steps"][0]["name"] == thinking_step
    assert status["steps"][0]["is_current"] == False

    # Web search step
    status_updator.update_chatbot_step(web_search_step, "Searching...")
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"]) == 2
    assert status["steps"][0]["name"] == thinking_step
    assert status["steps"][1]["name"] == web_search_step
    assert status["steps"][0]["is_current"] == False
    assert status["steps"][1]["is_current"] == True

    # Reading playbook step
    sub_steps = [
        (PlaygroundChatbotSubStep.PLAYBOOK_SEARCH, "Playbook search completed", False),
        (PlaygroundChatbotSubStep.ASSET_READ, "Asset read in progress", False),
        (PlaygroundChatbotSubStep.TARGET_READ, "Target read not started", False),
    ]
    for sub_step, output, is_finished in sub_steps:
        status_updator.update_substep(
            reading_playbook_step, sub_step, output, is_finished
        )

    # now we should have 2 steps running at the same time
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"]) == 3
    assert status["steps"][0]["name"] == thinking_step
    assert status["steps"][1]["name"] == web_search_step
    assert status["steps"][2]["name"] == reading_playbook_step
    assert status["steps"][0]["is_current"] == False
    assert status["steps"][1]["is_current"] == True
    assert status["steps"][2]["is_current"] == True

    # now we complete 2 of the sub-steps
    status_updator.update_substep(
        reading_playbook_step,
        PlaygroundChatbotSubStep.PLAYBOOK_SEARCH,
        "Playbook search completed",
        True,
    )
    status_updator.update_substep(
        reading_playbook_step,
        PlaygroundChatbotSubStep.ASSET_READ,
        "Asset read completed",
        True,
    )
    status = cache.get("test_task_id")
    assert status is not None
    assert status["steps"][2]["sub_steps"][0]["is_running"] == False
    assert status["steps"][2]["sub_steps"][1]["is_running"] == False
    assert status["steps"][2]["sub_steps"][2]["is_running"] == True
    assert status["steps"][0]["is_current"] == False
    assert status["steps"][1]["is_current"] == True
    assert status["steps"][2]["is_current"] == True

    # now we complete the web search step
    status_updator.update_chatbot_step(
        web_search_step, "Searching...finished", step_finished=True
    )
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"]) == 3
    assert status["steps"][0]["name"] == thinking_step
    assert status["steps"][1]["name"] == web_search_step
    assert status["steps"][2]["name"] == reading_playbook_step
    assert status["steps"][0]["is_current"] == False
    assert status["steps"][1]["is_current"] == False
    assert status["steps"][2]["is_current"] == True

    # now we complete the reading playbook step
    status_updator.update_chatbot_step(
        reading_playbook_step, "Reading playbook...finished", step_finished=True
    )
    status = cache.get("test_task_id")
    assert status is not None
    assert len(status["steps"]) == 3
    assert status["steps"][0]["name"] == thinking_step
    assert status["steps"][1]["name"] == web_search_step
    assert status["steps"][2]["name"] == reading_playbook_step
    assert status["steps"][0]["is_current"] == False
    assert status["steps"][1]["is_current"] == False
    assert status["steps"][2]["is_current"] == False


def test_update_chatbot_failed(status_updator):
    # Test updating failed status
    error_message = "Something went wrong!"
    status_updator.update_chatbot_failed(error_message)

    # Verify the failed status
    status = cache.get("test_task_id")
    assert status is not None
    assert status["status"] == TaskState.FAILED
    assert status["display_message"] == error_message
    assert len(status["steps"]) == 1
    assert status["steps"][0]["name"] == PlaygroundChatbotStep.FINAL_RESULT
    assert status["steps"][0]["output"] == error_message
