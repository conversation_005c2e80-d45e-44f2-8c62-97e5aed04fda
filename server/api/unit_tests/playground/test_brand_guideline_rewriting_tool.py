import json
from unittest.mock import Mock, patch

import pytest
from api.content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from api.feature.data_wrapper.data_wrapper import GenerateEnv
from api.model_caller import ModelCaller
from api.playground.chatbot_status import ChatbotStepStatusUpdator
from api.playground.langgraph.models import ToolStatus
from api.playground.langgraph.tools.brand_guideline_rewriting_tool import (
    BrandGuidelineRewritingTool,
)
from api.playground.playground_feature_builder import PlaygroundFeatureBuilder


@pytest.fixture
def mock_model_caller():
    return Mock(spec=ModelCaller)


@pytest.fixture
def mock_gen_env():
    return Mock(spec=GenerateEnv)


def test_brand_guideline_rewriting_tool_success(mock_model_caller, mock_gen_env):
    # Set up the mock_gen_env to have a _data_wrapper attribute
    mock_gen_env._data_wrapper = Mock()

    # Create a proper mock of DefaultContentGenPostprocessor
    with patch(
        "api.playground.langgraph.tools.brand_guideline_rewriting_tool.DefaultContentGenPostprocessor"
    ) as mock_class:
        # Create a proper mock for PlaygroundFeatureBuilder that inherits from it
        with patch(
            "api.playground.langgraph.tools.brand_guideline_rewriting_tool.PlaygroundFeatureBuilder"
        ) as mock_feature_builder_class:
            # Create a class that inherits from PlaygroundFeatureBuilder
            class MockFeatureBuilder(PlaygroundFeatureBuilder):
                def __init__(self, *args, **kwargs):
                    # Skip the parent class initialization to avoid validation issues
                    pass

                def get_brand_guidelines(self):
                    return "Test Brand Guidelines"

            # Use our custom class instead of a plain Mock
            mock_feature_builder_class.return_value = MockFeatureBuilder()

            class MockContentProcessor(DefaultContentGenPostprocessor):
                def __init__(self, *args, **kwargs):
                    # Skip the parent class initialization to avoid validation issues
                    pass

                def rewrite_with_brand_guideline(self, *args, **kwargs):
                    return ["Rewritten content"]

            # Use our custom class instead of a plain Mock
            mock_class.return_value = MockContentProcessor()

            status_updator = Mock(spec=ChatbotStepStatusUpdator)
            # Create the tool with mocks
            tool = BrandGuidelineRewritingTool(
                model_caller=mock_model_caller,
                gen_env=mock_gen_env,
                playbook_id=1234,
                task_id="test_task_id",
                status_updator=status_updator,
            )

            # Test the process method directly
            result_process = tool._run("Original content")
            result_dict_process = json.loads(result_process)

            # Verify the result
            assert result_dict_process["tool_status"] == ToolStatus.SUCCESS
            assert result_dict_process["output_text"] == "Rewritten content"
            assert result_dict_process["metadata"]["playbook_id"] == 1234
            assert (
                result_dict_process["metadata"]["brand_guidelines"]
                == "Test Brand Guidelines"
            )
            assert (
                result_dict_process["metadata"]["original_response"]
                == "Original content"
            )


def test_brand_guideline_rewriting_tool_no_guidelines(mock_model_caller, mock_gen_env):
    # Set up the mock_gen_env to have a _data_wrapper attribute
    mock_gen_env._data_wrapper = Mock()

    # Create a proper mock of DefaultContentGenPostprocessor
    with patch(
        "api.playground.langgraph.tools.brand_guideline_rewriting_tool.DefaultContentGenPostprocessor"
    ) as mock_class:
        # Create a proper mock for PlaygroundFeatureBuilder that inherits from it
        with patch(
            "api.playground.langgraph.tools.brand_guideline_rewriting_tool.PlaygroundFeatureBuilder"
        ) as mock_feature_builder_class:
            # Create a class that inherits from PlaygroundFeatureBuilder
            class MockFeatureBuilder(PlaygroundFeatureBuilder):
                def __init__(self, *args, **kwargs):
                    # Skip the parent class initialization to avoid validation issues
                    pass

                def get_brand_guidelines(self):
                    return None  # Return None to simulate no brand guidelines

            # Use our custom class instead of a plain Mock
            mock_feature_builder_class.return_value = MockFeatureBuilder()

            class MockContentProcessor(DefaultContentGenPostprocessor):
                def __init__(self, *args, **kwargs):
                    # Skip the parent class initialization to avoid validation issues
                    pass

                def rewrite_with_brand_guideline(self, *args, **kwargs):
                    return ["Rewritten content"]

            # Use our custom class instead of a plain Mock
            mock_class.return_value = MockContentProcessor()

            status_updator = Mock(spec=ChatbotStepStatusUpdator)
            # Create the tool with mocks
            tool = BrandGuidelineRewritingTool(
                model_caller=mock_model_caller,
                gen_env=mock_gen_env,
                playbook_id=1234,
                task_id="test_task_id",
                status_updator=status_updator,
            )

            # Test the process method directly
            result_process = tool._run("Original content")
            result_dict_process = json.loads(result_process)

            # Verify the result
            assert result_dict_process["tool_status"] == ToolStatus.ERROR
            assert result_dict_process["output_text"] == ""
            assert result_dict_process["metadata"] == {}
            assert (
                result_dict_process["tool_input"]["original_response"]
                == "Original content"
            )
