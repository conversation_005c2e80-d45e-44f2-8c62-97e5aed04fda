import json
from unittest.mock import Mock, patch

import pytest
from api.content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from api.feature.data_wrapper.data_wrapper import GenerateEnv
from api.model_caller import ModelCaller
from api.playground.chatbot_status import Chatbot<PERSON>tepStatusUpdator
from api.playground.langgraph.models import ToolStatus
from api.playground.langgraph.tools.base_custom_tool import Base<PERSON>ustomTool
from api.playground.langgraph.tools.brand_guideline_rewriting_tool import (
    BrandGuidelineRewritingTool,
)
from api.playground.langgraph.tools.get_assets_message_tool import GetAssetsMessageTool
from api.playground.langgraph.tools.get_targets_message_tool import (
    GetTargetsMessageTool,
)
from api.playground.langgraph.tools.playbook_search_tool import PlaybookSearchTool
from api.playground.langgraph.tools.tool_registry import (
    create_final_response_tools,
    create_tools,
)
from api.playground.langgraph.tools.url_read_tool import Url<PERSON>eadTool
from api.playground.langgraph.tools.web_search_tool import WebSearchTool
from api.playground.playground_chat_generator import GenerateChatRequest


@pytest.fixture
def mock_model_caller():
    return Mock(spec=ModelCaller)


@pytest.fixture
def mock_gen_env():
    return Mock(spec=GenerateEnv)


@pytest.fixture
def mock_chat_request():
    return Mock(spec=GenerateChatRequest)


def test_create_tools(mock_model_caller, mock_gen_env):
    chat_request = GenerateChatRequest(
        task_id="test_task",
        model="test_model",
        previous_messages=[],
        new_message="test message",
        use_company_info=True,
        use_brand_guidelines=True,
        targets={"test": ["target1"]},
        assets={"test": ["asset1"]},
    )

    status_updator = Mock(spec=ChatbotStepStatusUpdator)

    tools = create_tools(
        mock_gen_env, 10, 10, 1234, "5678", chat_request, status_updator
    )

    assert "web_search" in tools
    assert "playbook_search" in tools
    assert "url_read" in tools
    assert "get_assets_message" in tools
    assert "get_targets_message" in tools
    assert isinstance(tools["web_search"], WebSearchTool)
    assert isinstance(tools["playbook_search"], PlaybookSearchTool)
    assert isinstance(tools["url_read"], UrlReadTool)
    assert isinstance(tools["get_assets_message"], GetAssetsMessageTool)
    assert isinstance(tools["get_targets_message"], GetTargetsMessageTool)
    assert tools["get_assets_message"].gen_env == mock_gen_env
    assert tools["get_assets_message"].asset_budget == 10
    assert tools["get_targets_message"].gen_env == mock_gen_env
    assert tools["get_targets_message"].target_budget == 10


def test_create_tools_with_exclude_tools(mock_model_caller, mock_gen_env):
    chat_request = GenerateChatRequest(
        task_id="test_task",
        model="test_model",
        previous_messages=[],
        new_message="test message",
        use_company_info=True,
        use_brand_guidelines=True,
    )
    status_updator = Mock(spec=ChatbotStepStatusUpdator)
    tools = create_tools(
        mock_gen_env, 10, 10, 1234, "5678", chat_request, status_updator, ["web_search"]
    )

    assert "web_search" not in tools
    assert "playbook_search" in tools


def test_create_tools_with_playbook_and_task_id(mock_model_caller, mock_gen_env):
    chat_request = GenerateChatRequest(
        task_id="test_task",
        model="test_model",
        previous_messages=[],
        new_message="test message",
        use_company_info=True,
        use_brand_guidelines=True,
    )
    status_updator = Mock(spec=ChatbotStepStatusUpdator)
    tools = create_tools(
        mock_gen_env,
        10,
        10,
        playbook_id=1234,
        task_id="test_task_id",
        chat_request=chat_request,
        status_updator=status_updator,
    )

    # Check that BaseCustomTool instances have the playbook_id and task_id set
    for tool in tools.values():
        if isinstance(tool, BaseCustomTool):
            assert tool.playbook_id == 1234
            assert tool.task_id == "test_task_id"


def test_create_final_response_tools(mock_model_caller, mock_gen_env):
    # Set up the mock_gen_env to have a _data_wrapper attribute with playbook_instance
    mock_data_wrapper = Mock()
    mock_playbook_instance = Mock()
    mock_playbook_instance.id = 1234
    mock_data_wrapper.playbook_instance = mock_playbook_instance
    mock_gen_env._data_wrapper = mock_data_wrapper

    # Create a proper mock of DefaultContentGenPostprocessor
    with (
        patch(
            "api.playground.langgraph.tools.brand_guideline_rewriting_tool.DefaultContentGenPostprocessor"
        ) as mock_class,
        patch(
            "api.playground.langgraph.tools.tool_registry.PlaygroundFeatureBuilder"
        ) as mock_feature_builder,
    ):
        # Mock the feature builder
        mock_feature_builder_instance = Mock()
        mock_feature_builder_instance.get_brand_guidelines.return_value = (
            "Test brand guidelines"
        )
        mock_feature_builder.return_value = mock_feature_builder_instance

        # Create a class that inherits from DefaultContentGenPostprocessor
        class MockContentProcessor(DefaultContentGenPostprocessor):
            def __init__(self, *args, **kwargs):
                # Skip the parent class initialization to avoid validation issues
                pass

            def rewrite_with_brand_guideline(self, *args, **kwargs):
                return ["Rewritten content"]

        # Use our custom class instead of a plain Mock
        mock_class.return_value = MockContentProcessor()

        with patch(
            "api.playground.langgraph.nodes.base_node.BaseNode.get_model_caller_from_model_name",
            return_value=mock_model_caller,
        ):
            chat_request = GenerateChatRequest(
                task_id="test_task",
                model="test_model",
                previous_messages=[],
                new_message="test message",
                use_company_info=True,
                use_brand_guidelines=True,
            )
            status_updator = Mock(spec=ChatbotStepStatusUpdator)
            tools = create_final_response_tools(
                "test_model",
                mock_gen_env,
                chat_request,
                playbook_id=1234,
                task_id="test_task_id",
                status_updator=status_updator,
            )

        assert "brand_guideline_rewriting" in tools
        assert isinstance(
            tools["brand_guideline_rewriting"], BrandGuidelineRewritingTool
        )
        assert tools["brand_guideline_rewriting"].playbook_id == 1234
        assert tools["brand_guideline_rewriting"].task_id == "test_task_id"


def test_process_method():
    # Create a test implementation of BaseCustomTool
    class TestTool(BaseCustomTool):
        name: str = "test_tool"
        description: str = "A test tool"

        def _run(self, arg1, arg2):
            return f"{arg1}-{arg2}"

    # Create an instance of the test tool
    status_updator = Mock(spec=ChatbotStepStatusUpdator)
    tool = TestTool(playbook_id=1234, task_id="3456", status_updator=status_updator)

    result = tool._run("value1", "value2")
    assert result == "value1-value2"


def test_playbook_search_tool():
    # Create a mock for PineconeTofuSearcher
    with patch(
        "api.playground.langgraph.tools.playbook_search_tool.PineconeTofuSearcher"
    ) as mock_searcher_class:
        # Configure the mock
        mock_searcher_instance = Mock()
        mock_searcher_instance.search.return_value = [
            {
                "content": "Test content",
                "metadata": {
                    "column_id": "test_column",
                    "title": "Test Title",
                    "source": "Test Source",
                    "description": "Test Description",
                },
            }
        ]
        mock_searcher_class.return_value = mock_searcher_instance

        # Create the tool
        status_updator = Mock(spec=ChatbotStepStatusUpdator)
        tool = PlaybookSearchTool(
            playbook_id=1234, task_id="test_task_id", status_updator=status_updator
        )

        # Test the process method directly
        result_process = tool._run("test query", [])
        result_dict_process = json.loads(result_process)

        # Verify the result
        assert result_dict_process["tool_status"] == ToolStatus.SUCCESS
        assert "Playbook Search Results" in result_dict_process["output_text"]
        assert result_dict_process["metadata"]["playbook_id"] == 1234
        assert result_dict_process["metadata"]["results_count"] == 1

        # Test the _run method which should call process
        result_run = tool._run("test query", [])
        assert result_run == result_process

        # Verify the mock was called correctly (twice, once for each test)
        assert mock_searcher_class.call_count == 2
        mock_searcher_class.assert_called_with(playbook_id="1234")
        assert mock_searcher_instance.search.call_count == 2
        mock_searcher_instance.search.assert_called_with(
            query="test query", top_k=3, object_types=[]
        )

        # Additional test: object_types is not empty
        mock_searcher_instance.search.reset_mock()
        result_with_object_types = tool._run("test query with object_types", ["assets"])
        result_dict_with_object_types = json.loads(result_with_object_types)
        assert result_dict_with_object_types["tool_status"] == ToolStatus.SUCCESS
        mock_searcher_instance.search.assert_called_with(
            query="test query with object_types", top_k=3, object_types=["assets"]
        )


def test_create_tools_without_assets(mock_model_caller, mock_gen_env):
    chat_request = GenerateChatRequest(
        task_id="test_task",
        model="test_model",
        previous_messages=[],
        new_message="test message",
        use_company_info=True,
        use_brand_guidelines=True,
        targets={"test": ["target1"]},  # Only targets, no assets
    )
    status_updator = Mock(spec=ChatbotStepStatusUpdator)
    tools = create_tools(
        mock_gen_env, 10, 10, 1234, "5678", chat_request, status_updator
    )

    assert "web_search" in tools
    assert "playbook_search" in tools
    assert "url_read" in tools
    assert "get_targets_message" in tools
    assert "get_assets_message" not in tools  # Should not be present when no assets
    assert isinstance(tools["web_search"], WebSearchTool)
    assert isinstance(tools["playbook_search"], PlaybookSearchTool)
    assert isinstance(tools["url_read"], UrlReadTool)
    assert isinstance(tools["get_targets_message"], GetTargetsMessageTool)
    assert tools["get_targets_message"].gen_env == mock_gen_env
    assert tools["get_targets_message"].target_budget == 10


def test_create_final_response_tools_without_brand_guidelines(
    mock_model_caller, mock_gen_env
):
    # Set up the mock_gen_env to have a _data_wrapper attribute with playbook_instance
    mock_data_wrapper = Mock()
    mock_playbook_instance = Mock()
    mock_playbook_instance.id = 1234
    mock_data_wrapper.playbook_instance = mock_playbook_instance
    mock_gen_env._data_wrapper = mock_data_wrapper

    with patch(
        "api.playground.langgraph.tools.tool_registry.PlaygroundFeatureBuilder"
    ) as mock_feature_builder:
        # Mock the feature builder
        mock_feature_builder_instance = Mock()
        mock_feature_builder_instance.get_brand_guidelines.return_value = (
            "Test brand guidelines"
        )
        mock_feature_builder.return_value = mock_feature_builder_instance

        with patch(
            "api.playground.langgraph.nodes.base_node.BaseNode.get_model_caller_from_model_name",
            return_value=mock_model_caller,
        ):
            chat_request = GenerateChatRequest(
                task_id="test_task",
                model="test_model",
                previous_messages=[],
                new_message="test message",
                use_company_info=True,
                use_brand_guidelines=False,  # Brand guidelines disabled
            )
            status_updator = Mock(spec=ChatbotStepStatusUpdator)
            tools = create_final_response_tools(
                "test_model",
                mock_gen_env,
                chat_request,
                playbook_id=1234,
                task_id="test_task_id",
                status_updator=status_updator,
            )

        assert (
            "brand_guideline_rewriting" not in tools
        )  # Should not be present when use_brand_guidelines is False


def test_create_final_response_tools_without_brand_guidelines_in_playbook(
    mock_model_caller, mock_gen_env
):
    # Set up the mock_gen_env to have a _data_wrapper attribute with playbook_instance
    mock_data_wrapper = Mock()
    mock_playbook_instance = Mock()
    mock_playbook_instance.id = 1234
    mock_data_wrapper.playbook_instance = mock_playbook_instance
    mock_gen_env._data_wrapper = mock_data_wrapper

    with patch(
        "api.playground.langgraph.tools.tool_registry.PlaygroundFeatureBuilder"
    ) as mock_feature_builder:
        # Mock the feature builder to return None for brand guidelines
        mock_feature_builder_instance = Mock()
        mock_feature_builder_instance.get_brand_guidelines.return_value = None
        mock_feature_builder.return_value = mock_feature_builder_instance

        with patch(
            "api.playground.langgraph.nodes.base_node.BaseNode.get_model_caller_from_model_name",
            return_value=mock_model_caller,
        ):
            chat_request = GenerateChatRequest(
                task_id="test_task",
                model="test_model",
                previous_messages=[],
                new_message="test message",
                use_company_info=True,
                use_brand_guidelines=True,  # Brand guidelines enabled but not available in playbook
            )
            status_updator = Mock(spec=ChatbotStepStatusUpdator)
            tools = create_final_response_tools(
                "test_model",
                mock_gen_env,
                chat_request,
                playbook_id=1234,
                task_id="test_task_id",
                status_updator=status_updator,
            )

        assert (
            "brand_guideline_rewriting" not in tools
        )  # Should not be present when no brand guidelines in playbook
