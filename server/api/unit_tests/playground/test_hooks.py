from unittest.mock import Mock, patch

import pytest
from api.playground.langgraph.hooks import pre_model_hook_with_model_budget
from api.playground.langgraph.nodes.response_generation_node import (
    ResponseGenerationNode,
)
from api.utils import get_token_count
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage


@pytest.fixture
def base_state():
    return {
        "task_id": "test_task",
        "playbook_id": "test_playbook",
        "model_token_budget_by_node": {"response_generation": 1000},
        "system_message": "System message",
        "tool_results": "Test tool results",
        "reflection_output": "Test reflection",
        "messages": [
            HumanMessage(content="Test message 1"),
            AIMessage(content="Test response 1"),
            HumanMessage(content="Test message 2"),
        ],
    }


def test_pre_model_hook_basic(base_state):
    """Test basic pre-model hook with all components within budget."""
    result = pre_model_hook_with_model_budget(base_state, 1000)

    # Should return empty dict when no changes needed
    assert result == {}
    assert base_state["tool_results"] == "Test tool results"
    assert base_state["reflection_output"] == "Test reflection"


def test_pre_model_hook_token_reduction(base_state):
    """Test token reduction strategy when content exceeds budget."""

    # Add long content to force token reduction
    base_state["tool_results"] = "Very long tool results " * 100
    base_state["reflection_output"] = "Very long reflection " * 100
    base_state["messages"].extend(
        [
            HumanMessage(content="Very long message " * 50),
            AIMessage(content="Very long response " * 50),
        ]
    )

    result = pre_model_hook_with_model_budget(base_state, 800)

    # Verify token reduction strategies were applied
    assert "tool_results" in result  # Tool results should be modified
    assert "llm_input_messages" in result  # Messages should be trimmed


def test_pre_model_hook_minimal_content(base_state):
    """Test with minimal required content."""
    base_state["tool_results"] = ""
    base_state["reflection_output"] = ""
    base_state["previous_messages"] = ""
    base_state["messages"] = [SystemMessage(content="System message")]

    result = pre_model_hook_with_model_budget(base_state, 1000)

    # Should return empty dict when no changes needed
    assert result == {}


def test_pre_model_hook_token_exceeded(base_state):
    """Test when content still exceeds budget after all reductions."""
    # Set a very small budget
    base_state["model_token_budget_by_node"]["response_generation"] = 10

    # Add very long content
    base_state["system_message"] = "Very long system message " * 1000
    base_state["messages"] = [SystemMessage(content="Very long system message " * 1000)]

    with pytest.raises(
        ValueError,
        match="The chat message is too long and has reached the limit. Please start a new thread.",
    ):
        pre_model_hook_with_model_budget(base_state, 10)


def test_pre_model_hook_system_message_only(base_state):
    """Test when only system message is present."""
    base_state["tool_results"] = ""
    base_state["reflection_output"] = ""
    base_state["previous_messages"] = ""
    base_state["messages"] = [SystemMessage(content="System message")]

    result = pre_model_hook_with_model_budget(base_state, 1000)

    # Should return empty dict when no changes needed
    assert result == {}
    assert base_state["system_message"] == "System message"
    assert base_state["tool_results"] == ""
    assert base_state["reflection_output"] == ""
    assert base_state["previous_messages"] == ""
