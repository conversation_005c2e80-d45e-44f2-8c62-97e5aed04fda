from unittest.mock import Mock, patch
from uuid import UUID

from api.playground.chatbot_status import (
    ChatbotStepStatusUpdator,
    PlaygroundChatbotStep,
)
from api.playground.langgraph.callbacks import PlaygroundCallbackHandler


class TestPlaygroundCallbackHandler:
    def setup_method(self):
        self.status_updator = Mock(spec=ChatbotStepStatusUpdator)
        self.handler = PlaygroundCallbackHandler(self.status_updator)

    def test_on_chain_start_with_tools(self):
        # Create a mock AI message with tool calls
        ai_message = Mock()
        ai_message.type = "ai"
        ai_message.tool_calls = [{"name": "tool1"}, {"name": "tool2"}]

        # Create mock messages list
        messages = [Mock(), ai_message]

        # Call the handler
        self.handler.on_chain_start(
            serialized={},
            inputs={"messages": messages},
            name="tools",
            run_id=UUID("12345678-1234-5678-1234-************"),
        )

        # Verify the status updator was called with correct parameters
        self.status_updator.update_chatbot_step.assert_called_once_with(
            PlaygroundChatbotStep.THINKING,
            f"To better answer your question, I identified the tools {', '.join(set([tool['name'] for tool in ai_message.tool_calls]))} is helpful for me to use.",
            step_finished=True,
        )

    def test_on_chain_start_with_no_messages(self):
        # Call the handler with no messages
        self.handler.on_chain_start(
            serialized={},
            inputs={},
            name="tools",
            run_id=UUID("12345678-1234-5678-1234-************"),
        )

        # Verify the status updator was not called
        self.status_updator.update_chatbot_step.assert_not_called()

    def test_on_chain_start_with_invalid_messages(self):
        # Call the handler with invalid messages format
        self.handler.on_chain_start(
            serialized={},
            inputs={"messages": "not a list"},
            name="tools",
            run_id=UUID("12345678-1234-5678-1234-************"),
        )

        # Verify the status updator was not called
        self.status_updator.update_chatbot_step.assert_not_called()

    def test_on_chain_start_with_no_ai_message(self):
        # Create messages list without AI message
        messages = [Mock(type="human"), Mock(type="human")]

        # Call the handler
        self.handler.on_chain_start(
            serialized={},
            inputs={"messages": messages},
            name="tools",
            run_id=UUID("12345678-1234-5678-1234-************"),
        )

        # Verify the status updator was not called
        self.status_updator.update_chatbot_step.assert_not_called()

    def test_on_chain_start_with_no_tool_calls(self):
        # Create AI message without tool calls
        ai_message = Mock()
        ai_message.type = "ai"
        ai_message.tool_calls = []

        # Create messages list
        messages = [Mock(), ai_message]

        # Call the handler
        self.handler.on_chain_start(
            serialized={},
            inputs={"messages": messages},
            name="tools",
            run_id=UUID("12345678-1234-5678-1234-************"),
        )

        # Verify the status updator was not called
        self.status_updator.update_chatbot_step.assert_not_called()

    def test_on_chain_start_with_invalid_tool_call(self):
        # Create AI message with invalid tool call
        ai_message = Mock()
        ai_message.type = "ai"
        ai_message.tool_calls = ["not a dict"]

        # Create messages list
        messages = [Mock(), ai_message]

        # Call the handler
        self.handler.on_chain_start(
            serialized={},
            inputs={"messages": messages},
            name="tools",
            run_id=UUID("12345678-1234-5678-1234-************"),
        )

        # Verify the status updator was not called
        self.status_updator.update_chatbot_step.assert_not_called()

    def test_on_chain_start_with_missing_tool_name(self):
        # Create AI message with tool call missing name
        ai_message = Mock()
        ai_message.type = "ai"
        ai_message.tool_calls = [{"not_name": "value"}]

        # Create messages list
        messages = [Mock(), ai_message]

        # Call the handler
        self.handler.on_chain_start(
            serialized={},
            inputs={"messages": messages},
            name="tools",
            run_id=UUID("12345678-1234-5678-1234-************"),
        )

        # Verify the status updator was not called
        self.status_updator.update_chatbot_step.assert_not_called()

    def test_on_chain_end_with_successful_tool(self):
        # Set up the handler with a run_id
        run_id = UUID("12345678-1234-5678-1234-************")
        self.handler.run_id = run_id

        # Create a mock tool message with success status
        tool_message = Mock()
        tool_message.content = '{"tool_name": "search", "tool_status": "success"}'

        # Call the handler
        self.handler.on_chain_end(
            outputs={"messages": [Mock(), tool_message]}, run_id=run_id
        )

        # Verify the status updator was called with success message
        self.status_updator.update_chatbot_step.assert_called_once_with(
            PlaygroundChatbotStep.THINKING,
            "I've successfully used the search tool to gather information. Let me process this and check if we need to use other tools.\n\n",
        )

    def test_on_chain_end_with_failed_tool(self):
        # Set up the handler with a run_id
        run_id = UUID("12345678-1234-5678-1234-************")
        self.handler.run_id = run_id

        # Create a mock tool message with failure status
        tool_message = Mock()
        tool_message.content = '{"tool_name": "search", "tool_status": "failed"}'

        # Call the handler
        self.handler.on_chain_end(
            outputs={"messages": [Mock(), tool_message]}, run_id=run_id
        )

        # Verify the status updator was called with failure message
        self.status_updator.update_chatbot_step.assert_called_once_with(
            PlaygroundChatbotStep.THINKING,
            "I encountered an issue while using the search tool. Let me try a different approach.",
        )

    def test_on_chain_end_with_invalid_json(self):
        # Set up the handler with a run_id
        run_id = UUID("12345678-1234-5678-1234-************")
        self.handler.run_id = run_id

        # Create a mock tool message with invalid JSON
        tool_message = Mock()
        tool_message.content = "invalid json"

        # Call the handler
        self.handler.on_chain_end(
            outputs={"messages": [Mock(), tool_message]}, run_id=run_id
        )

        # Verify the status updator was called with error message
        self.status_updator.update_chatbot_step.assert_called_once_with(
            PlaygroundChatbotStep.THINKING,
            "I encountered an issue processing the tool response. Let me try a different approach.",
        )

    def test_on_chain_end_with_different_run_id(self):
        # Set up the handler with a run_id
        self.handler.run_id = UUID("12345678-1234-5678-1234-************")

        # Call the handler with a different run_id
        self.handler.on_chain_end(
            outputs={"messages": [Mock()]},
            run_id=UUID("*************-8765-4321-************"),
        )

        # Verify the status updator was not called
        self.status_updator.update_chatbot_step.assert_not_called()

    def test_combined_chain_start_and_end(self):
        # First, simulate chain start
        run_id = UUID("12345678-1234-5678-1234-************")
        ai_message = Mock()
        ai_message.type = "ai"
        ai_message.tool_calls = [{"name": "search"}]

        self.handler.on_chain_start(
            serialized={},
            inputs={"messages": [Mock(), ai_message]},
            name="tools",
            run_id=run_id,
        )

        # Verify the initial status update
        self.status_updator.update_chatbot_step.assert_called_with(
            PlaygroundChatbotStep.THINKING,
            "To better answer your question, I identified the tools search is helpful for me to use.",
            step_finished=True,
        )

        # Reset the mock for the next call
        self.status_updator.reset_mock()

        # Then, simulate chain end with success
        tool_message = Mock()
        tool_message.content = '{"tool_name": "search", "tool_status": "success"}'

        self.handler.on_chain_end(
            outputs={"messages": [Mock(), tool_message]}, run_id=run_id
        )

        # Verify the final status update
        self.status_updator.update_chatbot_step.assert_called_with(
            PlaygroundChatbotStep.THINKING,
            "I've successfully used the search tool to gather information. Let me process this and check if we need to use other tools.\n\n",
        )
