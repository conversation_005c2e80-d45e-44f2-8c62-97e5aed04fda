from datetime import datetime, timedelta
from unittest.mock import ANY, MagicMock, patch

import pytest
from django.utils import timezone

from ..models import (
    Campaign,
    CompanyInfo,
    FulltextLLMCache,
    Playbook,
    PlaybookUser,
    TofuUser,
)
from ..scheduled_tasks import (
    autopilot_sync_single_playbook,
    clean_e2e_test_accounts,
    clean_llm_cache,
    export_auto_sync,
    health_check,
)


@pytest.fixture
def mock_tofu_user_checker():
    with patch("api.scheduled_tasks.TofuUserCheckManager") as mock:
        yield mock.return_value


@pytest.fixture
def mock_playbook_checker():
    with patch("api.scheduled_tasks.PlaybookCheckManager") as mock:
        yield mock.return_value


@pytest.mark.django_db
class TestScheduledTasks:
    def test_health_check(self, mock_tofu_user_checker, mock_playbook_checker):
        health_check()

        mock_tofu_user_checker.run_health_check.assert_called_once()
        mock_playbook_checker.run_health_check.assert_called_once()

    @patch("api.scheduled_tasks.current_task")
    def test_export_auto_sync(self, mock_current_task):
        # Setup
        company_info = CompanyInfo.objects.create()
        playbook = Playbook.objects.create(company_object=company_info)
        Campaign.objects.create(
            playbook=playbook, campaign_params={"enable_auto_sync": True}
        )
        mock_current_task.request.id = "test_task_id"

        with patch("api.scheduled_tasks.autopilot_sync_single_playbook") as mock_sync:
            export_auto_sync()
            mock_sync.apply_async.assert_called_once()

    def test_autopilot_sync_single_playbook(self):
        company_info = CompanyInfo.objects.create()
        playbook = Playbook.objects.create(company_object=company_info)

        with patch("api.scheduled_tasks.PlaybookSyncerManager") as mock_syncer:
            autopilot_sync_single_playbook(playbook.id)
            mock_syncer.assert_called_once_with(playbook)
            mock_syncer.return_value.sync.assert_called_once()

    def test_clean_llm_cache(self):
        # Create old and new cache entries
        old_date = timezone.now() - timedelta(days=31)
        new_date = timezone.now() - timedelta(days=29)

        # Create with default created_at first
        old_cache = FulltextLLMCache.objects.create(
            prompt={"text": "old prompt"}, llm="test"
        )
        new_cache = FulltextLLMCache.objects.create(
            prompt={"text": "new prompt"}, llm="test"
        )

        # Then update the created_at fields
        FulltextLLMCache.objects.filter(id=old_cache.id).update(created_at=old_date)
        FulltextLLMCache.objects.filter(id=new_cache.id).update(created_at=new_date)

        # Refresh from database to get updated values
        old_cache.refresh_from_db()
        new_cache.refresh_from_db()

        clean_llm_cache()

        # Verify old cache is deleted and new cache remains
        assert FulltextLLMCache.objects.count() == 1
        assert FulltextLLMCache.objects.first() == new_cache

    @patch("api.scheduled_tasks.send_slack_message")
    def test_clean_e2e_test_accounts(self, mock_slack):
        # Create test user and playbook with an older timestamp
        old_time = timezone.now() - timedelta(days=1)  # Make it 1 day old

        test_user = TofuUser.objects.create(
            username="tofuadmin-e2etest-use-only-123", email="<EMAIL>"
        )
        # Update the date_joined field explicitly
        TofuUser.objects.filter(id=test_user.id).update(date_joined=old_time)

        company_info = CompanyInfo.objects.create()
        playbook = Playbook.objects.create(company_object=company_info)
        PlaybookUser.objects.create(playbook=playbook, user=test_user, type="creator")

        # Refresh from database to get updated timestamp
        test_user.refresh_from_db()

        clean_e2e_test_accounts()

        # Verify cleanup
        assert TofuUser.objects.filter(username=test_user.username).count() == 0
        assert Playbook.objects.filter(id=playbook.id).count() == 0
        mock_slack.assert_called_once_with("#bot_tests", ANY)

    @patch("api.scheduled_tasks.send_slack_message")
    def test_clean_e2e_test_accounts_threshold_exceeded(self, mock_slack):
        # Create more than threshold users
        old_time = timezone.now() - timedelta(days=1)  # Make them 1 day old
        users = []
        for i in range(51):
            user = TofuUser.objects.create(
                username=f"tofuadmin-e2e-test-use-only-{i}", email="<EMAIL>"
            )
            # Update the date_joined field explicitly
            TofuUser.objects.filter(id=user.id).update(date_joined=old_time)

            company_info = CompanyInfo.objects.create()
            playbook = Playbook.objects.create(company_object=company_info)
            PlaybookUser.objects.create(playbook=playbook, user=user, type="creator")
            users.append(user)

        clean_e2e_test_accounts()

        # Verify nothing was deleted
        assert TofuUser.objects.filter(email="<EMAIL>").count() == 51
        mock_slack.assert_called_once_with("#bot_tests", ANY)
