from unittest.mock import MagicMock, patch

import pytest

from ..feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    CampaignWrapper,
    ComponentWrapper,
    ContentGenSettings,
    ContentGroupWrapper,
    ContentWrapper,
    DataWrapperHolder,
    GenerateEnv,
    PlaybookWrapper,
)
from ..models import (
    Campaign,
    CompanyInfo,
    Content,
    ContentGroup,
    ContentVariation,
    Playbook,
)
from ..shared_definitions.protobuf.gen.action_define_pb2 import TofuData, TofuDataList
from ..shared_types import ContentType


@pytest.fixture
def mock_playbook():
    mock_company_info = MagicMock(spec=CompanyInfo)
    mock_company_info.docs = {
        "some-uuid": {
            "id": "some-uuid",
            "meta": {"field_name": "Company Name"},
            "type": "text",
            "value": "TestCo",
            "position": 1,
            "immutableField": True,
        }
    }

    return MagicMock(spec=Playbook, company_object=mock_company_info)


@pytest.fixture
def mock_campaign(mock_playbook):
    return MagicMock(
        spec=Campaign,
        playbook=mock_playbook,
        campaign_params={"campaign_goal": "Personalization", "num_of_variations": 3},
    )


@pytest.fixture
def mock_content_group(mock_campaign):
    return MagicMock(
        spec=ContentGroup,
        campaign=mock_campaign,
        content_group_params={"content_type": "BlogPost"},
    )


@pytest.fixture
def mock_content(mock_content_group):
    return MagicMock(spec=Content, content_group=mock_content_group)


def test_base_content_wrapper_from_data_instance(
    mock_playbook, mock_campaign, mock_content_group, mock_content
):
    assert isinstance(
        BaseContentWrapper.from_data_instance(mock_playbook), PlaybookWrapper
    )
    assert isinstance(
        BaseContentWrapper.from_data_instance(mock_campaign), CampaignWrapper
    )
    assert isinstance(
        BaseContentWrapper.from_data_instance(mock_content_group), ContentGroupWrapper
    )
    assert isinstance(
        BaseContentWrapper.from_data_instance(mock_content), ContentWrapper
    )

    with pytest.raises(ValueError):
        BaseContentWrapper.from_data_instance("invalid_instance")


def test_playbook_wrapper(mock_playbook):
    wrapper = PlaybookWrapper(mock_playbook)
    assert wrapper.company_name == "TestCo"
    assert wrapper.aggregated_asset_params == []
    assert wrapper.aggregated_custom_instructions == []


def test_campaign_wrapper(mock_campaign):
    wrapper = CampaignWrapper(mock_campaign)
    assert wrapper.campaign_goal == "Personalization"
    assert wrapper.num_of_variations == 3


def test_content_group_wrapper(mock_content_group):
    wrapper = ContentGroupWrapper(mock_content_group)
    assert wrapper.content_type == "BlogPost"
    assert wrapper.has_components


def test_content_wrapper(mock_content):
    wrapper = ContentWrapper(mock_content)
    assert wrapper.content_instance == mock_content


def test_component_wrapper(mock_content):
    mock_content.content_group.components = {
        "test_component": {"meta": {"type": "text"}}
    }
    wrapper = ComponentWrapper(mock_content, "test_component")
    assert not wrapper.has_components


def test_content_wrapper_get_component_current_value(mock_content):
    wrapper = ContentWrapper(mock_content)

    # Mock ContentVariation.objects.get
    mock_content_variation = MagicMock(spec=ContentVariation)
    mock_content_variation.variations = {
        "component1": {
            "meta": {
                "current_version": {"text": "Current value"},
                "variations": [
                    {"text": "Variation 1"},
                    {"text": "Variation 2"},
                ],
            }
        },
        "component2": {"meta": {"current_version": {"text": ""}, "variations": []}},
    }

    with patch(
        "api.models.ContentVariation.objects.get", return_value=mock_content_variation
    ):
        # Test getting current version (idx=0)
        assert wrapper.get_component_current_value("component1") == "Current value"

        # Test getting specific variation (idx=1)
        assert wrapper.get_component_current_value("component1", idx=1) == "Variation 1"

        # Test getting specific variation (idx=2)
        assert wrapper.get_component_current_value("component1", idx=2) == "Variation 2"

    # Test when ContentVariation is None
    with (
        patch(
            "api.models.ContentVariation.objects.get",
            return_value=None,
        ),
        patch("logging.error") as mock_logging,
    ):
        wrapper.get_component_current_value("component1")
        mock_logging.assert_called_with(
            f"Content variation not found for content: {mock_content.id} when fetching component current value."
        )

    # Test logging errors
    with (
        patch(
            "api.models.ContentVariation.objects.get",
            return_value=mock_content_variation,
        ),
        patch("logging.error") as mock_logging,
    ):
        wrapper.get_component_current_value("component2", idx=1)
        mock_logging.assert_called_with(
            f"Component component2 does not have variations {mock_content_variation.variations}"
        )

        wrapper.get_component_current_value("component1", idx=3)
        mock_logging.assert_called_with(
            "Component component1 does not have variation 3"
        )

        wrapper.get_component_current_value("component2")
        mock_logging.assert_called_with(
            f"Component component2 does not have value {mock_content_variation.variations}"
        )


def test_content_gen_settings():
    data_wrapper = MagicMock()
    data_wrapper.components = {
        "component1": {"meta": {"type": "text"}},
    }
    data_wrapper.content_group_instance = MagicMock()
    data_wrapper.content_group_instance.content_group_params = {}
    settings = ContentGenSettings(
        data_wrapper,
        num_of_variations=2,
        foundation_model="gpt-4o-2024-11-20",
        save_variations=False,
    )
    assert settings.num_of_variations == 2
    assert settings.foundation_model == "gpt-4o-2024-11-20"


def test_generate_env():
    data_wrapper = MagicMock()
    gen_settings = MagicMock()
    env = GenerateEnv(data_wrapper, gen_settings)
    assert env._data_wrapper == data_wrapper
    assert env._gen_settings == gen_settings


@pytest.fixture
def mock_gen_env():
    gen_env = MagicMock()
    gen_env._data_wrapper.content_source_format = "Text"
    gen_env._data_wrapper.campaign_goal = "Personalization"
    gen_env._gen_settings.foundation_model = "gpt-4o-2024-11-20"
    gen_env._data_wrapper.content_type = ContentType.BlogPost
    return gen_env


def test_data_wrapper_holder(mock_gen_env):
    holder = DataWrapperHolder(mock_gen_env)
    assert holder.content_source_format == "Text"
    assert holder.campaign_goal == "Personalization"
    assert holder.foundation_model == "gpt-4o-2024-11-20"
    assert holder.content_type == ContentType.BlogPost


def test_content_group_wrapper_is_email(mock_content_group):
    mock_content_group.content_group_params = {
        "content_type": ContentType.EmailMarketing
    }
    wrapper = ContentGroupWrapper(mock_content_group)
    assert wrapper.is_email

    mock_content_group.content_group_params = {"content_type": ContentType.BlogPost}
    wrapper = ContentGroupWrapper(mock_content_group)
    assert not wrapper.is_email


@pytest.fixture
def mock_content_group_with_components():
    campaign = MagicMock(spec=Campaign)
    campaign.campaign_params = {
        "campaign_goal": "Personalization",
        "custom_instructions": [{"instruction": "Test1"}],
    }
    content_group = MagicMock(spec=ContentGroup)
    content_group.components = {
        "component1": {"meta": {"type": "text"}},
        "component2": {"meta": {"type": "text"}},
    }
    content_group.content_group_params = {
        "foundation_model": "gpt-4o-2024-11-20",
        "custom_instructions": [
            {"instruction": "Test2", "asset": "Asset1"},
            {"instruction": "Test3"},
        ],
    }
    content_group.campaign = campaign
    return content_group


@pytest.fixture
def mock_data_wrapper(mock_content_group_with_components):
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    return data_wrapper


def test_content_gen_settings_resolve_foundation_model(mock_data_wrapper):

    settings = ContentGenSettings(mock_data_wrapper, save_variations=False)
    assert settings.foundation_model == "gpt-4o-2024-11-20"


def test_data_wrapper_holder_aggregated_custom_instructions(mock_data_wrapper):

    gen_settings = MagicMock()
    gen_settings.enable_custom = True
    gen_settings.content_collection_plan_gen = False
    gen_settings.template_generation = False

    gen_env = GenerateEnv(mock_data_wrapper, gen_settings)

    holder = DataWrapperHolder(gen_env)
    instructions = holder.aggregated_custom_instructions

    assert len(instructions) == 3
    assert isinstance(instructions[0], dict)
    assert instructions[-1] == {"instruction": "Test3"}


def test_data_wrapper_holder_template_instructions(mock_content_group_with_components):
    mock_content_group_with_components.content_group_params["template_instructions"] = [
        {"instruction": "Test4"},
    ]
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    gen_settings = MagicMock()
    gen_settings.enable_custom = True
    gen_settings.content_collection_plan_gen = False
    gen_settings.template_generation = True
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    holder = DataWrapperHolder(gen_env)
    instructions = holder.aggregated_custom_instructions
    assert len(instructions) == 4
    assert instructions[-1] == {"instruction": "Test4"}


def test_content_gen_settings_resolve_components(mock_data_wrapper):
    settings = ContentGenSettings(mock_data_wrapper, save_variations=False)
    settings._resolve_components()

    assert settings._components == {
        "component1": {"meta": {"type": "text"}},
        "component2": {"meta": {"type": "text"}},
    }


def test_resolve_text_gen_components(mock_content_group_with_components):
    mock_content_group_with_components.components = {
        "component1": {"meta": {"type": "text"}},
        "component2": {"meta": {"type": "text"}},
        "component3": {"meta": {"type": "text", "component_type": "edited"}},
        "component4": {"meta": {"type": "image"}},
    }
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    settings = ContentGenSettings(data_wrapper, save_variations=False)
    assert settings._data_wrapper.text_gen_components == {
        "component1": {"meta": {"type": "text"}},
        "component2": {"meta": {"type": "text"}},
    }


def test_resolve_text_gen_components_with_one_generation_component(
    mock_content_group_with_components,
):
    mock_content_group_with_components.components = {
        "component1": {"meta": {"type": "text", "component_type": "edited"}},
        "component2": {"meta": {"type": "text"}},
        "component4": {"meta": {"type": "image"}},
    }
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    settings = ContentGenSettings(data_wrapper, save_variations=False)
    assert settings._data_wrapper.text_gen_components == {
        "component2": {"meta": {"type": "text"}},
    }


def test_content_gen_settings_resolve_joint_generation(
    mock_content_group_with_components,
):
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    with patch(
        "api.feature.data_wrapper.data_wrapper.model_is_json_enabled", return_value=True
    ):
        # Test case 1: JSON-enabled model, multiple components, Personalization goal
        settings = ContentGenSettings(
            data_wrapper,
            joint_generation=True,
            foundation_model="gpt-4",
            save_variations=False,
        )
        settings._resolve_joint_generation()
        assert settings._joint_generation

        # Test case 2: Non-JSON-enabled model
        with patch(
            "api.feature.data_wrapper.data_wrapper.model_is_json_enabled",
            return_value=False,
        ):
            settings = ContentGenSettings(
                data_wrapper,
                joint_generation=True,
                foundation_model="gpt-4o-2024-11-20",
                save_variations=False,
            )
            settings._resolve_joint_generation()
            assert not settings._joint_generation

        # Test case 3: JSON-enabled model, single component
        mock_content_group_with_components.components = {
            "component1": {"meta": {"type": "text"}}
        }
        data_wrapper = BaseContentWrapper.from_data_instance(
            mock_content_group_with_components
        )
        settings = ContentGenSettings(
            data_wrapper,
            foundation_model="gpt-4",
            joint_generation=True,
            save_variations=False,
        )
        settings._resolve_joint_generation()
        assert not settings._joint_generation

        # Test case 4: JSON-enabled model, multiple components, non-Personalization goal
        mock_content_group_with_components.campaign.campaign_goal = "Other"
        data_wrapper = BaseContentWrapper.from_data_instance(
            mock_content_group_with_components
        )
        settings = ContentGenSettings(
            data_wrapper,
            foundation_model="gpt-4",
            joint_generation=False,
            save_variations=False,
        )
        settings._resolve_joint_generation()
        assert not settings._joint_generation

        # Test case 5: JSON-enabled model, multiple components, non-Personalization goal, joint_generation=True
        mock_content_group_with_components.components = {
            "component1": {"meta": {"type": "text"}},
            "component2": {"meta": {"type": "text"}},
            "component3": {"meta": {"type": "text", "component_type": "edited"}},
            "component4": {"meta": {"type": "image"}},
        }
        data_wrapper = BaseContentWrapper.from_data_instance(
            mock_content_group_with_components
        )
        settings = ContentGenSettings(
            data_wrapper,
            foundation_model="gpt-4",
            joint_generation=True,
            save_variations=False,
        )
        settings._resolve_joint_generation()
        assert settings._joint_generation
        assert len(settings._data_wrapper.text_gen_components) == 2

        # Test case 6: JSON-enabled model, single text component with other non generation components
        mock_content_group_with_components.components = {
            "component1": {"meta": {"type": "text"}},
            "component2": {"meta": {"type": "image"}},
        }
        data_wrapper = BaseContentWrapper.from_data_instance(
            mock_content_group_with_components
        )
        settings = ContentGenSettings(
            data_wrapper,
            foundation_model="gpt-4",
            joint_generation=True,
            save_variations=False,
        )
        settings._resolve_joint_generation()
        assert not settings._joint_generation


def test_content_gen_settings_resolve_num_of_variations(mock_data_wrapper):

    settings = ContentGenSettings(
        mock_data_wrapper, foundation_model="gpt-4o-2024-11-20", save_variations=False
    )
    settings._resolve_num_of_variations()

    assert settings._num_of_variations == 2


def test_content_gen_settings_resolve_long_form_generation():
    data_wrapper = MagicMock()
    data_wrapper.campaign_goal = "Repurpose Content"
    data_wrapper.content_type = ContentType.BlogPost
    data_wrapper.aggregated_custom_instructions = [
        {"instruction": "Generate a 3000 word blog post"}
    ]
    data_wrapper.components = {}
    data_wrapper.content_group_instance = MagicMock()
    data_wrapper.content_group_instance.content_group_params = {}
    settings = ContentGenSettings(
        data_wrapper,
        foundation_model="gpt-4o-2024-11-20",
        save_variations=False,
    )
    settings._resolve_long_form_generation()

    assert settings._is_long_form_generation
    assert settings._long_form_output_length == 3000
    assert settings._num_of_variations == 1


def test_content_gen_settings_resolve_long_form_generation_with_content_word_count():
    data_wrapper = MagicMock()
    data_wrapper.campaign_goal = "Repurpose Content"
    data_wrapper.content_type = ContentType.BlogPost

    data_wrapper.components = {}
    data_wrapper.content_group_instance = MagicMock()
    data_wrapper.content_word_count = "5000"
    data_wrapper.content_group_instance.content_group_params = {}
    settings = ContentGenSettings(
        data_wrapper,
        foundation_model="gpt-4o-2024-11-20",
        save_variations=False,
    )
    settings._resolve_long_form_generation()

    assert settings._is_long_form_generation
    assert settings._long_form_output_length == 5000
    assert settings._num_of_variations == 1


def test_content_gen_settings_resolve_long_form_generation_with_content_word_count_bigger_than_max():
    data_wrapper = MagicMock()
    data_wrapper.campaign_goal = "Repurpose Content"
    data_wrapper.content_type = ContentType.BlogPost

    data_wrapper.components = {}
    data_wrapper.content_group_instance = MagicMock()
    data_wrapper.content_word_count = "15000"
    data_wrapper.content_group_instance.content_group_params = {}
    settings = ContentGenSettings(
        data_wrapper,
        foundation_model="gpt-4o-2024-11-20",
        save_variations=False,
    )
    settings._resolve_long_form_generation()

    assert settings._is_long_form_generation
    assert settings._long_form_output_length == 10000
    assert settings._num_of_variations == 1


def test_content_gen_settings_resolve_long_form_generation_with_content_word_and_instruction():
    data_wrapper = MagicMock()
    data_wrapper.campaign_goal = "Repurpose Content"
    data_wrapper.content_type = ContentType.BlogPost

    data_wrapper.components = {}
    data_wrapper.content_group_instance = MagicMock()
    data_wrapper.content_word_count = "9000"
    data_wrapper.aggregated_custom_instructions = [
        {"instruction": "Generate a 3000 word blog post"}
    ]
    data_wrapper.content_group_instance.content_group_params = {}
    settings = ContentGenSettings(
        data_wrapper,
        foundation_model="gpt-4o-2024-11-20",
        save_variations=False,
    )
    settings._resolve_long_form_generation()

    assert settings._is_long_form_generation
    assert settings._long_form_output_length == 9000
    assert settings._num_of_variations == 1


def test_content_gen_settings_resolve_long_form_generation_with_content_word_count_is_none():
    data_wrapper = MagicMock()
    data_wrapper.campaign_goal = "Repurpose Content"
    data_wrapper.campaign_instance = MagicMock()
    data_wrapper.campaign_instance.campaign_params = {}
    data_wrapper.content_type = ContentType.BlogPost

    data_wrapper.components = {}
    data_wrapper.content_group_instance = MagicMock()
    data_wrapper.content_word_count = None
    data_wrapper.content_group_instance.content_group_params = {}
    settings = ContentGenSettings(
        data_wrapper,
        foundation_model="gpt-4o-2024-11-20",
        save_variations=False,
    )
    settings._resolve_long_form_generation()

    assert settings._is_long_form_generation == False
    assert settings._long_form_output_length == 0
    assert settings._num_of_variations == 2


def test_data_wrapper_holder_aggregated_asset_params(
    mock_content_group_with_components,
):
    mock_content_group_with_components.campaign.campaign_params["assets"] = [
        {"asset": "Asset1", "instruction": "Instruction1"},
        {"asset": "Asset2"},
    ]
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )

    gen_settings = MagicMock()
    gen_settings.enable_custom = True
    gen_settings.content_collection_plan_gen = False
    gen_settings.template_generation = False

    gen_env = GenerateEnv(data_wrapper, gen_settings)
    holder = DataWrapperHolder(gen_env)

    # Test with enable_custom = True
    asset_params = holder.aggregated_asset_params
    assert len(asset_params) == 2
    assert {"asset": "Asset1", "instruction": "Instruction1"} in asset_params
    assert {"asset": "Asset2"} in asset_params

    # Test with enable_custom = False
    gen_settings.enable_custom = False
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    holder = DataWrapperHolder(gen_env)
    asset_params = holder.aggregated_asset_params
    assert len(asset_params) == 1
    assert {"asset": "Asset2"} in asset_params


def test_content_gen_settings_resolve_repurpose_template(
    mock_content_group_with_components,
):
    mock_content_group_with_components.content_group_params[
        "repurpose_template_content_source_copy"
    ] = "template_content"
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )

    with patch(
        "api.feature.data_wrapper.data_wrapper.get_template"
    ) as mock_get_template:
        mock_get_template.return_value = {"template": "data"}

        settings = ContentGenSettings(data_wrapper, save_variations=False)

        assert settings._data_wrapper.repurpose_template == {"template": "data"}

    # Test when repurpose_template_content_source_copy is not set
    mock_content_group_with_components.content_group_params.pop(
        "repurpose_template_content_source_copy"
    )
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )

    settings = ContentGenSettings(data_wrapper, save_variations=False)

    assert settings._data_wrapper.repurpose_template == {}


def test_content_gen_settings_resolve_full_page_html_gen(
    mock_content_group_with_components,
):
    mock_content_group_with_components.campaign.campaign_params["campaign_goal"] = (
        "Repurpose Content"
    )
    mock_content_group_with_components.content_group_params["content_source_format"] = (
        "Html"
    )
    mock_content_group_with_components.components = {
        "component1": {"meta": {"type": "text"}},
        "component2": {"meta": {"type": "text"}},
    }
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )

    settings = ContentGenSettings(data_wrapper, save_variations=False)
    settings._resolve_full_page_html_gen()

    assert settings._full_page_html_gen

    # Test when conditions are not met
    mock_content_group_with_components.campaign.campaign_params["campaign_goal"] = (
        "Other"
    )
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )

    settings = ContentGenSettings(data_wrapper, save_variations=False)
    settings._resolve_full_page_html_gen()

    assert not settings._full_page_html_gen

    # Test with single component
    mock_content_group_with_components.campaign.campaign_params["campaign_goal"] = (
        "Repurpose Content"
    )
    mock_content_group_with_components.components = {
        "component1": {"meta": {"type": "text"}},
    }
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )

    settings = ContentGenSettings(data_wrapper, save_variations=False)
    settings._resolve_full_page_html_gen()

    assert not settings._full_page_html_gen


def test_free_gen_components_resolution(mock_content_group_with_components):
    mock_content_group_with_components.components = {
        "component1": {"meta": {"type": "text"}},
        "component2": {"meta": {"type": "text"}},
    }
    data_wrapper = BaseContentWrapper.from_data_instance(
        mock_content_group_with_components
    )
    components_param = {
        "component3": {
            "meta": {
                "parent_component_id": "component1",
                "custom_instructions": [{"instruction": "Foo"}],
            },
            "text": "Bar",
        }
    }
    gen_settings = ContentGenSettings(
        data_wrapper, components=components_param, free_gen=True, save_variations=False
    )
    gen_env = GenerateEnv(data_wrapper, gen_settings)
    assert gen_env._gen_settings._components == {
        "component3": {
            "meta": {
                "parent_component_id": "component1",
                "custom_instructions": [{"instruction": "Foo"}],
            },
            "text": "Bar",
        },
    }


def test_content_group_wrapper_content_word_count_v3():
    # Setup mock content group with v3 structure
    content_group = MagicMock(spec=ContentGroup)
    content_group.is_campaign_v3 = True

    # Mock action instance with proper TofuDataList structure
    action = MagicMock()
    word_count_data = TofuDataList()
    data = TofuData()
    data.int_value.value = 500
    word_count_data.data.append(data)
    action.inputs = {"content_word_count": word_count_data}
    content_group.action = action

    wrapper = ContentGroupWrapper(content_group)
    assert wrapper.content_word_count == 500

    # Test when action instance is None
    content_group.action = None
    wrapper = ContentGroupWrapper(content_group)
    assert wrapper.content_word_count == 0

    # Test when content_word_count is not in inputs
    action.inputs = {}
    content_group.action = action
    wrapper = ContentGroupWrapper(content_group)
    assert wrapper.content_word_count == 0


@patch("logging.error")
@patch("logging.info")
def test_content_group_wrapper_content_word_count_logging(mock_info, mock_error):
    # Test error logging when action instance not found
    content_group = MagicMock(spec=ContentGroup)
    content_group.is_campaign_v3 = True
    content_group.id = 123
    content_group.action = None

    wrapper = ContentGroupWrapper(content_group)
    wrapper.content_word_count
    mock_error.assert_called_with("Action instance not found for content group: 123")

    # Test info logging when content_word_count not found
    action = MagicMock()
    action.id = 456
    action.inputs = {}
    content_group.action = action

    wrapper = ContentGroupWrapper(content_group)
    wrapper.content_word_count
    mock_info.assert_called_with(
        "Content word count not found for action instance: 456"
    )


def test_template_settings_handles_non_dictionary_values():
    """
    Test that template_settings property handles non-dictionary values correctly
    by returning an empty dictionary instead.
    """
    # Create a mock content group
    mock_content_group = MagicMock(spec=ContentGroup)

    # Set up the content_group_params to return a non-dictionary value for template_settings
    mock_content_group.content_group_params = {"template_settings": "not_a_dictionary"}

    # Create the wrapper
    wrapper = ContentGroupWrapper(mock_content_group)

    # Verify that template_settings returns an empty dictionary
    assert wrapper.template_settings == {}

    # Test with other non-dictionary values
    mock_content_group.content_group_params = {"template_settings": 123}
    assert wrapper.template_settings == {}

    mock_content_group.content_group_params = {"template_settings": True}
    assert wrapper.template_settings == {}

    mock_content_group.content_group_params = {"template_settings": [1, 2, 3]}
    assert wrapper.template_settings == {}

    # Verify it still works with None
    mock_content_group.content_group_params = {"template_settings": None}
    assert wrapper.template_settings == {}
