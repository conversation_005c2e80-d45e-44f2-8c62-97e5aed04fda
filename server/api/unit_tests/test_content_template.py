from django.test import TestCase

from ..models import CompanyInfo, ContentTemplate, Playbook, TofuUser


class ContentTemplateDefaultTypesTest(TestCase):
    """Tests for ensuring unique default content types in ContentTemplate model."""

    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = TofuUser.objects.create_user(
            username="<EMAIL>", password="password123"
        )

        # Create a test playbook
        company_info = CompanyInfo.objects.create()
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=company_info
        )

        # Sample content types
        self.content_types = ["email", "blog", "social", "ad", "video"]

    def test_unique_default_content_types(self):
        """Test that default content types are unique across templates for a playbook."""
        # Create first template with 'email' and 'blog' as default types
        template1 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=self.playbook,
            name="Template 1",
            content_types=self.content_types,
            default_content_types=["email", "blog"],
        )

        # Create second template with 'social' and 'ad' as default types
        template2 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=self.playbook,
            name="Template 2",
            content_types=self.content_types,
            default_content_types=["social", "ad"],
        )

        # Verify both templates have their original default types
        template1.refresh_from_db()
        template2.refresh_from_db()
        self.assertEqual(set(template1.default_content_types), {"email", "blog"})
        self.assertEqual(set(template2.default_content_types), {"social", "ad"})

        # Update template1 to include 'social' which is in template2
        template1.default_content_types = ["email", "blog", "social"]
        template1.save()

        # Refresh from database and verify template2 no longer has 'social'
        template1.refresh_from_db()
        template2.refresh_from_db()
        self.assertEqual(
            set(template1.default_content_types), {"email", "blog", "social"}
        )
        self.assertEqual(set(template2.default_content_types), {"ad"})

    def test_multiple_templates_updated(self):
        """Test that multiple templates are updated correctly."""
        # Create three templates with different default types
        template1 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=self.playbook,
            name="Template 1",
            content_types=self.content_types,
            default_content_types=["email"],
        )

        template2 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=self.playbook,
            name="Template 2",
            content_types=self.content_types,
            default_content_types=["blog"],
        )

        template3 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=self.playbook,
            name="Template 3",
            content_types=self.content_types,
            default_content_types=["social"],
        )

        # Create a new template that takes over all default types
        template4 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=self.playbook,
            name="Template 4",
            content_types=self.content_types,
            default_content_types=["email", "blog", "social"],
        )

        # Refresh from database and verify other templates have empty default types
        template1.refresh_from_db()
        template2.refresh_from_db()
        template3.refresh_from_db()

        self.assertEqual(template1.default_content_types, [])
        self.assertEqual(template2.default_content_types, [])
        self.assertEqual(template3.default_content_types, [])
        self.assertEqual(
            set(template4.default_content_types), {"email", "blog", "social"}
        )

    def test_different_playbooks_not_affected(self):
        """Test that templates in different playbooks don't affect each other."""
        # Create another playbook
        company_info2 = CompanyInfo.objects.create()
        playbook2 = Playbook.objects.create(
            name="Second Playbook", company_object=company_info2
        )

        # Create template in first playbook
        template1 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=self.playbook,
            name="Template 1",
            content_types=self.content_types,
            default_content_types=["email", "blog"],
        )

        # Create template in second playbook with same default types
        template2 = ContentTemplate.objects.create(
            creator=self.user,
            playbook=playbook2,
            name="Template 2",
            content_types=self.content_types,
            default_content_types=["email", "blog"],
        )

        # Both templates should keep their default types
        template1.refresh_from_db()
        template2.refresh_from_db()
        self.assertEqual(set(template1.default_content_types), {"email", "blog"})
        self.assertEqual(set(template2.default_content_types), {"email", "blog"})
