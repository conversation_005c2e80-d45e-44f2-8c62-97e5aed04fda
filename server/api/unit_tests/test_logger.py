import json

from django.test import TestCase

from ..logger import TofuAxiomLogger


class TestTofuAxiomLogger(TestCase):
    def setUp(self):
        self.logger = TofuAxiomLogger()

    def test_remove_none_values_and_serialize_deep(self):
        # Test input with various levels of nesting and None values
        test_input = {
            "id": 1,
            "name": "test",
            "null_field": None,
            "level1": {
                "a": "value",
                "b": None,
                "level2": {"x": "deep", "y": None, "level3": {"deep_field": "value"}},
            },
            "another_level1": {"c": "value", "level2_another": {"z": "deep2"}},
        }

        expected_output = {
            "id": 1,
            "name": "test",
            "level1": {
                "a": "value",
                "level2": json.dumps(
                    {"x": "deep", "y": None, "level3": {"deep_field": "value"}}
                ),
            },
            "another_level1": {
                "c": "value",
                "level2_another": json.dumps({"z": "deep2"}),
            },
        }

        result = self.logger.remove_none_values_and_serialize_deep(test_input)

        # Compare the results
        self.assertEqual(result, expected_output)

        # Verify that serialized parts are valid JSON
        self.assertTrue(json.loads(result["level1"]["level2"]))
        self.assertTrue(json.loads(result["another_level1"]["level2_another"]))

    def test_remove_none_values_and_serialize_deep_edge_cases(self):
        # Test empty dict
        self.assertEqual(self.logger.remove_none_values_and_serialize_deep({}), {})

        # Test non-dict input
        self.assertEqual(
            self.logger.remove_none_values_and_serialize_deep("string"), "string"
        )
        self.assertEqual(self.logger.remove_none_values_and_serialize_deep(123), 123)

        # Test dict with only None values
        self.assertEqual(
            self.logger.remove_none_values_and_serialize_deep({"a": None, "b": None}),
            {},
        )

        # Test nested dict with no None values
        input_dict = {"a": {"b": {"c": "value"}}}
        expected = {"a": {"b": json.dumps({"c": "value"})}}
        self.assertEqual(
            self.logger.remove_none_values_and_serialize_deep(input_dict), expected
        )
