from unittest.mock import MagicMock, patch

import pytest
from api.playbook_build.playbook_builder import PlaybookBuilder


@pytest.fixture
def mock_playbook_instance():
    playbook_instance = MagicMock()
    playbook_instance.id = 1
    return playbook_instance


@pytest.fixture
def playbook_builder(mock_playbook_instance):
    with patch(
        "api.playbook_build.playbook_builder.PlaybookBuilder.__init__"
    ) as mock_init:
        mock_init.return_value = None
        builder = PlaybookBuilder(playbook_instance=mock_playbook_instance)
        builder.playbook_instance = mock_playbook_instance
        builder.update_context = MagicMock()
        return builder


def test_pre_build_playbook_context(playbook_builder):
    target_params = {
        "l1_key1": "l2_key1",
        "l1_key2": "l2_key2",
    }

    mock_target = MagicMock()

    with patch(
        "api.playbook_build.playbook_builder.TargetInfo.objects.filter"
    ) as mock_filter:
        mock_filter.return_value.first.return_value = mock_target

        playbook_builder.pre_build_playbook_context(target_params)

        # Assert that filter was called twice (once for each target)
        assert mock_filter.call_count == 2

        # Assert that update_context was called with correct parameters
        playbook_builder.update_context.assert_called_once_with(
            objects_to_rebuild=[mock_target, mock_target], parallel_threads=1
        )


def test_pre_build_playbook_context_target_not_found(playbook_builder):
    target_params = {
        "l1_key1": "l2_key1",
    }

    with (
        patch(
            "api.playbook_build.playbook_builder.TargetInfo.objects.filter"
        ) as mock_filter,
        patch(
            "api.playbook_build.playbook_builder.logging.error"
        ) as mock_logging_error,
    ):
        mock_filter.return_value = []

        with pytest.raises(Exception) as excinfo:
            playbook_builder.pre_build_playbook_context(target_params)

        # Assert that the correct exception message is raised
        assert (
            str(excinfo.value)
            == "Target l1_key1 l2_key1 not found for content gen pre-build"
        )

        # Assert that filter was called once
        mock_filter.assert_called_once()

        # Assert that logging.error was called
        mock_logging_error.assert_called_once_with(
            "Target l1_key1 l2_key1 not found for content gen pre-build"
        )

        # Assert that update_context was not called
        playbook_builder.update_context.assert_not_called()
