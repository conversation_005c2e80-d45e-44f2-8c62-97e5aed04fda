import logging
import os
from unittest.mock import MagicMock, patch

import pytest
from api.async_tasks import postprocess_company_info
from api.models import CompanyInfo, Playbook, TofuUser
from django.test import TestCase


class TestAsyncTasks(TestCase):
    def setUp(self):
        # Create the company info
        self.company_info = CompanyInfo.objects.create()
        # Create a mock playbook
        self.playbook = Playbook.objects.create(
            company_object=self.company_info,
        )
        # Create an admin user
        self.admin_user = TofuUser.objects.create(username="tofuadmin_test")
        self.playbook.users.add(self.admin_user)

    @patch("api.async_tasks.get_company_website")
    @patch("api.async_tasks.async_generated_suggested_connected_asset_group")
    @patch.dict(os.environ, {"TOFU_ENV": "not_unit_test"})
    def test_postprocess_company_info_no_website(
        self, mock_async_task, mock_get_website
    ):
        mock_get_website.return_value = None

        # Call the function directly
        postprocess_company_info(CompanyInfo, self.company_info, created=True)

        # Verify the async task was not called
        mock_async_task.apply_async.assert_not_called()

    @patch("api.async_tasks.get_company_website")
    @patch("api.async_tasks.async_generated_suggested_connected_asset_group")
    @patch.dict(os.environ, {"TOFU_ENV": "not_unit_test"})
    def test_postprocess_company_info_no_admin_user(
        self, mock_async_task, mock_get_website
    ):
        # Remove admin user and add non-admin user
        self.playbook.users.remove(self.admin_user)
        non_admin_user = TofuUser.objects.create(username="regular_user")
        self.playbook.users.add(non_admin_user)

        # Call the function directly
        postprocess_company_info(CompanyInfo, self.company_info, created=True)

        # Verify the async task was not called
        mock_async_task.apply_async.assert_called_once()

    @patch("api.async_tasks.logger")
    @patch.dict(os.environ, {"TOFU_ENV": "not_unit_test"})
    def test_postprocess_company_info_no_playbook(self, mock_logger):
        company_info = CompanyInfo.objects.create()

        # Call the function directly
        postprocess_company_info(CompanyInfo, company_info, created=True)

        # Verify no exception was logged
        mock_logger.exception.assert_not_called()

    @patch("api.async_tasks.get_company_website")
    @patch("api.async_tasks.async_generated_suggested_connected_asset_group")
    @patch.dict(os.environ, {"TOFU_ENV": "not_unit_test"})
    def test_postprocess_company_info_success(self, mock_async_task, mock_get_website):
        mock_get_website.return_value = "https://example.com"

        # Call the function directly
        postprocess_company_info(CompanyInfo, self.company_info, created=True)

        # Verify the async task was called
        mock_async_task.apply_async.assert_called_once()
