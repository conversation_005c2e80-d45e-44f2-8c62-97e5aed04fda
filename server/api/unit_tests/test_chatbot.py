from unittest.mock import MagicMock, call, patch

import pytest
from langchain.schema import AIMessage, Generation, HumanMessage

from ..chatbot import PlaygroundChatGenerator, get_url_message, get_urls_from_text
from ..content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from ..model_config import ModelConfig, ModelParams
from ..models import (
    AssetInfo,
    AssetInfoGroup,
    ChatHistory,
    CompanyInfo,
    Playbook,
    TofuUser,
)
from ..playground.chatbot_status import PlaygroundChatbotStatus, TaskState
from ..playground.langgraph.langgraph_chat_generator import GenerateChatRequest
from ..task_registry import GenerationGoal


@pytest.fixture
def mock_model_params():
    return ModelParams(
        model_name="gpt-4o-2024-11-20",
        model_params={
            "model_name": "gpt-4o-2024-11-20",
            "components": [],
            "n": 1,
            "max_retries": 3,
        },
    )


@pytest.fixture
def mock_model_config(mock_model_params):
    config = ModelConfig(model_budget=1000, model_params_list=[mock_model_params])
    return config


@patch("api.models.Playbook.objects.filter")
@patch("api.model_config.ModelConfigResolver.resolve")
@patch("api.utils.get_token_count")
@patch("api.chatbot.ModelCaller")
def test_get_chat_response(
    MockModelCaller,
    mock_get_token_count,
    mock_model_config_resolve,
    mock_playbook_objects_filter,
    mock_model_config,
):
    # Setup mocks
    # Mock Playbook query
    mock_playbook = MagicMock()
    mock_playbook_objects_filter.return_value.first.return_value = mock_playbook

    mock_model_config_resolve.return_value = mock_model_config
    mock_get_token_count.return_value = 10

    mock_model_caller = MagicMock()
    mock_model_caller.model_config = mock_model_config
    MockModelCaller.return_value = mock_model_caller

    mock_model_caller.check_and_set_lock_token_usage.return_value = None
    mock_model_caller.get_results_with_fallback.return_value = [
        Generation(text="AI response")
    ]
    mock_model_caller.get_messages_token_count.return_value = 40

    # Test inputs
    model = "gpt-4o-2024-11-20"
    previous_messages = [HumanMessage(content="Hello"), AIMessage(content="Hi there!")]
    new_message = HumanMessage(content="How are you?")
    system_context = {
        "company_name": "Test Company",
        "company_context": "Test context",
        "valid_target_keys": [],
        "brand_guidelines": "Test guidelines",
    }

    # Call the method
    user = MagicMock()
    chat_history = MagicMock()
    with (
        patch("api.feature.data_wrapper.data_wrapper.PlaybookWrapper"),
        patch("api.feature.data_wrapper.data_wrapper.DefaultGenSettings"),
        patch("api.feature.data_wrapper.data_wrapper.GenerateEnv"),
    ):
        chat_generator = PlaygroundChatGenerator(user, chat_history, "test_thread_id")
        chat_generator.model_caller = mock_model_caller
        chat_generator.model_budget = mock_model_config.model_budget
        response = chat_generator.get_chat_response(
            model, previous_messages, new_message, system_context, "test_task"
        )

    # Assertions
    assert isinstance(response, AIMessage)
    assert response.content == "AI response"


def test_brand_guideline_rewriting():
    playbook = MagicMock(spec=Playbook)
    user = MagicMock(spec=TofuUser)
    chat_history = MagicMock()
    with (
        patch("api.models.Playbook.objects.filter") as mock_filter,
        patch("api.feature.data_wrapper.data_wrapper.PlaybookWrapper"),
        patch("api.feature.data_wrapper.data_wrapper.DefaultGenSettings"),
        patch("api.feature.data_wrapper.data_wrapper.GenerateEnv"),
    ):
        mock_filter.return_value = MagicMock()
        mock_filter.return_value.first.return_value = playbook
        playground_generator = PlaygroundChatGenerator(
            user, chat_history, "test_thread_id"
        )
        playground_generator.model_caller = MagicMock()
        playground_generator.model_caller.model_config = MagicMock()
        playground_generator.model_caller.model_config.model_params_list = [MagicMock()]
        playground_generator.model_caller.model_config.model_params_list[
            0
        ].model_name = "gpt-4o-2024-11-20"
        playground_generator.model_caller.model_config.generation_goal = (
            GenerationGoal.CHATBOT
        )
        playground_generator.model_caller.model_config.model_budget = 1000
        with patch(
            "api.content_gen.default_content_gen_postprocessor.ModelCaller.get_results_with_fallback"
        ) as mock_get_results:
            mock_get_results.return_value = [
                Generation(
                    text="<thinking>...</thinking><content>rewritten_response</content>"
                )
            ]
            response = playground_generator.brand_guideline_rewriting(
                "response", "brand_guidelines"
            )
            assert response == "rewritten_response"

            default_content_gen_postprocessor = DefaultContentGenPostprocessor(
                gen_env=playground_generator.gen_env,
                model_caller=playground_generator.model_caller,
            )

            metadata = default_content_gen_postprocessor.get_metadata()
            assert metadata["brand_guideline_rewriting"] == True
            assert metadata["content_id"] == None
            assert metadata["campaign_id"] == None
            assert metadata["content_type"] == ""
            assert metadata["campaign_goal"] == None


def test_get_urls_from_text():
    # Test with no URLs
    assert get_urls_from_text("Hello world") == []

    # Test with single URL
    text_with_url = "Check out https://example.com"
    assert get_urls_from_text(text_with_url) == ["https://example.com"]

    # Test with different URL format
    text_with_url = "Check out http://www.example.com"
    assert get_urls_from_text(text_with_url) == ["http://www.example.com"]

    text_with_url = "Check out http://www.example.com/page?param=1"
    assert get_urls_from_text(text_with_url) == ["http://www.example.com/page?param=1"]

    text_with_url = "Check out www.example.com/"
    assert get_urls_from_text(text_with_url) == ["www.example.com/"]

    # Test with multiple URLs
    text_with_multiple_urls = (
        "Visit https://example.com and http://test.com/page?param=1"
    )
    expected_urls = ["https://example.com", "http://test.com/page?param=1"]
    assert get_urls_from_text(text_with_multiple_urls) == expected_urls

    # Test with various TLDs
    text_with_various_tlds = (
        "Visit example.com, example.org, example.net, and example.edu"
    )
    expected_urls_tlds = [
        "example.com",
        "example.org",
        "example.net",
        "example.edu",
    ]
    assert get_urls_from_text(text_with_various_tlds) == expected_urls_tlds

    text_with_more_tlds = "Check out example.gov, example.io, and example.co"
    expected_urls_more_tlds = [
        "example.gov",
        "example.io",
        "example.co",
    ]
    assert get_urls_from_text(text_with_more_tlds) == expected_urls_more_tlds

    text_with_even_more_tlds = "Visit example.ai, example.app, and example.dev"
    expected_urls_even_more_tlds = [
        "example.ai",
        "example.app",
        "example.dev",
    ]
    assert get_urls_from_text(text_with_even_more_tlds) == expected_urls_even_more_tlds

    # Test with PDF URL
    text_with_pdf_url = "Download our guide: https://assets.unanet.com/m/3b53019c361eca6c/original/UGC-G2-One-Pager-Feedback.pdf"
    expected_pdf_url = [
        "https://assets.unanet.com/m/3b53019c361eca6c/original/UGC-G2-One-Pager-Feedback.pdf"
    ]
    assert get_urls_from_text(text_with_pdf_url) == expected_pdf_url

    # Test with URLs wrapped in ** using the same pattern
    text_with_wrapped_urls = "write an email to **@<EMAIL>**, use the context from **@https://www.riskified.com** and check https://example.com"
    expected_wrapped_urls = ["https://example.com"]
    assert get_urls_from_text(text_with_wrapped_urls) == expected_wrapped_urls

    # Test with multiple wrapped URLs
    text_with_multiple_wrapped_urls = "use **@https://asset1.com** and **@https://asset2.com** but also check https://real-url.com"
    expected_multiple_wrapped_urls = ["https://real-url.com"]
    assert (
        get_urls_from_text(text_with_multiple_wrapped_urls)
        == expected_multiple_wrapped_urls
    )

    # Test with mixed wrapped and unwrapped URLs
    text_with_mixed_urls = "use **@https://asset1.com** and check https://real1.com and **@https://asset2.com** and https://real2.com"
    expected_mixed_urls = ["https://real1.com", "https://real2.com"]
    assert get_urls_from_text(text_with_mixed_urls) == expected_mixed_urls

    # Test with non-URL content wrapped in **
    text_with_non_url_wrapped = "use **@<EMAIL>** and **some other text** and check https://example.com"
    expected_non_url_wrapped = ["https://example.com"]
    assert get_urls_from_text(text_with_non_url_wrapped) == expected_non_url_wrapped

    # Test with wrapped URLs without https:// prefix
    text_with_wrapped_urls_no_https = "use **@www.asset1.com** and **@asset2.com** but also check https://real-url.com"
    expected_wrapped_urls_no_https = ["https://real-url.com"]
    assert (
        get_urls_from_text(text_with_wrapped_urls_no_https)
        == expected_wrapped_urls_no_https
    )

    # Test with wrapped URLs with different protocols
    text_with_wrapped_urls_protocols = "use **@http://asset1.com** and **@https://asset2.com** and **@www.asset3.com** but check https://real-url.com"
    expected_wrapped_urls_protocols = ["https://real-url.com"]
    assert (
        get_urls_from_text(text_with_wrapped_urls_protocols)
        == expected_wrapped_urls_protocols
    )

    # Test with wrapped URLs with paths
    text_with_wrapped_urls_paths = "use **@https://asset1.com/path1** and **@www.asset2.com/path2** but check https://real-url.com"
    expected_wrapped_urls_paths = ["https://real-url.com"]
    assert (
        get_urls_from_text(text_with_wrapped_urls_paths) == expected_wrapped_urls_paths
    )

    # Test with wrapped URLs with query parameters
    text_with_wrapped_urls_params = "use **@https://asset1.com?param=1** and **@www.asset2.com?param=2** but check https://real-url.com"
    expected_wrapped_urls_params = ["https://real-url.com"]
    assert (
        get_urls_from_text(text_with_wrapped_urls_params)
        == expected_wrapped_urls_params
    )

    # Test with wrapped URLs with subdomains
    text_with_wrapped_urls_subdomains = (
        "use **@sub.asset1.com** and **@www.asset2.com** but check https://real-url.com"
    )
    expected_wrapped_urls_subdomains = ["https://real-url.com"]
    assert (
        get_urls_from_text(text_with_wrapped_urls_subdomains)
        == expected_wrapped_urls_subdomains
    )

    # Test with wrapped URLs with different TLDs
    text_with_wrapped_urls_tlds = "use **@asset1.com** and **@asset2.org** and **@asset3.io** but check https://real-url.com"
    expected_wrapped_urls_tlds = ["https://real-url.com"]
    assert get_urls_from_text(text_with_wrapped_urls_tlds) == expected_wrapped_urls_tlds

    # Test with URLs containing special characters in paths
    text_with_special_chars = "Check **@https://example.com/path-with-dashes** and https://test.com/path_with_underscores"
    expected_special_chars = ["https://test.com/path_with_underscores"]
    assert get_urls_from_text(text_with_special_chars) == expected_special_chars

    # Test with URLs containing query parameters and fragments
    text_with_complex_urls = "Use **@https://example.com/path?param=value#section** and check https://test.com/path?q=1#top"
    expected_complex_urls = ["https://test.com/path?q=1#top"]
    assert get_urls_from_text(text_with_complex_urls) == expected_complex_urls

    # Test with URLs containing multiple subdomains
    text_with_multiple_subdomains = (
        "Check **@sub1.sub2.example.com** and https://test.com"
    )
    expected_multiple_subdomains = ["https://test.com"]
    assert (
        get_urls_from_text(text_with_multiple_subdomains)
        == expected_multiple_subdomains
    )


@patch("api.chatbot.get_urls_from_text")
@patch("api.chatbot.crawl_urls")
def test_get_url_message(mock_crawl_urls, mock_get_urls):
    # Test with no URLs
    mock_get_urls.return_value = []
    assert get_url_message("Hello world", "test_task") is None

    # Test with URLs but no content
    mock_get_urls.return_value = ["https://example.com"]
    mock_crawl_urls.return_value = {}
    assert get_url_message("Check https://example.com", "test_task") is None

    # Test with URLs and content
    mock_get_urls.return_value = ["https://example.com"]
    mock_crawl_urls.return_value = {"https://example.com": "Example content"}
    result = get_url_message("Check https://example.com", "test_task")

    assert isinstance(result, HumanMessage)
    assert 'url="https://example.com"' in result.content
    assert "Example content" in result.content
    assert result.additional_kwargs.get("keep_hidden") is True


@pytest.fixture
def mock_playbook():
    playbook = MagicMock(spec=Playbook)
    return playbook


@patch("api.models.Playbook.objects")
@patch(
    "api.playground.playground_feature_builder.PlaygroundFeatureBuilder.get_content_type_brand_guidelines"
)
@patch("api.chatbot.serialize_message_history")
def test_generate_chat_response_with_float_budget(
    mock_serialize_message_history,
    mock_get_content_type_brand_guidelines,
    mock_playbook,
):
    mock_serialize_message_history.return_value = []  # Mock to return empty list
    mock_get_content_type_brand_guidelines.return_value = "Test brand guidelines"

    # Mock dependencies
    user = MagicMock()
    chat_history = MagicMock()

    # Create generator instance
    with (
        patch("api.feature.data_wrapper.data_wrapper.PlaybookWrapper"),
        patch("api.feature.data_wrapper.data_wrapper.DefaultGenSettings"),
        patch("api.feature.data_wrapper.data_wrapper.GenerateEnv"),
    ):
        generator = PlaygroundChatGenerator(
            user=user, chat_history=chat_history, thread_id="test_thread_id"
        )

        # Mock model config to return a float budget
        model_config = MagicMock()
        model_config.model_budget = 10000.5  # Float budget

        expected_response = AIMessage(content="Test response")
        with patch.object(
            generator, "get_chat_response", return_value=expected_response
        ) as mock_get_chat_response:
            with patch(
                "api.model_config.ModelConfigResolver.resolve",
                return_value=model_config,
            ):
                generate_chat_request = GenerateChatRequest(
                    task_id="test_task",
                    model="test_model",
                    previous_messages=[],
                    new_message=MagicMock(content="test message"),
                    assets={"test_asset": ["asset1"]},
                )
                result = generator.generate_chat_response(
                    generate_chat_request,
                )
                # Verify get_chat_response was called with correct parameters
                mock_get_chat_response.assert_called_once()
                call_args = mock_get_chat_response.call_args[0]
                assert call_args[0] == "test_model"  # model
                assert isinstance(call_args[1], list)  # previous_messages
                assert call_args[2].content == "test message"  # new_message
                assert isinstance(call_args[3], dict)  # system_context

                # Verify serialize_message_history was called
                mock_serialize_message_history.assert_called_once()

                # Verify the final response
                assert result == "Test response"


@patch("api.chatbot.cache")
@patch("api.model_config.ModelConfigResolver.resolve")
@patch("api.chatbot.PlaygroundChatGenerator.brand_guideline_rewriting")
@patch("api.chatbot.get_url_message")
@patch("api.models.Playbook.objects.filter")
@patch("api.chatbot.get_urls_from_text")
@patch("api.chatbot.convert_messages_to_prompt_for_claude")
@patch("api.chatbot.PlaygroundChatGenerator.get_assets_message")
@patch("api.chatbot.PlaygroundChatGenerator.get_targets_message")
@patch("api.utils.get_token_count")
@patch("api.chatbot.ModelCaller")
def test_status_updates_with_url_and_brand_guidelines(
    MockModelCaller,
    mock_get_token_count,
    mock_get_targets_message,
    mock_get_assets_message,
    mock_convert_messages,
    mock_get_urls_from_text,
    mock_playbook_filter,
    mock_get_url_message,
    mock_brand_guideline_rewriting,
    mock_model_config_resolve,
    mock_cache,
):
    # Setup mocks
    user = MagicMock()
    chat_history = MagicMock()
    chat_history.key = "test_key"
    chat_history.json = {}

    # Mock the playbook query
    mock_playbook = MagicMock()
    mock_playbook_filter.return_value.first.return_value = mock_playbook

    mock_model_config = MagicMock()
    mock_model_config.model_budget = 1000
    mock_model_config_resolve.return_value = mock_model_config

    # Setup URL message
    mock_get_urls_from_text.return_value = ["https://example.com"]
    url_message = HumanMessage(
        content="URL content", additional_kwargs={"keep_hidden": True}
    )
    mock_get_url_message.return_value = url_message

    # Setup asset and target messages
    mock_get_assets_message.return_value = HumanMessage(
        content="Asset content", additional_kwargs={"keep_hidden": True}
    )
    mock_get_targets_message.return_value = HumanMessage(
        content="Target content", additional_kwargs={"keep_hidden": True}
    )

    # Setup chat response with enough words to trigger brand guideline rewriting
    long_response = "This is a response " + "word " * 60  # More than 50 words

    # Setup brand guideline rewriting
    mock_brand_guideline_rewriting.return_value = "Rewritten response"

    # Setup convert_messages_to_prompt_for_claude
    mock_convert_messages.return_value = [HumanMessage(content="Converted message")]

    # Mock token count to be under budget
    mock_get_token_count.return_value = 100

    # Create test data
    task_id = "test_task_id"
    model = "claude-3-sonnet-20240229"  # Use Claude model to trigger message conversion
    previous_messages = []
    new_message = HumanMessage(content="Test message with https://example.com")
    assets = {"asset_list": ["asset1"]}
    targets = {"target_list": ["target1"]}

    # Create generator and call method
    with (
        patch("api.feature.data_wrapper.data_wrapper.PlaybookWrapper"),
        patch("api.feature.data_wrapper.data_wrapper.DefaultGenSettings"),
        patch("api.feature.data_wrapper.data_wrapper.GenerateEnv"),
    ):
        # Reset the mock to clear any previous calls
        mock_cache.reset_mock()

        # Create the generator
        generator = PlaygroundChatGenerator(
            user=user, chat_history=chat_history, thread_id="test_thread_id"
        )
        generator.get_system_context = MagicMock(
            return_value={
                "company_name": "Test Company",
                "company_context": "Test context",
                "valid_target_keys": [],
                "brand_guidelines": "Test guidelines",
            }
        )

        # Setup model caller with proper response
        mock_model_caller = MagicMock()
        mock_model_caller.get_results_with_fallback = MagicMock(
            return_value=[Generation(text=long_response)]
        )
        MockModelCaller.return_value = mock_model_caller
        generator.model_caller = mock_model_caller
        generator.model_budget = 1000

        # Call the method that triggers the status updates
        generate_chat_request = GenerateChatRequest(
            task_id=task_id,
            model=model,
            previous_messages=previous_messages,
            new_message=new_message,
            assets=assets,
            targets=targets,
        )
        generator.generate_chat_response(
            generate_chat_request,
        )

    # Verify cache.set was called with the expected statuses in order
    expected_calls = [
        call(
            task_id,
            PlaygroundChatbotStatus(
                status=TaskState.RUNNING,
                display_message="Thinking...",
                current_step=None,
                step_output=None,
                steps=[],
            ),
            timeout=3600 * 24,
        ),
        call(
            task_id,
            PlaygroundChatbotStatus(
                status=TaskState.RUNNING,
                display_message="Reading Assets...",
                current_step="get_assets_message",
                step_output=f"Reading {len(assets)} assets",
                steps=[],
            ),
            timeout=3600 * 24,
        ),
        call(
            task_id,
            PlaygroundChatbotStatus(
                status=TaskState.RUNNING,
                display_message="Reading Targets...",
                current_step="get_targets_message",
                step_output=f"Reading {len(targets)} targets",
                steps=[],
            ),
            timeout=3600 * 24,
        ),
        call(
            task_id,
            PlaygroundChatbotStatus(
                status=TaskState.RUNNING,
                display_message="Thinking...",
                current_step=None,
                step_output=None,
                steps=[],
            ),
            timeout=3600 * 24,
        ),
        call(
            task_id,
            PlaygroundChatbotStatus(
                status=TaskState.RUNNING,
                display_message="Checking Style Guidelines...",
                current_step="brand_guideline_rewriting",
                step_output="rewritten response",
                steps=[],
            ),
            timeout=3600 * 24,
        ),
        call(
            task_id,
            PlaygroundChatbotStatus(
                status=TaskState.COMPLETED,
                message="Rewritten response",
                image_url=None,
                current_step=None,
                step_output=None,
                steps=[],
            ),
            timeout=3600 * 24,
        ),
    ]

    print(mock_cache.set.call_args_list)
    print(expected_calls)
    # Check that all expected calls were made in the correct order
    assert mock_cache.set.call_args_list == expected_calls


@pytest.mark.django_db
def test_cleanup_thread_assets():
    # Create test user and playbook
    user = TofuUser.objects.create(username="test_user")
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    playbook.users.add(user)

    # Create asset group
    asset_group = AssetInfoGroup.objects.create(
        playbook=playbook,
        asset_info_group_key="[TOFU Internal] Playground Files",
        meta={"position": 0},
    )

    # Create assets
    asset1 = AssetInfo.objects.create(
        asset_info_group=asset_group,
        asset_key="asset1",
        meta={"position": 0},
    )
    asset2 = AssetInfo.objects.create(
        asset_info_group=asset_group,
        asset_key="asset2",
        meta={"position": 1},
    )
    asset3 = AssetInfo.objects.create(
        asset_info_group=asset_group,
        asset_key="asset3",
        meta={"position": 2},
    )

    # Create test thread with thread_asset_ids in messages
    thread = ChatHistory.objects.create(
        creator=user,
        key="test_thread",
        model="test_model",
        json={
            "previous_messages": [
                {
                    "role": "user",
                    "content": "test message",
                    "thread_asset_ids": [asset1.id, asset2.id],
                }
            ]
        },
    )

    # Delete the thread
    thread.delete()

    # Verify assets with thread_asset_ids were deleted
    assert not AssetInfo.objects.filter(id=asset1.id).exists()
    assert not AssetInfo.objects.filter(id=asset2.id).exists()
    # Verify asset without thread_asset_ids still exists
    assert AssetInfo.objects.filter(id=asset3.id).exists()


@pytest.mark.django_db
def test_cleanup_thread_assets_no_assets():
    # Create test user and playbook
    user = TofuUser.objects.create(username="test_user")
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    playbook.users.add(user)

    # Create test thread with no thread_asset_ids
    thread = ChatHistory.objects.create(
        creator=user,
        key="test_thread",
        model="test_model",
        json={"previous_messages": []},
    )

    # Create asset group
    asset_group = AssetInfoGroup.objects.create(
        playbook=playbook,
        asset_info_group_key="[TOFU Internal] Playground Files",
        meta={"position": 0},
    )

    # Create an asset
    asset = AssetInfo.objects.create(
        asset_info_group=asset_group,
        asset_key="asset1",
        meta={"position": 0},
    )

    # Delete the thread
    thread.delete()

    # Verify asset still exists
    assert AssetInfo.objects.filter(id=asset.id).exists()


@pytest.mark.django_db
def test_cleanup_thread_assets_no_asset_group():
    # Create test user and playbook
    user = TofuUser.objects.create(username="test_user")
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    playbook.users.add(user)

    # Create test thread with thread_asset_ids
    thread = ChatHistory.objects.create(
        creator=user,
        key="test_thread",
        model="test_model",
        json={
            "previous_messages": [
                {
                    "role": "user",
                    "content": "test message",
                    "thread_asset_ids": [1, 2],  # Non-existent asset IDs
                }
            ]
        },
    )

    # Delete the thread (no asset group exists)
    thread.delete()

    # No exception should be raised
    assert True
