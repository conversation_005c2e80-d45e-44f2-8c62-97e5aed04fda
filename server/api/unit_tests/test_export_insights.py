import logging
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PropertyMock, patch

from django.core.cache import cache
from django.test import TestCase

from ..sync.e2e.hubspot_sync import PlaybookHubspotSyncer
from ..sync.e2e.salesforce_sync import PlaybookSalesforceSyncer


class ExportTofuInsightsTest(TestCase):
    def setUp(self):
        """Set up common test data and mocks"""
        # Mock the playbook instance
        self.mock_playbook = MagicMock()

        # Mock the target info group
        self.mock_target_info_group = MagicMock()

        # Mock cache
        self.original_cache_get = cache.get
        self.original_cache_set = cache.set
        cache.get = MagicMock(return_value={"task_return": {"processed": 0}})
        cache.set = MagicMock()

        # Sample insights to CRM field mapping
        self.mapping = {
            "summary_html": "tofu_summary",
            "value_prop_html": "tofu_value_proposition",
        }

        # Mock task ID
        self.task_id = "test_task_id"

        # Sample target insights data
        self.sample_insights_data = {
            "target_name": "Test Target",
            "target_id": "test123",
            "summary": "This is a summary",
            "value_prop": "This is a value proposition",
            "tofu_research": "Research data",
            "summary_html": "<p>This is a summary</p>",
            "value_prop_html": "<p>This is a value proposition</p>",
            "tofu_research_html": "<p>Research data</p>",
        }

        # Set up logging mock
        self.original_exception = logging.exception
        self.original_error = logging.error
        self.original_warning = logging.warning

        logging.exception = MagicMock()
        logging.error = MagicMock()
        logging.warning = MagicMock()

    def tearDown(self):
        """Restore original functions"""
        logging.exception = self.original_exception
        logging.error = self.original_error
        logging.warning = self.original_warning
        cache.get = self.original_cache_get
        cache.set = self.original_cache_set

    @patch("api.playbook.PlaybookHandler")
    @patch("api.models.TargetInfo.objects.filter")
    @patch(
        "api.playbook.PlaybookHandler.format_insight_as_html",
        return_value="<p>Mocked HTML</p>",
    )
    def test_hubspot_export_tofu_insights_success(
        self, mock_format_insight, mock_filter, mock_playbook_handler
    ):
        """Test successful export to HubSpot"""
        # Set up mocks
        mock_hubspot_agent = MagicMock()

        # Create mock targets
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.meta = {"hubspot_record_id": "hubspot1"}
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.meta = {"hubspot_record_id": "hubspot2"}
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"

        # Mock the queryset returned by filter
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = iter([mock_target1, mock_target2])
        mock_queryset.count.return_value = 2
        mock_filter.return_value = mock_queryset

        # Configure playbook handler
        mock_handler_instance = mock_playbook_handler.return_value
        mock_handler_instance.get_target_insights_data.return_value = (
            self.sample_insights_data
        )

        # Create and mock syncer with patch for the read-only properties
        with (
            patch.object(
                PlaybookHubspotSyncer,
                "record_type",
                new_callable=PropertyMock,
                return_value="contact",
            ),
            patch.object(
                PlaybookHubspotSyncer,
                "record_type_plural",
                new_callable=PropertyMock,
                return_value="contacts",
            ),
        ):

            syncer = PlaybookHubspotSyncer(
                self.mock_playbook, self.mock_target_info_group, [], None
            )
            syncer.hubspot_agent = mock_hubspot_agent

            # Call the method
            result = syncer.export_tofu_insights(self.task_id, self.mapping)

            # Assertions
            self.assertEqual(result, {"total": 2, "processed": 2})

            # Check that create_crm_field was called for each field in mapping
            calls = mock_hubspot_agent.create_crm_field.call_args_list
            self.assertEqual(len(calls), 2)

            # Check that update_crm_property was called for each target
            self.assertEqual(mock_hubspot_agent.update_crm_property.call_count, 2)

            # Check cache.set was called
            self.assertTrue(cache.set.called)
            # For the final call, check processed count
            last_call = cache.set.call_args_list[-1]
            self.assertEqual(last_call[0][1]["task_return"]["processed"], 2)

    @patch("api.playbook.PlaybookHandler")
    @patch("api.models.TargetInfo.objects.filter")
    @patch(
        "api.playbook.PlaybookHandler.format_insight_as_html",
        return_value="<p>Mocked HTML</p>",
    )
    def test_hubspot_export_tofu_insights_missing_hubspot_id(
        self, mock_format_insight, mock_filter, mock_playbook_handler
    ):
        """Test HubSpot export when a target is missing hubspot_record_id"""
        # Set up mocks
        mock_hubspot_agent = MagicMock()

        # Create mock targets (one with hubspot ID, one without)
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.meta = {"hubspot_record_id": "hubspot1"}
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.meta = {}  # Missing hubspot_record_id
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"

        # Mock the queryset returned by filter
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = iter([mock_target1, mock_target2])
        mock_queryset.count.return_value = 2
        mock_filter.return_value = mock_queryset

        # Configure playbook handler
        mock_handler_instance = mock_playbook_handler.return_value
        mock_handler_instance.get_target_insights_data.return_value = (
            self.sample_insights_data
        )

        # Create and mock syncer with patch for the read-only properties
        with (
            patch.object(
                PlaybookHubspotSyncer,
                "record_type",
                new_callable=PropertyMock,
                return_value="contact",
            ),
            patch.object(
                PlaybookHubspotSyncer,
                "record_type_plural",
                new_callable=PropertyMock,
                return_value="contacts",
            ),
        ):

            syncer = PlaybookHubspotSyncer(
                self.mock_playbook, self.mock_target_info_group, [], None
            )
            syncer.hubspot_agent = mock_hubspot_agent

            # Call the method
            result = syncer.export_tofu_insights(self.task_id, self.mapping)

            # Assertions
            self.assertEqual(result, {"total": 2, "processed": 1})

            # Check that update_crm_property was called only once (for the target with hubspot ID)
            self.assertEqual(mock_hubspot_agent.update_crm_property.call_count, 1)

            # Check that a warning was logged
            logging.warning.assert_called_once()

    @patch("api.playbook.PlaybookHandler")
    @patch("api.models.TargetInfo.objects.filter")
    @patch(
        "api.playbook.PlaybookHandler.format_insight_as_html",
        return_value="<p>Mocked HTML</p>",
    )
    def test_hubspot_export_tofu_insights_no_integration(
        self, mock_format_insight, mock_filter, mock_playbook_handler
    ):
        """Test HubSpot export when integration is not available"""
        syncer = PlaybookHubspotSyncer(
            self.mock_playbook, self.mock_target_info_group, [], None
        )
        syncer.hubspot_agent = None

        # Call the method and expect ValueError
        with self.assertRaises(ValueError):
            syncer.export_tofu_insights(self.task_id, self.mapping)

        # Check that an error was logged
        logging.error.assert_called_once()

    @patch("api.playbook.PlaybookHandler")
    @patch("api.models.TargetInfo.objects.filter")
    @patch(
        "api.playbook.PlaybookHandler.format_insight_as_html",
        return_value="<p>Mocked HTML</p>",
    )
    def test_salesforce_export_tofu_insights_success(
        self, mock_format_insight, mock_filter, mock_playbook_handler
    ):
        """Test successful export to Salesforce"""
        # Set up mocks
        mock_salesforce_agent = MagicMock()

        # Create mock targets
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.meta = {"salesforce": {"Id": "sf1", "object_type": "Contact"}}
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.meta = {"salesforce": {"Id": "sf2", "object_type": "Contact"}}
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"

        # Mock the queryset returned by filter
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = iter([mock_target1, mock_target2])
        mock_queryset.count.return_value = 2
        mock_filter.return_value = mock_queryset

        # Configure playbook handler
        mock_handler_instance = mock_playbook_handler.return_value
        mock_handler_instance.get_target_insights_data.return_value = (
            self.sample_insights_data
        )

        # Create and mock syncer with patch for the read-only properties
        with (
            patch.object(
                PlaybookSalesforceSyncer,
                "record_type",
                new_callable=PropertyMock,
                return_value="Contact",
            ),
            patch.object(
                PlaybookSalesforceSyncer,
                "record_type_plural",
                new_callable=PropertyMock,
                return_value="Contacts",
            ),
        ):

            syncer = PlaybookSalesforceSyncer(
                self.mock_playbook, self.mock_target_info_group, [], None
            )
            syncer.salesforce_agent = mock_salesforce_agent

            # Call the method
            result = syncer.export_tofu_insights(self.task_id, self.mapping.copy())

            # Assertions
            self.assertEqual(result, {"total": 2, "processed": 2})

            # Check that create_salesforce_html_fields was called
            mock_salesforce_agent.create_salesforce_html_fields.assert_called_once()

            # Make sure field names were updated with __c suffix
            sf_fields = mock_salesforce_agent.create_salesforce_html_fields.call_args[
                1
            ]["field_names"]
            self.assertTrue(all(field.endswith("__c") for field in sf_fields))

            # Check that update_object_fields was called for each target
            self.assertEqual(mock_salesforce_agent.update_object_fields.call_count, 2)

            # Check cache.set was called
            self.assertTrue(cache.set.called)
            # For the final call, check processed count
            last_call = cache.set.call_args_list[-1]
            self.assertEqual(last_call[0][1]["task_return"]["processed"], 2)

    @patch("api.playbook.PlaybookHandler")
    @patch("api.models.TargetInfo.objects.filter")
    @patch(
        "api.playbook.PlaybookHandler.format_insight_as_html",
        return_value="<p>Mocked HTML</p>",
    )
    def test_salesforce_export_tofu_insights_missing_salesforce_id(
        self, mock_format_insight, mock_filter, mock_playbook_handler
    ):
        """Test Salesforce export when a target is missing salesforce ID"""
        # Set up mocks
        mock_salesforce_agent = MagicMock()

        # Create mock targets (one with salesforce ID, one without)
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.meta = {"salesforce": {"Id": "sf1", "object_type": "Contact"}}
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.meta = {}  # Missing salesforce ID
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"

        # Mock the queryset returned by filter
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = iter([mock_target1, mock_target2])
        mock_queryset.count.return_value = 2
        mock_filter.return_value = mock_queryset

        # Configure playbook handler
        mock_handler_instance = mock_playbook_handler.return_value
        mock_handler_instance.get_target_insights_data.return_value = (
            self.sample_insights_data
        )

        # Create and mock syncer with patch for the read-only properties
        with (
            patch.object(
                PlaybookSalesforceSyncer,
                "record_type",
                new_callable=PropertyMock,
                return_value="Contact",
            ),
            patch.object(
                PlaybookSalesforceSyncer,
                "record_type_plural",
                new_callable=PropertyMock,
                return_value="Contacts",
            ),
        ):

            syncer = PlaybookSalesforceSyncer(
                self.mock_playbook, self.mock_target_info_group, [], None
            )
            syncer.salesforce_agent = mock_salesforce_agent

            # Call the method
            result = syncer.export_tofu_insights(self.task_id, self.mapping.copy())

            # Assertions
            self.assertEqual(result, {"total": 2, "processed": 1})

            # Check that update_object_fields was called only once (for the target with SF ID)
            self.assertEqual(mock_salesforce_agent.update_object_fields.call_count, 1)

            # Check that a warning was logged
            logging.warning.assert_called_once()

    @patch(
        "api.sync.e2e.salesforce_sync.ParagonWrapper.get_salesforce_agent",
        return_value=None,
    )
    def test_salesforce_export_tofu_insights_no_integration(self, mock_get_agent):
        """Test Salesforce export when integration is not available"""
        # Create syncer without a salesforce agent initially but with _init_integrations mocked
        syncer = PlaybookSalesforceSyncer(
            self.mock_playbook, self.mock_target_info_group, [], None
        )
        # After _init_integrations is called, salesforce_agent is still None
        syncer.salesforce_agent = None

        # Call the method and expect ValueError
        with self.assertRaises(ValueError):
            syncer.export_tofu_insights(self.task_id, self.mapping)

        # Check that an error was logged
        logging.error.assert_called_once()

    @patch("api.playbook.PlaybookHandler.get_target_insights_data")
    @patch("api.playbook.PlaybookHandler._get_target_doc_fields_data")
    @patch("api.playbook.PlaybookHandler._generate_insight_csv_header")
    @patch("api.playbook.check_file_exists")
    @patch("api.playbook.upload_file")
    @patch("api.playbook.create_presigned_url")
    @patch("api.models.TargetInfo.objects.filter")
    @patch("api.models.TargetInfoGroup.objects.filter")
    @patch("tempfile.NamedTemporaryFile")
    def test_get_insight_csv_url_basic(
        self,
        mock_temp_file,
        mock_target_group_filter,
        mock_target_filter,
        mock_create_url,
        mock_upload,
        mock_check_exists,
        mock_generate_header,
        mock_get_doc_fields,
        mock_get_insights,
    ):
        """Test basic CSV generation with standard insights data"""
        # Import the actual PlaybookHandler class
        from ..playbook import PlaybookHandler

        # Set up mocks
        mock_playbook = MagicMock()
        mock_playbook.id = "test_playbook_id"
        playbook_handler = PlaybookHandler(mock_playbook)

        # Mock target group
        mock_target_group = MagicMock()
        mock_target_group_filter.return_value.first.return_value = mock_target_group

        # Mock targets with basic insights data, no docs data
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.target_key = "Target 1"
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"
        mock_target1.additional_info = {
            "tofu_research": {"1": {"result": "Research 1", "updated_at": "2025-04-01"}}
        }
        mock_target1.docs = {}

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.target_key = "Target 2"
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"
        mock_target2.additional_info = {
            "tofu_research": {"1": {"result": "Research 2", "updated_at": "2025-04-02"}}
        }
        mock_target2.docs = {}

        # Mock the queryset returned by filter
        mock_target_filter.return_value = [mock_target1, mock_target2]

        # Mock insights data
        mock_get_insights.side_effect = [
            {
                "target_name": "Target 1",
                "target_id": "target1",
                "summary": "Summary 1",
                "value_prop": "Value Prop 1",
                "tofu_research": "Research 1",
                "summary_html": "<p>Summary 1</p>",
                "value_prop_html": "<p>Value Prop 1</p>",
                "tofu_research_html": "<p>Research 1</p>",
                "ordered_tofu_research": ["Research 1"],
                "ordered_tofu_research_html": ["<p>Research 1</p>"],
            },
            {
                "target_name": "Target 2",
                "target_id": "target2",
                "summary": "Summary 2",
                "value_prop": "Value Prop 2",
                "tofu_research": "Research 2",
                "summary_html": "<p>Summary 2</p>",
                "value_prop_html": "<p>Value Prop 2</p>",
                "tofu_research_html": "<p>Research 2</p>",
                "ordered_tofu_research": ["Research 2"],
                "ordered_tofu_research_html": ["<p>Research 2</p>"],
            },
        ]

        # Mock doc fields data
        mock_get_doc_fields.return_value = {}

        # Mock header generation
        mock_generate_header.return_value = [
            "Target Name",
            "Target ID",
            "Summary",
            "Value Props",
            "Tofu Research 1/1 - Latest",
            "Summary (HTML)",
            "Value Props (HTML)",
            "Tofu Research 1/1 - Latest (HTML)",
        ]

        # Mock file operations
        mock_file = MagicMock()
        mock_file.name = "/tmp/test.csv"
        mock_temp_file.return_value.__enter__.return_value = mock_file
        mock_check_exists.return_value = False
        mock_create_url.return_value = "https://example.com/test.csv"

        # Call the method
        result = playbook_handler.get_insight_csv_url("test_group")

        # Assertions
        self.assertEqual(result, "https://example.com/test.csv")
        mock_upload.assert_called_once()
        mock_create_url.assert_called_once()

        # Verify the target group filter was called correctly
        mock_target_group_filter.assert_called_once_with(
            playbook=mock_playbook, target_info_group_key="test_group"
        )

        # Verify the target filter was called correctly
        mock_target_filter.assert_called_once_with(target_info_group=mock_target_group)

        # Verify insights data was retrieved for each target
        self.assertEqual(mock_get_insights.call_count, 2)

        # Verify doc fields data was retrieved for each target
        self.assertEqual(mock_get_doc_fields.call_count, 2)

    @patch("api.playbook.PlaybookHandler.get_target_insights_data")
    @patch("api.playbook.PlaybookHandler._get_target_doc_fields_data")
    @patch("api.playbook.PlaybookHandler._generate_insight_csv_header")
    @patch("api.playbook.check_file_exists")
    @patch("api.playbook.upload_file")
    @patch("api.playbook.create_presigned_url")
    @patch("api.models.TargetInfo.objects.filter")
    @patch("api.models.TargetInfoGroup.objects.filter")
    @patch("tempfile.NamedTemporaryFile")
    def test_get_insight_csv_url_with_docs_data(
        self,
        mock_temp_file,
        mock_target_group_filter,
        mock_target_filter,
        mock_create_url,
        mock_upload,
        mock_check_exists,
        mock_generate_header,
        mock_get_doc_fields,
        mock_get_insights,
    ):
        """Test CSV generation with different docs data fields"""
        # Import the actual PlaybookHandler class
        from ..playbook import PlaybookHandler

        # Set up mocks
        mock_playbook = MagicMock()
        mock_playbook.id = "test_playbook_id"
        playbook_handler = PlaybookHandler(mock_playbook)

        # Mock target group
        mock_target_group = MagicMock()
        mock_target_group_filter.return_value.first.return_value = mock_target_group

        # Mock targets with different docs data fields
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.target_key = "Target 1"
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"
        mock_target1.additional_info = {
            "tofu_research": {"1": {"result": "Research 1", "updated_at": "2025-04-01"}}
        }

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.target_key = "Target 2"
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"
        mock_target2.additional_info = {
            "tofu_research": {"1": {"result": "Research 2", "updated_at": "2025-04-02"}}
        }

        # Mock the queryset returned by filter
        mock_target_filter.return_value = [mock_target1, mock_target2]

        # Mock insights data
        mock_get_insights.side_effect = [
            {
                "target_name": "Target 1",
                "target_id": "target1",
                "summary": "Summary 1",
                "value_prop": "Value Prop 1",
                "tofu_research": "Research 1",
                "summary_html": "<p>Summary 1</p>",
                "value_prop_html": "<p>Value Prop 1</p>",
                "tofu_research_html": "<p>Research 1</p>",
                "ordered_tofu_research": ["Research 1"],
                "ordered_tofu_research_html": ["<p>Research 1</p>"],
            },
            {
                "target_name": "Target 2",
                "target_id": "target2",
                "summary": "Summary 2",
                "value_prop": "Value Prop 2",
                "tofu_research": "Research 2",
                "summary_html": "<p>Summary 2</p>",
                "value_prop_html": "<p>Value Prop 2</p>",
                "tofu_research_html": "<p>Research 2</p>",
                "ordered_tofu_research": ["Research 2"],
                "ordered_tofu_research_html": ["<p>Research 2</p>"],
            },
        ]

        # Mock doc fields data - different fields for each target
        mock_get_doc_fields.side_effect = [
            {  # Target 1 has email and phone
                "Email": "<EMAIL>",
                "Phone": "************",
            },
            {  # Target 2 has email and company
                "Email": "<EMAIL>",
                "Company": "Acme Inc",
            },
        ]

        # Mock header generation with doc fields
        mock_generate_header.return_value = [
            "Target Name",
            "Target ID",
            "Email",
            "Phone",
            "Company",
            "Summary",
            "Value Props",
            "Tofu Research 1/1 - Latest",
            "Summary (HTML)",
            "Value Props (HTML)",
            "Tofu Research 1/1 - Latest (HTML)",
        ]

        # Mock file operations
        mock_file = MagicMock()
        mock_file.name = "/tmp/test.csv"
        mock_temp_file.return_value.__enter__.return_value = mock_file
        mock_check_exists.return_value = False
        mock_create_url.return_value = "https://example.com/test.csv"

        # Call the method
        result = playbook_handler.get_insight_csv_url("test_group")

        # Assertions
        self.assertEqual(result, "https://example.com/test.csv")
        mock_upload.assert_called_once()
        mock_create_url.assert_called_once()

        # Verify the target group filter was called correctly
        mock_target_group_filter.assert_called_once_with(
            playbook=mock_playbook, target_info_group_key="test_group"
        )

        # Verify the target filter was called correctly
        mock_target_filter.assert_called_once_with(target_info_group=mock_target_group)

        # Verify insights data was retrieved for each target
        self.assertEqual(mock_get_insights.call_count, 2)

        # Verify doc fields data was retrieved for each target
        self.assertEqual(mock_get_doc_fields.call_count, 2)

        # Verify header generation was called with the correct parameters
        mock_generate_header.assert_called_once()
        # The first parameter should be max_tofu_results=1
        self.assertEqual(mock_generate_header.call_args[0][0], 1)
        # The second parameter should be the list of doc field names
        doc_field_names = mock_generate_header.call_args[0][1]
        self.assertIn("Email", doc_field_names)
        self.assertIn("Phone", doc_field_names)
        self.assertIn("Company", doc_field_names)

    @patch("api.playbook.PlaybookHandler.get_target_insights_data")
    @patch("api.playbook.PlaybookHandler._get_target_doc_fields_data")
    @patch("api.playbook.PlaybookHandler._generate_insight_csv_header")
    @patch("api.playbook.check_file_exists")
    @patch("api.playbook.upload_file")
    @patch("api.playbook.create_presigned_url")
    @patch("api.models.TargetInfo.objects.filter")
    @patch("api.models.TargetInfoGroup.objects.filter")
    @patch("tempfile.NamedTemporaryFile")
    def test_get_insight_csv_url_multiple_tofu_research(
        self,
        mock_temp_file,
        mock_target_group_filter,
        mock_target_filter,
        mock_create_url,
        mock_upload,
        mock_check_exists,
        mock_generate_header,
        mock_get_doc_fields,
        mock_get_insights,
    ):
        """Test CSV generation with multiple tofu research entries"""
        # Import the actual PlaybookHandler class
        from ..playbook import PlaybookHandler

        # Set up mocks
        mock_playbook = MagicMock()
        mock_playbook.id = "test_playbook_id"
        playbook_handler = PlaybookHandler(mock_playbook)

        # Mock target group
        mock_target_group = MagicMock()
        mock_target_group_filter.return_value.first.return_value = mock_target_group

        # Mock targets with different numbers of tofu research entries
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.target_key = "Target 1"
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"
        mock_target1.docs = {}

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.target_key = "Target 2"
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"
        mock_target2.docs = {}

        # Mock the queryset returned by filter
        mock_target_filter.return_value = [mock_target1, mock_target2]

        # Mock insights data with multiple research entries for target 1
        mock_get_insights.side_effect = [
            {
                "target_name": "Target 1",
                "target_id": "target1",
                "summary": "Summary 1",
                "value_prop": "Value Prop 1",
                "tofu_research": "Research 1-1",
                "summary_html": "<p>Summary 1</p>",
                "value_prop_html": "<p>Value Prop 1</p>",
                "tofu_research_html": "<p>Research 1-1</p>",
                "ordered_tofu_research": [
                    "Research 1-1",
                    "Research 1-2",
                    "Research 1-3",
                ],
                "ordered_tofu_research_html": [
                    "<p>Research 1-1</p>",
                    "<p>Research 1-2</p>",
                    "<p>Research 1-3</p>",
                ],
            },
            {
                "target_name": "Target 2",
                "target_id": "target2",
                "summary": "Summary 2",
                "value_prop": "Value Prop 2",
                "tofu_research": "Research 2-1",
                "summary_html": "<p>Summary 2</p>",
                "value_prop_html": "<p>Value Prop 2</p>",
                "tofu_research_html": "<p>Research 2-1</p>",
                "ordered_tofu_research": ["Research 2-1"],
                "ordered_tofu_research_html": ["<p>Research 2-1</p>"],
            },
        ]

        # Mock doc fields data
        mock_get_doc_fields.return_value = {}

        # Mock header generation with multiple research entries
        mock_generate_header.return_value = [
            "Target Name",
            "Target ID",
            "Summary",
            "Value Props",
            "Tofu Research 1/3 - Latest",
            "Tofu Research 2/3",
            "Tofu Research 3/3",
            "Summary (HTML)",
            "Value Props (HTML)",
            "Tofu Research 1/3 - Latest (HTML)",
            "Tofu Research 2/3 (HTML)",
            "Tofu Research 3/3 (HTML)",
        ]

        # Mock file operations
        mock_file = MagicMock()
        mock_file.name = "/tmp/test.csv"
        mock_temp_file.return_value.__enter__.return_value = mock_file
        mock_check_exists.return_value = False
        mock_create_url.return_value = "https://example.com/test.csv"

        # Call the method
        result = playbook_handler.get_insight_csv_url("test_group")

        # Assertions
        self.assertEqual(result, "https://example.com/test.csv")
        mock_upload.assert_called_once()
        mock_create_url.assert_called_once()

        # Verify the target group filter was called correctly
        mock_target_group_filter.assert_called_once_with(
            playbook=mock_playbook, target_info_group_key="test_group"
        )

        # Verify the target filter was called correctly
        mock_target_filter.assert_called_once_with(target_info_group=mock_target_group)

        # Verify insights data was retrieved for each target
        self.assertEqual(mock_get_insights.call_count, 2)

        # Verify doc fields data was retrieved for each target
        self.assertEqual(mock_get_doc_fields.call_count, 2)

        # Verify header generation was called with the correct parameters
        mock_generate_header.assert_called_once()
        # The first parameter should be max_tofu_results=3
        self.assertEqual(mock_generate_header.call_args[0][0], 3)

    @patch("api.playbook.PlaybookHandler.get_target_insights_data")
    @patch("api.playbook.PlaybookHandler._get_target_doc_fields_data")
    @patch("api.playbook.PlaybookHandler._get_target_integration_ids")
    @patch("api.playbook.PlaybookHandler._generate_insight_csv_header")
    @patch("api.playbook.check_file_exists")
    @patch("api.playbook.upload_file")
    @patch("api.playbook.create_presigned_url")
    @patch("api.models.TargetInfo.objects.filter")
    @patch("api.models.TargetInfoGroup.objects.filter")
    @patch("tempfile.NamedTemporaryFile")
    def test_get_insight_csv_url_with_integration_ids(
        self,
        mock_temp_file,
        mock_target_group_filter,
        mock_target_filter,
        mock_create_url,
        mock_upload,
        mock_check_exists,
        mock_generate_header,
        mock_get_integration_ids,
        mock_get_doc_fields,
        mock_get_insights,
    ):
        """Test CSV generation with integration IDs (Hubspot, Salesforce, Marketo)"""
        # Import the actual PlaybookHandler class
        from ..playbook import PlaybookHandler

        # Set up mocks
        mock_playbook = MagicMock()
        mock_playbook.id = "test_playbook_id"
        playbook_handler = PlaybookHandler(mock_playbook)

        # Mock target group
        mock_target_group = MagicMock()
        mock_target_group_filter.return_value.first.return_value = mock_target_group

        # Mock targets with different integration IDs
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.target_key = "Target 1"
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"
        mock_target1.additional_info = {
            "tofu_research": {"1": {"result": "Research 1", "updated_at": "2025-04-01"}}
        }
        mock_target1.meta = {
            "hubspot_record_id": "hubspot1",
            "salesforce": {"Id": "sf1"},
            "marketo": {"lead_id": "marketo1"},
        }

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.target_key = "Target 2"
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"
        mock_target2.additional_info = {
            "tofu_research": {"1": {"result": "Research 2", "updated_at": "2025-04-02"}}
        }
        mock_target2.meta = {
            "hubspot_record_id": "hubspot2",
            # No Salesforce ID
            "marketo": {"lead_id": "marketo2"},
        }

        # Mock the queryset returned by filter
        mock_target_filter.return_value = [mock_target1, mock_target2]

        # Mock insights data
        mock_get_insights.side_effect = [
            {
                "target_name": "Target 1",
                "target_id": "target1",
                "summary": "Summary 1",
                "value_prop": "Value Prop 1",
                "tofu_research": "Research 1",
                "summary_html": "<p>Summary 1</p>",
                "value_prop_html": "<p>Value Prop 1</p>",
                "tofu_research_html": "<p>Research 1</p>",
                "ordered_tofu_research": ["Research 1"],
                "ordered_tofu_research_html": ["<p>Research 1</p>"],
            },
            {
                "target_name": "Target 2",
                "target_id": "target2",
                "summary": "Summary 2",
                "value_prop": "Value Prop 2",
                "tofu_research": "Research 2",
                "summary_html": "<p>Summary 2</p>",
                "value_prop_html": "<p>Value Prop 2</p>",
                "tofu_research_html": "<p>Research 2</p>",
                "ordered_tofu_research": ["Research 2"],
                "ordered_tofu_research_html": ["<p>Research 2</p>"],
            },
        ]

        # Mock doc fields data
        mock_get_doc_fields.return_value = {}

        # Mock integration IDs
        mock_get_integration_ids.side_effect = [
            {
                "Hubspot ID": "hubspot1",
                "Salesforce ID": "sf1",
                "Marketo ID": "marketo1",
            },
            {"Hubspot ID": "hubspot2", "Salesforce ID": "", "Marketo ID": "marketo2"},
        ]

        # Mock header generation with integration ID fields
        mock_generate_header.return_value = [
            "Target Name",
            "Target ID",
            "Hubspot ID",
            "Salesforce ID",
            "Marketo ID",
            "Summary",
            "Value Props",
            "Tofu Research 1/1 - Latest",
            "Summary (HTML)",
            "Value Props (HTML)",
            "Tofu Research 1/1 - Latest (HTML)",
        ]

        # Mock file operations
        mock_file = MagicMock()
        mock_file.name = "/tmp/test.csv"
        mock_temp_file.return_value.__enter__.return_value = mock_file
        mock_check_exists.return_value = False
        mock_create_url.return_value = "https://example.com/test.csv"

        # Call the method
        result = playbook_handler.get_insight_csv_url("test_group")

        # Assertions
        self.assertEqual(result, "https://example.com/test.csv")
        mock_upload.assert_called_once()
        mock_create_url.assert_called_once()

        # Verify the target group filter was called correctly
        mock_target_group_filter.assert_called_once_with(
            playbook=mock_playbook, target_info_group_key="test_group"
        )

        # Verify the target filter was called correctly
        mock_target_filter.assert_called_once_with(target_info_group=mock_target_group)

        # Verify insights data was retrieved for each target
        self.assertEqual(mock_get_insights.call_count, 2)

        # Verify doc fields data was retrieved for each target
        self.assertEqual(mock_get_doc_fields.call_count, 2)

        # Verify integration IDs were retrieved for each target
        self.assertEqual(mock_get_integration_ids.call_count, 2)

        # Verify header generation was called with the correct parameters
        mock_generate_header.assert_called_once()
        # The third parameter should include integration ID field names
        integration_id_field_names = mock_generate_header.call_args[0][2]
        self.assertIn("Hubspot ID", integration_id_field_names)
        self.assertIn("Salesforce ID", integration_id_field_names)
        self.assertIn("Marketo ID", integration_id_field_names)

    @patch("api.playbook.PlaybookHandler.get_target_insights_data")
    @patch("api.playbook.PlaybookHandler._get_target_doc_fields_data")
    @patch("api.playbook.PlaybookHandler._get_target_integration_ids")
    @patch("api.playbook.PlaybookHandler._generate_insight_csv_header")
    @patch("api.playbook.check_file_exists")
    @patch("api.playbook.upload_file")
    @patch("api.playbook.create_presigned_url")
    @patch("api.models.TargetInfo.objects.filter")
    @patch("api.models.TargetInfoGroup.objects.filter")
    @patch("tempfile.NamedTemporaryFile")
    def test_get_insight_csv_url_with_missing_integration_ids(
        self,
        mock_temp_file,
        mock_target_group_filter,
        mock_target_filter,
        mock_create_url,
        mock_upload,
        mock_check_exists,
        mock_generate_header,
        mock_get_integration_ids,
        mock_get_doc_fields,
        mock_get_insights,
    ):
        """Test CSV generation with targets that have no integration IDs"""
        # Import the actual PlaybookHandler class
        from ..playbook import PlaybookHandler

        # Set up mocks
        mock_playbook = MagicMock()
        mock_playbook.id = "test_playbook_id"
        playbook_handler = PlaybookHandler(mock_playbook)

        # Mock target group
        mock_target_group = MagicMock()
        mock_target_group_filter.return_value.first.return_value = mock_target_group

        # Mock targets - one with integration IDs, one without
        mock_target1 = MagicMock()
        mock_target1.id = "target1"
        mock_target1.target_key = "Target 1"
        mock_target1.summary = "Summary 1"
        mock_target1.value_prop = "Value Prop 1"
        mock_target1.additional_info = {
            "tofu_research": {"1": {"result": "Research 1", "updated_at": "2025-04-01"}}
        }
        mock_target1.meta = {
            "hubspot_record_id": "hubspot1",
            "salesforce": {"Id": "sf1"},
            "marketo": {"lead_id": "marketo1"},
        }

        mock_target2 = MagicMock()
        mock_target2.id = "target2"
        mock_target2.target_key = "Target 2"
        mock_target2.summary = "Summary 2"
        mock_target2.value_prop = "Value Prop 2"
        mock_target2.additional_info = {
            "tofu_research": {"1": {"result": "Research 2", "updated_at": "2025-04-02"}}
        }
        mock_target2.meta = {}  # No integration IDs

        # Mock the queryset returned by filter
        mock_target_filter.return_value = [mock_target1, mock_target2]

        # Mock insights data
        mock_get_insights.side_effect = [
            {
                "target_name": "Target 1",
                "target_id": "target1",
                "summary": "Summary 1",
                "value_prop": "Value Prop 1",
                "tofu_research": "Research 1",
                "summary_html": "<p>Summary 1</p>",
                "value_prop_html": "<p>Value Prop 1</p>",
                "tofu_research_html": "<p>Research 1</p>",
                "ordered_tofu_research": ["Research 1"],
                "ordered_tofu_research_html": ["<p>Research 1</p>"],
            },
            {
                "target_name": "Target 2",
                "target_id": "target2",
                "summary": "Summary 2",
                "value_prop": "Value Prop 2",
                "tofu_research": "Research 2",
                "summary_html": "<p>Summary 2</p>",
                "value_prop_html": "<p>Value Prop 2</p>",
                "tofu_research_html": "<p>Research 2</p>",
                "ordered_tofu_research": ["Research 2"],
                "ordered_tofu_research_html": ["<p>Research 2</p>"],
            },
        ]

        # Mock doc fields data
        mock_get_doc_fields.return_value = {}

        # Mock integration IDs - one with IDs, one without
        mock_get_integration_ids.side_effect = [
            {
                "Hubspot ID": "hubspot1",
                "Salesforce ID": "sf1",
                "Marketo ID": "marketo1",
            },
            {},  # No integration IDs
        ]

        # Mock header generation with integration ID fields
        mock_generate_header.return_value = [
            "Target Name",
            "Target ID",
            "Hubspot ID",
            "Salesforce ID",
            "Marketo ID",
            "Summary",
            "Value Props",
            "Tofu Research 1/1 - Latest",
            "Summary (HTML)",
            "Value Props (HTML)",
            "Tofu Research 1/1 - Latest (HTML)",
        ]

        # Mock file operations
        mock_file = MagicMock()
        mock_file.name = "/tmp/test.csv"
        mock_temp_file.return_value.__enter__.return_value = mock_file
        mock_check_exists.return_value = False
        mock_create_url.return_value = "https://example.com/test.csv"

        # Call the method
        result = playbook_handler.get_insight_csv_url("test_group")

        # Assertions
        self.assertEqual(result, "https://example.com/test.csv")
        mock_upload.assert_called_once()
        mock_create_url.assert_called_once()

        # Verify integration IDs were retrieved for each target
        self.assertEqual(mock_get_integration_ids.call_count, 2)

        # Verify header generation was called with the correct parameters
        mock_generate_header.assert_called_once()
        # The third parameter should include integration ID field names
        integration_id_field_names = mock_generate_header.call_args[0][2]
        self.assertIn("Hubspot ID", integration_id_field_names)
        self.assertIn("Salesforce ID", integration_id_field_names)
        self.assertIn("Marketo ID", integration_id_field_names)

    @patch("api.models.TargetInfoGroup.objects.filter")
    def test_get_insight_csv_url_target_group_not_found(self, mock_target_group_filter):
        """Test CSV generation when target group is not found"""
        # Import the actual PlaybookHandler class
        from ..playbook import PlaybookHandler

        # Set up mocks
        mock_playbook = MagicMock()
        mock_playbook.id = "test_playbook_id"
        playbook_handler = PlaybookHandler(mock_playbook)

        # Mock target group not found
        mock_target_group_filter.return_value.first.return_value = None

        # Call the method
        result = playbook_handler.get_insight_csv_url("nonexistent_group")

        # Assertions
        self.assertIsNone(result)
        # Verify the target group filter was called correctly
        mock_target_group_filter.assert_called_once_with(
            playbook=mock_playbook, target_info_group_key="nonexistent_group"
        )
        # Verify error was logged
        logging.error.assert_called_once()

    def test_get_target_integration_ids(self):
        """Test the _get_target_integration_ids method with different meta data scenarios"""
        # Import the actual PlaybookHandler class
        from ..playbook import PlaybookHandler

        # Set up mock playbook and handler
        mock_playbook = MagicMock()
        playbook_handler = PlaybookHandler(mock_playbook)

        # Test case 1: Target with all integration IDs
        target_with_all_ids = MagicMock()
        target_with_all_ids.meta = {
            "hubspot_record_id": "hubspot1",
            "salesforce": {"Id": "sf1"},
            "marketo": {"lead_id": "marketo1"},
        }
        integration_ids = playbook_handler._get_target_integration_ids(
            target_with_all_ids
        )
        self.assertEqual(integration_ids["Hubspot ID"], "hubspot1")
        self.assertEqual(integration_ids["Salesforce ID"], "sf1")
        self.assertEqual(integration_ids["Marketo ID"], "marketo1")

        # Test case 2: Target with some integration IDs
        target_with_some_ids = MagicMock()
        target_with_some_ids.meta = {
            "hubspot_record_id": "hubspot2",
            # No Salesforce ID
            "marketo": {"lead_id": "marketo2"},
        }
        integration_ids = playbook_handler._get_target_integration_ids(
            target_with_some_ids
        )
        self.assertEqual(integration_ids["Hubspot ID"], "hubspot2")
        self.assertNotIn(
            "Salesforce ID", integration_ids
        )  # Empty string for missing ID
        self.assertEqual(integration_ids["Marketo ID"], "marketo2")

        # Test case 3: Target with no integration IDs
        target_with_no_ids = MagicMock()
        target_with_no_ids.meta = {}
        integration_ids = playbook_handler._get_target_integration_ids(
            target_with_no_ids
        )
        self.assertEqual(integration_ids, {})  # Empty dictionary

        # Test case 4: Target with None meta
        target_with_none_meta = MagicMock()
        target_with_none_meta.meta = None
        integration_ids = playbook_handler._get_target_integration_ids(
            target_with_none_meta
        )
        self.assertEqual(integration_ids, {})  # Empty dictionary

    @patch("api.playbook.PlaybookHandler")
    @patch("api.models.TargetInfo.objects.filter")
    @patch(
        "api.playbook.PlaybookHandler.format_insight_as_html",
        return_value="<p>Mocked HTML</p>",
    )
    def test_salesforce_export_tofu_insights_append_c_suffix(
        self, mock_format_insight, mock_filter, mock_playbook_handler
    ):
        """Test that Salesforce field names get __c suffix if not already present"""
        # Set up mocks
        mock_salesforce_agent = MagicMock()

        # Mock the queryset returned by filter with empty list
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = iter([])
        mock_queryset.count.return_value = 0
        mock_filter.return_value = mock_queryset

        # Configure playbook handler
        mock_handler_instance = mock_playbook_handler.return_value

        # Create and mock syncer with patch for the read-only properties
        with patch.object(
            PlaybookSalesforceSyncer,
            "record_type",
            new_callable=PropertyMock,
            return_value="Contact",
        ):
            syncer = PlaybookSalesforceSyncer(
                self.mock_playbook, self.mock_target_info_group, [], None
            )
            syncer.salesforce_agent = mock_salesforce_agent

            # Test with field names with and without __c suffix
            mapping = {
                "summary": "tofu_summary",
                "value_prop": "tofu_value_already_with__c",
            }

            # Call the method
            syncer.export_tofu_insights(self.task_id, mapping)

            # Get the updated field names from the call to create_salesforce_html_fields
            field_names = mock_salesforce_agent.create_salesforce_html_fields.call_args[
                1
            ]["field_names"]

            # Check that all field names have __c suffix
            self.assertTrue("tofu_summary__c" in field_names)
            self.assertTrue(
                "tofu_value_already_with__c" in field_names
            )  # This shouldn't get a duplicate __c
