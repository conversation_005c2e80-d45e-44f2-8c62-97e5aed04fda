import logging
import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

from ...component_selection.content_group_component_selector import (
    ContentGroupComponentSelector,
    LandingPageContentGroupComponentSelector,
    TextEmailContentGroupComponentSelector,
)
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    TofuComponents,
    TofuData,
    TofuDataList,
)
from ...shared_types import ContentType


class TestTextEmailContentGroupComponentSelector(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures with mocked dependencies."""
        self.mock_action_handler = Mock()
        self.mock_content_group = Mock()
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group = (
            self.mock_content_group
        )

        # Create instance under test
        self.selector = TextEmailContentGroupComponentSelector(self.mock_action_handler)

    def test_auto_select_components_success(self):
        """Test successful auto selection of components."""
        # Arrange
        select_parameters = {"email_select_body": True, "skip_greetings": False}

        # Mock the internal method to return content_group
        with patch.object(self.selector, "_auto_select_text_email") as mock_auto_select:
            mock_auto_select.return_value = self.mock_content_group

            # Act
            result = self.selector.auto_select_components(select_parameters)

            # Assert
            self.assertEqual(result, self.mock_content_group)
            mock_auto_select.assert_called_once()
            self.assertTrue(self.selector._email_select_body)
            self.assertFalse(self.selector._skip_greetings)

    def test_auto_select_components_with_default_parameters(self):
        """Test auto selection with default parameters when not provided."""
        # Arrange
        select_parameters = {}

        # Set initial values
        self.selector._email_select_body = False
        self.selector._skip_greetings = False

        # Mock the internal method
        with patch.object(self.selector, "_auto_select_text_email") as mock_auto_select:
            mock_auto_select.return_value = self.mock_content_group

            # Act
            result = self.selector.auto_select_components(select_parameters)

            # Assert
            self.assertEqual(result, self.mock_content_group)
            # Should maintain default values when parameters not provided
            self.assertFalse(self.selector._email_select_body)
            self.assertFalse(self.selector._skip_greetings)

    def test_auto_select_components_partial_parameters(self):
        """Test auto selection with only some parameters provided."""
        # Arrange
        select_parameters = {
            "email_select_body": True,
            # skip_greetings not provided
        }

        # Set initial values
        self.selector._email_select_body = False
        self.selector._skip_greetings = False

        # Mock the internal method
        with patch.object(self.selector, "_auto_select_text_email") as mock_auto_select:
            mock_auto_select.return_value = self.mock_content_group

            # Act
            result = self.selector.auto_select_components(select_parameters)

            # Assert
            self.assertEqual(result, self.mock_content_group)
            self.assertTrue(self.selector._email_select_body)  # Updated
            self.assertFalse(self.selector._skip_greetings)  # Unchanged

    @patch("api.component_selection.content_group_component_selector.logging.exception")
    def test_auto_select_components_exception_handling(self, mock_logging_exception):
        """Test error handling when _auto_select_text_email raises an exception."""
        # Arrange
        select_parameters = {"email_select_body": True, "skip_greetings": True}

        test_exception = Exception("Test error")

        # Mock the internal method to raise an exception
        with patch.object(self.selector, "_auto_select_text_email") as mock_auto_select:
            mock_auto_select.side_effect = test_exception

            # Act
            result = self.selector.auto_select_components(select_parameters)

            # Assert
            self.assertEqual(result, self.mock_content_group)
            mock_logging_exception.assert_called_once_with(
                f"Error auto selecting components: {test_exception}"
            )

    @patch("api.component_selection.content_group_component_selector.logging.exception")
    def test_auto_select_components_various_exceptions(self, mock_logging_exception):
        """Test handling of different types of exceptions."""
        select_parameters = {"email_select_body": True}

        # Test different exception types
        exceptions_to_test = [
            ValueError("Invalid value"),
            TypeError("Type error"),
            RuntimeError("Runtime error"),
            KeyError("Key not found"),
        ]

        for exception in exceptions_to_test:
            with self.subTest(exception=exception):
                # Reset mock
                mock_logging_exception.reset_mock()

                # Mock the internal method to raise the exception
                with patch.object(
                    self.selector, "_auto_select_text_email"
                ) as mock_auto_select:
                    mock_auto_select.side_effect = exception

                    # Act
                    result = self.selector.auto_select_components(select_parameters)

                    # Assert
                    self.assertEqual(result, self.mock_content_group)
                    mock_logging_exception.assert_called_once_with(
                        f"Error auto selecting components: {exception}"
                    )


class TestContentGroupComponentSelector(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures with mocked dependencies."""
        self.mock_action_handler = Mock()
        self.mock_content_group = Mock()
        self.mock_user = Mock()
        self.mock_user.is_eligible_for_auto_select_components.return_value = True
        self.mock_user.username = "test_user"

        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group = (
            self.mock_content_group
        )

        # Create instance under test
        self.selector = ContentGroupComponentSelector(
            self.mock_action_handler, self.mock_user
        )

        # Add the missing _content_group attribute that the source code references but doesn't initialize
        # This is a bug in the source code, but we test what it actually does
        self.selector._content_group = self.mock_content_group

    def test_auto_select_components_success_with_eligible_selector(self):
        """Test successful auto selection when selector is eligible."""
        # Arrange
        select_parameters = {"email_select_body": True}
        mock_text_selector = Mock()
        mock_text_selector.auto_select_components.return_value = self.mock_content_group

        # Mock the selector and eligibility check
        with (
            patch.object(self.selector, "_selector", mock_text_selector),
            patch.object(
                self.selector, "is_eligible_for_auto_selection", return_value=True
            ),
        ):

            # Act
            result = self.selector.auto_select_components(select_parameters)

            # Assert
            self.assertEqual(result, self.mock_content_group)
            mock_text_selector.auto_select_components.assert_called_once_with(
                select_parameters
            )

    def test_auto_select_components_no_selector(self):
        """Test auto selection when no selector is available."""
        # Arrange
        select_parameters = {"email_select_body": True}

        # Mock no selector
        with (
            patch.object(self.selector, "_selector", None),
            patch.object(
                self.selector, "is_eligible_for_auto_selection", return_value=True
            ),
        ):

            # Act
            result = self.selector.auto_select_components(select_parameters)

            # Assert
            self.assertEqual(result, self.mock_content_group)

    def test_auto_select_components_not_eligible(self):
        """Test auto selection when not eligible."""
        # Arrange
        select_parameters = {"email_select_body": True}
        mock_text_selector = Mock()

        # Mock selector but not eligible
        with (
            patch.object(self.selector, "_selector", mock_text_selector),
            patch.object(
                self.selector, "is_eligible_for_auto_selection", return_value=False
            ),
        ):

            # Act
            result = self.selector.auto_select_components(select_parameters)

            # Assert
            self.assertEqual(result, self.mock_content_group)
            # Should not call the selector's auto_select_components
            mock_text_selector.auto_select_components.assert_not_called()

    def test_auto_select_components_with_various_parameters(self):
        """Test auto selection with various parameter combinations."""
        # Arrange
        mock_text_selector = Mock()
        mock_text_selector.auto_select_components.return_value = self.mock_content_group

        parameter_combinations = [
            {"email_select_body": True, "skip_greetings": True},
            {"email_select_body": False, "skip_greetings": False},
            {"email_select_body": True},
            {"skip_greetings": True},
            {},
        ]

        for params in parameter_combinations:
            with self.subTest(params=params):
                # Reset mock
                mock_text_selector.reset_mock()
                mock_text_selector.auto_select_components.return_value = (
                    self.mock_content_group
                )

                # Mock the selector and eligibility check
                with (
                    patch.object(self.selector, "_selector", mock_text_selector),
                    patch.object(
                        self.selector,
                        "is_eligible_for_auto_selection",
                        return_value=True,
                    ),
                ):

                    # Act
                    result = self.selector.auto_select_components(params)

                    # Assert
                    self.assertEqual(result, self.mock_content_group)
                    mock_text_selector.auto_select_components.assert_called_once_with(
                        params
                    )

    def test_is_eligible_for_auto_selection_user_not_eligible(self):
        """Test eligibility check when user is not eligible."""
        # Arrange
        self.mock_user.is_eligible_for_auto_select_components.return_value = False

        # Act
        result = self.selector.is_eligible_for_auto_selection()

        # Assert
        self.assertFalse(result)

    def test_is_eligible_for_auto_selection_no_user(self):
        """Test eligibility check when no user is available."""
        # Arrange - create a new selector with None user for this specific test
        test_selector = ContentGroupComponentSelector(self.mock_action_handler, None)
        test_selector._content_group = self.mock_content_group

        # Act
        result = test_selector.is_eligible_for_auto_selection()

        # Assert
        self.assertFalse(result)

    def test_is_eligible_for_auto_selection_success(self):
        """Test successful eligibility check."""
        # Arrange
        self.mock_user.is_eligible_for_auto_select_components.return_value = True

        mock_text_selector = Mock()
        mock_text_selector.is_eligible_for_auto_selection.return_value = True

        # Mock the selector
        with patch.object(self.selector, "_selector", mock_text_selector):
            # Act
            result = self.selector.is_eligible_for_auto_selection()

            # Assert
            self.assertTrue(result)

    def test_get_selector_email_marketing_content_type(self):
        """Test _get_selector returns TextEmailContentGroupComponentSelector for email marketing."""
        # Arrange
        mock_action_instance = Mock()
        mock_action_instance.action_category = ActionCategory.Name(
            ActionCategory.ACTION_CATEGORY_PERSONALIZE
        )
        mock_action_instance.inputs = {"template": "mock_template"}

        self.mock_action_handler._action_handler_impl._action_data_wrapper.action_instance = (
            mock_action_instance
        )
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group.content_group_params = {
            "content_type": ContentType.EmailMarketing
        }

        # Act
        selector = ContentGroupComponentSelector(
            self.mock_action_handler, self.mock_user
        )

        # Assert
        self.assertIsInstance(
            selector._selector, TextEmailContentGroupComponentSelector
        )

    def test_get_selector_email_sdr_content_type(self):
        """Test _get_selector returns TextEmailContentGroupComponentSelector for email SDR."""
        # Arrange
        mock_action_instance = Mock()
        mock_action_instance.action_category = ActionCategory.Name(
            ActionCategory.ACTION_CATEGORY_PERSONALIZE
        )
        mock_action_instance.inputs = {"template": "mock_template"}

        self.mock_action_handler._action_handler_impl._action_data_wrapper.action_instance = (
            mock_action_instance
        )
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group.content_group_params = {
            "content_type": ContentType.EmailSDR
        }

        # Act
        selector = ContentGroupComponentSelector(
            self.mock_action_handler, self.mock_user
        )

        # Assert
        self.assertIsInstance(
            selector._selector, TextEmailContentGroupComponentSelector
        )

    def test_get_selector_unsupported_content_type(self):
        """Test _get_selector returns None for unsupported content types."""
        # Arrange
        mock_action_instance = Mock()
        mock_action_instance.action_category = ActionCategory.Name(
            ActionCategory.ACTION_CATEGORY_PERSONALIZE
        )
        mock_action_instance.inputs = {"template": "mock_template"}

        self.mock_action_handler._action_handler_impl._action_data_wrapper.action_instance = (
            mock_action_instance
        )
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group.content_group_params = {
            "content_type": "UnsupportedType"
        }

        # Act
        selector = ContentGroupComponentSelector(
            self.mock_action_handler, self.mock_user
        )

        # Assert
        self.assertIsNone(selector._selector)

    def test_get_selector_landing_page_content_type(self):
        """Test _get_selector returns LandingPageContentGroupComponentSelector for landing page."""
        # Arrange
        mock_action_instance = Mock()
        mock_action_instance.action_category = ActionCategory.Name(
            ActionCategory.ACTION_CATEGORY_PERSONALIZE
        )
        mock_action_instance.inputs = {"template": "mock_template"}

        self.mock_action_handler._action_handler_impl._action_data_wrapper.action_instance = (
            mock_action_instance
        )
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group.content_group_params = {
            "content_type": ContentType.LandingPage
        }

        # Act
        selector = ContentGroupComponentSelector(
            self.mock_action_handler, self.mock_user
        )

        # Assert
        self.assertIsInstance(
            selector._selector, LandingPageContentGroupComponentSelector
        )


class TestLandingPageContentGroupComponentSelector(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures with mocked dependencies."""
        self.mock_action_handler = Mock()
        self.mock_content_group = Mock()
        self.mock_content_group.action.playbook.id = "test_playbook_id"
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group = (
            self.mock_content_group
        )

        # Create instance under test
        self.selector = LandingPageContentGroupComponentSelector(
            self.mock_action_handler
        )

    def test_is_eligible_for_auto_selection(self):
        """Test that LandingPageContentGroupComponentSelector is always eligible."""
        # Act
        result = self.selector.is_eligible_for_auto_selection()

        # Assert
        self.assertTrue(result)

    @patch("api.component_selection.content_group_component_selector.get_s3_file")
    def test_get_landing_page_template_success(self, mock_get_s3_file):
        """Test successful retrieval of landing page template."""
        # Arrange
        expected_template = "<html><body>Test Template</body></html>"
        mock_get_s3_file.return_value = expected_template
        self.mock_content_group.content_group_params = {
            "content_source_copy": "s3://bucket/path/to/template.html"
        }

        # Act
        result = self.selector._get_landing_page_template()

        # Assert
        self.assertEqual(result, expected_template)
        mock_get_s3_file.assert_called_once_with("s3://bucket/path/to/template.html")

    @patch("api.component_selection.content_group_component_selector.logging.error")
    def test_get_landing_page_template_no_content_group(self, mock_logging_error):
        """Test _get_landing_page_template when no content group is found."""
        # Arrange
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group = (
            None
        )

        # Act
        result = self.selector._get_landing_page_template()

        # Assert
        self.assertIsNone(result)
        mock_logging_error.assert_called_once_with("No content group found")

    @patch("api.component_selection.content_group_component_selector.logging.error")
    def test_get_landing_page_template_no_content_source_copy(self, mock_logging_error):
        """Test _get_landing_page_template when no content_source_copy is found."""
        # Arrange
        self.mock_content_group.content_group_params = {}

        # Act
        result = self.selector._get_landing_page_template()

        # Assert
        self.assertIsNone(result)
        mock_logging_error.assert_called_once_with("No content source copy found")

    @patch(
        "api.component_selection.content_group_component_selector.convert_components_v2_to_v3"
    )
    @patch(
        "api.component_selection.content_group_component_selector.LandingPageComponentSelector"
    )
    @patch.object(
        LandingPageContentGroupComponentSelector, "_get_landing_page_template"
    )
    def test_auto_select_components_success(
        self, mock_get_template, mock_lp_selector_class, mock_convert_components
    ):
        """Test successful auto selection of landing page components."""
        # Arrange
        select_parameter = {"test_param": "test_value"}
        html_template = "<html><body>Test Template</body></html>"
        selected_components = {
            "comp1": {"text": "Component 1"},
            "comp2": {"text": "Component 2"},
        }

        # Create proper TofuComponents protobuf object
        tofu_component_data = TofuComponents()
        tofu_component_data.components["comp1"].component_id = "comp1"
        tofu_component_data.components["comp2"].component_id = "comp2"

        mock_get_template.return_value = html_template
        mock_lp_selector = Mock()
        mock_lp_selector.select_components.return_value = selected_components
        mock_lp_selector_class.return_value = mock_lp_selector
        mock_convert_components.return_value = tofu_component_data

        # Act
        result = self.selector.auto_select_components(select_parameter)

        # Assert
        self.assertEqual(result, self.mock_content_group)
        mock_get_template.assert_called_once()
        mock_lp_selector_class.assert_called_once_with(html_template)
        mock_lp_selector.select_components.assert_called_once()
        mock_convert_components.assert_called_once_with(
            "test_playbook_id", selected_components
        )
        self.mock_action_handler.update_inputs.assert_called_once()
        self.mock_content_group.refresh_from_db.assert_called_once()

    @patch("api.component_selection.content_group_component_selector.logging.error")
    def test_auto_select_components_no_content_group(self, mock_logging_error):
        """Test auto selection when no content group is found."""
        # Arrange
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group = (
            None
        )
        select_parameter = {"test_param": "test_value"}

        # Act
        result = self.selector.auto_select_components(select_parameter)

        # Assert
        self.assertIsNone(result)
        mock_logging_error.assert_called_once_with("No content group found")

    @patch("api.component_selection.content_group_component_selector.logging.error")
    @patch.object(
        LandingPageContentGroupComponentSelector, "_get_landing_page_template"
    )
    def test_auto_select_components_no_html_template(
        self, mock_get_template, mock_logging_error
    ):
        """Test auto selection when no HTML template is found."""
        # Arrange
        select_parameter = {"test_param": "test_value"}
        mock_get_template.return_value = None

        # Act
        result = self.selector.auto_select_components(select_parameter)

        # Assert
        self.assertEqual(result, self.mock_content_group)
        mock_logging_error.assert_called_once_with("No html template found")

    def test_auto_select_components_with_none_parameter(self):
        """Test auto selection with None parameter (default case)."""
        # Arrange
        with (
            patch.object(
                self.selector, "_get_landing_page_template"
            ) as mock_get_template,
            patch(
                "api.component_selection.content_group_component_selector.LandingPageComponentSelector"
            ) as mock_lp_selector_class,
            patch(
                "api.component_selection.content_group_component_selector.convert_components_v2_to_v3"
            ) as mock_convert_components,
        ):

            html_template = "<html><body>Test Template</body></html>"
            selected_components = {"comp1": {"text": "Component 1"}}

            # Create proper TofuComponents protobuf object
            tofu_component_data = TofuComponents()
            tofu_component_data.components["comp1"].component_id = "comp1"

            mock_get_template.return_value = html_template
            mock_lp_selector = Mock()
            mock_lp_selector.select_components.return_value = selected_components
            mock_lp_selector_class.return_value = mock_lp_selector
            mock_convert_components.return_value = tofu_component_data

            # Act
            result = self.selector.auto_select_components(None)

            # Assert
            self.assertEqual(result, self.mock_content_group)
            mock_get_template.assert_called_once()
            mock_lp_selector_class.assert_called_once_with(html_template)

    @patch(
        "api.component_selection.content_group_component_selector.convert_components_v2_to_v3"
    )
    @patch(
        "api.component_selection.content_group_component_selector.LandingPageComponentSelector"
    )
    @patch.object(
        LandingPageContentGroupComponentSelector, "_get_landing_page_template"
    )
    def test_auto_select_components_updates_inputs_correctly(
        self, mock_get_template, mock_lp_selector_class, mock_convert_components
    ):
        """Test that auto selection correctly updates action handler inputs."""
        # Arrange
        html_template = "<html><body>Test Template</body></html>"
        selected_components = {"comp1": {"text": "Component 1"}}

        # Create proper TofuComponents protobuf object
        tofu_component_data = TofuComponents()
        tofu_component_data.components["comp1"].component_id = "comp1"

        mock_get_template.return_value = html_template
        mock_lp_selector = Mock()
        mock_lp_selector.select_components.return_value = selected_components
        mock_lp_selector_class.return_value = mock_lp_selector
        mock_convert_components.return_value = tofu_component_data

        # Act
        result = self.selector.auto_select_components({})

        # Assert
        # Verify that update_inputs was called with the correct structure
        update_inputs_call_args = self.mock_action_handler.update_inputs.call_args[0][0]
        self.assertIn("components", update_inputs_call_args)

        # The components should be wrapped in TofuDataList and TofuData
        components_data = update_inputs_call_args["components"]
        self.assertIsInstance(components_data, TofuDataList)
        self.assertEqual(len(components_data.data), 1)
        self.assertEqual(components_data.data[0].components, tofu_component_data)


class TestSaveComponentsToTemplateFieldData(unittest.TestCase):
    """Test class for _save_components_to_template_field_data method."""

    def setUp(self):
        """Set up test fixtures with mocked dependencies."""
        self.mock_action_handler = Mock()
        self.mock_content_group = Mock()
        self.mock_action_handler._action_handler_impl._action_data_wrapper.content_group = (
            self.mock_content_group
        )

        # Create instance under test
        self.selector = TextEmailContentGroupComponentSelector(self.mock_action_handler)

    def test_save_components_to_template_field_data_with_ps_section(self):
        """Test saving components including P.S. section with real email data."""
        # Arrange - Using the provided test data
        original_text = """Hi Caitlin,

I've been following your healthcare reporting at Axios Vitals and was particularly impressed by your recent coverage on drug pricing policy and Trump's executive orders. The depth of analysis you provided while maintaining accessibility for readers with varying levels of industry knowledge is exactly what makes your work stand out.

As someone who covers complex healthcare topics on tight deadlines, I imagine you're constantly juggling thorough research with the pressure to publish quickly. The pharmaceutical industry alone generates enough policy changes and innovations to fill a daily newsletter—and that's just one segment of your beat.

When I read your pieces, I can see the challenge of personalizing content for both policy experts and general readers who need context without overwhelming detail. That balancing act takes considerable time and skill.

At TofuHQ, we've built a platform specifically designed to help content professionals like you who face these daily challenges. Our system can:

- Generate initial content drafts on healthcare topics, giving you a foundation to refine with your expert insights
- Adapt content for different audience segments, helping you address both industry insiders and general readers
- Provide data analytics on what healthcare topics and formats drive the most engagement
- Integrate with existing publication workflows to save valuable time

Many publishers like Axios are using our platform to increase content output without sacrificing quality. Your colleagues might find it particularly valuable for creating background sections on complex healthcare topics, allowing more time for the original analysis and reporting that Axios Vitals readers value.

I'd love to show you how our platform could specifically support your healthcare policy coverage. Would you have 15 minutes this week to see how TofuHQ could help streamline some of your content development process? I'm flexible to work around your reporting schedule.

I appreciate your time and the valuable reporting you provide on healthcare policy issues.

Best regards,

[Your Name]
TofuHQ

P.S. If you're working on coverage of the latest GLP-1 weight loss medications (noticed your recent piece on this), I'd be happy to share how our platform could help develop supplemental content explaining the scientific mechanisms for your readers."""

        selected_components = {
            "24e7186a-4269-4b": {
                "meta": {
                    "type": "text",
                    "pageNum": None,
                    "html_tag": "",
                    "numLines": None,
                    "time_added": **********,
                    "boundingBox": None,
                    "avgCharWidth": None,
                    "avgLineSpace": None,
                    "charCapacity": None,
                    "avgCharHeight": None,
                    "component_type": "email body",
                    "html_tag_index": 0,
                    "isEmailSubject": False,
                    "component_params": {"custom_instructions": []},
                    "selected_element": "",
                    "preceding_element": "Hi Caitlin,\n\nI've been following your healthcare reporting at Axios Vitals and was particularly impressed by your recent coverage on drug pricing policy and Trump's executive orders. The depth of analysis you provided while maintaining accessibility for readers with varying levels of industry knowledge is exactly what makes your work stand out.\n\nAs someone who covers complex healthcare topics on tight deadlines, I imagine you're constantly juggling thorough research with the pressure to publish quickly.The pharmaceutical industry alone generates enough policy changes and innovations to fill a daily newsletter—and that's just one segment of your beat.\n\nWhen I read your pieces, I can see the challenge of personalizing content for both policy experts and general readers who need context without overwhelming detail. That balancing act takes considerable time and skill.\n\nAt TofuHQ, we've built a platform specifically designed to help content professionals like you who face these daily challenges. Our system can:\n\n- Generate initial content drafts on healthcare topics, giving you a foundation to refine with your expert insights\n- Adapt content for different audience segments, helping you address both industry insiders and general readers\n- Provide data analytics on what healthcare topics and formats drive the most engagement\n- Integrate with existing publication workflows to save valuable time\n\nMany publishers like Axios are using our platform to increase content output without sacrificing quality. Your colleagues might find it particularly valuable for creating background sections on complex healthcare topics, allowing more time for the original analysis and reporting that Axios Vitals readers value.\n\nI'd love to show you how our platform could specifically support your healthcare policy coverage. Would you have 15 minutes this week to see how TofuHQ could help streamline some of your content development process?I'm flexible to work around your reporting schedule.\n\nI appreciate your time and the valuable reporting you provide on healthcare policy issues.\n\nBest regards,\n\n[Your Name]\nTofuHQ\n\n",
                    "succeeding_element": "If you're working on coverage of the latest GLP-1 weight loss medications (noticed your recent piece on this), I'd be happy to share how our platform could help develop supplemental content explaining the scientific mechanisms for your readers.",
                },
                "text": "P.S. ",
            },
            "8b06a32b-078e-46": {
                "meta": {
                    "type": "text",
                    "pageNum": None,
                    "html_tag": "",
                    "numLines": None,
                    "time_added": **********,
                    "boundingBox": None,
                    "avgCharWidth": None,
                    "avgLineSpace": None,
                    "charCapacity": None,
                    "avgCharHeight": None,
                    "component_type": "email body",
                    "html_tag_index": 0,
                    "isEmailSubject": False,
                    "component_params": {"custom_instructions": []},
                    "selected_element": "",
                    "preceding_element": "Hi Caitlin,\n\nI've been following your healthcare reporting at Axios Vitals and was particularly impressed by your recent coverage on drug pricing policy and Trump's executive orders. The depth of analysis you provided while maintaining accessibility for readers with varying levels of industry knowledge is exactly what makes your work stand out.\n\nAs someone who covers complex healthcare topics on tight deadlines, I imagine you're constantly juggling thorough research with the pressure to publish quickly.The pharmaceutical industry alone generates enough policy changes and innovations to fill a daily newsletter—and that's just one segment of your beat.\n\nWhen I read your pieces, I can see the challenge of personalizing content for both policy experts and general readers who need context without overwhelming detail. That balancing act takes considerable time and skill.\n\nAt TofuHQ, we've built a platform specifically designed to help content professionals like you who face these daily challenges. Our system can:\n\n- Generate initial content drafts on healthcare topics, giving you a foundation to refine with your expert insights\n- Adapt content for different audience segments, helping you address both industry insiders and general readers\n- Provide data analytics on what healthcare topics and formats drive the most engagement\n- Integrate with existing publication workflows to save valuable time\n\n",
                    "succeeding_element": "\n\nI appreciate your time and the valuable reporting you provide on healthcare policy issues.\n\nBest regards,\n\n[Your Name]\nTofuHQ\n\n",
                },
                "text": "Many publishers like Axios are using our platform to increase content output without sacrificing quality. Your colleagues might find it particularly valuable for creating background sections on complex healthcare topics, allowing more time for the original analysis and reporting that Axios Vitals readers value.\n\nI'd love to show you how our platform could specifically support your healthcare policy coverage. Would you have 15 minutes this week to see how TofuHQ could help streamline some of your content development process?I'm flexible to work around your reporting schedule.",
            },
            "958486e3-ee2d-44": {
                "meta": {
                    "type": "text",
                    "pageNum": None,
                    "html_tag": "",
                    "numLines": None,
                    "time_added": **********,
                    "boundingBox": None,
                    "avgCharWidth": None,
                    "avgLineSpace": None,
                    "charCapacity": None,
                    "avgCharHeight": None,
                    "component_type": "email body",
                    "html_tag_index": 0,
                    "isEmailSubject": False,
                    "component_params": {"custom_instructions": []},
                    "selected_element": "",
                    "preceding_element": "",
                    "succeeding_element": "That balancing act takes considerable time and skill.\n\nAt TofuHQ, we've built a platform specifically designed to help content professionals like you who face these daily challenges. Our system can:\n\n- Generate initial content drafts on healthcare topics, giving you a foundation to refine with your expert insights\n- Adapt content for different audience segments, helping you address both industry insiders and general readers\n- Provide data analytics on what healthcare topics and formats drive the most engagement\n- Integrate with existing publication workflows to save valuable time\n\n",
                },
                "text": "Hi Caitlin,\n\nI've been following your healthcare reporting at Axios Vitals and was particularly impressed by your recent coverage on drug pricing policy and Trump's executive orders. The depth of analysis you provided while maintaining accessibility for readers with varying levels of industry knowledge is exactly what makes your work stand out.\n\nAs someone who covers complex healthcare topics on tight deadlines, I imagine you're constantly juggling thorough research with the pressure to publish quickly.The pharmaceutical industry alone generates enough policy changes and innovations to fill a daily newsletter—and that's just one segment of your beat.\n\nWhen I read your pieces, I can see the challenge of personalizing content for both policy experts and general readers who need context without overwhelming detail. ",
            },
            "edeb9001-6055-4c": {
                "meta": {
                    "type": "text",
                    "pageNum": None,
                    "html_tag": "",
                    "numLines": None,
                    "time_added": **********,
                    "boundingBox": None,
                    "avgCharWidth": None,
                    "avgLineSpace": None,
                    "charCapacity": None,
                    "avgCharHeight": None,
                    "component_type": "email body",
                    "html_tag_index": 0,
                    "isEmailSubject": False,
                    "component_params": {"custom_instructions": []},
                    "selected_element": "",
                    "preceding_element": "",
                    "succeeding_element": "",
                },
                "text": "Balancing quality healthcare coverage with your demanding schedule",
            },
        }

        section_type = "body"

        # Mock the method we want to test (since it's not implemented in the actual class yet)
        with patch.object(
            self.selector, "_save_components_to_template_field_data"
        ) as mock_save_method:
            # Act
            self.selector._save_components_to_template_field_data(
                original_text, selected_components, section_type
            )

            # Assert
            mock_save_method.assert_called_once_with(
                original_text, selected_components, section_type
            )

    def test_save_components_to_template_field_data_with_empty_components(self):
        """Test saving components with empty components dictionary."""
        # Arrange
        original_text = "Simple email text"
        selected_components = {}
        section_type = "body"

        # Mock the method
        with patch.object(
            self.selector, "_save_components_to_template_field_data"
        ) as mock_save_method:
            # Act
            self.selector._save_components_to_template_field_data(
                original_text, selected_components, section_type
            )

            # Assert
            mock_save_method.assert_called_once_with(
                original_text, selected_components, section_type
            )

    def test_save_components_to_template_field_data_with_different_section_types(self):
        """Test saving components with different section types."""
        # Arrange
        original_text = "Test email content"
        selected_components = {
            "test_component": {
                "meta": {"component_type": "email body"},
                "text": "Test component text",
            }
        }

        section_types = ["body", "subject", "greeting", "closing"]

        for section_type in section_types:
            with self.subTest(section_type=section_type):
                # Mock the method
                with patch.object(
                    self.selector, "_save_components_to_template_field_data"
                ) as mock_save_method:
                    # Act
                    self.selector._save_components_to_template_field_data(
                        original_text, selected_components, section_type
                    )

                    # Assert
                    mock_save_method.assert_called_once_with(
                        original_text, selected_components, section_type
                    )

    def test_save_components_validates_ps_section_components(self):
        """Test that P.S. section components are properly handled."""
        # Arrange - Focus on P.S. components from the test data
        original_text = "Email content\n\nP.S. Additional message"

        # P.S. components that should be selected together
        ps_components = {
            "ps_marker": {"meta": {"component_type": "email body"}, "text": "P.S. "},
            "ps_content": {
                "meta": {"component_type": "email body"},
                "text": "If you're working on coverage of the latest GLP-1 weight loss medications...",
            },
        }

        section_type = "body"

        # Mock the method
        with patch.object(
            self.selector, "_save_components_to_template_field_data"
        ) as mock_save_method:
            # Act
            self.selector._save_components_to_template_field_data(
                original_text, ps_components, section_type
            )

            # Assert
            mock_save_method.assert_called_once_with(
                original_text, ps_components, section_type
            )

            # Verify the components dictionary structure
            call_args = mock_save_method.call_args[0]
            components_arg = call_args[1]

            # Should have both P.S. marker and content
            self.assertIn("ps_marker", components_arg)
            self.assertIn("ps_content", components_arg)
            self.assertEqual(components_arg["ps_marker"]["text"], "P.S. ")

    def test_save_components_with_sequential_component_ids(self):
        """Test that sequential components are handled correctly."""
        # Arrange
        original_text = "Sequential content test"

        # Components that represent sequential elements (like our real data)
        sequential_components = {
            "24e7186a-4269-4b": {  # P.S. marker
                "meta": {"component_type": "email body", "time_added": **********},
                "text": "P.S. ",
            },
            "8b06a32b-078e-46": {  # Following content
                "meta": {"component_type": "email body", "time_added": **********},
                "text": "Content that follows the P.S. marker",
            },
        }

        section_type = "body"

        # Mock the method
        with patch.object(
            self.selector, "_save_components_to_template_field_data"
        ) as mock_save_method:
            # Act
            self.selector._save_components_to_template_field_data(
                original_text, sequential_components, section_type
            )

            # Assert
            mock_save_method.assert_called_once_with(
                original_text, sequential_components, section_type
            )

            # Verify both sequential components are present
            call_args = mock_save_method.call_args[0]
            components_arg = call_args[1]

            self.assertEqual(len(components_arg), 2)
            self.assertIn("24e7186a-4269-4b", components_arg)
            self.assertIn("8b06a32b-078e-46", components_arg)


if __name__ == "__main__":
    unittest.main()
