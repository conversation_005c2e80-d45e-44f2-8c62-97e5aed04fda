import unittest

from ...component_selection.template_segmenter import (
    LandingPageTemplateSegmenter,
    TextTemplateSegmenter,
)


class TestTextTemplateSegmenter(unittest.TestCase):
    def test_text_splitting_and_joining(self):
        # Test text with multiple paragraphs and empty lines
        test_text = """First paragraph. Second sentence.

Third paragraph. Fourth sentence.

Fifth paragraph. Sixth sentence.
"""  # Note the newline at the end

        segmenter = TextTemplateSegmenter(test_text)
        segments = segmenter._segments

        # Verify segments structure - newlines are separate components
        expected_segments = [
            "First paragraph. ",
            "Second sentence.",
            "\n\n",
            "Third paragraph. ",
            "Fourth sentence.",
            "\n\n",
            "Fifth paragraph. ",
            "Sixth sentence.",
            "\n",
        ]
        self.assertEqual(segments, expected_segments)

        # Verify joining segments gives original text
        joined_text = "".join(segments)
        self.assertEqual(joined_text, test_text)

    def test_special_cases(self):
        # Test text with decimal numbers, abbreviations, and other special cases
        test_text = """The price is 3.5 dollars. <PERSON><PERSON> said so.

The version is 2.0. It's great.

<PERSON><PERSON> has a Ph.D. in CS."""

        segmenter = TextTemplateSegmenter(test_text)
        segments = segmenter._segments

        # Verify segments structure
        expected_segments = [
            "The price is 3.5 dollars. ",
            "Dr. ",
            "<PERSON> said so.",
            "\n\n",
            "The version is 2.0. ",
            "It's great.",
            "\n\n",
            "Mr. ",
            "<PERSON> has a Ph.D. in CS.",
        ]
        self.assertEqual(segments, expected_segments)

        # Verify joining segments gives original text
        joined_text = "".join(segments)
        self.assertEqual(joined_text, test_text)

    def test_adjacent_components_merging(self):
        # Test text with multiple paragraphs
        test_text = """First paragraph. Second sentence.

Third paragraph. Fourth sentence.

Fifth paragraph. Sixth sentence.
"""  # Note the newline at the end

        segmenter = TextTemplateSegmenter(test_text)

        # Select first and last paragraphs (including their newlines)
        selected_idxs = [0, 1, 2, 6, 7, 8]
        segment_data = segmenter.segments_data
        selected_ids = [
            id
            for id, segment_data in segment_data.items()
            if segment_data["idx"] in selected_idxs
        ]

        segment_id_0 = None
        segment_id_6 = None

        for id, segment_data in segment_data.items():
            if segment_data["idx"] == 0:
                segment_id_0 = id
            if segment_data["idx"] == 6:
                segment_id_6 = id

        # Create components
        components = segmenter.get_components(selected_ids)

        # Verify we got two components (one for each paragraph)
        self.assertEqual(len(components), 2)

        # Verify first component (first paragraph)
        first_component = components[segment_id_0]
        self.assertEqual(
            first_component["text"], "First paragraph. Second sentence.\n\n"
        )

        # Verify second component (last paragraph)
        second_component = components[segment_id_6]
        self.assertEqual(second_component["text"], "Fifth paragraph. Sixth sentence.\n")

        # Verify preceding and succeeding elements
        self.assertEqual(first_component["meta"]["preceding_element"], "")
        self.assertEqual(
            first_component["meta"]["succeeding_element"],
            "Third paragraph. Fourth sentence.\n\n",
        )

        self.assertEqual(
            second_component["meta"]["preceding_element"],
            "First paragraph. Second sentence.\n\nThird paragraph. Fourth sentence.\n\n",
        )
        self.assertEqual(second_component["meta"]["succeeding_element"], "")

    def test_basic_sentence_splitting(self):
        # Test text with basic sentence endings
        test_text = """Hello! This is a test.

This has numbers: 3.5, 2.0, and 1.0.

Email: <EMAIL>
URL: https://example.com

Multiple spaces:    and tabs:		here.

Special chars: !@#$%^&*()_+-=[]{}|;:'",.<>/?~

Last line with newline at end.
"""

        segmenter = TextTemplateSegmenter(test_text)
        segments = segmenter._segments

        # Verify no characters are lost
        joined_text = "".join(segments)
        self.assertEqual(
            joined_text,
            test_text,
            "Character mismatch between original and joined text.\n"
            f"Original length: {len(test_text)}\n"
            f"Joined length: {len(joined_text)}\n"
            f"Original: {repr(test_text)}\n"
            f"Joined: {repr(joined_text)}",
        )

        # Verify segmentation
        expected_segments = [
            "Hello! ",
            "This is a test.",
            "\n\n",
            "This has numbers: 3.5, 2.0, and 1.0.",
            "\n\n",
            "Email: <EMAIL>",
            "\n",
            "URL: https://example.com",
            "\n\n",
            "Multiple spaces:    and tabs:\t\there.",
            "\n\n",
            "Special chars: !@#$%^&*()_+-=[]{}|;:'\",.<>/?~",
            "\n\n",
            "Last line with newline at end.",
            "\n",
        ]
        self.assertEqual(
            segments,
            expected_segments,
            "Segmentation mismatch.\n"
            f"Expected {len(expected_segments)} segments, got {len(segments)}.\n"
            f"Expected: {repr(expected_segments)}\n"
            f"Got: {repr(segments)}",
        )

    # TODO: fix this later
    @unittest.skip("Complex sentence splitting not implemented yet")
    def test_complex_sentence_splitting(self):
        # Test text with complex sentence endings and quotes
        test_text = """Hello! This is a test... With ellipsis.

This has numbers: 3.5, 2.0, and 1.0.

Dr. Smith said: "This is a quote!" (with parentheses).

Email: <EMAIL>
URL: https://example.com

Multiple spaces:    and tabs:		here.

Special chars: !@#$%^&*()_+-=[]{}|;:'",.<>/?~

Last line with newline at end.
"""

        segmenter = TextTemplateSegmenter(test_text)
        segments = segmenter._segments

        # Verify no characters are lost
        joined_text = "".join(segments)
        self.assertEqual(
            joined_text,
            test_text,
            "Character mismatch between original and joined text.\n"
            f"Original length: {len(test_text)}\n"
            f"Joined length: {len(joined_text)}\n"
            f"Original: {repr(test_text)}\n"
            f"Joined: {repr(joined_text)}",
        )

        # Verify segmentation
        expected_segments = [
            "Hello! ",
            "This is a test... ",
            "With ellipsis.",
            "\n\n",
            "This has numbers: 3.5, 2.0, and 1.0.",
            "\n\n",
            "Dr. Smith said: ",
            '"This is a quote!" ',
            "(with parentheses).",
            "\n\n",
            "Email: <EMAIL>",
            "\n",
            "URL: https://example.com",
            "\n\n",
            "Multiple spaces:    and tabs:\t\there.",
            "\n\n",
            "Special chars: !@#$%^&*()_+-=[]{}|;:'\",.<>/?~",
            "\n\n",
            "Last line with newline at end.",
            "\n",
        ]
        self.assertEqual(
            segments,
            expected_segments,
            "Segmentation mismatch.\n"
            f"Expected {len(expected_segments)} segments, got {len(segments)}.\n"
            f"Expected: {repr(expected_segments)}\n"
            f"Got: {repr(segments)}",
        )

    def test_email_content_merging(self):
        # Test text with email content
        test_text = """Hi Sarah,

Your recent piece on the disparities in C-section rates for Black women was eye-opening. As an investigative health reporter, you're constantly juggling complex topics, time-consuming research, and the need to protect your sources.

At TofuHQ, we've developed tools to support journalists like you in managing these challenges. Our platform can help streamline your content organization, making it easier to manage sensitive information and cross-reference your extensive research.

We'd love to show you how our system can integrate with your current workflow, potentially saving you hours on data management and giving you more time for in-depth reporting. It could be particularly useful for your ongoing investigations into hospital billing practices and medical debt.

Would you be open to a quick demo? I'd be happy to walk you through how it could specifically benefit your work at the New York Times.

Best regards,
[Your Name]
"""

        segmenter = TextTemplateSegmenter(test_text)

        # Select specific segments (indices 2, 3, 5, 6, 8, 9, 11, 12)
        selected_idxs = [2, 3, 5, 6, 8, 9, 11, 12]
        segment_data = segmenter.segments_data
        selected_ids = [
            id
            for id, segment_data in segment_data.items()
            if segment_data["idx"] in selected_idxs
        ]

        # Create components
        components = segmenter.get_components(selected_ids)

        # Verify we got two components (merged by newlines)
        self.assertEqual(len(components), 1)

        # Verify first component (all paragraphs)
        first_component = list(components.values())[0]
        self.assertEqual(
            first_component["text"],
            """Your recent piece on the disparities in C-section rates for Black women was eye-opening. As an investigative health reporter, you're constantly juggling complex topics, time-consuming research, and the need to protect your sources.

At TofuHQ, we've developed tools to support journalists like you in managing these challenges. Our platform can help streamline your content organization, making it easier to manage sensitive information and cross-reference your extensive research.

We'd love to show you how our system can integrate with your current workflow, potentially saving you hours on data management and giving you more time for in-depth reporting. It could be particularly useful for your ongoing investigations into hospital billing practices and medical debt.

Would you be open to a quick demo? I'd be happy to walk you through how it could specifically benefit your work at the New York Times.""",
        )

        # Verify preceding and succeeding elements
        self.assertEqual(first_component["meta"]["preceding_element"], "Hi Sarah,\n\n")
        self.assertEqual(
            first_component["meta"]["succeeding_element"],
            "\n\nBest regards,\n[Your Name]\n",
        )


class TestLandingPageTemplateSegmenter(unittest.TestCase):
    def test_html_parsing_and_tofu_element_extraction(self):
        # Test HTML with tofu-element segments (simplified from real website)
        test_html = """<!DOCTYPE html>
<html>
<head>
    <title>Tofu - Ship Integrated Campaigns 8x Faster</title>
    <style>.tofu-element{transition:box-shadow .3s ease-in-out;border-radius:4px;position:relative}</style>
</head>
<body>
    <div class="banner">
        <div class="text-block-4 tofu-element tofu-editable-element" data-tofu-id="PBiY4X9WPjHYF20x">Tofu raises $12M to cut martech bloat for GTM teams ✨</div>
        <a href="/story" class="button tofu-editable-element" data-tofu-id="hy5AniNaQeQcKJLJ">Read our story</a>
    </div>
    
    <section class="hero">
        <h1 class="tofu-element tofu-editable-element" data-tofu-id="Gv1tlyR7o0ZZ5KuL">Ship Integrated Campaigns 8x Faster</h1>
        <p class="tofu-element tofu-editable-element" data-tofu-id="6j2nPqQp4WEa91I7">1:1 ABM, personalized nurture, outbound prospecting, webinars and events, upsell and cross-sell.</p>
        <a href="/trial" class="button tofu-editable-element" data-tofu-id="bLeXWG2FW15DYApp">Start Free Trial</a>
        <a href="/demo" class="button tofu-editable-element" data-tofu-id="x4GZmdCjq4jIrHI-">Book A Demo</a>
    </section>
    
    <section class="workflow">
        <h2 class="tofu-element tofu-editable-element" data-tofu-id="RITIkbdGJjdAsjy8">One platform for every marketing workflow</h2>
        <p class="tofu-element tofu-editable-element" data-tofu-id="V0FWxLFRkaNs06wx">Regardless of your Marketing function, Tofu can help you do more with less as you scale your output and improve performance.</p>
    </section>
    
    <footer>
        <div class="footer-nav-link">Platform</div>
        <p class="text-size-xsmall tofu-element tofu-editable-element" data-tofu-id="rdXPifI5a_FJYpGl">© 2025 Tofu Technologies, Inc.</p>
    </footer>
</body>
</html>"""

        segmenter = LandingPageTemplateSegmenter(test_html)

        # Test that tofu-elements are extracted
        segments_to_select = segmenter.segments_to_select
        self.assertEqual(
            len(segments_to_select), 6
        )  # Should find 6 tofu-element segments

        # Test segments_data contains the expected data
        segments_data = segmenter.segments_data
        self.assertEqual(len(segments_data), 6)

        # Verify specific segments
        banner_segment = segments_data["PBiY4X9WPjHYF20x"]
        self.assertEqual(
            banner_segment["text"],
            "Tofu raises $12M to cut martech bloat for GTM teams ✨",
        )
        self.assertEqual(banner_segment["tag_name"], "div")
        self.assertEqual(banner_segment["idx"], 0)

        hero_title_segment = segments_data["Gv1tlyR7o0ZZ5KuL"]
        self.assertEqual(
            hero_title_segment["text"], "Ship Integrated Campaigns 8x Faster"
        )
        self.assertEqual(hero_title_segment["tag_name"], "h1")
        self.assertEqual(hero_title_segment["idx"], 1)

        # Test that non-tofu elements are not included
        self.assertNotIn("Platform", [data["text"] for data in segments_data.values()])

    def test_get_components_format(self):
        # Simplified HTML for component testing
        test_html = """<html><body>
        <h1 class="tofu-element tofu-editable-element" data-tofu-id="test-id-1">Main Title</h1>
        <p class="tofu-element tofu-editable-element" data-tofu-id="test-id-2">Description text</p>
        </body></html>"""

        segmenter = LandingPageTemplateSegmenter(test_html)

        # Select components
        component_ids = ["test-id-1", "test-id-2"]
        components = segmenter.get_components(component_ids)

        # Verify component structure matches expected format
        self.assertEqual(len(components), 2)

        # Test first component (h1)
        h1_component = components["test-id-1"]
        self.assertEqual(h1_component["text"], "Main Title")

        # Verify meta structure matches expected format
        meta = h1_component["meta"]
        self.assertEqual(meta["type"], "text")
        self.assertEqual(meta["component_type"], "unspecified")
        self.assertEqual(meta["html_tag"], "<h1>")
        self.assertEqual(meta["html_tag_index"], 0)
        self.assertEqual(meta["isEmailSubject"], False)
        self.assertIn("tofu-element", meta["selected_element"])
        self.assertIn('data-tofu-id="test-id-1"', meta["selected_element"])

        # Verify all required null fields
        self.assertIsNone(meta["pageNum"])
        self.assertIsNone(meta["numLines"])
        self.assertIsNone(meta["boundingBox"])
        self.assertIsNone(meta["avgCharWidth"])
        self.assertIsNone(meta["avgLineSpace"])
        self.assertIsNone(meta["charCapacity"])
        self.assertIsNone(meta["avgCharHeight"])

        # Test second component (p)
        p_component = components["test-id-2"]
        self.assertEqual(p_component["text"], "Description text")
        self.assertEqual(p_component["meta"]["html_tag"], "<p>")
        self.assertEqual(p_component["meta"]["html_tag_index"], 1)

    def test_context_extraction(self):
        # Test HTML with context elements
        test_html = """<html><body>
        <h2>Previous Section</h2>
        <p>Some context before.</p>
        <div class="tofu-element tofu-editable-element" data-tofu-id="target-element">Target content</div>
        <p>Some context after.</p>
        <h3>Next Section</h3>
        </body></html>"""

        segmenter = LandingPageTemplateSegmenter(test_html)
        components = segmenter.get_components(["target-element"])

        target_component = components["target-element"]

        # Verify context extraction
        self.assertIn(
            "Some context before", target_component["meta"]["preceding_element"]
        )
        self.assertIn(
            "Some context after", target_component["meta"]["succeeding_element"]
        )

    def test_empty_html(self):
        # Test with empty HTML
        segmenter = LandingPageTemplateSegmenter("")
        self.assertEqual(len(segmenter.segments_to_select), 0)
        self.assertEqual(len(segmenter.segments_data), 0)

    def test_html_without_tofu_elements(self):
        # Test HTML without tofu-element class
        test_html = """<html><body>
        <h1>Regular title</h1>
        <p>Regular paragraph</p>
        </body></html>"""

        segmenter = LandingPageTemplateSegmenter(test_html)
        self.assertEqual(len(segmenter.segments_to_select), 0)
        self.assertEqual(len(segmenter.segments_data), 0)

    def test_get_all_segments_info(self):
        # Test segments info summary
        test_html = """<html><body>
        <h1 class="tofu-element tofu-editable-element" data-tofu-id="title-1">Short Title</h1>
        <p class="tofu-element tofu-editable-element" data-tofu-id="long-text">This is a very long paragraph that contains more than one hundred characters and should be truncated in the summary view for better display purposes.</p>
        </body></html>"""

        segmenter = LandingPageTemplateSegmenter(test_html)
        segments_info = segmenter.get_all_segments_info()

        # Verify structure
        self.assertEqual(len(segments_info), 2)

        # Test short text (no truncation)
        title_info = segments_info["title-1"]
        self.assertEqual(title_info["text"], "Short Title")
        self.assertEqual(title_info["tag_name"], "h1")
        self.assertEqual(title_info["component_type"], "heading")

        # Test long text (truncated)
        long_info = segments_info["long-text"]
        self.assertTrue(long_info["text"].endswith("..."))
        self.assertLess(len(long_info["text"]), 110)  # Should be truncated
        self.assertEqual(long_info["tag_name"], "p")
        self.assertEqual(long_info["component_type"], "paragraph")

    def test_component_id_generation(self):
        # Test HTML without data-tofu-id (should generate IDs)
        test_html = """<html><body>
        <h1 class="tofu-element tofu-editable-element">Title without ID</h1>
        <p class="tofu-element tofu-editable-element">Paragraph without ID</p>
        </body></html>"""

        segmenter = LandingPageTemplateSegmenter(test_html)
        segments_data = segmenter.segments_data

        # Should generate IDs for elements without data-tofu-id
        self.assertEqual(len(segments_data), 2)

        # All segment IDs should be strings of length 16 (UUID shortened)
        for segment_id in segments_data.keys():
            self.assertIsInstance(segment_id, str)
            self.assertEqual(len(segment_id), 16)

    def test_invalid_html_handling(self):
        # Test with malformed HTML
        test_html = """<html><body>
        <h1 class="tofu-element tofu-editable-element" data-tofu-id="test-1">Title
        <p class="tofu-element tofu-editable-element" data-tofu-id="test-2">Unclosed paragraph
        </body></html>"""

        # Should not raise exception
        segmenter = LandingPageTemplateSegmenter(test_html)
        self.assertGreaterEqual(
            len(segmenter.segments_data), 0
        )  # Should parse what it can
