import unittest
import uuid
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from typing import List
from unittest.mock import Mock, call, patch

from django.test import TestCase
from langchain_core.documents import Document

from ..connected_assets.connected_assets_refresher import (
    ConnectedAssetsGroupRefreshStatus,
    URLAssetsGroupRefresher,
    delete_duplicated_suggested_connected_sources,
    get_connected_asset_group_refresh_status,
    update_connected_asset_group_refresh_status,
)
from ..connected_assets.connected_assets_utils import AssetCreationResult
from ..models import AssetInfo, AssetInfoGroup, CompanyInfo, Playbook


class TestURLAssetsGroupRefresher(TestCase):
    def setUp(self):
        # Create test company and playbook
        self.company = CompanyInfo.objects.create()
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company
        )

        # Create test asset group
        self.asset_group = AssetInfoGroup.objects.create(
            playbook=self.playbook,
            asset_info_group_key="test_group",
            meta={
                "tofu_connected_assets_metadata": {
                    "category": "test_category",
                    "tofu_connected_source_type_info": {
                        "urls": ["https://example.com"],
                        "max_depth": 3,
                        "max_pages": 100,
                    },
                }
            },
        )

        # Create mocked metadata extractor
        self.mock_metadata_extractor = Mock()
        self.refresher = URLAssetsGroupRefresher(
            metadata_extractor=self.mock_metadata_extractor
        )
        # Add a mock document map for parallel tests
        self.mock_doc_map = {
            "https://example.com/valid1": Document(
                page_content="doc1", metadata={"source": "https://example.com/valid1"}
            ),
            "https://example.com/valid2": Document(
                page_content="doc2", metadata={"source": "https://example.com/valid2"}
            ),
            "https://example.com/mismatch": Document(
                page_content="doc3", metadata={"source": "https://example.com/mismatch"}
            ),
            "https://example.com/extract_fail": Document(
                page_content="doc4",
                metadata={"source": "https://example.com/extract_fail"},
            ),
        }

    def test_validate_metadata_valid(self):
        """Test metadata validation with valid data"""
        meta_data = {
            "category": "test_category",
            "tofu_connected_source_type_info": {"urls": ["https://example.com"]},
        }
        self.assertTrue(self.refresher.validate_metadata(meta_data))

    def test_validate_metadata_invalid_no_category(self):
        """Test metadata validation with missing category"""
        meta_data = {
            "tofu_connected_source_type_info": {"urls": ["https://example.com"]}
        }
        self.assertTrue(self.refresher.validate_metadata(meta_data))

    def test_validate_metadata_invalid_no_urls(self):
        """Test metadata validation with missing URLs"""
        meta_data = {"category": "test_category", "tofu_connected_source_type_info": {}}
        self.assertFalse(self.refresher.validate_metadata(meta_data))

    @patch("api.connected_assets.connected_assets_refresher.TofuWebPageLoader")
    def test_crawl_with_same_folder(self, mock_loader):
        """Test crawling with same folder structure"""
        # Mock the loader response
        mock_loader_instance = Mock()
        mock_loader_instance.load_deep.return_value = [
            Document(
                page_content="Test content",
                metadata={"source": "https://example.com/page1"},
            )
        ]
        mock_loader.return_value = mock_loader_instance

        refresher = URLAssetsGroupRefresher(
            metadata_extractor=self.mock_metadata_extractor
        )
        docs = refresher._crawl_with_same_folder(
            "https://example.com", max_depth=3, max_pages=100
        )

        # Verify loader was called with correct parameters
        mock_loader.assert_called_once_with(
            "https://example.com",
            deep_crawl=True,
            crawler_max_depth=3,
            crawler_max_page=100,
        )
        mock_loader_instance.load_deep.assert_called_once_with(
            deep_crawl_only_same_folder=True
        )

        # Verify returned documents
        self.assertEqual(len(docs), 1)
        self.assertEqual(docs[0].page_content, "Test content")
        self.assertEqual(docs[0].metadata["source"], "https://example.com/page1")

    @patch("api.connected_assets.connected_assets_refresher.TofuWebPageLoader")
    def test_crawl_with_llm_verification(self, mock_loader):
        """Test crawling with LLM verification"""
        # Mock the loader response
        mock_loader_instance = Mock()
        mock_loader_instance.load_deep.return_value = [
            Document(
                page_content="Test content 1",
                metadata={"source": "https://example.com/page1"},
            ),
            Document(
                page_content="Test content 2",
                metadata={"source": "https://example.com/page2"},
            ),
        ]
        mock_loader.return_value = mock_loader_instance

        # Mock the extractor response
        self.mock_metadata_extractor.verify_urls_relevance.return_value = []
        refresher = URLAssetsGroupRefresher(
            metadata_extractor=self.mock_metadata_extractor
        )
        docs = refresher._crawl_with_llm_verification(
            "https://example.com", max_depth=3, max_pages=100, category="test_category"
        )

        # Verify loader was called with correct parameters
        mock_loader.assert_called_once_with(
            "https://example.com",
            deep_crawl=True,
            crawler_max_depth=3,
            crawler_max_page=100,
        )
        mock_loader_instance.load_deep.assert_called_once_with(
            deep_crawl_only_same_folder=False, always_crawl_next_page=True
        )

        # Verify extractor was called with correct parameters
        self.mock_metadata_extractor.verify_urls_relevance.assert_called_once_with(
            listing_page_url="https://example.com",
            urls=["example.com/page1", "example.com/page2"],
            category="test_category",
        )

        # Verify no documents were returned when no URLs were relevant
        assert len(docs) == 0

    @patch("api.connected_assets.connected_assets_refresher.TofuWebPageLoader")
    def test_crawl_urls_for_asset_group_with_llm_verification(self, mock_loader):
        """Test crawling URLs for asset group with LLM verification"""
        # Mock the loader response
        mock_loader_instance = Mock()
        mock_loader_instance.load_deep.side_effect = [
            [
                Document(
                    page_content="Test content 2",
                    metadata={"source": "https://example.com/page2"},
                ),
                Document(
                    page_content="Test content 3",
                    metadata={"source": "https://example.com/page3"},
                ),
            ],
        ]
        mock_loader.return_value = mock_loader_instance

        # Set up the mock metadata extractor response
        self.mock_metadata_extractor.verify_urls_relevance.return_value = [
            "example.com/page2",
            "example.com/page3",
        ]

        refresher = URLAssetsGroupRefresher(
            metadata_extractor=self.mock_metadata_extractor
        )
        docs = refresher._crawl_urls_for_asset_group(
            ["https://example.com"],  # Pass URL as a list
            max_depth=3,
            max_pages=100,
            category="test_category",
            asset_group_id="test_group",
        )

        # Verify loader was called once with correct parameters
        assert mock_loader.call_count == 1
        mock_loader.assert_has_calls(
            [
                call(
                    "https://example.com",
                    deep_crawl=True,
                    crawler_max_depth=3,
                    crawler_max_page=100,
                ),
                call().load_deep(
                    deep_crawl_only_same_folder=False, always_crawl_next_page=True
                ),
            ],
            any_order=False,
        )

        # Verify extractor was called with correct parameters
        self.mock_metadata_extractor.verify_urls_relevance.assert_called_once_with(
            listing_page_url="https://example.com",
            urls=["example.com/page2", "example.com/page3"],
            category="test_category",
        )

        # Verify we got all three documents
        assert len(docs) == 2
        assert all(
            doc.metadata["source"]
            in [
                "https://example.com/page1",
                "https://example.com/page2",
                "https://example.com/page3",
            ]
            for doc in docs
        )

    @patch("api.connected_assets.connected_assets_refresher.TofuWebPageLoader")
    @patch(
        "api.connected_assets.connected_assets_refresher.URLAssetsGroupRefresher._create_assets_in_parallel"
    )
    @patch("api.connected_assets.connected_assets_refresher.parallel_build_docs")
    @patch("api.connected_assets.connected_assets_refresher.verify_url_exists")
    @patch(
        "api.connected_assets.connected_assets_refresher.update_connected_asset_group_refresh_status"
    )
    @patch(
        "api.connected_assets.connected_assets_refresher.get_connected_asset_group_refresh_status"
    )
    def test_refresh_assets(
        self,
        mock_get_status,
        mock_update_status,
        mock_verify_url,
        mock_parallel_build_docs,
        mock_create_parallel,
        mock_loader,
    ):
        """Test the refresh_assets method for URL management and asset lifecycle."""
        # Mock initial status
        mock_get_status.return_value = (
            ConnectedAssetsGroupRefreshStatus.NOT_STARTED.value
        )

        # Mock the loader response
        mock_loader_instance = Mock()
        # Return page1, page2 (relevant), page3 (excluded), page_deleted (existing, but not in crawl)
        mock_loader_instance.load_deep.return_value = [
            Document(
                page_content="Test content 1",
                metadata={"source": "https://example.com/page1"},
            ),
            Document(
                page_content="Test content 2",
                metadata={"source": "https://example.com/page2"},
            ),
            Document(
                page_content="Test content 3",
                metadata={"source": "https://example.com/page3"},
            ),  # Will be filtered by excluded_urls
        ]
        mock_loader.return_value = mock_loader_instance

        # Mock the url relevance verification (only page1, page2 are relevant)
        self.mock_metadata_extractor.verify_urls_relevance.return_value = [
            "example.com/page1",
            "example.com/page2",
        ]

        # Update the asset group metadata to include excluded_urls
        self.asset_group.meta["tofu_connected_assets_metadata"] = {
            "last_synced_at": "2024-03-18:12:00:00",
            "last_synced_status": ConnectedAssetsGroupRefreshStatus.NOT_STARTED.value,
            "category": "test_category",
            "tofu_connected_source_type_info": {
                "urls": ["https://example.com"],
                "max_depth": 3,
                "max_pages": 100,
                "excluded_urls": ["https://example.com/page3"],  # Exclude page3
            },
        }
        self.asset_group.save()

        # Create existing assets
        existing_asset1 = AssetInfo.objects.create(
            asset_info_group=self.asset_group,
            asset_key="existing-1",
            docs={
                "doc1": {"type": "url", "value": "https://example.com/page1"}
            },  # Kept/Updated
            meta={"exclude_from_sync": False},
        )
        existing_asset_deleted = AssetInfo.objects.create(
            asset_info_group=self.asset_group,
            asset_key="existing-deleted",
            docs={
                "doc_del": {"type": "url", "value": "https://example.com/page_deleted"}
            },  # Should be marked deleted
            meta={"exclude_from_sync": False},
        )

        # Mock the parallel asset creation to simulate creating page2
        # page1 exists, page3 is excluded, page_deleted doesn't exist anymore
        mock_new_asset_page2 = Mock(
            spec=AssetInfo, asset_key="https://example.com/page2"
        )
        mock_create_parallel.return_value = AssetCreationResult(
            created_assets=[mock_new_asset_page2],
            created_asset_keys=["https://example.com/page2"],
            failures=[],
        )

        # Mock URL verification for the deletion check
        mock_verify_url.return_value = (
            False  # Simulate page_deleted URL no longer exists
        )

        # Call refresh_assets
        result = self.refresher.refresh_assets(self.asset_group)

        # --- Assertions ---

        # Verify status updates: IN_PROGRESS then SUCCESS
        mock_get_status.assert_called_once_with(self.asset_group)
        mock_update_status.assert_has_calls(
            [
                call(self.asset_group, ConnectedAssetsGroupRefreshStatus.IN_PROGRESS),
                call(self.asset_group, ConnectedAssetsGroupRefreshStatus.SUCCESS),
            ]
        )

        # Verify loader was called
        mock_loader.assert_called_once_with(
            "https://example.com",
            deep_crawl=True,
            crawler_max_depth=3,
            crawler_max_page=100,
        )
        mock_loader_instance.load_deep.assert_called_once_with(
            deep_crawl_only_same_folder=False, always_crawl_next_page=True
        )

        # Verify relevance check was called correctly (page1, page2, page3 input)
        self.mock_metadata_extractor.verify_urls_relevance.assert_called_once_with(
            listing_page_url="https://example.com",
            urls=["example.com/page1", "example.com/page2", "example.com/page3"],
            category="test_category",
        )

        # Verify parallel creation was called correctly (only page2, as page1 exists and page3 is excluded/irrelevant)
        # The input to _create_assets_in_parallel is urls_to_create
        # urls_to_doc_map = {'https://example.com/page1': doc1, 'https://example.com/page2': doc2} (page3 excluded)
        # existing_urls = {'https://example.com/page1': asset1, 'https://example.com/page_deleted': asset_deleted}
        # urls_to_create = page2
        mock_create_parallel.assert_called_once()
        call_args, call_kwargs = mock_create_parallel.call_args
        urls_to_create_arg = call_args[0]
        asset_group_arg = call_args[1]
        urls_to_doc_map_arg = call_args[2]
        category_arg = call_args[4]

        self.assertEqual(urls_to_create_arg, {"https://example.com/page2"})
        self.assertEqual(asset_group_arg, self.asset_group)
        self.assertIn("https://example.com/page1", urls_to_doc_map_arg)
        self.assertIn("https://example.com/page2", urls_to_doc_map_arg)
        self.assertNotIn("https://example.com/page3", urls_to_doc_map_arg)  # Excluded
        self.assertEqual(category_arg, "test_category")

        # Verify parallel_build_docs was called with the newly created asset
        mock_parallel_build_docs.assert_called_once_with(
            [mock_new_asset_page2],
            rebuild=True,
            check_and_rebuild=True,
            parallel_threads=8,
        )

        # Verify URL existence check for deletion
        mock_verify_url.assert_called_once_with("https://example.com/page_deleted")

        # Verify results dict
        self.assertEqual(result["new_assets"], ["https://example.com/page2"])
        self.assertEqual(
            result["updated_assets"], ["existing-1"]
        )  # Asset key for page1

        # Verify existing assets still exist (no actual deletion happens)
        self.assertTrue(AssetInfo.objects.filter(asset_key="existing-1").exists())
        self.assertTrue(AssetInfo.objects.filter(asset_key="existing-deleted").exists())

    @patch("api.connected_assets.connected_assets_refresher.TofuWebPageLoader")
    @patch(
        "api.connected_assets.connected_assets_refresher.URLAssetsGroupRefresher._create_assets_in_parallel"
    )
    @patch("api.connected_assets.connected_assets_refresher.parallel_build_docs")
    @patch(
        "api.connected_assets.connected_assets_refresher.update_connected_asset_group_category"
    )
    @patch(
        "api.connected_assets.connected_assets_refresher.update_connected_asset_group_refresh_status"
    )
    @patch(
        "api.connected_assets.connected_assets_refresher.get_connected_asset_group_refresh_status"
    )
    def test_deduce_category_from_url(
        self,
        mock_get_status,
        mock_update_status,
        mock_update_cat,
        mock_parallel_build_docs,
        mock_create_parallel,
        mock_loader,
    ):
        """Test category deduction from URL when category is not provided in metadata."""
        # Mock initial status
        mock_get_status.return_value = (
            ConnectedAssetsGroupRefreshStatus.NOT_STARTED.value
        )

        # Create test asset group without category in metadata
        asset_group_no_cat = AssetInfoGroup.objects.create(
            playbook=self.playbook,
            asset_info_group_key="test_group_no_category",
            meta={
                "tofu_connected_assets_metadata": {
                    "tofu_connected_source_type_info": {
                        "urls": ["https://example.com/deduce"],
                        "max_depth": 1,
                        "max_pages": 10,
                    },
                    # No "category" here initially
                    "last_synced_status": ConnectedAssetsGroupRefreshStatus.NOT_STARTED.value,
                }
            },
        )

        # Mock the deep load for actual crawling (will use deduced category)
        mock_loader_instance = Mock()
        mock_docs_deep = [
            Document(
                page_content="Deep content",
                metadata={"source": "https://example.com/deduce/page1"},
            )
        ]
        mock_loader_instance.load_deep.return_value = mock_docs_deep
        mock_loader.return_value = mock_loader_instance

        # Mock metadata extractor for category deduction and asset metadata
        deduced_category = "Blog Posts"  # Using one of the valid categories
        self.mock_metadata_extractor.deduce_category_from_url.return_value = (
            deduced_category
        )

        def metadata_side_effect(
            doc, starting_listing_page_url, starting_listing_page_category
        ):
            if doc.metadata["source"] == "https://example.com/deduce/page1":
                return {
                    "category": "Other",  # Different category but not "Ignore"
                    "title": "Page 1 Title",
                    "published_date": "2024-01-01",
                }
            return {}

        self.mock_metadata_extractor.extract_metadata.side_effect = metadata_side_effect

        # Mock relevance check
        self.mock_metadata_extractor.verify_urls_relevance.return_value = [
            "example.com/deduce/page1"
        ]

        # Mock parallel asset creation
        mock_new_asset = Mock(spec=AssetInfo, asset_key="Page 1 Title")
        mock_create_parallel.return_value = AssetCreationResult(
            created_assets=[mock_new_asset],
            created_asset_keys=["Page 1 Title"],
            failures=[],
        )

        # Call refresh_assets
        refresher = URLAssetsGroupRefresher(
            metadata_extractor=self.mock_metadata_extractor
        )
        result = refresher.refresh_assets(asset_group_no_cat)

        # Verify status updates
        mock_get_status.assert_called_once_with(asset_group_no_cat)
        mock_update_status.assert_has_calls(
            [
                call(asset_group_no_cat, ConnectedAssetsGroupRefreshStatus.IN_PROGRESS),
                call(asset_group_no_cat, ConnectedAssetsGroupRefreshStatus.SUCCESS),
            ]
        )

        # Verify category deduction was called
        self.mock_metadata_extractor.deduce_category_from_url.assert_called_once_with(
            "https://example.com/deduce"
        )

        # Verify loader calls (only deep crawl, no shallow load)
        mock_loader.assert_called_once_with(
            "https://example.com/deduce",
            deep_crawl=True,
            crawler_max_depth=1,
            crawler_max_page=10,
        )
        mock_loader_instance.load_deep.assert_called_once_with(
            deep_crawl_only_same_folder=False, always_crawl_next_page=True
        )

        # Verify category update function was called
        mock_update_cat.assert_called_once_with(asset_group_no_cat, deduced_category)

        # Verify relevance check called with deduced category
        self.mock_metadata_extractor.verify_urls_relevance.assert_called_once_with(
            listing_page_url="https://example.com/deduce",
            urls=["example.com/deduce/page1"],
            category=deduced_category,
        )

        # Verify parallel creation called with deduced category
        mock_create_parallel.assert_called_once()
        call_args, _ = mock_create_parallel.call_args
        self.assertEqual(
            call_args[0], {"https://example.com/deduce/page1"}
        )  # urls_to_create
        self.assertEqual(call_args[1], asset_group_no_cat)  # asset_group
        self.assertIn(
            "https://example.com/deduce/page1", call_args[2]
        )  # urls_to_doc_map
        self.assertEqual(call_args[4], deduced_category)  # category

        # Verify parallel build called with the mock asset
        mock_parallel_build_docs.assert_called_once_with(
            [mock_new_asset], rebuild=True, check_and_rebuild=True, parallel_threads=8
        )

        # Verify result dict
        self.assertEqual(result["new_assets"], ["Page 1 Title"])
        self.assertEqual(result["updated_assets"], [])
        self.assertEqual(result["deleted_assets"], [])

        # Clean up the extra asset group
        asset_group_no_cat.delete()

    @patch.object(URLAssetsGroupRefresher, "_do_process_and_create_assets")
    @patch("api.connected_assets.connected_assets_refresher.ThreadPoolExecutor")
    def test_create_assets_in_parallel_success(self, mock_executor, mock_process_batch):
        """Test _create_assets_in_parallel orchestrates batch processing successfully."""
        urls_to_create = {
            "https://a.com/1",
            "https://b.com/2",
            "https://c.com/3",
            "https://d.com/4",
            "https://e.com/5",
        }
        category = "test_category"
        num_workers = 2  # Expect batch size 2 -> 3 batches (2, 2, 1)

        # Mock the behavior of _do_process_and_create_assets for each batch
        result1 = AssetCreationResult(
            created_assets=[Mock()], created_asset_keys=["key1a", "key1b"], failures=[]
        )
        result2 = AssetCreationResult(
            created_assets=[Mock()], created_asset_keys=["key2a", "key2b"], failures=[]
        )
        result3 = AssetCreationResult(
            created_assets=[Mock()], created_asset_keys=["key3a"], failures=[]
        )
        mock_process_batch.side_effect = [
            result1,
            result2,
            result3,
        ]  # Provide 3 results

        # Mock ThreadPoolExecutor context manager
        mock_executor_instance = Mock()
        mock_executor.return_value.__enter__.return_value = mock_executor_instance

        # Store futures to verify submissions
        futures = {}
        # We need to capture the side_effect results correctly when mocking submit + as_completed
        side_effect_iterator = iter(mock_process_batch.side_effect)

        def mock_submit(func, *args, **kwargs):
            future = Mock()
            # Don't call mock_process_batch here, associate result with future
            try:
                future.result.return_value = next(side_effect_iterator)
            except StopIteration:
                # Should not happen if side_effect list is correct size
                future.result.side_effect = StopIteration("Mock side_effect exhausted")
            batch_arg = args[0]  # Get the url_batch argument
            futures[future] = batch_arg
            return future

        mock_executor_instance.submit.side_effect = mock_submit

        # Replace as_completed with a simple iteration over the created futures
        # Important: use list(futures.keys()) to evaluate keys *before* iteration
        with patch(
            "api.connected_assets.connected_assets_refresher.as_completed",
            lambda f_map: list(f_map.keys()),
        ):
            refresher = URLAssetsGroupRefresher(
                metadata_extractor=self.mock_metadata_extractor
            )
            final_result = refresher._create_assets_in_parallel(
                urls_to_create,
                self.asset_group,
                self.mock_doc_map,
                "https://a.com",
                category,
                num_workers=num_workers,
            )

        # Assertions
        self.assertEqual(
            mock_executor_instance.submit.call_count, 3
        )  # 5 urls, batch size 2 -> 3 calls
        submitted_batches = list(futures.values())
        self.assertEqual(len(submitted_batches[0]), 2)  # First batch size
        self.assertEqual(len(submitted_batches[1]), 2)  # Second batch size
        self.assertEqual(len(submitted_batches[2]), 1)  # Third batch size
        # Check that all original URLs were submitted across batches
        submitted_urls = set().union(*submitted_batches)
        self.assertEqual(submitted_urls, urls_to_create)

        # Check aggregated results
        self.assertEqual(len(final_result.created_assets), 3)  # 1 mock asset per result
        self.assertEqual(len(final_result.created_asset_keys), 5)  # 2 + 2 + 1 keys
        # Order depends on as_completed mock (list(keys)), check content with sets
        self.assertEqual(
            set(final_result.created_asset_keys),
            {"key1a", "key1b", "key2a", "key2b", "key3a"},
        )
        self.assertEqual(len(final_result.failures), 0)

    @patch.object(URLAssetsGroupRefresher, "_do_process_and_create_assets")
    @patch("api.connected_assets.connected_assets_refresher.ThreadPoolExecutor")
    @patch("api.connected_assets.connected_assets_refresher.CloudWatchMetrics")
    def test_create_assets_in_parallel_batch_failure(
        self, mock_metrics, mock_executor, mock_process_batch
    ):
        """Test _create_assets_in_parallel aggregates failures from batches."""
        urls_to_create = {"https://a.com/1", "https://b.com/2", "https://c.com/3"}
        category = "test_category"
        num_workers = 2  # Expect batch size 1 -> 3 batches

        # Mock _do_process_and_create_assets to return failures for one batch
        result_ok1 = AssetCreationResult(
            created_assets=[Mock()], created_asset_keys=["key_a"], failures=[]
        )
        result_with_failure = AssetCreationResult(
            created_assets=[],
            created_asset_keys=[],
            failures=[("https://b.com/2", "Failed")],
        )
        result_ok3 = AssetCreationResult(
            created_assets=[Mock()], created_asset_keys=["key_c"], failures=[]
        )
        mock_process_batch.side_effect = [result_ok1, result_with_failure, result_ok3]

        # Mock ThreadPoolExecutor
        mock_executor_instance = Mock()
        mock_executor.return_value.__enter__.return_value = mock_executor_instance
        futures = {}
        side_effect_iterator = iter(mock_process_batch.side_effect)

        def mock_submit(func, *args, **kwargs):
            future = Mock()
            try:
                future.result.return_value = next(side_effect_iterator)
            except StopIteration:
                future.result.side_effect = StopIteration("Mock side_effect exhausted")
            futures[future] = args[0]
            return future

        mock_executor_instance.submit.side_effect = mock_submit

        with patch(
            "api.connected_assets.connected_assets_refresher.as_completed",
            lambda f_map: list(f_map.keys()),
        ):
            refresher = URLAssetsGroupRefresher(
                metadata_extractor=self.mock_metadata_extractor
            )
            final_result = refresher._create_assets_in_parallel(
                urls_to_create,
                self.asset_group,
                self.mock_doc_map,
                "https://a.com",
                category,
                num_workers=num_workers,
            )

        # Assertions
        self.assertEqual(mock_executor_instance.submit.call_count, 3)  # batch size 1
        self.assertEqual(len(final_result.created_assets), 2)  # Two successes
        self.assertEqual(len(final_result.created_asset_keys), 2)
        self.assertEqual(set(final_result.created_asset_keys), {"key_a", "key_c"})
        self.assertEqual(len(final_result.failures), 1)
        self.assertEqual(final_result.failures[0], ("https://b.com/2", "Failed"))
        # Check CloudWatch metric was sent for failures
        mock_metrics.put_metric.assert_called_with(
            "connected_assets_creation_failed_assets",
            1,  # Number of failures
            [{"Name": "asset_group_id", "Value": str(self.asset_group.id)}],
        )

    @patch.object(URLAssetsGroupRefresher, "_do_process_and_create_assets")
    @patch("api.connected_assets.connected_assets_refresher.ThreadPoolExecutor")
    @patch("api.connected_assets.connected_assets_refresher.CloudWatchMetrics")
    def test_create_assets_in_parallel_batch_exception(
        self, mock_metrics, mock_executor, mock_process_batch
    ):
        """Test _create_assets_in_parallel handles exceptions during batch processing."""
        urls_to_create = {"https://a.com/1", "https://b.com/2", "https://c.com/3"}
        category = "test_category"
        num_workers = 1  # Process in one batch

        # Mock _do_process_and_create_assets to raise an exception
        mock_process_batch.side_effect = ValueError("Something went wrong!")

        # Mock ThreadPoolExecutor
        mock_executor_instance = Mock()
        mock_executor.return_value.__enter__.return_value = mock_executor_instance
        futures = {}

        def mock_submit(func, *args, **kwargs):
            future = Mock()
            # Simulate the exception being raised when result() is called
            future.result.side_effect = mock_process_batch.side_effect
            futures[future] = args[0]  # Store the batch associated with the future
            return future

        mock_executor_instance.submit.side_effect = mock_submit

        # Mock as_completed to return the future that will raise the exception
        with patch(
            "api.connected_assets.connected_assets_refresher.as_completed",
            lambda f_map: f_map.keys(),
        ):
            refresher = URLAssetsGroupRefresher(
                metadata_extractor=self.mock_metadata_extractor
            )
            final_result = refresher._create_assets_in_parallel(
                urls_to_create,
                self.asset_group,
                self.mock_doc_map,
                "https://a.com/1",
                category,
                num_workers=num_workers,
            )

        # Assertions
        self.assertEqual(mock_executor_instance.submit.call_count, 1)
        submitted_batches = list(futures.values())
        self.assertEqual(len(submitted_batches[0]), 3)  # The batch that failed

        # Check that the exception was caught and resulted in failures for the whole batch
        self.assertEqual(len(final_result.created_assets), 0)
        self.assertEqual(len(final_result.created_asset_keys), 0)
        self.assertEqual(len(final_result.failures), 3)
        # The order might vary, so check the URLs
        failed_urls = {f[0] for f in final_result.failures}
        self.assertEqual(failed_urls, urls_to_create)
        # Check the error message (it should be the string representation of the exception)
        self.assertEqual(final_result.failures[0][1], "Something went wrong!")

        # Check CloudWatch metrics were sent
        mock_metrics.put_metric.assert_has_calls(
            [
                call(
                    "connected_assets_creation_failure",
                    1,
                    [{"Name": "asset_group_id", "Value": str(self.asset_group.id)}],
                ),
                call(
                    "connected_assets_creation_failed_assets",
                    3,  # Number of URLs in the failed batch
                    [{"Name": "asset_group_id", "Value": str(self.asset_group.id)}],
                ),
            ],
            any_order=True,
        )  # Use any_order=True as the order might vary slightly

    def test_create_assets_in_parallel_no_urls(self):
        """Test _create_assets_in_parallel handles empty URL set."""
        urls_to_create = set()
        category = "test_category"

        refresher = URLAssetsGroupRefresher(
            metadata_extractor=self.mock_metadata_extractor
        )
        result = refresher._create_assets_in_parallel(
            urls_to_create,
            self.asset_group,
            self.mock_doc_map,
            "https://a.com",
            category,
            num_workers=4,
        )

        # Assertions
        self.assertEqual(len(result.created_assets), 0)
        self.assertEqual(len(result.created_asset_keys), 0)
        self.assertEqual(len(result.failures), 0)
        # No mocks needed as it should return early

    @patch("api.connected_assets.connected_assets_refresher.bulk_create_assets")
    @patch("api.connected_assets.connected_assets_refresher.CloudWatchMetrics")
    def test_do_process_and_create_assets_category_mismatch(
        self, mock_cloudwatch, mock_bulk_create
    ):
        """Test that _do_process_and_create_assets correctly filters out assets with 'Ignore' category."""
        # Setup test data
        urls_batch = [
            "https://example.com/valid1",
            "https://example.com/ignore1",
            "https://example.com/valid2",
            "https://example.com/ignore2",
        ]
        expected_category = "Blog Posts"

        # Create mock documents for each URL
        mock_docs = {}
        for url in urls_batch:
            mock_doc = Mock()
            mock_doc.metadata = {"source": url}
            mock_docs[url] = mock_doc

        # Mock metadata extraction to return different categories
        def mock_extract_metadata(
            doc, starting_listing_page_url, starting_listing_page_category
        ):
            url = doc.metadata.get("source")
            if "ignore" in url:
                return {
                    "title": f"Title {url}",
                    "category": "Ignore",  # Should be filtered out
                    "published_date": "2024-03-20",
                }
            return {
                "title": f"Title {url}",
                "category": "Other",  # Different from expected but not "Ignore" - should be kept
                "published_date": "2024-03-20",
            }

        self.mock_metadata_extractor.extract_metadata.side_effect = (
            mock_extract_metadata
        )

        # Mock bulk_create_assets to return success for the valid assets
        mock_created_assets = [
            Mock(spec=AssetInfo, asset_key="Title https://example.com/valid1"),
            Mock(spec=AssetInfo, asset_key="Title https://example.com/valid2"),
        ]
        mock_bulk_create.return_value = AssetCreationResult(
            created_assets=mock_created_assets,
            created_asset_keys=[
                "Title https://example.com/valid1",
                "Title https://example.com/valid2",
            ],
            failures=[],
        )

        # Call the method under test
        refresher = URLAssetsGroupRefresher(
            metadata_extractor=self.mock_metadata_extractor
        )
        result = refresher._do_process_and_create_assets(
            urls_batch,
            self.asset_group,
            mock_docs,
            "www.example.com/example",
            expected_category,
        )

        # Verify metadata extractor was called for all URLs
        self.assertEqual(self.mock_metadata_extractor.extract_metadata.call_count, 4)

        # Verify bulk_create_assets was called only with valid assets (non-Ignore category)
        self.assertEqual(mock_bulk_create.call_count, 1)
        bulk_create_call_args = mock_bulk_create.call_args[0]
        self.assertEqual(len(bulk_create_call_args[0]), 2)  # Should have 2 assets

        # Get the URLs from the assets' document metadata
        created_urls = []
        for asset in bulk_create_call_args[0]:
            doc = next(iter(asset.docs.values()))
            created_urls.append(doc["value"])

        # Verify the correct URLs were used (non-Ignore category URLs)
        self.assertIn("https://example.com/valid1", created_urls)
        self.assertIn("https://example.com/valid2", created_urls)
        self.assertNotIn("https://example.com/ignore1", created_urls)
        self.assertNotIn("https://example.com/ignore2", created_urls)

        # Verify all assets have the expected category in their metadata
        for asset in bulk_create_call_args[0]:
            self.assertEqual(asset.meta["category"], expected_category)

        # Verify the result contains only the valid assets
        self.assertEqual(len(result.created_assets), 2)
        self.assertEqual(len(result.created_asset_keys), 2)
        self.assertEqual(len(result.failures), 0)

        # Verify CloudWatch metrics for skipped assets
        # Since we have 2 ignored URLs, the metric should be called twice
        mock_cloudwatch.put_metric.assert_has_calls(
            [
                call(
                    "connected_assets_refresher_url_asset_creation_skipped",
                    1,
                    [
                        {
                            "Name": "AssetGroup",
                            "Value": str(self.asset_group.id),
                        }
                    ],
                ),
                call(
                    "connected_assets_refresher_url_asset_creation_skipped",
                    1,
                    [
                        {
                            "Name": "AssetGroup",
                            "Value": str(self.asset_group.id),
                        }
                    ],
                ),
            ]
        )

    @patch("api.connected_assets.connected_assets_refresher.cache")
    def test_update_status_for_existing_task(self, mock_cache):
        """Test status update for an existing task with Redis cache."""
        # Setup test data
        task_id = "test_task_123"
        self.asset_group.meta["tofu_connected_assets_metadata"] = {
            "refresh_task_id": task_id
        }
        self.asset_group.save()

        # Call update_status
        update_connected_asset_group_refresh_status(
            self.asset_group, ConnectedAssetsGroupRefreshStatus.IN_PROGRESS
        )

        # Verify cache was updated
        mock_cache.set.assert_called_once_with(
            task_id,
            {"status": ConnectedAssetsGroupRefreshStatus.IN_PROGRESS.value},
            timeout=60 * 60 * 6 + 60 * 10,
        )

        # Verify metadata was updated
        self.asset_group.refresh_from_db()
        self.assertEqual(
            self.asset_group.meta["tofu_connected_assets_metadata"][
                "last_synced_status"
            ],
            ConnectedAssetsGroupRefreshStatus.IN_PROGRESS.value,
        )

    @patch("api.connected_assets.connected_assets_refresher.cache")
    def test_get_status_with_cache(self, mock_cache):
        """Test getting status when it exists in Redis cache."""
        # Setup test data
        task_id = "test_task_123"
        self.asset_group.meta["tofu_connected_assets_metadata"] = {
            "refresh_task_id": task_id
        }
        self.asset_group.save()

        # Mock cache response
        mock_cache.get.return_value = {
            "status": ConnectedAssetsGroupRefreshStatus.IN_PROGRESS.value
        }

        # Get status
        status = get_connected_asset_group_refresh_status(self.asset_group)

        # Verify cache was checked
        mock_cache.get.assert_called_with(task_id)

        # Verify correct status was returned
        self.assertEqual(status, ConnectedAssetsGroupRefreshStatus.IN_PROGRESS)

    @patch("api.connected_assets.connected_assets_refresher.cache")
    def test_get_status_without_cache_fallback_to_metadata(self, mock_cache):
        """Test getting status when cache is empty, falling back to metadata."""
        # Setup test data
        task_id = "test_task_123"
        self.asset_group.meta["tofu_connected_assets_metadata"] = {
            "refresh_task_id": task_id,
            "last_synced_status": ConnectedAssetsGroupRefreshStatus.SUCCESS.value,
        }
        self.asset_group.save()

        # Mock empty cache response
        mock_cache.get.return_value = None

        # Get status
        status = get_connected_asset_group_refresh_status(self.asset_group)

        # Verify cache was checked
        mock_cache.get.assert_called_once_with(task_id)

        # Verify correct status was returned from metadata
        self.assertEqual(status, ConnectedAssetsGroupRefreshStatus.SUCCESS)

    @patch("api.connected_assets.connected_assets_refresher.cache")
    def test_get_status_without_task_id(self, mock_cache):
        """Test getting status when no task ID exists."""
        # Setup test data with no task ID
        self.asset_group.meta["tofu_connected_assets_metadata"] = {
            "last_synced_status": ConnectedAssetsGroupRefreshStatus.NOT_STARTED.value
        }
        self.asset_group.save()

        # Get status
        status = get_connected_asset_group_refresh_status(self.asset_group)

        # Verify cache was not checked
        mock_cache.get.assert_not_called()

        # Verify correct default status was returned
        self.assertEqual(status, ConnectedAssetsGroupRefreshStatus.NOT_STARTED)

    @patch("api.connected_assets.connected_assets_refresher.cache")
    def test_status_update_flow(self, mock_cache):
        """Test complete flow of status updates and checks."""
        # Setup test data
        task_id = "test_task_123"
        self.asset_group.meta["tofu_connected_assets_metadata"] = {
            "refresh_task_id": task_id
        }
        self.asset_group.save()

        # Update status to IN_PROGRESS
        update_connected_asset_group_refresh_status(
            self.asset_group, ConnectedAssetsGroupRefreshStatus.IN_PROGRESS
        )

        # Mock cache response for IN_PROGRESS
        mock_cache.get.return_value = {
            "status": ConnectedAssetsGroupRefreshStatus.IN_PROGRESS.value
        }

        # Verify status is IN_PROGRESS
        status = get_connected_asset_group_refresh_status(self.asset_group)
        self.assertEqual(status, ConnectedAssetsGroupRefreshStatus.IN_PROGRESS)

        # Update status to SUCCESS
        update_connected_asset_group_refresh_status(
            self.asset_group, ConnectedAssetsGroupRefreshStatus.SUCCESS
        )

        # Mock cache response for SUCCESS
        mock_cache.get.return_value = {
            "status": ConnectedAssetsGroupRefreshStatus.SUCCESS.value
        }

        # Verify status is SUCCESS
        status = get_connected_asset_group_refresh_status(self.asset_group)
        self.assertEqual(status, ConnectedAssetsGroupRefreshStatus.SUCCESS)

        # Verify cache was updated twice
        self.assertEqual(mock_cache.set.call_count, 2)
        mock_cache.set.assert_has_calls(
            [
                call(
                    task_id,
                    {"status": ConnectedAssetsGroupRefreshStatus.IN_PROGRESS.value},
                    timeout=60 * 60 * 6 + 60 * 10,
                ),
                call(
                    task_id,
                    {"status": ConnectedAssetsGroupRefreshStatus.SUCCESS.value},
                    timeout=60 * 60 * 6 + 60 * 10,
                ),
            ]
        )

    def test_delete_duplicated_suggested_connected_sources(self):
        """Test deletion of duplicated suggested connected sources."""
        # Setup test data
        # Create a suggested connected source with the same URL
        suggested_source = AssetInfoGroup.objects.create(
            playbook=self.playbook,
            asset_info_group_key="suggested_group",
            meta={
                "tofu_connected_assets_metadata": {
                    "tofu_connected_source": True,
                    "tofu_suggested_connected_source": True,
                    "tofu_connected_source_type": "url",
                    "tofu_connected_source_type_info": {
                        "urls": ["https://example.com"]
                    },
                }
            },
        )

        # Create another suggested source with a different URL
        different_source = AssetInfoGroup.objects.create(
            playbook=self.playbook,
            asset_info_group_key="different_group",
            meta={
                "tofu_connected_assets_metadata": {
                    "tofu_connected_source": True,
                    "tofu_suggested_connected_source": True,
                    "tofu_connected_source_type": "url",
                    "tofu_connected_source_type_info": {
                        "urls": ["https://different.com"]
                    },
                }
            },
        )

        # create a non-suggested connected sources
        connected_source = AssetInfoGroup.objects.create(
            playbook=self.playbook,
            asset_info_group_key="non_suggested_group",
            meta={
                "tofu_connected_assets_metadata": {
                    "tofu_connected_source": True,
                    "tofu_connected_source_type": "url",
                    "tofu_connected_source_type_info": {"urls": ["example.com"]},
                }
            },
        )

        # Call the function
        delete_duplicated_suggested_connected_sources(connected_source)

        # Verify the duplicated source was deleted
        self.assertFalse(AssetInfoGroup.objects.filter(id=suggested_source.id).exists())
        # Verify the different source still exists
        self.assertTrue(AssetInfoGroup.objects.filter(id=different_source.id).exists())

    def tearDown(self):
        # Clean up created objects
        AssetInfo.objects.all().delete()
        AssetInfoGroup.objects.all().delete()
        self.playbook.delete()
        self.company.delete()
