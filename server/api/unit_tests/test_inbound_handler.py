from unittest.mock import Mock, patch

import pytest
from django.core.cache import cache

from ..inbound_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, InboundProvider


@pytest.fixture(autouse=True)
def clear_cache():
    """Clear cache before and after each test"""
    cache.clear()
    yield
    cache.clear()


class TestInboundProviderCache:
    def test_happierleads_cache_hit(self):
        # Arrange
        ip = "*******"
        cache_key = InboundProvider.cache_key_happierleads(ip)
        cached_data = {"status_code": 200, "data": {"company": "test"}}
        cache.set(cache_key, cached_data)

        # Act
        response = InboundProvider.resolve_identification_happierleads(ip)

        # Assert
        assert response.status_code == 200
        assert response.json() == {"company": "test"}

    def test_leadfeeder_cache_hit(self):
        # Arrange
        ip = "*******"
        cache_key = InboundProvider.cache_key_leadfeeder(ip)
        cached_data = {"status_code": 200, "data": {"company": "test"}}
        cache.set(cache_key, cached_data)

        # Act
        response = InboundProvider.resolve_identification_leadfeeder(ip)

        # Assert
        assert response.status_code == 200
        assert response.json() == {"company": "test"}

    @patch("requests.get")
    def test_happierleads_cache_miss_then_hit(self, mock_get):
        # Arrange
        ip = "*******"
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"company": "test"}
        mock_get.return_value = mock_response

        # Act - First call (cache miss)
        response1 = InboundProvider.resolve_identification_happierleads(ip)
        # Second call (should be cache hit)
        response2 = InboundProvider.resolve_identification_happierleads(ip)

        # Assert
        assert mock_get.call_count == 1  # Only called once due to caching
        assert response1.json() == response2.json()


class TestInboundHandlerCache:
    @pytest.fixture
    def handler(self):
        return InboundHandler(
            ip_address="*******",
            cookie_id="test_cookie",
            passed_tofu_content_id="test_content",
            url="http://test.com",
        )

    def test_cache_keys(self, handler):
        assert handler.cache_key_ip == "inbound_cache:test_content:*******"
        assert handler.cache_key_cookie == "inbound_cache:test_content:test_cookie"

    def test_save_and_retrieve_from_cache(self, handler):
        # Arrange
        test_data = ("test_id", "test_slug", {"var": "test"})

        # Act
        handler._save_to_cache(*test_data)
        cached_data = handler._check_cached_data()

        # Assert - expect the tuple format that _check_cached_data returns
        assert (
            cached_data == test_data
        )  # Will be ('test_id', 'test_slug', {'var': 'test'})

    def test_cache_expiry(self, handler):
        # Arrange
        test_data = ("test_id", "test_slug", {"var": "test"})

        # Act
        with patch("django.core.cache.cache.set") as mock_cache_set:
            handler._save_to_cache(*test_data)

        # Assert
        assert mock_cache_set.call_count == 2  # Called for both IP and cookie
        # Check that timeout is 30 days (in seconds)
        assert mock_cache_set.call_args_list[0].kwargs["timeout"] == 60 * 60 * 24 * 30
