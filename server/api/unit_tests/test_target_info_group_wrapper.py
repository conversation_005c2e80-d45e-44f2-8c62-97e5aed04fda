import unittest
from unittest.mock import MagicMock, patch

from django.test import TestCase

from ..models import CompanyInfo, Playbook, TargetInfo, TargetInfoGroup
from ..playbook_build.target_info_group_enricher import TargetInfoGroupEnricher
from ..playbook_build.target_info_group_wrapper import TargetInfoGroupWrapper


class TestTargetInfoGroupWrapper(TestCase):
    def setUp(self):
        self.target_info_group = MagicMock(spec=TargetInfoGroup)
        self.wrapper = TargetInfoGroupWrapper(self.target_info_group)

    def test_sync_tofu_research(self):
        with patch(
            "api.playbook_build.target_info_group_wrapper.ObjectBuilder"
        ) as mock_object_builder:
            # Mock targets
            target1 = MagicMock(id=1)
            target2 = MagicMock(id=2)
            targets = [target1, target2]

            # Mock ObjectBuilder
            mock_builder = MagicMock()
            mock_object_builder.get_builder.return_value = mock_builder
            mock_builder.tofu_research.side_effect = [
                {"result": "data1"},
                {"result": "data2"},
            ]

            result = self.wrapper._sync_tofu_research(
                "query_id", "test query", targets, preview=True
            )

            self.assertEqual(result, {1: {"result": "data1"}, 2: {"result": "data2"}})
            mock_builder.tofu_research.assert_called_with(
                query_id="query_id", query="test query", preview=True
            )

    @patch("api.playbook_build.target_info_group_wrapper.async_tofu_research")
    def test_async_tofu_research(self, mock_async_tofu_research):
        targets = [MagicMock(id=1), MagicMock(id=2)]

        result = self.wrapper._async_tofu_research("query_id", "test query", targets)

        self.assertTrue(result.startswith("tofu_research:"))
        mock_async_tofu_research.delay.assert_called_once()

    def test_tofu_research(self):
        with patch(
            "api.playbook_build.target_info_group_wrapper.TargetInfo"
        ) as mock_target_info:
            with patch.object(
                TargetInfoGroupWrapper, "_sync_tofu_research"
            ) as mock_sync:
                with patch.object(
                    TargetInfoGroupWrapper, "_async_tofu_research"
                ) as mock_async:
                    mock_target_info.objects.filter.return_value = [
                        MagicMock(id=1),
                        MagicMock(id=2),
                    ]

                    # Test preview mode
                    self.wrapper.tofu_research("query_id", "test query", preview=True)
                    mock_sync.assert_called_once()
                    mock_async.assert_not_called()

                    mock_sync.reset_mock()

                    # Test async mode
                    self.wrapper.tofu_research("query_id", "test query", preview=False)
                    mock_sync.assert_not_called()
                    mock_async.assert_called_once()

    def test_delete_tofu_research(self):
        with patch(
            "api.playbook_build.target_info_group_wrapper.TargetInfo"
        ) as mock_target_info:
            with patch(
                "api.playbook_build.target_info_group_wrapper.ObjectBuilder"
            ) as mock_object_builder:
                self.target_info_group.meta = {
                    "tofu_research": {"query_id": {"query": "test"}}
                }
                self.target_info_group.save()

                mock_target = MagicMock()
                mock_target_info.objects.filter.return_value = [mock_target]

                mock_builder = MagicMock()
                mock_object_builder.get_builder.return_value = mock_builder
                mock_builder.delete_tofu_research.return_value = True

                result = self.wrapper.delete_tofu_research("query_id")

                self.assertTrue(result)
                self.assertNotIn(
                    "query_id", self.target_info_group.meta["tofu_research"]
                )
                mock_builder.delete_tofu_research.assert_called_with(
                    "query_id", save=False
                )
                mock_target_info.objects.bulk_update.assert_called_once()


class TestTargetInfoGroupEnricher(TestCase):

    def setUp(self):
        # Create a playbook first (required for target_info_group)
        company_info = CompanyInfo.objects.create()
        self.playbook = Playbook.objects.create(company_object=company_info)

        # Create a target info group with correct fields
        self.target_info_group = TargetInfoGroup.objects.create(
            playbook=self.playbook,
            target_info_group_key="test_group",  # This is required instead of "name"
            meta={"type": "Company"},  # Add type to meta field
            status={},
        )

        # Create a wrapper for the group
        self.enricher = TargetInfoGroupEnricher(self.target_info_group)

        # Fields to enrich
        self.fields = ["company_name", "website"]

    @patch("api.playbook_build.target_info_group_enricher.ObjectBuilder")
    def test_sync_process_data_enrichment(self, mock_object_builder):
        """Test synchronous data enrichment when target count < 10"""
        # Create a few target infos (less than 10)
        targets = []
        for i in range(5):
            target = TargetInfo.objects.create(
                target_key=f"key_{i}",
                target_info_group=self.target_info_group,
                additional_info={},
            )
            targets.append(target)

        # Mock the ObjectBuilder
        mock_builder = MagicMock()
        mock_object_builder.get_builder.return_value = mock_builder

        # Call the method
        result = self.enricher.process_data_enrichment(self.fields)

        # Verify that _sync_process_data_enrichment was used
        # Check that the builder was called for each target
        self.assertEqual(mock_object_builder.get_builder.call_count, 5)
        self.assertEqual(mock_builder.process_data_enrichment.call_count, 5)

        # Check that process_data_enrichment was called with the right fields
        mock_builder.process_data_enrichment.assert_called_with(self.fields)

    @patch("api.playbook_build.target_info_group_enricher.process_enrichment_batch")
    def test_async_process_data_enrichment(self, mock_process_batch):
        """Test asynchronous data enrichment when target count >= 10"""
        # Create 15 target infos
        targets = []
        for i in range(15):
            target = TargetInfo.objects.create(
                target_key=f"key_{i}",
                target_info_group=self.target_info_group,
                additional_info={},
            )
            targets.append(target)

        # Mock the task chain
        mock_set = MagicMock()
        mock_task = MagicMock()
        mock_process_batch.s.return_value = mock_set
        mock_set.set.return_value = mock_task

        # Call the method
        result = self.enricher.process_data_enrichment(self.fields)

        # Verify that _async_process_data_enrichment was used
        # Check that the correct task was created
        mock_process_batch.s.assert_called()
        mock_set.set.assert_called()
        mock_task.apply_async.assert_called()

        # Ensure we're properly batching targets (15 targets with batch size 10)
        # Should create 2 batches
        self.assertEqual(mock_process_batch.s.call_count, 2)


if __name__ == "__main__":
    unittest.main()
