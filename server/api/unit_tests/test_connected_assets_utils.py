from unittest.mock import MagicMock, patch

import pytest

from ..connected_assets.connected_assets_utils import (
    bulk_create_assets,
    join_url,
    normalize_url,
    verify_url_exists,
)
from ..models import AssetInfo


def test_normalize_url():
    # Test cases for normalize_url
    test_cases = [
        ("https://www.tofuhq.com/blog", "tofuhq.com/blog"),
        ("http://www.tofuhq.com/blog", "tofuhq.com/blog"),
        ("https://tofuhq.com/blog", "tofuhq.com/blog"),
        ("https://www.tofuhq.com", "tofuhq.com"),
        ("https://www.tofuhq.com/", "tofuhq.com"),
        ("https://www.tofuhq.com/blog/", "tofuhq.com/blog"),
        ("https://www.tofuhq.com/blog/posts", "tofuhq.com/blog/posts"),
        ("https://www.tofuhq.com/blog/posts.html", "tofuhq.com/blog/posts"),
        ("https://www.tofuhq.com/blog/posts.htm", "tofuhq.com/blog/posts"),
        (
            "www.tofuhq.com/resource/blogpost",
            "tofuhq.com/resource/blogpost",
        ),  # Test without protocol
    ]

    for input_url, expected in test_cases:
        assert normalize_url(input_url) == expected


def test_join_url():
    # Test cases for join_url
    test_cases = [
        ("https://www.tofuhq.com", "blog", "https://www.tofuhq.com/blog"),
        ("https://www.tofuhq.com/", "blog", "https://www.tofuhq.com/blog"),
        ("https://www.tofuhq.com", "/blog", "https://www.tofuhq.com/blog"),
        ("https://www.tofuhq.com/", "/blog/", "https://www.tofuhq.com/blog"),
        ("https://www.tofuhq.com/blog", "posts", "https://www.tofuhq.com/blog/posts"),
    ]

    for base_url, path, expected in test_cases:
        assert join_url(base_url, path) == expected


@patch("api.connected_assets.connected_assets_utils.TofuWebPageLoader")
def test_verify_url_exists(mock_tofu_web_page_loader):
    # Test case 1: Invalid URL
    assert verify_url_exists("https://atlan") == False

    # Test case 2: Valid URL with content
    mock_loader_instance = MagicMock()
    mock_tofu_web_page_loader.return_value = mock_loader_instance
    mock_loader_instance.load_shallow.return_value = [
        MagicMock()
    ]  # Return a non-empty list

    assert verify_url_exists("https://example.com") == True
    mock_tofu_web_page_loader.assert_called_with(
        url="https://example.com", deep_crawl=False
    )
    mock_loader_instance.load_shallow.assert_called_once()

    # Test case 3: Valid URL but no content
    mock_loader_instance.load_shallow.return_value = []  # Return an empty list

    assert verify_url_exists("https://example.com/empty") == False
    mock_tofu_web_page_loader.assert_called_with(
        url="https://example.com/empty", deep_crawl=False
    )

    # Test case 4: Exception during loading
    mock_loader_instance.load_shallow.side_effect = Exception("Connection error")

    assert verify_url_exists("https://example.com/error") == False
    mock_tofu_web_page_loader.assert_called_with(
        url="https://example.com/error", deep_crawl=False
    )


class TestConnectedAssetsUtils:
    def test_join_url_basic(self):
        """Test basic URL joining functionality"""
        base_url = "https://example.com"
        path = "blog/post"
        expected = "https://example.com/blog/post"
        result = join_url(base_url, path)
        assert result == expected

    def test_join_url_with_trailing_slash_base(self):
        """Test URL joining with trailing slash in base URL"""
        base_url = "https://example.com/"
        path = "blog/post"
        expected = "https://example.com/blog/post"
        result = join_url(base_url, path)
        assert result == expected

    def test_join_url_with_leading_slash_path(self):
        """Test URL joining with leading slash in path"""
        base_url = "https://example.com"
        path = "/blog/post"
        expected = "https://example.com/blog/post"
        result = join_url(base_url, path)
        assert result == expected

    def test_join_url_with_trailing_slash_path(self):
        """Test URL joining with trailing slash in path"""
        base_url = "https://example.com"
        path = "blog/post/"
        expected = "https://example.com/blog/post"
        result = join_url(base_url, path)
        assert result == expected

    def test_join_url_with_html_extension(self):
        """Test URL joining with HTML extension"""
        base_url = "https://example.com"
        path = "blog/post.html"
        expected = "https://example.com/blog/post.html"
        result = join_url(base_url, path)
        assert result == expected

    def test_join_url_with_query_parameters(self):
        """Test URL joining with query parameters"""
        base_url = "https://example.com"
        path = "blog/post?id=123"
        expected = "https://example.com/blog/post?id=123"
        result = join_url(base_url, path)
        assert result == expected

    def test_join_url_with_fragment(self):
        """Test URL joining with fragment"""
        base_url = "https://example.com"
        path = "blog/post#section"
        expected = "https://example.com/blog/post#section"
        result = join_url(base_url, path)
        assert result == expected


@pytest.mark.django_db
class TestBulkCreateAssets:
    @patch("api.connected_assets.connected_assets_utils.AssetInfo.objects.bulk_create")
    @patch("api.connected_assets.connected_assets_utils.AssetInfo.objects.filter")
    @patch("api.utils.CloudWatchMetrics.put_metric")
    @patch("logging.info")
    def test_bulk_create_success(
        self, mock_logging, mock_metrics, mock_filter, mock_bulk_create
    ):
        # Arrange
        assets = [AssetInfo(asset_key=f"key_{i}") for i in range(5)]
        mock_bulk_create.return_value = assets  # Simulate successful bulk creation
        mock_filter.return_value = assets

        # Act
        result = bulk_create_assets(assets)

        # Assert
        assert len(result.created_assets) == 5
        assert len(result.failures) == 0
        mock_logging.assert_any_call("Bulk created 5 assets successfully.")
        mock_metrics.assert_not_called()
        mock_filter.assert_called_once_with(
            asset_key__in={f"key_{i}" for i in range(5)}
        )

    @patch("api.connected_assets.connected_assets_utils.AssetInfo.objects.bulk_create")
    @patch("api.connected_assets.connected_assets_utils.AssetInfo.objects.filter")
    @patch("api.connected_assets.connected_assets_utils.AssetInfo.save")
    @patch("api.utils.CloudWatchMetrics.put_metric")
    @patch("logging.info")
    def test_bulk_create_with_conflicts(
        self, mock_logging, mock_metrics, mock_save, mock_filter, mock_bulk_create
    ):
        # Arrange
        assets = [AssetInfo(asset_key=f"key_{i}") for i in range(5)]
        bulk_created_assets = assets[:3]  # key_0, key_1, key_2
        mock_bulk_create.return_value = bulk_created_assets  # Simulate partial success
        mock_filter.return_value = bulk_created_assets
        mock_save.side_effect = lambda: None  # Simulate successful individual save

        # Act
        result = bulk_create_assets(assets)

        # Assert
        assert len(result.created_assets) == 5  # 3 from bulk, 2 individual
        assert len(result.failures) == 0
        mock_logging.assert_any_call("Bulk created 3 assets successfully.")
        mock_logging.assert_any_call("Individually created asset key_3.")
        mock_logging.assert_any_call("Individually created asset key_4.")
        mock_metrics.assert_not_called()
        mock_filter.assert_called_once_with(asset_key__in={"key_0", "key_1", "key_2"})

    @patch("api.connected_assets.connected_assets_utils.AssetInfo.objects.bulk_create")
    @patch("api.utils.CloudWatchMetrics.put_metric")
    @patch("logging.exception")
    def test_bulk_create_failure(self, mock_logging, mock_metrics, mock_bulk_create):
        # Arrange
        assets = [AssetInfo(asset_key=f"key_{i}") for i in range(5)]
        mock_bulk_create.side_effect = Exception("Bulk creation failed")

        # Act
        result = bulk_create_assets(assets)

        # Assert
        assert len(result.created_assets) == 0
        assert len(result.failures) == 5
        mock_logging.assert_any_call(
            "Bulk creation failed: Bulk creation failed, will fallback to individual creation"
        )
        mock_metrics.assert_any_call("bulk_asset_creation_failure", 1, [])
