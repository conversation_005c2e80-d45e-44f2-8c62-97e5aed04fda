import json
from datetime import date, timedelta
from unittest.mock import MagicMock, patch

import pytest
from bs4 import BeautifulSoup
from django.db.models import Q
from django.test import TestCase
from langchain.schema import Generation
from langchain_core.messages import HumanMessage, SystemMessage

from ..async_tasks import postprocess_company_info
from ..connected_assets.connected_assets_utils import extract_links_info_from_soup
from ..connected_assets.site_map_discover import (
    SiteMapDiscover,
    SiteMapResult,
    SiteMapSection,
)
from ..model_config import ModelConfig, ModelParams
from ..models import CompanyInfo, Playbook, TofuUser


@pytest.fixture
def mock_model_params():
    return ModelParams(
        model_name="gpt-4o-2024-11-20",
        model_params={
            "model_name": "gpt-4o-2024-11-20",
            "components": [],
            "n": 1,
            "max_retries": 3,
        },
    )


@pytest.fixture
def mock_model_config(mock_model_params):
    config = ModelConfig(model_budget=1000, model_params_list=[mock_model_params])
    return config


@pytest.fixture
def mock_soup():
    html = """
    <html>
        <head>
            <title>Test Site</title>
        </head>
        <body>
            <nav>
                <a href="/about">About</a>
                <a href="/products">Products</a>
            </nav>
            <main>
                <a href="/contact">Contact Us</a>
            </main>
        </body>
    </html>
    """
    return BeautifulSoup(html, "html.parser")


@pytest.fixture
def mock_loader():
    loader = MagicMock()
    loader.scrape.return_value = BeautifulSoup(
        "<html><body><a href='/test'>Test</a></body></html>", "html.parser"
    )
    loader.load_shallow.return_value = [MagicMock(page_content="Test page content")]
    loader.load_deep.return_value = [
        MagicMock(
            metadata={"source": "https://example.com/test"},
            page_content="Test deep content",
        )
    ]
    return loader


@pytest.fixture
def site_map_discover(mock_loader):
    with (
        patch(
            "api.connected_assets.site_map_discover.TofuWebPageLoader",
            return_value=mock_loader,
        ),
        patch(
            "api.connected_assets.site_map_discover.ModelConfigResolver.resolve"
        ) as mock_resolve,
        patch(
            "api.connected_assets.site_map_discover.ModelCaller"
        ) as mock_model_caller,
        patch("api.utils.get_token_count") as mock_get_token_count,
    ):
        mock_model_caller_instance = MagicMock()
        mock_model_config = ModelConfig(
            model_budget=1000,
            model_params_list=[
                ModelParams(
                    model_name="gpt-4o-2024-11-20",
                    model_params={
                        "model_name": "gpt-4o-2024-11-20",
                        "components": [],
                        "n": 1,
                        "max_retries": 3,
                    },
                )
            ],
        )
        mock_model_caller_instance.model_config = mock_model_config
        mock_model_caller.return_value = mock_model_caller_instance

        # Mock token count to return a fixed value
        mock_get_token_count.return_value = 10

        discoverer = SiteMapDiscover(
            url="https://example.com",
            playbook_id=123,
            max_depth=2,
            max_pages=500,
            customer_instructions="Test instructions",
        )
        discoverer.model_config = mock_model_config  # Set model_config directly
        return discoverer


def test_initialization(site_map_discover):
    assert site_map_discover.url == "https://example.com"
    assert site_map_discover.max_depth == 2
    assert site_map_discover.max_pages == 500
    assert site_map_discover.customer_instructions == "Test instructions"


def test_extract_links_info_from_soup(site_map_discover, mock_soup):
    links_data = extract_links_info_from_soup(mock_soup, site_map_discover.url)

    assert links_data["page_title"] == "Test Site"
    assert links_data["base_url"] == "https://example.com"
    assert len(links_data["links"]) == 3

    # Verify link data structure
    for link in links_data["links"]:
        assert "text" in link
        assert "href" in link
        assert "path" in link
        assert "parent_elements" in link


def test_analyze_root_url(site_map_discover):
    # Patch the instance method directly
    site_map_discover.model_caller.get_llm_dict_response = MagicMock()
    mock_llm_response = {
        "content_sections": [
            {
                "category": "case study",
                "confidence": "high",
                "url_paths": ["/case-studies", "/success-stories"],
            }
        ],
        "suggested_paths": [
            {"category": "blog post", "url_paths": ["/blog"], "confidence": "high"}
        ],
        "forbidden_urls_patterns": ["/admin/*"],
    }
    site_map_discover.model_caller.get_llm_dict_response.return_value = (
        mock_llm_response
    )

    # Mock the cache to return None on first call and the mock response on second call
    with patch(
        "django.core.cache.cache.get", side_effect=[None, json.dumps(mock_llm_response)]
    ):
        # First call - should not use cache
        result = site_map_discover._analyze_root_url()
        assert result == mock_llm_response
        site_map_discover.model_caller.get_llm_dict_response.assert_called_once()

        # Second call - should use cache
        result = site_map_discover._analyze_root_url()
        assert result == mock_llm_response
        # Verify that get_llm_dict_response was not called again
        assert site_map_discover.model_caller.get_llm_dict_response.call_count == 1


def test_analyze_root_url_cache_error(site_map_discover):
    # Patch cache.get to raise an exception
    with patch("django.core.cache.cache.get", side_effect=Exception("Cache error")):
        site_map_discover.model_caller.get_llm_dict_response = MagicMock()
        mock_llm_response = {
            "content_sections": [
                {
                    "category": "case study",
                    "confidence": "high",
                    "url_paths": ["/case-studies"],
                }
            ],
            "suggested_paths": [],
            "forbidden_urls_patterns": [],
        }
        site_map_discover.model_caller.get_llm_dict_response.return_value = (
            mock_llm_response
        )

        # Should still work despite cache error
        result = site_map_discover._analyze_root_url()
        assert result == mock_llm_response
        site_map_discover.model_caller.get_llm_dict_response.assert_called_once()


def test_analyze_urls(site_map_discover):
    # Patch the instance method directly
    site_map_discover.model_caller.get_llm_dict_response = MagicMock()
    mock_llm_response = {
        "content_sections": [
            {
                "category": "blog post",
                "confidence": "high",
                "url_paths": ["/blog/post1", "/blog/post2"],
            }
        ],
        "suggested_paths": [
            {"category": "blog post", "url_paths": ["/blog"], "confidence": "high"}
        ],
        "forbidden_urls_patterns": ["/admin/*"],
    }
    site_map_discover.model_caller.get_llm_dict_response.return_value = (
        mock_llm_response
    )

    # Mock the cache to return None on first call and the mock response on second call
    with patch(
        "django.core.cache.cache.get", side_effect=[None, json.dumps(mock_llm_response)]
    ):
        # First call - should not use cache
        result = site_map_discover._analyze_urls("https://example.com", 1, [])
        assert result == mock_llm_response
        site_map_discover.model_caller.get_llm_dict_response.assert_called_once()

        # Second call - should use cache
        result = site_map_discover._analyze_urls("https://example.com", 1, [])
        assert result == mock_llm_response
        # Verify that get_llm_dict_response was not called again
        assert site_map_discover.model_caller.get_llm_dict_response.call_count == 1


def test_analyze_urls_cache_error(site_map_discover):
    # Patch cache.get to raise an exception
    with patch("django.core.cache.cache.get", side_effect=Exception("Cache error")):
        site_map_discover.model_caller.get_llm_dict_response = MagicMock()
        mock_response = {
            "content_sections": [
                {
                    "category": "case study",
                    "confidence": "high",
                    "url_paths": ["/case-studies"],
                }
            ],
            "suggested_paths": [],
            "forbidden_urls_patterns": [],
        }
        site_map_discover.model_caller.get_llm_dict_response.return_value = (
            mock_response
        )

        # Should still work despite cache error
        result = site_map_discover._analyze_urls(
            url="https://example.com/case-studies",
            level=1,
            skipped_patterns=["/admin/*"],
        )
        assert result == mock_response
        site_map_discover.model_caller.get_llm_dict_response.assert_called_once()


def test_cache_key_generation(site_map_discover):
    # Test root URL analysis cache key
    cache_key = site_map_discover._root_url_analysis_cache_key()
    assert isinstance(cache_key, str)
    assert len(cache_key) > 0
    assert "root_url_analysis" in cache_key

    # Test pattern analysis cache key
    skipped_patterns = ["/admin/*", "/private/*"]
    pattern_cache_key = site_map_discover._pattern_analysis_cache_key(skipped_patterns)
    assert pattern_cache_key.startswith("site_map_discover_pattern_analysis:")
    assert site_map_discover.url in pattern_cache_key
    assert "level_" in pattern_cache_key
    assert "patterns_" in pattern_cache_key
    assert "prompt_" in pattern_cache_key
    assert "catdefs_" in pattern_cache_key
    assert "format_" in pattern_cache_key
    assert "instructions_" in pattern_cache_key

    # Test that different skipped patterns generate different cache keys
    different_patterns = ["/admin/*", "/public/*"]
    different_pattern_cache_key = site_map_discover._pattern_analysis_cache_key(
        different_patterns
    )
    assert pattern_cache_key != different_pattern_cache_key

    # Verify cache keys are different
    assert cache_key != pattern_cache_key


def test_verify_url_path(site_map_discover):
    with patch(
        "api.connected_assets.connected_assets_utils.TofuWebPageLoader"
    ) as mock_loader:
        mock_loader_instance = MagicMock()
        mock_loader_instance.load_shallow.return_value = [MagicMock()]
        mock_loader.return_value = mock_loader_instance

        assert site_map_discover._verify_url_paths(["/test"]) == ["/test"]

        mock_loader_instance.load_shallow.return_value = []
        assert site_map_discover._verify_url_paths(["/invalid"]) == []


@patch("api.connected_assets.site_map_discover.SiteMapDiscover._analyze_root_url")
@patch("api.connected_assets.site_map_discover.SiteMapDiscover._analyze_urls")
@patch("api.connected_assets.site_map_discover.SiteMapDiscover._verify_url_paths")
def test_discover_structure(
    mock_verify_url_paths,
    mock_analyze_urls,
    mock_analyze_root_url,
    site_map_discover,
):
    mock_verify_url_paths.side_effect = (
        lambda paths: paths
    )  # Make function return input list

    mock_root_analysis = {
        "content_sections": [
            {
                "category": "case study",
                "confidence": "high",
                "url_paths": ["/case-studies"],
            }
        ],
        "suggested_paths": [{"category": "blog post", "url_paths": ["/blog"]}],
        "forbidden_urls_patterns": ["/admin/*"],
    }

    mock_level_result = {
        "content_sections": [
            {
                "category": "case study",
                "confidence": "high",
                "url_paths": ["/success-stories", "/case-studies.html"],
            }
        ],
        "suggested_paths": [{"category": "blog post", "url_paths": ["/articles"]}],
    }

    mock_analyze_root_url.return_value = mock_root_analysis
    mock_analyze_urls.return_value = mock_level_result

    result = site_map_discover.discover_structure()

    assert result.root_url == "https://example.com"
    assert result.root_url == "https://example.com"
    assert (
        result.content_sections[0].category
        == mock_root_analysis["content_sections"][0]["category"]
    )
    assert (
        result.content_sections[0].confidence
        == mock_root_analysis["content_sections"][0]["confidence"]
    )
    assert set(result.content_sections[0].url_paths) == set(
        ["/case-studies", "/success-stories"]
    )
    assert (
        result.suggested_paths[0].category
        == mock_root_analysis["suggested_paths"][0]["category"]
    )
    assert set(result.suggested_paths[0].url_paths) == set(["/blog"])
    assert result.execution_time > 0

    # Verify content sections are combined and deduplicated
    assert len(result.content_sections) == 1  # Same category, different paths

    # Verify suggested paths are combined
    assert len(result.suggested_paths) == 2  # Different categories


def test_validate_result_format(site_map_discover):
    """Test the validation of result format."""
    # Test valid result
    valid_result = {
        "content_sections": [
            {
                "category": "case study",
                "confidence": "high",
                "url_paths": ["/case-studies", "/success-stories"],
            }
        ],
        "suggested_paths": [
            {
                "category": "blog post",
                "url_paths": ["/blog", "/articles"],
                "confidence": "high",
            }
        ],
        "forbidden_urls_patterns": ["/admin/*", "/private/*"],
    }
    assert site_map_discover._validate_result_format(valid_result) is True

    # Test missing required fields in content section
    invalid_content_section = {
        "content_sections": [
            {
                "category": "case study",  # Missing confidence and url_paths
            }
        ],
        "suggested_paths": [],
        "forbidden_urls_patterns": [],
    }
    assert site_map_discover._validate_result_format(invalid_content_section) is False

    # Test invalid confidence value
    invalid_confidence = {
        "content_sections": [
            {
                "category": "case study",
                "confidence": "invalid",  # Invalid confidence value
                "url_paths": ["/case-studies"],
            }
        ],
        "suggested_paths": [],
        "forbidden_urls_patterns": [],
    }
    assert site_map_discover._validate_result_format(invalid_confidence) is False

    # Test invalid field types
    invalid_types = {
        "content_sections": [
            {
                "category": 123,  # Should be string
                "confidence": "high",
                "url_paths": "not_a_list",  # Should be list
            }
        ],
        "suggested_paths": [],
        "forbidden_urls_patterns": [],
    }
    assert site_map_discover._validate_result_format(invalid_types) is False

    # Test invalid forbidden patterns
    invalid_forbidden = {
        "content_sections": [],
        "suggested_paths": [],
        "forbidden_urls_patterns": 123,  # Should be list
    }
    assert site_map_discover._validate_result_format(invalid_forbidden) is False

    # Test optional fields are allowed
    result_with_optional = {
        "content_sections": [
            {
                "category": "case study",
                "confidence": "high",
                "url_paths": ["/case-studies"],
                "estimated_url_count": 10,  # Optional field
                "indicators": ["indicator1"],  # Optional field
            }
        ],
        "suggested_paths": [
            {
                "category": "blog post",
                "url_paths": ["/blog"],
                "confidence": "high",  # Optional field
                "estimated_url_count": 5,  # Optional field
            }
        ],
        "forbidden_urls_patterns": ["/admin/*"],
    }
    assert site_map_discover._validate_result_format(result_with_optional) is True


def test_merge_content_sections(site_map_discover):
    """Test merging of content sections, especially confidence handling."""
    # Setup test data
    with patch(
        "api.connected_assets.site_map_discover.SiteMapDiscover._process_content_section"
    ) as mock_process:
        # Mock the _process_content_section to return the input with verified paths
        mock_process.side_effect = lambda section: section

        # Test case: Sections with same category but different confidence
        sections = [
            {"category": "blog", "confidence": "low", "url_paths": ["/blog/1"]},
            {"category": "blog", "confidence": "medium", "url_paths": ["/blog/2"]},
            {"category": "blog", "confidence": "high", "url_paths": ["/blog/3"]},
        ]

        # Also add a section with different category
        sections.append(
            {"category": "about", "confidence": "medium", "url_paths": ["/about"]}
        )

        # Call the method
        result = site_map_discover._merge_content_sections(sections)

        # Verify results
        assert len(result) == 2  # blog, about

        # Find the blog section and verify it has high confidence (due to one section having high)
        blog_section = next((s for s in result if s["category"] == "blog"), None)
        assert blog_section is not None
        assert (
            blog_section["confidence"] == "high"
        )  # This should be high because one section had high confidence
        assert len(blog_section["url_paths"]) == 3  # Combined url_paths

        # Verify the about section remains unchanged
        about_section = next((s for s in result if s["category"] == "about"), None)
        assert about_section is not None
        assert about_section["confidence"] == "medium"


@pytest.mark.django_db
def test_postprocess_company_info_skip_scenarios():
    """Test that postprocess_company_info skips for lite users, e2e test users, and eval users."""

    # Test 1: Skip for lite users
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    lite_user = TofuUser.objects.create(
        username="lite_user",
        email="<EMAIL>",
        customer_type=TofuUser.CustomerType.LITE,
    )
    playbook.users.add(lite_user)

    with patch("os.environ.get", return_value=None):  # Allow function to run in tests
        with patch(
            "api.async_tasks.async_generated_suggested_connected_asset_group.apply_async"
        ) as mock_task:
            postprocess_company_info(CompanyInfo, company_info, created=True)
            mock_task.assert_not_called()

    # Test 2: Skip for e2e test users
    company_info2 = CompanyInfo.objects.create()
    playbook2 = Playbook.objects.create(
        name="Test Playbook 2", company_object=company_info2
    )
    e2e_user = TofuUser.objects.create(
        username="tofuadmin-e2etest-12345",
        email="<EMAIL>",
        customer_type=TofuUser.CustomerType.NORMAL,
    )
    playbook2.users.add(e2e_user)

    with patch("os.environ.get", return_value=None):  # Allow function to run in tests
        with patch(
            "api.async_tasks.async_generated_suggested_connected_asset_group.apply_async"
        ) as mock_task:
            postprocess_company_info(CompanyInfo, company_info2, created=True)
            mock_task.assert_not_called()

    # Test 3: Skip for eval users
    company_info3 = CompanyInfo.objects.create()
    playbook3 = Playbook.objects.create(
        name="Test Playbook 3", company_object=company_info3
    )
    eval_user = TofuUser.objects.create(
        username="tofuadmin-eval-test",
        email="<EMAIL>",
        customer_type=TofuUser.CustomerType.NORMAL,
    )
    playbook3.users.add(eval_user)

    with patch("os.environ.get", return_value=None):  # Allow function to run in tests
        with patch(
            "api.async_tasks.async_generated_suggested_connected_asset_group.apply_async"
        ) as mock_task:
            postprocess_company_info(CompanyInfo, company_info3, created=True)
            mock_task.assert_not_called()

    # Test 4: Should NOT skip for regular paid users
    company_info4 = CompanyInfo.objects.create()
    playbook4 = Playbook.objects.create(
        name="Test Playbook 4", company_object=company_info4
    )
    regular_user = TofuUser.objects.create(
        username="regular_user",
        email="<EMAIL>",
        customer_type=TofuUser.CustomerType.NORMAL,
    )
    playbook4.users.add(regular_user)

    with patch("os.environ.get", return_value=None):  # Allow function to run in tests
        with patch(
            "api.async_tasks.get_company_website", return_value="https://example.com"
        ):
            with patch(
                "api.async_tasks.async_generated_suggested_connected_asset_group.apply_async"
            ) as mock_task:
                postprocess_company_info(CompanyInfo, company_info4, created=True)
                mock_task.assert_called_once()
