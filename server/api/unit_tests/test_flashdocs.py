import unittest
from unittest.mock import MagicMock, patch

from django.test import TestCase

from ..slides.flashdocs import (
    FlashdocsDocumentFormat,
    FlashdocsDocumentType,
    async_generate_flashdocs_slides,
    get_document_config,
    get_slide_metadata,
    list_document_configurations,
    poll_task_until_done,
    sync_generate_flashdocs_slides,
    upload_google_slides_to_flashdocs,
    upload_s3_ppt_to_flashdocs,
)


class TestFlashdocs(TestCase):
    def setUp(self):
        self.test_google_slides_url = "https://docs.google.com/presentation/d/test"
        self.test_s3_bucket = "test-bucket"
        self.test_s3_filename = "test.pptx"
        self.test_document_id = "test-doc-id"

    @patch("api.slides.flashdocs.requests.post")
    def test_upload_google_slides_to_flashdocs_success(self, mock_post):
        # Setup
        mock_response = MagicMock()
        mock_response.json.return_value = {"document_id": self.test_document_id}
        mock_post.return_value = mock_response

        # Execute
        result = upload_google_slides_to_flashdocs(
            self.test_google_slides_url,
            document_type=FlashdocsDocumentType.TEMPLATE,
            name="Test Presentation",
            description="Test Description",
        )

        # Assert
        self.assertEqual(result["document_id"], self.test_document_id)
        mock_post.assert_called_once()

    @patch("api.slides.flashdocs.requests.post")
    def test_upload_google_slides_to_flashdocs_failure(self, mock_post):
        # Setup
        mock_post.side_effect = Exception("API Error")

        # Execute and Assert
        with self.assertRaises(Exception):
            upload_google_slides_to_flashdocs(self.test_google_slides_url)

    @patch("api.slides.flashdocs.TempS3File")
    @patch("api.slides.flashdocs.requests.post")
    @patch("api.slides.flashdocs.shutil.copy")
    @patch("builtins.open", create=True)
    def test_upload_s3_ppt_to_flashdocs_success(
        self, mock_open, mock_copy, mock_post, mock_temp_file
    ):
        # Setup
        mock_response = MagicMock()
        mock_response.json.return_value = {"document_id": self.test_document_id}
        mock_post.return_value = mock_response

        # Mock the context manager behavior
        mock_context = MagicMock()
        mock_context.__enter__.return_value = "/tmp/mock_temp_file"
        mock_context.__exit__.return_value = None
        mock_temp_file.return_value = mock_context

        # Mock the file object
        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file

        # Execute
        result = upload_s3_ppt_to_flashdocs(
            self.test_s3_bucket,
            self.test_s3_filename,
            document_type=FlashdocsDocumentType.TEMPLATE,
            name="Test Presentation",
            description="Test Description",
        )

        # Assert
        self.assertEqual(result["document_id"], self.test_document_id)
        mock_post.assert_called_once()
        mock_temp_file.assert_called_once_with(
            self.test_s3_bucket, self.test_s3_filename
        )
        mock_copy.assert_called_once()

    @patch("api.slides.flashdocs.requests.post")
    def test_sync_generate_flashdocs_slides_success(self, mock_post):
        # Setup
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "task_id": "test-task-id",
            "link_to_deck": "https://docs.google.com/presentation/d/test",
        }
        mock_post.return_value = mock_response

        # Execute
        result = sync_generate_flashdocs_slides(
            prompt="Test prompt",
            source_document_id=self.test_document_id,
            outline=[{"slide_id": "1", "content": "Test content"}],
        )

        # Assert
        self.assertEqual(result["task_id"], "test-task-id")
        self.assertEqual(
            result["link_to_deck"], "https://docs.google.com/presentation/d/test"
        )
        mock_post.assert_called_once()

    @patch("api.slides.flashdocs.requests.get")
    def test_get_document_config_success(self, mock_get):
        # Setup
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "id": self.test_document_id,
            "name": "Test Presentation",
            "slides": [{"id": "1", "content": "Test content"}],
        }
        mock_get.return_value = mock_response

        # Execute
        result = get_document_config(self.test_document_id, include_slides=True)

        # Assert
        self.assertEqual(result["id"], self.test_document_id)
        self.assertEqual(len(result["slides"]), 1)
        mock_get.assert_called_once()

    @patch("api.slides.flashdocs.requests.post")
    def test_async_generate_flashdocs_slides_success(self, mock_post):
        # Setup
        mock_response = MagicMock()
        mock_response.json.return_value = {"task_id": "test-task-id"}
        mock_post.return_value = mock_response

        # Execute
        result = async_generate_flashdocs_slides(
            prompt="Test prompt", source_document_id=self.test_document_id
        )

        # Assert
        self.assertEqual(result["task_id"], "test-task-id")
        mock_post.assert_called_once()

    @patch("api.slides.flashdocs.requests.request")
    def test_poll_task_until_done_success(self, mock_request):
        # Setup
        # Create responses for polling sequence: 202 (in progress) -> 202 -> 200 (success)
        mock_in_progress = MagicMock()
        mock_in_progress.status_code = 202

        mock_success = MagicMock()
        mock_success.status_code = 200
        mock_success.json.return_value = {
            "link_to_deck": "https://docs.google.com/presentation/d/test"
        }

        # Set up the request mock to return "in progress" twice, then success
        mock_request.side_effect = [mock_in_progress, mock_in_progress, mock_success]

        # Execute
        result = poll_task_until_done("test-task-id", max_attempts=5, delay=0)

        # Assert
        self.assertEqual(
            result["link_to_deck"], "https://docs.google.com/presentation/d/test"
        )
        # Verify the request was called 3 times (2 in progress + 1 success)
        self.assertEqual(mock_request.call_count, 3)

    @patch("api.slides.flashdocs.requests.get")
    def test_get_slide_metadata_success(self, mock_get):
        # Setup
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "id": "test-slide-id",
            "content": "Test content",
        }
        mock_get.return_value = mock_response

        # Execute
        result = get_slide_metadata("test-slide-id")

        # Assert
        self.assertEqual(result["id"], "test-slide-id")
        mock_get.assert_called_once()

    @patch("api.slides.flashdocs.requests.get")
    def test_list_document_configurations_success(self, mock_get):
        # Setup
        mock_response = MagicMock()
        mock_response.json.return_value = [
            {
                "id": self.test_document_id,
                "type": FlashdocsDocumentType.TEMPLATE.value,
                "format": FlashdocsDocumentFormat.GOOGLE.value,
            }
        ]
        mock_get.return_value = mock_response

        # Execute
        result = list_document_configurations(
            FlashdocsDocumentType.TEMPLATE, FlashdocsDocumentFormat.GOOGLE
        )

        # Assert
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["id"], self.test_document_id)
        mock_get.assert_called_once()
