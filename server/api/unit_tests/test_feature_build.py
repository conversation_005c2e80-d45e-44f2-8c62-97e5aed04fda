from unittest.mock import MagicMock, patch

import pytest

from ..feature.feature_builder.joint_component_feature_builder import (
    JointComponentFeatureBuilder,
)
from ..models import AssetInfo, AssetInfoGroup


@pytest.fixture
def mock_asset_info_group():
    return MagicMock(spec=AssetInfoGroup)


@pytest.fixture
def mock_playbook():
    return MagicMock()


@pytest.fixture
def mock_gen_env():
    return MagicMock()


def test_component_personalization_joint_component_custom_prompt_build(
    mock_playbook, mock_gen_env
):
    # Mock components data
    components = {
        "comp1": {
            "meta": {
                "component_params": {
                    "custom_instructions": [
                        {
                            "assets": {"Smart replace url list": None},
                            "instruction": "Replace the statistic reference with one from the list",
                        }
                    ]
                }
            }
        }
    }

    # Mock AssetInfo objects
    mock_asset_infos = [
        MagicMock(spec=AssetInfo, asset_key="asset1"),
        MagicMock(spec=AssetInfo, asset_key="asset2"),
    ]
    with patch("api.models.AssetInfo.objects") as mock_objects:

        # Configure the mock to return our mock asset infos
        mock_objects.filter.return_value = mock_asset_infos

        # Create builder instance
        builder = JointComponentFeatureBuilder(components, mock_playbook, mock_gen_env)
        mock_gen_env._gen_settings.enable_custom = True
        mock_gen_env._data_wrapper.repurpose_template = None

        # Execute the method
        result = builder.extract_component_level_custom_prompts()

        # Verify the result
        expected_assets = (
            "{Smart replace url list:asset1}\n{Smart replace url list:asset2}"
        )
        expected_xml = (
            "<component id='comp1'>\n"
            "<componentSpecificInstruction>\n"
            "<referenceContent>\n"
            "<assets>\n"
            f"{expected_assets}\n"
            "</assets>\n"
            "<instruction (using reference content)>\n"
            "Replace the statistic reference with one from the list\n"
            "</instruction>\n"
            "</referenceContent>\n"
            "</componentSpecificInstruction>\n"
            "</component>\n"
        )

        assert result == expected_xml
