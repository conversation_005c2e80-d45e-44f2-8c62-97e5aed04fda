from unittest.mock import MagicMock, patch

import pytest
from django.test import TestCase

from ..content import ContentGenerator
from ..content_collection import ContentCollectionHandler
from ..content_gen.base_content_generator import BaseContentGenerator
from ..content_gen.disjoint_content_gen import DisjointContentGenerator
from ..content_gen.email_template_personalization_gen import (
    EmailTemplatePersonalizationGenerator,
)
from ..content_gen.free_content_gen import FreeContentGenerator
from ..content_gen.full_page_html_gen import FullPageHtmlGenerator
from ..content_gen.joint_content_gen import (
    JointContentGenerator,
    JointGenerationMessageBuilder,
)
from ..content_gen.long_form_content_gen import LongFormContentGenerator
from ..content_gen.template_gen import TemplateGenerator
from ..feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    ContentGenSettings,
    ContentWrapper,
    GenerateEnv,
)
from ..feature.feature_assembler.generate_feature_assembler import (
    GenerateFeatureAssembler,
)
from ..models import (
    Campaign,
    CompanyInfo,
    Content,
    ContentGroup,
    ContentVariation,
    Playbook,
    TofuUser,
)
from ..playbook import PlaybookHandler


@pytest.fixture
def mock_playbook_handler():
    return MagicMock()


@pytest.fixture
def mock_content():
    content = MagicMock(spec=Content)
    content.content_status = {"gen_status": {"status": "NOT_STARTED"}}
    content_group = MagicMock(spec=ContentGroup)
    content_group.content_group_params = {}
    content.content_group = content_group
    campaign = MagicMock(spec=Campaign)
    campaign.campaign_params = {}
    content_group.campaign = campaign
    content.creator.context = {"model": "gpt-4o-2024-11-20"}

    return content


@pytest.fixture
def content_generator(mock_playbook_handler, mock_content):
    return ContentGenerator(mock_playbook_handler, mock_content)


def test_content_generator_initialization(
    content_generator, mock_playbook_handler, mock_content
):
    assert content_generator.playbook_handler == mock_playbook_handler
    assert content_generator.content_instance == mock_content
    assert isinstance(content_generator._data_wrapper, ContentWrapper)


@pytest.mark.django_db
def test_set_settings(content_generator):
    content_generator.set_settings(
        foundation_model="us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        save_variations=False,
    )
    assert (
        content_generator._gen_settings.foundation_model
        == "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
    )


@pytest.mark.django_db
def test_initialize_content_status(content_generator):
    content_generator._initialize_content_status()
    assert "gen_status" in content_generator.content_instance.content_status
    assert (
        content_generator.content_instance.content_status["gen_status"]["status"]
        == "IN_PROGRESS"
    )


@pytest.mark.django_db
def test_merge_variations_to_db(content_generator):
    db_variations = {
        "comp1": {
            "meta": {
                "variations": [{"text": "Old variation"}],
                "current_variation_index": 0,
                "current_version": 1,
            }
        }
    }
    gen_variations = {
        "comp1": {
            "meta": {
                "variations": [{"text": "New variation"}],
                "current_variation_index": 0,
                "current_version": 2,
            }
        }
    }
    result = content_generator.merge_variations_to_db(db_variations, gen_variations)
    assert len(result["comp1"]["meta"]["variations"]) == 2
    assert result["comp1"]["meta"]["current_variation_index"] == 1
    assert result["comp1"]["meta"]["current_version"] == 2
    assert result["comp1"]["meta"]["variations"][0]["text"] == "Old variation"


@pytest.mark.parametrize(
    "gen_setting, expected_generator",
    [
        ("free_gen", FreeContentGenerator),
        ("is_long_form_generation", LongFormContentGenerator),
        ("is_template_personalization", EmailTemplatePersonalizationGenerator),
        ("template_generation", TemplateGenerator),
        ("joint_generation", JointContentGenerator),
        ("full_page_html_gen", FullPageHtmlGenerator),
        (None, DisjointContentGenerator),
    ],
)
def test_create_generator(content_generator, gen_setting, expected_generator):
    content_generator._gen_settings = MagicMock()
    content_generator._data_wrapper = MagicMock()
    content_generator._gen_settings.foundation_model = "gpt-4o-2024-11-20"
    gen_env = GenerateEnv(
        content_generator._data_wrapper, content_generator._gen_settings
    )

    # Set all attributes to False
    for attr in [
        "free_gen",
        "is_long_form_generation",
        "is_template_personalization",
        "template_generation",
        "joint_generation",
        "full_page_html_gen",
    ]:
        setattr(content_generator._gen_settings, attr, False)

    # Set the specific attribute to True
    if gen_setting:
        setattr(content_generator._gen_settings, gen_setting, True)

    # Special case for email template personalization
    if gen_setting == "is_template_personalization":
        content_generator._data_wrapper.is_email = True

    # Create mock for get_prev_gen_variations
    mock_get_prev_gen = MagicMock(return_value={})

    # Create a mock ChatBedrock instance
    mock_chat_bedrock = MagicMock()

    # Patch both functions in multiple possible locations
    patches = [
        patch.object(
            BaseContentGenerator, "get_prev_gen_variations", new=mock_get_prev_gen
        ),
    ]

    for p in patches:
        p.start()

    try:
        generator = content_generator._create_generator(gen_env)
        assert isinstance(generator, expected_generator)
    finally:
        # Stop all patches
        for p in patches:
            p.stop()


@pytest.mark.parametrize(
    "username, expected_result",
    [
        ("tofuadmin-export-e2e-3", True),
        ("tofuadmin-test-be", True),
        ("e2etest-homepage-newuser", True),
        ("tofuadmin-jian-test", True),
        ("tofuadmin-e2etest-user", True),
        ("e2etest-user", True),
        ("regular-user", False),
        (None, False),
    ],
)
def test_should_use_cache(content_generator, username, expected_result):
    with patch("api.models.TofuUser") as MockTofuUser:
        mock_user = MockTofuUser.return_value
        mock_user.is_eligible_for_llm_cache.return_value = expected_result

        user = mock_user if username else None
        if user:
            user.username = username

        result = user.is_eligible_for_llm_cache() if user else False
        assert result == expected_result

        if user:
            mock_user.is_eligible_for_llm_cache.assert_called_once()


@patch("api.content.FreeContentGenerator")
@patch("api.content.PlaybookBuilder")
@patch(
    "api.content_gen.base_content_generator.BaseContentGenerator.get_prev_gen_variations"
)
def test_free_gen_components(
    mock_get_prev_gen, mock_playbook_builder, mock_free_content_generator
):
    campaign = MagicMock(spec=Campaign)
    campaign.campaign_params = {"campaign_goal": "Repurpose Content"}
    content_group = MagicMock(spec=ContentGroup)
    content_group.components = {
        "component1": {"text": "text1"},
        "component2": {"text": "text2"},
    }
    content = MagicMock(spec=Content)
    content.content_group = content_group
    content.content_status = {"gen_status": {"status": "NOT_STARTED"}}
    playbook_handler = MagicMock(spec=PlaybookHandler)
    playbook_handler.playbook_instance = MagicMock(spec=Playbook)
    components_param = {
        "component3": {
            "meta": {
                "parent_component_id": "component1",
                "custom_instructions": [{"instruction": "Foo"}],
            },
            "text": "Bar",
        }
    }
    mock_playbook_builder_instance = MagicMock()
    mock_playbook_builder.return_value = mock_playbook_builder_instance
    mock_playbook_builder_instance.pre_build_playbook_context.return_value = None
    mock_get_prev_gen.return_value = {}

    # Mock the FreeContentGenerator instance and its gen method
    mock_free_gen_instance = MagicMock()
    mock_free_gen_instance.gen.return_value = components_param
    mock_free_content_generator.return_value = mock_free_gen_instance

    # Create ContentGenerator and generate variations
    content_generator = ContentGenerator(playbook_handler, content)
    content_generator.set_settings(
        components=components_param,
        free_gen=True,
        foundation_model="gpt-4o-2024-11-20",
        num_of_variations=1,
        save_variations=False,
    )
    content_generator.gen()

    # Assertions
    mock_free_content_generator.assert_called_once()
    assert content_generator._gen_settings._components == components_param
    assert content_generator._gen_settings.free_gen
    assert content_generator._gen_settings.foundation_model == "gpt-4o-2024-11-20"
    assert content_generator._gen_settings.num_of_variations == 1
    # check that the content status has not been initialized
    assert content.content_status == {"gen_status": {"status": "NOT_STARTED"}}


class TestJointSingleComponentGen(TestCase):

    def setUp(self):
        self.user = TofuUser.objects.create(username="testuser")
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=CompanyInfo.objects.create(),
        )
        self.playbook.users.set([self.user])
        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            creator=self.user,
            playbook=self.playbook,
            campaign_params={
                "targets": [{"target1": ["value1"]}],
                "foundation_model": "gpt-4o-2024-11-20",
                "campaign_goal": "Personalization",
            },
            campaign_status={},
        )
        self.content_group = ContentGroup.objects.create(
            campaign=self.campaign,
            creator=self.user,
            components={
                "comp1": {
                    "text": "text1",
                    "meta": {"type": "text", "component_type": ""},
                },
                "comp2": {
                    "text": "text2",
                    "meta": {"type": "text", "component_type": ""},
                },
            },
            content_group_params={},
            content_group_status={},
        )
        self.content = Content.objects.create(
            content_group=self.content_group, content_params={}, content_status={}
        )

    @patch(
        "api.content_gen.base_content_generator.BaseContentGenerator.get_prev_gen_variations"
    )
    @pytest.mark.django_db
    def test_joint_single_component_gen(self, mock_get_prev_gen):
        mock_get_prev_gen.return_value = {}

        playbook_handler = PlaybookHandler(self.playbook)
        content_generator = ContentGenerator(playbook_handler, self.content)

        content_generator.set_settings(
            components={"comp1": {"text": "text1", "meta": {}}},
            joint_generation=True,
            foundation_model="gpt-4o-2024-11-20",
            num_of_variations=1,
            save_variations=True,
        )

        assert content_generator._gen_settings._components == {
            "comp1": {"text": "text1", "meta": {}}
        }
        assert content_generator._gen_settings.joint_generation
        assert content_generator._gen_settings.foundation_model == "gpt-4o-2024-11-20"
        assert content_generator._gen_settings.num_of_variations == 1
        assert content_generator._gen_settings.save_variations
        with (
            patch.object(GenerateFeatureAssembler, "build"),
            patch.object(
                JointGenerationMessageBuilder, "create_llm_inputs"
            ) as mock_create_llm_inputs,
            patch.object(JointContentGenerator, "get_results") as mock_get_results,
            patch.object(BaseContentGenerator, "_log_generation_stats"),
        ):
            # Mock the create_llm_inputs method
            mock_create_llm_inputs.return_value = {"mocked": "llm_inputs"}

            # Mock the _generate_and_validate method
            mock_get_results.return_value = [
                MagicMock(text='{"comp1": {"text": "generated text 1", "meta": {}}}')
            ]

            # Call the gen() method
            ret_variations = content_generator.gen()

            # get variations and check if only comp1 is in there
            assert len(ret_variations.variations) == 1
            assert "comp1" in ret_variations.variations
            assert (
                ret_variations.variations["comp1"]["meta"]["variations"][0]["text"]
                == "generated text 1"
            )

    @pytest.mark.django_db
    def test_joint_single_component_gen_feature_builder(self):

        playbook_handler = PlaybookHandler(self.playbook)
        content_generator = ContentGenerator(playbook_handler, self.content)

        content_generator.set_settings(
            components={"comp1": {"text": "text1", "meta": {}}},
            joint_generation=True,
            foundation_model="gpt-4o-2024-11-20",
            num_of_variations=1,
            save_variations=True,
        )
        gen_env = GenerateEnv(
            data_wrapper=content_generator._data_wrapper,
            gen_settings=content_generator._gen_settings,
        )
        gen_feature_assembler = GenerateFeatureAssembler(
            model_budget=8000,
            gen_env=gen_env,
        )
        gen_feature_assembler.build()
        joint_message_builder = JointGenerationMessageBuilder(
            gen_feature_assembler=gen_feature_assembler,
            model_budget=8000,
            prev_gen_variations={},
            gen_env=gen_env,
        )

        joint_message_builder.create_llm_inputs(
            content_generator._data_wrapper.text_gen_components
        )
        features = joint_message_builder._features
        assert "contents_xml" in features
        assert (
            "text1" in features["contents_xml"] and "text2" in features["contents_xml"]
        )

    @pytest.mark.django_db
    @patch("api.content.PlaybookBuilder")
    @patch(
        "api.content_gen.base_content_generator.BaseContentGenerator.get_prev_gen_variations"
    )
    @patch(
        "api.content_gen.disjoint_content_gen.DefaultMessageInputBuilder.create_llm_inputs"
    )
    @patch(
        "api.feature.feature_assembler.generate_feature_assembler.GenerateFeatureAssembler.build"
    )
    def test_disjoint_gen_components(
        self,
        mock_feature_assembler_build,
        mock_create_llm_inputs,
        mock_get_prev_gen,
        mock_playbook_builder,
    ):

        # Mock PlaybookBuilder
        mock_playbook_builder_instance = MagicMock()
        mock_playbook_builder.return_value = mock_playbook_builder_instance
        mock_playbook_builder_instance.pre_build_playbook_context.return_value = None

        # Mock get_prev_gen_variations
        mock_get_prev_gen.return_value = {}

        # Mock create_llm_inputs
        mock_create_llm_inputs.return_value = {"mocked": "llm_inputs"}

        # Mock feature_assembler_build
        mock_feature_assembler_build.return_value = {"mocked": "features"}

        self.content_group.components = {
            "comp1": {"text": "text1", "meta": {}},
        }

        playbook_handler = PlaybookHandler(self.playbook)
        content_generator = ContentGenerator(playbook_handler, self.content)

        # Create ContentGenerator and generate variations
        components_param = {
            "comp1": {"text": "text1", "meta": {}},
        }
        content_generator.set_settings(
            components=components_param,
            foundation_model="gpt-4o-2024-11-20",
            num_of_variations=1,
            save_variations=True,
            joint_generation=False,
        )
        # Mock the get_results method of DisjointContentGenerator
        mock_get_results = MagicMock(return_value=[MagicMock(text="generated text 1")])
        with (
            patch.object(BaseContentGenerator, "get_results", mock_get_results),
            patch.object(BaseContentGenerator, "_log_generation_stats"),
        ):
            result = content_generator.gen()

        # Assertions
        gen_env = GenerateEnv(
            data_wrapper=content_generator._data_wrapper,
            gen_settings=content_generator._gen_settings,
        )
        generator = content_generator._create_generator(gen_env)
        assert isinstance(generator, DisjointContentGenerator)
        mock_create_llm_inputs.assert_called()
        mock_feature_assembler_build.assert_called()
        assert content_generator._gen_settings._components == components_param
        assert content_generator._gen_settings.foundation_model == "gpt-4o-2024-11-20"
        assert content_generator._gen_settings.num_of_variations == 1
        assert isinstance(result, ContentVariation)
        assert len(result.variations) == 1
        assert "comp1" in result.variations
        assert (
            result.variations["comp1"]["meta"]["variations"][0]["text"]
            == "generated text 1"
        )

    @pytest.mark.django_db
    @patch("api.content.PlaybookBuilder")
    @patch(
        "api.content_gen.base_content_generator.BaseContentGenerator.get_prev_gen_variations"
    )
    @patch(
        "api.content_gen.joint_content_gen.JointGenerationMessageBuilder.create_llm_inputs"
    )
    @patch(
        "api.content_gen.disjoint_content_gen.DefaultMessageInputBuilder.create_llm_inputs"
    )
    @patch(
        "api.feature.feature_assembler.generate_feature_assembler.GenerateFeatureAssembler.build"
    )
    def test_joint_gen_fallback(
        self,
        mock_feature_assembler_build,
        mock_create_llm_inputs_default,
        mock_create_llm_inputs_joint,
        mock_get_prev_gen,
        mock_playbook_builder,
    ):
        # Mock PlaybookBuilder
        mock_playbook_builder_instance = MagicMock()
        mock_playbook_builder.return_value = mock_playbook_builder_instance
        mock_playbook_builder_instance.pre_build_playbook_context.return_value = None

        # Mock get_prev_gen_variations
        mock_get_prev_gen.return_value = {}

        # Mock create_llm_inputs
        mock_create_llm_inputs_default.return_value = {"mocked": "llm_inputs_default"}
        mock_create_llm_inputs_joint.return_value = {"mocked": "llm_inputs_joint"}

        # Mock feature_assembler_build
        mock_feature_assembler_build.return_value = {"mocked": "features"}
        self.content_group.components = {
            "comp1": {"text": "text1", "meta": {}},
            "comp2": {"text": "text2", "meta": {}},
        }

        playbook_handler = PlaybookHandler(self.playbook)
        content_generator = ContentGenerator(playbook_handler, self.content)

        # Create ContentGenerator and generate variations
        content_generator.set_settings(
            foundation_model="gpt-4o-2024-11-20",
            num_of_variations=1,
            save_variations=True,
            joint_generation=True,
        )

        # Mock the get_results to return different generations depending on joint or not
        def mock_get_results(llm_inputs, json_output=False):
            return [MagicMock(text="disjoint generated text")]

        mock_get_results = MagicMock(side_effect=mock_get_results)

        with (
            patch.object(BaseContentGenerator, "get_results", new=mock_get_results),
            patch.object(BaseContentGenerator, "_log_generation_stats"),
        ):
            result = content_generator.gen()
            assert (
                result.variations["comp1"]["meta"]["variations"][0]["text"]
                == "disjoint generated text"
            )
            assert (
                result.variations["comp2"]["meta"]["variations"][0]["text"]
                == "disjoint generated text"
            )


class TestContentCollectionPlanGen(TestCase):

    def setUp(self):
        self.user = TofuUser.objects.create(username="testuser")
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=CompanyInfo.objects.create(),
        )
        self.playbook.users.set([self.user])
        self.campaign_instance = Campaign.objects.create(
            campaign_name="Test Campaign",
            creator=self.user,
            playbook=self.playbook,
            campaign_params={
                "targets": [{"target1": ["value1"]}],
                "foundation_model": "gpt-4o-2024-11-20",
                "campaign_goal": "Repurpose Content",
                "assets": [
                    {
                        "asset_key": "asset1",
                        "asset_type": "text",
                        "asset_value": "asset1_value",
                    }
                ],
            },
        )
        self.collection_ids = ["collection1"]
        # build content groups for this collection.
        content_group_1 = ContentGroup.objects.create(
            id="1",
            campaign=self.campaign_instance,
            creator=self.user,
            components={"comp1": {"text": "text1", "meta": {}}},
            content_group_params={
                "content_collection": {"id": "collection1", "prev": None, "next": ["2"]}
            },
            content_group_status={},
        )
        content_group_2 = ContentGroup.objects.create(
            id="2",
            campaign=self.campaign_instance,
            creator=self.user,
            components={"comp2": {"text": "text2", "meta": {}}},
            content_group_params={
                "content_collection": {
                    "id": "collection1",
                    "prev": ["1"],
                    "next": ["3"],
                }
            },
            content_group_status={},
        )
        content_group_3 = ContentGroup.objects.create(
            id="3",
            campaign=self.campaign_instance,
            creator=self.user,
            components={"comp3": {"text": "text3", "meta": {}}},
            content_group_params={
                "content_collection": {"id": "collection1", "prev": ["2"], "next": None}
            },
            content_group_status={},
        )

    @pytest.mark.django_db
    @patch("api.content_collection.upload_file")
    @patch(
        "api.content_gen.content_collection_plan_gen.ContentCollectionPlanInputBuilder"
    )
    def test_content_collection_plan_gen(
        self, mock_input_builder_class, mock_upload_file
    ):
        # Mock the upload_file function within s3_utils
        mock_upload_file.return_value = "https://mocked-s3-url.com/file.json"

        # Create a mock instance with all required attributes
        mock_input_builder_instance = MagicMock()
        mock_input_builder_instance.all_features = {
            "custom_prompts_string": "mocked custom prompts"
        }
        mock_input_builder_instance.create_llm_inputs.return_value = {}

        # Make the class return our mock instance
        mock_input_builder_class.return_value = mock_input_builder_instance

        collection_data_wrapper = BaseContentWrapper.from_data_instance(
            self.campaign_instance
        )
        collection_gen_settings = ContentGenSettings(
            collection_data_wrapper,
            num_of_variations=1,
            foundation_model="gpt-4o-2024-11-20",
            content_collection_plan_gen=True,
            save_variations=False,
        )
        collection_gen_env = GenerateEnv(
            collection_data_wrapper, collection_gen_settings
        )
        content_collection_handler = ContentCollectionHandler(
            collection_gen_env,
        )
        mock_get_results = MagicMock(
            return_value=[MagicMock(text="generated content collection plan 1")]
        )
        with patch.object(BaseContentGenerator, "get_results", mock_get_results):
            for collection_id in self.collection_ids:
                content_collection_handler.update_content_group_collections_param(
                    collection_id
                )
        # Assert that content_collection_instructions are added correctly to each content group
        for (
            content_group_id
        ) in content_collection_handler.content_collection.content_collection_map:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_collection_instructions = content_group.content_group_params.get(
                "content_collection", {}
            ).get("content_collection_instructions", [])

            assert len(content_collection_instructions) == 1
            assert "assets" in content_collection_instructions[0]
            assert "instruction" in content_collection_instructions[0]
            assert content_collection_instructions[0]["instruction"].startswith(
                "Follow the directions from this plan for the content:"
            )

            # Verify that the content_collection_map is updated
            assert (
                content_collection_handler.content_collection.content_collection_map[
                    content_group_id
                ]["content_collection_instructions"]
                == content_collection_instructions
            )


class TestReviewedContentsFeature(TestCase):

    def setUp(self):
        self.user = TofuUser.objects.create(username="testuser")
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=CompanyInfo.objects.create(),
        )
        self.playbook.users.set([self.user])
        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            creator=self.user,
            playbook=self.playbook,
            campaign_params={
                "targets": [{"target1": ["value1"]}],
                "foundation_model": "gpt-4o-2024-11-20",
                "campaign_goal": "Personalization",
            },
        )
        self.content_group = ContentGroup.objects.create(
            campaign=self.campaign,
            creator=self.user,
            components={"comp1": {"text": "text1", "meta": {}}},
            content_group_params={},
            content_group_status={},
        )
        self.content = Content.objects.create(
            content_group=self.content_group,
            content_name="content1",
            content_params={},
            content_status={},
        )

        reviewed_content = Content.objects.create(
            content_group=self.content_group,
            content_name="content2",
            content_params={},
            content_status={},
        )
        self.content_group.content_group_params["reviewed_content_list"] = [
            {
                "reviewed_time": "2024-01-01",
                "positive_example": True,
                "content_id": reviewed_content.id,
            }
        ]

    def test_reviewed_contents_feature(self):
        playbook_handler = PlaybookHandler(self.playbook)
        content_generator = ContentGenerator(playbook_handler, self.content)

        content_generator.set_settings(
            components={"comp1": {"text": "text1", "meta": {}}},
            joint_generation=False,
            foundation_model="gpt-4o-2024-11-20",
            save_variations=False,
        )
        gen_env = GenerateEnv(
            data_wrapper=content_generator._data_wrapper,
            gen_settings=content_generator._gen_settings,
        )
        gen_feature_assembler = GenerateFeatureAssembler(
            model_budget=8000,
            gen_env=gen_env,
        )
        gen_feature_assembler.build()
        with patch(
            "api.feature.feature_builder.joint_component_feature_builder.get_curr_selected_variation_json"
        ) as mock_get_variation:
            mock_get_variation.return_value = {
                "comp1": {"text": "mocked text", "word count": 2}
            }

            joint_message_builder = JointGenerationMessageBuilder(
                gen_feature_assembler=gen_feature_assembler,
                model_budget=8000,
                prev_gen_variations={},
                gen_env=gen_env,
            )
            joint_message_builder.create_llm_inputs(
                content_generator._data_wrapper.text_gen_components
            )
            features = joint_message_builder._features
            assert "reviewed_contents_string" in features
            assert "<example>" in features["reviewed_contents_string"]
            assert "mocked text" in features["reviewed_contents_string"]
