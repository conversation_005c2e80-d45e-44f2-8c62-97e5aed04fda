from django.test import TestCase as DjangoTestCase

from ...models import CompanyInfo, Playbook, TofuUser


class TofuTestCase(DjangoTestCase):
    def create_user(
        self, username, is_staff=False, is_superuser=False, email=None, password=None
    ):
        if email is None:
            email = "{}@tofuhq.com".format(username)

        if password is None:
            password = "generic password"

        if is_superuser:
            return TofuUser.objects.create_superuser(
                username=username, email=email, password=password
            )
        else:
            return TofuUser.objects.create_user(
                username=username, email=email, password=password, is_staff=is_staff
            )
