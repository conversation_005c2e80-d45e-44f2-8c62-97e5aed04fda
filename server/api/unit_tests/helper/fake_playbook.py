import factory
from django.utils import timezone
from faker import Faker

from ...models import CompanyInfo, Playbook, TofuUser

fake = Faker()


class TofuUserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TofuUser

    username = factory.Faker("user_name")
    full_name = factory.LazyAttribute(lambda obj: f"{obj.first_name} {obj.last_name}")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    email = factory.Faker("email")
    context = factory.LazyFunction(lambda: {"key": "value"})
    is_staff = False
    is_active = True
    date_joined = factory.LazyFunction(timezone.now)


class CompanyInfoFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CompanyInfo

    docs = factory.LazyFunction(lambda: {"docs_key": "docs_value"})
    meta = factory.LazyFunction(lambda: {"meta_key": "meta_value"})
    summary = factory.Faker("sentence")
    index = factory.LazyFunction(lambda: {"index_key": "index_value"})
    additional_info = factory.LazyFunction(
        lambda: {"additional_info_key": "additional_info_value"}
    )
    docs_last_build = factory.LazyFunction(
        lambda: {"last_build_key": "last_build_value"}
    )
    docs_build_status = factory.LazyFunction(
        lambda: {"build_status_key": "build_status_value"}
    )
    updated_at = factory.LazyFunction(timezone.now)
    created_at = factory.LazyFunction(timezone.now)


class PlaybookFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Playbook

    name = factory.Faker("word")
    company_domain = factory.Faker("domain_name")
    company_info = factory.LazyFunction(lambda: {"info": fake.sentence()})
    company_info_expanded = factory.LazyFunction(lambda: {"info_expanded": fake.text()})
    target_info = factory.LazyFunction(lambda: {"target": fake.word()})
    target_info_expanded = factory.LazyFunction(
        lambda: {"target_expanded": fake.text()}
    )
    assets = factory.LazyFunction(lambda: {"assets": [fake.word() for _ in range(3)]})
    assets_expanded = factory.LazyFunction(
        lambda: {"assets_expanded": [fake.text() for _ in range(3)]}
    )
    custom_instructions = factory.LazyFunction(
        lambda: {"instructions": fake.sentence()}
    )
    settings = factory.LazyFunction(lambda: {"setting": fake.word()})

    # For the OneToOneField, you might want to ensure a related object is created:
    company_object = factory.SubFactory(CompanyInfoFactory)

    # For the ManyToMany relationships, you can handle them post-generation
    # using factory's post_generation hook or manually add them in tests

    @factory.post_generation
    def users(self, create, extracted, **kwargs):
        if not create:
            # Simple build, do nothing
            return

        if extracted:
            # A list of users were passed in, use them
            for user in extracted:
                self.users.add(user)
