import factory
from api.models import TofuUser
from django.test import TestCase as DjangoTestCase
from django.utils import timezone
from faker import Faker
from rest_framework.test import APIClient

from ...models import CompanyInfo, Playbook, TofuUser

fake = Faker()


class TofuUserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TofuUser

    username = factory.Faker("user_name")
    full_name = factory.LazyAttribute(lambda obj: f"{obj.first_name} {obj.last_name}")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    email = factory.Faker("email")
    context = factory.LazyFunction(lambda: {"key": "value"})
    is_staff = False
    is_active = True
    date_joined = factory.LazyFunction(timezone.now)
