import unittest
from unittest.mock import patch

from api.utils import fuzzy_match_json_key


class TestFuzzyMatchJsonKeys(unittest.TestCase):
    @patch("api.utils.edit_distance")
    def test_closest_key_found(self, mock_edit_distance):
        # Setup mock return values for edit_distance
        # Simulate the edit distances for various keys
        def side_effect(key, json_key):
            return {"key1": 1, "key2": 3, "key3": 5}[json_key]

        mock_edit_distance.side_effect = side_effect

        json_object = {"key1": "value1", "key2": "value2", "key3": "value3"}
        closest_key = fuzzy_match_json_key("key", json_object)

        # Assert the closest key is correctly identified
        self.assertEqual(closest_key, "key1")

    @patch("api.utils.edit_distance")
    def test_no_close_match(self, mock_edit_distance):
        mock_edit_distance.return_value = 10  # No match is close enough
        json_object = {"key1": "value1", "key2": "value2"}
        closest_key = fuzzy_match_json_key("key", json_object)

        # Assert no key is found
        self.assertIsNone(closest_key)


if __name__ == "__main__":
    unittest.main()
