import unittest

from langchain_core.documents import Document

from ..playbook_build.doc_loader import DocLoader


class TestDocLoaderOptimizeDocs(unittest.TestCase):
    def test_remove_whitespace(self):
        docs = [
            Document(page_content="This is    a test   " + chr(10240) + "sentence.")
        ]
        optimized_docs = DocLoader.optimize_docs(docs)
        self.assertEqual(optimized_docs[0].page_content, "This is a test sentence.")

    def test_remove_empty_docs(self):
        docs = [Document(page_content=""), Document(page_content="Not empty")]
        optimized_docs = DocLoader.optimize_docs(docs)
        self.assertEqual(len(optimized_docs), 1)
        self.assertEqual(optimized_docs[0].page_content, "Not empty")

    def test_remove_low_signal_docs(self):
        docs = [
            Document(page_content="Too short"),
            Document(page_content="This document shall have enough content"),
        ]
        optimized_docs = DocLoader.optimize_docs(docs, remove_low_signal_docs=True)
        self.assertEqual(len(optimized_docs), 1)
        self.assertEqual(
            optimized_docs[0].page_content, "This document shall have enough content"
        )

    def test_add_metadata_to_content(self):
        docs = [
            Document(
                page_content="Content", metadata={"key_ids": "123", "source": "test"}
            )
        ]
        optimized_docs = DocLoader.optimize_docs(docs, add_metadata_to_content=True)
        expected_content_start = "<context>\n<meta>\n<keyIds>123</keyIds>\n<source>test</source>\n</meta>\n<content>\nContent\n</content>\n</context>"
        self.assertTrue(
            optimized_docs[0].page_content.startswith(expected_content_start)
        )


if __name__ == "__main__":
    unittest.main()
