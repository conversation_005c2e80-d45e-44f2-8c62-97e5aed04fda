import pytest

from ..model_config import ModelConfigResolver
from ..task_registry import GenerationGoal, TaskRegistry


# For each task in task registry, check that it has budget_limit, default_model, internal_default_model, model_fallback, model_config
def test_task_registry_completeness():
    required_fields = [
        "budget_limit",
        "default_model",
        "internal_default_model",
        "model_fallback",
        "model_config",
    ]

    for goal in GenerationGoal:
        task_config = TaskRegistry.get_task_registry(goal)
        for field in required_fields:
            assert (
                field in task_config
            ), f"Field '{field}' missing in {goal.value} task config"

        # Check that model_fallback and model_config have entries for each model
        all_models = set(task_config["model_config"].keys())
        fallback_models = set(task_config["model_fallback"].keys())
        assert (
            all_models == fallback_models
        ), f"Mismatch in models for {goal.value}: model_config and model_fallback don't have the same models"

        # Check that default_model and internal_default_model are valid
        assert (
            task_config["default_model"] in all_models
        ), f"Invalid default_model for {goal.value}"
        assert (
            task_config["internal_default_model"] in all_models
        ), f"Invalid internal_default_model for {goal.value}"


@pytest.mark.parametrize("goal", GenerationGoal)
def test_resolve_with_default_model_for_all_goals(goal):
    model_config = ModelConfigResolver.resolve(goal)
    task_registry = TaskRegistry.get_task_registry(goal)
    expected_default_model = task_registry["default_model"]

    if "budget_limit" in task_registry["model_config"][expected_default_model]:
        assert (
            model_config.model_budget
            == task_registry["model_config"][expected_default_model]["budget_limit"]
        )
    else:
        assert model_config.model_budget == task_registry["budget_limit"]
    assert len(model_config.model_params_list) == 1 + len(
        task_registry["model_fallback"][expected_default_model]
    )
    assert model_config.model_params_list[0].model_name == expected_default_model
    expected_model_params = task_registry["model_config"][expected_default_model]
    if goal in [
        GenerationGoal.GENERATION,
        GenerationGoal.GENERATION_REPURPOSING,
    ]:
        expected_model_params["n"] = 1
    expected_model_params.pop("budget_limit", None)
    assert model_config.model_params_list[0].model_params == expected_model_params
    for i, fallback_model in enumerate(
        [expected_default_model]
        + task_registry["model_fallback"][expected_default_model]
    ):
        assert model_config.model_params_list[i].model_name == fallback_model
        expected_model_params = task_registry["model_config"][fallback_model]
        if goal in [
            GenerationGoal.GENERATION,
            GenerationGoal.GENERATION_REPURPOSING,
        ]:
            if "deepseek" not in fallback_model:
                expected_model_params["n"] = 1
        expected_model_params.pop("budget_limit", None)
        assert model_config.model_params_list[i].model_params == expected_model_params


@pytest.mark.parametrize("goal", GenerationGoal)
def test_resolve_with_specified_model_for_all_goals(goal):
    task_registry = TaskRegistry.get_task_registry(goal)
    for specified_model in task_registry["model_config"]:
        model_config = ModelConfigResolver.resolve(
            goal, foundation_model=specified_model
        )

        if "budget_limit" in task_registry["model_config"][specified_model]:
            assert (
                model_config.model_budget
                == task_registry["model_config"][specified_model]["budget_limit"]
            )
        else:
            assert model_config.model_budget == task_registry["budget_limit"]
        expected_fallback_count = len(
            task_registry["model_fallback"].get(specified_model, [])
        )
        assert (
            len(model_config.model_params_list) == 1 + expected_fallback_count
        ), f"Incorrect fallback count for {specified_model} in {goal}"
        assert model_config.model_params_list[0].model_name == specified_model
        expected_model_params = task_registry["model_config"][specified_model]
        if goal in [
            GenerationGoal.GENERATION,
            GenerationGoal.GENERATION_REPURPOSING,
        ]:
            if "deepseek" not in specified_model:
                expected_model_params["n"] = 1
        expected_model_params.pop("budget_limit", None)
        assert model_config.model_params_list[0].model_params == expected_model_params

        # Check fallback models if any
        for i, fallback_model in enumerate(
            task_registry["model_fallback"].get(specified_model, []), 1
        ):
            assert (
                model_config.model_params_list[i].model_name == fallback_model
            ), f"Incorrect fallback model at position {i} for {specified_model} in {goal}"
            expected_model_params = task_registry["model_config"][fallback_model]
            if goal in [
                GenerationGoal.GENERATION,
                GenerationGoal.GENERATION_REPURPOSING,
            ]:
                if "deepseek" not in fallback_model:
                    expected_model_params["n"] = 1
            expected_model_params.pop("budget_limit", None)
            assert (
                model_config.model_params_list[i].model_params == expected_model_params
            ), f"Incorrect fallback model at position {i} for {specified_model} in {goal}"


def test_resolve_with_invalid_goal():
    with pytest.raises(ValueError, match="Invalid generation goal: invalid_goal"):
        ModelConfigResolver.resolve("invalid_goal")


@pytest.mark.parametrize("goal", GenerationGoal)
def test_resolve_with_invalid_model_for_all_goals(goal):
    with pytest.raises(KeyError, match="'invalid_model'"):
        ModelConfigResolver.resolve(goal, foundation_model="invalid_model")


@pytest.mark.parametrize("goal", GenerationGoal)
def test_resolve_with_fallback_models_for_all_goals(goal):
    model_config = ModelConfigResolver.resolve(goal)
    task_registry = TaskRegistry.get_task_registry(goal)
    expected_default_model = task_registry["default_model"]

    assert model_config.model_budget == task_registry["budget_limit"]
    assert len(model_config.model_params_list) == 1 + len(
        task_registry["model_fallback"][expected_default_model]
    )
    assert model_config.model_params_list[0].model_name == expected_default_model
    assert (
        model_config.model_params_list[0].model_params
        == task_registry["model_config"][expected_default_model]
    )
    for i, fallback_model in enumerate(
        task_registry["model_fallback"][expected_default_model], 1
    ):
        assert model_config.model_params_list[i].model_name == fallback_model
        expected_model_params = task_registry["model_config"][fallback_model]
        expected_model_params.pop("budget_limit", None)
        assert model_config.model_params_list[i].model_params == expected_model_params
