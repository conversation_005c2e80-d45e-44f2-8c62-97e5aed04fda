import uuid

from django.contrib.admin.sites import AdminSite
from django.contrib.auth import get_user_model
from django.test import Client, TestCase
from django.urls import reverse

from ..admin import (
    CampaignAdmin,
    CampaignTemplateForm,
    ContentGroupAdmin,
    PlaybookAdmin,
    TargetInfoGroupAdmin,
    TofuUserAdmin,
    TofuUserForm,
)
from ..models import (
    Campaign,
    CompanyInfo,
    ContentGroup,
    Playbook,
    Tag,
    TargetInfoGroup,
    TofuUser,
)


class MockRequest:
    def __init__(self):
        self.user = None


class AdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.client = Client()

        # Create superuser
        self.admin_user = get_user_model().objects.create_superuser(
            username="admin", email="<EMAIL>", password="admin123"
        )
        self.client.login(username="admin", password="admin123")

        # Create regular user
        self.user = get_user_model().objects.create_user(
            username="testuser", email="<EMAIL>", password="test123"
        )


class TofuUserAdminTest(AdminTest):
    def setUp(self):
        super().setUp()
        self.user_admin = TofuUserAdmin(TofuUser, self.site)

    def test_get_verified_user(self):
        # Test user with no context
        user = TofuUser.objects.create(username="nocontext")
        self.assertTrue(self.user_admin.get_verified_user(user))

        # Test user with some context
        user_with_context = TofuUser.objects.create(
            username="withcontext", context={"verified_user": False}
        )
        self.assertFalse(self.user_admin.get_verified_user(user_with_context))

    def test_get_model(self):
        user = TofuUser.objects.create(username="modeluser", context={"model": "gpt-4"})
        self.assertEqual(self.user_admin.get_model(user), "gpt-4")

    def test_password_change_form_access(self):
        # Test accessing the password change form
        user = TofuUser.objects.create_user(
            username="testuser_form", password="oldpass123"
        )
        url = reverse("admin:auth_user_password_change", args=[user.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_password_change_functionality(self):
        # Test changing a user's password
        user = TofuUser.objects.create_user(
            username="testuser_func", password="oldpass123"
        )
        url = reverse("admin:auth_user_password_change", args=[user.id])

        # Try changing the password
        data = {
            "password1": "newpass123",
            "password2": "newpass123",
        }
        response = self.client.post(url, data)

        # Should redirect after successful password change
        self.assertEqual(response.status_code, 302)

        # Verify the password was changed
        user.refresh_from_db()
        self.assertTrue(user.check_password("newpass123"))

    def test_password_change_validation(self):
        # Test password validation
        user = TofuUser.objects.create_user(
            username="testuser_val", password="oldpass123"
        )
        url = reverse("admin:auth_user_password_change", args=[user.id])

        # Try changing with mismatched passwords
        data = {
            "password1": "newpass123",
            "password2": "differentpass123",
        }
        response = self.client.post(url, data)

        # Should stay on the same page with error
        self.assertEqual(response.status_code, 200)
        # Check for form errors. Our form uses regular ASCII chars in error messages.
        # Django error message uses Unicode apostrophe \u2019 which is not the same as the ASCII apostrophe
        self.assertFormError(
            response,
            "form",
            "password2",
            ["The two password fields didn't match."],
        )

        # Verify the password was not changed
        user.refresh_from_db()
        self.assertTrue(user.check_password("oldpass123"))

    def test_password_hashing(self):
        # Test that passwords are properly hashed
        user = TofuUser.objects.create_user(
            username="testuser_hash", password="oldpass123"
        )
        url = reverse("admin:auth_user_password_change", args=[user.id])

        data = {
            "password1": "newpass123",
            "password2": "newpass123",
        }
        self.client.post(url, data)

        # Refresh user from database
        user.refresh_from_db()

        # Verify the password is hashed
        self.assertNotEqual(user.password, "newpass123")
        self.assertTrue(user.password.startswith("pbkdf2_sha256$"))

    def test_password_change_with_authentication_api(self):
        # Create a test user
        user = TofuUser.objects.create_user(
            username="testuser_api", password="oldpass123"
        )
        url = reverse("admin:auth_user_password_change", args=[user.id])

        # Change password through admin interface
        data = {
            "password1": "newpass123",
            "password2": "newpass123",
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302)  # Should redirect after success

        # Now verify the new password using the authentication API
        auth_url = reverse("user-authentication")
        auth_data = {"username": "testuser_api", "password": "newpass123"}
        auth_response = self.client.post(auth_url, auth_data)

        # Check that authentication succeeds with new password
        self.assertEqual(auth_response.status_code, 200)
        self.assertEqual(auth_response.data["username"], "testuser_api")

        # Try with old password - should fail
        auth_data["password"] = "oldpass123"
        auth_response = self.client.post(auth_url, auth_data)
        self.assertEqual(auth_response.status_code, 401)
        self.assertEqual(auth_response.data["message"], "Invalid credentials")


class PlaybookAdminTest(AdminTest):
    def setUp(self):
        super().setUp()
        self.playbook_admin = PlaybookAdmin(Playbook, self.site)

        # Create a CompanyInfo object and convert to dict
        company_info = CompanyInfo.objects.create()
        company_info_dict = {"docs": company_info.docs, "meta": company_info.meta}

        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            settings={"enableDataStructureV2": True, "enableFakeMetrics": False},
            company_info=company_info_dict,  # Use dict instead of object
            company_object=company_info,
        )

    def test_get_enable_data_structure_v2(self):
        self.assertTrue(self.playbook_admin.get_enable_data_structure_v2(self.playbook))

    def test_get_enable_fake_metrics(self):
        self.assertFalse(self.playbook_admin.get_enable_fake_metrics(self.playbook))


class CampaignAdminTest(AdminTest):
    def setUp(self):
        super().setUp()
        self.campaign_admin = CampaignAdmin(Campaign, self.site)

        # Create CompanyInfo and convert to dict
        company_info = CompanyInfo.objects.create()
        company_info_dict = {"docs": company_info.docs, "meta": company_info.meta}

        # Create Playbook with dict
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_info=company_info_dict,
            company_object=company_info,
        )

        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            campaign_params={
                "suggested_campaign": True,
                "suggested_campaign_desc": "Test description",
            },
            creator=self.user,  # Add creator
            playbook=self.playbook,  # Add playbook
        )

    def test_tags_list(self):
        # Create tag with required creator field
        tag = Tag.objects.create(
            name="Test Tag", creator=self.user  # Add required creator
        )
        self.campaign.campaignTags.add(tag)
        self.assertEqual(self.campaign_admin.tags_list(self.campaign), "Test Tag")


class TargetInfoGroupAdminTest(AdminTest):
    def setUp(self):
        super().setUp()
        self.target_group_admin = TargetInfoGroupAdmin(TargetInfoGroup, self.site)

        # Create CompanyInfo and convert to dict
        company_info = CompanyInfo.objects.create()
        company_info_dict = {"docs": company_info.docs, "meta": company_info.meta}

        # Create Playbook with dict
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_info=company_info_dict,
            company_object=company_info,
        )

        self.target_group = TargetInfoGroup.objects.create(
            target_info_group_key="test-key",
            meta={"autopilot_duration": 30},
            playbook=self.playbook,  # Add required playbook
        )

    def test_get_autopilot_duration(self):
        self.assertEqual(
            self.target_group_admin.get_autopilot_duration(self.target_group), 30
        )

    def test_validate_autopilot_duration(self):
        from ..admin import validate_autopilot_duration

        # Test valid duration
        self.assertEqual(validate_autopilot_duration(30), 30)

        # Test duration below minimum
        self.assertEqual(validate_autopilot_duration(0), 1)

        # Test duration above maximum
        self.assertEqual(validate_autopilot_duration(2000), 1440)

        # Test invalid type
        self.assertEqual(validate_autopilot_duration("invalid"), 20)


class ContentGroupAdminTest(AdminTest):
    def setUp(self):
        super().setUp()
        self.content_group_admin = ContentGroupAdmin(ContentGroup, self.site)

        # Create CompanyInfo and convert to dict
        company_info = CompanyInfo.objects.create()
        company_info_dict = {"docs": company_info.docs, "meta": company_info.meta}

        # Create Playbook with dict
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_info=company_info_dict,
            company_object=company_info,
        )

        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign", creator=self.user, playbook=self.playbook
        )

        # Create content group for tests
        self.content_group = ContentGroup.objects.create(
            content_group_name="Test Content Group",
            campaign=self.campaign,
            creator=self.user,
        )


class AdminViewsTest(AdminTest):
    def test_admin_login(self):
        response = self.client.get(reverse("admin:index"))
        self.assertEqual(response.status_code, 200)

    def test_tofu_user_admin_view(self):
        response = self.client.get(reverse("admin:api_tofuuser_changelist"))
        self.assertEqual(response.status_code, 200)

    def test_playbook_admin_view(self):
        response = self.client.get(reverse("admin:api_playbook_changelist"))
        self.assertEqual(response.status_code, 200)

    def test_campaign_admin_view(self):
        response = self.client.get(reverse("admin:api_campaign_changelist"))
        self.assertEqual(response.status_code, 200)


class CampaignTemplateFormTest(TestCase):
    def setUp(self):
        self.form_data = {
            "category": "ABM",
            "is_active": True,
            "deliverables": "Email sequence\nLanding page\nAds-General",
            "image_entry_point": "https://tofu-public-files.s3.us-east-2.amazonaws.com/test.png",
            "anchor_description": "Test description",
            "image_template_modal": "https://tofu-public-files.s3.us-east-2.amazonaws.com/test_modal.png",
            "template_description": "Test template description",
        }

    def test_valid_form(self):
        form = CampaignTemplateForm(data=self.form_data)
        self.assertTrue(form.is_valid())

    def test_invalid_image_url_format(self):
        # Test with incorrect S3 URL format
        invalid_data = self.form_data.copy()
        invalid_data["image_entry_point"] = (
            "https://wrong-bucket.amazonaws.com/test.png"
        )
        form = CampaignTemplateForm(data=invalid_data)
        self.assertFalse(form.is_valid())
        self.assertIn("image_entry_point", form.errors)


class CampaignTemplateAdminTest(AdminTest):
    def setUp(self):
        super().setUp()
        # Create CompanyInfo and convert to dict
        company_info = CompanyInfo.objects.create()
        company_info_dict = {"docs": company_info.docs, "meta": company_info.meta}

        # Create Playbook with dict
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_info=company_info_dict,
            company_object=company_info,
        )

        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            creator=self.user,
            playbook=self.playbook,
            campaign_params={
                "campaign_template": {
                    "category": "Updated ABM",
                    "is_active": True,
                    "deliverables": "Email sequence\nLanding page",
                    "image_entry_point": "https://tofu-public-files.s3.us-east-2.amazonaws.com/test.png",
                    "anchor_description": "Test description",
                    "image_template_modal": "https://tofu-public-files.s3.us-east-2.amazonaws.com/test_modal.png",
                    "template_description": "Test template",
                }
            },
        )

    def test_edit_template_view_get(self):
        url = reverse("admin:api_campaign_edit_template", args=[self.campaign.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(
            response, "admin/api/campaign/campaign_template_form.html"
        )

    def test_template_url_validation(self):
        url = reverse("admin:api_campaign_edit_template", args=[self.campaign.id])

        # Test with invalid S3 URL
        data = {
            "category": "ABM",
            "is_active": True,
            "deliverables": "Test deliverables",
            "image_entry_point": "https://wrong-bucket.amazonaws.com/test.png",
            "anchor_description": "Test description",
            "template_description": "Test template",
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)  # Form should return with errors
        self.assertFormError(
            response,
            "form",
            "image_entry_point",
            "URL must start with 'https://tofu-public-files.s3.us-east-2.amazonaws.com/'",
        )
