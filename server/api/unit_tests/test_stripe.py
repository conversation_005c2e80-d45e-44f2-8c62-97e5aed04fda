import logging
import os
from unittest.mock import MagicMock, patch

import pytest
import stripe
from api.models import TofuUser
from api.stripe import (
    cancel_subscription,
    change_subscription,
    get_checkout_session_id_from_payment_intent,
    get_checkout_session_tofu_user_id,
    handle_payment_failure,
    handle_payment_success,
    handle_stripe_event,
    has_subscription,
    link_tofu_user_checkout_session_customer_id,
    link_tofu_user_to_checkout_session,
)
from django.test import TestCase


class TestStripeFunctions(TestCase):
    def setUp(self):
        self.test_user = TofuUser.objects.create(
            username="testuser",
            email="<EMAIL>",
            stripe_customer_id="cus_test123",
            stripe_checkout_session_id="cs_test123",
        )
        self.test_payment_intent = MagicMock()
        self.test_payment_intent.id = "pi_test123"
        self.test_payment_intent.customer = "cus_test123"

    @patch("api.stripe.stripe.api_key")
    def test_handle_stripe_event_success(self, mock_stripe_api_key):
        # Mock Stripe API key
        mock_stripe_api_key = "sk_test123"

        # Create a mock event
        mock_event = MagicMock()
        mock_event.type = "payment_intent.succeeded"
        mock_event.data.object = self.test_payment_intent

        # Mock handle_payment_success
        with patch("api.stripe.handle_payment_success") as mock_handle_success:
            handle_stripe_event(mock_event)
            mock_handle_success.assert_called_once_with(self.test_payment_intent)

    @patch("api.stripe.stripe.api_key")
    def test_handle_stripe_event_failure(self, mock_stripe_api_key):
        # Mock Stripe API key
        mock_stripe_api_key = "sk_test123"

        # Create a mock event
        mock_event = MagicMock()
        mock_event.type = "payment_intent.payment_failed"
        mock_event.data.object = self.test_payment_intent

        # Mock handle_payment_failure
        with patch("api.stripe.handle_payment_failure") as mock_handle_failure:
            handle_stripe_event(mock_event)
            mock_handle_failure.assert_called_once_with(self.test_payment_intent)

    @patch("api.stripe.stripe.api_key")
    def test_handle_stripe_event_invalid(self, mock_stripe_api_key):
        # Mock Stripe API key
        mock_stripe_api_key = "sk_test123"

        # Create an invalid event
        mock_event = MagicMock()
        mock_event.type = None

        # Test that invalid event raises ValueError
        with self.assertRaises(ValueError):
            handle_stripe_event(mock_event)

    @patch("api.stripe.get_checkout_session_id_from_payment_intent")
    @patch("api.stripe.reset_credits_for_tofu_lite")
    @patch("api.stripe.stripe.Invoice.retrieve")
    @patch("api.stripe.stripe.Subscription.retrieve")
    def test_handle_payment_success_with_customer(
        self,
        mock_subscription_retrieve,
        mock_invoice_retrieve,
        mock_reset_credits,
        mock_get_session_id,
    ):
        # Mock invoice and subscription IDs
        self.test_payment_intent.invoice = "inv_test123"
        mock_invoice = MagicMock()
        mock_invoice.subscription = "sub_test123"
        mock_invoice_retrieve.return_value = mock_invoice

        # Mock subscription with tofu lite price
        mock_subscription = {
            "items": {
                "data": [
                    {
                        "price": {
                            "product": "prod_RXsnaNmDJ4fwEk"  # Using actual product ID
                        }
                    }
                ]
            }
        }
        mock_subscription_retrieve.return_value = mock_subscription

        # Mock checkout session ID retrieval
        mock_get_session_id.return_value = "cs_test123"

        # Mock TofuUser.objects.filter
        with patch("api.stripe.TofuUser.objects.filter") as mock_filter:
            mock_filter.return_value.first.return_value = self.test_user

            # Call the function
            handle_payment_success(self.test_payment_intent)

            # Assert reset_credits was called with payment_info parameter
            mock_reset_credits.assert_called_once_with(
                self.test_user.id, payment_info=self.test_payment_intent
            )

    @patch("api.stripe.stripe.Invoice.retrieve")
    def test_handle_payment_success_no_invoice(self, mock_invoice_retrieve):
        # Create payment intent without invoice
        payment_intent = MagicMock()
        payment_intent.id = "pi_test123"
        payment_intent.invoice = None

        # Test that it raises ValueError
        with self.assertRaises(ValueError) as exc_info:
            handle_payment_success(payment_intent)
        self.assertEqual(
            str(exc_info.exception), "No invoice id found for payment intent pi_test123"
        )

    @patch("api.stripe.stripe.Invoice.retrieve")
    def test_handle_payment_success_invoice_retrieval_error(
        self, mock_invoice_retrieve
    ):
        # Mock invoice retrieval error
        mock_invoice_retrieve.side_effect = Exception("API Error")

        # Call the function
        handle_payment_success(self.test_payment_intent)

        # Assert no further processing occurred
        self.test_user.refresh_from_db()
        self.assertEqual(
            self.test_user.tofu_lite_subscription_tier,
            TofuUser.TofuLiteSubscriptionTier.FREE_TIER,
        )

    @patch("api.stripe.stripe.Invoice.retrieve")
    @patch("api.stripe.stripe.Subscription.retrieve")
    def test_handle_payment_success_no_tofu_lite_price(
        self, mock_subscription_retrieve, mock_invoice_retrieve
    ):
        # Mock invoice
        mock_invoice = MagicMock()
        mock_invoice.subscription = "sub_test123"
        mock_invoice_retrieve.return_value = mock_invoice

        # Mock subscription without tofu lite price
        mock_subscription = MagicMock()
        mock_subscription.items = MagicMock()
        mock_subscription.items.data = [{"price": {"product": "prod_other"}}]
        mock_subscription_retrieve.return_value = mock_subscription

        # Call the function
        handle_payment_success(self.test_payment_intent)

        # Assert no further processing occurred
        self.test_user.refresh_from_db()
        self.assertEqual(
            self.test_user.tofu_lite_subscription_tier,
            TofuUser.TofuLiteSubscriptionTier.FREE_TIER,
        )

    @patch("api.stripe.stripe.Invoice.retrieve")
    @patch("api.stripe.stripe.Subscription.retrieve")
    def test_handle_payment_success_subscription_retrieval_error(
        self, mock_subscription_retrieve, mock_invoice_retrieve
    ):
        # Mock invoice
        mock_invoice = MagicMock()
        mock_invoice.subscription = "sub_test123"
        mock_invoice_retrieve.return_value = mock_invoice

        # Mock subscription retrieval error
        mock_subscription_retrieve.side_effect = Exception("API Error")

        # Call the function
        handle_payment_success(self.test_payment_intent)

        # Assert no further processing occurred
        self.test_user.refresh_from_db()
        self.assertEqual(
            self.test_user.tofu_lite_subscription_tier,
            TofuUser.TofuLiteSubscriptionTier.FREE_TIER,
        )

    @patch("api.stripe.stripe.checkout.Session.list")
    def test_get_checkout_session_id_from_payment_intent_success(
        self, mock_session_list
    ):
        # Mock Stripe API response
        mock_session = MagicMock()
        mock_session.id = "cs_test123"
        mock_session_list.return_value.data = [mock_session]

        # Call the function
        session_id = get_checkout_session_id_from_payment_intent(
            self.test_payment_intent
        )

        # Assert result
        self.assertEqual(session_id, "cs_test123")

    @patch("api.stripe.stripe.checkout.Session.list")
    def test_get_checkout_session_id_from_payment_intent_failure(
        self, mock_session_list
    ):
        # Mock empty Stripe API response
        mock_session_list.return_value.data = []

        # Test that it raises Exception
        with pytest.raises(Exception):
            get_checkout_session_id_from_payment_intent(self.test_payment_intent)

    @patch("api.stripe.link_tofu_user_checkout_session_customer_id")
    def test_get_checkout_session_tofu_user_id_success(self, mock_link_customer):
        # Mock the function
        mock_link_customer.return_value = "cus_test123"

        # Call the function
        user_id = get_checkout_session_tofu_user_id("cs_test123")

        # Assert result
        self.assertEqual(user_id, self.test_user.id)

    @patch("api.stripe.link_tofu_user_checkout_session_customer_id")
    def test_get_checkout_session_tofu_user_id_failure(self, mock_link_customer):
        # Mock the function to return None
        mock_link_customer.return_value = None

        # Mock TofuUser.objects.filter to return None
        with patch("api.stripe.TofuUser.objects.filter") as mock_filter:
            mock_filter.return_value.first.return_value = None

            # Call the function
            user_id = get_checkout_session_tofu_user_id("cs_test123")

            # Assert result
            self.assertIsNone(user_id)

    def test_link_tofu_user_to_checkout_session_success(self):
        # Call the function
        result = link_tofu_user_to_checkout_session(self.test_user.id, "cs_new123")

        # Assert result
        self.assertTrue(result)

        # Refresh user from database
        self.test_user.refresh_from_db()
        self.assertEqual(self.test_user.stripe_checkout_session_id, "cs_new123")

    def test_link_tofu_user_to_checkout_session_failure(self):
        # Test with invalid user ID
        with patch(
            "api.stripe.TofuUser.objects.select_for_update"
        ) as mock_select_for_update:
            # Set up the mock to raise DoesNotExist when get() is called
            mock_select_for_update.return_value.get.side_effect = (
                TofuUser.DoesNotExist()
            )

            # Call the function and verify it returns False
            result = link_tofu_user_to_checkout_session(999999, "cs_new123")
            self.assertFalse(result)
            mock_select_for_update.return_value.get.assert_called_once_with(id=999999)

    @patch("api.stripe.stripe.checkout.Session.retrieve")
    def test_link_tofu_user_checkout_session_customer_id_success(self, mock_retrieve):
        # Mock Stripe API response
        mock_session = MagicMock()
        mock_session.customer = "cus_new123"
        mock_retrieve.return_value = mock_session

        # Call the function
        customer_id = link_tofu_user_checkout_session_customer_id(self.test_user.id)

        # Assert result
        self.assertEqual(customer_id, "cus_new123")

        # Refresh user from database
        self.test_user.refresh_from_db()
        self.assertEqual(self.test_user.stripe_customer_id, "cus_new123")

    @patch("api.stripe.stripe.checkout.Session.retrieve")
    def test_link_tofu_user_checkout_session_customer_id_failure(self, mock_retrieve):
        # Mock Stripe API error
        mock_retrieve.side_effect = Exception("API Error")

        # Call the function
        customer_id = link_tofu_user_checkout_session_customer_id(self.test_user.id)

        # Assert result
        self.assertIsNone(customer_id)

    @patch("api.stripe.stripe.Subscription.list")
    def test_has_subscription_success(self, mock_subscription_list):
        # Mock Stripe API response
        mock_subscription = MagicMock()
        mock_subscription_list.return_value.data = [mock_subscription]

        # Call the function
        result = has_subscription(self.test_user.id)

        # Assert result
        self.assertTrue(result)

    @patch("api.stripe.stripe.Subscription.list")
    def test_has_subscription_no_subscription(self, mock_subscription_list):
        # Mock empty Stripe API response
        mock_subscription_list.return_value.data = []

        # Call the function
        result = has_subscription(self.test_user.id)

        # Assert result
        self.assertFalse(result)

    @patch("api.stripe.stripe.Subscription.cancel")
    def test_cancel_subscription_success(self, mock_cancel):
        # Mock Stripe API response
        mock_subscription = MagicMock()
        mock_subscription.id = "sub_test123"
        mock_subscription_list = MagicMock()
        mock_subscription_list.data = [mock_subscription]

        # Mock _get_active_subscriptions
        with patch("api.stripe._get_active_subscriptions") as mock_get_subscriptions:
            mock_get_subscriptions.return_value = (
                self.test_user,
                mock_subscription_list.data,
            )

            # Call the function
            cancel_subscription(self.test_user.id)

            # Assert Stripe API was called
            mock_cancel.assert_called_once_with("sub_test123")

            # Refresh user from database
            self.test_user.refresh_from_db()
            self.assertEqual(
                self.test_user.tofu_lite_subscription_tier,
                TofuUser.TofuLiteSubscriptionTier.FREE_TIER,
            )

    @patch("api.stripe.stripe.Subscription.cancel")
    def test_cancel_subscription_failure(self, mock_cancel):
        # Mock Stripe API error
        mock_cancel.side_effect = Exception("API Error")

        # Mock subscription
        mock_subscription = MagicMock()
        mock_subscription.id = "sub_test123"
        mock_subscription_list = MagicMock()
        mock_subscription_list.data = [mock_subscription]

        # Mock _get_active_subscriptions
        with patch("api.stripe._get_active_subscriptions") as mock_get_subscriptions:
            mock_get_subscriptions.return_value = (
                self.test_user,
                mock_subscription_list.data,
            )

            # Test that it raises Exception with the correct message
            with pytest.raises(Exception) as exc_info:
                cancel_subscription(self.test_user.id)
            self.assertEqual(
                str(exc_info.value),
                "Error cancelling stripe subscription: Error cancelling subscription sub_test123: API Error",
            )

    @patch("api.stripe.stripe")
    @patch("api.stripe.logging")
    def test_handle_payment_failure_non_subscription(self, mock_logging, mock_stripe):
        # Test case for non-subscription payment failure
        payment_intent = MagicMock()
        payment_intent.invoice = None

        # Call the function
        handle_payment_failure(payment_intent)

        # Assert logging.info was called for non-subscription payment
        mock_logging.info.assert_called_once_with(
            "Ignoring payment failure for non-subscription payment (no invoice ID)"
        )

    @patch("api.stripe.stripe")
    @patch("api.stripe.logging")
    def test_handle_payment_failure_subscription(self, mock_logging, mock_stripe):
        # Test case for subscription payment failure
        payment_intent = MagicMock()
        payment_intent.invoice = "inv_123"

        # Mock the invoice retrieval
        mock_invoice = MagicMock()
        mock_invoice.subscription = "sub_123"
        mock_stripe.Invoice.retrieve.return_value = mock_invoice

        # Call the function
        handle_payment_failure(payment_intent)

        # Assert stripe.Invoice.retrieve was called
        mock_stripe.Invoice.retrieve.assert_called_once_with("inv_123")

        # Assert logging.error was called with the payment intent
        mock_logging.error.assert_called_once_with(
            f"Payment intent failed: {payment_intent}"
        )

    @patch("api.stripe.stripe")
    @patch("api.stripe.logging")
    def test_handle_payment_failure_stripe_error(self, mock_logging, mock_stripe):
        # Test case for Stripe API error
        payment_intent = MagicMock()
        payment_intent.invoice = "inv_123"

        # Mock Stripe error
        mock_stripe.Invoice.retrieve.side_effect = Exception("API Error")

        # Call the function
        handle_payment_failure(payment_intent)

        # Assert error was logged
        mock_logging.error.assert_called_once_with(
            "Error retrieving invoice inv_123: API Error"
        )

    @patch("api.stripe.stripe")
    @patch("api.stripe.logging")
    def test_handle_payment_failure_no_subscription(self, mock_logging, mock_stripe):
        # Test case for invoice without subscription
        payment_intent = MagicMock()
        payment_intent.invoice = "inv_123"

        # Mock invoice without subscription
        mock_invoice = MagicMock()
        mock_invoice.subscription = None
        mock_stripe.Invoice.retrieve.return_value = mock_invoice

        # Call the function
        handle_payment_failure(payment_intent)

        # Assert warning was logged
        mock_logging.warning.assert_called_once_with(
            "No subscription id found for invoice inv_123"
        )

    @patch("api.stripe.get_checkout_session_id_from_payment_intent")
    @patch("api.stripe.get_checkout_session_tofu_user_id")
    @patch("api.stripe.reset_credits_for_tofu_lite")
    @patch("api.stripe.stripe.Invoice.retrieve")
    def test_handle_payment_success_non_subscription_payment(
        self,
        mock_invoice_retrieve,
        mock_reset_credits,
        mock_get_user_id,
        mock_get_session_id,
    ):
        # Mock payment intent with invoice ID
        self.test_payment_intent.invoice = "inv_test123"

        # Mock invoice without subscription
        mock_invoice = MagicMock()
        mock_invoice.subscription = None
        mock_invoice_retrieve.return_value = mock_invoice

        # Call the function
        handle_payment_success(self.test_payment_intent)

        # Assert that reset_credits was not called, indicating early return
        mock_reset_credits.assert_not_called()

        # Assert that other functions were not called
        mock_get_session_id.assert_not_called()
        mock_get_user_id.assert_not_called()


class TestStripeChangeSubscription(TestCase):
    def setUp(self):
        self.test_user = TofuUser.objects.create(
            username="testuser",
            email="<EMAIL>",
            stripe_customer_id="cus_test123",
            tofu_lite_subscription_tier=TofuUser.TofuLiteSubscriptionTier.TIER_1,
        )
        self.test_price_id = "price_test123"

    @patch("api.stripe._get_active_subscriptions")
    @patch("stripe.Subscription.modify")
    @patch("stripe.SubscriptionItem.list")
    @patch("api.stripe.get_tier_from_price_id")
    def test_change_subscription_success(
        self, mock_get_tier, mock_list, mock_modify, mock_get_subscriptions
    ):
        # Mock active subscriptions
        mock_subscription = MagicMock()
        mock_subscription.id = "sub_test123"
        mock_get_subscriptions.return_value = (self.test_user, [mock_subscription])

        # Mock the modify call to simulate a successful API call
        mock_modify.return_value = MagicMock()

        # Mock the list call to return a controlled response
        mock_subscription_item = MagicMock()
        mock_subscription_item.id = "sub_item_id"
        mock_list.return_value.data = [mock_subscription_item]

        # Mock the get_tier_from_price_id to return a valid tier
        mock_get_tier.return_value = TofuUser.TofuLiteSubscriptionTier.TIER_2

        # Call the function
        change_subscription(self.test_user.id, self.test_price_id)

        # Assert that the subscription was modified
        self.assertEqual(mock_modify.call_count, 2)

        # Refresh user from database and check tier
        self.test_user.refresh_from_db()
        self.assertEqual(
            self.test_user.tofu_lite_subscription_tier,
            TofuUser.TofuLiteSubscriptionTier.TIER_2,
        )

    @patch("api.stripe._get_active_subscriptions")
    def test_change_subscription_multiple_subscriptions(self, mock_get_subscriptions):
        # Mock multiple active subscriptions
        mock_subscription1 = MagicMock()
        mock_subscription2 = MagicMock()
        mock_get_subscriptions.return_value = (
            self.test_user,
            [mock_subscription1, mock_subscription2],
        )

        # Test that it raises ValueError
        with self.assertRaises(Exception):
            change_subscription(self.test_user.id, self.test_price_id)

    @patch("api.stripe._get_active_subscriptions")
    @patch("stripe.Subscription.modify")
    @patch("stripe.SubscriptionItem.list")
    def test_change_subscription_invalid_price_id(
        self, mock_list, mock_modify, mock_get_subscriptions
    ):
        # Mock active subscriptions
        mock_subscription = MagicMock()
        mock_subscription.id = "sub_test123"
        mock_get_subscriptions.return_value = (self.test_user, [mock_subscription])

        # Mock subscription item list
        mock_subscription_item = MagicMock()
        mock_subscription_item.id = "sub_item_id"
        mock_list.return_value.data = [mock_subscription_item]

        # Mock modify to raise an exception
        mock_modify.side_effect = Exception("Invalid price ID")

        # Test that it raises Exception
        with self.assertRaises(Exception):
            change_subscription(self.test_user.id, "invalid_price_id")

    @patch("api.stripe._get_active_subscriptions")
    @patch("stripe.Subscription.modify")
    @patch("stripe.SubscriptionItem.list")
    def test_change_subscription_stripe_error(
        self, mock_list, mock_modify, mock_get_subscriptions
    ):
        # Mock active subscriptions
        mock_subscription = MagicMock()
        mock_subscription.id = "sub_test123"
        mock_get_subscriptions.return_value = (self.test_user, [mock_subscription])

        # Mock subscription item list
        mock_subscription_item = MagicMock()
        mock_subscription_item.id = "sub_item_id"
        mock_list.return_value.data = [mock_subscription_item]

        # Mock modify to raise a StripeError
        mock_modify.side_effect = Exception("API Error")

        # Test that it raises Exception
        with self.assertRaises(Exception):
            change_subscription(self.test_user.id, self.test_price_id)
