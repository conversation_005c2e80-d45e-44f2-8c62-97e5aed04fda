from unittest.mock import MagicMock

import pytest
from langchain_core.messages import HumanMessage

from ..llm.chat_mock import ChatMock
from ..model_caller import ModelCaller
from ..model_config import ModelConfig, ModelParams


class TestModelCaller:

    def test_mock_model_caller(self):
        mock_model_config = MagicMock(spec=ModelConfig)
        components = {"comp1": {"text": "text1", "meta": {}}}
        mock_model_config.model_params_list = [
            ModelParams("mock", {"model_name": "mock", "components": components}),
        ]
        model_caller = ModelCaller(mock_model_config)

        llm_inputs = [HumanMessage(content="Hello, how are you?")]
        result = model_caller.get_results_with_fallback(llm_inputs)
        assert model_caller.model_name == "mock"
        assert isinstance(model_caller.llm, ChatMock)
        assert result[0].text == "{'comp1': {'text': 'text1', 'word count': 1}}"
        model_caller = ModelCaller(mock_model_config)
        model_caller.model_name = "mock"
        model_caller.model_kwargs = {"model_name": "mock", "components": components}
        result = model_caller._get_results(llm_inputs)
        assert result[0].text == "{'comp1': {'text': 'text1', 'word count': 1}}"

    @pytest.fixture
    def mock_fallback_model_config(self):
        mock = MagicMock(spec=ModelConfig)
        components = {"comp1": {"text": "text1", "meta": {}}}
        mock.model_params_list = [
            ModelParams(
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                {"model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0"},
            ),
            ModelParams("mock", {"model_name": "mock", "components": components}),
        ]
        return mock

    def test_calling_fallback(self, mock_fallback_model_config):
        model_caller = ModelCaller(mock_fallback_model_config)
        llm_inputs = [HumanMessage(content="Hello, how are you?")]
        result = model_caller.get_results_with_fallback(llm_inputs)
        assert result[0].text == "{'comp1': {'text': 'text1', 'word count': 1}}"
