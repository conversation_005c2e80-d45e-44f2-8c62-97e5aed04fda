import hashlib
import json
import unittest
from unittest.mock import ANY, MagicMock, patch

from langchain_core.documents import Document

from ..connected_assets.connected_asset_metadata_extractor import (
    ConnectedAssetMetadataExtractor,
)
from ..prompt.prompt_library.prompt_connected_assets import (
    CATEGORY_DEFINITIONS,
    METADATA_PROMPT,
)


class TestConnectedAssetMetadataExtractor(unittest.TestCase):
    """Test cases for ConnectedAssetMetadataExtractor class."""

    def setUp(self):
        """Set up the test environment."""
        # Patch the logging to avoid formatting errors
        self.logging_patcher = patch(
            "api.connected_assets.connected_asset_metadata_extractor.logging"
        )
        self.mock_logging = self.logging_patcher.start()

        # Create patches for dependencies
        self.model_config_patcher = patch(
            "api.connected_assets.connected_asset_metadata_extractor.ModelConfigResolver"
        )
        self.model_caller_patcher = patch(
            "api.connected_assets.connected_asset_metadata_extractor.ModelCaller"
        )
        self.cache_patcher = patch(
            "api.connected_assets.connected_asset_metadata_extractor.cache"
        )
        self.get_token_count_patcher = patch(
            "api.connected_assets.connected_asset_metadata_extractor.get_token_count"
        )
        self.cloudwatch_patcher = patch(
            "api.connected_assets.connected_asset_metadata_extractor.CloudWatchMetrics"
        )

        # Start the patches
        self.mock_model_config = self.model_config_patcher.start()
        self.mock_model_caller = self.model_caller_patcher.start()
        self.mock_cache = self.cache_patcher.start()
        self.mock_get_token_count = self.get_token_count_patcher.start()
        self.mock_cloudwatch = self.cloudwatch_patcher.start()

        # Configure the model_config mock
        model_config_instance = MagicMock()
        model_config_instance.model_budget = 10000  # Add model_budget attribute
        self.mock_model_config.resolve.return_value = model_config_instance

        # Setup the ModelCaller mock correctly
        mock_model_caller_instance = MagicMock()
        self.mock_model_caller.return_value = mock_model_caller_instance

        # Configure default behavior for calls
        mock_model_caller_instance.get_llm_dict_response.return_value = {}

        self.mock_get_token_count.return_value = 1000

        # Create a sample Document
        self.sample_doc = Document(
            page_content="This is a sample blog post content.",
            metadata={
                "source": "https://example.com/blog/sample-post",
                "title": "Sample Title",
            },
        )

        # Create the extractor with properly mocked dependencies
        self.extractor = ConnectedAssetMetadataExtractor()

        # Replace the real model_caller with our mock
        self.extractor.model_caller = mock_model_caller_instance
        self.extractor.model_config = model_config_instance

    def tearDown(self):
        """Clean up after tests."""
        # Stop all patches
        self.model_config_patcher.stop()
        self.model_caller_patcher.stop()
        self.cache_patcher.stop()
        self.get_token_count_patcher.stop()
        self.logging_patcher.stop()
        self.cloudwatch_patcher.stop()

    def test_create_metadata_cache_key(self):
        """Test the cache key creation."""
        # Calculate expected cache key components
        url = self.sample_doc.metadata.get("source", "")
        prompt_version = hashlib.md5(METADATA_PROMPT.encode("utf-8")).hexdigest()[:8]
        cat_defs_version = hashlib.md5(
            CATEGORY_DEFINITIONS.encode("utf-8")
        ).hexdigest()[:8]
        expected_key = f"connected_source_metadata:{url}:prompt_{prompt_version}:catdefs_{cat_defs_version}"

        # Get the actual key
        actual_key = self.extractor._create_metadata_cache_key(self.sample_doc)

        # Assert
        self.assertEqual(expected_key, actual_key)

    def test_extract_metadata_cache_hit(self):
        """Test extract_metadata when the cache has a hit."""
        # Prepare the expected cached result
        expected_metadata = {
            "author": "John Doe",
            "published_date": "2023-05-15",
            "category": "Blog Post",
            "title": "Sample Title",
        }
        cached_json = json.dumps(expected_metadata)

        # Configure the cache to return a hit
        self.mock_cache.get.return_value = cached_json

        # Call the method with new required parameters
        result = self.extractor.extract_metadata(
            self.sample_doc,
            starting_listing_page_url="https://example.com/blog",
            starting_listing_page_category="Blog Post",
        )

        # Assert the cache was checked with the correct key
        self.mock_cache.get.assert_called_once()

        # Verify we got back the cached metadata
        self.assertEqual(expected_metadata, result)

        # Verify the LLM was not called
        self.extractor.model_caller.get_llm_dict_response.assert_not_called()

    def test_extract_metadata_cache_miss(self):
        """Test extract_metadata when there's a cache miss."""
        # Configure the cache to return a miss
        self.mock_cache.get.return_value = None

        # Configure the LLM response
        expected_metadata = {
            "author": "Jane Smith",
            "published_date": "2023-07-22",
            "category": "Blog Post",
            "title": "Sample Title",
        }
        self.extractor.model_caller.get_llm_dict_response.return_value = (
            expected_metadata
        )

        # Call the method with new required parameters
        result = self.extractor.extract_metadata(
            self.sample_doc,
            starting_listing_page_url="https://example.com/blog",
            starting_listing_page_category="Blog Post",
        )

        # Assert the cache was checked
        self.mock_cache.get.assert_called_once()

        # Assert the LLM was called
        self.extractor.model_caller.get_llm_dict_response.assert_called_once()

        # Assert the result was cached
        self.mock_cache.set.assert_called_once_with(
            ANY, json.dumps(expected_metadata), self.extractor.CACHE_TTL
        )

        # Verify we got back the expected metadata
        self.assertEqual(expected_metadata, result)

    def test_extract_metadata_invalid_date(self):
        """Test extract_metadata with invalid date format."""
        # Configure the cache to return a miss
        self.mock_cache.get.return_value = None

        # Configure the LLM response with invalid date
        llm_response = {
            "author": "Jane Smith",
            "published_date": "22-07-2023",  # Invalid format (should be YYYY-MM-DD)
            "category": "Blog Post",
            "title": "Sample Title",
        }
        self.extractor.model_caller.get_llm_dict_response.return_value = llm_response

        # Call the method with new required parameters
        result = self.extractor.extract_metadata(
            self.sample_doc,
            starting_listing_page_url="https://example.com/blog",
            starting_listing_page_category="Blog Post",
        )

        # Check that the date was cleared
        self.assertEqual("", result["published_date"])

        # Assert the result was cached with the corrected date
        expected_cached_metadata = {
            "author": "Jane Smith",
            "published_date": "",
            "category": "Blog Post",
            "title": "Sample Title",
        }
        self.mock_cache.set.assert_called_once_with(
            ANY, json.dumps(expected_cached_metadata), self.extractor.CACHE_TTL
        )

    def test_extract_metadata_caching_different_urls(self):
        """Test that different URLs create different cache keys."""
        # Create two documents with different URLs
        doc1 = Document(
            page_content="Content 1",
            metadata={"source": "https://example.com/blog/post1", "title": "Title 1"},
        )
        doc2 = Document(
            page_content="Content 2",
            metadata={"source": "https://example.com/blog/post2", "title": "Title 2"},
        )

        # Get cache keys for both documents
        key1 = self.extractor._create_metadata_cache_key(doc1)
        key2 = self.extractor._create_metadata_cache_key(doc2)

        # Assert the keys are different
        self.assertNotEqual(key1, key2)

    def test_truncate_content_by_tokens_if_needed(self):
        """Test the _truncate_content_by_tokens_if_needed method."""
        # Test case 1: Content within token limit
        content = "This is a short text"
        self.mock_get_token_count.return_value = 5
        result = self.extractor._truncate_content_by_tokens_if_needed(content, 10)
        self.assertEqual(content, result)

        # Test case 2: Content needs truncation
        content = "This is a longer text that needs to be truncated"
        self.mock_get_token_count.return_value = 20
        result = self.extractor._truncate_content_by_tokens_if_needed(content, 10)
        # Since we're using a ratio, the new length should be roughly half
        self.assertLess(len(result), len(content))
        self.assertGreater(len(result), 0)

        # Test case 3: Empty content
        content = ""
        self.mock_get_token_count.return_value = 0
        result = self.extractor._truncate_content_by_tokens_if_needed(content, 10)
        self.assertEqual("", result)

        # Test case 4: Content exactly at token limit
        content = "This is exactly at the limit"
        self.mock_get_token_count.return_value = 10
        result = self.extractor._truncate_content_by_tokens_if_needed(content, 10)
        self.assertEqual(content, result)

    def test_deduce_category_from_url_success(self):
        """Test successful category deduction from URL."""
        self.extractor.model_caller.get_llm_dict_response.return_value = {
            "category": "Blog Posts"
        }

        url = "https://example.com/blog/post-1"
        result = self.extractor.deduce_category_from_url(url)

        self.assertEqual(result, "Blog Posts")

        self.extractor.model_caller.get_llm_dict_response.assert_called_once()

    def test_deduce_category_from_url_error(self):
        """Test error handling in category deduction."""
        self.extractor.model_caller.get_llm_dict_response.side_effect = Exception(
            "Model error"
        )

        url = "https://example.com/some-page"
        result = self.extractor.deduce_category_from_url(url)

        self.assertEqual(result, "Other")

        self.mock_logging.exception.assert_called_once()
        self.mock_cloudwatch.put_metric.assert_called_with(
            "connected_asset_metadata_extractor_url_category_deduction_error",
            1,
            [],
        )
