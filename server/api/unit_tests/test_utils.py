import pytest
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from ..utils import (
    CloudWatchDimensionError,
    CloudWatchMetrics,
    alter_llm_messages,
    fuzzy_match_json_key,
    is_integer,
    iter_campaign_target_params,
    try_parse_int,
)


def test_try_parse_int():
    assert try_parse_int("123") == (123, True)
    assert try_parse_int("123.45") == (None, False)
    assert try_parse_int("-123") == (-123, True)
    assert try_parse_int("abc") == (None, False)
    assert try_parse_int(123) == (123, True)
    assert try_parse_int(123.45) == (None, False)
    assert try_parse_int(-123) == (-123, True)
    assert try_parse_int("123abc") == (None, False)


def test_is_integer():
    assert is_integer("123")
    assert not is_integer("123.45")
    assert is_integer("-123")
    assert not is_integer("abc")
    assert is_integer(123)
    assert not is_integer("")
    assert not is_integer(None)
    assert not is_integer(" 42 ")
    assert not is_integer(True)
    assert is_integer("9" * 20)
    assert not is_integer(123.45)


def test_fuzzy_match_json_key():
    # Test exact match
    json_obj = {"hello": 1, "world": 2}
    assert fuzzy_match_json_key("hello", json_obj) == "hello"

    # Test close match with small edit distance
    json_obj = {"hello": 1, "world": 2}
    assert fuzzy_match_json_key("helo", json_obj) == "hello"

    # Test long string with matching first third
    json_obj = {"introduction_paragraph": 1, "introduction_summary": 2}
    assert (
        fuzzy_match_json_key("introduction_paragraph_draft", json_obj)
        == "introduction_paragraph"
    )

    # Test when no good match exists
    json_obj = {"hello": 1, "world": 2}
    assert fuzzy_match_json_key("completely_different", json_obj) is None

    # Test empty json object
    json_obj = {}
    assert fuzzy_match_json_key("hello", json_obj) is None

    # Test with multiple close matches - should return the closest one
    json_obj = {"hello": 1, "helo": 2, "hallo": 3}
    assert fuzzy_match_json_key("helo", json_obj) == "helo"


def test_iter_campaign_target_params():
    # Test dictionary case with is_targets_concat=False
    target_params = [
        {
            "audience": ["young_professionals", "seniors"],
            "location": ["New York", "Los Angeles"],
        }
    ]
    assert list(iter_campaign_target_params(target_params, False)) == [
        {"audience": "young_professionals", "location": "New York"},
        {"audience": "young_professionals", "location": "Los Angeles"},
        {"audience": "seniors", "location": "New York"},
        {"audience": "seniors", "location": "Los Angeles"},
    ]

    # Test dictionary case with is_targets_concat=True
    target_params = [
        {
            "audience": ["young_professionals", "seniors"],
            "location": ["New York", "Los Angeles"],
        }
    ]
    assert list(iter_campaign_target_params(target_params, True)) == [
        {"audience": "young_professionals"},
        {"audience": "seniors"},
        {"location": "New York"},
        {"location": "Los Angeles"},
    ]

    # Test tuple case
    target_params = [
        ("audience", "young_professionals"),
        ("location", "New York"),
    ]
    assert list(iter_campaign_target_params(target_params, False)) == [
        {"audience": "young_professionals"},
        {"location": "New York"},
    ]
    assert list(iter_campaign_target_params(target_params, True)) == [
        {"audience": "young_professionals"},
        {"location": "New York"},
    ]


def test_process_dimensions():
    # Test with valid dimensions
    dimensions = [{"Name": "Test", "Value": "test_value"}]
    processed_dimensions = CloudWatchMetrics._process_dimensions(dimensions)
    assert processed_dimensions == dimensions

    # Test with various non-ASCII characters
    test_cases = [
        # Spanish characters
        {"input": "test_value_ñ", "expected": "test_value_"},
        # Emojis
        {"input": "test_value_😀", "expected": "test_value_"},
        # Accented characters
        {"input": "test_value_éèêë", "expected": "test_value_"},
    ]

    for test_case in test_cases:
        dimensions = [{"Name": "Test", "Value": test_case["input"]}]
        processed_dimensions = CloudWatchMetrics._process_dimensions(dimensions)
        assert (
            processed_dimensions[0]["Value"] == test_case["expected"]
        ), f"Failed to clean non-ASCII characters. Input: {test_case['input']}, Expected: {test_case['expected']}, Got: {processed_dimensions[0]['Value']}"

    # Test with value exceeding length limit
    dimensions = [{"Name": "Test", "Value": "a" * 1025}]
    processed_dimensions = CloudWatchMetrics._process_dimensions(dimensions)
    assert len(processed_dimensions[0]["Value"]) == 1024

    # Test with invalid dimension structure
    dimensions = [{"Name": "Test"}]  # Missing "Value" key
    with pytest.raises(CloudWatchDimensionError) as exc_info:
        CloudWatchMetrics._process_dimensions(dimensions)
    assert "Missing 'Value' key in dimension" in str(exc_info.value)

    # Test with completely invalid input
    with pytest.raises(CloudWatchDimensionError) as exc_info:
        CloudWatchMetrics._process_dimensions([{"InvalidKey": "InvalidValue"}])
    assert "Missing 'Value' key in dimension" in str(exc_info.value)


def test_put_metric():
    # Test in a non-unit test environment
    original_env = CloudWatchMetrics._env
    CloudWatchMetrics._env = "prod"  # Set to a non-test environment

    # Create a mock CloudWatch client
    class MockCloudWatchClient:
        def put_metric_data(self, **kwargs):
            pass  # Mock implementation that does nothing

    # Mock the CloudWatch client
    original_cloudwatch = CloudWatchMetrics._cloudwatch
    CloudWatchMetrics._cloudwatch = MockCloudWatchClient()

    try:
        # Test with valid inputs
        CloudWatchMetrics.put_metric(
            "test_metric", 1.0, [{"Name": "Test", "Value": "test_value"}]
        )
        # No exception should be raised

        # Test with invalid dimensions - should log error but not raise exception
        CloudWatchMetrics.put_metric(
            "test_metric", 1.0, [{"Name": "Test"}]
        )  # Missing "Value" key

    finally:
        # Restore original values
        CloudWatchMetrics._env = original_env
        CloudWatchMetrics._cloudwatch = original_cloudwatch


def test_alter_llm_messages_merging_and_conversion():
    # Test merging of adjacent HumanMessages
    llm_inputs = [
        HumanMessage(content="Hello"),
        HumanMessage(content="How are you?"),
        AIMessage(content="I'm fine."),
        AIMessage(content="How can I help?"),
    ]
    result = alter_llm_messages(llm_inputs, no_system_message=True)
    assert len(result) == 2
    assert isinstance(result[0], HumanMessage)
    assert "Hello" in result[0].content and "How are you?" in result[0].content
    assert isinstance(result[1], AIMessage)
    assert "I'm fine." in result[1].content and "How can I help?" in result[1].content

    # Test conversion of SystemMessage to HumanMessage
    llm_inputs = [
        SystemMessage(content="System info"),
        HumanMessage(content="User input"),
        AIMessage(content="AI response"),
    ]
    result = alter_llm_messages(llm_inputs, no_system_message=True)
    assert isinstance(result[0], SystemMessage)
    assert isinstance(result[1], HumanMessage)
    assert isinstance(result[2], AIMessage)

    # Test that SystemMessage after the first is converted to HumanMessage and merged with following HumanMessage
    llm_inputs = [
        SystemMessage(content="System 1"),
        SystemMessage(content="System 2"),
        HumanMessage(content="User input"),
    ]
    result = alter_llm_messages(llm_inputs, no_system_message=True)
    assert len(result) == 2
    assert isinstance(result[0], SystemMessage)
    assert result[0].content == "System 1"
    assert isinstance(result[1], HumanMessage)
    assert result[1].content == "System 2\n\n\nUser input"

    # Test insertion of HumanMessage if first non-system is AIMessage
    llm_inputs = [
        SystemMessage(content="System 1"),
        AIMessage(content="AI response"),
    ]
    result = alter_llm_messages(llm_inputs, no_system_message=True)
    assert isinstance(result[0], SystemMessage)
    assert isinstance(result[1], HumanMessage)
    assert result[1].content == "Let's start"
    assert isinstance(result[2], AIMessage)
    assert result[2].content == "AI response"

    # Test empty input
    assert alter_llm_messages([], no_system_message=True) == []
