import json
from unittest.mock import Mock, call, patch

import pytest

from ..connected_assets.connected_assets_url_validator import (
    ConnectedAssetsUrlValidationResult,
    ConnectedAssetsUrlValidator,
    ValidationError,
    ValidationStatus,
)


@pytest.fixture
def url_validator():
    return ConnectedAssetsUrlValidator("https://example.com")


def test_notion_page(url_validator):
    # Test notion page detection
    notion_url = "https://notion.so/mypage"
    validator = ConnectedAssetsUrlValidator(notion_url)
    result = validator.validate()
    assert isinstance(result, ConnectedAssetsUrlValidationResult)
    assert result.status == ValidationStatus.ERROR
    assert "Notion" in result.error_message


@patch("api.connected_assets.connected_assets_url_validator.TofuWebPageLoader")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_root_url(mock_verify_url, mock_loader):
    # Mock the dependencies
    mock_verify_url.return_value = True

    # Test root URLs (should fail)
    root_urls = [
        "https://example.com",
        "https://example.com/",
    ]
    for url in root_urls:
        validator = ConnectedAssetsUrlValidator(url)
        validator._is_meaningful_content = Mock(
            return_value={"label": "PASS", "comment": None}
        )
        result = validator.validate()
        assert result.status == ValidationStatus.WARNING
        assert ValidationError.ROOT_URL in result.error_message

    # Test non-root URLs (should pass)
    not_root_urls = [
        "https://example.com/page",
        "https://example.com/page/",
    ]
    for url in not_root_urls:
        validator = ConnectedAssetsUrlValidator(url)
        validator._is_meaningful_content = Mock(
            return_value={"label": "PASS", "comment": None}
        )
        result = validator.validate()
        assert result.status == ValidationStatus.OK
        assert result.error_message is None


@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_gated_url(mock_verify_url):
    # Test gated URL detection
    mock_verify_url.return_value = False
    validator = ConnectedAssetsUrlValidator("https://example.com/resources")
    result = validator.validate()
    assert isinstance(result, ConnectedAssetsUrlValidationResult)
    assert result.status == ValidationStatus.WARNING
    assert "gated" in result.error_message


@patch("api.model_caller.ModelCaller.get_llm_dict_response")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_content_meaningfulness(mock_verify_url, mock_call_llm):
    # Mock the dependencies
    mock_verify_url.return_value = True
    mock_call_llm.return_value = {"label": "PASS", "comment": None}
    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    validator._is_meaningful_content = Mock(
        return_value={"label": "PASS", "comment": None}
    )
    result = validator.validate()

    assert isinstance(result, ConnectedAssetsUrlValidationResult)
    assert result.status == ValidationStatus.OK
    assert result.error_message is None
    # Verify that _is_meaningful_content was called
    validator._is_meaningful_content.assert_called_once()


@patch("api.model_caller.ModelCaller.get_llm_dict_response")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_content_meaningfulness_fail(mock_verify_url, mock_call_llm):
    # Mock the dependencies
    mock_verify_url.return_value = True
    mock_call_llm.return_value = {"label": "FAIL", "comment": "This is a test comment"}

    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    validator._is_meaningful_content = Mock(
        return_value={
            "label": "FAIL",
            "comment": "This is a test comment",
            "reason": "ERROR",
        }
    )
    result = validator.validate()

    assert isinstance(result, ConnectedAssetsUrlValidationResult)
    assert result.status == ValidationStatus.WARNING
    assert result.error_message == "Failed to validate the URL, content checking failed"
    # Verify that _is_meaningful_content was called
    validator._is_meaningful_content.assert_called_once()


@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_validate_full_flow(mock_verify_url):
    # Test the full validation flow with a normal URL
    mock_verify_url.return_value = True

    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    validator._is_meaningful_content = Mock(
        return_value={"label": "PASS", "comment": None}
    )
    result = validator.validate()

    assert isinstance(result, ConnectedAssetsUrlValidationResult)
    assert result.status == ValidationStatus.OK
    assert result.error_message is None
    # Verify that _is_meaningful_content was called
    validator._is_meaningful_content.assert_called_once()


@patch("api.connected_assets.connected_assets_url_validator.cache")
@patch("api.connected_assets.connected_assets_url_validator.TofuWebPageLoader")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_content_meaningfulness_cache_hit(mock_verify_url, mock_loader, mock_cache):
    # Mock the dependencies
    mock_verify_url.return_value = True
    cached_result = {"label": "PASS", "comment": "Cached result", "reason": "OK"}
    mock_cache.get.return_value = json.dumps(cached_result)

    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    result = validator.validate()

    # Verify cache was checked
    mock_cache.get.assert_called_once()
    # Verify loader was not called since we got a cache hit
    mock_loader.assert_not_called()
    # Verify result matches cached data
    assert result.status == ValidationStatus.OK
    assert result.error_message is None


@patch("api.connected_assets.connected_assets_url_validator.cache")
@patch("api.connected_assets.connected_assets_url_validator.TofuWebPageLoader")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_content_meaningfulness_cache_miss(mock_verify_url, mock_loader, mock_cache):
    # Mock the dependencies
    mock_verify_url.return_value = True
    mock_loader.return_value.load.return_value = [
        Mock(page_content="Test content", metadata={"title": "Test Title"})
    ]
    mock_cache.get.return_value = None  # Ensure cache miss

    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    validator.model_caller.get_llm_dict_response = Mock(
        return_value={"label": "PASS", "comment": "Test comment", "reason": "OK"}
    )

    result = validator.validate()

    # Verify cache was checked
    mock_cache.get.assert_called_once()
    mock_verify_url.assert_called_once_with("https://example.com/blog")
    assert result.status == ValidationStatus.OK
    assert result.error_message is None


@patch("api.connected_assets.connected_assets_url_validator.cache")
@patch("api.connected_assets.connected_assets_url_validator.TofuWebPageLoader")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_content_meaningfulness_no_cache_on_failure(
    mock_verify_url, mock_loader, mock_cache
):
    # Mock the dependencies
    mock_verify_url.return_value = True
    mock_loader.return_value.load.return_value = []  # No content
    mock_cache.get.return_value = None  # Ensure cache miss

    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    result = validator.validate()

    # Verify cache was checked
    mock_cache.get.assert_called_once()
    mock_verify_url.assert_called_once_with("https://example.com/blog")
    assert result.status == ValidationStatus.WARNING


@patch("api.connected_assets.connected_assets_url_validator.TofuWebPageLoader")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
@patch("api.connected_assets.connected_assets_url_validator.get_token_count")
def test_main_content_budget_allocation(
    mock_get_token_count, mock_verify_url, mock_loader
):
    # Mock the dependencies
    mock_verify_url.return_value = True
    mock_loader.return_value.load.return_value = [
        Mock(
            page_content="Test content",
            metadata={"title": "Test Title", "description": "Test Description"},
        )
    ]
    mock_get_token_count.return_value = 100  # Mock token count

    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    validator.model_caller.get_llm_dict_response = Mock(
        return_value={"label": "PASS", "comment": "Test comment", "reason": "OK"}
    )

    # Clear the cache before running the test
    with patch(
        "api.connected_assets.connected_assets_url_validator.cache"
    ) as mock_cache:
        mock_cache.get.return_value = None  # Ensure cache miss
        result = validator.validate()

    # Verify token count was calculated
    mock_get_token_count.assert_called()
    assert result.status == ValidationStatus.OK
    assert result.error_message is None


@patch("api.connected_assets.connected_assets_url_validator.TofuWebPageLoader")
@patch("api.connected_assets.connected_assets_url_validator.verify_url_exists")
def test_generate_sub_pages_content(mock_verify_url, mock_loader):
    # Mock the dependencies
    mock_verify_url.return_value = True

    # Create mock documents with different token counts
    mock_docs = [
        Mock(
            page_content="Short content",
            metadata={"source": "https://example.com/page1"},
        ),
        Mock(
            page_content="Long content " * 100,  # More tokens
            metadata={"source": "https://example.com/page2"},
        ),
        Mock(
            page_content="Medium content " * 50,
            metadata={"source": "https://example.com/page3"},
        ),
    ]

    mock_loader.return_value.load_deep.return_value = mock_docs
    mock_loader.return_value.clean_text.return_value = mock_docs

    validator = ConnectedAssetsUrlValidator("https://example.com/blog")
    validator.model_caller.model_config.model_budget = 1000  # Set a reasonable budget

    result = validator._generate_sub_pages_content()

    # Verify loader was called with correct parameters
    mock_loader.return_value.load_deep.assert_called_once_with(
        deep_crawl_only_same_folder=True, always_crawl_next_page=False
    )
    # Verify result contains expected format
    assert "Page URL: https://example.com/page1" in result
    assert "Page Content: Short content" in result
    assert "---" in result  # Verify separator is present


@patch("api.utils.CloudWatchMetrics.put_metric")
@patch("api.connected_assets.connected_assets_url_validator.get_token_count")
def test_truncate_content_by_tokens(
    mock_get_token_count, mock_put_metric, url_validator
):
    # Mock token count for original content
    mock_get_token_count.side_effect = [1000, 500]  # Original tokens, truncated tokens

    content = "test " * 600  # 600 tokens
    token_budget = 500

    result = url_validator._truncate_content_by_tokens(content, token_budget)

    # Verify content was truncated
    assert len(result) < len(content)
    # Verify token count was called
    assert mock_get_token_count.call_count == 2
    # Verify metrics were published
    mock_put_metric.assert_called_with(
        "connected_assets_url_validator_content_truncated",
        1,
        [],
    )


@patch("api.utils.CloudWatchMetrics.put_metric")
@patch("api.connected_assets.connected_assets_url_validator.get_token_count")
def test_no_truncation_needed(mock_get_token_count, mock_put_metric, url_validator):
    # Mock token count for content within budget
    mock_get_token_count.return_value = 100

    content = "test content"
    token_budget = 500

    result = url_validator._truncate_content_by_tokens(content, token_budget)

    # Verify content was not truncated
    assert result == content
    # Verify token count was called once
    mock_get_token_count.assert_called_once()
    # Verify no metrics were published
    mock_put_metric.assert_not_called()
