import unittest

from ..shared_definitions.protobuf.action_system_config import (
    ACTION_CATEGORY_TO_ACTION_TYPES,
)
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionDefinition,
    ActionType,
)
from ..utils import sort_action_types


class TestActionUtils(unittest.TestCase):
    def test_sort_action_types(self):
        # Create a sample dictionary with action types in random order
        test_actions = {
            "ACTION_TYPE_REPURPOSE_OTHER": ActionDefinition(),
            "ACTION_TYPE_PERSONALIZE_EMAIL_SDR": ActionDefinition(),
            "ACTION_TYPE_REPURPOSE_EMAIL_SDR": ActionDefinition(),
            "ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT": ActionDefinition(),
        }

        # Sort the dictionary
        sorted_actions = sort_action_types(test_actions)

        # Get the keys as a list to check order
        sorted_keys = list(sorted_actions.keys())

        # Verify that the order follows the expected pattern:
        # 1. User input actions first
        # 2. Repurpose actions next
        # 3. Personalize actions after that

        # Check if UPLOAD_OR_SELECT_ANCHOR_CONTENT comes before REPURPOSE actions
        upload_index = sorted_keys.index("ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT")
        repurpose_index = sorted_keys.index("ACTION_TYPE_REPURPOSE_EMAIL_SDR")
        self.assertLess(upload_index, repurpose_index)

        # Check if REPURPOSE actions come before PERSONALIZE actions
        personalize_index = sorted_keys.index("ACTION_TYPE_PERSONALIZE_EMAIL_SDR")
        self.assertLess(repurpose_index, personalize_index)

        # Ensure all keys from original dict are present in sorted dict
        self.assertEqual(set(test_actions.keys()), set(sorted_actions.keys()))

        # Ensure the values are preserved
        for key in test_actions:
            self.assertEqual(test_actions[key], sorted_actions[key])

    def test_sort_action_types_with_unknown_action_type(self):
        # Create a dictionary with an unknown action type
        test_actions = {
            "ACTION_TYPE_REPURPOSE_EMAIL_SDR": ActionDefinition(),
            "UNKNOWN_ACTION_TYPE": ActionDefinition(),  # Not in the predefined list
        }

        # Sort the dictionary
        sorted_actions = sort_action_types(test_actions)

        # Get the keys as a list to check order
        sorted_keys = list(sorted_actions.keys())

        # Unknown action types should be at the end
        self.assertEqual(sorted_keys[-1], "UNKNOWN_ACTION_TYPE")

        # All keys should still be present
        self.assertEqual(set(test_actions.keys()), set(sorted_actions.keys()))

        # Ensure the values are preserved
        for key in test_actions:
            self.assertEqual(test_actions[key], sorted_actions[key])

    def test_sort_action_types_with_dict_values(self):
        # Create a dictionary with plain dict values instead of ActionEdgeDefinition
        test_actions = {
            "ACTION_TYPE_REPURPOSE_OTHER": {"is_shown": True},
            "ACTION_TYPE_PERSONALIZE_EMAIL_SDR": {"is_shown": False},
            "ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT": {"is_shown": True},
        }

        # Sort the dictionary
        sorted_actions = sort_action_types(test_actions)

        # Get the keys as a list to check order
        sorted_keys = list(sorted_actions.keys())

        # Check if UPLOAD_OR_SELECT_ANCHOR_CONTENT comes before other actions
        upload_index = sorted_keys.index("ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT")
        personalize_index = sorted_keys.index("ACTION_TYPE_PERSONALIZE_EMAIL_SDR")
        self.assertLess(upload_index, personalize_index)

        # Ensure all keys from original dict are present in sorted dict
        self.assertEqual(set(test_actions.keys()), set(sorted_actions.keys()))

        # Ensure the values are preserved and are still dictionaries
        for key in test_actions:
            self.assertEqual(test_actions[key], sorted_actions[key])
            self.assertIsInstance(sorted_actions[key], dict)

    def test_sort_action_types_preserves_all_data(self):
        """Test that sorting preserves all action types from the original configuration."""
        # Create a dictionary with all action types from the configuration
        all_action_types = {}
        for category, action_types in ACTION_CATEGORY_TO_ACTION_TYPES.items():
            for action_type in action_types:
                action_type_name = ActionType.Name(action_type)
                all_action_types[action_type_name] = ActionDefinition()

        # Count the original number of action types
        original_count = len(all_action_types)

        # Sort the dictionary
        sorted_actions = sort_action_types(all_action_types)

        # Verify the count is the same
        self.assertEqual(len(sorted_actions), original_count)

        # Verify all keys are preserved
        self.assertEqual(set(all_action_types.keys()), set(sorted_actions.keys()))

        # Verify the order follows the category order
        sorted_keys = list(sorted_actions.keys())

        # Check that user input actions come first
        for user_input_action in [
            ActionType.Name(at)
            for at in ACTION_CATEGORY_TO_ACTION_TYPES[
                ActionCategory.ACTION_CATEGORY_USER_INPUT
            ]
        ]:
            user_input_index = sorted_keys.index(user_input_action)

            # All repurpose actions should come after user input actions
            for repurpose_action in [
                ActionType.Name(at)
                for at in ACTION_CATEGORY_TO_ACTION_TYPES[
                    ActionCategory.ACTION_CATEGORY_REPURPOSE
                ]
            ]:
                repurpose_index = sorted_keys.index(repurpose_action)
                self.assertLess(user_input_index, repurpose_index)
