from unittest.mock import Mock, patch

from django.test import TestCase

from ..connected_assets.connected_asset_info_groups_handler import (
    ConnectedAssetInfoGroupsHandler,
)
from ..connected_assets.connected_assets_utils import normalize_url
from ..connected_assets.site_map_discover import SiteMapResult, SiteMapSection
from ..models import AssetInfoGroup, CompanyInfo, Playbook


class TestConnectedAssetsHandler(TestCase):
    def setUp(self):
        # Create test company
        self.company = CompanyInfo.objects.create()

        # Create test playbook
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company
        )

        self.handler = ConnectedAssetInfoGroupsHandler(
            playbook_id=self.playbook.id,
            customer_instructions=["Test instructions"],
            max_depth=2,
            max_pages=500,
        )

    def test_initialization(self):
        """Test handler initialization"""
        self.assertEqual(self.handler.playbook_id, self.playbook.id)
        self.assertEqual(self.handler.customer_instructions, ["Test instructions"])
        self.assertEqual(self.handler.max_depth, 2)
        self.assertEqual(self.handler.max_pages, 500)
        self.assertEqual(self.handler.playbook, self.playbook)

    @patch("api.connected_assets.connected_asset_info_groups_handler.SiteMapDiscover")
    def test_discover_and_store_new_url(self, mock_site_map_discover):
        """Test discovering and storing content from a new URL"""
        # Mock the discover_structure response
        mock_structure = SiteMapResult(
            root_url="https://example.com",
            content_sections=[
                SiteMapSection(
                    category="products",
                    confidence="high",
                    url_paths=["/products", "/catalog"],
                )
            ],
            suggested_paths=[
                SiteMapSection(
                    category="about", confidence="high", url_paths=["/about-us"]
                )
            ],
            execution_time=0,
        )

        mock_discover = Mock()
        mock_discover.discover_structure.return_value = mock_structure
        mock_site_map_discover.return_value = mock_discover

        test_url = "https://example.com"
        structure, asset_groups = self.handler.discover_and_store(test_url)

        # Verify structure matches mock
        self.assertEqual(structure, mock_structure)

        # Verify asset groups were created
        self.assertEqual(len(asset_groups), 3)  # 2 URLs for products + 1 for about

        # Check that the URLs were properly stored
        stored_urls = set()
        for group in asset_groups:
            metadata = group.meta.get("tofu_connected_assets_metadata", {})
            urls = metadata.get("tofu_connected_source_type_info", {}).get("urls", [])
            stored_urls.update(urls)

        expected_urls = {
            "https://example.com/products",
            "https://example.com/catalog",
            "https://example.com/about-us",
        }
        self.assertEqual(stored_urls, expected_urls)

    @patch("api.connected_assets.connected_asset_info_groups_handler.SiteMapDiscover")
    def test_discover_and_store_duplicate_url(self, mock_site_map_discover):
        """Test discovering from an already processed URL"""
        # Mock the discover_structure response with some content to ensure processing completes
        mock_discover = Mock()

        mock_structure = SiteMapResult(
            root_url="https://example.com",
            content_sections=[
                SiteMapSection(category="test", confidence="high", url_paths=["/test"])
            ],
            suggested_paths=[],
            execution_time=0,
        )

        mock_discover.discover_structure.return_value = mock_structure
        mock_site_map_discover.return_value = mock_discover

        test_url = "example.com"

        # First call - should process the URL and store metadata
        structure, asset_groups = self.handler.discover_and_store(test_url)

        # Verify the URL was stored in metadata after successful discovery
        self.playbook.company_object.refresh_from_db()  # Refresh to get latest metadata
        stored_url = self.playbook.company_object.meta.get(
            "site_map_discovering_processed_url"
        )
        self.assertEqual(
            stored_url,
            test_url,
            "URL should be stored in metadata after successful discovery",
        )

        # Second call - should skip processing
        structure2, asset_groups2 = self.handler.discover_and_store(test_url)

        # Should return None and empty list for duplicate URL
        self.assertIsNone(structure2)
        self.assertEqual(len(asset_groups2), 0)

        # Verify mock was only called once
        mock_site_map_discover.assert_called_once()

    def test_url_normalization(self):
        """Test URL normalization in discover_and_store"""
        urls = [
            "example.com",
            "http://example.com",
            "https://example.com",
            "https://example.com/",
        ]

        for url in urls:
            # Create a new handler for each test to avoid state from previous tests
            handler = ConnectedAssetInfoGroupsHandler(self.playbook.id)

            with patch(
                "api.connected_assets.connected_asset_info_groups_handler.SiteMapDiscover"
            ) as mock_site_map_discover:
                mock_discover = Mock()
                mock_discover.discover_structure.return_value = SiteMapResult(
                    root_url="https://example.com",
                    content_sections=[],
                    suggested_paths=[],
                    execution_time=0,
                )
                mock_site_map_discover.return_value = mock_discover

                handler.discover_and_store(url)

                # Verify that the URL was normalized and stored correctly
                stored_url = handler.playbook.company_object.meta.get(
                    "site_map_discovering_processed_url"
                )
                self.assertIsNotNone(stored_url, "URL should be stored in metadata")
                normalized_url = normalize_url(stored_url)
                self.assertEqual(
                    normalized_url, "example.com", "URL should be normalized correctly"
                )

                # Reset the metadata for the next test
                handler.playbook.company_object.meta = {}
                handler.playbook.company_object.save()

    def tearDown(self):
        # Clean up created objects
        AssetInfoGroup.objects.all().delete()
        self.playbook.delete()
        self.company.delete()
