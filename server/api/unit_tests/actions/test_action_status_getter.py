import logging
import unittest

from google.protobuf.json_format import MessageToDict

from ...actions.legacy_converter.legacy_export_response_converter import (
    convert_export_response_v2_to_v3,
)
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType


class TestActionStatusGetter(unittest.TestCase):
    def setUp(self):
        # Setup code if needed
        pass

    def tearDown(self):
        # Teardown code if needed
        pass

    def test_get_export_status_p1(self):
        content_group_params = {
            "content_type": "Email - Marketing",
            "export_response": {"marketo": {"id": 6957}},
            "export_settings": {
                "marketo": {
                    "email": {
                        "dynamic": {
                            "exportType": "dynamic",
                            "destination": "marketo",
                            "targetsSetting": [
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076947,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [5, 7, 418],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["Tofu"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": ["Jian's autopilot list 4: Tofu"],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076951,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [6],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["Apple"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": ["Jian's autopilot list 4: Apple"],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076952,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [104],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["Reprise"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": [
                                        "Jian's autopilot list 4: Reprise"
                                    ],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076953,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [103],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["TofuHQ2"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": [
                                        "Jian's autopilot list 4: TofuHQ2"
                                    ],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                            ],
                            "advancedSetting": {
                                "emailType": "automated",
                                "emailFooter": True,
                            },
                            "componentsSetting": {
                                "kMBHlNje6trLXqLA": "tofu_content_2",
                                "sw1K-J5FQu7j1H0a": "tofu_content_1",
                            },
                        }
                    }
                },
                "exportType": "dynamic",
                "exportDestination": "marketo",
            },
        }
        platform_type = PlatformType.PLATFORM_TYPE_MARKETO
        action_status_details = convert_export_response_v2_to_v3(
            platform_type, content_group_params
        )

        action_status_json_compare = {
            "stats": {"cntsSucc": 4},
            "exportDetails": {
                "targetStatus": {
                    "2076947": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [5, 7, 418]},
                    },
                    "2076951": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [6]},
                    },
                    "2076953": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [103]},
                    },
                    "2076952": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [104]},
                    },
                }
            },
        }

        # Replace this with an actual assertion
        self.assertEqual(
            MessageToDict(action_status_details), action_status_json_compare
        )

    def test_get_export_status_p2(self):
        content_group_params = {
            "content_type": "Email - Marketing",
            "export_response": {"marketo": {"id": 6957}},
            "export_settings": {
                "marketo": {
                    "exportType": "dynamic",
                    "email": {
                        "dynamic": {
                            "exportType": "dynamic",
                            "destination": "marketo",
                            "targetsSetting": [
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076947,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [5, 7, 418],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["Tofu"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": ["Jian's autopilot list 4: Tofu"],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076951,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [6],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["Apple"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": ["Jian's autopilot list 4: Apple"],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076952,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [104],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["Reprise"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": [
                                        "Jian's autopilot list 4: Reprise"
                                    ],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                                {
                                    "draftURL": "https://app-ab64.marketo.com/#EM6957B2",
                                    "contentId": 2076953,
                                    "emailName": "Tofu Email - Marketing 1",
                                    "marketoIds": {
                                        "lead_ids": [103],
                                        "object_type": "company",
                                        "object_identifier": "lead_ids",
                                    },
                                    "targetNames": ["TofuHQ2"],
                                    "exportStatus": "Completed",
                                    "lastExportAt": 1731353303184,
                                    "targetLabels": [
                                        "Jian's autopilot list 4: TofuHQ2"
                                    ],
                                    "isExportTarget": False,
                                    "targetListNames": ["Jian's autopilot list 4"],
                                    "hasJustBeenExported": True,
                                },
                            ],
                            "advancedSetting": {
                                "emailType": "automated",
                                "emailFooter": True,
                            },
                            "componentsSetting": {
                                "kMBHlNje6trLXqLA": "tofu_content_2",
                                "sw1K-J5FQu7j1H0a": "tofu_content_1",
                            },
                        }
                    },
                },
                # "exportType": "dynamic",
                "exportDestination": "marketo",
            },
        }
        platform_type = PlatformType.PLATFORM_TYPE_MARKETO
        action_status_details = convert_export_response_v2_to_v3(
            platform_type, content_group_params
        )

        action_status_json_compare = {
            "stats": {"cntsSucc": 4},
            "exportDetails": {
                "targetStatus": {
                    "2076947": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [5, 7, 418]},
                    },
                    "2076951": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [6]},
                    },
                    "2076953": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [103]},
                    },
                    "2076952": {
                        "exportStatus": "EXPORT_STATUS_TYPE_COMPLETE",
                        "lastExportAt": "1731353303184",
                        "marketoObjectExportStatus": {"exportedIds": [104]},
                    },
                }
            },
        }

        # Replace this with an actual assertion
        self.assertEqual(
            MessageToDict(action_status_details), action_status_json_compare
        )


if __name__ == "__main__":
    unittest.main()
