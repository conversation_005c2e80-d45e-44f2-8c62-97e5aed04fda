from unittest.mock import Mock, patch

import pytest
from django.core.exceptions import ObjectDoesNotExist
from django.test import TestCase

from ...actions.action_edge_creator import ActionEdgeCreator
from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionCategory


class TestActionEdgeCreator(TestCase):
    def setUp(self):
        self.creator = ActionEdgeCreator()

    def test_handle_personalize_to_export_success(self):
        # Setup
        mock_content_group_data = (
            TofuDataListHandler.convert_content_group_ids_to_tofu_data([1])
        )

        with (
            patch("api.actions.action_edge_creator.Action") as mock_action_class,
            patch(
                "api.actions.action_edge_creator.ContentGroup"
            ) as mock_content_group_class,
        ):

            # Mock setup
            from_action = Mock()
            from_action.outputs = {"content_group": mock_content_group_data}

            outgoing_action = Mock()
            outgoing_action.inputs = {}
            outgoing_action.outputs = {}

            # Execute
            self.creator._handle_create_personalize_to_export(
                from_action, outgoing_action
            )

            # Assert
            assert outgoing_action.inputs["content_group"] == mock_content_group_data
            assert outgoing_action.outputs["content_group"] == mock_content_group_data
            outgoing_action.save.assert_called_once()

    def test_handle_personalize_to_export_missing_content_group(self):
        with patch("api.actions.action_edge_creator.Action") as mock_action_class:
            # Setup
            from_action = Mock()
            from_action.outputs = {}
            outgoing_action = Mock()

            # Execute & Assert
            with pytest.raises(
                ValueError, match="Content group is required for personalize action"
            ):
                self.creator._handle_create_personalize_to_export(
                    from_action, outgoing_action
                )

    def test_handle_personalize_to_export_multiple_content_groups(self):
        with patch("api.actions.action_edge_creator.Action") as mock_action_class:

            # Setup
            from_action = Mock()
            mock_content_group_data = (
                TofuDataListHandler.convert_content_group_ids_to_tofu_data([1, 2, 3])
            )  # Multiple content group IDs
            from_action.outputs = {"content_group": mock_content_group_data}
            outgoing_action = Mock()

            # Execute & Assert
            with pytest.raises(
                ValueError,
                match="Only one content group is allowed for personalize action",
            ):
                self.creator._handle_create_personalize_to_export(
                    from_action, outgoing_action
                )

    def test_create_success(self):
        with (
            patch("api.actions.action_edge_creator.Action") as mock_action_class,
            patch("api.actions.action_edge_creator.ActionEdge") as mock_edge_class,
            patch.object(
                ActionEdgeCreator, "_handle_create_personalize_to_export"
            ) as mock_handle_method,
        ):

            # Setup
            outgoing_action = Mock()
            outgoing_action.action_category = "ACTION_CATEGORY_EXPORT"
            outgoing_action.id = 2

            from_action = Mock()
            from_action.action_category = "ACTION_CATEGORY_PERSONALIZE"
            from_action.id = 1

            mock_action_class.objects.get.return_value = from_action
            incoming_edges = [
                {
                    "from_action": 1,
                    "config": {
                        "input_name_to_outgoing_action": "input_name",
                        "output_name_from_incoming_action": "output_name",
                    },
                }
            ]

            # Execute
            self.creator.create(outgoing_action, incoming_edges)

            # Assert
            mock_edge_class.objects.create.assert_called_once_with(
                from_action=from_action,
                to_action=outgoing_action,
                config={
                    "input_name_to_outgoing_action": "input_name",
                    "output_name_from_incoming_action": "output_name",
                },
            )
            mock_handle_method.assert_called_once_with(from_action, outgoing_action)

    def test_create_invalid_from_action(self):
        with (
            patch("api.actions.action_edge_creator.Action") as mock_action_class,
            patch("api.actions.action_edge_creator.ActionEdge") as mock_edge_class,
        ):
            # Setup
            outgoing_action = Mock()
            outgoing_action.id = 2
            incoming_edges = [{"from_action": 999}]

            # Configure the mock to raise ObjectDoesNotExist
            mock_action_class.objects.get.side_effect = ObjectDoesNotExist()

            # Execute & Assert
            with pytest.raises(ValueError, match="from_action_id: 999 does not exist"):
                self.creator.create(outgoing_action, incoming_edges)

            # Verify that ActionEdge.objects.create was not called
            mock_edge_class.objects.create.assert_not_called()

    def test_create_with_empty_incoming_edges(self):
        with patch("api.actions.action_edge_creator.Action") as mock_action_class:
            # Setup
            outgoing_action = Mock()
            incoming_edges = []

            # Execute
            result = self.creator.create(outgoing_action, incoming_edges)

            # Assert
            assert result is True
            mock_action_class.objects.get.assert_not_called()

    def test_create_with_missing_outgoing_action(self):
        with patch("api.actions.action_edge_creator.Action") as mock_action_class:
            # Setup
            incoming_edges = [{"from_action": 1}]

            # Execute & Assert
            with pytest.raises(ValueError, match="Outgoing action is required"):
                self.creator.create(None, incoming_edges)

    def test_update_success(self):
        with patch("api.actions.action_edge_creator.ActionEdge") as mock_edge_class:
            # Setup
            action_edge = Mock()
            new_config = {"updated": "config"}

            # Execute
            self.creator.update(action_edge, new_config)

            # Assert
            assert action_edge.config == new_config
            action_edge.save.assert_called_once_with(update_fields=["config"])
