from unittest.mock import MagicMock, patch

from ...actions.repurpose_to_anchor import RepurposeToAnchorConverter
from ...models import (
    Action,
    AssetInfo,
    AssetInfoGroup,
    Campaign,
    Content,
    ContentGroup,
    Playbook,
)
from ...shared_types import ContentType


class TestRepurposeToAnchorConverter:
    def _create_django_mock(self, spec_class):
        mock = MagicMock(spec=spec_class)
        mock._state = MagicMock()
        return mock

    def test_linkedin_ad_conversion(self):
        # Setup mocks
        mock_playbook = self._create_django_mock(Playbook)
        mock_campaign = self._create_django_mock(Campaign)
        mock_campaign.playbook = mock_playbook

        # Create a mock Action
        mock_action = self._create_django_mock(Action)
        mock_action.id = 101
        mock_action.campaign = mock_campaign

        mock_content_group = self._create_django_mock(ContentGroup)
        mock_content_group.id = 1
        mock_content_group.campaign = mock_campaign
        mock_content_group.content_group_params = {
            "content_type": ContentType.AdCampaignLinkedin,
        }
        mock_content_group.components = {
            "1": {"text": "Test Headline", "meta": {"component_type": "headline"}},
            "2": {
                "text": "Test Description",
                "meta": {"component_type": "description"},
            },
            "3": {
                "text": "Test Intro",
                "meta": {"component_type": "introductory-text"},
            },
            "4": {"text": "Test Ad Copy", "meta": {"component_type": "ad-copy"}},
        }

        mock_content = self._create_django_mock(Content)
        mock_content.content_group = mock_content_group
        mock_content.id = 1

        # Create mock AssetInfoGroup
        mock_asset_info_group = self._create_django_mock(AssetInfoGroup)
        mock_asset_info_group_filter = MagicMock()
        mock_asset_info_group_filter.first.return_value = mock_asset_info_group

        # Create a mock AssetInfo object
        mock_asset_info = self._create_django_mock(AssetInfo)
        mock_asset_info.id = 123

        # Create a mock transaction context manager
        mock_atomic = MagicMock()
        mock_atomic.__enter__ = MagicMock()
        mock_atomic.__exit__ = MagicMock()

        with (
            patch(
                "api.actions.repurpose_to_anchor.Content.objects.filter",
                return_value=MagicMock(first=lambda: mock_content),
            ),
            patch(
                "api.actions.repurpose_to_anchor.upload_file",
            ),
            patch(
                "api.actions.repurpose_to_anchor.AssetInfoGroup.objects.filter",
                return_value=mock_asset_info_group_filter,
            ),
            patch(
                "api.actions.repurpose_to_anchor.AssetInfo.objects.create",
                return_value=mock_asset_info,
            ),
            patch(
                "api.actions.repurpose_to_anchor.cache.get",
                return_value=None,
            ),
            patch(
                "api.actions.repurpose_to_anchor.replace_with_content_variations",
                return_value=mock_content_group.components,
            ),
            patch(
                "django.db.transaction.atomic",
                return_value=mock_atomic,
            ),
        ):
            converter = RepurposeToAnchorConverter(
                mock_content_group, to_action=mock_action
            )
            tofu_asset = converter.process()
            assert tofu_asset is not None
            assert hasattr(tofu_asset, "asset_id")

    def test_email_conversion(self):
        # Setup mocks
        mock_playbook = self._create_django_mock(Playbook)
        mock_campaign = self._create_django_mock(Campaign)
        mock_campaign.playbook = mock_playbook

        # Create a mock Action
        mock_action = self._create_django_mock(Action)
        mock_action.id = 102
        mock_action.campaign = mock_campaign

        mock_content_group = self._create_django_mock(ContentGroup)
        mock_content_group.id = 2
        mock_content_group.campaign = mock_campaign
        mock_content_group.content_group_params = {
            "content_type": ContentType.EmailMarketing,
        }
        mock_content_group.components = {
            "1": {"text": "Test Subject", "meta": {"component_type": "email subject"}},
            "2": {"text": "Test Body", "meta": {"component_type": "email body"}},
        }

        mock_content = self._create_django_mock(Content)
        mock_content.content_group = mock_content_group
        mock_content.id = 2

        # Create mock AssetInfoGroup
        mock_asset_info_group = self._create_django_mock(AssetInfoGroup)
        mock_asset_info_group_filter = MagicMock()
        mock_asset_info_group_filter.first.return_value = mock_asset_info_group

        # Create a mock AssetInfo object
        mock_asset_info = self._create_django_mock(AssetInfo)
        mock_asset_info.id = 456

        # Create a mock transaction context manager
        mock_atomic = MagicMock()
        mock_atomic.__enter__ = MagicMock()
        mock_atomic.__exit__ = MagicMock()

        with (
            patch(
                "api.actions.repurpose_to_anchor.Content.objects.filter",
                return_value=MagicMock(first=lambda: mock_content),
            ),
            patch(
                "api.actions.repurpose_to_anchor.upload_file",
            ),
            patch(
                "api.actions.repurpose_to_anchor.AssetInfoGroup.objects.filter",
                return_value=mock_asset_info_group_filter,
            ),
            patch(
                "api.actions.repurpose_to_anchor.AssetInfo.objects.create",
                return_value=mock_asset_info,
            ),
            patch(
                "api.actions.repurpose_to_anchor.cache.get",
                return_value=None,
            ),
            patch(
                "api.actions.repurpose_to_anchor.replace_with_content_variations",
                return_value=mock_content_group.components,
            ),
            patch(
                "django.db.transaction.atomic",
                return_value=mock_atomic,
            ),
        ):
            converter = RepurposeToAnchorConverter(
                mock_content_group, to_action=mock_action
            )
            tofu_asset = converter.process()
            assert tofu_asset is not None
            assert hasattr(tofu_asset, "asset_id")

    def test_generic_conversion(self):
        # Setup mocks
        mock_playbook = self._create_django_mock(Playbook)
        mock_campaign = self._create_django_mock(Campaign)
        mock_campaign.playbook = mock_playbook

        # Create a mock Action
        mock_action = self._create_django_mock(Action)
        mock_action.id = 103
        mock_action.campaign = mock_campaign

        mock_content_group = self._create_django_mock(ContentGroup)
        mock_content_group.id = 3
        mock_content_group.campaign = mock_campaign
        mock_content_group.content_group_params = {
            "content_type": "generic",
        }
        mock_content_group.components = {
            "1": {
                "text": "Generic Text Content",
                "meta": {"component_type": "generic"},
            },
        }

        mock_content = self._create_django_mock(Content)
        mock_content.content_group = mock_content_group
        mock_content.id = 3

        # Create mock AssetInfoGroup
        mock_asset_info_group = self._create_django_mock(AssetInfoGroup)
        mock_asset_info_group_filter = MagicMock()
        mock_asset_info_group_filter.first.return_value = mock_asset_info_group

        # Create a mock AssetInfo object
        mock_asset_info = self._create_django_mock(AssetInfo)
        mock_asset_info.id = 789

        # Create a mock transaction context manager
        mock_atomic = MagicMock()
        mock_atomic.__enter__ = MagicMock()
        mock_atomic.__exit__ = MagicMock()

        with (
            patch(
                "api.actions.repurpose_to_anchor.Content.objects.filter",
                return_value=MagicMock(first=lambda: mock_content),
            ),
            patch(
                "api.actions.repurpose_to_anchor.upload_file",
            ),
            patch(
                "api.actions.repurpose_to_anchor.AssetInfoGroup.objects.filter",
                return_value=mock_asset_info_group_filter,
            ),
            patch(
                "api.actions.repurpose_to_anchor.AssetInfo.objects.create",
                return_value=mock_asset_info,
            ),
            patch(
                "api.actions.repurpose_to_anchor.cache.get",
                return_value=None,
            ),
            patch(
                "api.actions.repurpose_to_anchor.replace_with_content_variations",
                return_value=mock_content_group.components,
            ),
            patch(
                "django.db.transaction.atomic",
                return_value=mock_atomic,
            ),
        ):
            converter = RepurposeToAnchorConverter(
                mock_content_group, to_action=mock_action
            )
            tofu_asset = converter.process()
            assert tofu_asset is not None
            assert hasattr(tofu_asset, "asset_id")
