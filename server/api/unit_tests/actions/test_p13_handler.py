import unittest
from unittest.mock import Mock, patch

import pytest

from ...actions.action_handler_personalize import (
    PersonalizationActionHandler,
)
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionInputOutputDefinition,
    PredefinedValue,
    TofuComponent,
    TofuComponentMetaType,
    TofuComponents,
    TofuData,
    TofuDataList,
    TofuDataType,
    TofuTemplate,
    TofuTemplateField,
)


class TestPersonalizationActionHandler(unittest.TestCase):
    def setUp(self):
        self.handler = PersonalizationActionHandler.__new__(
            PersonalizationActionHandler
        )

    def test_invalid_inputs(self):
        """Test that _check_components_fulfilled handles invalid inputs correctly"""
        # None input
        self.assertFalse(self.handler._check_components_fulfilled(None))

        # Wrong type input
        with self.assertRaises(ValueError):
            self.handler._check_components_fulfilled("not a TofuDataList")

        # Empty data list
        self.assertFalse(self.handler._check_components_fulfilled(TofuDataList()))

        # Wrong data type
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.string_value.value = "not components"
        tofu_data_list.data.append(tofu_data)
        self.assertFalse(self.handler._check_components_fulfilled(tofu_data_list))

    def test_empty_components(self):
        """Test that _check_components_fulfilled returns False when components is empty"""
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.components.CopyFrom(TofuComponents())
        tofu_data_list.data.append(tofu_data)

        self.assertFalse(self.handler._check_components_fulfilled(tofu_data_list))

    def test_components_without_text_type(self):
        """Test that _check_components_fulfilled returns False when no component has TEXT meta type"""
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()

        components = TofuComponents()
        component = components.components["test_component"]
        component.component_id = "test_component"
        component.component_meta_type = (
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_IMAGE
        )

        tofu_data.components.CopyFrom(components)
        tofu_data_list.data.append(tofu_data)

        self.assertFalse(self.handler._check_components_fulfilled(tofu_data_list))

    def test_components_with_text_type(self):
        """Test that _check_components_fulfilled returns True when at least one component has TEXT meta type"""
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()

        components = TofuComponents()

        # Non-text component
        image_component = components.components["image_component"]
        image_component.component_id = "image_component"
        image_component.component_meta_type = (
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_IMAGE
        )

        # Text component
        text_component = components.components["text_component"]
        text_component.component_id = "text_component"
        text_component.component_meta_type = (
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_TEXT
        )

        tofu_data.components.CopyFrom(components)
        tofu_data_list.data.append(tofu_data)

        self.assertTrue(self.handler._check_components_fulfilled(tofu_data_list))

    def test_invalid_template_inputs(self):
        """Test that _check_template_fulfilled handles invalid inputs correctly"""
        # None input
        self.assertFalse(self.handler._check_template_fulfilled(None))

        # Wrong type input
        with self.assertRaises(ValueError):
            self.handler._check_template_fulfilled("not a TofuDataList")

        # Empty data list
        self.assertFalse(self.handler._check_template_fulfilled(TofuDataList()))

        # Wrong data type
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.string_value.value = "not template"
        tofu_data_list.data.append(tofu_data)
        self.assertFalse(self.handler._check_template_fulfilled(tofu_data_list))

    def test_template_without_filled_fields(self):
        """Test that _check_template_fulfilled returns False when no field has content_source_copy or slate_content_source_copy"""
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()

        template = TofuTemplate()
        # Create a field without setting content_source_copy or slate_content_source_copy
        field = template.template_fields["field1"]
        # Note: field_id doesn't exist, removing it

        tofu_data.template.CopyFrom(template)
        tofu_data_list.data.append(tofu_data)

        self.assertFalse(self.handler._check_template_fulfilled(tofu_data_list))

    def test_template_with_content_source_copy_filled(self):
        """Test that _check_template_fulfilled returns True when at least one field has content_source_copy filled"""
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()

        template = TofuTemplate()

        # Empty field
        empty_field = template.template_fields["empty_field"]
        # Note: field_id doesn't exist, removing it

        # Field with content_source_copy
        filled_field = template.template_fields["filled_field"]
        # Note: field_id doesn't exist, removing it
        filled_field.content_source_copy = "Some content source copy"

        tofu_data.template.CopyFrom(template)
        tofu_data_list.data.append(tofu_data)

        self.assertTrue(self.handler._check_template_fulfilled(tofu_data_list))

    def test_template_with_slate_content_source_copy_filled(self):
        """Test that _check_template_fulfilled returns True when at least one field has slate_content_source_copy filled"""
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()

        template = TofuTemplate()

        # Empty field
        empty_field = template.template_fields["empty_field"]
        # Note: field_id doesn't exist, removing it

        # Field with slate_content_source_copy
        filled_field = template.template_fields["filled_field"]
        # Note: field_id doesn't exist, removing it
        filled_field.slate_content_source_copy = "Some slate content source copy"

        tofu_data.template.CopyFrom(template)
        tofu_data_list.data.append(tofu_data)

        self.assertTrue(self.handler._check_template_fulfilled(tofu_data_list))

    @pytest.mark.django_db
    @patch("api.actions.action_handler_base.ActionDataWrapper")
    def test_update_inputs_with_inputs_to_delete(self, mock_action_data_wrapper):
        """Test that update_inputs correctly deletes inputs when their values are empty/None"""
        # Setup mock action instance
        mock_action_instance = Mock()
        mock_action_instance.id = 39737
        mock_action_instance.inputs = {
            "content_type": {
                "data": [{"content_type": {"content_type": "Landing Page"}}]
            },
            "custom_instructions": {
                "data": [{"custom_instruction": {"instruction": "test"}}]
            },
            "targets": {"data": [{"target": {"target_id": "256787"}}]},
            "template": {
                "data": [{"template": {"template_fields": {}}}]
            },  # This will be deleted
        }
        # Add meta field with applied_content_template_id that should be removed when template is deleted
        mock_action_instance.meta = {
            "applied_content_template_id": "3774",
            "other_meta_field": "should_remain",
        }
        mock_action_instance.save = Mock()

        # Setup mock action definition with input definitions
        mock_action_definition = Mock()
        mock_action_definition.required_inputs = {
            "content_type": ActionInputOutputDefinition(
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_TYPE,
                is_saved_to_action=True,
                need_special_handling=False,
            ),
            "template": ActionInputOutputDefinition(
                data_type=TofuDataType.TOFU_DATA_TYPE_TEMPLATE,
                is_saved_to_action=True,
                need_special_handling=True,
            ),
        }
        mock_action_definition.optional_inputs = {
            "custom_instructions": ActionInputOutputDefinition(
                data_type=TofuDataType.TOFU_DATA_TYPE_CUSTOM_INSTRUCTION,
                is_saved_to_action=True,
                need_special_handling=True,
            ),
        }
        # No predefined_inputs for this test
        mock_action_definition.predefined_inputs = []

        # Setup mock action data wrapper
        mock_wrapper_instance = Mock()
        mock_wrapper_instance._action_instance = mock_action_instance
        mock_wrapper_instance.action_definition = mock_action_definition
        mock_action_data_wrapper.return_value = mock_wrapper_instance

        # Create handler instance
        handler = PersonalizationActionHandler(mock_action_instance)

        # Mock the _handle_special_input method to avoid actual processing
        handler._handle_special_input = Mock()

        # Mock the _updated_fields list
        handler._updated_fields = []

        # Create proper TofuData objects for testing
        content_type_data = TofuData()
        content_type_data.content_type.content_type = "Landing Page"

        # Prepare inputs with some empty/None values to be deleted
        inputs_to_update = {
            "content_type": TofuDataList(data=[content_type_data]),
            "template": None,  # This should be deleted
            "custom_instructions": None,  # This should be deleted (changed from "" to None)
        }

        # Call update_inputs
        handler.update_inputs(inputs_to_update, partial_update=True, update_status=True)

        # Verify that the action instance was saved
        mock_action_instance.save.assert_called_once()

        # Verify that empty/None inputs were removed from the action instance inputs
        # template and custom_instructions should be removed because they were None/empty
        self.assertNotIn("template", mock_action_instance.inputs)
        self.assertNotIn("custom_instructions", mock_action_instance.inputs)

        # Verify that content_type was updated (non-empty value)
        self.assertIn("content_type", mock_action_instance.inputs)

        # Verify that applied_content_template_id was removed from meta when template was deleted
        self.assertNotIn("applied_content_template_id", mock_action_instance.meta)
        # Verify that other meta fields remain untouched
        self.assertIn("other_meta_field", mock_action_instance.meta)
        self.assertEqual(mock_action_instance.meta["other_meta_field"], "should_remain")

        # Verify that _handle_special_input was not called for deleted inputs
        # Only content_type should be processed since it's not empty and doesn't have special handling
        # template and custom_instructions should not be called since they were empty/None
        call_count = handler._handle_special_input.call_count
        self.assertEqual(call_count, 0)  # content_type doesn't have special handling

    @pytest.mark.django_db
    @patch("api.actions.action_handler_base.ActionDataWrapper")
    def test_update_inputs_cannot_delete_predefined_inputs(
        self, mock_action_data_wrapper
    ):
        """Test that update_inputs prevents deletion of predefined inputs"""
        # Setup mock action instance
        mock_action_instance = Mock()
        mock_action_instance.id = 39737
        mock_action_instance.inputs = {
            "content_type": {
                "data": [{"content_type": {"content_type": "Landing Page"}}]
            },
            "template": {"data": [{"template": {"template_fields": {}}}]},
        }
        mock_action_instance.save = Mock()

        # Setup mock action definition with predefined inputs
        mock_action_definition = Mock()
        mock_action_definition.required_inputs = {
            "content_type": ActionInputOutputDefinition(
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_TYPE,
                is_saved_to_action=True,
                need_special_handling=False,
            ),
            "template": ActionInputOutputDefinition(
                data_type=TofuDataType.TOFU_DATA_TYPE_TEMPLATE,
                is_saved_to_action=True,
                need_special_handling=True,
            ),
        }
        mock_action_definition.optional_inputs = {}

        # Create predefined inputs - content_type should not be deletable
        predefined_value = PredefinedValue()
        predefined_value.value_type = "content_type"
        predefined_value.value.CopyFrom(
            TofuDataList(data=[TofuData(content_type={"content_type": "Landing Page"})])
        )
        mock_action_definition.predefined_inputs = [predefined_value]

        # Setup mock action data wrapper
        mock_wrapper_instance = Mock()
        mock_wrapper_instance._action_instance = mock_action_instance
        mock_wrapper_instance.action_definition = mock_action_definition
        mock_action_data_wrapper.return_value = mock_wrapper_instance

        # Create handler instance
        handler = PersonalizationActionHandler(mock_action_instance)

        # Mock the _handle_special_input method to avoid actual processing
        handler._handle_special_input = Mock()

        # Mock the _updated_fields list
        handler._updated_fields = []

        # Prepare inputs with some empty/None values to be deleted, including a predefined input
        inputs_to_update = {
            "content_type": None,  # This should NOT be deleted (predefined)
            "template": None,  # This should be deleted (not predefined)
        }

        # Call update_inputs and expect it to raise ValueError for predefined input deletion
        with self.assertRaises(ValueError) as context:
            handler.update_inputs(
                inputs_to_update, partial_update=True, update_status=True
            )

        # Verify the error message mentions the predefined input
        error_message = str(context.exception)
        self.assertIn("Cannot delete predefined inputs", error_message)
        self.assertIn("content_type", error_message)

        # Verify that the action instance was NOT saved due to the error
        mock_action_instance.save.assert_not_called()


if __name__ == "__main__":
    unittest.main()
