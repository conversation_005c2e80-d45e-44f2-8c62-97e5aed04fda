import unittest
from unittest.mock import MagicMock, Mock, patch

import pytest

from ...actions.action_handler_export import ExportActionHandler
from ...models import ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionExecutionParams,
    ActionStatus,
    ActionStatusDetails,
    ActionStatusType,
    ExportEmailParams,
    ExportEmailSetting,
    ExportEmailType,
    ExportPageSetting,
    ExportPageTargetSetting,
    ExportPageType,
    ExportPageUrlSetting,
    ExportParams,
    PlatformType,
    TofuData,
    TofuDataList,
    TofuExportSettings,
)


@pytest.mark.django_db
class TestExportActionHandler(unittest.TestCase):
    def setUp(self):
        self.handler = ExportActionHandler.__new__(ExportActionHandler)
        self.handler._action_data_wrapper = Mock()

        # Setup mock content group
        self.mock_content_group = Mock(spec=ContentGroup)
        self.mock_content_group.id = 1
        # Create a minimal TofuDataList protobuf for export_settings
        tofu_export_settings = TofuExportSettings()
        tofu_export_settings.platform_type = 1  # e.g., PLATFORM_TYPE_HUBSPOT
        tofu_data_list = TofuDataList()
        tofu_data = tofu_data_list.data.add()
        tofu_data.export_settings.CopyFrom(tofu_export_settings)
        self.tofu_data_list = tofu_data_list
        self.mock_content_group.content_group_params = {
            "export_settings": tofu_data_list
        }
        self.handler._action_data_wrapper.content_groups = [self.mock_content_group]

    @patch("api.actions.action_handler_export.get_export_handler")
    @patch("api.actions.action_handler_export.ExportSettingsWrapper")
    def test_execute_impl_success_new_export(
        self, mock_wrapper_class, mock_get_export_handler
    ):
        # Setup export settings wrapper
        mock_wrapper = Mock()
        mock_wrapper.has_settings.return_value = True
        mock_wrapper_class.return_value = mock_wrapper

        # Setup export handler
        mock_export_handler = Mock()
        mock_get_export_handler.return_value = mock_export_handler

        # Setup platform type
        self.handler.get_platform_type = Mock(return_value="hubspot")

        # Setup execution params
        execution_params = ActionExecutionParams()
        export_params = ExportParams()
        export_params.is_new_export = True
        email_params = ExportEmailParams()
        email_params.html_content = "test content"
        email_params.email_file_name = "test.html"
        email_params.email_subject = "Test Subject"
        export_params.email_params.CopyFrom(email_params)
        execution_params.export_params.CopyFrom(export_params)

        # Execute
        self.handler._execute_impl(execution_params)

        # Verify export settings were updated
        self.assertEqual(
            self.mock_content_group.content_group_params["export_settings"],
            self.tofu_data_list,
        )

    @patch("api.actions.action_handler_export.get_export_handler")
    @patch("api.actions.action_handler_export.ExportSettingsWrapper")
    def test_execute_impl_success_existing_export(
        self, mock_wrapper_class, mock_get_export_handler
    ):
        # Setup export settings wrapper
        mock_wrapper = Mock()
        mock_wrapper.has_settings.return_value = True
        mock_wrapper_class.return_value = mock_wrapper

        # Setup export handler
        mock_export_handler = Mock()
        mock_get_export_handler.return_value = mock_export_handler

        # Setup platform type
        self.handler.get_platform_type = Mock(return_value="hubspot")

        # Setup execution params for existing export
        execution_params = ActionExecutionParams()
        export_params = ExportParams()
        export_params.is_new_export = False
        email_params = ExportEmailParams()
        export_params.email_params.CopyFrom(email_params)
        execution_params.export_params.CopyFrom(export_params)

        # Execute
        self.handler._execute_impl(execution_params)

        # Verify export settings were updated
        self.assertEqual(
            self.mock_content_group.content_group_params["export_settings"],
            self.tofu_data_list,
        )

    def test_execute_impl_no_content_groups(self):
        """Test that _execute_impl raises ValueError when no content groups"""
        self.handler._action_data_wrapper.content_groups = []
        execution_params = ActionExecutionParams()

        with self.assertRaises(ValueError) as context:
            self.handler._execute_impl(execution_params)

        self.assertIn("Expected 1 content group, got 0", str(context.exception))

    def test_execute_impl_multiple_content_groups(self):
        """Test that _execute_impl raises ValueError when multiple content groups"""
        mock_content_group1 = Mock(spec=ContentGroup)
        mock_content_group2 = Mock(spec=ContentGroup)
        self.handler._action_data_wrapper.content_groups = [
            mock_content_group1,
            mock_content_group2,
        ]
        execution_params = ActionExecutionParams()

        with self.assertRaises(ValueError) as context:
            self.handler._execute_impl(execution_params)

        self.assertIn("Expected 1 content group, got 2", str(context.exception))

    @patch("api.actions.action_handler_export.ExportSettingsWrapper")
    def test_execute_impl_no_export_settings(self, mock_wrapper_class):
        """Test that _execute_impl raises ValueError when no export settings"""
        # Setup export settings wrapper with no settings
        mock_wrapper = Mock()
        mock_wrapper.has_settings.return_value = False
        mock_wrapper_class.return_value = mock_wrapper

        execution_params = ActionExecutionParams()

        with self.assertRaises(ValueError) as context:
            self.handler._execute_impl(execution_params)

        self.assertIn(
            "No export settings found for content group 1", str(context.exception)
        )

    @patch("api.actions.action_handler_export.get_export_handler")
    @patch("api.actions.action_handler_export.ExportSettingsWrapper")
    def test_execute_impl_no_export_handler(
        self, mock_wrapper_class, mock_get_export_handler
    ):
        """Test that _execute_impl raises ValueError when no export handler found"""
        # Setup export settings wrapper
        mock_wrapper = Mock()
        mock_wrapper.has_settings.return_value = True
        mock_wrapper_class.return_value = mock_wrapper

        # Setup platform type
        self.handler.get_platform_type = Mock(return_value="hubspot")

        # No export handler returned
        mock_get_export_handler.return_value = None

        execution_params = ActionExecutionParams()

        with self.assertRaises(ValueError) as context:
            self.handler._execute_impl(execution_params)

        self.assertIn(
            "No export handler found for content group 1", str(context.exception)
        )

    def test_validate_execution_params_invalid_type(self):
        """Test _validate_execution_params with invalid type"""
        with self.assertRaises(ValueError) as context:
            self.handler._validate_execution_params("invalid")

        self.assertIn("Invalid execution_params type", str(context.exception))

    def test_validate_execution_params_wrong_params_field(self):
        """Test _validate_execution_params with wrong params field"""
        execution_params = ActionExecutionParams()
        # Don't set export_params, so WhichOneof will return something else or None

        with self.assertRaises(ValueError) as context:
            self.handler._validate_execution_params(execution_params)

        self.assertIn(
            "Expected export_params in execution_params", str(context.exception)
        )

    def test_validate_execution_params_no_export_params(self):
        """Test _validate_execution_params with no export_params set"""
        execution_params = ActionExecutionParams()
        export_params = ExportParams()
        # Don't set any fields in export_params
        execution_params.export_params.CopyFrom(export_params)

        # The handler actually logs an error and returns empty params instead of raising
        result = self.handler._validate_execution_params(execution_params)

        expected = {
            "is_new_export": False,
            "params": {},
        }
        self.assertEqual(result, expected)

    def test_validate_execution_params_success_with_email_params(self):
        """Test _validate_execution_params with valid email params"""
        execution_params = ActionExecutionParams()
        export_params = ExportParams()
        export_params.is_new_export = True

        email_params = ExportEmailParams()
        email_params.html_content = "test content"
        email_params.email_file_name = "test.html"
        email_params.email_subject = "Test Subject"
        export_params.email_params.CopyFrom(email_params)
        execution_params.export_params.CopyFrom(export_params)

        # The current implementation will raise AttributeError when trying to access email_type
        result = self.handler._validate_execution_params(execution_params)

        expected = {
            "is_new_export": True,
            "params": {
                "html_content": "test content",
                "email_file_name": "test.html",
                "email_subject": "Test Subject",
            },
        }
        self.assertEqual(result, expected)

    def test_validate_execution_params_with_email_params_missing_email_type_field(self):
        """Test that _validate_execution_params fails due to missing email_type field in protobuf"""
        execution_params = ActionExecutionParams()
        export_params = ExportParams()
        export_params.is_new_export = True

        email_params = ExportEmailParams()
        email_params.html_content = "test content"
        email_params.email_file_name = "test.html"
        email_params.email_subject = "Test Subject"
        export_params.email_params.CopyFrom(email_params)
        execution_params.export_params.CopyFrom(export_params)

        result = self.handler._validate_execution_params(execution_params)

        expected = {
            "is_new_export": True,
            "params": {
                "html_content": "test content",
                "email_file_name": "test.html",
                "email_subject": "Test Subject",
            },
        }
        self.assertEqual(result, expected)

    def test_validate_execution_params_no_email_params(self):
        """Test _validate_execution_params with no email params (logs error, returns empty params)"""
        execution_params = ActionExecutionParams()
        export_params = ExportParams()
        export_params.is_new_export = False
        # Don't set email_params
        execution_params.export_params.CopyFrom(export_params)

        with patch("api.actions.action_handler_export.logging") as mock_logging:
            result = self.handler._validate_execution_params(execution_params)

        expected = {
            "is_new_export": False,
            "params": {},
        }
        self.assertEqual(result, expected)
        mock_logging.error.assert_called()

    def test_get_platform_type(self):
        """Test get_platform_type method"""
        # Setup mock data
        mock_platform_type_data = Mock()
        self.handler._action_data_wrapper.get_input_by_name.return_value = (
            mock_platform_type_data
        )

        with patch(
            "api.actions.action_handler_export.TofuDataListHandler"
        ) as mock_handler:
            mock_handler.get_platform_type.return_value = "hubspot"

            result = self.handler.get_platform_type()

            self.assertEqual(result, "hubspot")
            self.handler._action_data_wrapper.get_input_by_name.assert_called_once_with(
                "platform_type"
            )
            mock_handler.get_platform_type.assert_called_once_with(
                mock_platform_type_data
            )

    @patch("api.actions.action_handler_export.ContentGroup.objects")
    @patch("api.actions.action_handler_export.CloudWatchMetrics")
    @patch("api.actions.action_handler_export.logging")
    @patch("api.actions.action_handler_export.is_export_settings_v2_equal")
    @patch("api.actions.action_handler_export.convert_export_settings_v3_to_v2")
    def test_update_export_settings_shadow_testing(
        self,
        mock_convert,
        mock_is_equal,
        mock_logging,
        mock_cloudwatch,
        mock_content_group_objects,
    ):
        """Test _update_export_settings with is_shadow_testing True triggers logging and metrics, but not DB update."""
        # Setup mock content group
        mock_content_group = Mock(spec=ContentGroup)
        mock_content_group.id = 123
        mock_content_group.content_group_params = {
            "export_settings": self.tofu_data_list
        }

        # Patch self.handler.content_groups to include our mock
        with patch.object(type(self.handler), "content_groups", [mock_content_group]):
            # Patch convert_export_settings_v3_to_v2 to return a new dict each time
            mock_convert.return_value = {"some": "data"}
            # Patch is_export_settings_v2_equal to always return False
            mock_is_equal.return_value = False

            # Set is_shadow_testing = True on the tofu_export_settings
            tofu_export_settings = TofuExportSettings()
            tofu_export_settings.platform_type = 1
            tofu_export_settings.is_shadow_testing = True
            tofu_data_list = TofuDataList()
            tofu_data = tofu_data_list.data.add()
            tofu_data.export_settings.CopyFrom(tofu_export_settings)

            # Call the method
            self.handler._update_export_settings(tofu_data_list)

            # Should log error and put metric
            mock_logging.error.assert_called()
            mock_cloudwatch.put_metric.assert_called_with(
                "ExportSettingsMismatch",
                1,
                [{"Name": "content_group_id", "Value": str(mock_content_group.id)}],
            )
            # Should NOT call bulk_update
            mock_content_group_objects.bulk_update.assert_not_called()

    @patch("api.actions.action_handler_export.ContentGroup.objects")
    @patch("api.actions.action_handler_export.convert_export_settings_v3_to_v2")
    @patch("api.actions.action_handler_export.logging")
    def test_update_export_settings_early_return(
        self, mock_logging, mock_convert, mock_content_group_objects
    ):
        """Test _update_export_settings method returns early when is_shadow_testing is False (temporary behavior until FE changes merge)."""
        # Setup mock content groups
        mock_content_group1 = Mock(spec=ContentGroup)
        mock_content_group1.content_group_params = {"existing": "data"}
        mock_content_group2 = Mock(spec=ContentGroup)

        # Mock the content_groups property
        with patch.object(
            type(self.handler),
            "content_groups",
            [mock_content_group1, mock_content_group2],
        ):
            # Create export settings with is_shadow_testing = False (default)
            export_settings = TofuDataList()
            tofu_data = TofuData()
            tofu_export_settings = TofuExportSettings()
            tofu_export_settings.platform_type = PlatformType.PLATFORM_TYPE_HUBSPOT
            # is_shadow_testing defaults to False

            # Set up email settings as the content category
            email_setting = ExportEmailSetting()
            email_setting.export_type = ExportEmailType.EXPORT_EMAIL_TYPE_DYNAMIC
            email_setting.components_setting["test_component"] = "test_token"
            tofu_export_settings.email.CopyFrom(email_setting)

            tofu_data.export_settings.CopyFrom(tofu_export_settings)
            export_settings.data.append(tofu_data)

            # Execute
            self.handler._update_export_settings(export_settings)

            # When shadow testing is disabled, should return early and skip processing
            # Verify converter was NOT called
            mock_convert.assert_not_called()

            # Verify bulk update was NOT called
            mock_content_group_objects.bulk_update.assert_not_called()

            # Verify early return logging message
            mock_logging.info.assert_called_with(
                "shadow testing is disabled, skipping update export settings"
            )

    # TODO: Uncomment this test once FE changes are merged and remove the early return logic + test_update_export_settings_early_return
    # @patch("api.actions.action_handler_export.ContentGroup.objects")
    # @patch("api.actions.action_handler_export.convert_export_settings_v3_to_v2")
    # def test_update_export_settings(self, mock_convert, mock_content_group_objects):
    #     """Test _update_export_settings method when is_shadow_testing is False (default) - normal processing."""
    #     # Setup mock content groups
    #     mock_content_group1 = Mock(spec=ContentGroup)
    #     mock_content_group1.content_group_params = {"existing": "data"}
    #     mock_content_group2 = Mock(spec=ContentGroup)
    #     # Create a minimal TofuDataList protobuf for export_settings
    #     tofu_export_settings = TofuExportSettings()
    #     tofu_export_settings.platform_type = 1  # e.g., PLATFORM_TYPE_HUBSPOT
    #     tofu_data_list = TofuDataList()
    #     tofu_data = tofu_data_list.data.add()
    #     tofu_data.export_settings.CopyFrom(tofu_export_settings)
    #     mock_content_group2.content_group_params = {"export_settings": tofu_data_list}

    #     # Mock the content_groups property
    #     with patch.object(
    #         type(self.handler),
    #         "content_groups",
    #         [mock_content_group1, mock_content_group2],
    #     ):
    #         # Setup mock converter
    #         tofu_export_settings_new1 = TofuDataList()
    #         tofu_export_settings_new2 = TofuDataList()
    #         mock_convert.side_effect = [
    #             tofu_export_settings_new1,
    #             tofu_export_settings_new2,
    #         ]

    #         # Create a proper TofuDataList with export settings (is_shadow_testing defaults to False)
    #         export_settings = TofuDataList()
    #         tofu_data = TofuData()
    #         tofu_export_settings = TofuExportSettings()
    #         tofu_export_settings.platform_type = PlatformType.PLATFORM_TYPE_HUBSPOT
    #         # is_shadow_testing defaults to False

    #         # Set up email settings as the content category
    #         email_setting = ExportEmailSetting()
    #         email_setting.export_type = ExportEmailType.EXPORT_EMAIL_TYPE_DYNAMIC
    #         email_setting.components_setting["test_component"] = "test_token"
    #         tofu_export_settings.email.CopyFrom(email_setting)

    #         tofu_data.export_settings.CopyFrom(tofu_export_settings)
    #         export_settings.data.append(tofu_data)

    #         # Execute
    #         self.handler._update_export_settings(export_settings)

    #         # Verify converter was called correctly
    #         self.assertEqual(mock_convert.call_count, 2)
    #         mock_convert.assert_any_call(export_settings, {}, mock_content_group1)
    #         mock_convert.assert_any_call(
    #             export_settings, tofu_data_list, mock_content_group2
    #         )

    #         # Verify content group params were updated
    #         self.assertEqual(
    #             mock_content_group1.content_group_params["export_settings"],
    #             tofu_export_settings_new1,
    #         )
    #         self.assertEqual(
    #             mock_content_group2.content_group_params["export_settings"],
    #             tofu_export_settings_new2,
    #         )

    #         # Verify bulk update was called
    #         mock_content_group_objects.bulk_update.assert_called_once_with(
    #             [mock_content_group1, mock_content_group2], ["content_group_params"]
    #         )

    # TODO: Uncomment this test once FE changes are merged and remove the early return logic + we support landing page export settings
    # @patch("api.actions.action_handler_export.ContentGroup.objects")
    # @patch("api.actions.action_handler_export.convert_export_settings_v3_to_v2")
    # def test_update_export_settings_with_landing_page(
    #     self, mock_convert, mock_content_group_objects
    # ):
    #     """Test _update_export_settings method with landing page settings"""
    #     # Setup mock content groups
    #     mock_content_group1 = Mock(spec=ContentGroup)
    #     mock_content_group1.content_group_params = {"existing": "data"}
    #     mock_content_group2 = Mock(spec=ContentGroup)
    #     mock_content_group2.content_group_params = {
    #         "export_settings": {"old": "settings"}
    #     }

    #     # Mock the content_groups property
    #     with patch.object(
    #         type(self.handler),
    #         "content_groups",
    #         [mock_content_group1, mock_content_group2],
    #     ):
    #         # Setup mock converter
    #         mock_convert.side_effect = [{"new": "settings1"}, {"new": "settings2"}]

    #         # Create a proper TofuDataList with landing page export settings
    #         export_settings = TofuDataList()
    #         tofu_data = TofuData()
    #         tofu_export_settings = TofuExportSettings()
    #         tofu_export_settings.platform_type = PlatformType.PLATFORM_TYPE_HUBSPOT

    #         # Set up landing page settings as the content category
    #         page_setting = ExportPageSetting()
    #         page_setting.export_type = ExportPageType.EXPORT_PAGE_TYPE_DYNAMIC

    #         # Add target settings
    #         page_target_setting_1 = ExportPageTargetSetting()
    #         page_target_setting_1.page_slug = "jamie_brown@www_austintexas_gov"
    #         page_setting.targets_setting[*********].CopyFrom(page_target_setting_1)

    #         page_target_setting_2 = ExportPageTargetSetting()
    #         page_target_setting_2.page_slug = "morgan_johnson@www_illinoistollway_com"
    #         page_setting.targets_setting[*********].CopyFrom(page_target_setting_2)

    #         # Add URL settings
    #         page_url_setting = ExportPageUrlSetting()
    #         page_url_setting.domain = (
    #             "tofu-demo-account-********.hubspotpagebuilder.com/"
    #         )
    #         page_url_setting.group_slug = "carbyne_prospects_list_csv_3"
    #         page_url_setting.URL_token = "tofu_url_361286"
    #         page_setting.url_setting.CopyFrom(page_url_setting)

    #         # Add components setting (token mapping)
    #         page_setting.components_setting["test_component"] = "test_token"

    #         tofu_export_settings.page.CopyFrom(page_setting)
    #         tofu_data.export_settings.CopyFrom(tofu_export_settings)
    #         export_settings.data.append(tofu_data)

    #         # Execute - this should not raise any errors
    #         self.handler._update_export_settings(export_settings)

    #         # Verify converter was called correctly
    #         self.assertEqual(mock_convert.call_count, 2)
    #         mock_convert.assert_any_call(export_settings, {}, mock_content_group1)
    #         mock_convert.assert_any_call(
    #             export_settings, {"old": "settings"}, mock_content_group2
    #         )

    #         # Verify content group params were updated
    #         self.assertEqual(
    #             mock_content_group1.content_group_params["export_settings"],
    #             {"new": "settings1"},
    #         )
    #         self.assertEqual(
    #             mock_content_group2.content_group_params["export_settings"],
    #             {"new": "settings2"},
    #         )

    #         # Verify bulk update was called
    #         mock_content_group_objects.bulk_update.assert_called_once_with(
    #             [mock_content_group1, mock_content_group2], ["content_group_params"]
    #         )

    @patch("api.actions.action_handler_export.logging")
    @patch("api.actions.action_handler_export.GenStatusUpdater")
    @patch("api.actions.action_handler_export.convert_export_response_v2_to_v3")
    def test_get_post_ready_status_details_no_content_groups(
        self, mock_convert, mock_gen_status_updater, mock_logging
    ):
        """Test _get_post_ready_status_details when no content groups are found"""
        # Setup empty content groups
        self.handler._action_data_wrapper.content_groups = []

        # Setup mocks to simulate no generated content
        mock_gen_status_updater_instance = Mock()
        mock_gen_status_updater.return_value = mock_gen_status_updater_instance

        # Execute
        result = self.handler._get_post_ready_status_details()

        # Should log error about no content groups
        mock_logging.error.assert_called_with(
            "no content_groups found for export action"
        )

        # Should return ActionStatus with MISSING_INPUT since no content groups means no generated content
        expected_status = ActionStatus(
            status_type=ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT,
        )
        self.assertEqual(result.status_type, expected_status.status_type)
        self.assertFalse(result.HasField("details"))

    @patch("api.actions.action_handler_export.logging")
    @patch("api.actions.action_handler_export.GenStatusUpdater")
    def test_get_post_ready_status_details_no_generated_content(
        self, mock_gen_status_updater, mock_logging
    ):
        """Test _get_post_ready_status_details when no content has been generated"""
        # Setup mock content groups
        mock_content_group1 = Mock(spec=ContentGroup)
        mock_content_group2 = Mock(spec=ContentGroup)
        self.handler._action_data_wrapper.content_groups = [
            mock_content_group1,
            mock_content_group2,
        ]

        # Setup GenStatusUpdater to return stats with 0 success count
        mock_gen_status_updater_instance = Mock()
        mock_gen_status_updater.return_value = mock_gen_status_updater_instance

        mock_stats = Mock()
        mock_stats.cnts_succ = 0
        mock_status = {"stats": mock_stats}
        mock_gen_status_updater_instance.get_content_group_gen_status_v3.return_value = (
            mock_status
        )

        # Execute
        result = self.handler._get_post_ready_status_details()

        # Should return ActionStatus with MISSING_INPUT
        expected_status = ActionStatus(
            status_type=ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT,
        )
        self.assertEqual(result.status_type, expected_status.status_type)
        self.assertFalse(result.HasField("details"))

    @patch("api.actions.action_handler_export.logging")
    @patch("api.actions.action_handler_export.GenStatusUpdater")
    def test_get_post_ready_status_details_exception_during_status_check(
        self, mock_gen_status_updater, mock_logging
    ):
        """Test _get_post_ready_status_details when exception occurs during status check"""
        # Setup mock content groups
        mock_content_group1 = Mock(spec=ContentGroup)
        mock_content_group2 = Mock(spec=ContentGroup)
        self.handler._action_data_wrapper.content_groups = [
            mock_content_group1,
            mock_content_group2,
        ]

        # Setup GenStatusUpdater to raise exception for first content group, succeed for second
        mock_gen_status_updater_instance = Mock()
        mock_gen_status_updater.return_value = mock_gen_status_updater_instance

        def side_effect(content_group):
            if content_group == mock_content_group1:
                raise Exception("Test exception")
            else:
                # Return stats with 0 success count for second group
                mock_stats = Mock()
                mock_stats.cnts_succ = 0
                return {"stats": mock_stats}

        mock_gen_status_updater_instance.get_content_group_gen_status_v3.side_effect = (
            side_effect
        )

        # Execute
        result = self.handler._get_post_ready_status_details()

        # Should log exception
        mock_logging.exception.assert_called()

        # Should still return ActionStatus with MISSING_INPUT since no successful content found
        expected_status = ActionStatus(
            status_type=ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT,
        )
        self.assertEqual(result.status_type, expected_status.status_type)
        self.assertFalse(result.HasField("details"))

    @patch("api.actions.action_handler_export.convert_export_response_v2_to_v3")
    def test_get_post_ready_status_details_invalid_export_response(self, mock_convert):
        """Test _get_post_ready_status_details when convert_export_response_v2_to_v3 returns invalid response"""
        # Setup mock content group with generated content
        mock_content_group = Mock(spec=ContentGroup)
        self.handler._action_data_wrapper.content_groups = [mock_content_group]
        self.handler._action_data_wrapper.content_group = mock_content_group
        mock_content_group.content_group_params = {"export_response": {"some": "data"}}

        # Setup GenStatusUpdater to return stats with success count > 0
        with patch(
            "api.actions.action_handler_export.GenStatusUpdater"
        ) as mock_gen_status_updater:
            mock_gen_status_updater_instance = Mock()
            mock_gen_status_updater.return_value = mock_gen_status_updater_instance

            mock_stats = Mock()
            mock_stats.cnts_succ = 5
            mock_status = {"stats": mock_stats}
            mock_gen_status_updater_instance.get_content_group_gen_status_v3.return_value = (
                mock_status
            )

            # Setup platform type
            self.handler.get_platform_type = Mock(return_value="hubspot")

            # Setup convert function to return invalid response (not ActionStatusDetails)
            mock_convert.return_value = "invalid_response"

            # Execute - should raise ValueError
            with self.assertRaises(ValueError) as context:
                self.handler._get_post_ready_status_details()

            self.assertIn(
                "Invalid export response: invalid_response", str(context.exception)
            )

    @patch("api.actions.action_handler_export.convert_export_response_v2_to_v3")
    @patch("api.actions.action_handler_export.GenStatusUpdater")
    def test_get_post_ready_status_details_success_with_proper_details(
        self, mock_gen_status_updater, mock_convert
    ):
        """Test _get_post_ready_status_details success case with proper details populated"""
        # Setup mock content group with generated content
        mock_content_group = Mock(spec=ContentGroup)
        self.handler._action_data_wrapper.content_groups = [mock_content_group]
        self.handler._action_data_wrapper.content_group = mock_content_group
        mock_content_group.content_group_params = {"export_response": {"some": "data"}}

        # Setup GenStatusUpdater to return stats with success count > 0
        mock_gen_status_updater_instance = Mock()
        mock_gen_status_updater.return_value = mock_gen_status_updater_instance

        mock_stats = Mock()
        mock_stats.cnts_succ = 5
        mock_status = {"stats": mock_stats}
        mock_gen_status_updater_instance.get_content_group_gen_status_v3.return_value = (
            mock_status
        )

        # Setup platform type
        self.handler.get_platform_type = Mock(return_value="hubspot")

        # Setup convert function to return proper ActionStatusDetails
        mock_action_status_details = ActionStatusDetails()
        mock_action_status_details.stats.cnts_fail = 2
        mock_action_status_details.stats.cnts_succ = 8
        mock_action_status_details.stats.cnts_running = 1
        mock_convert.return_value = mock_action_status_details

        # Setup _get_status_type_based_stats method
        self.handler._get_status_type_based_stats = Mock(
            return_value=ActionStatusType.ACTION_STATUS_TYPE_RUNNING
        )

        # Execute
        result = self.handler._get_post_ready_status_details()

        # Verify GenStatusUpdater was called
        mock_gen_status_updater_instance.get_content_group_gen_status_v3.assert_called_with(
            mock_content_group
        )

        # Verify get_platform_type was called
        self.handler.get_platform_type.assert_called_once()

        # Verify convert_export_response_v2_to_v3 was called with correct params
        mock_convert.assert_called_once_with(
            "hubspot", mock_content_group.content_group_params
        )

        # Verify _get_status_type_based_stats was called with correct stats
        self.handler._get_status_type_based_stats.assert_called_once_with(
            total_fail=2, total_succ=8, total_running=1
        )

        # Verify result has proper structure
        self.assertIsInstance(result, ActionStatus)
        self.assertEqual(
            result.status_type, ActionStatusType.ACTION_STATUS_TYPE_RUNNING
        )

        # Verify details are properly populated
        self.assertTrue(result.HasField("details"))
        self.assertEqual(result.details.stats.cnts_fail, 2)
        self.assertEqual(result.details.stats.cnts_succ, 8)
        self.assertEqual(result.details.stats.cnts_running, 1)

    @patch("api.actions.action_handler_export.convert_export_response_v2_to_v3")
    @patch("api.actions.action_handler_export.GenStatusUpdater")
    def test_get_post_ready_status_details_multiple_content_groups_with_success(
        self, mock_gen_status_updater, mock_convert
    ):
        """Test _get_post_ready_status_details with multiple content groups where second has generated content"""
        # Setup mock content groups
        mock_content_group1 = Mock(spec=ContentGroup)
        mock_content_group2 = Mock(spec=ContentGroup)
        self.handler._action_data_wrapper.content_groups = [
            mock_content_group1,
            mock_content_group2,
        ]
        self.handler._action_data_wrapper.content_group = (
            mock_content_group1  # First content group used for details
        )
        mock_content_group1.content_group_params = {"export_response": {"some": "data"}}

        # Setup GenStatusUpdater
        mock_gen_status_updater_instance = Mock()
        mock_gen_status_updater.return_value = mock_gen_status_updater_instance

        def get_status_side_effect(content_group):
            if content_group == mock_content_group1:
                # First group has no successful content
                mock_stats = Mock()
                mock_stats.cnts_succ = 0
                return {"stats": mock_stats}
            else:
                # Second group has successful content
                mock_stats = Mock()
                mock_stats.cnts_succ = 3
                return {"stats": mock_stats}

        mock_gen_status_updater_instance.get_content_group_gen_status_v3.side_effect = (
            get_status_side_effect
        )

        # Setup platform type and other mocks
        self.handler.get_platform_type = Mock(return_value="hubspot")

        # Setup convert function to return proper ActionStatusDetails
        mock_action_status_details = ActionStatusDetails()
        mock_action_status_details.stats.cnts_fail = 0
        mock_action_status_details.stats.cnts_succ = 5
        mock_action_status_details.stats.cnts_running = 0
        mock_convert.return_value = mock_action_status_details

        # Setup _get_status_type_based_stats method
        self.handler._get_status_type_based_stats = Mock(
            return_value=ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        )

        # Execute
        result = self.handler._get_post_ready_status_details()

        # Should process both content groups and find generated content in second group
        self.assertEqual(
            mock_gen_status_updater_instance.get_content_group_gen_status_v3.call_count,
            2,
        )

        # Should use first content group for details (as per _action_data_wrapper.content_group)
        mock_convert.assert_called_once_with(
            "hubspot", mock_content_group1.content_group_params
        )

        # Should return proper ActionStatus with details
        self.assertEqual(
            result.status_type, ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        )
        self.assertTrue(result.HasField("details"))
        self.assertEqual(result.details.stats.cnts_succ, 5)


if __name__ == "__main__":
    unittest.main()
