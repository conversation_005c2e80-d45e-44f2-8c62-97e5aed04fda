import unittest
from typing import Optional

from ...actions.action_system_config_loader import ActionSystemConfigLoader
from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionType,
    PlatformType,
)
from ...shared_types import ContentType


class TestActionSystemConfigLoader(unittest.TestCase):
    def setUp(self):
        self.loader = ActionSystemConfigLoader()

    def test_get_definition_with_string(self):
        definition = self.loader.get_definition("ACTION_TYPE_REPURPOSE_EMAIL_SDR")
        self.assertIsNotNone(definition)
        self.assertEqual(
            definition.action_type, ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR
        )

    def test_get_definition_with_enum(self):
        definition = self.loader.get_definition(
            ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR
        )
        self.assertIsNotNone(definition)
        self.assertEqual(
            definition.action_type, ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR
        )

    def test_get_definition_invalid_type(self):
        with self.assertRaises(ValueError):
            self.loader.get_definition("INVALID_ACTION_TYPE")

    def test_get_definition_by_action_category_and_inputs_user_input(self):
        inputs = {}
        definition = self.loader.get_definition_by_action_category_and_inputs(
            ActionCategory.ACTION_CATEGORY_USER_INPUT, inputs
        )
        self.assertIsNotNone(definition)
        self.assertEqual(
            definition.action_type,
            ActionType.ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT,
        )

    def test_get_definition_by_action_category_and_inputs_repurpose(self):
        inputs = {
            "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                ContentType.EmailSDR.value
            )
        }
        definition = self.loader.get_definition_by_action_category_and_inputs(
            ActionCategory.ACTION_CATEGORY_REPURPOSE, inputs
        )
        self.assertIsNotNone(definition)
        self.assertEqual(
            definition.action_type, ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR
        )

    def test_get_definition_by_action_category_and_inputs_personalize(self):
        inputs = {
            "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                ContentType.EmailSDR.value
            )
        }
        definition = self.loader.get_definition_by_action_category_and_inputs(
            ActionCategory.ACTION_CATEGORY_PERSONALIZE, inputs
        )
        self.assertIsNotNone(definition)
        self.assertEqual(
            definition.action_type, ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_SDR
        )

    def test_get_definition_by_action_category_and_inputs_export(self):
        inputs = {
            "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                ContentType.EmailSDR.value
            ),
            "platform_type": TofuDataListHandler.convert_platform_type_to_tofu_data(
                PlatformType.PLATFORM_TYPE_HUBSPOT
            ),
        }
        definition = self.loader.get_definition_by_action_category_and_inputs(
            ActionCategory.ACTION_CATEGORY_EXPORT, inputs
        )
        self.assertIsNotNone(definition)
        self.assertEqual(
            definition.action_type, ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL
        )

    def test_get_definition_by_action_category_and_inputs_invalid_content_type(self):
        inputs = {
            "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                ContentType.Webinar.value
            )
        }
        with self.assertRaises(ValueError):
            self.loader.get_definition_by_action_category_and_inputs(
                ActionCategory.ACTION_CATEGORY_PERSONALIZE, inputs
            )

    def test_get_definition_by_action_category_and_inputs_invalid_platform_type(self):
        inputs = {
            "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                ContentType.EmailSDR.value
            ),
            "platform_type": TofuDataListHandler.convert_platform_type_to_tofu_data(
                PlatformType.PLATFORM_TYPE_LINKEDIN
            ),
        }
        with self.assertRaises(ValueError):
            self.loader.get_definition_by_action_category_and_inputs(
                ActionCategory.ACTION_CATEGORY_EXPORT, inputs
            )

    def test_get_definition_by_action_category_and_inputs_invalid_category(self):
        inputs = {}
        with self.assertRaises(ValueError):
            self.loader.get_definition_by_action_category_and_inputs(
                "INVALID_CATEGORY", inputs
            )

    def test_get_definition_by_action_category_and_inputs_repurpose_all_content_types(
        self,
    ):
        """Test repurpose action with all supported content types."""
        content_types_and_actions = {
            ContentType.EmailMarketing: ActionType.ACTION_TYPE_REPURPOSE_EMAIL_MARKETING,
            ContentType.LandingPage: ActionType.ACTION_TYPE_REPURPOSE_LANDING_PAGE,
            ContentType.Whitepaper: ActionType.ACTION_TYPE_REPURPOSE_WHITEPAPER,
            ContentType.EBook: ActionType.ACTION_TYPE_REPURPOSE_EBOOK,
            ContentType.CaseStudy: ActionType.ACTION_TYPE_REPURPOSE_CASE_STUDY,
            ContentType.SalesDeck: ActionType.ACTION_TYPE_REPURPOSE_SALES_DECK,
            ContentType.Webinar: ActionType.ACTION_TYPE_REPURPOSE_WEBINAR,
            ContentType.BlogPost: ActionType.ACTION_TYPE_REPURPOSE_BLOG_POST,
            ContentType.QuotesHighlights: ActionType.ACTION_TYPE_REPURPOSE_QUOTES_HIGHLIGHTS,
            ContentType.Statistics: ActionType.ACTION_TYPE_REPURPOSE_STATISTICS,
            ContentType.AdCampaignGeneral: ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GENERAL,
            ContentType.AdCampaignLinkedin: ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN,
            ContentType.SocialGeneral: ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_GENERAL,
            ContentType.SocialLinkedin: ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_LINKEDIN,
            ContentType.MessageLinkedin: ActionType.ACTION_TYPE_REPURPOSE_MESSAGE_LINKEDIN,
            ContentType.Other: ActionType.ACTION_TYPE_REPURPOSE_OTHER,
        }

        for content_type, expected_action in content_types_and_actions.items():
            inputs = {
                "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                    content_type.value
                )
            }
            definition = self.loader.get_definition_by_action_category_and_inputs(
                ActionCategory.ACTION_CATEGORY_REPURPOSE, inputs
            )
            self.assertIsNotNone(definition)
            self.assertEqual(definition.action_type, expected_action)

    def test_get_definition_by_action_category_and_inputs_personalize_all_content_types(
        self,
    ):
        """Test personalize action with all supported content types."""
        content_types_and_actions = {
            ContentType.EmailMarketing: ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_MARKETING,
            ContentType.LandingPage: ActionType.ACTION_TYPE_PERSONALIZE_LANDING_PAGE,
            ContentType.Whitepaper: ActionType.ACTION_TYPE_PERSONALIZE_WHITEPAPER,
            ContentType.EBook: ActionType.ACTION_TYPE_PERSONALIZE_EBOOK,
            ContentType.AdCampaignGeneral: ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GENERAL,
            ContentType.AdCampaignLinkedin: ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN,
            ContentType.MessageLinkedin: ActionType.ACTION_TYPE_PERSONALIZE_MESSAGE_LINKEDIN,
            ContentType.SalesDeck: ActionType.ACTION_TYPE_PERSONALIZE_SALES_DECK,
            ContentType.Other: ActionType.ACTION_TYPE_PERSONALIZE_OTHER,
        }

        for content_type, expected_action in content_types_and_actions.items():
            inputs = {
                "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                    content_type.value
                )
            }
            definition = self.loader.get_definition_by_action_category_and_inputs(
                ActionCategory.ACTION_CATEGORY_PERSONALIZE, inputs
            )
            self.assertIsNotNone(definition)
            self.assertEqual(definition.action_type, expected_action)

    def test_get_definition_by_action_category_and_inputs_export_all_platforms(self):
        """Test export action with all supported platform and content type combinations."""
        test_cases = [
            {
                "platform": PlatformType.PLATFORM_TYPE_HUBSPOT,
                "content_type": ContentType.EmailSDR,
                "expected_action": ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL,
            },
            {
                "platform": PlatformType.PLATFORM_TYPE_HUBSPOT,
                "content_type": ContentType.EmailMarketing,
                "expected_action": ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL,
            },
            {
                "platform": PlatformType.PLATFORM_TYPE_HUBSPOT,
                "content_type": ContentType.LandingPage,
                "expected_action": ActionType.ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE,
            },
            {
                "platform": PlatformType.PLATFORM_TYPE_MARKETO,
                "content_type": ContentType.EmailSDR,
                "expected_action": ActionType.ACTION_TYPE_EXPORT_MARKETO_EMAIL,
            },
            {
                "platform": PlatformType.PLATFORM_TYPE_MARKETO,
                "content_type": ContentType.LandingPage,
                "expected_action": ActionType.ACTION_TYPE_EXPORT_MARKETO_LANDING_PAGE,
            },
            {
                "platform": PlatformType.PLATFORM_TYPE_SALESFORCE,
                "content_type": ContentType.EmailSDR,
                "expected_action": ActionType.ACTION_TYPE_EXPORT_SALESFORCE_EMAIL,
            },
        ]

        for test_case in test_cases:
            inputs = {
                "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                    test_case["content_type"].value
                ),
                "platform_type": TofuDataListHandler.convert_platform_type_to_tofu_data(
                    test_case["platform"]
                ),
            }
            definition = self.loader.get_definition_by_action_category_and_inputs(
                ActionCategory.ACTION_CATEGORY_EXPORT, inputs
            )
            self.assertIsNotNone(definition)
            self.assertEqual(definition.action_type, test_case["expected_action"])

    def test_get_definition_by_action_category_and_inputs_export_invalid_combinations(
        self,
    ):
        """Test export action with invalid platform and content type combinations."""
        invalid_test_cases = [
            {
                "platform": PlatformType.PLATFORM_TYPE_HUBSPOT,
                "content_type": ContentType.Whitepaper,
            },
            {
                "platform": PlatformType.PLATFORM_TYPE_MARKETO,
                "content_type": ContentType.SalesDeck,
            },
            {
                "platform": PlatformType.PLATFORM_TYPE_SALESFORCE,
                "content_type": ContentType.LandingPage,
            },
        ]

        for test_case in invalid_test_cases:
            inputs = {
                "content_type": TofuDataListHandler.convert_content_type_to_tofu_data(
                    test_case["content_type"].value
                ),
                "platform_type": TofuDataListHandler.convert_platform_type_to_tofu_data(
                    test_case["platform"]
                ),
            }
            with self.assertRaises(ValueError):
                self.loader.get_definition_by_action_category_and_inputs(
                    ActionCategory.ACTION_CATEGORY_EXPORT, inputs
                )

    def test_get_definition_with_action_type_int(self):
        """Test get_definition with integer action type."""
        definition = self.loader.get_definition(
            int(ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR)
        )
        self.assertIsNotNone(definition)
        self.assertEqual(
            definition.action_type, ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR
        )

    def test_get_all_action_types_str_in_order(self):
        """Test getting ordered list of all action types as strings."""
        action_types_str = self.loader.get_all_action_types_str_in_order()

        # Verify result is non-empty list
        self.assertIsInstance(action_types_str, list)
        self.assertTrue(len(action_types_str) > 0)

        # Verify all elements are strings and valid action type names
        for action_type in action_types_str:
            self.assertIsInstance(action_type, str)
            # Verify we can convert back to enum (would raise ValueError if invalid)
            self.assertIsNotNone(ActionType.Value(action_type))

        # Check some expected action types are present
        expected_types = [
            "ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT",
            "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
            "ACTION_TYPE_EXPORT_HUBSPOT_EMAIL",
        ]
        for expected_type in expected_types:
            self.assertIn(expected_type, action_types_str)

    def test_get_all_eligible_actions(self):
        """Test getting all eligible actions."""
        eligible_actions = self.loader.get_all_eligible_actions()

        # Verify result is not None
        self.assertIsNotNone(eligible_actions)

        # Verify it has content (assuming it's a collection)
        if hasattr(eligible_actions, "__len__"):
            self.assertTrue(
                len(eligible_actions) > 0, "Eligible actions should not be empty"
            )

        # If it's a mapping/iterable, check it contains data
        if hasattr(eligible_actions, "items"):
            # It's dictionary-like
            self.assertTrue(
                list(eligible_actions.items()),
                "Eligible actions dictionary should not be empty",
            )
        elif hasattr(eligible_actions, "__iter__") and not isinstance(
            eligible_actions, (str, bytes)
        ):
            # It's an iterable (but not a string)
            self.assertTrue(
                list(eligible_actions), "Eligible actions iterable should not be empty"
            )


if __name__ == "__main__":
    unittest.main()
