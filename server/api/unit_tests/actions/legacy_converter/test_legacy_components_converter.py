import logging
import unittest
from datetime import datetime

import pytest

from ....actions.legacy_converter.legacy_components_converter import (
    ComponentConvertComparisor,
    convert_components_v2_to_v3,
    convert_components_v3_to_v2,
)
from ....models import AssetInfo, AssetInfoGroup, CompanyInfo, Playbook
from ....validator.content_group_validator import (
    ComponentMetaType,
    ComponentType,
    ContentGroupComponent,
    ContentGroupComponentMeta,
    ContentGroupImageComponent,
    ContentGroupLinkComponent,
    ContentGroupVideoComponent,
)


@pytest.mark.django_db
class TestLegacyComponentsConverter(unittest.TestCase):
    def setUp(self):
        # Create a CompanyInfo instance
        self.company_info = CompanyInfo.objects.create()

        # Create a Playbook instance with the associated CompanyInfo
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=self.company_info,  # Associate with the company info
        )
        self.playbook_id = self.playbook.id

        # Add data to the temporary database
        self.asset_group1 = AssetInfoGroup.objects.create(
            asset_info_group_key="group1",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.asset_group2 = AssetInfoGroup.objects.create(
            asset_info_group_key="group2",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.assets = [
            AssetInfo.objects.create(
                asset_key="asset1", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset2", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset3", asset_info_group=self.asset_group2
            ),
        ]

    def util_test_conversion(self, components_v2):
        components_v3 = convert_components_v2_to_v3(self.playbook_id, components_v2)
        components_v2_converted = convert_components_v3_to_v2(components_v3)
        if components_v2 != components_v2_converted:
            self.assertTrue(
                ComponentConvertComparisor().compare(
                    components_v2, components_v2_converted
                )
            )
        return True

    def test_convert_components_p1(self):
        components_v2 = {
            "1iBNcy4VSPbQmWWA": {
                "link": {
                    "type": "tofu_link",
                    "label": "NH&P DG / Landing Page 1",
                    "contentGroupId": 234320,
                },
                "meta": {
                    "type": "link",
                    "time_added": 1734459313059,
                    "component_type": "body",
                    "isEmailSubject": False,
                    "precedingContent": "",
                    "succeedingContent": "",
                },
                "text": ".",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p2(self):
        components_v2 = {
            "8y5yz5EXB27Q7_7L": {
                "meta": {
                    "time_added": 1733840516004,
                    "component_type": "email body",
                    "isEmailSubject": False,
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": None,
                                "instruction": "this should be just the target's first name",
                            }
                        ]
                    },
                    "precedingContent": "Hey ",
                    "succeedingContent": " - I saw Company (congrats on the move btw) are focused on growing your enterprise customer base in EMEA. Creating and repurposing more assets that appeal to this target group could help with that. Would be great to chat in the future if that's relevant. Sure you're going to do great at Company!Best, JoePS - made you this custom version of the Tofu ABM page as an example of another channel we work over - .",
                },
                "text": "firstName",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p3(self):
        components_v2 = {
            "9NVqNu-an8HBUI6n": {
                "meta": {
                    "time_added": 1733840344468,
                    "component_type": "email body",
                    "isEmailSubject": False,
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": None,
                                "instruction": "this should be just the target's company name\n\n",
                            }
                        ]
                    },
                    "precedingContent": "Hey firstName - I saw Company (congrats on the move btw) are focused on growing your enterprise customer base in EMEA . Creating and repurposing more assets that appeal to this target group could help with that. Would be great to chat in the future if that's relevant. Sure you're going to do great at ",
                    "succeedingContent": "!Best, JoePS - made you this custom version of the Tofu ABM page as an example of another channel we work over - .",
                },
                "text": "Company",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p4(self):
        components_v2 = {
            "D_Pj5r8hVM3wR5Gw": {
                "meta": {
                    "time_added": 1733839836173,
                    "component_type": "email body",
                    "isEmailSubject": False,
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": None,
                                "instruction": "this should reference a company goal or initiative from tofu insights",
                            },
                            {
                                "assets": None,
                                "instruction": 'this should start with "focused on..."',
                            },
                        ]
                    },
                    "precedingContent": "Hey firstName - I saw Company (congrats on the move btw) are ",
                    "succeedingContent": ". Creating and repurposing more assets that appeal to this target group could help with that. Would be great to chat in the future if that's relevant. Sure you're going to do great at Company!Best, JoePS - made you this custom version of the Tofu ABM page as an example of another channel we work over - .",
                },
                "text": "focused on growing your enterprise customer base in EMEA",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p5(self):
        components_v2 = {
            "rN4m_T4vTidOjrew": {
                "meta": {
                    "time_added": 1733839830284,
                    "component_type": "email body",
                    "isEmailSubject": False,
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": None,
                                "instruction": "this should be just the target's company name",
                            }
                        ]
                    },
                    "precedingContent": "Hey firstName - I saw ",
                    "succeedingContent": " (congrats on the move btw) are focused on growing your enterprise customer base in EMEA. Creating and repurposing more assets that appeal to this target group could help with that. Would be great to chat in the future if that's relevant. Sure you're going to do great at Company!Best, JoePS - made you this custom version of the Tofu ABM page as an example of another channel we work over - .",
                },
                "text": "Company",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p6(self):
        components_v2 = {
            "uXX0J4E7CovedlG4": {
                "meta": {
                    "time_added": 1733839828703,
                    "component_type": "email subject",
                    "isEmailSubject": True,
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": None,
                                "instruction": "this should be a 3-5 word subject line which mentions the target's company by name once",
                            }
                        ]
                    },
                    "precedingContent": " ",
                    "succeedingContent": "",
                },
                "text": "Thoughts on ABM at Company",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p7(self):
        components_v2 = {
            "5nRp6jqrshvgnt_z": {
                "meta": {
                    "type": "text",
                    "html_tag": "<span>",
                    "time_added": 1719512677781,
                    "html_tag_index": None,
                    "component_params": {"custom_instructions": []},
                    "preceding_element": "<h2>Spec's fraud protection reduced Indiegogo’s attack pressure by 90%</h2>",
                    "succeeding_element": "<strong>Find out how Indiegogo achieved real results with Spec:</strong>",
                },
                "text": "Indiegogo, an online marketplace that connects investors to entrepreneurs, achieved measurable results after deploying Spec's fraud solution, which enables end-to-end customer visibility using complete journey data. Now, attackers are funneled into intelligent honeypots that prevent future threats.",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p8(self):
        components_v2 = {
            "307659ef-8caf-4325-b417-ba9fa07fdcb4": {
                "meta": {
                    "pageNum": 5,
                    "numLines": 3,
                    "boundingBox": {
                        "top": 277.49490000000003,
                        "left": 810,
                        "right": 999.6543,
                        "width": 189.65430000000003,
                        "bottom": 313.4572125000001,
                        "height": 35.96231250000005,
                    },
                    "avgCharWidth": 3.833245774647888,
                    "avgLineSpace": 3.0256875000000036,
                    "charCapacity": 147,
                    "avgCharHeight": 9.970312500000015,
                    "precedingContent": " digital workplace up and running (and managed over the long-term) with no major burden on IT. Integrations with critical tools Igloo allows you to maximize your existing technology investments by integrating with Microsoft, Google, Salesforce, Zendesk, and many others. And if we haven’t built it, our Developer Program and SDK make it easy to extend the platform to your unique needs. Mobile first ",
                    "succeedingContent": " Best-in-class security Igloo is hosted in the global Microsoft Azure cloud. Your data is encrypted using government-grade security, both in transit and at rest. Flexible branding and configuration Every Igloo digital workplace can be designed to match your brand. With the ability to move and re-configure features, it is easy to tailor a user experience that combines familiar workflows with native",
                },
                "text": "Igloo is accessible from anywhere, on any device,\nwith a responsive web experience, native apps for\niOS and Android, and full email integration.",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p9(self):
        components_v2 = {
            "03_qECqVHerjWvel": {
                "meta": {
                    "type": "text",
                    "html_tag": "<h2>",
                    "time_added": 1725486162210,
                    "html_tag_index": None,
                    "selected_element": '<h2 class="css-1bph346 tofu-element tofu-editable-element tofu-hovered-element" data-tofu-id="03_qECqVHerjWvel">Open up your product development process</h2>',
                    "preceding_element": "<p>COLLABORATION</p>",
                    "succeeding_element": "<p>1/3</p>",
                },
                "text": "Open up your product development process",
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p10(self):

        components_v2 = {
            "lRHqLy9nrb3HfIgH": {
                "meta": {
                    "type": "image",
                    "html_tag": "<img>",
                    "time_added": 1721149949505,
                    "html_tag_index": None,
                    "selected_element": '<img class="offset-hero-image tofu-element tofu-hovered-element" src="https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=710&amp;height=489&amp;name=LegitSecurity-Platform-Hero.png" alt="LegitSecurity-Platform-Hero" loading="eager" width="710" height="489" style="max-width: 100%; height: auto;" srcset="https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=355&amp;height=245&amp;name=LegitSecurity-Platform-Hero.png 355w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=710&amp;height=489&amp;name=LegitSecurity-Platform-Hero.png 710w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=1065&amp;height=734&amp;name=LegitSecurity-Platform-Hero.png 1065w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=1420&amp;height=978&amp;name=LegitSecurity-Platform-Hero.png 1420w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=1775&amp;height=1223&amp;name=LegitSecurity-Platform-Hero.png 1775w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=2130&amp;height=1467&amp;name=LegitSecurity-Platform-Hero.png 2130w" sizes="(max-width: 710px) 100vw, 710px" data-tofu-id="lRHqLy9nrb3HfIgH">',
                    "preceding_element": "<a>© 2024 Legit Security</a>",
                    "succeeding_element": "<img></img>",
                },
                "image": {
                    "alt": "LegitSecurity-Platform-Hero",
                    "url": "https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=710&height=489&name=LegitSecurity-Platform-Hero.png",
                    "width": 710,
                    "height": 489,
                    "srcset": "https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=355&height=245&name=LegitSecurity-Platform-Hero.png 355w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=710&height=489&name=LegitSecurity-Platform-Hero.png 710w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=1065&height=734&name=LegitSecurity-Platform-Hero.png 1065w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=1420&height=978&name=LegitSecurity-Platform-Hero.png 1420w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=1775&height=1223&name=LegitSecurity-Platform-Hero.png 1775w, https://www.legitsecurity.com/hs-fs/hubfs/LegitSecurity-Platform-Hero.png?width=2130&height=1467&name=LegitSecurity-Platform-Hero.png 2130w",
                    "loading": "eager",
                },
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p11(self):
        components_v2 = {
            "BK3YIjU-VZ6O6mo_": {
                "meta": {
                    "time_added": 1724899873872,
                    "component_type": "email body",
                    "isEmailSubject": False,
                    "precedingContent": " ",
                    "succeedingContent": "LinkSome more content",
                },
                "text": "Content here",
            },
            "U0AvjzwXhuu12Hy4": {
                "link": {"path": "https://tofuhq.com/", "type": "external_link"},
                "meta": {
                    "type": "link",
                    "time_added": 1724899892266,
                    "component_type": "body",
                    "isEmailSubject": False,
                    "precedingContent": "",
                    "succeedingContent": "",
                },
                "text": "Link",
            },
            "YCNH1jEca-QyO5rQ": {
                "meta": {
                    "time_added": 1724899881551,
                    "component_type": "email subject",
                    "isEmailSubject": True,
                    "precedingContent": " ",
                    "succeedingContent": "",
                },
                "text": "Hello world",
            },
        }

        self.util_test_conversion(components_v2)

    def test_convert_components_p12(self):
        components_v2 = {
            "vM0WIWSuFAeEcFdw": {
                "meta": {
                    "type": "anchor",
                    "html_tag": "<a>",
                    "time_added": 1722579341283,
                    "component_type": "edited",
                    "html_tag_index": None,
                    "preceding_element": None,
                    "succeeding_element": None,
                },
                "text": "See a Product Demo",
                "anchor": {
                    "href": "https://www.tofuhq.com/lp/request-a-demo",
                    "text": "See a Product Demo",
                },
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p13(self):
        components_v2 = {
            "gRBbinxwU4dQ2K4x": {
                "meta": {
                    "type": "image",
                    "html_tag": "<img>",
                    "time_added": 1726182482249,
                    "html_tag_index": 53,
                    "selected_element": '<img src="https://cdn.prod.website-files.com/66bd17075ac69080f2505aee/66bd17075ac69080f2505bcf_hubspot.svg" loading="lazy" id="w-node-_9ee9599d-19d7-21b5-b98c-c1ca576f75cf-f2505af0" alt="the logo for hubspy" class="image soft_left tofu-editable-element" data-tofu-id="gRBbinxwU4dQ2K4x">',
                    "preceding_element": "<a>Terms of Use</a>",
                    "succeeding_element": "<img></img>",
                },
                "text": "",
                "image": {
                    "alt": "the logo for hubspy",
                    "url": "https://cdn.prod.website-files.com/66bd17075ac69080f2505aee/66bd17075ac69080f2505bcf_hubspot.svg",
                    "width": 172,
                    "height": 82,
                    "srcset": "",
                    "loading": "lazy",
                },
            }
        }
        self.util_test_conversion(components_v2)

    def test_convert_components_p14(self):
        components_v2 = {
            "8w9NhULNUC4uqvfX": {
                "link": {
                    "href": "https://www.tofuhq.com/lp/request-a-demo",
                    "text": "See a Product Demo",
                },
                "meta": {
                    "type": "link",
                    "html_tag": "<a>",
                    "time_added": 1723412673808,
                    "component_type": "edited",
                    "html_tag_index": None,
                    "preceding_element": None,
                    "succeeding_element": None,
                },
                "text": "See a Product Demo",
            },
        }

        self.util_test_conversion(components_v2)

    def test_convert_components_p15(self):
        components_v2 = {
            "ScnxM_C2FDtzuLAb": {
                "meta": {
                    "type": "text",
                    "html_tag": "<div>",
                    "time_added": 1730767419924,
                    "html_tag_index": None,
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": {"group1": ["asset1", "asset2"]},
                                "instruction": "Use the contents from this link https://www.tofuhq.com/post/maximizing-event-roi-with-generative-ai",
                            }
                        ]
                    },
                    "selected_element": '<div class="subtext tofu-element tofu-editable-element tofu-hovered-element" data-tofu-id="ScnxM_C2FDtzuLAb">Scale your content efforts, personalize your GTM campaigns, and increase conversion</div>',
                    "preceding_element": "<h1>YOUR AI SIDEKICK FOR B2B MARKETING</h1>",
                    "succeeding_element": "<a>See a Product Demo</a>",
                },
                "text": "Scale your content efforts, personalize your GTM campaigns, and increase conversion",
            },
        }

        self.util_test_conversion(components_v2)

    def test_convert_components_p16(self):
        return
        # TODO: the data might not be correct
        components_v2 = {
            "1Yo1UjYdxbX4RPTy": {
                "meta": {
                    "type": "text",
                    "html_tag": "<h3>",
                    "time_added": 1734468803231,
                    "html_tag_index": None,
                    "selected_element": '<h3 class="tofu-element tofu-hovered-element" data-tofu-id="1Yo1UjYdxbX4RPTy">generate</h3>',
                    "preceding_element": "",
                    "succeeding_element": "",
                },
                "text": "GENERATE",
                "component_params": {
                    "custom_instructions": [
                        {
                            "assets": {"Campaign Assets": ["-TvDv1a3NjPuU2sj.html"]},
                            "instruction": "sefgwrtgfeqrwt",
                        }
                    ]
                },
            },
        }

        self.util_test_conversion(components_v2)


class TestComponentConvertComparisor(unittest.TestCase):
    def setUp(self):
        self.comparisor = ComponentConvertComparisor()

    def test_compare_meta_fields_none_vs_unspecified(self):
        meta_v2 = {"component_type": None, "other_field": "value1"}
        meta_v2_conv = {"component_type": "unspecified", "other_field": "value1"}

        differences = self.comparisor._compare_meta_fields(meta_v2, meta_v2_conv)
        self.assertEqual(
            differences,
            [],
            "Expected no differences when component_type is None vs unspecified",
        )


if __name__ == "__main__":
    unittest.main()
