import copy
import logging
import unittest

import pytest

from ....actions.legacy_converter.legacy_repurpose_template_converter import (
    RepurposeTemplateConvertComparisor,
    convert_repurpose_template_v2_to_v3,
    convert_repurpose_template_v3_to_v2,
)
from ....models import AssetInfo, AssetInfoGroup, CompanyInfo, Playbook


@pytest.mark.django_db
class TestLegacyRepurposeTemplateConverter(unittest.TestCase):
    def setUp(self):
        # Create a CompanyInfo instance
        self.company_info = CompanyInfo.objects.create()

        # Create a Playbook instance with the associated CompanyInfo
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=self.company_info,  # Associate with the company info
        )
        self.playbook_id = self.playbook.id

        # Add data to the temporary database
        self.asset_group1 = AssetInfoGroup.objects.create(
            asset_info_group_key="group1",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.asset_group2 = AssetInfoGroup.objects.create(
            asset_info_group_key="group2",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.assets = [
            AssetInfo.objects.create(
                asset_key="asset1", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset2", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset3", asset_info_group=self.asset_group2
            ),
        ]

    def util_test_conversion(self, content_group_params):
        """Helper method to test v2->v3->v2 conversion."""
        # Convert v2 to v3
        template_v3 = convert_repurpose_template_v2_to_v3(
            self.playbook_id, content_group_params
        )
        if not template_v3:
            return True  # No template fields present
        logging.info(f"template_v3: {template_v3}")

        content_group_params_input = copy.deepcopy(content_group_params)

        # Convert back to v2
        content_group_params_converted = convert_repurpose_template_v3_to_v2(
            template_v3, content_group_params_input
        )

        comparisor = RepurposeTemplateConvertComparisor()
        self.assertTrue(
            comparisor.compare(content_group_params, content_group_params_converted)
        )

        return True

    def test_convert_repurpose_template_p1(self):
        content_group_params = {
            "gen_status": {"status": "NOT_STARTED"},
            "content_type": "Email - SDR",
            "initialSetup": False,
            "content_source": "Repurpose Content",
            "foundation_model": "gpt-4-0125-preview",
            "custom_instructions": [],
            "content_source_format": "Text",
            "content_source_upload_method": "Text",
            "repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=798d3856-59f5-06c8-5200-3d3b4dd3444b.json&fileType=application/json&directory=tofu-uploaded-files",
            "slate_repurpose_template_content_source": "144045.json",
            "slate_repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=f3f80020-2e90-9863-dad3-9ee20b08e60b.json&fileType=application/json&directory=tofu-uploaded-files",
        }
        self.util_test_conversion(content_group_params)

    def test_convert_repurpose_template_p2(self):
        content_group_params = {
            "gen_status": {"status": "NOT_STARTED"},
            "content_type": "Email - SDR",
            "initialSetup": False,
            "hasAnalysisRun": True,
            "template_settings": {
                "follow_tone": True,
                "follow_length": True,
                "tone_reference": None,
                "follow_core_message_and_key_point": False,
            },
            "custom_instructions": [],
            "content_source_format": "Text",
            "content_source_upload_method": "Text",
            "content_source": "1562565.json",
            "repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=7e00eba1-7aab-371e-3c08-ddc7f3f90c37.json&fileType=application/json&directory=tofu-uploaded-files",
            "slate_repurpose_template_content_source": "1562565.json",
            "slate_repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=69842ad6-72a2-93b2-1cd5-c3937077a7b3.json&fileType=application/json&directory=tofu-uploaded-files",
        }
        self.util_test_conversion(content_group_params)

    def test_convert_repurpose_template_p3(self):
        content_group_params = {
            "targets": [],
            "gen_status": {"status": "NOT_STARTED"},
            "content_type": "Blog Post",
            "initialSetup": False,
            "content_source": "1562565.json",
            "template_settings": {
                "follow_tone": False,
                "follow_length": True,
                "tone_reference": [{"assets": {"group1": ["asset1"]}}],
                "follow_core_message_and_key_point": False,
            },
            "custom_instructions": [],
            "content_source_format": "Text",
            "orig_content_group_id": 204503,
            "content_source_upload_method": "Text",
        }

        self.util_test_conversion(content_group_params)

    def test_convert_repurpose_template_p4(self):
        content_group_params = {
            "gen_status": {"status": "NOT_STARTED"},
            "content_type": "Social - LinkedIn",
            "initialSetup": True,
            "content_source": "1562565.json",
            "hideFromCampaign": True,
            "content_source_format": "Text",
            "content_source_upload_method": "Text",
            "repurpose_template_content_source_copy": None,
            "slate_repurpose_template_content_source": None,
            "slate_repurpose_template_content_source_copy": None,
        }
        self.util_test_conversion(content_group_params)

    def test_convert_repurpose_template_p5(self):
        content_group_params = {
            "gen_status": {"status": "NOT_STARTED"},
            "content_type": "Email - SDR",
            "initialSetup": False,
            "content_source": "1562565.json",
            "hideFromCampaign": True,
            "template_settings": {
                "follow_tone": True,
                "follow_length": True,
                "tone_reference": None,
                "follow_core_message_and_key_point": False,
            },
            "content_collection": {
                "name": "Email - SDR 2",
                "numOfContent": "1",
                "isDummyContentCollectionContentGroup": True,
            },
            "content_source_format": "Text",
            "content_source_upload_method": "Text",
            "repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=38794a53-ef72-9164-cb24-90d6aa1ced59.json&fileType=application/json&directory=tofu-uploaded-files",
            "slate_repurpose_template_content_source": "23466201.json",
            "slate_repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=ed4f512c-f490-ef4a-011d-4602a0ace255.json&fileType=application/json&directory=tofu-uploaded-files",
        }
        self.util_test_conversion(content_group_params)

    def test_convert_repurpose_template_p6(self):
        content_group_params = {
            "action_index": 0,
            "content_goal": "Repurpose Content",
            "content_type": "Email - Marketing",
            "content_source_format": "Html",
            "content_source_upload_method": "Link",
            "content_source": "http://tofuhq.com|tofuhq.com",
            "content_source_copy": "",
            "export_content_source_copy": "",
            "repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=http://cc09d93a-3b61-87ce-34cf-a49da75089b0.com|cc09d93a-3b61-87ce-34cf-a49da75089b0.com&fileType=text/html&directory=tofu-uploaded-files",
            "slate_repurpose_template_content_source": "",
            "slate_repurpose_template_content_source_copy": "",
            "subject_line_only_content_source": "258705621.json",
            "subject_line_only_content_source_copy": "/api/web/storage/s3-presigned-url?file=ddd9e6cc-7d1f-89e2-de3c-7e3d0d368212.json&fileType=application/json&directory=tofu-uploaded-files",
        }
        self.util_test_conversion(content_group_params)


if __name__ == "__main__":
    unittest.main()
