import unittest
from unittest.mock import MagicMock, patch

import pytest

from ....actions.legacy_converter.legacy_campaign_targets_converter import (
    convert_campaign_targets_v2_to_v3,
    convert_campaign_targets_v3_to_v2,
)
from ....models import CompanyInfo, Playbook, TargetInfo, TargetInfoGroup
from ....shared_definitions.protobuf.gen.action_define_pb2 import TofuTarget


@pytest.mark.django_db
class TestLegacyTargetsConverter(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    def setUp(self):
        super().setUp()
        # Create a test playbook
        self.playbook = Playbook.objects.create(
            company_object=CompanyInfo.objects.create()
        )
        self.playbook_id = self.playbook.id  # Use the actual playbook ID

        # Create target groups
        self.group1 = TargetInfoGroup.objects.create(
            playbook=self.playbook, target_info_group_key="group1"
        )

        self.group2 = TargetInfoGroup.objects.create(
            playbook=self.playbook, target_info_group_key="group2"
        )

        # Create targets
        self.target1 = TargetInfo.objects.create(
            target_info_group=self.group1, target_key="target1"
        )

        self.target2 = TargetInfo.objects.create(
            target_info_group=self.group1, target_key="target2"
        )

        self.target3 = TargetInfo.objects.create(
            target_info_group=self.group2, target_key="target3"
        )

    def tearDown(self):
        super().tearDown()
        # Django will automatically clean up the test database after each test

    def util_test_conversion(self, targets_v2):
        # Test v2 -> v3 -> v2 conversion
        targets_v3 = convert_campaign_targets_v2_to_v3(targets_v2, self.playbook_id)
        targets_v2_converted = convert_campaign_targets_v3_to_v2(
            targets_v3, self.playbook_id
        )

        # Compare lists in an order-insensitive way
        self.assertEqual(len(targets_v2), len(targets_v2_converted))
        for group_dict1, group_dict2 in zip(
            sorted(targets_v2, key=lambda x: list(x.keys())[0]),
            sorted(targets_v2_converted, key=lambda x: list(x.keys())[0]),
        ):
            group1 = list(group_dict1.items())[0]
            group2 = list(group_dict2.items())[0]

            self.assertEqual(group1[0], group2[0])  # Compare group names
            self.assertCountEqual(
                group1[1], group2[1]
            )  # Compare targets order-insensitively

        return True

    def test_convert_targets_simple(self):
        targets_v2 = [{"group1": ["target1"]}]
        self.util_test_conversion(targets_v2)

    def test_convert_targets_multiple_groups(self):
        targets_v2 = [{"group1": ["target1", "target2"]}, {"group2": ["target3"]}]
        self.util_test_conversion(targets_v2)

    def test_convert_empty_targets(self):
        targets_v2 = []
        self.util_test_conversion(targets_v2)

    def test_invalid_v3_format(self):
        invalid_targets = "not a TofuTarget object"
        with self.assertRaises(ValueError):
            convert_campaign_targets_v3_to_v2(invalid_targets, self.playbook_id)


if __name__ == "__main__":
    unittest.main()
