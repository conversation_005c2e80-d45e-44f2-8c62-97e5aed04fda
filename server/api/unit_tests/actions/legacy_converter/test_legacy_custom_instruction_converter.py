import logging

from django.test import TestCase

from ....actions.legacy_converter.legacy_custom_instruction_converter import (
    AssetsConvertComparator,
    CustomInstructionConvertComparisor,
    convert_assets_v2_to_v3,
    convert_assets_v3_to_v2,
    convert_custom_instructions_v2_to_v3,
    convert_custom_instructions_v3_to_v2,
)
from ....models import AssetInfo, AssetInfoGroup, CompanyInfo, Playbook
from ....shared_definitions.protobuf.gen.action_define_pb2 import TofuDataList


class TestLegacyCustomInstructionConverter(TestCase):
    def setUp(self):
        self.assets_v2 = {"group1": ["asset1", "asset2"], "group2": None}
        self.custom_instructions_v2 = [
            {"assets": self.assets_v2, "instruction": "Sample instruction"},
            {"assets": self.assets_v2, "instruction": None},
        ]

        # Create a CompanyInfo instance
        self.company_info = CompanyInfo.objects.create()

        # Create a Playbook instance with the associated CompanyInfo
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=self.company_info,  # Associate with the company info
        )
        self.playbook_id = self.playbook.id

        # Add data to the temporary database
        self.asset_group1 = AssetInfoGroup.objects.create(
            asset_info_group_key="group1",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.asset_group2 = AssetInfoGroup.objects.create(
            asset_info_group_key="group2",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.assets = [
            AssetInfo.objects.create(
                asset_key="asset1", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset2", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset3", asset_info_group=self.asset_group2
            ),
        ]

    def test_convert_assets_v2_to_v3_and_back(self):
        # Convert v2 to v3
        tofu_data_list = convert_assets_v2_to_v3(self.playbook_id, self.assets_v2)
        self.assertTrue(isinstance(tofu_data_list, TofuDataList))
        # Convert back to v2
        assets_v2_converted = convert_assets_v3_to_v2(tofu_data_list)

        comparator = AssetsConvertComparator()
        differences = comparator.compare(self.assets_v2, assets_v2_converted)
        self.assertEqual(differences, [])

    def test_convert_custom_instructions_v2_to_v3_and_back(self):
        # Convert v2 to v3
        custom_instructions_v3 = convert_custom_instructions_v2_to_v3(
            self.playbook_id, self.custom_instructions_v2
        )
        # Convert back to v2
        custom_instructions_v2_converted = convert_custom_instructions_v3_to_v2(
            custom_instructions_v3
        )

        comparator = CustomInstructionConvertComparisor()
        differences = comparator.compare(
            self.custom_instructions_v2, custom_instructions_v2_converted
        )
        self.assertEqual(differences, [])
