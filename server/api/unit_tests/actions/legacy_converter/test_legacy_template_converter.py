import copy
import logging
import unittest

import pytest

from ....actions.legacy_converter.legacy_template_converter import (
    TemplateConvertComparisor,
    convert_template_v2_to_v3,
    convert_template_v3_to_v2,
)
from ....models import AssetInfo, AssetInfoGroup, CompanyInfo, Playbook


@pytest.mark.django_db
class TestLegacyTemplateConverter(unittest.TestCase):
    def setUp(self):
        # Create a CompanyInfo instance
        self.company_info = CompanyInfo.objects.create()

        # Create a Playbook instance with the associated CompanyInfo
        self.playbook = Playbook.objects.create(
            name="Test Playbook",
            company_object=self.company_info,  # Associate with the company info
        )
        self.playbook_id = self.playbook.id

        # Add data to the temporary database
        self.asset_group1 = AssetInfoGroup.objects.create(
            asset_info_group_key="group1",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.asset_group2 = AssetInfoGroup.objects.create(
            asset_info_group_key="group2",
            playbook=self.playbook,  # Associate with the playbook
        )
        self.assets = [
            AssetInfo.objects.create(
                asset_key="asset1", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset2", asset_info_group=self.asset_group1
            ),
            AssetInfo.objects.create(
                asset_key="asset3", asset_info_group=self.asset_group2
            ),
        ]

    def util_test_conversion(self, content_group_params):
        """Helper method to test v2->v3->v2 conversion."""
        # Convert v2 to v3
        template_v3 = convert_template_v2_to_v3(self.playbook_id, content_group_params)
        if not template_v3:
            return True  # No template fields present
        logging.info(f"template_v3: {template_v3}")

        content_group_params_input = copy.deepcopy(content_group_params)

        # Convert back to v2
        content_group_params_converted = convert_template_v3_to_v2(
            template_v3, content_group_params_input
        )

        comparisor = TemplateConvertComparisor()
        self.assertTrue(
            comparisor.compare(content_group_params, content_group_params_converted)
        )

        return True

    def test_convert_template_p1(self):
        content_group_params = {
            "content_type": "Email - SDR",
            "initialSetup": False,
            "content_source": "3200738.json",
            "no_rename_alert": True,
            "template_settings": {
                "follow_tone": True,
                "follow_length": True,
                "tone_reference": None,
                "follow_core_message_and_key_point": True,
            },
            "content_source_copy": "/api/web/storage/s3-presigned-url?file=b5e9eabc-2712-4bfa-80ff-5d21fac2e27d-3bbbaa5a-6926-439c-a941-a4daf647a9aa-1ad289ba-e7b4-45b1-9260-aa4e6576f05d-7289d1f4-3647-4685-aac7-0fc44140be91-eec59fe7-2c41-2419-aae7-f6bfae445d5d.json&fileType=application/json&directory=tofu-uploaded-files",
            "custom_instructions": [
                {"assets": None, "instruction": "don't use cliches. "}
            ],
            "selected_pdf_method": "pdf_content_edit",
            "content_source_format": "Text",
            "orig_content_group_id": 234312,
            "content_source_upload_method": "Text",
            "subject_line_only_content_source_copy": None,
        }
        self.util_test_conversion(content_group_params)

    def test_convert_template_p2(self):
        content_group_params = {
            "targets": [{"Text Upsell - No Vendor": ["Clorox.com"]}],
            "content_type": "Email - Marketing",
            "initialSetup": False,
            "content_source": "local",
            "content_source_copy": "/api/web/storage/s3-presigned-url?file=e8cfc53f-1500-43e3-b57a-06c29a234aa2&fileType=text/html&directory=tofu-uploaded-files",
            "custom_instructions": [
                {
                    "assets": None,
                    "instruction": "Don't use the company target name directly",
                },
                {
                    "assets": None,
                    "instruction": "Focus on the industry that the target is in",
                },
            ],
            "content_source_format": "Html",
            "orig_content_group_id": 140595,
            "export_content_source_copy": "",
            "content_source_upload_method": "File",
            "subject_line_only_content_source": "71569_subject_line.json",
            "subject_line_only_content_source_copy": "/api/web/storage/s3-presigned-url?file=fe92a729-131f-694b-044b-915ee919b6e9&fileType=application/json&directory=tofu-uploaded-files",
        }
        self.util_test_conversion(content_group_params)

    def test_convert_template_p3(self):
        content_group_params = {
            "tofu_lite": True,
            "content_type": "Landing Page",
            "initialSetup": False,
            "content_source": None,
            "template_settings": {
                "follow_tone": True,
                "follow_length": True,
                "tone_reference": None,
                "follow_core_message_and_key_point": True,
            },
            "content_source_copy": None,
            "content_source_format": "Html",
            "content_source_upload_method": None,
            "subject_line_only_content_source_copy": None,
        }

        self.util_test_conversion(content_group_params)

    def test_convert_template_p4(self):
        content_group_params = {
            "tofu_lite": True,
            "content_type": "Landing Page",
            "initialSetup": False,
            "content_source": None,
            "template_settings": {
                "follow_tone": True,
                "follow_length": True,
                "tone_reference": None,
                "follow_core_message_and_key_point": True,
            },
            "content_source_copy": '""',
            "content_source_format": "Html",
            "content_source_upload_method": None,
            "subject_line_only_content_source_copy": None,
        }

        self.util_test_conversion(content_group_params)


if __name__ == "__main__":
    unittest.main()
