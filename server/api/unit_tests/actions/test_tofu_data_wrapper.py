import pytest

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    PlatformType,
    TofuAsset,
    TofuComponent,
    TofuComponentMetaType,
    TofuComponents,
    TofuComponentType,
    TofuContentGroup,
    TofuContentType,
    TofuCustomInstruction,
    TofuData,
    TofuDataList,
    TofuExportSettings,
    TofuPlatformType,
    TofuTarget,
    TofuTemplate,
    TofuTemplateField,
)
from ...shared_types import ContentType


class TestTofuDataListHandler:
    def test_parse_json_to_tofu_data(self):
        # Test valid json
        json_data = {"data": [{"int_value": {"value": 42}}]}
        result = TofuDataListHandler.parse_json_to_tofu_data(json_data)
        assert isinstance(result, TofuDataList)
        assert result.data[0].int_value.value == 42

        # Test invalid input
        with pytest.raises(ValueError):
            TofuDataListHandler.parse_json_to_tofu_data([])

    def test_int_value_handling(self):
        # Test conversion to TofuData
        result = TofuDataListHandler.convert_int_to_tofu_data(42)
        assert isinstance(result, TofuDataList)
        assert result.data[0].int_value.value == 42

        # Test extraction from TofuData
        assert TofuDataListHandler.get_int_value(result) == 42
        with pytest.raises(ValueError):
            TofuDataListHandler.get_int_value(None)

    def test_string_value_handling(self):
        # Test conversion to TofuData
        result = TofuDataListHandler.convert_string_to_tofu_data("test")
        assert isinstance(result, TofuDataList)
        assert result.data[0].string_value.value == "test"

        # Test extraction from TofuData
        assert TofuDataListHandler.get_string_value(result) == "test"
        with pytest.raises(ValueError):
            TofuDataListHandler.get_string_value(None)

    def test_bool_value_handling(self):
        # Test conversion to TofuData
        result = TofuDataListHandler.convert_bool_to_tofu_data(True)
        assert isinstance(result, TofuDataList)
        assert result.data[0].bool_value.value is True

        # Test extraction from TofuData
        assert TofuDataListHandler.get_bool_value(result) is True
        with pytest.raises(ValueError):
            TofuDataListHandler.get_bool_value(None)

    def test_content_type_handling(self):
        # Test conversion to TofuData
        result = TofuDataListHandler.convert_content_type_to_tofu_data(
            ContentType.LandingPage
        )
        assert isinstance(result, TofuDataList)
        assert result.data[0].content_type.content_type == ContentType.LandingPage.value

        # Test extraction from TofuData
        assert (
            TofuDataListHandler.get_content_type(result)
            == ContentType.LandingPage.value
        )
        with pytest.raises(ValueError):
            TofuDataListHandler.get_content_type(None)

    def test_content_group_handling(self):
        # Test conversion to TofuData
        result = TofuDataListHandler.convert_content_group_ids_to_tofu_data([1, 2])
        assert isinstance(result, TofuDataList)
        assert len(result.data) == 2
        assert result.data[0].content_group.content_group_id == 1
        assert result.data[1].content_group.content_group_id == 2

        # Test extraction from TofuData
        assert TofuDataListHandler.get_content_group_ids(result) == [1, 2]
        with pytest.raises(ValueError):
            TofuDataListHandler.get_content_group_ids(None)

    def test_asset_handling(self):
        # Test conversion to TofuData
        result = TofuDataListHandler.convert_asset_to_tofu_data([1, 2], [])
        assert isinstance(result, TofuDataList)
        assert len(result.data) == 2
        assert result.data[0].asset.asset_id == 1
        assert result.data[1].asset.asset_id == 2

        # Test extraction from TofuData
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(result)
        assert asset_ids == [1, 2]
        assert asset_group_ids == []

        # Test with only asset groups
        result = TofuDataListHandler.convert_asset_to_tofu_data([], [3, 4])
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(result)
        assert asset_ids == []
        assert asset_group_ids == [3, 4]

        # Test with both types
        result = TofuDataListHandler.convert_asset_to_tofu_data([1], [2])
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(result)
        assert asset_ids == [1]
        assert asset_group_ids == [2]

        # Test with empty lists
        result = TofuDataListHandler.convert_asset_to_tofu_data([], [])
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(result)
        assert asset_ids == []
        assert asset_group_ids == []

        # Test with None inputs
        result = TofuDataListHandler.convert_asset_to_tofu_data(None, None)
        assert isinstance(result, TofuDataList)
        assert len(result.data) == 0

        # Test with invalid inputs
        with pytest.raises(ValueError):
            TofuDataListHandler.convert_asset_to_tofu_data(["1"], [])
        with pytest.raises(ValueError):
            TofuDataListHandler.convert_asset_to_tofu_data([], ["2"])

    def test_target_handling(self):
        # Test conversion to TofuData with simple target IDs
        result = TofuDataListHandler.convert_targets_to_tofu_data([1, 2])
        assert isinstance(result, TofuDataList)
        assert len(result.data) == 2
        assert result.data[0].target.target_id == 1
        assert result.data[1].target.target_id == 2

        # Test extraction from TofuData
        assert TofuDataListHandler.get_targets(result) == [1, 2]
        with pytest.raises(ValueError):
            TofuDataListHandler.get_targets(None)

    def test_components_handling(self):
        # Test conversion to TofuData
        components = TofuComponents()

        # Create first component
        comp1 = TofuComponent()
        comp1.component_id = "comp1"
        comp1.time_added = 1234567890
        comp1.component_type = TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
        comp1.component_meta_type = TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_TEXT
        comp1.text.text = "Hello World"
        comp1.component_context_data.preceding_element = "header"
        comp1.component_context_data.succeeding_element = "footer"

        # Create a custom instruction and add it to the component
        instruction_data = TofuData()
        instruction_data.custom_instruction.instruction = "Make it friendly"

        # Initialize the assets field as a TofuDataList and add an asset
        asset_data_list = TofuDataList()
        asset_data = TofuData()
        asset_data.asset.asset_id = 123  # Example asset ID
        asset_data_list.data.append(asset_data)

        # Correctly access the assets field through custom_instruction
        instruction_data.custom_instruction.assets.CopyFrom(asset_data_list)

        # Add the custom instruction to the component
        comp1.component_custom_instructions.data.append(instruction_data)

        # Create second component
        comp2 = TofuComponent()
        comp2.component_id = "comp2"
        comp2.time_added = 1234567891
        comp2.component_type = TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
        comp2.component_meta_type = TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_LINK
        comp2.link.text = "Click here"
        comp2.link.href = "https://example.com"
        comp2.link.content_group_id = 456

        # Add components to the components message
        components.components["comp1"].CopyFrom(comp1)
        components.components["comp2"].CopyFrom(comp2)

        result = TofuDataListHandler.convert_components_to_tofu_data(components)

        # Verify the result
        assert isinstance(result, TofuDataList)
        result_components = result.data[0].components.components

        # Check comp1
        assert result_components["comp1"].component_id == "comp1"
        assert result_components["comp1"].time_added == 1234567890
        assert (
            result_components["comp1"].component_type
            == TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
        )
        assert (
            result_components["comp1"].component_meta_type
            == TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_TEXT
        )
        assert result_components["comp1"].text.text == "Hello World"
        assert (
            result_components["comp1"].component_context_data.preceding_element
            == "header"
        )
        assert (
            result_components["comp1"].component_context_data.succeeding_element
            == "footer"
        )
        # Ensure component_custom_instructions is accessed correctly
        assert len(result_components["comp1"].component_custom_instructions.data) == 1
        custom_instruction_data = result_components[
            "comp1"
        ].component_custom_instructions.data[0]
        assert custom_instruction_data.HasField("custom_instruction")
        assert (
            custom_instruction_data.custom_instruction.instruction == "Make it friendly"
        )

        # Check if assets are populated
        assert (
            len(custom_instruction_data.custom_instruction.assets.data) > 0
        ), "Assets data is empty"
        assert (
            custom_instruction_data.custom_instruction.assets.data[0].asset.asset_id
            == 123
        )

        # Check comp2
        assert result_components["comp2"].component_id == "comp2"
        assert result_components["comp2"].time_added == 1234567891
        assert (
            result_components["comp2"].component_type
            == TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
        )
        assert (
            result_components["comp2"].component_meta_type
            == TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_LINK
        )
        assert result_components["comp2"].link.text == "Click here"
        assert result_components["comp2"].link.href == "https://example.com"
        assert result_components["comp2"].link.content_group_id == 456

        # Test extraction from TofuData
        extracted = TofuDataListHandler.get_components(result)
        assert extracted.components["comp1"].text.text == "Hello World"
        assert extracted.components["comp2"].link.href == "https://example.com"

        with pytest.raises(ValueError):
            TofuDataListHandler.get_components(None)

    def test_template_handling(self):
        # Create a template
        template = TofuTemplate()

        # Add a template field
        template_field = TofuTemplateField()
        template_field.template_component_type = (
            TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
        )
        template_field.content_source = "example source"
        template_field.content_source_copy = "example copy"
        template_field.content_source_format = "text"
        template_field.content_source_upload_method = "manual"
        template_field.slate_content_source = "cleaned source"
        template_field.slate_content_source_copy = "cleaned copy"

        # Add the field to template
        template.template_fields[
            str(TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY)
        ].CopyFrom(template_field)

        # Set other template properties
        tone_asset = TofuAsset()
        tone_asset.asset_id = 123
        tone_reference_data = TofuData()
        tone_reference_data.asset.CopyFrom(tone_asset)
        tone_reference_list = TofuDataList()
        tone_reference_list.data.append(tone_reference_data)
        template.tone_reference.CopyFrom(tone_reference_list)

        instruction = TofuCustomInstruction()
        instruction.instruction = "Follow this tone"
        template.template_custom_instructions.append(instruction)

        template.follow_template_tone = True
        template.follow_template_length = True
        template.follow_template_core_message_and_key_point = True

        # Convert template to TofuData
        result = TofuDataListHandler.convert_template_to_tofu_data(template)

        # Verify the result
        assert isinstance(result, TofuDataList)
        extracted = result.data[0].template
        assert (
            extracted.template_fields[
                str(TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY)
            ].content_source
            == "example source"
        )
        assert extracted.tone_reference.data[0].asset.asset_id == 123
        assert len(extracted.template_custom_instructions) == 1
        assert (
            extracted.template_custom_instructions[0].instruction == "Follow this tone"
        )
        assert extracted.follow_template_tone is True
        assert extracted.follow_template_length is True
        assert extracted.follow_template_core_message_and_key_point is True

        # Test extraction from TofuData
        extracted = TofuDataListHandler.get_template(result)
        assert (
            extracted.template_fields[
                str(TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY)
            ].content_source
            == "example source"
        )
        assert extracted.tone_reference.data[0].asset.asset_id == 123

        with pytest.raises(ValueError):
            TofuDataListHandler.get_template(None)

    def test_platform_type_handling(self):
        # Test conversion to TofuData
        result = TofuDataListHandler.convert_platform_type_to_tofu_data(
            PlatformType.PLATFORM_TYPE_LINKEDIN
        )
        assert isinstance(result, TofuDataList)
        assert (
            result.data[0].platform_type.platform_type
            == PlatformType.PLATFORM_TYPE_LINKEDIN
        )

        # Test extraction from TofuData
        assert (
            TofuDataListHandler.get_platform_type(result)
            == PlatformType.PLATFORM_TYPE_LINKEDIN
        )
        with pytest.raises(ValueError):
            TofuDataListHandler.get_platform_type(None)
