from unittest.mock import MagicMock, patch
from urllib.parse import urlparse

from ...actions.repurpose_to_p13n import (
    RepurposeToP13nTemplateConverter,
    copy_template_file,
)
from ...models import Content, ContentGroup, ContentVariation
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuComponentType,
    TofuTemplate,
    TofuTemplateField,
)
from ...shared_types import ContentType


class TestRepurposeToP13nTemplateConverter:
    def test_convert_linkedin_ad_template(self):
        mock_content_group = MagicMock(spec=ContentGroup)
        mock_content_group.content_group_params = {
            "content_type": ContentType.AdCampaignLinkedin,
        }
        mock_content_group.components = {
            1: {"meta": {"component_type": "headline"}, "text": "headline"},
            2: {"meta": {"component_type": "description"}, "text": "description"},
            3: {
                "meta": {"component_type": "introductory-text"},
                "text": "introductory-text",
            },
            4: {"meta": {"component_type": "ad-copy"}, "text": "ad-copy"},
        }
        mock_content_group.id = 1234567890

        mock_to_action = MagicMock()
        mock_to_action.id = 9876543210

        mock_content = MagicMock(spec=Content)
        mock_content.content_group = mock_content_group

        mock_content_variation = MagicMock(spec=ContentVariation)
        mock_content_variation.content = mock_content

        # Mock the Content.objects.filter().first() chain
        mock_content_filter = MagicMock()
        mock_content_filter.first.return_value = mock_content

        mock_content_variation_filter = MagicMock()
        mock_content_variation_filter.first.return_value = mock_content_variation

        # Create a proper UUID mock that converts to string correctly
        mock_uuid = MagicMock()
        mock_uuid.__str__.return_value = "1234567890"
        mock_uuid.hex = "1234567890"

        # Create the expected S3 path using both content group id and to_action id
        expected_filename = (
            f"repurpose_to_p13n_converter_template_{mock_content_group.id}.json"
        )
        dummy_s3_path = f"/api/web/storage/s3-presigned-url?file={expected_filename}&fileType=application/json&directory=tofu-uploaded-files"

        with (
            patch(
                "api.actions.repurpose_to_p13n.Content.objects.filter",
                return_value=mock_content_filter,
            ),
            patch(
                "api.utils.Content.objects.get",
                return_value=mock_content,
            ),
            patch(
                "api.utils.ContentVariation.objects.filter",
                return_value=mock_content_variation_filter,
            ),
            patch(
                "api.actions.repurpose_to_p13n.upload_file",
                return_value=dummy_s3_path,
            ),
        ):
            with patch(
                "api.actions.repurpose_to_p13n.uuid.uuid4",
                return_value=mock_uuid,
            ):
                converter = RepurposeToP13nTemplateConverter(
                    mock_content_group, mock_to_action
                )
                tofu_template = converter.process()
                assert tofu_template is not None
                assert tofu_template.template_fields is not None
                assert len(tofu_template.template_fields) == 1
                template_field = tofu_template.template_fields[
                    TofuComponentType.Name(
                        TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
                    )
                ]
                assert template_field.content_source == ""
                assert template_field.content_source_copy == dummy_s3_path
                assert template_field.content_source_format == "Text"
                assert template_field.content_source_upload_method == "Text"

                # Next step: Test the template copier using the same tofu_template
                expected_copied_filename = f"repurpose_to_p13n_converter_template_{mock_content_group.id}_{mock_to_action.id}.json"
                expected_copied_s3_path = f"/api/web/storage/s3-presigned-url?file={expected_copied_filename}&fileType=application/json&directory=tofu-uploaded-files"

                # Extract just the filename from the dummy_s3_path
                dummy_filename = (
                    urlparse(dummy_s3_path).query.split("&")[0].split("=")[1]
                )

                with patch(
                    "api.actions.repurpose_to_p13n.copy_s3_file",
                ) as mock_copy_s3_file:
                    # Make the mock return the expected path
                    mock_copy_s3_file.return_value = expected_copied_s3_path

                    # Mock the boto3 S3 client and capture what would be copied
                    with patch("boto3.client") as mock_boto3_client:
                        mock_s3_client = MagicMock()
                        mock_boto3_client.return_value = mock_s3_client

                        # Test copying the template file
                        content_type = ContentType.AdCampaignLinkedin
                        copied_template = copy_template_file(
                            content_type, tofu_template, mock_to_action
                        )

                        # Check that copy_s3_file was called with the right parameters
                        mock_copy_s3_file.assert_called_once_with(
                            dummy_filename,  # Just the filename, not the full URL
                            expected_copied_filename,
                            bucket_name="tofu-uploaded-files",
                        )
