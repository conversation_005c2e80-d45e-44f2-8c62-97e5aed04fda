import unittest
from unittest.mock import MagicMock, patch

from ...actions.action_handler_user_input import UserInputActionHandler
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionStatus,
    ActionStatusDetails,
    ActionStatusDetailsAnchorPrecheck,
    ActionStatusStats,
    ActionStatusType,
    AnchorPrecheckDocResult,
    AnchorPrecheckResult,
    AnchorPrecheckResultLabel,
)


class TestUserInputActionHandler(unittest.TestCase):
    @patch("api.actions.action_handler_base.ActionDataWrapper")
    def setUp(self, mock_action_data_wrapper):
        # Create a mock action_instance
        mock_action_instance = MagicMock()

        # Mock the ActionDataWrapper to avoid actual initialization
        mock_wrapper_instance = MagicMock()
        mock_action_data_wrapper.return_value = mock_wrapper_instance

        # Create a UserInputActionHandler instance with the mock
        self.handler = UserInputActionHandler(action_instance=mock_action_instance)

    def test_resolve_status_empty_result(self):
        # Test with None
        result = None
        status = self.handler._resolve_status(result)

        self.assertEqual(
            status.status_type, ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        )
        self.assertEqual(status.message, "Action is complete")

        # Test with empty dict
        result = {}
        status = self.handler._resolve_status(result)

        self.assertEqual(
            status.status_type, ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        )
        self.assertEqual(status.message, "Action is complete")

    def test_resolve_status_single_pass(self):
        result = {1: {"label": "PASS", "comment": "All good"}}

        status = self.handler._resolve_status(result)

        self.assertEqual(
            status.status_type, ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        )
        self.assertEqual(status.message, "Action is complete")
        self.assertEqual(status.details.stats.cnts_succ, 1)
        self.assertEqual(status.details.stats.cnts_fail, 0)
        self.assertTrue(
            1 in status.details.anchor_precheck_details.asset_precheck_results
        )
        self.assertEqual(
            status.details.anchor_precheck_details.asset_precheck_results[1].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS,
        )

    def test_resolve_status_single_fail(self):
        result = {1: {"label": "FAIL", "comment": "Something went wrong"}}

        status = self.handler._resolve_status(result)

        self.assertEqual(status.status_type, ActionStatusType.ACTION_STATUS_TYPE_FAIL)
        self.assertEqual(
            status.message,
            "Some documents failed validation. Please fix all issues to proceed.",
        )
        self.assertEqual(status.details.stats.cnts_succ, 0)
        self.assertEqual(status.details.stats.cnts_fail, 1)
        self.assertTrue(
            1 in status.details.anchor_precheck_details.asset_precheck_results
        )
        self.assertEqual(
            status.details.anchor_precheck_details.asset_precheck_results[1].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL,
        )

    def test_resolve_status_mixed_results(self):
        result = {
            1: {"label": "PASS", "comment": "All good"},
            2: {"label": "FAIL", "comment": "Something went wrong"},
        }

        status = self.handler._resolve_status(result)

        self.assertEqual(status.status_type, ActionStatusType.ACTION_STATUS_TYPE_FAIL)
        self.assertEqual(
            status.message,
            "Some documents failed validation. Please fix all issues to proceed.",
        )
        self.assertEqual(status.details.stats.cnts_succ, 1)
        self.assertEqual(status.details.stats.cnts_fail, 1)

    def test_resolve_status_multiple_doc_case(self):
        result = {
            1: {
                "doc1": {"label": "PASS", "comment": "Doc1 good"},
                "doc2": {"label": "FAIL", "comment": "Doc2 bad"},
            }
        }

        status = self.handler._resolve_status(result)

        self.assertEqual(status.status_type, ActionStatusType.ACTION_STATUS_TYPE_FAIL)
        self.assertEqual(
            status.message,
            "Some documents failed validation. Please fix all issues to proceed.",
        )
        self.assertEqual(status.details.stats.cnts_succ, 0)
        self.assertEqual(status.details.stats.cnts_fail, 1)

        asset_result = status.details.anchor_precheck_details.asset_precheck_results[1]
        self.assertEqual(
            asset_result.label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL,
        )
        self.assertEqual(
            asset_result.message,
            "We failed to process some contents you have uploaded to this asset. Please correct the failed ones or reach out to Tofu team for help.",
        )
        self.assertEqual(
            asset_result.anchor_precheck_doc_results["doc1"].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS,
        )
        self.assertEqual(
            asset_result.anchor_precheck_doc_results["doc2"].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL,
        )

    def test_resolve_status_multiple_doc_all_pass(self):
        result = {
            1: {
                "doc1": {"label": "PASS", "comment": "Doc1 good"},
                "doc2": {"label": "PASS", "comment": "Doc2 good"},
            }
        }

        status = self.handler._resolve_status(result)

        self.assertEqual(
            status.status_type, ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        )
        self.assertEqual(status.message, "Action is complete")
        self.assertEqual(status.details.stats.cnts_succ, 1)
        self.assertEqual(status.details.stats.cnts_fail, 0)

        asset_result = status.details.anchor_precheck_details.asset_precheck_results[1]
        self.assertEqual(
            asset_result.label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS,
        )
        self.assertEqual(asset_result.message, "")

    def test_resolve_status_invalid_result(self):
        # Missing label
        result = {1: {"comment": "No label here"}}

        with self.assertRaises(Exception):
            self.handler._resolve_status(result)

        # Missing comment
        result = {1: {"label": "PASS"}}

        with self.assertRaises(Exception):
            self.handler._resolve_status(result)

        # Invalid doc result
        result = {1: {"doc1": {"missing_label": "oops"}}}

        with self.assertRaises(Exception):
            self.handler._resolve_status(result)

    def test_resolve_status_complex_mixed_anchors(self):
        result = {
            964: {
                "2d7d09a5-c238-f251-52ca-58deaa28ae64": {
                    "label": "FAIL",
                    "comment": "We couldn't crawl the URL test.test. Remove the URL and paste the content as text instead in order to generate content.",
                },
                "db37b64b-d07e-4816-ba1d-218a44ef752b": {
                    "label": "PASS",
                    "comment": None,
                },
                "f47a44c1-a0cf-b265-f183-6cc02846c69f": {
                    "label": "PASS",
                    "comment": None,
                },
            },
            966: {
                "label": "FAIL",
                "comment": "Insufficient Content: The text is too brief and generic. Add more specific and detailed information to the anchor content to make it useful for creating derived content.",
            },
            1224: {"label": "PASS", "comment": None},
            963: {
                "label": "FAIL",
                "comment": "Empty Content: The text is completely empty. Add some relevant information to the anchor content to provide a useful reference for creating derived content.",
            },
        }

        status = self.handler._resolve_status(result)

        # Overall status should be FAIL since there are failures
        self.assertEqual(status.status_type, ActionStatusType.ACTION_STATUS_TYPE_FAIL)
        self.assertEqual(
            status.message,
            "Some documents failed validation. Please fix all issues to proceed.",
        )

        # Stats should count 1 pass (1224) and 3 fails (964, 966, 963)
        self.assertEqual(status.details.stats.cnts_succ, 1)
        self.assertEqual(status.details.stats.cnts_fail, 3)

        # Check anchor 964 with multiple docs
        self.assertTrue(
            964 in status.details.anchor_precheck_details.asset_precheck_results
        )
        asset_964 = status.details.anchor_precheck_details.asset_precheck_results[964]
        self.assertEqual(
            asset_964.label, AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL
        )
        self.assertEqual(
            asset_964.message,
            "We failed to process some contents you have uploaded to this asset. Please correct the failed ones or reach out to Tofu team for help.",
        )

        # Check the document results for anchor 964
        doc_results = asset_964.anchor_precheck_doc_results
        self.assertEqual(len(doc_results), 3)
        self.assertEqual(
            doc_results["2d7d09a5-c238-f251-52ca-58deaa28ae64"].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL,
        )
        self.assertEqual(
            doc_results["db37b64b-d07e-4816-ba1d-218a44ef752b"].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS,
        )
        self.assertEqual(
            doc_results["f47a44c1-a0cf-b265-f183-6cc02846c69f"].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS,
        )

        # Check other anchor results
        self.assertTrue(
            966 in status.details.anchor_precheck_details.asset_precheck_results
        )
        self.assertEqual(
            status.details.anchor_precheck_details.asset_precheck_results[966].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL,
        )

        self.assertTrue(
            1224 in status.details.anchor_precheck_details.asset_precheck_results
        )
        self.assertEqual(
            status.details.anchor_precheck_details.asset_precheck_results[1224].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS,
        )

        self.assertTrue(
            963 in status.details.anchor_precheck_details.asset_precheck_results
        )
        self.assertEqual(
            status.details.anchor_precheck_details.asset_precheck_results[963].label,
            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL,
        )


if __name__ == "__main__":
    unittest.main()
