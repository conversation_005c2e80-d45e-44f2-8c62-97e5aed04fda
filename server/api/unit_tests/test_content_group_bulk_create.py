from unittest import mock

import pytest
from django.test import TestCase

from ..content_group import ContentGroupHandler
from ..logger import log_content_creation
from ..models import Campaign, Content, ContentGroup, Playbook, TofuUser
from ..playbook_build.index_builder import IndexBuilder
from ..playbook_build.playbook_builder import PlaybookBuilder
from ..validator.campaign_validator import CampaignGoal


@pytest.mark.django_db
class TestContentGroupBulkCreate(TestCase):
    def setUp(self):
        # Mock environment variables
        self.env_patcher = mock.patch.dict(
            "os.environ",
            {
                "PINECONE_INDEX_HOST": "test-host",
                "PINECONE_API_KEY": "test-key",
                "PINECONE_ENV": "test-env",
            },
        )
        self.env_patcher.start()

        # Mock log_content_creation to bypass logging
        self.log_content_creation_patcher = mock.patch(
            "api.content_group.log_content_creation"
        )
        self.mock_log_content_creation = self.log_content_creation_patcher.start()
        self.mock_log_content_creation.return_value = None

        # Mock Pinecone client
        self.pinecone_patcher = mock.patch("pinecone.Pinecone")
        self.mock_pinecone = self.pinecone_patcher.start()
        self.mock_pinecone_instance = mock.MagicMock()
        self.mock_pinecone.return_value = self.mock_pinecone_instance
        self.mock_index = mock.MagicMock()
        self.mock_pinecone_instance.Index.return_value = self.mock_index

        # Mock IndexBuilder to bypass Pinecone operations
        self.index_builder_patcher = mock.patch(
            "api.playbook_build.index_builder.IndexBuilder"
        )
        self.mock_index_builder = self.index_builder_patcher.start()
        self.mock_index_builder.return_value.delete_index.return_value = None
        self.mock_index_builder.return_value.create_index.return_value = {
            "index_name": "test",
            "namespace": "test",
        }
        self.mock_index_builder.return_value.update_index.return_value = {
            "index_name": "test",
            "namespace": "test",
        }

        # Create user - this will automatically create a playbook
        self.user = TofuUser.objects.create_user(username="<EMAIL>")
        # Get the playbook that was automatically created
        self.playbook = Playbook.objects.get(playbookuser__user=self.user)

        # Create campaign with targets
        self.campaign = Campaign.objects.create(
            creator=self.user,
            playbook=self.playbook,
            campaign_params={
                "is_campaign_v3": True,
                "targets": [
                    {
                        "audience": ["young_professionals", "seniors"],
                        "company": ["Acme", "Globex"],
                    }
                ],
                "targets_concat": True,
                "campaign_goal": CampaignGoal.Personalization,
            },
        )

        # Create content group
        self.content_group = ContentGroup.objects.create(
            creator=self.user,
            campaign=self.campaign,
            content_group_name="test_personalization_group",
            components={"component1": {"meta": {"component_type": "carousel"}}},
            content_group_params={
                "content_type": "Ad Campaign - LinkedIn Carousel",
                "content_source_format": "Text",
                "content_source": "*********.json",
                "content_source_copy": "/api/web/storage/s3-presigned-url?file=test.json",
                "content_source_upload_method": "Text",
            },
        )

    @mock.patch("os.environ.get")
    @mock.patch("api.playbook_build.playbook_builder.PlaybookBuilder")
    def test_personalization_content_goal(
        self, mock_playbook_builder, mock_environ_get
    ):
        # Setup environment for sequential execution
        mock_environ_get.return_value = "unit_test"

        # Setup PlaybookBuilder mock
        mock_playbook_builder.return_value.cleanup_before_delete.return_value = None

        # Create handler and call method
        handler = ContentGroupHandler(self.content_group)
        result = handler.bulk_create_content()

        # Count number of contents created (should be 4 based on target combinations)
        self.assertEqual(
            Content.objects.filter(content_group=self.content_group).count(), 4
        )

        # now add new target to the campaign
        self.campaign.campaign_params["targets"].append({"contact": ["Amy", "Bob"]})
        self.campaign.save()

        # now call bulk_create_content again
        result = handler.bulk_create_content()
        # Should now have 6 contents (4 original + 2 new from contact targets)
        self.assertEqual(
            Content.objects.filter(content_group=self.content_group).count(), 6
        )

        # now delete the target and add a new list
        self.campaign.campaign_params["targets"] = [{"contact": ["Amy", "Charlie"]}]
        self.campaign.save()

        # Call bulk_create_content again
        result = handler.bulk_create_content()

        # Should now have 2 contents (only the new contact targets)
        self.assertEqual(
            Content.objects.filter(content_group=self.content_group).count(), 2
        )

    def test_repurpose_content_goal(self):
        # Create campaign with repurpose goal
        campaign = Campaign.objects.create(
            playbook=self.playbook,
            campaign_params={"campaign_goal": CampaignGoal.Repurposing},
        )

        content_group = ContentGroup.objects.create(
            content_group_name="test_repurpose_group",
            campaign=campaign,
            creator=self.user,
            components={"component1": {"meta": {"component_type": "carousel"}}},
            content_group_params={
                "content_type": "Ad Campaign - LinkedIn Carousel",
                "content_source_format": "Text",
                "content_source": "*********.json",
                "content_source_copy": "/api/web/storage/s3-presigned-url?file=test.json",
                "content_source_upload_method": "Text",
            },
        )

        handler = ContentGroupHandler(content_group)
        result = handler.bulk_create_content()

        # For repurpose, should create exactly one content with no targets
        self.assertEqual(Content.objects.filter(content_group=content_group).count(), 1)
        content = Content.objects.get(content_group=content_group)
        self.assertNotIn("targets", content.content_params)

        # Running again should not create new content
        result = handler.bulk_create_content()
        self.assertEqual(Content.objects.filter(content_group=content_group).count(), 1)

    def test_seq_personalize_template_content_goal(self):
        # Create campaign with sequential personalization template
        campaign = Campaign.objects.create(
            playbook=self.playbook,
            campaign_params={
                "targets": [
                    {
                        "audience": ["young_professionals", "seniors"],
                        "company": ["Acme", "Globex"],
                    }
                ],
            },
        )

        content_group = ContentGroup.objects.create(
            content_group_name="test_seq_template_group",
            campaign=campaign,
            creator=self.user,
            components={"component1": {"meta": {"component_type": "carousel"}}},
            content_group_params={
                "content_goal": CampaignGoal.SeqPersonalizeTemplate,
                "content_type": "Ad Campaign - LinkedIn Carousel",
                "content_source_format": "Text",
                "content_source": "*********.json",
                "content_source_copy": "/api/web/storage/s3-presigned-url?file=test.json",
                "content_source_upload_method": "Text",
            },
        )

        # Create initial content
        existing_content = Content.objects.create(
            creator=self.user,
            playbook=self.playbook,
            content_group=content_group,
            content_name="test_content",
            content_params={
                "targets": {"audience": "young_professionals"},
                "content_type": "Ad Campaign - LinkedIn Carousel",
            },
            content_status={"gen_status": {"status": "NOT_STARTED"}},
        )

        handler = ContentGroupHandler(content_group)
        result = handler.bulk_create_content()

        # Should create new content with first target and delete existing content
        self.assertFalse(Content.objects.filter(id=existing_content.id).exists())
        self.assertEqual(Content.objects.filter(content_group=content_group).count(), 1)
        new_content = Content.objects.get(content_group=content_group)
        self.assertEqual(
            new_content.content_params["targets"],
            {"audience": "seniors"},  # First target lexicographically
        )

    def tearDown(self):
        # Stop the mocks
        self.log_content_creation_patcher.stop()
        self.index_builder_patcher.stop()

        # Clean up all created objects
        Content.objects.all().delete()
        ContentGroup.objects.all().delete()
        Campaign.objects.all().delete()
        Playbook.objects.all().delete()
        TofuUser.objects.all().delete()
