from unittest.mock import Mock, patch

import pytest
from api.search.tofu_searcher import PineconeTofuSearcher, SearchObjectType
from langchain_core.documents import Document


@pytest.fixture
def mock_pinecone_index():
    with patch("api.search.tofu_searcher.PineconeVectorStore") as mock:
        mock_instance = Mock()
        # Configure the mock methods to return empty lists by default
        mock_instance.similarity_search.return_value = []
        mock_instance.max_marginal_relevance_search.return_value = []
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_llm():
    with patch("api.search.tofu_searcher.get_llm_for_embeddings") as mock:
        mock.return_value = (None, Mock())
        yield mock


@pytest.fixture
def searcher(mock_pinecone_index, mock_llm):
    searcher = PineconeTofuSearcher(playbook_id="test_playbook", use_mrmr=False)
    return searcher


def test_search_no_object_types(searcher):
    # Test when no object types are specified
    query = "test query"
    top_k = 5
    object_types = []

    mock_docs = [
        Document(page_content="doc1", metadata={"column_id": "target_info"}),
        Document(page_content="doc2", metadata={"column_id": "assets"}),
    ]

    searcher.pinecone_index.similarity_search.return_value = mock_docs

    results = searcher.search(query, top_k, object_types)

    # Verify that search was called without filter
    searcher.pinecone_index.similarity_search.assert_called_once_with(
        query, top_k, namespace="test_playbook_serverless", filter={}
    )

    # Verify results format
    assert len(results) == 2
    assert results[0]["content"] == "doc1"
    assert results[0]["metadata"]["column_id"] == "target_info"


def test_search_with_single_object_type(searcher):
    # Test when one object type is specified
    query = "test query"
    top_k = 5
    object_types = [SearchObjectType.targets]

    mock_docs = [Document(page_content="doc1", metadata={"column_id": "target_info"})]

    searcher.pinecone_index.similarity_search.return_value = mock_docs

    results = searcher.search(query, top_k, object_types)

    # Verify that search was called with correct filter
    expected_filter = {"$or": [{"column_id": "target_info"}]}
    searcher.pinecone_index.similarity_search.assert_called_once_with(
        query, top_k, namespace="test_playbook_serverless", filter=expected_filter
    )

    assert len(results) == 1
    assert results[0]["content"] == "doc1"


def test_search_with_multiple_object_types(searcher):
    # Test when multiple object types are specified
    query = "test query"
    top_k = 5
    object_types = [SearchObjectType.targets, SearchObjectType.assets]

    mock_docs = [
        Document(page_content="doc1", metadata={"column_id": "target_info"}),
        Document(page_content="doc2", metadata={"column_id": "assets"}),
    ]

    searcher.pinecone_index.similarity_search.return_value = mock_docs

    results = searcher.search(query, top_k, object_types)

    # Verify that search was called with correct filter
    expected_filter = {"$or": [{"column_id": "target_info"}, {"column_id": "assets"}]}
    searcher.pinecone_index.similarity_search.assert_called_once_with(
        query, top_k, namespace="test_playbook_serverless", filter=expected_filter
    )

    assert len(results) == 2


def test_search_with_mrmr(searcher):
    # Test when use_mrmr is True
    searcher.use_mrmr = True
    query = "test query"
    top_k = 5
    object_types = [SearchObjectType.targets]

    mock_docs = [Document(page_content="doc1", metadata={"column_id": "target_info"})]

    searcher.pinecone_index.max_marginal_relevance_search.return_value = mock_docs

    results = searcher.search(query, top_k, object_types)

    # Verify that max_marginal_relevance_search was called instead of similarity_search
    searcher.pinecone_index.max_marginal_relevance_search.assert_called_once()
    searcher.pinecone_index.similarity_search.assert_not_called()

    assert len(results) == 1
    assert results[0]["content"] == "doc1"


def test_search_without_mrmr(searcher):
    # Test when use_mrmr is False
    searcher.use_mrmr = False
    query = "test query"
    top_k = 5
    object_types = [SearchObjectType.targets]

    mock_docs = [Document(page_content="doc1", metadata={"column_id": "target_info"})]

    searcher.pinecone_index.similarity_search.return_value = mock_docs

    results = searcher.search(query, top_k, object_types)

    # Verify that similarity_search was called instead of max_marginal_relevance_search
    searcher.pinecone_index.similarity_search.assert_called_once()
    searcher.pinecone_index.max_marginal_relevance_search.assert_not_called()

    assert len(results) == 1
    assert results[0]["content"] == "doc1"
