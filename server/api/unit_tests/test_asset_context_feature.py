# Test cases:
# 1. Existing custom instruction with one asset, at campaign, content_group, and component level.
# 2. One asset in new schema, at campaign, content_group, and component level.
# 3. One asset list in new schema, at campaign, content_group, and component level.
# 3. Multiple assets of one list in new schema, at campaign, content_group, and component level.
# 4. Multiple asset lists in new schema, at campaign, content_group, and component level.
# 5. One asset list and one asset of a different list in new schema, at campaign, content_group, and component level.
# 6. Existing campaign anchor content asset
# 7. Asset list campaign anchor content asset

from unittest.mock import MagicMock, patch

import pytest

from ..feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    ContentGenSettings,
    DataWrapperHolder,
    GenerateEnv,
)
from ..feature.feature_builder.asset_context_feature_builder import (
    AssetContextFeatureBuilder,
)
from ..models import (
    AssetInfo,
    AssetInfoGroup,
    Campaign,
    Content,
    ContentGroup,
    Playbook,
)


@pytest.fixture
def mock_playbook():
    return MagicMock(spec=Playbook)


@pytest.fixture
def mock_campaign(mock_playbook):
    campaign = MagicMock(spec=Campaign)
    campaign.playbook = mock_playbook
    campaign.campaign_params = {}
    return campaign


@pytest.fixture
def mock_content_group(mock_campaign):
    content_group = MagicMock(spec=ContentGroup)
    content_group.campaign = mock_campaign
    content_group.content_group_params = {}
    return content_group


@pytest.fixture
def mock_content(mock_content_group):
    content = MagicMock(spec=Content)
    content.content_group = mock_content_group
    content.content_params = {}
    return content


@pytest.fixture
def mock_gen_env(mock_content):
    data_wrapper = MagicMock()
    data_wrapper.playbook_instance = mock_content.content_group.campaign.playbook
    data_wrapper.campaign_instance = mock_content.content_group.campaign
    data_wrapper.content_group_instance = mock_content.content_group
    data_wrapper.content_instance = mock_content
    data_wrapper.content_type = "TODO"
    data_wrapper.aggregated_asset_params = []
    data_wrapper.aggregated_custom_instructions = []

    gen_settings = MagicMock()
    gen_settings.foundation_model = "TODO"

    gen_env = GenerateEnv(data_wrapper, gen_settings)
    return gen_env


@pytest.fixture
def feature_builder(mock_gen_env):
    return AssetContextFeatureBuilder(mock_gen_env)


@pytest.fixture
def mock_asset_info():
    asset = MagicMock(spec=AssetInfo)
    asset.asset_key = "Asset 1"
    asset.summary = "Test asset summary"
    asset.index = {"namespace": "test_namespace"}
    return asset


@pytest.fixture
def mock_asset_info_group(mock_asset_info):
    group = MagicMock(spec=AssetInfoGroup)
    group.assets = [mock_asset_info]
    return group


def test_extract_asset_context_no_assets(feature_builder):
    result = feature_builder.extract_asset_context()
    assert result == ""


@patch("api.feature.feature_builder.asset_context_feature_builder.AssetInfo")
def test_extract_asset_context_single_asset(
    mock_asset_info, feature_builder, mock_gen_env
):
    mock_gen_env._data_wrapper.aggregated_asset_params = [
        {
            "assets": {"Asset Group 1": "Asset 1"},
            "instruction": "Use this asset for the component",
        }
    ]

    mock_asset = MagicMock(spec=AssetInfo)
    mock_asset.summary = "Test asset summary"
    mock_asset.index = {"namespace": "test_namespace"}
    mock_asset_info.objects.get.return_value = mock_asset

    feature_builder.set_budgets({"asset_context": 100})

    result = feature_builder.extract_asset_context()

    expected = '<asset name="Asset Group 1 - Asset 1">\n<context>\n<summary>\nTest asset summary\n</summary>\n</context>\n\n</asset>\n\n<customInstruction>\nUse this asset for the component\n</customInstruction>'

    assert result == expected


def test_component_level_custom_instruction_with_asset(mock_content_group):
    # Add component level custom instruction with asset
    component_key = "test_component"
    component_data = {
        "meta": {
            "component_params": {
                "custom_instructions": [
                    {
                        "assets": [{"Asset Group 1": "Asset 1"}],
                        "instruction": "Use this asset for the component",
                    }
                ],
            }
        }
    }
    mock_content_group.components = {component_key: component_data}

    # Create ComponentWrapper
    content_wrapper = BaseContentWrapper.from_data_instance(mock_content_group)
    gen_settings = ContentGenSettings(
        content_wrapper, save_variations=False, foundation_model="mock"
    )
    gen_env = GenerateEnv(content_wrapper, gen_settings)
    data_wrapper_holder = DataWrapperHolder(gen_env)
    aggregated_asset_params = data_wrapper_holder.aggregated_asset_params

    assert len(aggregated_asset_params) == 1
    assert aggregated_asset_params[0] == {
        "assets": {"Asset Group 1": "Asset 1"},
        "instruction": "Use this asset for the component",
    }


def test_new_schema_component_custom_instruction_with_asset(
    mock_content_group, mock_asset_info_group
):
    with patch("api.models.AssetInfoGroup.objects") as mock_asset_info_group_objects:
        mock_asset_info_group_objects.create.return_value = mock_asset_info_group
        with patch("api.models.AssetInfo.objects") as mock_asset_info_objects:
            mock_asset_info_objects.get.return_value = mock_asset_info_group.assets[0]
            # Add component level custom instruction with asset
            component_key = "test_component"
            component_data = {
                "meta": {
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": {"Asset Group 1": ["Asset 1"]},
                                "instruction": "Use this asset for the component",
                            }
                        ],
                    }
                }
            }
            mock_content_group.components = {component_key: component_data}

            content_wrapper = BaseContentWrapper.from_data_instance(mock_content_group)
            gen_settings = ContentGenSettings(
                content_wrapper, save_variations=False, foundation_model="mock"
            )
            gen_env = GenerateEnv(content_wrapper, gen_settings)
            data_wrapper_holder = DataWrapperHolder(gen_env)
            aggregated_asset_params = data_wrapper_holder.aggregated_asset_params

            assert len(aggregated_asset_params) == 1
            assert aggregated_asset_params[0] == {
                "assets": {"Asset Group 1": "Asset 1"},
                "instruction": "Use this asset for the component",
            }

            # Test extract_asset_context
            feature_builder = AssetContextFeatureBuilder(gen_env)
            feature_builder.set_budgets({"asset_context": 100})

            result = feature_builder.extract_asset_context()

            expected = '<asset name="Asset Group 1 - Asset 1">\n<context>\n<summary>\nTest asset summary\n</summary>\n</context>\n\n</asset>\n\n<customInstruction>\nUse this asset for the component\n</customInstruction>'
            assert result == expected


def test_new_schema_component_custom_instruction_with_asset_list(
    mock_content_group, mock_asset_info_group, mock_asset_info
):
    with patch("api.models.AssetInfoGroup.objects") as mock_asset_info_group_objects:
        mock_asset_info_group_objects.create.return_value = mock_asset_info_group
        with patch("api.models.AssetInfo.objects") as mock_asset_info_objects:
            # Mock the filter method to return a list containing the mock_asset_info
            mock_asset_info_objects.filter.return_value = [mock_asset_info]
            mock_asset_info_objects.get.return_value = mock_asset_info

            # Add component level custom instruction with asset
            component_key = "test_component"
            component_data = {
                "meta": {
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": {"Asset Group 1": None},
                                "instruction": "Use this asset for the component",
                            }
                        ],
                    }
                }
            }
            mock_content_group.components = {component_key: component_data}

            content_wrapper = BaseContentWrapper.from_data_instance(mock_content_group)
            gen_settings = ContentGenSettings(
                content_wrapper, save_variations=False, foundation_model="mock"
            )
            gen_env = GenerateEnv(content_wrapper, gen_settings)
            data_wrapper_holder = DataWrapperHolder(gen_env)
            aggregated_asset_params = data_wrapper_holder.aggregated_asset_params

            assert len(aggregated_asset_params) == 1
            assert aggregated_asset_params[0] == {
                "assets": {"Asset Group 1": "Asset 1"},
                "instruction": "Use this asset for the component",
            }

            # Test extract_asset_context
            feature_builder = AssetContextFeatureBuilder(gen_env)
            feature_builder.set_budgets({"asset_context": 100})

            result = feature_builder.extract_asset_context()

            expected = '<asset name="Asset Group 1 - Asset 1">\n<context>\n<summary>\nTest asset summary\n</summary>\n</context>\n\n</asset>\n\n<customInstruction>\nUse this asset for the component\n</customInstruction>'
            assert result == expected


def test_new_schema_component_custom_instruction_one_list_two_assets(
    mock_content_group, mock_asset_info_group, mock_asset_info
):
    mock_asset_info_2 = MagicMock(spec=AssetInfo)
    mock_asset_info_2.asset_key = "Asset 2"
    mock_asset_info_2.summary = "Test asset summary 2"
    mock_asset_info_2.index = {"namespace": "test_namespace_2"}
    mock_asset_info_group.assets = [mock_asset_info, mock_asset_info_2]

    with patch("api.models.AssetInfoGroup.objects") as mock_asset_info_group_objects:
        mock_asset_info_group_objects.create.return_value = mock_asset_info_group
        with patch("api.models.AssetInfo.objects") as mock_asset_info_objects:
            # Mock the filter method to return a list containing the mock_asset_info
            mock_asset_info_objects.filter.return_value = [mock_asset_info]
            mock_asset_info_objects.get.return_value = mock_asset_info

            # Add component level custom instruction with asset
            component_key = "test_component"
            component_data = {
                "meta": {
                    "component_params": {
                        "custom_instructions": [
                            {
                                "assets": {"Asset Group 1": None},
                                "instruction": "Use this asset for the component",
                            }
                        ],
                    }
                }
            }
            mock_content_group.components = {component_key: component_data}

            content_wrapper = BaseContentWrapper.from_data_instance(mock_content_group)
            gen_settings = ContentGenSettings(
                content_wrapper, save_variations=False, foundation_model="mock"
            )
            gen_env = GenerateEnv(content_wrapper, gen_settings)
            data_wrapper_holder = DataWrapperHolder(gen_env)
            aggregated_asset_params = data_wrapper_holder.aggregated_asset_params

            assert len(aggregated_asset_params) == 1
            assert aggregated_asset_params[0] == {
                "assets": {"Asset Group 1": "Asset 1"},
                "instruction": "Use this asset for the component",
            }

            # Test extract_asset_context
            feature_builder = AssetContextFeatureBuilder(gen_env)
            feature_builder.set_budgets({"asset_context": 100})

            result = feature_builder.extract_asset_context()

            expected = '<asset name="Asset Group 1 - Asset 1">\n<context>\n<summary>\nTest asset summary\n</summary>\n</context>\n\n</asset>\n\n<customInstruction>\nUse this asset for the component\n</customInstruction>'
            assert result == expected


def test_campaign_anchor_content_asset(
    mock_campaign, mock_content, mock_playbook, mock_asset_info, mock_asset_info_group
):
    mock_campaign.campaign_params = {"assets": {"Asset Group 1": ["Asset 1"]}}
    mock_campaign.playbook = mock_playbook

    content_wrapper = BaseContentWrapper.from_data_instance(mock_content)
    gen_settings = ContentGenSettings(
        content_wrapper, save_variations=False, foundation_model="mock"
    )
    gen_env = GenerateEnv(content_wrapper, gen_settings)
    data_wrapper_holder = DataWrapperHolder(gen_env)
    aggregated_asset_params = data_wrapper_holder.aggregated_asset_params

    assert len(aggregated_asset_params) == 1
    assert aggregated_asset_params[0] == {
        "assets": {"Asset Group 1": "Asset 1"},
        "instruction": None,
        "meta": "repurpose_anchor_content",
    }

    feature_builder = AssetContextFeatureBuilder(gen_env)
    feature_builder.set_budgets({"asset_context": 100})

    with patch("api.models.AssetInfoGroup.objects") as mock_asset_info_group_objects:
        mock_asset_info_group_objects.create.return_value = mock_asset_info_group
        with patch("api.models.AssetInfo.objects") as mock_asset_info_objects:
            # Mock the filter method to return a list containing the mock_asset_info
            mock_asset_info_objects.filter.return_value = [mock_asset_info]
            mock_asset_info_objects.get.return_value = mock_asset_info
            result = feature_builder.extract_asset_context()

            expected = '<asset name="Asset Group 1 - Asset 1">\n<context>\n<summary>\nTest asset summary\n</summary>\n</context>\n\n</asset>\n'
            assert result == expected


def test_campaign_anchor_content_asset_list(
    mock_campaign, mock_content, mock_playbook, mock_asset_info, mock_asset_info_group
):
    mock_campaign.campaign_params = {"assets": {"Asset Group 1": None}}
    mock_campaign.playbook = mock_playbook

    with patch("api.models.AssetInfoGroup.objects") as mock_asset_info_group_objects:
        mock_asset_info_group_objects.create.return_value = mock_asset_info_group
        with patch("api.models.AssetInfo.objects") as mock_asset_info_objects:
            # Mock the filter method to return a list containing the mock_asset_info
            mock_asset_info_objects.filter.return_value = [mock_asset_info]
            mock_asset_info_objects.get.return_value = mock_asset_info
            content_wrapper = BaseContentWrapper.from_data_instance(mock_content)
            gen_settings = ContentGenSettings(
                content_wrapper, save_variations=False, foundation_model="mock"
            )
            gen_env = GenerateEnv(content_wrapper, gen_settings)
            data_wrapper_holder = DataWrapperHolder(gen_env)
            aggregated_asset_params = data_wrapper_holder.aggregated_asset_params

            assert len(aggregated_asset_params) == 1
            assert aggregated_asset_params[0] == {
                "assets": {"Asset Group 1": "Asset 1"},
                "instruction": None,
                "meta": "repurpose_anchor_content",
            }

            feature_builder = AssetContextFeatureBuilder(gen_env)
            feature_builder.set_budgets({"asset_context": 100})
            result = feature_builder.extract_asset_context()

            expected = '<asset name="Asset Group 1 - Asset 1">\n<context>\n<summary>\nTest asset summary\n</summary>\n</context>\n\n</asset>\n'
            assert result == expected


def test_campaign_anchor_content_asset_list_multiple_assets(
    mock_campaign, mock_content, mock_playbook, mock_asset_info, mock_asset_info_group
):
    mock_asset_info_2 = MagicMock(spec=AssetInfo)
    mock_asset_info_2.asset_key = "Asset 2"
    mock_asset_info_2.summary = "Test asset summary 2"
    mock_asset_info_2.index = {"namespace": "test_namespace_2"}
    mock_asset_info_group.assets = [mock_asset_info, mock_asset_info_2]

    mock_campaign.campaign_params = {
        "assets": {"Asset Group 1": ["Asset 1", "Asset 2"]}
    }
    mock_campaign.playbook = mock_playbook

    content_wrapper = BaseContentWrapper.from_data_instance(mock_content)
    gen_settings = ContentGenSettings(
        content_wrapper, save_variations=False, foundation_model="mock"
    )
    gen_env = GenerateEnv(content_wrapper, gen_settings)
    data_wrapper_holder = DataWrapperHolder(gen_env)
    aggregated_asset_params = data_wrapper_holder.aggregated_asset_params

    assert len(aggregated_asset_params) == 2
    assert aggregated_asset_params[0] == {
        "assets": {"Asset Group 1": "Asset 1"},
        "instruction": None,
        "meta": "repurpose_anchor_content",
    }
    assert aggregated_asset_params[1] == {
        "assets": {"Asset Group 1": "Asset 2"},
        "instruction": None,
        "meta": "repurpose_anchor_content",
    }

    feature_builder = AssetContextFeatureBuilder(gen_env)
    feature_builder.set_budgets({"asset_context": 100})

    with patch("api.models.AssetInfoGroup.objects") as mock_asset_info_group_objects:
        mock_asset_info_group_objects.create.return_value = mock_asset_info_group
        with patch("api.models.AssetInfo.objects") as mock_asset_info_objects:
            # Mock the filter method to return a list containing the mock_asset_info
            mock_asset_info_objects.filter.return_value = [
                mock_asset_info,
                mock_asset_info_2,
            ]

            def mock_get(asset_key, *args, **kwargs):
                if asset_key == "Asset 1":
                    return mock_asset_info
                elif asset_key == "Asset 2":
                    return mock_asset_info_2
                return None

            mock_asset_info_objects.get.side_effect = mock_get
            result = feature_builder.extract_asset_context()

            expected = '<asset name="Asset Group 1 - Asset 1">\n<context>\n<summary>\nTest asset summary\n</summary>\n</context>\n\n</asset>\n<asset name="Asset Group 1 - Asset 2">\n<context>\n<summary>\nTest asset summary 2\n</summary>\n</context>\n\n</asset>\n'
            assert result == expected
