# List of all the views in the API
# 'api-root'
# 'heartbeat-p13n-campaign-gen'
# 'heartbeat-celery'
# 'public-analytics_page_view'
# 'public-components'
# 'task-status'
# 'eval-gen-review-template'
# 'eval-gen-review-repurpose'
# 'eval-gen-review'
# 'eval-gen-repurpose'
# 'eval-gen'
# 'chatbot-reset'
# 'chatbot-chat'
# 'content-variation-detail'
# 'content-prompt-templates-v2'
# 'content-prompt-templates'
# 'content-make-result-public'
# 'content-get-llm-inputs'
# 'content-gen'
# 'content-detail'
# 'content-list'
# 'content-group-bulk-create-content'
# 'content-group-detail'
# 'content-group-list'
# 'campaign-terminate-gen'
# 'campaign-metric-tiles'
# 'campaign-gen-status'
# 'campaign-gen'
# 'campaign-delete-results'
# 'campaign-clone'
# 'campaign-detail'
# 'campaign-list'
# 'target-info-rebuild'
# 'target-info-extract-external'
# 'target-info-detail'
# 'target-info-retrieve-by-keys'
# 'target-info-list'
# 'target-info-group-rebuild'
# 'target-info-group-bulk-update'
# 'target-info-group-bulk-delete'
# 'target-info-group-bulk-create'
# 'target-info-group-detail'
# 'target-info-group-list'
# 'playbook-status'
# 'playbook-metric-tiles'
# 'playbook-force-context-refresh'
# 'playbook-copy-from-playbook'
# 'playbook-detail'
# 'playbook-list'
# 'user-set-password'
# 'user-detail'
# 'user-authentication'
# 'user-list'
# 'schema-redoc'
# 'schema-swagger-ui'
# 'index'
