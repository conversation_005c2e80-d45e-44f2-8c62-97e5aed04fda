from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

import pytest
from django.db.models import QuerySet

from ..autopilot_run import AutopilotObject<PERSON>uilder, AutopilotSessionProcessor
from ..models import AutopilotRun, Campaign, Content, TargetInfoGroup


@pytest.fixture
def mock_campaign():
    return MagicMock(spec=Campaign, id=123)


@pytest.fixture
def mock_target_info_group():
    return MagicMock(spec=TargetInfoGroup, target_info_group_key="Test Group")


@pytest.fixture
def builder(mock_campaign):
    return AutopilotObjectBuilder(campaign_id=mock_campaign.id)


@pytest.fixture
def session_processor():
    return AutopilotSessionProcessor(session_runs=[])


class TestAutopilotObjectBuilder:

    def test_get_base_runs(self, builder, mock_campaign):
        """Test _get_base_runs returns correct initial structure"""
        with patch("django.utils.timezone.now") as mock_now:
            mock_now.return_value = datetime(2024, 1, 1, tzinfo=timezone.utc)
            with patch.object(Campaign.objects, "get") as mock_get:
                mock_get.return_value = mock_campaign
                with patch.object(AutopilotRun.objects, "filter") as mock_filter:
                    # Create mock querysets
                    mock_campaign_queryset = MagicMock(spec=QuerySet)
                    mock_session_queryset = MagicMock(spec=QuerySet)
                    mock_final_queryset = MagicMock(spec=QuerySet)

                    # Setup first queryset (campaign_runs)
                    mock_campaign_queryset.exclude.return_value.values_list.return_value.distinct.return_value = [
                        "session1",
                        "session2",
                    ]

                    # Setup second queryset chain (all_session_runs)
                    mock_session_queryset.filter.return_value = mock_final_queryset
                    mock_session_runs = [
                        MagicMock(session_id="session1"),
                        MagicMock(session_id="session2"),
                    ]
                    mock_final_queryset.order_by.return_value = mock_session_runs

                    # Make filter return different querysets based on arguments
                    def mock_filter_side_effect(**kwargs):
                        if "campaign" in kwargs and "created_at__gte" in kwargs:
                            return mock_campaign_queryset
                        if "session_id__in" in kwargs:
                            return mock_session_queryset
                        return mock_final_queryset

                    mock_filter.side_effect = mock_filter_side_effect

                    result = builder._get_base_runs()

                    assert len(result) == 2
                    assert "session1" in result
                    assert "session2" in result
                    assert len(result["session1"]) == 1
                    assert len(result["session2"]) == 1

    def test_process_crm_sync(self, session_processor, mock_target_info_group):
        """Test _process_crm_sync updates visibility correctly"""
        mock_run = MagicMock(
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            target_info_group=mock_target_info_group,
        )

        session_processor._process_crm_sync(mock_run)

        assert session_processor.timestamp == mock_run.created_at
        assert (
            session_processor.target_info_group_name
            == mock_run.target_info_group.target_info_group_key
        )

    def test_process_target_creation_success(self, session_processor):
        """Test _process_target_creation with successful status"""
        mock_run = MagicMock(
            campaign=None,
            status={
                "Done": ["target1", "target2"],
                "In Progress": [],
                "Error": [],
            },
        )

        session_processor._process_target_creation(mock_run)

        assert "target_creation" in session_processor.autopilot_visibility
        assert session_processor.autopilot_visibility["target_creation"]["Done"] == [
            "target1",
            "target2",
        ]
        assert (
            session_processor.autopilot_visibility["target_creation"]["In Progress"]
            == []
        )
        assert session_processor.autopilot_visibility["target_creation"]["Error"] == []

    def test_process_content_generation_success(self, session_processor, mock_campaign):
        """Test _process_content_generation with successful status"""
        mock_content = MagicMock(
            id=1,
            content_group=MagicMock(
                content_group_name="Test Content Group",
            ),
        )

        mock_run = MagicMock(
            campaign=mock_campaign,
            status={
                "Done": [1],
                "In Progress": [],
                "Error": [],
            },
        )

        with patch.object(Content.objects, "filter") as mock_filter:
            mock_filter.return_value.select_related.return_value = [mock_content]
            session_processor._process_content_generation(mock_run)

        assert "content_generation" in session_processor.autopilot_visibility
        assert (
            "Test Content Group"
            in session_processor.autopilot_visibility["content_generation"]
        )
        assert session_processor.autopilot_visibility["content_generation"][
            "Test Content Group"
        ]["Done"] == [1]
        assert (
            session_processor.autopilot_visibility["content_generation"][
                "Test Content Group"
            ]["In Progress"]
            == []
        )
        assert (
            session_processor.autopilot_visibility["content_generation"][
                "Test Content Group"
            ]["Error"]
            == []
        )

    def test_process_export_success(self, session_processor, mock_campaign):
        """Test _process_export with successful status"""
        mock_content = MagicMock(
            id=1, content_group=MagicMock(content_group_name="Test Content Group")
        )

        mock_run = MagicMock(
            campaign=mock_campaign,
            status={
                "Done": [1],
                "In Progress": [],
                "Error": [],
            },
        )

        with patch.object(Content.objects, "filter") as mock_filter:
            mock_filter.return_value.select_related.return_value = [mock_content]
            session_processor._process_export(mock_run)

        assert "export" in session_processor.autopilot_visibility
        assert "Test Content Group" in session_processor.autopilot_visibility["export"]
        assert session_processor.autopilot_visibility["export"]["Test Content Group"][
            "Done"
        ] == [1]
        assert (
            session_processor.autopilot_visibility["export"]["Test Content Group"][
                "In Progress"
            ]
            == []
        )
        assert (
            session_processor.autopilot_visibility["export"]["Test Content Group"][
                "Error"
            ]
            == []
        )

    def test_get_autopilot_visibility_integration(
        self, session_processor, mock_campaign, mock_target_info_group
    ):
        """Test full integration of get_autopilot_visibility"""
        mock_runs = [
            MagicMock(
                session_id="session1",
                autopilot_action_type=AutopilotRun.AutopilotActionType.CRM_SYNC,
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                target_info_group=mock_target_info_group,
                campaign=mock_campaign,
                status={
                    "Done": ["target1"],
                    "In Progress": [],
                    "Error": [],
                },
            ),
            MagicMock(
                session_id="session1",
                autopilot_action_type=AutopilotRun.AutopilotActionType.TARGET_CREATION,
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                campaign=None,
                status={
                    "Done": ["target1"],
                    "In Progress": [],
                    "Error": [],
                },
            ),
        ]

        with patch("django.utils.timezone.now") as mock_now:
            mock_now.return_value = datetime(2024, 1, 1, tzinfo=timezone.utc)
            with patch.object(Campaign.objects, "get") as mock_get:
                mock_get.return_value = mock_campaign
                with patch.object(AutopilotRun.objects, "filter") as mock_filter:
                    mock_filter.return_value = mock_runs
                    session_processor.session_runs = mock_runs
                    session_processor.process()

                    assert len(session_processor.autopilot_visibility) == 1
                    assert "target_creation" in session_processor.autopilot_visibility
                    assert session_processor.autopilot_visibility["target_creation"][
                        "Done"
                    ] == ["target1"]
                    assert (
                        session_processor.autopilot_visibility["target_creation"][
                            "In Progress"
                        ]
                        == []
                    )
                    assert (
                        session_processor.autopilot_visibility["target_creation"][
                            "Error"
                        ]
                        == []
                    )

    @pytest.mark.parametrize(
        "autopilot_visibility,expected_status,expected_message",
        [
            (
                {
                    "all_targets_creation": {
                        "Done": ["target1"],
                        "In Progress": [],
                        "Error": [],
                    },
                    "target_creation": {
                        "Done": ["target1"],
                        "In Progress": [],
                        "Error": [],
                    },
                    "content_generation": {
                        "Test Content Group": {
                            "Done": [1],
                            "In Progress": [],
                            "Error": [],
                        }
                    },
                    "export": {
                        "Test Content Group": {
                            "Done": [1],
                            "In Progress": [],
                            "Error": [],
                        }
                    },
                },
                "Success",
                "All tasks completed",
            ),
            (
                {
                    "target_creation": {
                        "Done": [],
                        "In Progress": [],
                        "Error": ["target1"],
                    },
                },
                "Error",
                "1 tasks failed",
            ),
            (
                {
                    "target_creation": {
                        "Done": [],
                        "In Progress": ["target1"],
                        "Error": [],
                    },
                },
                "In Progress",
                "1 tasks pending",
            ),
            (
                {},
                "Unknown",
                "Unknown status",
            ),
        ],
    )
    def test_build_status_message(
        self, session_processor, autopilot_visibility, expected_status, expected_message
    ):
        """Test build_status_message with different status combinations"""
        session_processor.autopilot_visibility = autopilot_visibility
        result_status, result_message = session_processor.build_status_message()
        assert result_status == expected_status
        assert result_message == expected_message
