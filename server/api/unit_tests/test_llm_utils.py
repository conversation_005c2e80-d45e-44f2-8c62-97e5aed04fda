from unittest.mock import MagicMock, patch

import pytest
from langchain_anthropic import ChatAnthropic
from langchain_openai import ChatOpenAI

from ..llms import model_is_json_enabled


def test_model_is_json_enabled():
    assert model_is_json_enabled("gpt-4o-2024-11-20")
    assert model_is_json_enabled("claude-3-5-sonnet-20240620")
    assert model_is_json_enabled("us.anthropic.claude-3-5-sonnet-20240620-v1:0")
    assert not model_is_json_enabled("any_other_model")
