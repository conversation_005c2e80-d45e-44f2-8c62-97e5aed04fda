import unittest

from ..content_group import ContentGroupHandler


class TestContentGroupHandler(unittest.TestCase):
    def setUp(self):
        # Create mock campaign with campaign_params
        self.campaign = type("Campaign", (object,), {"campaign_params": {}})

        # Create mock content_group with campaign reference
        self.content_group = type(
            "ContentGroup",
            (object,),
            {"content_group_name": "test_group", "campaign": self.campaign},
        )

        self.handler = ContentGroupHandler(self.content_group)

    def test_get_content_name(self):
        target_list = {"b": "beta", "a": "alpha", "c": "gamma"}
        expected_name = "test_group_alpha-beta-gamma"
        result = self.handler.get_content_name(target_list)
        self.assertEqual(result, expected_name)

    def test_get_content_name_single_element(self):
        target_list = {"a": "alpha"}
        expected_name = "test_group_alpha"
        result = self.handler.get_content_name(target_list)
        self.assertEqual(result, expected_name)

    def test_get_content_name_different_order(self):
        target_list = {"b": "alpha", "a": "beta", "c": "gamma"}
        expected_name = "test_group_beta-alpha-gamma"
        result = self.handler.get_content_name(target_list)
        self.assertEqual(result, expected_name)

    def test_get_content_name_with_concat_enabled(self):
        # Set targets_concat before creating the handler
        self.campaign.campaign_params["targets_concat"] = True

        # Re-create the handler to ensure it has the updated campaign params
        self.handler = ContentGroupHandler(self.content_group)

        target_list = {"audience": "young_professionals"}
        expected_name = "test_group_audience_young_professionals"
        result = self.handler.get_content_name(target_list)
        self.assertEqual(result, expected_name)


if __name__ == "__main__":
    unittest.main()
