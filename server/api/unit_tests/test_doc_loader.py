import unittest
from unittest.mock import MagicMock, patch

from api.playbook_build.doc_loader import TofuURLLoader
from django.test import TestCase

from ..playbook_build.doc_loader import DocLoader, get_template


class TestDocLoader(TestCase):
    def setUp(self):
        self.test_metadata = {
            "playbook_id": "test-playbook",
            "column_id": "test-column",
            "key_ids": ["test-key"],
        }
        self.doc_loader = DocLoader(self.test_metadata)

    def test_extract_docs_with_change_type(self):
        # Setup
        test_docs = {
            "doc1": {
                "type": "text",
                "value": "Test content",
                "meta": {"label": "Test Label"},
            }
        }

        # Execute
        documents, errors, build_status = self.doc_loader.extract_docs_with_change_type(
            test_docs, "data_added"
        )

        # Assert
        self.assertEqual(len(documents), 1)
        self.assertEqual(len(errors), 0)
        self.assertEqual(build_status["doc1"]["status"], "success")
        self.assertEqual(documents[0].metadata["field_name"], "Test Label")
        self.assertEqual(documents[0].metadata["change_type"], "data_added")

    def test_extract_docs_delta(self):
        # Setup
        old_docs = {
            "doc1": {
                "type": "text",
                "value": "Old content",
                "meta": {"label": "Test Label"},
            }
        }
        new_docs = {
            "doc1": {
                "type": "text",
                "value": "New content",
                "meta": {"label": "Test Label"},
            },
            "doc2": {
                "type": "text",
                "value": "New doc",
                "meta": {"label": "New Label"},
            },
        }

        # Execute
        documents, errors, build_status = self.doc_loader.extract_docs_delta(
            old_docs, new_docs
        )

        # Assert
        self.assertEqual(len(documents), 2)  # One modified, one added
        self.assertEqual(len(errors), 0)
        self.assertEqual(build_status["doc1"]["status"], "success")
        self.assertEqual(build_status["doc2"]["status"], "success")

    @patch("api.playbook_build.doc_loader.get_s3_file")
    def test_get_template_s3(self, mock_get_s3_file):
        # Setup
        mock_get_s3_file.return_value = '{"text": "Test content"}'
        template_url = "/api/web/storage/s3-presigned-url?file=test.json&fileType=application/json&directory=test-bucket"

        # Execute
        result = get_template(template_url)

        # Assert
        self.assertEqual(result["text"], "Test content")
        mock_get_s3_file.assert_called_once()

    @patch.object(TofuURLLoader, "load")
    def test_get_template_url(self, mock_load):
        # Setup
        mock_load.return_value = [MagicMock(page_content="Test content")]
        template_url = "https://example.com/test"

        # Execute
        result = get_template(template_url)

        # Assert
        self.assertEqual(result["text"], "Test content")
        mock_load.assert_called_once()
