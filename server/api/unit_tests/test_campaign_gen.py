import uuid
from unittest.mock import MagicMock, patch

from django.test import TestCase

from ..campaign import CampaignHandler
from ..models import (
    Campaign,
    CompanyInfo,
    Content,
    ContentGroup,
    ContentVariation,
    Playbook,
    TofuUser,
)


class TestCampaignGen(TestCase):
    # Given content gen mocking, test campaign gen.
    def setUp(self):
        self.user = TofuUser.objects.create(username="testuser")
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=CompanyInfo.objects.create()
        )
        self.campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            creator=self.user,
            playbook=self.playbook,
            campaign_params={
                "targets": [{"target1": ["value1"]}],
                "foundation_model": "gpt-4o-2024-11-20",
                "campaign_goal": "Personalization",
            },
            campaign_status={},
        )
        self.content_group = ContentGroup.objects.create(
            campaign=self.campaign,
            creator=self.user,
            components={
                "comp1": {"meta": {"type": "text", "component_type": ""}},
                "comp2": {"meta": {"type": "text", "component_type": ""}},
            },
            content_group_params={},
            content_group_status={},
        )
        self.content = Content.objects.create(
            content_group=self.content_group, content_params={}, content_status={}
        )

    @patch("api.content.JointContentGenerator.gen")
    @patch("api.content.PlaybookBuilder")
    @patch("api.playbook.PlaybookHandler.load_from_db")
    def test_campaign_gen(
        self, mock_playbook_handler, mock_playbook_builder, mock_content_gen
    ):
        # Mock LLM return values
        mock_content_gen.return_value = {
            "comp1": {
                "meta": {
                    "variations": [{"text": "Mocked content for comp1"}],
                    "current_variation_index": 0,
                    "current_version": {"text": "Mocked content for comp1"},
                }
            },
            "comp2": {
                "meta": {
                    "variations": [{"text": "Mocked content for comp2"}],
                    "current_variation_index": 0,
                    "current_version": {"text": "Mocked content for comp2"},
                }
            },
        }

        mock_playbook_handler.return_value = MagicMock()
        mock_playbook_builder_instance = MagicMock()
        mock_playbook_builder.return_value = mock_playbook_builder_instance
        mock_playbook_builder_instance.pre_build_playbook_context.return_value = None

        campaign_handler = CampaignHandler(self.campaign)
        cache_key = str(uuid.uuid4())

        # Call the gen method
        generated_content_ids = campaign_handler.gen(
            content_group_ids=[self.content_group.id],
            content_ids=[self.content.id],
            cache_key=cache_key,
            model_name="gpt-4o-2024-11-20",
            num_of_variations=1,
        )

        # Assertions
        self.assertEqual(len(generated_content_ids), 1)
        self.assertEqual(generated_content_ids[0], self.content.id)

        # Verify content status
        updated_content = Content.objects.get(id=self.content.id)
        self.assertEqual(
            updated_content.content_status["gen_status"]["status"], "FINISHED"
        )

        # Verify content variation
        content_variation = ContentVariation.objects.get(content=self.content)
        self.assertIn("comp1", content_variation.variations)
        self.assertIn("comp2", content_variation.variations)
        self.assertEqual(
            content_variation.variations["comp1"]["meta"]["current_version"]["text"],
            "Mocked content for comp1",
        )
        self.assertEqual(
            content_variation.variations["comp2"]["meta"]["current_version"]["text"],
            "Mocked content for comp2",
        )

        # Verify that the mocked methods were called
        mock_playbook_handler.assert_called_once_with(self.playbook.id)
        mock_content_gen.assert_called_once()
