import logging
from unittest.mock import ANY, patch

# from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model

# tests.py
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient, APITestCase
from server.celery import app as celery_app

from ..models import Playbook
from .helper.tofu_testcase import TofuTestCase

celery_app.conf.update(CELERY_ALWAYS_EAGER=True)


PLAYBOOK_LIST_API = "playbook-list"
PLAYBOOK_CREATE_API = "playbook-list"
PLAYBOOK_RETRIEVE_API = "playbook-list"
PLAYBOOK_PATCH_API = "playbook-list"
PLAYBOOK_DELETE_API = "playbook-list"


class PlaybookAPITest(APITestCase, TofuTestCase):
    # @patch("api.tasks.async_postprocess_playbook.apply_async")
    def setUp(self):
        self.admin_user = self.create_user(username="test_tofu_admin", is_staff=True)

        self.admin_user_client = APIClient()
        self.admin_user_client.force_authenticate(user=self.admin_user)

    def get_playbook(self):
        playbook = Playbook.objects.filter(users=self.admin_user).first()
        return playbook

    def test_create_playbook(self):
        url = reverse("playbook-list")  # Assuming the list route is used for creation
        data = {"name": "New Playbook"}
        response = self.admin_user_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            Playbook.objects.count(), 2
        )  # 1 is created together with the user creation
        # self.assertEqual(Playbook.objects.get().name, "New Playbook")

    def test_list_playbooks(self):
        url = reverse("playbook-list")
        response = self.admin_user_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Additional assertions can be made based on the response data structure

    def test_retrieve_playbook(self):
        playbook = self.get_playbook()  # A method to get or create a playbook instance
        url = reverse("playbook-detail", kwargs={"playbook_id": playbook.id})
        response = self.admin_user_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], playbook.name)

    def test_update_playbook(self):
        playbook = self.get_playbook()
        url = reverse("playbook-detail", kwargs={"playbook_id": playbook.id})

        data = {"name": "Updated Playbook Name"}  # Example update data
        response = self.admin_user_client.patch(
            url, data, format="json"
        )  # Use 'patch' or 'put' as needed
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    # def test_delete_playbook(self):
    #     playbook = self.get_playbook()
    #     url = reverse("playbook-detail", kwargs={"playbook_id": playbook.id})
    #     response = self.admin_user_client.delete(url)
    #     self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
    #     self.assertFalse(Playbook.objects.filter(id=playbook.id).exists())
