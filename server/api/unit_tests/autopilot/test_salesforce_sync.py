import unittest
from unittest.mock import Mock, patch

from ...models import Playbook, TargetInfo, TargetInfoGroup
from ...sync.e2e.salesforce_sync import PlaybookSalesforceSyncer


class TestPlaybookSalesforceSyncer(unittest.TestCase):
    def setUp(self):
        # Mock the necessary objects
        self.playbook_instance = Mock(spec=Playbook)
        self.target_info_group = Mock(spec=TargetInfoGroup)
        self.target_info_group.meta = {
            "type": "Account",
            "importListSettings": {
                "type": "Account",
                "listType": "dynamic",
                "tableData": [
                    {"columnName": "Id", "dataType": "salesforce_identifier"},
                    {"columnName": "Name", "dataType": "subtarget_name"},
                    {"columnName": "Description", "dataType": "text"},
                    {"columnName": "Website", "dataType": "url"},
                ],
            },
        }
        self.campaigns = []
        self.list_id = "test_list_id"

        self.syncer = PlaybookSalesforceSyncer(
            self.playbook_instance, self.target_info_group, self.campaigns, self.list_id
        )

    def test_record_type(self):
        """Test record_type property returns correct value"""
        self.assertEqual(self.syncer.record_type, "Account")

    def test_record_type_plural(self):
        """Test record_type_plural property returns correct value"""
        self.assertEqual(self.syncer.record_type_plural, "Accounts")

    def test_get_playbook_records(self):
        """Test _get_playbook_records method"""
        # Setup mock target info objects
        mock_records = [
            Mock(meta={"salesforce": {"Id": "001xxx", "object_identifier": "Id"}}),
            Mock(meta={"salesforce": {"Id": "002xxx", "object_identifier": "Id"}}),
        ]

        with patch(
            "api.sync.e2e.salesforce_sync.TargetInfo.objects"
        ) as mock_target_info:
            mock_target_info.filter.return_value = mock_records
            records = self.syncer.integration._get_playbook_records()
            self.assertEqual(records, ["001xxx", "002xxx"])

    def test_get_platform_records(self):
        """Test _get_platform_records method"""
        # Setup mock Salesforce agent
        mock_sf_agent = Mock()
        mock_sf_agent.get_all_object_type_properties.return_value = ["Id", "Name"]
        mock_sf_agent.get_list_records.return_value = [
            {"Id": "001xxx", "Name": "Test Account 1"},
            {"Id": "002xxx", "Name": "Test Account 2"},
        ]

        with patch("api.sync.e2e.salesforce_sync.ParagonWrapper") as mock_paragon:
            mock_paragon.return_value.get_salesforce_agent.return_value = mock_sf_agent
            self.syncer._init_integrations()
            records = self.syncer.integration._get_platform_records()
            self.assertEqual(records, ["001xxx", "002xxx"])

    def test_create_target_data(self):
        """Test _create_target_data method"""
        record_data = {
            "Id": "001xxx",
            "Name": "Test Account",
            "Description": "Test Description",
            "Website": "https://example.com",
        }
        target_key, meta, target_data = self.syncer.integration._create_target_data(
            record_data, 0
        )

        # Verify results
        self.assertEqual(target_key, "Test Account")
        self.assertEqual(meta["synced_from"], "salesforce")
        self.assertEqual(meta["salesforce"]["object_type"], "Account")
        self.assertEqual(meta["salesforce"]["object_identifier"], "Id")

        # Verify target data contains correct entries
        self.assertTrue(
            any(
                doc["type"] == "text" and doc["value"] == "Test Description"
                for doc in target_data.values()
            )
        )
        self.assertTrue(
            any(
                doc["type"] == "url" and doc["value"] == "https://example.com"
                for doc in target_data.values()
            )
        )

    def test_create_target_data_missing_required_fields(self):
        """Test _create_target_data method with missing required fields"""
        record_data = {
            "Id": "001xxx",
            # Missing Name field which is required as subtarget_name
            "Description": "Test Description",
        }
        target_key, meta, target_data = self.syncer.integration._create_target_data(
            record_data, 0
        )
        self.assertIsNone(target_key)
        self.assertIsNone(meta)
        self.assertIsNone(target_data)


if __name__ == "__main__":
    unittest.main()
