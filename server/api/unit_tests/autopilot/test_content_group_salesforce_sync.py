import unittest
import uuid
from unittest.mock import Mock, patch

from ...paragon_wrapper import BaseParagonAgent, SalesforceAgent
from ...sync.content_sync.content_group_salesforce_sync import (
    ContentGroupSalesforceSyncer,
    has_completed_export,
)


class TestContentGroupSalesforceSyncer(unittest.TestCase):
    def setUp(self):
        # Mock content group and its attributes
        self.content_group = Mock()
        self.content_group.campaign.playbook = Mock()
        self.content_group.content_group_params = {
            "export_settings": {
                "salesforce": {"email": {"dynamic": {"targetsSetting": []}}},
                "other": {"page": {"embed": {}}},
            }
        }

        # Initialize syncer
        self.syncer = ContentGroupSalesforceSyncer(
            content_group=self.content_group,
            record_type="TestType",
            record_type_plural="TestTypes",
        )

    def test_has_completed_export(self):
        # Test empty settings
        self.assertFalse(has_completed_export(None))
        self.assertFalse(has_completed_export({}))

        # Test incomplete export
        settings = {"targetsSetting": [{"exportStatus": "Pending"}]}
        self.assertFalse(has_completed_export(settings))

        # Test completed export
        settings = {"targetsSetting": [{"exportStatus": "Completed"}]}
        self.assertTrue(has_completed_export(settings))

    def test_is_eligible_auto_sync_email(self):
        # Test ineligible case
        self.assertFalse(self.syncer._is_eligible_auto_sync_email())

        # Test eligible case
        self.content_group.content_group_params["export_settings"]["salesforce"][
            "email"
        ]["dynamic"] = {"targetsSetting": [{"exportStatus": "Completed"}]}
        self.assertTrue(self.syncer._is_eligible_auto_sync_email())

    def test_is_eligible_auto_sync_page(self):
        # Test ineligible case
        self.assertFalse(self.syncer._is_eligible_auto_sync_page())

        # Test eligible case
        self.content_group.content_group_params["export_settings"]["other"]["page"][
            "embed"
        ] = {"targetsSetting": [{"exportStatus": "Completed"}]}
        self.assertTrue(self.syncer._is_eligible_auto_sync_page())

    def test_export_with_no_contents(self):
        with self.assertRaises(ValueError):
            self.syncer.export()

    def test_get_unexported_contents(self):
        with (
            patch("api.models.Content.objects") as mock_content_objects,
            patch(
                "api.models.ContentVariation.objects"
            ) as mock_content_variation_objects,
            patch("django.db.models.Exists") as mock_exists,
            patch("django.db.models.OuterRef") as mock_outer_ref,
        ):

            # Setup mock queryset chain
            mock_queryset = Mock()
            mock_queryset_exclude = Mock()
            mock_queryset_final = Mock()

            mock_content_objects.filter.return_value = mock_queryset
            mock_queryset.exclude.return_value = mock_queryset_exclude
            mock_queryset_exclude.filter.return_value = mock_queryset_final

            # Mock the final result
            mock_queryset_final.__iter__ = Mock(
                return_value=iter(["content1", "content2"])
            )

            # Setup content group with some target settings to avoid early return
            self.content_group.content_group_params["export_settings"]["salesforce"][
                "email"
            ]["dynamic"]["targetsSetting"] = [
                {"contentId": 1, "exportStatus": "Completed"}
            ]

            # Test getting unexported contents
            result = self.syncer.get_unexported_contents()

            # Verify the result
            self.assertEqual(list(result), ["content1", "content2"])

            # Verify the filtering chain was called correctly
            mock_content_objects.filter.assert_called_once_with(
                content_group=self.content_group
            )
            mock_queryset.exclude.assert_called_once()
            mock_queryset_exclude.filter.assert_called_once()

    def test_get_unexported_contents_no_target_settings(self):
        # Test case when there are no target settings (should return empty list)
        self.content_group.content_group_params["export_settings"]["salesforce"][
            "email"
        ]["dynamic"]["targetsSetting"] = []
        self.content_group.content_group_params["export_settings"]["other"]["page"][
            "embed"
        ] = {}

        result = self.syncer.get_unexported_contents()

        # Should return empty list when no target settings exist
        self.assertEqual(result, [])

    def test_get_unexported_contents_filtered_out_by_conditions(self):
        with (
            patch("api.models.Content.objects") as mock_content_objects,
            patch(
                "api.models.ContentVariation.objects"
            ) as mock_content_variation_objects,
            patch("django.db.models.Exists") as mock_exists,
            patch("django.db.models.OuterRef") as mock_outer_ref,
        ):

            # Setup mock queryset chain
            mock_queryset = Mock()
            mock_queryset_exclude = Mock()
            mock_queryset_final = Mock()

            mock_content_objects.filter.return_value = mock_queryset
            mock_queryset.exclude.return_value = mock_queryset_exclude
            # Final filter returns empty list (content filtered out by conditions)
            mock_queryset_exclude.filter.return_value = []

            # Setup content group with target settings but different exported content
            self.content_group.content_group_params["export_settings"]["salesforce"][
                "email"
            ]["dynamic"]["targetsSetting"] = [
                {
                    "contentId": 999,
                    "exportStatus": "Completed",
                }  # Different ID so content passes exclude
            ]

            # Test getting unexported contents
            result = self.syncer.get_unexported_contents()

            # Verify that content was filtered out by the additional conditions
            self.assertEqual(list(result), [])

            # Verify the filtering chain was called correctly
            mock_content_objects.filter.assert_called_once_with(
                content_group=self.content_group
            )
            mock_queryset.exclude.assert_called_once()
            # The final filter should be called with the ContentVariation and status conditions
            mock_queryset_exclude.filter.assert_called_once()


if __name__ == "__main__":
    unittest.main()
