from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, patch

from django.core.cache import cache
from django.test import TestCase

from ...models import Campaign, CompanyInfo, Playbook, TargetInfoGroup
from ...playbook_sync import PlaybookSyncerManager, sync_list_task


class TestPlaybookSync(TestCase):
    def setUp(self):
        # Create CompanyInfo first
        self.company_info = CompanyInfo.objects.create()
        # Then create Playbook with company_object
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company_info
        )
        self.campaign = Campaign.objects.create(
            playbook=self.playbook,
            campaign_params={
                "enable_auto_sync": True,
                "allSelectedTargets": ["test_target_group"],
            },
        )
        self.target_info_group = TargetInfoGroup.objects.create(
            playbook=self.playbook,
            target_info_group_key="test_target_group",
            meta={
                "autopilot_duration": 20,
                "importListSettings": {"listId": "test_list_id", "syncFrom": "hubspot"},
            },
        )

    def tearDown(self):
        # Clean up cache after each test
        cache.clear()

    @patch("api.playbook_sync.sync_list_task.delay")
    def test_sync_manager_dispatches_task(self, mock_sync_task):
        # Test that sync manager correctly dispatches tasks
        manager = PlaybookSyncerManager(self.playbook)
        manager.sync()

        mock_sync_task.assert_called_once_with(
            playbook_id=self.playbook.id,
            target_info_group_id=self.target_info_group.id,
            campaign_ids=[self.campaign.id],
            list_id="test_list_id",
            source="hubspot",
        )

    def test_sync_manager_respects_duration(self):
        # Test that sync is skipped if duration hasn't elapsed
        self.target_info_group.meta["autopilot_last_synced_at"] = datetime.now(
            timezone.utc
        ).isoformat()
        self.target_info_group.save()

        manager = PlaybookSyncerManager(self.playbook)
        lists_to_sync = list(manager.get_lists_to_sync())

        self.assertEqual(len(lists_to_sync), 0)

    @patch("api.playbook_sync.PlaybookHubspotSyncer")
    def test_sync_list_task_hubspot(self, mock_hubspot_syncer):
        # Test hubspot sync task
        mock_instance = MagicMock()
        mock_hubspot_syncer.return_value = mock_instance

        sync_list_task(
            self.playbook.id,
            self.target_info_group.id,
            [self.campaign.id],
            "test_list_id",
            "hubspot",
        )

        mock_hubspot_syncer.assert_called_once()
        mock_instance.sync.assert_called_once()

    def test_sync_list_task_lock_mechanism(self):
        # Test that lock prevents concurrent syncs
        lock_key = f"autopilot:list_lock:target_info_group:{self.target_info_group.id}"
        cache.add(lock_key, True, timeout=3600)

        sync_list_task(
            self.playbook.id,
            self.target_info_group.id,
            [self.campaign.id],
            "test_list_id",
            "hubspot",
        )

        # Verify that the sync was skipped due to lock
        self.assertTrue(cache.get(lock_key))


class TestPlaybookSyncError(TestCase):
    def setUp(self):
        # Create CompanyInfo first
        self.company_info = CompanyInfo.objects.create()
        # Then create Playbook with company_object
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company_info
        )

    def test_error_logging_deduplication(self):
        manager = PlaybookSyncerManager(self.playbook)
        error_key = "autopilot:error:test_key"
        error_message = "Test error message"

        # First error should be logged
        self.assertTrue(manager._should_log_error(error_key, error_message))

        # Same error within 24h should not be logged
        self.assertFalse(manager._should_log_error(error_key, error_message))

        # Different error message should be logged
        self.assertTrue(manager._should_log_error(error_key, "Different error"))
