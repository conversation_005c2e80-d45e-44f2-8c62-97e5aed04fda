import unittest
from unittest.mock import Mock, patch

from ...paragon_wrapper import SalesforceAgent


class TestSalesforceAgent(unittest.TestCase):
    def setUp(self):
        # First set up the AWS mock before creating the agent
        self.aws_patcher = patch("boto3.client")
        self.mock_aws = self.aws_patcher.start()
        self.mock_secrets = Mock()
        self.mock_secrets.get_secret_value.return_value = {
            "SecretString": '{"sf_token": "fake-token", "sf_instance_url": "https://test.salesforce.com", "paragon_token": "fake-paragon-token"}'
        }
        self.mock_aws.return_value = self.mock_secrets

        # Then set up the agent
        self.project_id = "test_project"
        self.playbook = Mock()
        self.agent = SalesforceAgent(self.project_id, self.playbook)
        self.agent.paragon_token = "fake-paragon-token"
        self.agent.common_headers = {
            "LinkedIn-Version": "202402",
            "X-Restli-Protocol-Version": "2.0.0",
        }

    def tearDown(self):
        self.aws_patcher.stop()

    @patch("requests.Session.send")
    @patch("requests.Request")
    def test_fetch_records_by_ids(self, mock_request, mock_send):
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "output": {
                "records": [
                    {
                        "Id": "001",
                        "Name": "Test Account",
                        "Account": {"Type": "Customer"},
                    }
                ]
            }
        }
        mock_send.return_value = mock_response

        # Test parameters
        ids = ["001"]
        record_type = "Account"
        properties = ["Id", "Name", "Account.Type"]

        # Execute
        result = self.agent.fetch_records_by_ids(ids, record_type, properties)

        # Assert
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["Id"], "001")
        self.assertEqual(result[0]["Name"], "Test Account")
        self.assertEqual(result[0]["Account.Type"], "Customer")

    @patch("requests.Session.send")
    @patch("requests.Request")
    def test_update_object_fields(self, mock_request, mock_send):
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 204  # Successful update status
        mock_send.return_value = mock_response

        # Test parameters
        salesforce_ids = {"object_type": "Account", "Id": "001"}
        data_to_push = {"Name": "Updated Account", "Description": "Test Description"}

        # Execute
        result = self.agent.update_object_fields(salesforce_ids, data_to_push)

        # Assert
        self.assertTrue(mock_send.called)
        self.assertEqual(mock_response.status_code, 204)
        mock_request.assert_called_once()

    def test_update_object_fields_invalid_ids(self):
        # Test invalid salesforce_ids
        invalid_ids = {"wrong_key": "value"}
        data_to_push = {"Name": "Test"}

        # Execute and Assert
        with self.assertRaises(Exception):
            self.agent.update_object_fields(invalid_ids, data_to_push)

    def test_sanitize_soql_input(self):
        # Test cases
        test_cases = [
            ("Normal'Input", "Normal''Input"),  # Single quote
            ("Input;With;Semicolons", "InputWithSemicolons"),  # Semicolons
            ("Normal Input", "Normal Input"),  # No special chars
            ("'Multiple'Quotes'", "''Multiple''Quotes''"),  # Multiple quotes
        ]

        # Execute and Assert
        for input_str, expected in test_cases:
            result = self.agent.sanitize_soql_input(input_str)
            self.assertEqual(result, expected)

    @patch("requests.Session.send")
    @patch("requests.Request")
    def test_create_salesforce_html_fields(self, mock_request, mock_send):
        # Mock responses
        mock_missing_fields_response = Mock()
        mock_missing_fields_response.status_code = 200
        mock_missing_fields_response.json.return_value = {
            "output": {"records": []}  # All fields are missing
        }

        mock_create_field_response = Mock()
        mock_create_field_response.status_code = 200
        mock_create_field_response.json.return_value = {"success": True}

        mock_profiles_response = Mock()
        mock_profiles_response.status_code = 200
        mock_profiles_response.json.return_value = {
            "output": {"records": [{"Id": "prof1", "Name": "Standard User"}]}
        }

        mock_security_response = Mock()
        mock_security_response.status_code = 200
        mock_security_response.json.return_value = {"success": True}

        # Configure mocks to return different responses
        mock_send.side_effect = [
            mock_missing_fields_response,
            mock_create_field_response,
            mock_profiles_response,
            mock_security_response,
        ]

        # Test parameters
        object_type = "Account"
        field_names = ["Custom_Field__c"]

        # Execute
        result = self.agent.create_salesforce_html_fields(object_type, field_names)

        # Assert
        self.assertIn("createdFields", result)
        self.assertIn("securityResults", result)
        self.assertIn("missingFieldsInitially", result)
        self.assertEqual(len(result["missingFieldsInitially"]), 1)


if __name__ == "__main__":
    unittest.main()
