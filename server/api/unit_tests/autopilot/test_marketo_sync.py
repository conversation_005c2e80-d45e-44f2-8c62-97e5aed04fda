from unittest.mock import Mock, patch

import pytest
from django.test import TestCase

from ...models import CompanyInfo, Playbook, TargetInfo, TargetInfoGroup
from ...sync.e2e.marketo_sync import PlaybookMarketoSyncer


@pytest.mark.django_db
class TestPlaybookMarketoSyncer(TestCase):
    def setUp(self):
        # Create test data
        self.company_info = CompanyInfo.objects.create()
        self.playbook = Playbook.objects.create(
            name="Test Playbook", settings={}, company_object=self.company_info
        )

        # Update the table_data through meta field
        self.table_data = [
            {"columnName": "company", "dataType": "subtarget_name"},
            {"columnName": "id", "dataType": "marketo_identifier"},
            {"columnName": "website", "dataType": "url"},
            {"columnName": "description", "dataType": "text"},
        ]

        self.target_info_group = TargetInfoGroup.objects.create(
            playbook=self.playbook,
            target_info_group_key="Test Group",
            meta={
                "type": "Company",
                "importListSettings": {"type": "static", "tableData": self.table_data},
            },
        )

        self.list_id = "test_list_123"
        self.campaigns = []

    @patch("api.sync.e2e.marketo_sync.ParagonWrapper")
    def test_get_platform_records(self, mock_paragon):
        # Setup mock returns
        mock_marketo = Mock()
        mock_marketo.get_list_records.return_value = [
            {"id": "1", "company": "Company A", "website": "http://a.com"},
            {"id": "2", "company": "Company B", "website": "http://b.com"},
        ]

        mock_paragon.return_value.get_marketo_agent.return_value = mock_marketo

        syncer = PlaybookMarketoSyncer(
            self.playbook, self.target_info_group, self.campaigns, self.list_id
        )

        # Execute
        with patch("api.sync.e2e.marketo_sync.math.isnan") as mock_isnan:
            mock_isnan.return_value = False
            result = syncer.integration._get_platform_records()

        # Assert using set comparison since order doesn't matter
        assert set(result) == {"1", "2"}
        mock_marketo.get_list_records.assert_called_once_with(
            self.list_id, "static", ["company", "id", "website", "description"]
        )

    @patch("api.sync.e2e.marketo_sync.ParagonWrapper")
    def test_batch_create_and_build_targets(self, mock_paragon):
        # Setup test data
        batch_record_data = [
            {
                "id": "1",
                "company": "Company A",
                "website": "http://a.com",
                "description": "Test company A",
            }
        ]

        syncer = PlaybookMarketoSyncer(
            self.playbook, self.target_info_group, self.campaigns, self.list_id
        )

        # Execute
        with patch(
            "api.playbook_build.target_info_group_wrapper.TargetInfoGroupWrapper.bulk_create"
        ) as mock_bulk_create:
            mock_target = Mock(
                target_key="Company A",
                meta={"marketo": {"lead_ids": ["1"]}},
                docs={"doc1": {}, "doc2": {}},
            )
            mock_bulk_create.return_value = [mock_target]
            result = syncer._batch_create_and_build_targets(batch_record_data)

        # Assert the mock was called with correct data
        mock_bulk_create.assert_called_once()
        call_args = mock_bulk_create.call_args[0][0]
        assert "Company A" in call_args
        target_data = call_args["Company A"]
        assert target_data["meta"]["marketo"]["lead_ids"] == ["1"]
        # The code creates a doc for both website and description fields
        assert len(target_data["docs"]) == 2
        # Verify the docs contain the expected fields
        doc_fields = {doc["meta"]["field_name"] for doc in target_data["docs"].values()}
        assert doc_fields == {"website", "description"}

    @patch("api.sync.e2e.marketo_sync.ParagonWrapper")
    def test_handle_duplicate_company_records(self, mock_paragon):
        # Setup test data with duplicate company entries
        batch_record_data = [
            {"id": "1", "company": "Company A", "website": "http://a.com"},
            {"id": "2", "company": "Company A", "website": "http://a2.com"},
        ]

        syncer = PlaybookMarketoSyncer(
            self.playbook, self.target_info_group, self.campaigns, self.list_id
        )

        # Execute
        with patch(
            "api.playbook_build.target_info_group_wrapper.TargetInfoGroupWrapper.bulk_create"
        ) as mock_bulk_create:
            mock_target = Mock(
                target_key="Company A",
                meta={"marketo": {"lead_ids": ["1", "2"]}},
                docs={"doc1": {}},
            )
            mock_bulk_create.return_value = [mock_target]
            result = syncer._batch_create_and_build_targets(batch_record_data)

        # Assert the mock was called with correct data
        mock_bulk_create.assert_called_once()
        call_args = mock_bulk_create.call_args[0][0]
        assert "Company A" in call_args
        target_data = call_args["Company A"]
        assert set(target_data["meta"]["marketo"]["lead_ids"]) == {"1", "2"}
        # The code creates a doc for the website field only
        assert len(target_data["docs"]) == 1
        # Verify the doc contains the website field
        doc = list(target_data["docs"].values())[0]
        assert doc["meta"]["field_name"] == "website"
