from unittest import TestCase
from unittest.mock import Mock, patch

from ...models import ContentVariation, PublicContent, TargetInfo
from ...sync.content_sync.content_salesforce_sync import ContentSalesforceSyncer


class TestContentSalesforceSyncer(TestCase):
    def setUp(self):
        self.mock_content = Mock()
        self.mock_content.id = 123
        self.mock_content.content_params = {"targets": {"group1": "target1"}}
        self.syncer = ContentSalesforceSyncer(self.mock_content)

    def test_fetch_salesforce_id(self):
        # Mock target_info with salesforce ID
        mock_target = Mock()
        mock_target.meta = {"salesforce": {"Id": "SF123"}}

        with patch.object(
            ContentSalesforceSyncer, "get_target_info", return_value=mock_target
        ):
            result = self.syncer.fetch_salesforce_id()
            self.assertEqual(result, "SF123")

    def test_fetch_salesforce_id_no_target(self):
        with patch.object(
            ContentSalesforceSyncer, "get_target_info", return_value=None
        ):
            result = self.syncer.fetch_salesforce_id()
            self.assertIsNone(result)

    def test_get_target_keys(self):
        result = self.syncer.get_target_keys()
        self.assertEqual(result, {"group1": "target1"})

    def test_get_full_url_preview(self):
        test_cases = [
            {
                "export_type": "static",
                "destination": "marketo",
                "target_slug": "test-slug",
                "group_slug": "group1",
                "domain": "example.com",
                "tofu_content_id": "123",
                "expected": "https://example.com/group1/test-slug.html",
            },
            {
                "export_type": "dynamic",
                "destination": "other",
                "target_slug": "test-slug",
                "group_slug": "",
                "domain": "example.com",
                "tofu_content_id": "123",
                "expected": "https://example.com?tofu_content_id=123&tofu_slug=test-slug",
            },
        ]

        for case in test_cases:
            result = ContentSalesforceSyncer.get_full_url_preview(
                case["export_type"],
                case["destination"],
                case["target_slug"],
                case["group_slug"],
                case["domain"],
                case["tofu_content_id"],
            )
            self.assertEqual(result, case["expected"])

    def test_fetch_components_results(self):
        mock_variation = Mock()
        mock_variation.variations = {
            "comp1": {"meta": {"current_version": {"text": "Test content 1"}}},
            "comp2": {"meta": {"current_version": {"text": "Test content 2"}}},
        }

        with patch.object(ContentVariation.objects, "get", return_value=mock_variation):
            result = self.syncer.fetch_components_results(["comp1", "comp2"])

            expected = {
                "comp1": {"text": "Test content 1"},
                "comp2": {"text": "Test content 2"},
            }
            self.assertEqual(result, expected)

    def test_fetch_components_results_missing_component(self):
        mock_variation = Mock()
        mock_variation.variations = {
            "comp1": {"meta": {"current_version": {"text": "Test content 1"}}}
        }

        with patch.object(ContentVariation.objects, "get", return_value=mock_variation):
            result = self.syncer.fetch_components_results(["comp1", "comp2"])

            expected = {"comp1": {"text": "Test content 1"}}
            self.assertEqual(result, expected)
