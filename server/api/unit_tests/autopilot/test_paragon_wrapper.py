from unittest.mock import MagicMock, patch

import pytest
from api.auth import gen_paragon_token
from api.paragon_wrapper import BaseParagonAgent


class DummyUser:
    def __init__(self, username):
        self.username = username


class DummyPlaybook:
    def __init__(self, company_domain=None):
        self.company_domain = company_domain
        self.users = MagicMock()
        self.users.first.return_value = MagicMock()


def fake_get_paragon_secret():
    return "dummy_secret"


@patch("api.auth.get_paragon_secret", side_effect=fake_get_paragon_secret)
@patch(
    "api.auth.jwt.encode",
    side_effect=lambda payload, secret, algorithm: f"jwt_for_{payload['sub']}",
)
def test_gen_paragon_token_with_company_domain(mock_jwt_encode, mock_get_secret):
    user = DummyUser("<EMAIL>")
    playbook = DummyPlaybook(company_domain="company.com")
    token, subject = gen_paragon_token(user, playbook)
    assert subject == "company.com"
    assert token == "jwt_for_company.com"


@patch("api.auth.get_paragon_secret", side_effect=fake_get_paragon_secret)
@patch(
    "api.auth.jwt.encode",
    side_effect=lambda payload, secret, algorithm: f"jwt_for_{payload['sub']}",
)
def test_gen_paragon_token_without_company_domain(mock_jwt_encode, mock_get_secret):
    user = DummyUser("<EMAIL>")
    playbook = DummyPlaybook(company_domain=None)
    token, subject = gen_paragon_token(user, playbook)
    assert subject == "<EMAIL>"
    assert token == "<EMAIL>"


@patch("api.auth.get_paragon_secret", side_effect=fake_get_paragon_secret)
@patch(
    "api.auth.jwt.encode",
    side_effect=lambda payload, secret, algorithm: f"jwt_for_{payload['sub']}",
)
def test_gen_paragon_token_tofuadmin(mock_jwt_encode, mock_get_secret):
    user = DummyUser("tofuadmin-test")
    playbook = DummyPlaybook(company_domain="tofuadmin-test.com")
    token, subject = gen_paragon_token(user, playbook)
    assert subject == "tofuadmin"
    assert token == "jwt_for_tofuadmin"


@patch.object(BaseParagonAgent, "gen_paragon_token", return_value=("token", "subject"))
@patch("time.sleep", return_value=None)
def test_get_request_no_retry_on_non_retryable_status(mock_gen_token, mock_sleep):
    agent = BaseParagonAgent("project_id", DummyPlaybook())
    mock_response = MagicMock()
    mock_response.status_code = 418  # Not in RETRIABLE_STATUS_CODES
    with patch("requests.get") as mock_get:
        mock_get.return_value = mock_response
        response = agent.get_request("http://fake-url", enable_retry=True)
        assert response.status_code == 418
        # Should only call once
        assert mock_get.call_count == 1


@patch.object(BaseParagonAgent, "gen_paragon_token", return_value=("token", "subject"))
@patch("time.sleep", return_value=None)
def test_get_request_no_retry_when_disabled(mock_gen_token, mock_sleep):
    agent = BaseParagonAgent("project_id", DummyPlaybook())
    mock_response = MagicMock()
    mock_response.status_code = 606  # Would be retryable, but retry is disabled
    with patch("requests.get") as mock_get:
        mock_get.return_value = mock_response
        response = agent.get_request("http://fake-url", enable_retry=False)
        assert response.status_code == 606
        mock_get.assert_called_once()


@patch.object(BaseParagonAgent, "gen_paragon_token", return_value=("token", "subject"))
@patch("time.sleep", return_value=None)
def test_get_request_retries_on_429(mock_sleep, mock_gen_token):
    agent = BaseParagonAgent("project_id", DummyPlaybook())
    mock_response_429 = MagicMock()
    mock_response_429.status_code = 429
    mock_response_ok = MagicMock()
    mock_response_ok.status_code = 200
    with patch("requests.get") as mock_get:
        mock_get.side_effect = [mock_response_429, mock_response_ok]
        response = agent.get_request("http://fake-url", enable_retry=True)
        assert response.status_code == 200
        assert mock_get.call_count == 2


@patch.object(BaseParagonAgent, "gen_paragon_token", return_value=("token", "subject"))
@patch("time.sleep", return_value=None)
def test_get_request_retries_on_606(mock_sleep, mock_gen_token):
    agent = BaseParagonAgent("project_id", DummyPlaybook())
    # Simulate Marketo 606 error: status 200 but error_code '606' in JSON
    mock_response_606 = MagicMock()
    mock_response_606.status_code = 200
    mock_response_606.json.return_value = {"errors": [{"code": "606"}]}
    mock_response_ok = MagicMock()
    mock_response_ok.status_code = 200
    mock_response_ok.json.return_value = {}
    with patch("requests.get") as mock_get:
        mock_get.side_effect = [mock_response_606, mock_response_ok]
        response = agent.get_request("http://fake-url", enable_retry=True)
        assert response.status_code == 200
        assert mock_get.call_count == 2
