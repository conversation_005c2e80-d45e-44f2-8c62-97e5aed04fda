from django.contrib.auth import get_user_model
from django.test import TestCase

from ..models import CompanyDomain, CompanyInfo, Playbook, PlaybookUser


class TofuUserManagerTest(TestCase):
    def setUp(self):
        self.User = get_user_model()
        self.company_domain = "example.com"
        CompanyDomain.objects.create(
            allow_register_domain=self.company_domain,
            link_playbook_domain=self.company_domain,
        )

    def test_create_user(self):
        # Test creating a regular user
        user = self.User.objects.create_user(
            username="<EMAIL>", password="testpass123"
        )

        self.assertIsNotNone(user)
        self.assertEqual(user.username, "<EMAIL>")
        self.assertTrue(user.check_password("testpass123"))
        self.assertFalse(user.is_superuser)
        self.assertFalse(user.is_staff)
        self.assertTrue(user.is_active)

        # Check context
        self.assertTrue(user.context["verified_user"])
        self.assertFalse(user.context["internalFeatures"])
        self.assertEqual(
            user.context["model"], "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        )
        self.assertEqual(
            user.context["model_for_repurpose"],
            "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        )

        # Check if playbook was created
        playbook = Playbook.objects.filter(company_domain=self.company_domain).first()
        self.assertIsNotNone(playbook)

        # Check if PlaybookUser was created
        playbook_user = PlaybookUser.objects.filter(
            user=user, playbook=playbook
        ).first()
        self.assertIsNotNone(playbook_user)
        self.assertEqual(playbook_user.type, "creator")

    def test_create_user_tofuadmin(self):
        # Test creating a tofuadmin user
        user = self.User.objects.create_user(
            username="tofuadmin-testuser", password="testpass123"
        )

        self.assertIsNotNone(user)
        self.assertEqual(user.username, "tofuadmin-testuser")
        self.assertTrue(user.check_password("testpass123"))
        self.assertFalse(user.is_superuser)
        self.assertFalse(user.is_staff)
        self.assertTrue(user.is_active)

        self.assertTrue(user.context["verified_user"])
        self.assertTrue(user.context["internalFeatures"])
        self.assertEqual(
            user.context["model"], "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        )
        self.assertEqual(
            user.context["model_for_repurpose"],
            "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        )

    def test_create_user_with_existing_playbook(self):
        # Create a playbook first
        company_info = CompanyInfo.objects.create()
        existing_playbook = Playbook.objects.create(
            company_domain=self.company_domain, company_object=company_info
        )

        # Create a user with the same company domain
        user = self.User.objects.create_user(
            username="<EMAIL>", password="testpass123"
        )

        # Check if the user is linked to the existing playbook
        playbook_user = PlaybookUser.objects.filter(
            user=user, playbook=existing_playbook
        ).first()
        self.assertIsNotNone(playbook_user)
        self.assertEqual(playbook_user.type, "user")

    def test_create_user_invalid_username(self):
        with self.assertRaises(ValueError):
            self.User.objects.create_user(username="")

    def test_create_tofuadmin_user(self):
        user = self.User.objects.create_user(
            username="<EMAIL>", password="testpass123"
        )

        self.assertTrue(user.context["verified_user"])
        self.assertTrue(user.context["internalFeatures"])

    def test_create_user_unregistered_domain(self):
        user = self.User.objects.create_user(
            username="<EMAIL>", password="testpass123"
        )

        self.assertFalse(user.context["verified_user"])
        self.assertFalse(user.context["internalFeatures"])
