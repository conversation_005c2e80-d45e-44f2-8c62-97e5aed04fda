import os
from unittest.mock import patch

from api.models import CompanyInfo, Playbook, TofuUser
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient


class TestTofuPagesKillSwitch(TestCase):
    def setUp(self):
        # Create test users
        self.lite_user = TofuUser.objects.create_user(
            username="lite_user",
            password="testpass123",
            customer_type=TofuUser.CustomerType.LITE,
        )
        self.enterprise_user = TofuUser.objects.create_user(
            username="enterprise_user",
            password="testpass123",
            customer_type=TofuUser.CustomerType.NORMAL,
        )
        self.superuser = TofuUser.objects.create_superuser(
            username="admin_user", password="testpass123", email="<EMAIL>"
        )
        self.client = APIClient()

        # Create CompanyInfo first
        self.company_info = CompanyInfo.objects.create()

        # Create test playbook
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company_info
        )

    def test_list_playbooks_normal_user_kill_switch_disabled(self):
        """Test that normal users get 503 when kill switch is disabled"""
        self.client.force_authenticate(user=self.lite_user)
        url = reverse("playbook-list")

        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "true"}):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_503_SERVICE_UNAVAILABLE)
            self.assertEqual(
                response.json(), {"detail": "Tofu pages are currently disabled"}
            )

    def test_list_playbooks_normal_user_kill_switch_enabled(self):
        """Test that normal users can access when kill switch is enabled"""
        self.client.force_authenticate(user=self.lite_user)
        url = reverse("playbook-list")

        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "false"}):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_list_playbooks_enterprise_user_kill_switch_disabled(self):
        """Test that enterprise users can still access when kill switch is disabled"""
        self.client.force_authenticate(user=self.enterprise_user)
        url = reverse("playbook-list")

        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "False"}):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_list_playbooks_unauthenticated_kill_switch_disabled(self):
        """Test that unauthenticated users get auth error before kill switch"""
        url = reverse("playbook-list")

        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "True"}):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
