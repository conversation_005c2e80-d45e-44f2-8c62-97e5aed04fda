from api.api_views.pagination.pagination_mixin import PaginatedListMixin
from api.models import CompanyInfo, Playbook, TargetInfo, TargetInfoGroup, TofuUser
from django.test import TestCase
from rest_framework import serializers
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.request import Request
from rest_framework.test import APIRequestFactory


class TargetInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = TargetInfo
        fields = ["id", "target_key", "value_prop"]


class PaginatedListMixinTestCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create required related objects
        company_info = CompanyInfo.objects.create()
        user = TofuUser.objects.create(username="testuser")
        playbook = Playbook.objects.create(company_object=company_info)
        group = TargetInfoGroup.objects.create(
            playbook=playbook, target_info_group_key="group1"
        )
        cls.group = group
        # Create 30 TargetInfo objects
        for i in range(1, 31):
            TargetInfo.objects.create(
                target_info_group=group, target_key=f"target{i}", value_prop=f"value{i}"
            )

    def setUp(self):
        self.factory = APIRequestFactory()
        self.mixer = PaginatedListMixin()

    def test_get_pagination_params_valid(self):
        request = self.factory.get("/dummy", {"page": 2, "page_size": 10})
        drf_request = Request(request)
        page, page_size = self.mixer.get_pagination_params(drf_request)
        self.assertEqual(page, 2)
        self.assertEqual(page_size, 10)

    def test_get_pagination_params_invalid(self):
        request = self.factory.get("/dummy", {"page": "abc", "page_size": 10})
        drf_request = Request(request)
        with self.assertRaises(ValidationError):
            self.mixer.get_pagination_params(drf_request)

    def test_paginate_queryset_valid(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        page_obj = self.mixer.paginate_queryset(queryset, 2, 10)
        paginator = page_obj.paginator
        self.assertEqual(page_obj.number, 2)
        self.assertEqual(len(page_obj.object_list), 10)
        self.assertEqual(paginator.num_pages, 3)

    def test_paginate_queryset_out_of_range(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        with self.assertRaises(NotFound):
            self.mixer.paginate_queryset(queryset, 5, 10)

    def test_get_paginated_response(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        page_obj = self.mixer.paginate_queryset(queryset, 1, 10)
        paginator = page_obj.paginator
        serializer = TargetInfoSerializer(page_obj, many=True)
        response = self.mixer.get_paginated_response(serializer, page_obj)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["page"], 1)
        self.assertEqual(response.data["total_pages"], 3)
        self.assertEqual(response.data["total_items"], 30)
        self.assertEqual(len(response.data["results"]), 10)

    def test_apply_ordering_valid(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        ordered = self.mixer.apply_ordering(
            queryset, "target_key", "desc", ["id", "target_key", "value_prop"]
        )
        values = list(ordered.values_list("target_key", flat=True))
        self.assertEqual(values, sorted(values, reverse=True))

    def test_apply_ordering_invalid(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        with self.assertRaises(ValidationError):
            self.mixer.apply_ordering(
                queryset, "notafield", "asc", ["id", "target_key", "value_prop"]
            )

    def test_paginate_queryset_no_page(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        # page is None, page_size is valid
        page_obj = self.mixer.paginate_queryset(queryset, None, 10)
        self.assertIsNone(page_obj)

    def test_paginate_queryset_no_page_size(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        # page_size is None, page is valid
        page_obj = self.mixer.paginate_queryset(queryset, 1, None)
        self.assertIsNone(page_obj)

    def test_paginate_queryset_both_none(self):
        queryset = TargetInfo.objects.filter(target_info_group=self.group)
        # both page and page_size are None
        page_obj = self.mixer.paginate_queryset(queryset, None, None)
        self.assertIsNone(page_obj)
