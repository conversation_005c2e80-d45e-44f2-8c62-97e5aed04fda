import os
from unittest.mock import Mock, patch

from api.models import CompanyInfo, Playbook, PlaybookUser, TofuUser
from django.core.cache import cache
from django.test import RequestFactory, TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient


class TestTofuLiteRateLimit(TestCase):
    def setUp(self):
        # Create test users
        self.lite_user = TofuUser.objects.create_user(
            username="lite_user",
            password="testpass123",
            customer_type=TofuUser.CustomerType.LITE,
        )
        self.enterprise_user = TofuUser.objects.create_user(
            username="enterprise_user",
            password="testpass123",
            customer_type=TofuUser.CustomerType.ADMIN,
        )
        self.superuser = TofuUser.objects.create_superuser(
            username="admin_user", password="testpass123", email="<EMAIL>"
        )
        self.client = APIClient()
        self.factory = RequestFactory()

        # Create CompanyInfo first
        self.company_info = CompanyInfo.objects.create()

        # Create test playbook
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company_info
        )

        # Create playbook user relationship
        PlaybookUser.objects.create(
            user=self.lite_user, playbook=self.playbook, type="user"
        )

        # Clear cache before tests
        cache.clear()

    def tearDown(self):
        # Clear cache after tests
        cache.clear()

    def test_rate_limit_lite_user(self):
        """Test that Lite users are rate limited"""

        self.client.force_authenticate(user=self.lite_user)
        url = reverse("playbook-list")

        # Enable tofu pages to avoid kill switch
        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "false"}):
            # Make requests up to the limit (20 per minute)
            for _ in range(10):
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)

            for _ in range(15):
                response = self.client.get(url)

            # The request should be rate limited
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_no_rate_limit_enterprise_user(self):
        """Test that Enterprise users are not rate limited"""

        self.client.force_authenticate(user=self.enterprise_user)
        url = reverse("playbook-list")

        # Enable tofu pages to avoid kill switch
        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "false"}):
            # Make more requests than the lite user limit
            for _ in range(25):  # Reduced from 101 to speed up test
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_no_rate_limit_superuser(self):
        """Test that Superusers are not rate limited"""
        self.client.force_authenticate(user=self.superuser)
        url = reverse("playbook-list")

        # Enable tofu pages to avoid kill switch
        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "false"}):
            # Make more requests than the lite user limit
            for _ in range(25):  # Reduced from 101 to speed up test
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_unauthenticated_user(self):
        """Test that unauthenticated users get auth error before rate limit"""
        # Don't authenticate the client
        url = reverse("playbook-list")

        # Make multiple requests without authentication
        for _ in range(5):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_rate_limit_exception_handling(self):
        """Test that exceptions in the rate limiter itself are properly handled"""
        self.client.force_authenticate(user=self.lite_user)
        url = reverse("playbook-list")

        # Mock ratelimit function to raise an exception
        with (
            patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "false"}),
            patch(
                "django_ratelimit.decorators.ratelimit",
                side_effect=Exception("Rate limit error"),
            ),
        ):
            # The request should still go through despite the exception in the rate limiter
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_rate_limit_per_api_endpoint(self):
        """Test that rate limits are applied per API endpoint rather than globally"""
        self.client.force_authenticate(user=self.lite_user)

        # Two different endpoints
        playbook_url = reverse("playbook-list")
        # Assuming there's another endpoint we can use for testing
        playbook_get_url = reverse(
            "playbook-detail", kwargs={"playbook_id": self.playbook.id}
        )

        # Enable tofu pages to avoid kill switch
        with patch.dict(os.environ, {"DISABLE_TOFU_PAGES": "false"}):
            # Exhaust rate limit for the first endpoint
            for _ in range(35):
                response = self.client.get(playbook_url)

            # Verify rate limit is reached for the first endpoint
            response = self.client.get(playbook_url)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

            # But the second endpoint should still be accessible
            for _ in range(10):
                response = self.client.get(playbook_get_url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Exhaust rate limit for the second endpoint
            for _ in range(25):
                response = self.client.get(playbook_get_url)

            # After 25 requests, the second endpoint should also be rate limited
            response = self.client.get(playbook_get_url)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
