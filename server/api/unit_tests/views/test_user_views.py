import base64

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from ...models import TofuUser


class TestUserViewSet(TestCase):
    def setUp(self):
        self.client = APIClient()

        # Create test users
        self.admin_user = TofuUser.objects.create_superuser(
            username="admin", password="adminpass", full_name="Admin User"
        )

        self.normal_user = TofuUser.objects.create_user(
            username="normal", password="normalpass", full_name="Normal User"
        )

        self.another_user = TofuUser.objects.create_user(
            username="another", password="anotherpass", full_name="Another User"
        )

    def test_authentication_success(self):
        url = reverse("user-authentication")
        data = {"username": "normal", "password": "normalpass"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["username"], "normal")
        self.assertEqual(response.data["fullname"], "Normal User")
        self.assertTrue("user_id" in response.data)

    def test_authentication_invalid_credentials(self):
        url = reverse("user-authentication")
        data = {"username": "normal", "password": "wrongpass"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.data["message"], "Invalid credentials")

    def test_authentication_missing_fields(self):
        url = reverse("user-authentication")
        data = {"username": "normal"}  # Missing password
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_list_as_admin(self):
        self.client.force_authenticate(user=self.admin_user)
        url = reverse("user-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)  # Three users total

    def test_list_as_normal_user(self):
        self.client.force_authenticate(user=self.normal_user)
        url = reverse("user-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_own_profile(self):
        self.client.force_authenticate(user=self.normal_user)
        encoded_username = base64.b64encode(self.normal_user.username.encode()).decode()
        url = reverse("user-detail", kwargs={"base64_username": encoded_username})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["username"], "normal")

    def test_retrieve_other_profile_as_normal_user(self):
        self.client.force_authenticate(user=self.normal_user)
        encoded_username = base64.b64encode(
            self.another_user.username.encode()
        ).decode()
        url = reverse("user-detail", kwargs={"base64_username": encoded_username})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_other_profile_as_admin(self):
        self.client.force_authenticate(user=self.admin_user)
        encoded_username = base64.b64encode(self.normal_user.username.encode()).decode()
        url = reverse("user-detail", kwargs={"base64_username": encoded_username})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["username"], "normal")

    def test_set_password_own_account(self):
        self.client.force_authenticate(user=self.normal_user)
        encoded_username = base64.b64encode(self.normal_user.username.encode()).decode()
        url = reverse("user-set-password", kwargs={"base64_username": encoded_username})
        data = {"new_password": "newpassword123"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify new password works
        success = self.client.login(username="normal", password="newpassword123")
        self.assertTrue(success)

    def test_set_password_other_account(self):
        self.client.force_authenticate(user=self.normal_user)
        encoded_username = base64.b64encode(
            self.another_user.username.encode()
        ).decode()
        url = reverse("user-set-password", kwargs={"base64_username": encoded_username})
        data = {"new_password": "newpassword123"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_set_password_as_admin(self):
        self.client.force_authenticate(user=self.admin_user)
        encoded_username = base64.b64encode(self.normal_user.username.encode()).decode()
        url = reverse("user-set-password", kwargs={"base64_username": encoded_username})
        data = {"new_password": "newpassword123"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify new password works
        success = self.client.login(username="normal", password="newpassword123")
        self.assertTrue(success)

    def test_delete_user_as_admin(self):
        self.client.force_authenticate(user=self.admin_user)
        encoded_username = base64.b64encode(self.normal_user.username.encode()).decode()
        url = reverse("user-detail", kwargs={"base64_username": encoded_username})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(TofuUser.objects.filter(username="normal").exists())

    def test_delete_user_as_normal_user(self):
        self.client.force_authenticate(user=self.normal_user)
        encoded_username = base64.b64encode(
            self.another_user.username.encode()
        ).decode()
        url = reverse("user-detail", kwargs={"base64_username": encoded_username})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertTrue(TofuUser.objects.filter(username="another").exists())
