from unittest.mock import MagicMock, patch

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from ...models import (
    Campaign,
    CompanyInfo,
    ContentGroup,
    ContentTemplate,
    Playbook,
    PlaybookUser,
    TofuUser,
)


class PlaybookViewSetTests(TestCase):
    def setUp(self):
        # Create test users
        self.admin_user = TofuUser.objects.create_superuser(
            username="admin", email="<EMAIL>", password="testpass123"
        )
        self.normal_user = TofuUser.objects.create_user(
            username="normal", email="<EMAIL>", password="testpass123"
        )

        # Create CompanyInfo first
        self.company_info = CompanyInfo.objects.create()

        # Create test playbook with company_object
        self.playbook = Playbook.objects.create(
            name="Test Playbook", company_object=self.company_info
        )

        # Create playbook user relationship - only create one relationship
        self.playbook_user = PlaybookUser.objects.create(
            playbook=self.playbook, user=self.normal_user, type="creator"
        )

        # Remove any other playbook relationships that might have been created automatically
        PlaybookUser.objects.filter(user=self.normal_user).exclude(
            playbook=self.playbook
        ).delete()

        self.client = APIClient()

    def test_list_playbooks_authenticated(self):
        """Test listing playbooks for authenticated user"""
        self.client.force_authenticate(user=self.normal_user)
        url = reverse("playbook-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 1
        )  # Should only see playbooks user has access to
        self.assertEqual(response.data[0]["name"], self.playbook.name)

    def test_list_playbooks_unauthenticated(self):
        """Test listing playbooks fails for unauthenticated user"""
        self.client.force_authenticate(user=None)  # Explicitly remove authentication
        url = reverse("playbook-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_playbook_creator(self):
        """Test retrieving playbook as creator"""
        self.client.force_authenticate(user=self.normal_user)
        url = reverse("playbook-detail", kwargs={"playbook_id": self.playbook.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], self.playbook.name)

    def test_retrieve_playbook_unauthorized(self):
        """Test retrieving playbook fails for unauthorized user"""
        unauthorized_user = TofuUser.objects.create_user(
            username="unauthorized",
            email="<EMAIL>",
            password="testpass123",
        )
        self.client.force_authenticate(user=unauthorized_user)
        url = reverse("playbook-detail", kwargs={"playbook_id": self.playbook.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    @patch("api.api_views.playbook.PlaybookHandler")
    def test_copy_from_playbook(self, mock_playbook_handler):
        """Test copying from one playbook to another"""
        self.client.force_authenticate(user=self.normal_user)
        source_company_info = CompanyInfo.objects.create()
        source_playbook = Playbook.objects.create(
            name="Source Playbook", company_object=source_company_info
        )

        url = reverse(
            "playbook-copy-from-playbook", kwargs={"playbook_id": self.playbook.id}
        )
        data = {"source_playbook_id": source_playbook.id}

        mock_handler_instance = mock_playbook_handler.return_value
        mock_handler_instance.copy_from_playbook.return_value = None

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_handler_instance.copy_from_playbook.assert_called_once_with(
            source_playbook
        )

    @patch("api.api_views.playbook.PlaybookCampaignHandler")
    def test_copy_campaign(self, mock_campaign_handler):
        """Test copying campaign between playbooks"""
        self.client.force_authenticate(user=self.normal_user)
        source_campaign = Campaign.objects.create(
            playbook=self.playbook, campaign_name="Test Campaign"
        )

        url = reverse(
            "playbook-copy-campaign", kwargs={"playbook_id": self.playbook.id}
        )
        data = {"source_campaign_id": source_campaign.id, "copy_generations": True}

        mock_handler_instance = mock_campaign_handler.return_value
        mock_handler_instance.copy_campaign.return_value = source_campaign

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["campaign_name"], source_campaign.campaign_name)
        mock_handler_instance.copy_campaign.assert_called_once_with(
            source_campaign, True
        )

    def test_status_endpoint(self):
        """Test getting playbook status"""
        self.client.force_authenticate(user=self.normal_user)
        url = reverse("playbook-status", kwargs={"playbook_id": self.playbook.id})

        with patch("api.api_views.playbook.StatusHandler") as mock_status_handler:
            mock_status_handler.get_playbook_status_queryset.return_value = []
            response = self.client.get(url)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn("playbook_status", response.data)
            self.assertIn("target_status", response.data)
            self.assertIn("asset_status", response.data)
            self.assertIn("doc_status", response.data)
            self.assertIn("tone_status", response.data)

    @patch("api.api_views.playbook.async_refresh_playbook_context")
    def test_force_context_refresh(self, mock_refresh_task):
        """Test forcing context refresh"""
        self.client.force_authenticate(user=self.normal_user)
        url = reverse(
            "playbook-force-context-refresh", kwargs={"playbook_id": self.playbook.id}
        )
        data = {"column_ids": ["company_info", "target_info"]}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("task_id", response.data)
        mock_refresh_task.apply_async.assert_called_once()

    # @patch("api.views.PlaybookHandler")
    # def test_web_search(self, mock_playbook_handler):
    #     """Test web search functionality"""
    #     self.client.force_authenticate(user=self.normal_user)
    #     url = reverse("playbook-web-search", kwargs={"playbook_id": self.playbook.id})
    #     data = {"keyword": "test search", "num_links": 5, "include_sitelinks": False}

    #     mock_handler_instance = mock_playbook_handler.return_value
    #     mock_handler_instance.web_search.return_value = ["link1", "link2"]

    #     response = self.client.post(url, data, format="json")

    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     self.assertIn("links", response.data)
    #     mock_handler_instance.web_search.assert_called_once_with(
    #         keyword="test search", num_links=5, include_sitelinks=False
    #     )

    def test_save_content_template(self):
        """Test saving content as template"""
        self.client.force_authenticate(user=self.normal_user)
        campaign = Campaign.objects.create(
            playbook=self.playbook, campaign_name="Test Campaign"
        )
        content_group = ContentGroup.objects.create(
            creator=self.normal_user,
            campaign=campaign,
            content_group_name="Test Content Group",
        )

        url = reverse(
            "playbook-save-content-template", kwargs={"playbook_id": self.playbook.id}
        )
        data = {
            "content_group_id": content_group.id,
            "content_template_name": "Test Template",
            "save_repurpose_variations": True,
        }

        with patch(
            "api.api_views.playbook.ContentTemplateHandler"
        ) as mock_template_handler:
            mock_handler_instance = mock_template_handler.return_value
            mock_template = ContentTemplate(name="Test Template")
            mock_handler_instance.save_content_as_template.return_value = mock_template

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            mock_handler_instance.save_content_as_template.assert_called_once_with(
                content_group, "Test Template", save_repurpose_variations=True
            )
