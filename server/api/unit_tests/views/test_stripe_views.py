import base64
import json
import os
from unittest.mock import MagicMock, patch

import pytest
import stripe
from api.models import TofuUser
from api.views import PublicViewSet
from rest_framework import status
from rest_framework.test import APIClient, APITestCase


class TestStripeViews(APITestCase):
    def setUp(self):
        self.client = APIClient()
        self.view = PublicViewSet()
        self.test_user = TofuUser.objects.create(
            username="testuser",
            email="<EMAIL>",
            stripe_customer_id="cus_test123",
            stripe_checkout_session_id="cs_test123",
        )
        self.test_price_id = "price_test123"
        self.test_domain_url = "https://test.example.com"
        self.username_base64 = base64.b64encode(
            self.test_user.username.encode()
        ).decode()

    @patch("api.api_views.public.stripe.Webhook.construct_event")
    @patch("api.api_views.public.handle_stripe_event")
    def test_stripe_webhook_success(self, mock_handle_event, mock_construct_event):
        # Mock the webhook secret
        with patch.dict(os.environ, {"STRIPE_WEBHOOK_SECRET": "whsec_test123"}):
            # Create a mock event
            mock_event = MagicMock()
            mock_event.type = "payment_intent.succeeded"
            mock_construct_event.return_value = mock_event

            # Create request with mock data
            response = self.client.post(
                "/api/public/stripe/webhook/",
                data=json.dumps({"type": "payment_intent.succeeded"}),
                content_type="application/json",
                HTTP_STRIPE_SIGNATURE="test_signature",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data, {"success": True})
            mock_handle_event.assert_called_once_with(mock_event)

    @patch("api.api_views.public.stripe.Webhook.construct_event")
    @patch("api.api_views.public.handle_stripe_event")
    def test_stripe_webhook_payment_failed(
        self, mock_handle_event, mock_construct_event
    ):
        # Mock the webhook secret
        with patch.dict(os.environ, {"STRIPE_WEBHOOK_SECRET": "whsec_test123"}):
            # Create a mock event
            mock_event = MagicMock()
            mock_event.type = "payment_intent.payment_failed"
            mock_construct_event.return_value = mock_event

            # Create request with mock data
            response = self.client.post(
                "/api/public/stripe/webhook/",
                data=json.dumps({"type": "payment_intent.payment_failed"}),
                content_type="application/json",
                HTTP_STRIPE_SIGNATURE="test_signature",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data, {"success": True})
            mock_handle_event.assert_called_once_with(mock_event)

    @patch("api.api_views.public.stripe.Webhook.construct_event")
    def test_stripe_webhook_invalid_signature(self, mock_construct_event):
        # Mock the webhook secret
        with patch.dict(os.environ, {"STRIPE_WEBHOOK_SECRET": "whsec_test123"}):
            # Mock signature verification failure
            mock_construct_event.side_effect = stripe.error.SignatureVerificationError(
                "Invalid signature", "sig_header", "payload"
            )

            # Create request
            response = self.client.post(
                "/api/public/stripe/webhook/",
                data=json.dumps({"type": "payment_intent.succeeded"}),
                content_type="application/json",
                HTTP_STRIPE_SIGNATURE="invalid_signature",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.data, {"success": False})

    @patch("api.api_views.public.stripe.Webhook.construct_event")
    def test_stripe_webhook_invalid_json(self, mock_construct_event):
        # Mock the webhook secret
        with patch.dict(os.environ, {"STRIPE_WEBHOOK_SECRET": "whsec_test123"}):
            # Mock JSON decode error
            mock_construct_event.side_effect = json.JSONDecodeError(
                "Invalid JSON", "invalid json", 0
            )

            # Create request with invalid JSON
            response = self.client.post(
                "/api/public/stripe/webhook/",
                data="invalid json",
                content_type="application/json",
                HTTP_STRIPE_SIGNATURE="test_signature",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.data, {"success": False})

    def test_stripe_webhook_missing_secret(self):
        # Test without webhook secret
        with patch.dict(os.environ, {"STRIPE_WEBHOOK_SECRET": ""}):
            response = self.client.post(
                "/api/public/stripe/webhook/",
                data=json.dumps({"type": "payment_intent.succeeded"}),
                content_type="application/json",
                HTTP_STRIPE_SIGNATURE="test_signature",
            )

            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.data, {"success": False})

    @patch("api.api_views.public.stripe.checkout.Session.create")
    def test_stripe_checkout_success(self, mock_session_create):
        # Mock Stripe API key and domain URL
        with patch.dict(
            os.environ,
            {
                "STRIPE_API_KEY": "sk_test123",
                "STRIPE_DOMAIN_URL": self.test_domain_url,
            },
        ):
            # Mock checkout session creation
            mock_session = MagicMock()
            mock_session.id = "cs_test123"
            mock_session.url = "https://checkout.stripe.com/test"
            mock_session_create.return_value = mock_session

            # Create request data
            request_data = {
                "priceId": self.test_price_id,
                "tofuUserId": self.test_user.id,
                "mode": "subscription",
            }

            # Create request
            response = self.client.post(
                "/api/public/stripe/checkout/",
                data=request_data,
                format="json",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data, {"checkout_session_url": mock_session.url})

    @patch("api.api_views.public.stripe.checkout.Session.create")
    def test_stripe_checkout_one_time_payment(self, mock_session_create):
        # Mock Stripe API key and domain URL
        with patch.dict(
            os.environ,
            {
                "STRIPE_API_KEY": "sk_test123",
                "STRIPE_DOMAIN_URL": self.test_domain_url,
            },
        ):
            # Mock checkout session creation
            mock_session = MagicMock()
            mock_session.id = "cs_test123"
            mock_session.url = "https://checkout.stripe.com/test"
            mock_session_create.return_value = mock_session

            # Create request data for one-time payment
            request_data = {
                "priceId": self.test_price_id,
                "tofuUserId": self.test_user.id,
                "mode": "payment",
            }

            # Create request
            response = self.client.post(
                "/api/public/stripe/checkout/",
                data=request_data,
                format="json",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data, {"checkout_session_url": mock_session.url})

    @patch("api.api_views.public.stripe.checkout.Session.create")
    def test_stripe_checkout_session_creation_failure(self, mock_session_create):
        # Mock Stripe API key and domain URL
        with patch.dict(
            os.environ,
            {
                "STRIPE_API_KEY": "sk_test123",
                "STRIPE_DOMAIN_URL": self.test_domain_url,
            },
        ):
            # Mock checkout session creation failure
            mock_session_create.side_effect = Exception("API Error")

            # Create request data
            request_data = {
                "priceId": self.test_price_id,
                "tofuUserId": self.test_user.id,
                "mode": "subscription",
            }

            # Create request
            response = self.client.post(
                "/api/public/stripe/checkout/",
                data=request_data,
                format="json",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(
                response.data,
                {"error": {"message": "Error creating checkout session: API Error"}},
            )

    def test_stripe_checkout_missing_api_key(self):
        # Test without Stripe API key
        with patch.dict(os.environ, {"STRIPE_API_KEY": ""}):
            request_data = {
                "priceId": self.test_price_id,
                "tofuUserId": self.test_user.id,
                "mode": "subscription",
            }

            response = self.client.post(
                "/api/public/stripe/checkout/",
                data=request_data,
                format="json",
            )

            self.assertEqual(
                response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            self.assertEqual(
                response.data, {"error": {"message": "Stripe API key not configured"}}
            )

    def test_stripe_checkout_invalid_mode(self):
        # Test with invalid checkout mode
        request_data = {
            "priceId": self.test_price_id,
            "tofuUserId": self.test_user.id,
            "mode": "invalid_mode",
        }

        response = self.client.post(
            "/api/public/stripe/checkout/",
            data=request_data,
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data, {"error": {"message": "Invalid mode"}})

    def test_stripe_checkout_missing_price_id(self):
        # Test without price ID
        request_data = {
            "tofuUserId": self.test_user.id,
            "mode": "subscription",
        }

        response = self.client.post(
            "/api/public/stripe/checkout/",
            data=request_data,
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data, {"error": {"message": "Price ID is required"}})

    def test_stripe_checkout_missing_domain_url(self):
        # Test without domain URL
        with patch.dict(os.environ, {"STRIPE_DOMAIN_URL": ""}):
            request_data = {
                "priceId": self.test_price_id,
                "tofuUserId": self.test_user.id,
                "mode": "subscription",
            }

            response = self.client.post(
                "/api/public/stripe/checkout/",
                data=request_data,
                format="json",
            )

            self.assertEqual(
                response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            self.assertEqual(
                response.data,
                {"error": {"message": "Stripe domain URL not configured"}},
            )

    @patch("api.api_views.user.cancel_subscription")
    @patch("api.api_views.user.has_subscription")
    def test_stripe_cancel_subscription_success(
        self, mock_has_subscription, mock_cancel_subscription
    ):
        # Mock Stripe API key
        with patch.dict(os.environ, {"STRIPE_API_KEY": "sk_test123"}):
            # Mock has_subscription to return True
            mock_has_subscription.return_value = True

            # Create request data
            request_data = {"tofuUserId": self.test_user.id}

            # Authenticate the test client as the test user
            self.client.force_authenticate(user=self.test_user)

            # Create request
            response = self.client.post(
                f"/api/user/{self.username_base64}/cancel_stripe_subscription/",
                data=request_data,
                format="json",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(
                response.data, {"message": "Subscription cancelled successfully"}
            )

    @patch("api.api_views.user.cancel_subscription")
    @patch("api.api_views.user.has_subscription")
    def test_stripe_cancel_subscription_failure(
        self, mock_has_subscription, mock_cancel_subscription
    ):
        # Mock Stripe API key
        with patch.dict(os.environ, {"STRIPE_API_KEY": "sk_test123"}):
            # Mock has_subscription to return True
            mock_has_subscription.return_value = True

            # Mock subscription cancellation failure
            mock_cancel_subscription.side_effect = Exception(
                "Error cancelling stripe subscription: API Error"
            )

            # Create request data
            request_data = {"tofuUserId": self.test_user.id}

            # Authenticate the test client as the test user
            self.client.force_authenticate(user=self.test_user)

            # Create request
            response = self.client.post(
                f"/api/user/{self.username_base64}/cancel_stripe_subscription/",
                data=request_data,
                format="json",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(
                response.data,
                {
                    "error": {
                        "message": "Error cancelling stripe subscription: API Error"
                    }
                },
            )

    @patch("api.api_views.user.has_subscription")
    def test_stripe_cancel_subscription_no_active_subscription(
        self, mock_has_subscription
    ):
        # Mock Stripe API key
        with patch.dict(os.environ, {"STRIPE_API_KEY": "sk_test123"}):
            # Mock has_subscription to return False
            mock_has_subscription.return_value = False

            # Create request data
            request_data = {"tofuUserId": self.test_user.id}

            # Authenticate the test client as the test user
            self.client.force_authenticate(user=self.test_user)

            # Create request
            response = self.client.post(
                f"/api/user/{self.username_base64}/cancel_stripe_subscription/",
                data=request_data,
                format="json",
            )

            # Assert response
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(
                response.data,
                {"error": {"message": "User does not have an active subscription"}},
            )
