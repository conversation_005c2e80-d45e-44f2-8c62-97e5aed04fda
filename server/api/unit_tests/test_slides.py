import logging
import unittest
from unittest.mock import MagicMock, patch

from django.test import TestCase

from ..slides.core import SlidesDeliverableGenerator
from ..slides.parser import (
    _add_missing_placeholders,
    _extract_single_placeholder_value,
    _is_valid_doc_config,
    _is_well_formed_placeholder,
    _parse_slide_tags,
    _prepare_multiple_placeholder_insertions,
    _remove_extra_placeholders,
    _validate_and_normalize_entries,
    _validate_brace_pairing,
    get_text_placeholder_manual_insertions,
    parse_output_as_slides_manual_insertions,
)
from ..slides.template import (
    _get_s3_pptx_slides_text,
    get_pptx_text_contents,
    get_slides_template_content,
)


class TestSlides(TestCase):
    def setUp(self):
        # Mock objects for testing
        self.mock_gen_env = MagicMock()
        self.mock_data_wrapper = MagicMock()
        self.mock_gen_env._data_wrapper = self.mock_data_wrapper

    @patch("api.slides.template.TempS3File")
    @patch("api.slides.template.Presentation")
    def test_get_s3_pptx_slides_text_success(self, mock_presentation, mock_temp_file):
        # Setup
        mock_slide = MagicMock()
        mock_shape = MagicMock()
        mock_text_frame = MagicMock()
        mock_paragraph = MagicMock()
        mock_run = MagicMock()

        # Configure mocks
        mock_run.text = "Test text"
        mock_run.font.bold = True
        mock_paragraph.runs = [mock_run]
        mock_paragraph.level = 0
        mock_text_frame.paragraphs = [mock_paragraph]
        mock_shape.has_text_frame = True
        mock_shape.text_frame = mock_text_frame
        mock_slide.shapes = [mock_shape]
        mock_presentation.return_value.slides = [mock_slide]

        # Execute
        result = _get_s3_pptx_slides_text("test-bucket", "test.pptx")

        # Assert
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["slide_index"], 0)
        self.assertIn("**Test text**", result[0]["text"])

        # Test error case - invalid file
        mock_presentation.side_effect = Exception("Invalid file")
        with self.assertRaises(ValueError):
            _get_s3_pptx_slides_text("test-bucket", "invalid.pptx")

    @patch("api.slides.parser.get_document_config")
    @patch("api.slides.parser.Outline")
    def test_parse_output_as_slides_manual_insertions(
        self, mock_outline, mock_get_config
    ):
        mock_get_config.return_value = {
            "slides": [
                {
                    "id": "slide1",
                    "text_placeholders": [
                        {"placeholder": "[header]", "original_text": "Original Header"},
                        {"placeholder": "[body]", "original_text": "Original Body"},
                    ],
                },
                {
                    "id": "slide2",
                    "text_placeholders": [
                        {"placeholder": "[title]", "original_text": "Original Title"}
                    ],
                },
            ]
        }

        mock_outline_instance = MagicMock()
        mock_outline.return_value = mock_outline_instance
        mock_outline_instance.to_dict.return_value = {
            "slide_id": "test",
            "insertions": [],
        }

        # Test input with valid JSON content
        test_input = """
        <slide_1>[{"placeholder": "[header]", "value": "New Header"}, {"placeholder": "[body]", "value": "New Body"}]</slide_1>
        <slide_2>[{"placeholder": "[title]", "value": "New Title"}]</slide_2>
        """

        # Execute
        result = parse_output_as_slides_manual_insertions(test_input, "test_doc_id")

        # Assert
        self.assertEqual(len(result), 2)  # Should have 2 slides
        mock_outline.assert_called()  # Outline should be called for each slide
        self.assertEqual(mock_outline.call_count, 2)  # Called twice for 2 slides

        # Test with empty slide content
        test_input_empty = """
        <slide_1></slide_1>
        <slide_2>[{"placeholder": "[title]", "value": "New Title"}]</slide_2>
        """

        # Reset mock for the empty content test
        mock_outline.reset_mock()
        mock_outline_instance.to_dict.return_value = {
            "slide_id": "test",
            "insertions": [],
        }

        # Update mock config for empty slide test
        mock_get_config.return_value = {
            "slides": [
                {
                    "id": "slide1",
                    "text_placeholders": [],  # Empty placeholders for empty slide
                },
                {
                    "id": "slide2",
                    "text_placeholders": [
                        {"placeholder": "[title]", "original_text": "Original Title"}
                    ],
                },
            ]
        }

        # Execute with empty content
        result_empty = parse_output_as_slides_manual_insertions(
            test_input_empty, "test_doc_id"
        )

        # Assert
        self.assertEqual(len(result_empty), 2)  # Should still have 2 slides
        mock_outline.assert_called()  # Outline should be called for each slide
        self.assertEqual(mock_outline.call_count, 2)  # Called twice for 2 slides

        # Test with invalid JSON
        test_input_invalid = """
        <slide_1>{invalid json}</slide_1>
        <slide_2>[{"placeholder": "[title]", "value": "New Title"}]</slide_2>
        """

        # Reset mock for invalid JSON test
        mock_outline.reset_mock()
        mock_outline_instance.to_dict.return_value = {
            "slide_id": "test",
            "insertions": [],
        }

        # Reset mock config for invalid JSON test
        mock_get_config.return_value = {
            "slides": [
                {
                    "id": "slide1",
                    "text_placeholders": [
                        {"placeholder": "[header]", "original_text": "Original Header"}
                    ],
                },
                {
                    "id": "slide2",
                    "text_placeholders": [
                        {"placeholder": "[title]", "original_text": "Original Title"}
                    ],
                },
            ]
        }

        # Execute with invalid JSON
        result_invalid = parse_output_as_slides_manual_insertions(
            test_input_invalid, "test_doc_id"
        )

        # Assert that invalid JSON is handled gracefully
        self.assertEqual(len(result_invalid), 2)  # Should still have 2 slides
        mock_outline.assert_called()  # Outline should be called for each slide
        self.assertEqual(mock_outline.call_count, 2)  # Called twice for 2 slides

        # Test with mismatched slide count - fixed test case
        # Reset mock for mismatched slide count test
        mock_outline.reset_mock()
        mock_outline_instance.to_dict.return_value = {
            "slide_id": "test",
            "insertions": [],
        }

        # Configure mock to expect only 1 slide, but input has 2 slides
        mock_get_config.return_value = {
            "slides": [
                {
                    "id": "slide1",
                    "text_placeholders": [
                        {"placeholder": "[header]", "original_text": "Original Header"}
                    ],
                }
            ]
        }

        # Execute - in the harmonized code, this should handle out-of-range slides gracefully
        # The harmonized code logs a warning for slide 2 being out of range but continues processing
        # This is improved behavior - it's more robust than the original code
        result_mismatched = parse_output_as_slides_manual_insertions(
            test_input, "test_doc_id"
        )

        # Should successfully return 1 slide (matching the expected count)
        self.assertEqual(len(result_mismatched), 1)
        mock_outline.assert_called_once()  # Called once for the single expected slide

        # Test with true slide count mismatch - when parser finds fewer slides than expected
        test_input_fewer_slides = """
        <slide_1>[{"placeholder": "[header]", "value": "New Header"}]</slide_1>
        """

        # Reset mock and configure for 2 expected slides
        mock_outline.reset_mock()
        mock_get_config.return_value = {
            "slides": [
                {
                    "id": "slide1",
                    "text_placeholders": [
                        {"placeholder": "[header]", "original_text": "Original Header"}
                    ],
                },
                {
                    "id": "slide2",
                    "text_placeholders": [
                        {"placeholder": "[title]", "original_text": "Original Title"}
                    ],
                },
            ]
        }

        # This should work fine - slide 2 will be empty but still included in the result
        result_partial = parse_output_as_slides_manual_insertions(
            test_input_fewer_slides, "test_doc_id"
        )
        self.assertEqual(
            len(result_partial), 2
        )  # Should have 2 slides, second one empty
        self.assertEqual(mock_outline.call_count, 2)  # Called twice for 2 slides

    def test_extract_single_placeholder_value(self):
        """Test the single placeholder value extraction helper function."""
        # Test with dict input
        dict_input = {"value": "Test Value"}
        result = _extract_single_placeholder_value(dict_input)
        self.assertEqual(result, "Test Value")

        # Test with missing value key
        dict_no_value = {"other_key": "test"}
        result = _extract_single_placeholder_value(dict_no_value)
        self.assertEqual(result, "")

        # Test with list input - single item
        list_single = [{"value": "List Value"}]
        result = _extract_single_placeholder_value(list_single)
        self.assertEqual(result, "List Value")

        # Test with empty list
        list_empty = []
        result = _extract_single_placeholder_value(list_empty)
        self.assertEqual(result, "")

        # Test with multiple items in list (should log error and return first)
        list_multiple = [{"value": "First"}, {"value": "Second"}]
        result = _extract_single_placeholder_value(list_multiple)
        self.assertEqual(result, "First")

    def test_prepare_multiple_placeholder_insertions(self):
        """Test the multiple placeholder insertions preparation helper function."""
        # Test with list input
        list_input = [{"placeholder": "[test]", "value": "test"}]
        result = _prepare_multiple_placeholder_insertions(list_input)
        self.assertEqual(result, list_input)

        # Test with dict input
        dict_input = {"placeholder": "[test]", "value": "test"}
        result = _prepare_multiple_placeholder_insertions(dict_input)
        self.assertEqual(result, [dict_input])

    def test_validate_and_normalize_entries(self):
        """Test the entry validation and normalization helper function."""
        # Define dummy text_placeholders for the tests
        text_placeholders = [
            {"placeholder": "[test1]"},
            {"placeholder": "[test2]"},
            {"placeholder": "[test]"},
        ]

        # Test with valid entries
        valid_entries = [
            {"placeholder": "[test1]", "value": "value1"},
            {"placeholder": "[test2]", "value": "value2"},
        ]
        _validate_and_normalize_entries(
            text_placeholders, valid_entries
        )  # Should not raise
        self.assertEqual(len(valid_entries), 2)

        # Test with original_text instead of value
        entries_with_original = [
            {"placeholder": "[test]", "original_text": "original value"}
        ]
        _validate_and_normalize_entries(text_placeholders, entries_with_original)
        self.assertEqual(entries_with_original[0]["value"], "original value")
        self.assertNotIn("original_text", entries_with_original[0])

        # Test with invalid entry (missing keys) - should be removed, not raise error
        invalid_entries = [{"placeholder": "[test]"}]  # Missing value
        _validate_and_normalize_entries(text_placeholders, invalid_entries)
        self.assertEqual(len(invalid_entries), 0)  # Entry should be removed

        # Test with completely invalid entry - should be removed, not raise error
        completely_invalid = [
            {"other_key": "test"}
        ]  # Missing both placeholder and value
        _validate_and_normalize_entries(text_placeholders, completely_invalid)
        self.assertEqual(len(completely_invalid), 0)  # Entry should be removed

        # Test mixed valid/invalid entries - critical test for list modification bug
        mixed_entries = [
            {"placeholder": "[test1]", "value": "value1"},  # Valid
            {"invalid": "entry1"},  # Invalid - should be removed
            {"placeholder": "[test2]", "value": "value2"},  # Valid
            {"placeholder": "[test]"},  # Invalid - missing value, should be removed
            {
                "placeholder": "[test3]",
                "original_text": "original",
            },  # Valid with conversion
        ]
        original_length = len(mixed_entries)
        _validate_and_normalize_entries(text_placeholders, mixed_entries)

        # Should have 3 valid entries remaining (2 valid + 1 converted from original_text)
        self.assertEqual(len(mixed_entries), 3)
        self.assertTrue(
            original_length > len(mixed_entries)
        )  # Some entries were removed

        # Check that remaining entries are valid
        for entry in mixed_entries:
            self.assertIn("placeholder", entry)
            self.assertIn("value", entry)
            self.assertNotIn("original_text", entry)  # Should be converted

        # Test edge case: all entries invalid
        all_invalid = [
            {"invalid": "entry1"},
            {"invalid": "entry2"},
            {"placeholder": "[test]"},  # Missing value
        ]
        _validate_and_normalize_entries(text_placeholders, all_invalid)
        self.assertEqual(len(all_invalid), 0)  # All should be removed

        # Test edge case: empty entries list
        empty_entries = []
        _validate_and_normalize_entries(text_placeholders, empty_entries)
        self.assertEqual(len(empty_entries), 0)  # Should remain empty

    def test_validate_and_normalize_entries_list_modification_safety(self):
        """Test that list modification during iteration works correctly - regression test for the bug we fixed."""
        text_placeholders = [
            {"placeholder": "{test1}"},
            {"placeholder": "{test2}"},
            {"placeholder": "{test3}"},
        ]

        # Test case that specifically triggers the list modification during iteration scenario
        # This would have failed with the old implementation due to index shifting
        entries = [
            {"invalid1": "should_be_removed"},  # Index 0 - invalid, will be removed
            {
                "placeholder": "{test1}",
                "value": "value1",
            },  # Index 1 - valid, should remain
            {"invalid2": "should_be_removed"},  # Index 2 - invalid, will be removed
            {"invalid3": "should_be_removed"},  # Index 3 - invalid, will be removed
            {
                "placeholder": "{test2}",
                "value": "value2",
            },  # Index 4 - valid, should remain
            {
                "placeholder": "{test3}",
                "original_text": "text3",
            },  # Index 5 - valid with conversion
            {"invalid4": "should_be_removed"},  # Index 6 - invalid, will be removed
        ]

        # This should work without issues (would have failed with old implementation)
        _validate_and_normalize_entries(text_placeholders, entries)

        # Should have exactly 3 valid entries remaining
        self.assertEqual(len(entries), 3)

        # Check that correct entries remain and are valid
        expected_placeholders = {"{test1}", "{test2}", "{test3}"}
        actual_placeholders = {entry["placeholder"] for entry in entries}
        self.assertEqual(actual_placeholders, expected_placeholders)

        # Check all entries have values
        for entry in entries:
            self.assertIn("value", entry)
            self.assertNotIn("original_text", entry)  # Should be converted

        # Check specific conversions worked
        test3_entry = next(e for e in entries if e["placeholder"] == "{test3}")
        self.assertEqual(
            test3_entry["value"], "text3"
        )  # Should be converted from original_text

    def test_add_missing_placeholders(self):
        """Test the missing placeholders addition helper function."""
        text_placeholders = [
            {"placeholder": "[existing]"},
            {"placeholder": "[missing]"},
            {"placeholder": "[also_missing]"},
        ]

        insertions = [{"placeholder": "[existing]", "value": "existing value"}]

        _add_missing_placeholders(text_placeholders, insertions)

        # Should have 3 entries now
        self.assertEqual(len(insertions), 3)

        # Check that missing placeholders were added with empty values
        placeholder_values = {
            entry["placeholder"]: entry["value"] for entry in insertions
        }
        self.assertEqual(placeholder_values["[existing]"], "existing value")
        self.assertEqual(placeholder_values["[missing]"], "")
        self.assertEqual(placeholder_values["[also_missing]"], "")

    def test_remove_extra_placeholders(self):
        """Test removal of extra placeholders from text_placeholder_manual_insertions."""

        # Test case 1: No extra placeholders - should return same list
        text_placeholders = [
            {"placeholder": "{header}"},
            {"placeholder": "{body}"},
            {"placeholder": "{footer}"},
        ]
        text_placeholder_manual_insertions = [
            {"placeholder": "{header}", "value": "Header Value"},
            {"placeholder": "{body}", "value": "Body Value"},
            {"placeholder": "{footer}", "value": "Footer Value"},
        ]

        result = _remove_extra_placeholders(
            text_placeholders, text_placeholder_manual_insertions.copy()
        )

        expected = [
            {"placeholder": "{header}", "value": "Header Value"},
            {"placeholder": "{body}", "value": "Body Value"},
            {"placeholder": "{footer}", "value": "Footer Value"},
        ]

        self.assertEqual(result, expected)
        self.assertEqual(len(result), 3)

        # Test case 2: Has extra placeholders - should filter them out
        text_placeholder_manual_insertions_with_extra = [
            {"placeholder": "{header}", "value": "Header Value"},
            {"placeholder": "{body}", "value": "Body Value"},
            {"placeholder": "{footer}", "value": "Footer Value"},
            {"placeholder": "{extra1}", "value": "Extra Value 1"},  # Should be removed
            {"placeholder": "{extra2}", "value": "Extra Value 2"},  # Should be removed
        ]

        result = _remove_extra_placeholders(
            text_placeholders, text_placeholder_manual_insertions_with_extra.copy()
        )

        expected = [
            {"placeholder": "{header}", "value": "Header Value"},
            {"placeholder": "{body}", "value": "Body Value"},
            {"placeholder": "{footer}", "value": "Footer Value"},
        ]

        self.assertEqual(result, expected)
        self.assertEqual(len(result), 3)

        # Test case 3: Missing some placeholders - should keep only valid ones
        text_placeholder_manual_insertions_partial = [
            {"placeholder": "{header}", "value": "Header Value"},
            {"placeholder": "{extra}", "value": "Extra Value"},  # Should be removed
        ]

        result = _remove_extra_placeholders(
            text_placeholders, text_placeholder_manual_insertions_partial.copy()
        )

        expected = [{"placeholder": "{header}", "value": "Header Value"}]

        self.assertEqual(result, expected)
        self.assertEqual(len(result), 1)

        # Test case 4: Empty text_placeholders - should return empty list
        result = _remove_extra_placeholders(
            [], text_placeholder_manual_insertions.copy()
        )

        self.assertEqual(result, [])
        self.assertEqual(len(result), 0)

        # Test case 5: Empty text_placeholder_manual_insertions - should return empty list
        result = _remove_extra_placeholders(text_placeholders, [])

        self.assertEqual(result, [])
        self.assertEqual(len(result), 0)

        # Test case 6: Different placeholder formats (double braces)
        text_placeholders_double = [
            {"placeholder": "{{header}}"},
            {"placeholder": "{{body}}"},
        ]
        text_placeholder_manual_insertions_mixed = [
            {"placeholder": "{{header}}", "value": "Header Value"},
            {"placeholder": "{{body}}", "value": "Body Value"},
            {"placeholder": "{single}", "value": "Single Value"},  # Should be removed
            {"placeholder": "{{extra}}", "value": "Extra Value"},  # Should be removed
        ]

        result = _remove_extra_placeholders(
            text_placeholders_double, text_placeholder_manual_insertions_mixed.copy()
        )

        expected = [
            {"placeholder": "{{header}}", "value": "Header Value"},
            {"placeholder": "{{body}}", "value": "Body Value"},
        ]

        self.assertEqual(result, expected)
        self.assertEqual(len(result), 2)

    def test_get_text_placeholder_manual_insertions_with_extra_placeholders(self):
        """Test that extra placeholders are filtered out in the main function."""
        text_placeholders = [
            {"placeholder": "{valid1}"},
            {"placeholder": "{valid2}"},
        ]
        json_mapping = [
            {"placeholder": "{valid1}", "value": "Value 1"},
            {"placeholder": "{valid2}", "value": "Value 2"},
            {
                "placeholder": "{extra}",
                "value": "Extra Value",
            },  # Should be filtered out
        ]

        result = get_text_placeholder_manual_insertions(text_placeholders, json_mapping)

        # Should only contain valid placeholders
        self.assertEqual(len(result), 2)
        placeholder_names = {r["placeholder"] for r in result}
        self.assertEqual(placeholder_names, {"{valid1}", "{valid2}"})

    def test_is_valid_doc_config(self):
        """Test the document configuration validation helper function."""
        # Valid config
        valid_config = {"slides": [{"id": "slide1"}]}
        self.assertTrue(_is_valid_doc_config(valid_config))

        # Invalid configs
        self.assertFalse(_is_valid_doc_config(None))
        self.assertFalse(_is_valid_doc_config({}))
        self.assertFalse(_is_valid_doc_config({"slides": []}))
        self.assertFalse(_is_valid_doc_config({"slides": None}))
        self.assertFalse(_is_valid_doc_config({"other_key": "value"}))
        self.assertFalse(_is_valid_doc_config("not a dict"))

    def test_validate_brace_pairing(self):
        """Test the brace pairing validation helper function."""
        # Valid cases - properly paired braces
        self.assertTrue(_validate_brace_pairing("simple_text"))
        self.assertTrue(_validate_brace_pairing("text{placeholder}text"))
        self.assertTrue(_validate_brace_pairing("text{{placeholder}}text"))
        self.assertTrue(_validate_brace_pairing("{single}"))
        self.assertTrue(_validate_brace_pairing("{{double}}"))
        self.assertTrue(_validate_brace_pairing("{first}{second}"))
        self.assertTrue(_validate_brace_pairing("{{first}}{{second}}"))
        self.assertTrue(_validate_brace_pairing("text{nested{{inner}}content}text"))
        self.assertTrue(_validate_brace_pairing("{{outer{inner}content}}"))
        self.assertTrue(
            _validate_brace_pairing("complex{{nested{deep{deeper}deep}nested}}complex")
        )
        self.assertTrue(_validate_brace_pairing(""))  # Empty string
        self.assertTrue(
            _validate_brace_pairing("mixed{{braces}}")
        )  # Text with valid double braces

        # Valid mixed nesting
        self.assertTrue(_validate_brace_pairing("{single{{double}}single}"))
        self.assertTrue(_validate_brace_pairing("{{double{single}double}}"))

        # Invalid cases - unmatched braces
        self.assertFalse(_validate_brace_pairing("{unmatched"))
        self.assertFalse(_validate_brace_pairing("unmatched}"))
        self.assertFalse(_validate_brace_pairing("{{unmatched"))
        self.assertFalse(_validate_brace_pairing("unmatched}}"))
        self.assertFalse(_validate_brace_pairing("{mixed{{braces}"))
        self.assertFalse(_validate_brace_pairing("{single}{{incomplete"))
        self.assertFalse(_validate_brace_pairing("incomplete}}{single}"))

        # Invalid nesting - mismatched brace types
        self.assertFalse(
            _validate_brace_pairing("{single{{double}single}")
        )  # Missing closing }}
        self.assertFalse(
            _validate_brace_pairing("{{double{single}}single}")
        )  # Missing opening {
        self.assertFalse(
            _validate_brace_pairing("{wrong}}")
        )  # Single open, double close
        self.assertFalse(
            _validate_brace_pairing("{{wrong}")
        )  # Double open, single close

        # Edge cases
        self.assertFalse(_validate_brace_pairing("}{"))  # Backwards
        self.assertFalse(_validate_brace_pairing("}}{{"))  # Backwards double
        self.assertFalse(_validate_brace_pairing("{{}"))  # Incomplete mixed

    def test_is_well_formed_placeholder(self):
        """Test the placeholder validation function."""
        # Invalid input types
        self.assertFalse(_is_well_formed_placeholder(None))
        self.assertFalse(_is_well_formed_placeholder(""))
        self.assertFalse(_is_well_formed_placeholder(123))
        self.assertFalse(_is_well_formed_placeholder([]))

        # Invalid basic format
        self.assertFalse(_is_well_formed_placeholder("no_braces"))
        self.assertFalse(_is_well_formed_placeholder("{}"))  # Too short
        self.assertFalse(_is_well_formed_placeholder("{{}}"))  # Too short

        # Valid simple placeholders
        self.assertTrue(_is_well_formed_placeholder("{simple}"))
        self.assertTrue(_is_well_formed_placeholder("{{simple}}"))
        self.assertTrue(_is_well_formed_placeholder("{placeholder_name}"))
        self.assertTrue(_is_well_formed_placeholder("{{placeholder_name}}"))

        # Valid placeholders with internal content
        self.assertTrue(_is_well_formed_placeholder("{header.title}"))
        self.assertTrue(_is_well_formed_placeholder("{{user.first_name}}"))
        self.assertTrue(_is_well_formed_placeholder("{data[0]}"))
        self.assertTrue(_is_well_formed_placeholder("{{items[index].value}}"))

        # Valid nested braces (properly paired)
        self.assertTrue(_is_well_formed_placeholder("{outer{inner}content}"))
        self.assertTrue(_is_well_formed_placeholder("{{outer{{inner}}content}}"))
        self.assertTrue(_is_well_formed_placeholder("{{complex{mixed}nesting}}"))
        self.assertTrue(_is_well_formed_placeholder("{data{key{subkey}key}data}"))

        # Invalid - wrong start/end format
        self.assertFalse(_is_well_formed_placeholder("{missing_end"))
        self.assertFalse(_is_well_formed_placeholder("missing_start}"))
        self.assertFalse(_is_well_formed_placeholder("{{missing_end}"))
        self.assertFalse(_is_well_formed_placeholder("{missing_end}}"))

        # Invalid - unmatched internal braces
        self.assertFalse(_is_well_formed_placeholder("{unmatched{inner}"))
        self.assertFalse(_is_well_formed_placeholder("{{unmatched{{inner}}"))
        self.assertFalse(_is_well_formed_placeholder("{inner}unmatched}"))
        self.assertFalse(_is_well_formed_placeholder("{{inner}}unmatched}}"))

        # Invalid - mismatched internal brace types
        self.assertFalse(_is_well_formed_placeholder("{single{{double}single}"))
        self.assertFalse(_is_well_formed_placeholder("{{double{single}}single}"))
        self.assertFalse(_is_well_formed_placeholder("{wrong}}"))
        self.assertFalse(_is_well_formed_placeholder("{{wrong}"))

        # Edge cases - complex valid nesting
        self.assertTrue(_is_well_formed_placeholder("{a{b{c}b}a}"))
        self.assertTrue(_is_well_formed_placeholder("{{a{{b{{c}}b}}a}}"))
        self.assertTrue(_is_well_formed_placeholder("{{mixed{single}mixed}}"))
        self.assertTrue(_is_well_formed_placeholder("{mixed{{double}}mixed}"))

        # Edge cases - complex invalid nesting
        self.assertFalse(_is_well_formed_placeholder("{a{b{c}b}"))  # Missing closing
        self.assertFalse(_is_well_formed_placeholder("{a{b}c}b}"))  # Extra closing
        self.assertFalse(
            _is_well_formed_placeholder("{{a{b}}c}")
        )  # Mixed type mismatch

    def test_parse_slide_tags(self):
        """Test the slide tags parsing helper function."""
        original_slides = [{"id": "slide1"}, {"id": "slide2"}, {"id": "slide3"}]

        # Test with valid slide tags
        text_with_slides = """
        <slide_1>{"test": "data1"}</slide_1>
        <slide_3>{"test": "data3"}</slide_3>
        """

        result = _parse_slide_tags(text_with_slides, original_slides)

        # Should have 3 entries (same length as original_slides)
        self.assertEqual(len(result), 3)

        # Check that slides were parsed correctly
        self.assertEqual(result[0]["json"]["test"], "data1")
        self.assertEqual(result[1], {})  # No slide_2 in input
        self.assertEqual(result[2]["json"]["test"], "data3")

        # Test with empty slide content
        text_with_empty = """
        <slide_1></slide_1>
        <slide_2>{"test": "data"}</slide_2>
        """

        result_empty = _parse_slide_tags(text_with_empty, original_slides[:2])
        self.assertEqual(result_empty[0]["content_instruction"], "DNE")
        self.assertEqual(result_empty[1]["json"]["test"], "data")

        # Test with invalid JSON that cannot be repaired
        text_with_invalid = """
        <slide_1>This is not JSON at all - no braces or brackets!</slide_1>
        """

        result_invalid = _parse_slide_tags(text_with_invalid, original_slides[:1])

        # The strip_for_json function is very robust and converts non-JSON text to empty string
        # which then gets parsed successfully as JSON string
        self.assertTrue("json" in result_invalid[0])
        self.assertEqual(result_invalid[0]["json"], "")

        # Test with out of range slide numbers
        text_out_of_range = """
        <slide_5>{"test": "data"}</slide_5>
        """

        result_out_of_range = _parse_slide_tags(text_out_of_range, original_slides[:2])
        # Should handle gracefully - no changes to the result
        self.assertEqual(result_out_of_range, [{}, {}])

    def test_get_text_placeholder_manual_insertions_edge_cases(self):
        """Test edge cases for get_text_placeholder_manual_insertions function."""
        # Test with empty placeholders
        result = get_text_placeholder_manual_insertions([], {})
        self.assertEqual(result, [])

        # Test single placeholder with list containing empty value
        single_placeholder = [{"placeholder": "[test]"}]
        json_mapping_empty_list = []
        result = get_text_placeholder_manual_insertions(
            single_placeholder, json_mapping_empty_list
        )
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["placeholder"], "[test]")
        self.assertEqual(result[0]["value"], "")

        # Test multiple placeholders with mixed valid/invalid entries
        multi_placeholders = [
            {"placeholder": "[header]"},
            {"placeholder": "[body]"},
            {"placeholder": "[footer]"},
        ]

        json_mapping_mixed = [
            {"placeholder": "[header]", "original_text": "Header Text"},
            {"placeholder": "[footer]", "value": "Footer Text"},
            # Missing [body] placeholder
        ]

        result = get_text_placeholder_manual_insertions(
            multi_placeholders, json_mapping_mixed
        )
        self.assertEqual(len(result), 3)

        # Check that original_text was converted to value
        header_entry = next(e for e in result if e["placeholder"] == "[header]")
        self.assertEqual(header_entry["value"], "Header Text")

        # Check that missing placeholder was added with empty value
        body_entry = next(e for e in result if e["placeholder"] == "[body]")
        self.assertEqual(body_entry["value"], "")

        # Check that regular value is preserved
        footer_entry = next(e for e in result if e["placeholder"] == "[footer]")
        self.assertEqual(footer_entry["value"], "Footer Text")

    @patch("api.slides.parser.get_document_config")
    def test_parse_output_invalid_document_config(self, mock_get_config):
        """Test parsing with invalid document configuration."""
        # Test with None response
        mock_get_config.return_value = None
        with self.assertRaises(ValueError) as context:
            parse_output_as_slides_manual_insertions("test", "doc_id")
        self.assertIn(
            "Failed to retrieve document configuration", str(context.exception)
        )

        # Test with empty response
        mock_get_config.return_value = {}
        with self.assertRaises(ValueError):
            parse_output_as_slides_manual_insertions("test", "doc_id")

        # Test with invalid slides key
        mock_get_config.return_value = {"slides": None}
        with self.assertRaises(ValueError):
            parse_output_as_slides_manual_insertions("test", "doc_id")

    @patch("api.slides.template._get_s3_pptx_slides_text")
    @patch("api.slides.template.upload_s3_ppt_to_flashdocs")
    @patch("api.slides.template.get_document_config")
    @patch("api.slides.template.cache")
    def test_get_slides_template_content_pptx(
        self,
        mock_cache,
        mock_get_document_config,
        mock_upload_s3_ppt,
        mock_get_slides_text,
    ):
        # Setup - mock cache to return None so no cached results are used
        mock_cache.get.return_value = None

        # Setup
        mock_get_slides_text.return_value = [
            {"slide_index": 1, "text": "Test content without placeholders"}
        ]

        # Mock Flashdocs API calls
        mock_upload_s3_ppt.return_value = {"document_id": "test_flashdocs_doc_id"}
        mock_get_document_config.return_value = {
            "slides": [
                {
                    "id": "slide1",
                    "index": 0,
                    "text_placeholders": [
                        {"placeholder": "[title]", "original_text": "Default Title"}
                    ],
                }
            ]
        }

        template_url = "/api/web/storage/s3-presigned-url?file=test.pptx&fileType=application/vnd.openxmlformats-officedocument.presentationml.presentation&directory=test-bucket"

        # Execute
        result = get_slides_template_content(template_url)

        # Assert - should call Flashdocs API since no placeholders detected
        self.assertIn("slide 1", result["text"])
        self.assertIn("[title]", result["text"])
        mock_upload_s3_ppt.assert_called_once()
        mock_get_document_config.assert_called_once()

        # Test case with placeholders (should not call Flashdocs)
        mock_upload_s3_ppt.reset_mock()
        mock_get_document_config.reset_mock()
        mock_get_slides_text.return_value = [
            {"slide_index": 1, "text": "[Test content with placeholders]"}
        ]

        result = get_slides_template_content(template_url)

        # Should use slides text directly without Flashdocs calls
        self.assertEqual(
            result["text"],
            '[{"slide_index": 1, "text": "[Test content with placeholders]"}]',
        )
        mock_upload_s3_ppt.assert_not_called()
        mock_get_document_config.assert_not_called()

        # Test error case - invalid file type
        invalid_url = "/api/web/storage/s3-presigned-url?file=test.txt&fileType=text/plain&directory=test-bucket"
        with self.assertRaises(ValueError):
            get_slides_template_content(invalid_url)

        # Test error case - invalid S3 URL
        invalid_s3_url = "invalid-url"
        with self.assertRaises(ValueError):
            get_slides_template_content(invalid_s3_url)

    @patch("api.slides.core.sync_generate_flashdocs_slides")
    @patch("api.slides.core.upload_cloudfront_pptx_file_to_google_drive")
    @patch("api.slides.core.get_repurpose_template_flashdocs_document_id")
    @patch("api.slides.core.parse_output_as_slides_manual_insertions")
    @patch("api.slides.core.copy_google_drive_file_with_retry")
    @patch("api.slides.core.copy_permissions_from_template_to_slides")
    def test_update_slides_variations_with_template(
        self,
        mock_copy_permissions,
        mock_copy_file,
        mock_parse,
        mock_get_doc_id,
        mock_upload,
        mock_generate,
    ):
        # Setup
        self.mock_data_wrapper.should_use_slides_template = True
        self.mock_data_wrapper.content_group_params = {
            "repurpose_template_content_source_copy": "test_template_url"
        }

        # Mock Flashdocs document ID
        mock_get_doc_id.return_value = "test_flashdocs_doc_id"

        # Mock slide parsing
        mock_parse.return_value = [
            {
                "slide_id": "slide1",
                "insertions": [{"placeholder": "[title]", "value": "Test Title"}],
            }
        ]

        # Mock Flashdocs generation
        mock_generate.return_value = {
            "link_to_deck": "https://cloudfront.net/tofuhq.com/creations/test.pptx"
        }

        # Mock Google Drive upload
        mock_upload.return_value = (
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit"
        )

        # Mock Google Drive file copy
        mock_copy_file.return_value = (
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit"
        )

        # Mock permissions copying
        mock_copy_permissions.return_value = None

        variation_meta = {
            "variations": [
                {"text": "Test variation 1", "google_slides_url": None},
                {"text": "Test variation 2", "google_slides_url": None},
            ],
            "current_version": {},
        }

        # Execute
        result = SlidesDeliverableGenerator(self.mock_gen_env).update_slides_variations(
            variation_meta
        )

        # Assert
        self.assertEqual(len(result["variations"]), 2)
        self.assertEqual(
            result["variations"][0]["google_slides_url"],
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit",
        )
        self.assertEqual(
            result["variations"][1]["google_slides_url"],
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit",
        )
        self.assertEqual(
            result["current_version"]["google_slides_url"],
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit",
        )

        # Verify Flashdocs calls
        mock_get_doc_id.assert_called_once_with("test_template_url")
        self.assertEqual(mock_parse.call_count, 2)  # Called for each variation
        mock_generate.assert_called()

        # Verify Google Drive calls
        mock_upload.assert_called_with(
            "https://cloudfront.net/tofuhq.com/creations/test.pptx"
        )

    @patch("api.slides.core.sync_generate_flashdocs_slides")
    @patch("api.slides.core.copy_google_drive_file_with_retry")
    def test_update_slides_variations_without_template(
        self, mock_copy_file, mock_generate
    ):
        # Setup
        self.mock_data_wrapper.should_use_slides_template = False

        # Mock Flashdocs generation with direct Google Slides URL
        mock_generate.return_value = {
            "link_to_deck": "https://docs.google.com/presentation/d/test12345678901234567890123456/edit"
        }

        # Mock Google Drive file copy
        mock_copy_file.return_value = (
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit"
        )

        variation_meta = {
            "variations": [{"text": "Test variation 1", "google_slides_url": None}],
            "current_version": {},
        }

        # Execute
        result = SlidesDeliverableGenerator(self.mock_gen_env).update_slides_variations(
            variation_meta
        )

        # Assert
        self.assertEqual(len(result["variations"]), 1)
        self.assertEqual(
            result["variations"][0]["google_slides_url"],
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit",
        )
        self.assertEqual(
            result["current_version"]["google_slides_url"],
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit",
        )

        # Verify Flashdocs call
        mock_generate.assert_called_once_with(
            prompt="Test variation 1",
            export_options={
                "google_slides_permission_options": {
                    "anyone_edit_with_link": False,
                    "anyone_view_with_link": False,
                    "google_document_editors": [
                        "<EMAIL>",
                        "<EMAIL>",
                    ],
                },
                "output_format": "google_slides_direct_link",
            },
        )

        # Verify Google Drive copy was called
        mock_copy_file.assert_called_once_with(
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit",
            name="Generated Slides",
        )

    @patch("api.slides.core.sync_generate_flashdocs_slides")
    @patch("api.slides.core.upload_cloudfront_pptx_file_to_google_drive")
    @patch("api.slides.core.copy_google_drive_file_with_retry")
    def test_update_slides_variations_cloudfront_upload(
        self,
        mock_copy_file,
        mock_upload,
        mock_generate,
    ):
        # Setup
        self.mock_data_wrapper.should_use_slides_template = False

        # Mock Flashdocs generation with CloudFront URL
        mock_generate.return_value = {
            "link_to_deck": "https://cloudfront.net/tofuhq.com/creations/test.pptx"
        }

        # Mock Google Drive upload
        mock_upload.return_value = (
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit"
        )

        # Mock Google Drive file copy
        mock_copy_file.return_value = (
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit"
        )

        variation_meta = {
            "variations": [{"text": "Test variation 1", "google_slides_url": None}],
            "current_version": {},
        }

        # Execute
        result = SlidesDeliverableGenerator(self.mock_gen_env).update_slides_variations(
            variation_meta
        )

        # Assert
        self.assertEqual(
            result["variations"][0]["google_slides_url"],
            "https://docs.google.com/presentation/d/test12345678901234567890123456/edit",
        )

        # Verify CloudFront to Google Drive flow
        mock_upload.assert_called_once_with(
            "https://cloudfront.net/tofuhq.com/creations/test.pptx"
        )

    @patch("api.slides.core.sync_generate_flashdocs_slides")
    def test_update_slides_variations_generation_failure(self, mock_generate):
        # Setup
        self.mock_data_wrapper.should_use_slides_template = False
        mock_generate.return_value = {}  # Empty response to simulate failure

        variation = {"text": "Test variation 1", "google_slides_url": None}

        # Execute and assert - test the actual _process_single_variation method
        slides_generator = SlidesDeliverableGenerator(self.mock_gen_env)
        with self.assertRaises(ValueError) as context:
            slides_generator._process_single_variation(variation, None)

        self.assertIn("Failed to generate link to deck", str(context.exception))

    @patch("api.slides.core.sync_generate_flashdocs_slides")
    def test_update_slides_variations_generation_exception(self, mock_generate):
        # Setup
        mock_generate.side_effect = Exception("Generation failed")

        generator = SlidesDeliverableGenerator(self.mock_gen_env)
        variation_meta = {
            "variations": [
                {"text": "Test content"},
            ]
        }

        # Execute and assert
        with self.assertRaises(Exception):
            generator.update_slides_variations(variation_meta)

    @patch("api.slides.template.cache")
    @patch("api.slides.template.TempS3File")
    @patch("api.slides.template.Presentation")
    @patch("api.slides.template.parse_s3_presigned_url")
    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_success(
        self,
        mock_is_s3_url,
        mock_parse_url,
        mock_presentation,
        mock_temp_file,
        mock_cache,
    ):
        """Test successful text extraction from PPTX file."""
        # Setup
        test_url = "https://s3.amazonaws.com/test-bucket/test.pptx"
        mock_is_s3_url.return_value = True
        mock_cache.get.return_value = None  # No cached result
        mock_parse_url.return_value = (
            "test.pptx",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "test-bucket",
        )

        # Create mock slide with positioned text elements
        mock_slide = MagicMock()

        # First shape (top-left position)
        mock_shape1 = MagicMock()
        mock_shape1.has_text_frame = True
        mock_shape1.top = 100
        mock_shape1.left = 50
        mock_run1 = MagicMock()
        mock_run1.text = "Title Text"
        mock_paragraph1 = MagicMock()
        mock_paragraph1.runs = [mock_run1]
        mock_shape1.text_frame.paragraphs = [mock_paragraph1]

        # Second shape (bottom-right position)
        mock_shape2 = MagicMock()
        mock_shape2.has_text_frame = True
        mock_shape2.top = 300
        mock_shape2.left = 200
        mock_run2 = MagicMock()
        mock_run2.text = "Body Text"
        mock_paragraph2 = MagicMock()
        mock_paragraph2.runs = [mock_run2]
        mock_shape2.text_frame.paragraphs = [mock_paragraph2]

        # Third shape (middle position)
        mock_shape3 = MagicMock()
        mock_shape3.has_text_frame = True
        mock_shape3.top = 200
        mock_shape3.left = 100
        mock_run3 = MagicMock()
        mock_run3.text = "Subtitle Text"
        mock_paragraph3 = MagicMock()
        mock_paragraph3.runs = [mock_run3]
        mock_shape3.text_frame.paragraphs = [mock_paragraph3]

        mock_slide.shapes = [mock_shape1, mock_shape2, mock_shape3]
        mock_presentation.return_value.slides = [mock_slide]

        # Execute
        result = get_pptx_text_contents(test_url)

        # Assert
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["slide_index"], 0)

        # Text should be ordered by position (y first, then x)
        expected_text = "Title Text\nSubtitle Text\nBody Text"
        self.assertEqual(result[0]["text"], expected_text)

        # Verify caching
        mock_cache.set.assert_called_once_with(
            f"pptx_text_contents_{test_url}", result, 60
        )

    @patch("api.slides.template.cache")
    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_cache_hit(self, mock_is_s3_url, mock_cache):
        """Test that cached results are returned when available."""
        # Setup
        test_url = "https://s3.amazonaws.com/test-bucket/test.pptx"
        mock_is_s3_url.return_value = True
        cached_result = [{"slide_index": 0, "text": "Cached text"}]
        mock_cache.get.return_value = cached_result

        # Execute
        result = get_pptx_text_contents(test_url)

        # Assert
        self.assertEqual(result, cached_result)
        mock_cache.set.assert_not_called()  # Should not set cache if already cached

    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_invalid_url(self, mock_is_s3_url):
        """Test error handling for invalid URLs."""
        # Setup
        test_url = "invalid-url"
        mock_is_s3_url.return_value = False

        # Execute and Assert
        with self.assertRaises(ValueError) as context:
            get_pptx_text_contents(test_url)

        self.assertIn("Invalid PPTX URL", str(context.exception))

    @patch("api.slides.template.cache")
    @patch("api.slides.template.TempS3File")
    @patch("api.slides.template.Presentation")
    @patch("api.slides.template.parse_s3_presigned_url")
    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_multiple_paragraphs(
        self,
        mock_is_s3_url,
        mock_parse_url,
        mock_presentation,
        mock_temp_file,
        mock_cache,
    ):
        """Test text extraction with multiple paragraphs and runs."""
        # Setup
        test_url = "https://s3.amazonaws.com/test-bucket/test.pptx"
        mock_is_s3_url.return_value = True
        mock_cache.get.return_value = None
        mock_parse_url.return_value = (
            "test.pptx",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "test-bucket",
        )

        # Create mock slide with shape containing multiple paragraphs
        mock_slide = MagicMock()
        mock_shape = MagicMock()
        mock_shape.has_text_frame = True
        mock_shape.top = 100
        mock_shape.left = 50

        # First paragraph with multiple runs
        mock_run1_1 = MagicMock()
        mock_run1_1.text = "First "
        mock_run1_2 = MagicMock()
        mock_run1_2.text = "paragraph"
        mock_paragraph1 = MagicMock()
        mock_paragraph1.runs = [mock_run1_1, mock_run1_2]

        # Second paragraph with single run
        mock_run2 = MagicMock()
        mock_run2.text = "Second paragraph"
        mock_paragraph2 = MagicMock()
        mock_paragraph2.runs = [mock_run2]

        mock_shape.text_frame.paragraphs = [mock_paragraph1, mock_paragraph2]
        mock_slide.shapes = [mock_shape]
        mock_presentation.return_value.slides = [mock_slide]

        # Execute
        result = get_pptx_text_contents(test_url)

        # Assert
        self.assertEqual(len(result), 1)
        expected_text = "First paragraph\nSecond paragraph"
        self.assertEqual(result[0]["text"], expected_text)

    @patch("api.slides.template.cache")
    @patch("api.slides.template.TempS3File")
    @patch("api.slides.template.Presentation")
    @patch("api.slides.template.parse_s3_presigned_url")
    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_empty_slides(
        self,
        mock_is_s3_url,
        mock_parse_url,
        mock_presentation,
        mock_temp_file,
        mock_cache,
    ):
        """Test handling of slides with no text content."""
        # Setup
        test_url = "https://s3.amazonaws.com/test-bucket/test.pptx"
        mock_is_s3_url.return_value = True
        mock_cache.get.return_value = None
        mock_parse_url.return_value = (
            "test.pptx",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "test-bucket",
        )

        # Create slides: one empty, one with text, one with only whitespace
        mock_slide1 = MagicMock()
        mock_slide1.shapes = []  # No shapes

        mock_slide2 = MagicMock()
        mock_shape_with_text = MagicMock()
        mock_shape_with_text.has_text_frame = True
        mock_shape_with_text.top = 100
        mock_shape_with_text.left = 50
        mock_run = MagicMock()
        mock_run.text = "Valid text"
        mock_paragraph = MagicMock()
        mock_paragraph.runs = [mock_run]
        mock_shape_with_text.text_frame.paragraphs = [mock_paragraph]
        mock_slide2.shapes = [mock_shape_with_text]

        mock_slide3 = MagicMock()
        mock_shape_whitespace = MagicMock()
        mock_shape_whitespace.has_text_frame = True
        mock_shape_whitespace.top = 100
        mock_shape_whitespace.left = 50
        mock_run_whitespace = MagicMock()
        mock_run_whitespace.text = "   \n\t  "  # Only whitespace
        mock_paragraph_whitespace = MagicMock()
        mock_paragraph_whitespace.runs = [mock_run_whitespace]
        mock_shape_whitespace.text_frame.paragraphs = [mock_paragraph_whitespace]
        mock_slide3.shapes = [mock_shape_whitespace]

        mock_presentation.return_value.slides = [mock_slide1, mock_slide2, mock_slide3]

        # Execute
        result = get_pptx_text_contents(test_url)

        # Assert - only slide with actual text content should be included
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["slide_index"], 1)
        self.assertEqual(result[0]["text"], "Valid text")

    @patch("api.slides.template.cache")
    @patch("api.slides.template.TempS3File")
    @patch("api.slides.template.Presentation")
    @patch("api.slides.template.parse_s3_presigned_url")
    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_shape_without_text_frame(
        self,
        mock_is_s3_url,
        mock_parse_url,
        mock_presentation,
        mock_temp_file,
        mock_cache,
    ):
        """Test handling of shapes without text frames."""
        # Setup
        test_url = "https://s3.amazonaws.com/test-bucket/test.pptx"
        mock_is_s3_url.return_value = True
        mock_cache.get.return_value = None
        mock_parse_url.return_value = (
            "test.pptx",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "test-bucket",
        )

        mock_slide = MagicMock()

        # Shape without text frame (e.g., image or drawing)
        mock_shape_no_text = MagicMock()
        mock_shape_no_text.has_text_frame = False

        # Shape with text frame
        mock_shape_with_text = MagicMock()
        mock_shape_with_text.has_text_frame = True
        mock_shape_with_text.top = 100
        mock_shape_with_text.left = 50
        mock_run = MagicMock()
        mock_run.text = "Text content"
        mock_paragraph = MagicMock()
        mock_paragraph.runs = [mock_run]
        mock_shape_with_text.text_frame.paragraphs = [mock_paragraph]

        mock_slide.shapes = [mock_shape_no_text, mock_shape_with_text]
        mock_presentation.return_value.slides = [mock_slide]

        # Execute
        result = get_pptx_text_contents(test_url)

        # Assert - only text from shapes with text frames should be included
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["text"], "Text content")

    @patch("api.slides.template.cache")
    @patch("api.slides.template.TempS3File")
    @patch("api.slides.template.parse_s3_presigned_url")
    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_processing_error(
        self,
        mock_is_s3_url,
        mock_parse_url,
        mock_temp_file,
        mock_cache,
    ):
        """Test error handling during file processing."""
        # Setup
        test_url = "https://s3.amazonaws.com/test-bucket/test.pptx"
        mock_is_s3_url.return_value = True
        mock_cache.get.return_value = None
        mock_parse_url.return_value = (
            "test.pptx",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "test-bucket",
        )

        # Simulate file processing error
        mock_temp_file.side_effect = Exception("File processing error")

        # Execute and Assert
        with self.assertRaises(Exception) as context:
            get_pptx_text_contents(test_url)

        self.assertIn("File processing error", str(context.exception))

    @patch("api.slides.template.cache")
    @patch("api.slides.template.TempS3File")
    @patch("api.slides.template.Presentation")
    @patch("api.slides.template.parse_s3_presigned_url")
    @patch("api.slides.template.is_s3_url")
    def test_get_pptx_text_contents_position_handling(
        self,
        mock_is_s3_url,
        mock_parse_url,
        mock_presentation,
        mock_temp_file,
        mock_cache,
    ):
        """Test handling of shapes with missing position attributes."""
        # Setup
        test_url = "https://s3.amazonaws.com/test-bucket/test.pptx"
        mock_is_s3_url.return_value = True
        mock_cache.get.return_value = None
        mock_parse_url.return_value = (
            "test.pptx",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "test-bucket",
        )

        mock_slide = MagicMock()

        # Shape with no position attributes
        mock_shape1 = MagicMock()
        mock_shape1.has_text_frame = True
        # Simulate missing top/left attributes
        mock_shape1.top = None
        mock_shape1.left = None
        mock_run1 = MagicMock()
        mock_run1.text = "No position"
        mock_paragraph1 = MagicMock()
        mock_paragraph1.runs = [mock_run1]
        mock_shape1.text_frame.paragraphs = [mock_paragraph1]

        # Shape with position
        mock_shape2 = MagicMock()
        mock_shape2.has_text_frame = True
        mock_shape2.top = 100
        mock_shape2.left = 50
        mock_run2 = MagicMock()
        mock_run2.text = "With position"
        mock_paragraph2 = MagicMock()
        mock_paragraph2.runs = [mock_run2]
        mock_shape2.text_frame.paragraphs = [mock_paragraph2]

        mock_slide.shapes = [mock_shape1, mock_shape2]
        mock_presentation.return_value.slides = [mock_slide]

        # Execute
        result = get_pptx_text_contents(test_url)

        # Assert - should handle missing positions gracefully (default to 0)
        self.assertEqual(len(result), 1)
        # Shape with no position (0,0) should come before shape with position (100,50)
        expected_text = "No position\nWith position"
        self.assertEqual(result[0]["text"], expected_text)

    @patch("api.gdrive_file_ops.copy_permissions_from_template_to_slides")
    def test_copy_permissions_from_template_to_slides(self, mock_copy_permissions):
        """Test copying permissions from template to slides."""
        from ..gdrive_file_ops import copy_permissions_from_template_to_slides

        template_url = "https://docs.google.com/presentation/d/template_id/edit"
        slides_url = "https://docs.google.com/presentation/d/slides_id/edit"

        # Mock the function to return successfully
        mock_copy_permissions.return_value = None

        # Execute
        copy_permissions_from_template_to_slides(template_url, slides_url)

        # Assert
        mock_copy_permissions.assert_called_once_with(template_url, slides_url)

    @patch("api.gdrive_file_ops.service_account.Credentials.from_service_account_file")
    @patch("api.gdrive_file_ops.googleapiclient.discovery.build")
    @patch("api.gdrive_file_ops.extract_google_drive_file_id")
    @patch("api.gdrive_file_ops.retry_operation")
    def test_create_tofu_owned_copy_with_template_permissions(
        self, mock_retry_operation, mock_extract_id, mock_build, mock_credentials
    ):
        """Test creating a Tofu-owned copy with template permissions by mocking Google Drive API calls."""
        from ..gdrive_file_ops import (
            copy_google_drive_file_with_retry,
            copy_permissions_from_template_to_slides,
        )

        # Test data
        original_url = "https://docs.google.com/presentation/d/original_id/edit"
        tofu_copy_url = "https://docs.google.com/presentation/d/tofu_copy_id/edit"
        template_url = "https://docs.google.com/presentation/d/template_id/edit"

        # Mock file IDs
        file_id_mapping = {
            original_url: "original_id",
            template_url: "template_id",
            tofu_copy_url: "tofu_copy_id",
        }
        mock_extract_id.side_effect = lambda url: file_id_mapping[url]

        # Mock credentials
        mock_creds = MagicMock()
        mock_credentials.return_value = mock_creds

        # Mock Google Drive service
        mock_drive = MagicMock()
        mock_build.return_value = mock_drive

        # Mock files service for copy operation
        mock_files = MagicMock()
        mock_drive.files.return_value = mock_files

        # Mock get operation to return file name
        mock_get_request = MagicMock()
        mock_get_request.execute.return_value = {"name": "Original Presentation"}
        mock_files.get.return_value = mock_get_request

        # Mock copy operation with supportsAllDrives=True verification
        mock_copy_request = MagicMock()
        mock_copy_request.execute.return_value = {
            "id": "tofu_copy_id",
            "webViewLink": tofu_copy_url,
        }
        mock_files.copy.return_value = mock_copy_request

        # Mock permissions service
        mock_permissions = MagicMock()
        mock_drive.permissions.return_value = mock_permissions

        # Mock template permissions list
        template_permissions = [
            {
                "id": "permission1",
                "type": "user",
                "role": "writer",
                "emailAddress": "<EMAIL>",
                "allowFileDiscovery": True,
            },
            {
                "id": "permission2",
                "type": "user",
                "role": "reader",
                "emailAddress": "<EMAIL>",
                "allowFileDiscovery": False,
            },
            {
                "id": "owner_permission",
                "type": "user",
                "role": "owner",
                "emailAddress": "<EMAIL>",
            },
            {
                "id": "service_permission",
                "type": "user",
                "role": "writer",
                "emailAddress": "<EMAIL>",
            },
            {
                "id": "flashdocs_permission",
                "type": "user",
                "role": "writer",
                "emailAddress": "<EMAIL>",
            },
        ]

        # Mock original file permissions (for copy operation)
        original_permissions = [
            {
                "id": "original_permission",
                "type": "user",
                "role": "writer",
                "emailAddress": "<EMAIL>",
            }
        ]

        # Mock permissions list calls
        def mock_permissions_list(*args, **kwargs):
            mock_list_request = MagicMock()
            file_id = kwargs.get("fileId")
            if file_id == "original_id":
                mock_list_request.execute.return_value = {
                    "permissions": original_permissions
                }
            elif file_id == "template_id":
                mock_list_request.execute.return_value = {
                    "permissions": template_permissions
                }
            return mock_list_request

        mock_permissions.list.side_effect = mock_permissions_list

        # Mock permissions create calls
        mock_create_request = MagicMock()
        mock_create_request.execute.return_value = {"id": "new_permission_id"}
        mock_permissions.create.return_value = mock_create_request

        # Mock retry_operation to execute the function directly (no actual retry)
        def mock_retry_side_effect(operation, **kwargs):
            return operation()

        mock_retry_operation.side_effect = mock_retry_side_effect

        # Execute copy operation
        result = copy_google_drive_file_with_retry(
            original_url, name="Generated Slides"
        )
        self.assertEqual(result, tofu_copy_url)

        # Execute permissions copy
        copy_permissions_from_template_to_slides(template_url, tofu_copy_url)

        # Verify copy operation was called with supportsAllDrives=True
        mock_files.copy.assert_called_once()
        copy_call_args = mock_files.copy.call_args
        self.assertEqual(copy_call_args[1]["fileId"], "original_id")
        self.assertEqual(copy_call_args[1]["body"]["name"], "Generated Slides")
        self.assertTrue(copy_call_args[1]["supportsAllDrives"])

        # Verify permissions list calls with supportsAllDrives=True
        expected_list_calls = [
            # From copy operation
            unittest.mock.call(
                fileId="original_id",
                supportsAllDrives=True,
                fields="permissions(emailAddress,role,type,allowFileDiscovery,domain)",
            ),
            # From permissions copy operation
            unittest.mock.call(
                fileId="template_id",
                supportsAllDrives=True,
                fields="permissions(emailAddress,role,type,allowFileDiscovery,domain)",
            ),
        ]
        self.assertEqual(mock_permissions.list.call_count, 2)
        mock_permissions.list.assert_has_calls(expected_list_calls)

        # Verify permissions create calls
        # Should be called for:
        # 1. Original file permission during copy (<EMAIL> should be included)
        # 2. Template permissions that aren't filtered out (user1, user2, and owner converted to writer)
        self.assertEqual(mock_permissions.create.call_count, 4)

        # Get all create calls
        create_calls = mock_permissions.create.call_args_list

        # Verify that all create calls have supportsAllDrives=True and sendNotificationEmail=False
        for call in create_calls:
            call_kwargs = call[1]
            self.assertTrue(call_kwargs["supportsAllDrives"])
            self.assertFalse(call_kwargs["sendNotificationEmail"])

        # Verify specific permissions were created for template copy
        template_permission_calls = [
            call for call in create_calls if call[1]["fileId"] == "tofu_copy_id"
        ]

        # Should have 3 template permission calls (user1, user2, and owner converted to writer)
        template_calls_for_template_perms = [
            call
            for call in template_permission_calls
            if call[1]["body"].get("emailAddress")
            in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        ]
        self.assertEqual(len(template_calls_for_template_perms), 3)

        # Verify that the owner permission was converted to writer role
        owner_permission_call = next(
            (
                call
                for call in template_permission_calls
                if call[1]["body"].get("emailAddress") == "<EMAIL>"
            ),
            None,
        )
        self.assertIsNotNone(owner_permission_call)
        self.assertEqual(owner_permission_call[1]["body"]["role"], "writer")

        # Verify that service account and flashdocs permissions were filtered out
        excluded_emails = [
            "<EMAIL>",
            "<EMAIL>",
        ]
        for call in create_calls:
            email = call[1]["body"].get("emailAddress")
            self.assertNotIn(
                email,
                excluded_emails,
                f"Excluded email {email} should not have been granted permission",
            )

        # Verify that permission bodies don't contain 'id' or 'kind' fields
        for call in create_calls:
            permission_body = call[1]["body"]
            self.assertNotIn("id", permission_body)
            self.assertNotIn("kind", permission_body)

    @patch("api.gdrive_file_ops.copy_google_drive_file_with_retry")
    def test_create_tofu_owned_copy_without_template(self, mock_copy_file):
        """Test creating a Tofu-owned copy without template permissions."""
        from ..gdrive_file_ops import copy_google_drive_file_with_retry

        original_url = "https://docs.google.com/presentation/d/original_id/edit"
        tofu_copy_url = "https://docs.google.com/presentation/d/tofu_copy_id/edit"

        # Mock the copy operation
        mock_copy_file.return_value = tofu_copy_url

        # Execute
        result = copy_google_drive_file_with_retry(
            original_url, name="Generated Slides"
        )

        # Assert
        self.assertEqual(result, tofu_copy_url)
        mock_copy_file.assert_called_once_with(original_url, name="Generated Slides")

    @patch("api.gdrive_file_ops.service_account.Credentials.from_service_account_file")
    @patch("api.gdrive_file_ops.googleapiclient.discovery.build")
    @patch("api.gdrive_file_ops.extract_google_drive_file_id")
    @patch("api.gdrive_file_ops.retry_operation")
    @patch("api.gdrive_file_ops._delete_google_drive_file")
    def test_copy_google_drive_file_with_retry_orphan_cleanup(
        self,
        mock_delete_file,
        mock_retry_operation,
        mock_extract_id,
        mock_build,
        mock_credentials,
    ):
        """Test that copy_google_drive_file_with_retry cleans up orphaned files on failure."""
        from ..gdrive_file_ops import copy_google_drive_file_with_retry

        # Test data
        original_url = "https://docs.google.com/presentation/d/original_id/edit"
        mock_extract_id.return_value = "original_id"

        # Mock credentials
        mock_creds = MagicMock()
        mock_credentials.return_value = mock_creds

        # Mock Google Drive service
        mock_drive = MagicMock()
        mock_build.return_value = mock_drive

        # Mock files service
        mock_files = MagicMock()
        mock_drive.files.return_value = mock_files

        # Mock get operation to return file name
        mock_get_request = MagicMock()
        mock_get_request.execute.return_value = {"name": "Original Presentation"}
        mock_files.get.return_value = mock_get_request

        # Mock copy operation - succeeds
        mock_copy_request = MagicMock()
        mock_copy_request.execute.return_value = {"id": "new_file_id"}
        mock_files.copy.return_value = mock_copy_request

        # Mock permissions service - will fail during permission application
        mock_permissions = MagicMock()
        mock_drive.permissions.return_value = mock_permissions

        # Mock permissions list to return some permissions
        mock_list_request = MagicMock()
        mock_list_request.execute.return_value = {
            "permissions": [
                {
                    "id": "permission1",
                    "type": "user",
                    "role": "writer",
                    "emailAddress": "<EMAIL>",
                }
            ]
        }
        mock_permissions.list.return_value = mock_list_request

        # Mock permissions create to fail
        mock_permissions.create.side_effect = Exception("Permission creation failed")

        # Mock retry_operation to execute the function directly (simulating retry failure)
        def mock_retry_side_effect(operation, **kwargs):
            try:
                return operation()
            except Exception as e:
                # Simulate that retry_operation exhausts retries and raises the final exception
                raise e

        mock_retry_operation.side_effect = mock_retry_side_effect

        # Execute and expect exception
        with self.assertRaises(Exception) as context:
            copy_google_drive_file_with_retry(original_url, name="Generated Slides")

        self.assertEqual(str(context.exception), "Permission creation failed")

        # Verify that cleanup was called for the orphaned file
        mock_delete_file.assert_called_with("new_file_id", mock_drive)
