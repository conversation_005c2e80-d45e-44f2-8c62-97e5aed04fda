from unittest.mock import patch

from api.campaign_gen import CampaignGenerator
from api.models import Campaign, Content, ContentGroup, TofuUser, UserCreditAdjustment
from api.tofu_lite import add_credit_adjustment_to_user
from django.test import TestCase


class TofuLiteTests(TestCase):
    def setUp(self):
        self.user = TofuUser.objects.create(
            username="test_user",
            customer_type=TofuUser.CustomerType.LITE,
            credits_available=5,
        )
        self.regular_user = TofuUser.objects.create(
            username="regular_user",
            customer_type=TofuUser.CustomerType.NORMAL,
        )

        # Create campaign.
        self.campaign = Campaign.objects.create()
        # Create content groups
        self.lite_group = ContentGroup.objects.create(
            campaign=self.campaign,
            content_group_params={"tofu_lite": True},
        )
        self.regular_group = ContentGroup.objects.create(
            campaign=self.campaign, content_group_params={"tofu_lite": False}
        )

        # Create contents
        self.lite_content = Content.objects.create(content_group=self.lite_group)
        self.regular_content = Content.objects.create(content_group=self.regular_group)

    def test_add_credit_adjustment(self):
        initial_credits = self.user.credits_available
        credit_adjustment = 10

        add_credit_adjustment_to_user(self.user.id, credit_adjustment, "test_payment")

        # Refresh user from database
        self.user.refresh_from_db()

        self.assertEqual(
            self.user.credits_available, initial_credits + credit_adjustment
        )
        self.assertTrue(
            UserCreditAdjustment.objects.filter(
                user=self.user,
                credit_adjustment=credit_adjustment,
                payment_info="test_payment",
            ).exists()
        )

    @patch.object(CampaignGenerator, "gen_content_collection")
    @patch.object(CampaignGenerator, "gen_runner")
    def test_campaign_gen_credit_deduction(self, mock_gen_runner, mock_gen_collection):
        # Setup
        mock_gen_runner.return_value = ["content_1", "content_2"]
        mock_gen_collection.return_value = None

        initial_credits = self.user.credits_available
        generator = CampaignGenerator(self.campaign)
        contents_to_update = {self.lite_group.id: ["some_content_id"]}

        # Execute generation
        generated_ids = generator.gen(
            user_id=self.user.id,
            contents_to_update=contents_to_update,
            collection_ids=[],
            cache_key="test_key",
            joint_generation=False,
        )

        # Verify credit deduction
        self.user.refresh_from_db()
        self.assertEqual(
            self.user.credits_available, initial_credits - len(generated_ids)
        )

        # Verify credit adjustment record
        adjustment = UserCreditAdjustment.objects.filter(
            user=self.user, credit_adjustment=-len(generated_ids)
        ).first()
        self.assertIsNotNone(adjustment)
        self.assertEqual(
            adjustment.additional_info, {"content_gen": ["content_1", "content_2"]}
        )
