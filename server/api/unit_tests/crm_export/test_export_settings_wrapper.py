import pytest
from api.shared_types import ContentType
from api.sync.utils.export_settings_wrapper import ExportSettingsWrapper


class DummyContentGroup:
    def __init__(self, export_settings):
        self.content_group_params = {
            "export_settings": export_settings,
            "content_type": ContentType.EmailMarketing,
        }


@pytest.fixture
def base_export_settings():
    return {
        "hubspot": {
            "email": {
                "dynamic": {
                    "targetsSetting": [],
                }
            }
        },
        "exportType": "dynamic",
        "exportDestination": "hubspot",
    }


def make_wrapper(targets_setting):
    export_settings = {
        "hubspot": {
            "email": {
                "dynamic": {
                    "targetsSetting": targets_setting,
                }
            }
        },
        "exportType": "dynamic",
        "exportDestination": "hubspot",
    }
    return ExportSettingsWrapper(DummyContentGroup(export_settings))


def test_append_new_target():
    wrapper = make_wrapper([])
    new_target = {"hubIds": {"id": 1}, "contentId": 100, "foo": "bar"}
    wrapper.update_or_append_target_settings(new_target)
    assert wrapper.target_settings == [new_target]


def test_update_existing_target():
    existing = {"hubIds": {"id": 1}, "contentId": 100, "foo": "old"}
    wrapper = make_wrapper([existing])
    new_target = {"hubIds": {"id": 1}, "contentId": 100, "foo": "new"}
    wrapper.update_or_append_target_settings(new_target)
    assert wrapper.target_settings == [new_target]


def test_no_update_if_only_hubids_match():
    existing = {"hubIds": {"id": 1}, "contentId": 100, "foo": "old"}
    wrapper = make_wrapper([existing])
    new_target = {"hubIds": {"id": 1}, "contentId": 200, "foo": "new"}
    wrapper.update_or_append_target_settings(new_target)
    assert len(wrapper.target_settings) == 2
    assert existing in wrapper.target_settings
    assert new_target in wrapper.target_settings
