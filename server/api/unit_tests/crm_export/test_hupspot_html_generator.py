from unittest.mock import patch

import pytest
from api.models import Campaign, CompanyInfo, ContentGroup, Playbook, TofuUser
from api.sync.crm_export.html_generator.hubspot_html_helpers import HubspotHtmlGenerator


@pytest.mark.django_db
def test_generate_html_and_subject_real_group():
    # Create all required objects
    user = TofuUser.objects.create(username="<EMAIL>")
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    campaign = Campaign.objects.create(
        creator=user, playbook=playbook, campaign_name="Zaicheng Export Testing"
    )
    components = {
        "5Laleee_FIEZXS7d": {
            "text": "- Save time on booking trips\n- Always get your favorite seats and rooms\n- Change plans quickly if something comes up"
        },
        "7iqIR2O-NY1yrxN8": {
            "text": "\nOtto, our AI travel helper, could make business travel easier for you. It learns what you like and books everything for you. No more spending hours looking at different websites or worrying about finding the right hotel. "
        },
        "F00vcR_CQL_Y4pu6": {"text": "Happy planning! 😊"},
        "Ozdetzmh7gFg00mY": {
            "text": "I saw your exciting plans for a Beauty Café in Spring 2025. That's so cool! 🎉 Mixing coffee and beauty services sounds like a fun way to pamper clients."
        },
        "Y0zU4GjqrYHKygm2": {"text": "Simplify L'Amor's business travel 🧳"},
        "i2yzyjBpwlIvVoTb": {"text": "L'Amor"},
    }
    content_group_params = {
        "content_goal": "Personalization",
        "content_type": "Email - SDR",
        "content_source_copy": "/api/web/storage/s3-presigned-url?file=acf6fe39-8847-2c17-a04d-bc9a67383b8b.json&fileType=application/json&directory=tofu-uploaded-files",
        "content_source_format": "Text",
        "export_settings": {
            "hubspot": {
                "email": {
                    "dynamic": {
                        "exportType": "dynamic",
                        "destination": "hubspot",
                        "targetsSetting": [
                            {
                                "hubIds": {
                                    "hubspot_record_id": "***********",
                                    "hubspot_object_type": "company",
                                },
                                "emailName": "Zaicheng Export Testing",
                            }
                        ],
                        "advancedSetting": {
                            "emailType": "automated",
                            "emailFooter": True,
                        },
                        "componentsSetting": {
                            "5Laleee_FIEZXS7d": "tofu_email_322307_2",
                            "7iqIR2O-NY1yrxN8": "tofu_email_322307_4",
                            "F00vcR_CQL_Y4pu6": "tofu_email_322307_3",
                            "Ozdetzmh7gFg00mY": "tofu_email_322307_6",
                            "Y0zU4GjqrYHKygm2": "tofu_email_322307_1",
                            "i2yzyjBpwlIvVoTb": "tofu_email_322307_5",
                        },
                    }
                }
            },
            "exportType": "dynamic",
            "exportDestination": "hubspot",
        },
    }
    content_group = ContentGroup.objects.create(
        creator=user,
        campaign=campaign,
        content_group_name="Zaicheng Export Testing",
        content_group_params=content_group_params,
        components=components,
    )
    components_setting = {
        "5Laleee_FIEZXS7d": "tofu_email_322307_2",
        "7iqIR2O-NY1yrxN8": "tofu_email_322307_4",
        "F00vcR_CQL_Y4pu6": "tofu_email_322307_3",
        "Ozdetzmh7gFg00mY": "tofu_email_322307_6",
        "Y0zU4GjqrYHKygm2": "tofu_email_322307_1",
        "i2yzyjBpwlIvVoTb": "tofu_email_322307_5",
    }

    def fake_get_s3_file(source):
        import json

        slate = [
            {
                "type": "subject-line",
                "children": [
                    {
                        "type": "paragraph",
                        "children": [
                            {"text": ""},
                            {
                                "type": "selected-component",
                                "id": "Y0zU4GjqrYHKygm2",
                                "children": [
                                    {"text": "Simplify L'Amor's business travel 🧳"}
                                ],
                            },
                            {"text": ""},
                        ],
                    }
                ],
            },
            {
                "type": "body",
                "children": [
                    {
                        "type": "paragraph",
                        "children": [
                            {"text": "Hey "},
                            {
                                "type": "selected-component",
                                "id": "i2yzyjBpwlIvVoTb",
                                "children": [{"text": "L'Amor"}],
                            },
                            {"text": " team! 👋\n\n"},
                            {
                                "type": "selected-component",
                                "id": "Ozdetzmh7gFg00mY",
                                "children": [
                                    {
                                        "text": "I saw your exciting plans for a Beauty Café in Spring 2025. That's so cool! 🎉 Mixing coffee and beauty services sounds like a fun way to pamper clients."
                                    }
                                ],
                            },
                            {
                                "text": "\n\nWith all the work you're doing to grow your business, I bet planning work trips can be a real headache. 😓 Booking flights and hotels takes time away from important stuff, like picking the perfect coffee beans for your café!\n"
                            },
                            {
                                "type": "selected-component",
                                "id": "7iqIR2O-NY1yrxN8",
                                "children": [
                                    {
                                        "text": "Otto, our AI travel helper, could make business travel easier for you. It learns what you like and books everything for you. No more spending hours looking at different websites or worrying about finding the right hotel."
                                    }
                                ],
                            },
                            {"text": "\n\nOtto could help you:\n"},
                            {
                                "type": "selected-component",
                                "id": "5Laleee_FIEZXS7d",
                                "children": [
                                    {
                                        "text": "- Save time on booking trips\n- Always get your favorite seats and rooms\n- Change plans quickly if something comes up"
                                    }
                                ],
                            },
                            {
                                "text": "\n\nWould you want to try Otto for free for a year? It might make planning work trips as easy as making a perfect latte! ☕\n\nLet me know if you'd like to chat more about how Otto could help L'Amor's travel needs.\n\n"
                            },
                            {
                                "type": "selected-component",
                                "id": "F00vcR_CQL_Y4pu6",
                                "children": [{"text": "Happy planning! 😊"}],
                            },
                            {"text": ""},
                        ],
                    }
                ],
            },
        ]

        return json.dumps(slate)

    with patch(
        "api.sync.crm_export.html_generator.html_utils.get_s3_file",
        side_effect=fake_get_s3_file,
    ):
        generator = HubspotHtmlGenerator(
            content_group=content_group,
            components_setting=components_setting,
            hub_object_type="company",
            content_source_format="Text",
        )
        html, subject = generator.generate_html_and_subject()
        assert html
        assert subject
        assert "personalization_token" in html or "company." in html
        assert "company.tofu_email" in subject
