from unittest.mock import MagicMock, patch

import pytest
from api.models import (
    Campaign,
    CompanyInfo,
    Content,
    ContentGroup,
    Playbook,
    TargetInfo,
    TargetInfoGroup,
    TofuUser,
)
from api.shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from api.sync.crm_export.crm_export_hubspot import HubspotEmailExportHandler
from django.core.cache import cache


@pytest.mark.django_db
def test_hubspot_email_export_handler_export_methods_comprehensive():
    cache.clear()
    # Create all required objects
    user = TofuUser.objects.create(username="<EMAIL>")
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook", company_object=company_info
    )
    campaign = Campaign.objects.create(
        creator=user, playbook=playbook, campaign_name="Zaicheng Export Testing"
    )
    components = {
        "comp1": {"text": "Component 1 text"},
        "comp2": {"text": "Component 2 text"},
    }
    content_group_params = {
        "content_goal": "Personalization",
        "content_type": "Email - SDR",
        "export_settings": {
            "hubspot": {
                "email": {
                    "dynamic": {
                        "exportType": "dynamic",
                        "destination": "hubspot",
                        "targetsSetting": [
                            {
                                "hubIds": {
                                    "hubspot_record_id": "21733644134",
                                    "hubspot_object_type": "company",
                                },
                                "emailName": "Zaicheng Export Testing",
                            }
                        ],
                        "advancedSetting": {
                            "emailType": "automated",
                            "emailFooter": True,
                        },
                        "componentsSetting": {
                            "comp1": "tofu_email_322307_1",
                            "comp2": "tofu_email_322307_2",
                        },
                    }
                }
            },
            "exportType": "dynamic",
            "exportDestination": "hubspot",
        },
    }
    content_group = ContentGroup.objects.create(
        creator=user,
        campaign=campaign,
        content_group_name="Zaicheng Export Testing",
        content_group_params=content_group_params,
        components=components,
    )

    # Create TargetInfoGroup and TargetInfo for the test
    target_info_group = TargetInfoGroup.objects.create(
        playbook=playbook,
        target_info_group_key="list1",
        meta={},
    )
    TargetInfo.objects.create(
        target_info_group=target_info_group,
        target_key="target1",
        meta={
            "hubspot_record_id": "21733644134",
            "hubspot_object_type": "company",
        },
    )

    # Add a Content object to the content group, referencing the above target
    Content.objects.create(
        creator=user,
        playbook=playbook,
        content_group=content_group,
        content_name="Test Content",
        content_params={"targets": {"list1": "target1"}},
    )

    # Patch ParagonWrapper and HubspotIntegration to avoid real external calls
    with (
        patch("api.paragon_wrapper.ParagonWrapper") as MockParagonWrapper,
        patch(
            "api.sync.integration_sync.hubspot_integration.HubspotIntegration"
        ) as MockHubspotIntegration,
    ):
        # Setup mocks
        mock_paragon = MockParagonWrapper.return_value
        mock_agent = MagicMock()
        mock_paragon.get_hubspot_agent.return_value = mock_agent

        handler = HubspotEmailExportHandler(
            playbook_id=playbook.id, source_platform=PlatformType.PLATFORM_TYPE_HUBSPOT
        )

        # Test _create_export_settings
        export_settings = handler.create_or_update_export_settings(content_group)
        assert export_settings is not None
        assert "hubspot" in export_settings
        assert "email" in export_settings["hubspot"]
        assert "dynamic" in export_settings["hubspot"]["email"]
        dynamic_settings = export_settings["hubspot"]["email"]["dynamic"]
        # Verify structure and content
        assert dynamic_settings["exportType"] == "dynamic"
        assert dynamic_settings["destination"] == "hubspot"
        assert isinstance(dynamic_settings["componentsSetting"], dict)
        assert set(dynamic_settings["componentsSetting"].keys()) == set(
            components.keys()
        )
        assert isinstance(dynamic_settings["targetsSetting"], list)
        assert len(dynamic_settings["targetsSetting"]) >= 1
        for target in dynamic_settings["targetsSetting"]:
            assert "contentId" in target or "emailName" in target
        assert isinstance(dynamic_settings["advancedSetting"], dict)
        assert dynamic_settings["advancedSetting"].get("emailType") == "automated"
        # Should update content_group as well
        content_group.refresh_from_db()
        assert "export_settings" in content_group.content_group_params
        # The exportType and exportDestination at the top level
        assert export_settings["exportType"] == "dynamic"
        assert export_settings["exportDestination"] == "hubspot"

        # Test _extract_and_validate_export_settings
        components_setting, targets_setting, advanced_setting, hub_object_type = (
            handler._extract_and_validate_export_settings(content_group)
        )
        assert isinstance(components_setting, dict)
        assert isinstance(targets_setting, list)
        assert isinstance(advanced_setting, dict)
        assert isinstance(hub_object_type, str)
        assert hub_object_type == "company"

        # Patch only actual external calls for export_new/export_existing
        with (
            patch.object(
                handler, "_create_crm_group_and_properties", return_value=None
            ) as mock_crm_group,
            patch.object(
                handler, "do_export_existing", return_value=None
            ) as mock_update_content_group,
            patch.object(
                handler, "_update_draft_url", return_value="http://fake-draft-url"
            ) as mock_update_draft_url,
            patch.object(
                handler,
                "_compare_email_template_input",
                return_value={
                    "html_content": "<html></html>",
                    "email_file_name": "file.html",
                    "email_subject": "subject",
                    "email_type": "automated",
                    "source": "BE",
                },
            ) as mock_compare_email_template_input,
            patch.object(handler, "integration", create=True) as mock_integration_obj,
        ):
            mock_integration_obj.create_marketing_email.return_value = {"id": 123}
            # Should not raise
            handler.export_new(content_group)
            handler.export_existing(content_group)
            # Ensure our internal methods were called
            mock_crm_group.assert_called()
            mock_update_content_group.assert_called()
            mock_update_draft_url.assert_called()
            mock_compare_email_template_input.assert_called()
            assert mock_integration_obj.create_marketing_email.called


@pytest.mark.django_db
def test_hubspot_email_export_handler_export_existing():
    cache.clear()
    # Setup required objects
    user = TofuUser.objects.create(username="<EMAIL>")
    company_info = CompanyInfo.objects.create()
    playbook = Playbook.objects.create(
        name="Test Playbook 2", company_object=company_info
    )
    campaign = Campaign.objects.create(
        creator=user, playbook=playbook, campaign_name="Zaicheng Export Testing 2"
    )
    components = {
        "comp1": {"text": "Component 1 text"},
        "comp2": {"text": "Component 2 text"},
    }
    content_group_params = {
        "content_goal": "Personalization",
        "content_type": "Email - SDR",
        "export_settings": {
            "hubspot": {
                "email": {
                    "dynamic": {
                        "exportType": "dynamic",
                        "destination": "hubspot",
                        "targetsSetting": [
                            {
                                "hubIds": {
                                    "hubspot_record_id": "21733644134",
                                    "hubspot_object_type": "company",
                                },
                                "emailName": "Zaicheng Export Testing 2",
                            }
                        ],
                        "advancedSetting": {
                            "emailType": "automated",
                            "emailFooter": True,
                        },
                        "componentsSetting": {
                            "comp1": "tofu_email_322308_1",
                            "comp2": "tofu_email_322308_2",
                        },
                    }
                }
            },
            "exportType": "dynamic",
            "exportDestination": "hubspot",
        },
    }
    content_group = ContentGroup.objects.create(
        creator=user,
        campaign=campaign,
        content_group_name="Zaicheng Export Testing 2",
        content_group_params=content_group_params,
        components=components,
    )
    # Patch ContentGroupHubspotSyncer and _extract_and_validate_export_settings
    with (
        patch(
            "api.sync.crm_export.crm_export_hubspot.ContentGroupHubspotSyncer"
        ) as MockSyncer,
        patch.object(
            HubspotEmailExportHandler, "_extract_and_validate_export_settings"
        ) as mock_extract,
    ):
        mock_syncer_instance = MockSyncer.return_value
        mock_syncer_instance.get_unexported_contents.return_value = [
            {"id": "content1"},
            {"id": "content2"},
        ]
        mock_syncer_instance.export.return_value = None
        # Return values for _extract_and_validate_export_settings
        mock_components_setting = {
            "comp1": "tofu_email_322308_1",
            "comp2": "tofu_email_322308_2",
        }
        mock_targets_setting = [{"hubIds": {"hubspot_object_type": "company"}}]
        mock_advanced_setting = {"emailType": "automated"}
        mock_hub_object_type = "company"
        mock_extract.return_value = (
            mock_components_setting,
            mock_targets_setting,
            mock_advanced_setting,
            mock_hub_object_type,
        )
        handler = HubspotEmailExportHandler(
            playbook_id=playbook.id, source_platform=PlatformType.PLATFORM_TYPE_HUBSPOT
        )
        # Should not raise
        handler.export_existing(content_group)
        handler.export_existing(content_group)
        # Assert that ContentGroupHubspotSyncer was called with correct args
        MockSyncer.assert_called_with(
            content_group,
            record_type=mock_hub_object_type,
            record_type_plural=mock_hub_object_type,
        )
        # Assert get_unexported_contents and export were called
        assert mock_syncer_instance.get_unexported_contents.called
        assert mock_syncer_instance.export.called
