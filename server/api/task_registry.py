from enum import Enum


class GenerationGoal(Enum):
    GENERATION = "generation"
    GENERATION_REPURPOSING = "generation_repurposing"
    CHATBOT = "chatbot"
    EXPANSION = "expansion"
    PDF_MULTIMODAL = "pdf_multimodal"
    TRANSCRIPTION_POSTPROCESSING = "transcription_postprocessing"
    REFINE_SUMMARIZATION = "refine_summarization"
    EVALUATION = "evaluation"
    IMAGE_GENERATION = "image_generation"
    CONNECTED_SOURCE_REFRESH = "connected_source_refresh"
    CONNECTED_SOURCE_SITE_MAP_DISCOVERY = "connected_source_site_map_discovery"


class TaskRegistry:
    task_registry = {
        GenerationGoal.IMAGE_GENERATION: {
            "budget_limit": 1048576,
            "model_config": {
                "gemini-2.0-flash-exp-image-generation": {
                    "model_name": "gemini-2.0-flash-exp-image-generation",
                },
                "gpt-image-1": {
                    "model_name": "gpt-image-1",
                    "max_retries": 3,
                    "request_timeout": 300,
                    "n": 1,
                    "quality": "auto",
                },
            },
            "model_fallback": {
                "gemini-2.0-flash-exp-image-generation": [],
                "gpt-image-1": [],
            },
            "default_model": "gpt-image-1",
            "internal_default_model": "gpt-image-1",
        },
        GenerationGoal.EVALUATION: {
            "budget_limit": 30000,
            "model_config": {
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "claude-3-7-sonnet-20250219": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "claude-3-7-sonnet-20250219-thinking": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 0,
                    "max_tokens": 20000,
                    "max_tokens_to_sample": 20000,
                    "thinking": {
                        "type": "enabled",
                        "budget_tokens": 16000,
                    },
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "gpt-4o-2024-11-20": {
                    "model_name": "gpt-4o-2024-11-20",
                    "temperature": 0,
                    "n": 1,
                    "max_retries": 3,
                },
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20241022": {
                    "model_name": "claude-3-5-sonnet-20241022",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
            },
            "model_fallback": {
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620",
                    "gpt-4o-2024-11-20",
                ],
                "claude-3-5-sonnet-20240620": [],
                "gpt-4o-2024-11-20": ["us.anthropic.claude-3-5-sonnet-20240620-v1:0"],
                "claude-3-5-sonnet-20241022": [],
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": [
                    "claude-3-5-sonnet-20241022"
                ],
                "claude-3-7-sonnet-20250219": [],
                "claude-3-7-sonnet-20250219-thinking": [],
            },
            "default_model": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
            "internal_default_model": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        },
        GenerationGoal.TRANSCRIPTION_POSTPROCESSING: {
            "budget_limit": 128000,
            "model_config": {
                "gpt-4o-mini-2024-07-18": {
                    "model_name": "gpt-4o-mini-2024-07-18",
                    "temperature": 0,
                    "max_tokens": None,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "gpt-4o-2024-11-20": {
                    "model_name": "gpt-4o-2024-11-20",
                    "temperature": 0,
                    "max_tokens": None,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4.1-mini-2025-04-14": {
                    "model_name": "gpt-4.1-mini-2025-04-14",
                    "temperature": 0,
                    "max_tokens": None,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
            },
            "model_fallback": {
                "gpt-4o-mini-2024-07-18": [
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
                ],
                "claude-3-5-sonnet-20240620": [
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
                ],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620"
                ],
                "gpt-4o-2024-11-20": ["us.anthropic.claude-3-5-sonnet-20240620-v1:0"],
                "gpt-4.1-mini-2025-04-14": ["gpt-4o-2024-11-20"],
            },
            "default_model": "gpt-4.1-mini-2025-04-14",
            "internal_default_model": "gpt-4.1-mini-2025-04-14",
        },
        GenerationGoal.REFINE_SUMMARIZATION: {
            "budget_limit": 128000,
            "model_config": {
                "gpt-4o-mini-2024-07-18": {
                    "model_name": "gpt-4o-mini-2024-07-18",
                    "temperature": 0,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": None,
                    "request_timeout": 300,
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
            },
            "model_fallback": {
                "gpt-4o-mini-2024-07-18": [
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
                ],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [],
            },
            "default_model": "gpt-4o-mini-2024-07-18",
            "internal_default_model": "gpt-4o-mini-2024-07-18",
        },
        GenerationGoal.PDF_MULTIMODAL: {
            "budget_limit": 200000,
            "model_config": {
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 0,
                    "max_tokens_to_sample": 8192,
                    "max_tokens": 8192,
                    "extra_headers": {
                        "anthropic-beta": "max-tokens-3-5-sonnet-2024-07-15,prompt-caching-2024-07-31"
                    },
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "gpt-4o-mini-2024-07-18": {
                    "budget_limit": 128000,
                    "model_name": "gpt-4o-mini-2024-07-18",
                    "temperature": 0,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
            },
            "model_fallback": {
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620",
                    "gpt-4o-mini-2024-07-18",
                ],
                "claude-3-5-sonnet-20240620": ["gpt-4o-mini-2024-07-18"],
                "gpt-4o-mini-2024-07-18": [],
            },
            "default_model": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
            "internal_default_model": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
        },
        GenerationGoal.EXPANSION: {
            "budget_limit": 128000,
            "model_config": {
                "gpt-4o-2024-11-20": {
                    "model_name": "gpt-4o-2024-11-20",
                    "temperature": 0,
                    "n": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20241022": {
                    "model_name": "claude-3-5-sonnet-20241022",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "claude-3-7-sonnet-20250219": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {
                    "model_name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
            },
            "model_fallback": {
                "gpt-4o-2024-11-20": ["us.anthropic.claude-3-5-sonnet-20240620-v1:0"],
                "claude-3-5-sonnet-20240620": [
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
                ],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620"
                ],
                "claude-3-5-sonnet-20241022": [
                    "us.anthropic.claude-3-5-sonnet-20241022-v2:0"
                ],
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": [
                    "claude-3-5-sonnet-20241022"
                ],
                "claude-3-7-sonnet-20250219": [
                    "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
                ],
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": [
                    "claude-3-7-sonnet-20250219"
                ],
            },
            "default_model": "gpt-4o-2024-11-20",
            "internal_default_model": "gpt-4o-2024-11-20",
        },
        GenerationGoal.CHATBOT: {
            "budget_limit": 200000,
            "model_config": {
                "gpt-4o-2024-11-20": {
                    "budget_limit": 128000,
                    "model_name": "gpt-4o-2024-11-20",
                    "temperature": 1,
                    "max_tokens": None,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0": {
                    "model_name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "temperature": 1,
                    "max_tokens": 20000,
                    "thinking": {
                        "type": "enabled",
                        "budget_tokens": 16000,
                    },
                },
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {
                    "model_name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "claude-3-7-sonnet-20250219": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "claude-3-7-sonnet-20250219-thinking": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 1,
                    "max_tokens": 20000,
                    "max_tokens_to_sample": 20000,
                    "thinking": {
                        "type": "enabled",
                        "budget_tokens": 16000,
                    },
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "claude-3-5-sonnet-20241022": {
                    "model_name": "claude-3-5-sonnet-20241022",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "deepseek-chat": {
                    "budget_limit": 64000,
                    "model_name": "deepseek-chat",
                    "temperature": 1.3,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": 8192,
                },
                "deepseek-reasoner": {
                    "budget_limit": 64000,
                    "model_name": "deepseek-reasoner",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": 8192,
                },
                "gemini-2.0-flash-exp": {
                    "budget_limit": 1048576,
                    "model_name": "gemini-2.0-flash-exp",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "o1-2024-12-17": {
                    "budget_limit": 128000,
                    "model_name": "o1-2024-12-17",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "o3-2025-04-16": {
                    "budget_limit": 128000,
                    "model_name": "o3-2025-04-16",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "o3-mini-2025-01-31": {
                    "budget_limit": 200000,
                    "model_name": "o3-mini-2025-01-31",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4.5-preview": {
                    "model_name": "gpt-4.5-preview",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                    "n": 1,
                },
                "o4-mini-2025-04-16": {
                    "budget_limit": 200000,
                    "model_name": "o4-mini-2025-04-16",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4.1-2025-04-14": {
                    "model_name": "gpt-4.1-2025-04-14",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                    "n": 1,
                },
                "claude-sonnet-4-20250514": {
                    "model_name": "claude-sonnet-4-20250514",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "claude-opus-4-20250514": {
                    "model_name": "claude-opus-4-20250514",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-sonnet-4-20250514-v1:0": {
                    "model_name": "us.anthropic.claude-sonnet-4-20250514-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "us.anthropic.claude-opus-4-20250514-v1:0": {
                    "model_name": "us.anthropic.claude-opus-4-20250514-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
            },
            "model_fallback": {
                "gpt-4o-2024-11-20": ["us.anthropic.claude-3-5-sonnet-20240620-v1:0"],
                "claude-3-5-sonnet-20240620": [],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620"
                ],
                "claude-3-5-sonnet-20241022": [],
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": [
                    "claude-3-5-sonnet-20241022"
                ],
                "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0": [
                    "claude-3-7-sonnet-20250219-thinking"
                ],
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": [
                    "claude-3-7-sonnet-20250219"
                ],
                "deepseek-chat": [],
                "deepseek-reasoner": [],
                "gemini-2.0-flash-exp": [],
                "o1-2024-12-17": [],
                "o3-mini-2025-01-31": [],
                "o3-2025-04-16": [],
                "o4-mini-2025-04-16": [],
                "claude-3-7-sonnet-20250219": [],
                "claude-3-7-sonnet-20250219-thinking": [],
                "gpt-4.5-preview": [],
                "gpt-4.1-2025-04-14": [],
                "claude-sonnet-4-20250514": [
                    "us.anthropic.claude-sonnet-4-20250514-v1:0"
                ],
                "claude-opus-4-20250514": ["us.anthropic.claude-opus-4-20250514-v1:0"],
                "us.anthropic.claude-sonnet-4-20250514-v1:0": [
                    "claude-sonnet-4-20250514"
                ],
                "us.anthropic.claude-opus-4-20250514-v1:0": ["claude-opus-4-20250514"],
            },
            "default_model": "gpt-4o-2024-11-20",
            "internal_default_model": "gpt-4o-2024-11-20",
        },
        GenerationGoal.GENERATION_REPURPOSING: {
            "budget_limit": 200000,
            "model_config": {
                "gpt-4o-2024-11-20": {
                    "budget_limit": 128000,
                    "model_name": "gpt-4o-2024-11-20",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "claude-3-7-sonnet-20250219-thinking": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 1,
                    "max_tokens": 20000,
                    "max_tokens_to_sample": 20000,
                    "thinking": {
                        "type": "enabled",
                        "budget_tokens": 16000,
                    },
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "claude-3-7-sonnet-20250219": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "claude-3-5-sonnet-20241022": {
                    "model_name": "claude-3-5-sonnet-20241022",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {
                    "model_name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0": {
                    "model_name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "temperature": 1,
                    "max_tokens": 20000,
                    "thinking": {
                        "type": "enabled",
                        "budget_tokens": 16000,
                    },
                },
                "deepseek-chat": {
                    "budget_limit": 64000,
                    "model_name": "deepseek-chat",
                    "temperature": 1.3,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": 8192,
                },
                "deepseek-reasoner": {
                    "budget_limit": 64000,
                    "model_name": "deepseek-reasoner",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": 8192,
                },
                "gemini-2.0-flash-exp": {
                    "budget_limit": 1048576,
                    "model_name": "gemini-2.0-flash-exp",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "o1-2024-12-17": {
                    "model_name": "o1-2024-12-17",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "o3-mini-2025-01-31": {
                    "model_name": "o3-mini-2025-01-31",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "o3-2025-04-16": {
                    "model_name": "o3-2025-04-16",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4.5-preview": {
                    "model_name": "gpt-4.5-preview",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gemini-2.5-pro-preview-03-25": {
                    "model_name": "gemini-2.5-pro-preview-03-25",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "o4-mini-2025-04-16": {
                    "model_name": "o4-mini-2025-04-16",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4.1-2025-04-14": {
                    "model_name": "gpt-4.1-2025-04-14",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "claude-sonnet-4-20250514": {
                    "model_name": "claude-sonnet-4-20250514",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "claude-opus-4-20250514": {
                    "model_name": "claude-opus-4-20250514",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-sonnet-4-20250514-v1:0": {
                    "model_name": "us.anthropic.claude-sonnet-4-20250514-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "us.anthropic.claude-opus-4-20250514-v1:0": {
                    "model_name": "us.anthropic.claude-opus-4-20250514-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
            },
            "model_fallback": {
                "gpt-4o-2024-11-20": ["us.anthropic.claude-3-5-sonnet-20240620-v1:0"],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620"
                ],
                "claude-3-5-sonnet-20240620": [
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
                ],
                "claude-3-5-sonnet-20241022": [
                    "us.anthropic.claude-3-5-sonnet-20241022-v2:0"
                ],
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": [
                    "claude-3-5-sonnet-20241022"
                ],
                "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0": [
                    "claude-3-7-sonnet-20250219-thinking"
                ],
                "deepseek-chat": [],
                "deepseek-reasoner": [],
                "gemini-2.0-flash-exp": [],
                "o1-2024-12-17": [],
                "o3-mini-2025-01-31": [],
                "claude-3-7-sonnet-20250219": [],
                "claude-3-7-sonnet-20250219-thinking": [],
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": [
                    "claude-3-7-sonnet-20250219"
                ],
                "gpt-4.5-preview": [],
                "gemini-2.5-pro-preview-03-25": [],
                "o4-mini-2025-04-16": [],
                "gpt-4.1-2025-04-14": [],
                "o3-2025-04-16": [],
                "claude-sonnet-4-20250514": [
                    "us.anthropic.claude-sonnet-4-20250514-v1:0"
                ],
                "claude-opus-4-20250514": ["us.anthropic.claude-opus-4-20250514-v1:0"],
                "us.anthropic.claude-sonnet-4-20250514-v1:0": [
                    "claude-sonnet-4-20250514"
                ],
                "us.anthropic.claude-opus-4-20250514-v1:0": ["claude-opus-4-20250514"],
            },
            "default_model": "gpt-4o-2024-11-20",
            "internal_default_model": "gpt-4o-2024-11-20",
        },
        GenerationGoal.GENERATION: {
            "budget_limit": 30000,
            "model_config": {
                "gpt-4o-2024-11-20": {
                    "model_name": "gpt-4o-2024-11-20",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "claude-3-7-sonnet-20250219-thinking": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 1,
                    "max_tokens": 20000,
                    "max_tokens_to_sample": 20000,
                    "thinking": {
                        "type": "enabled",
                        "budget_tokens": 16000,
                    },
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "claude-3-7-sonnet-20250219": {
                    "model_name": "claude-3-7-sonnet-20250219",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                    "extra_headers": {"anthropic-beta": "output-128k-2025-02-19"},
                },
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {
                    "model_name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0": {
                    "model_name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "temperature": 1,
                    "max_tokens": 20000,
                    "thinking": {
                        "type": "enabled",
                        "budget_tokens": 16000,
                    },
                },
                "claude-3-5-sonnet-20241022": {
                    "model_name": "claude-3-5-sonnet-20241022",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "deepseek-chat": {
                    "model_name": "deepseek-chat",
                    "temperature": 1.3,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": 8192,
                },
                "deepseek-reasoner": {
                    "model_name": "deepseek-reasoner",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": 8192,
                },
                "gemini-2.0-flash-exp": {
                    "model_name": "gemini-2.0-flash-exp",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "o1-2024-12-17": {
                    "model_name": "o1-2024-12-17",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "o3-mini-2025-01-31": {
                    "model_name": "o3-mini-2025-01-31",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4.5-preview": {
                    "model_name": "gpt-4.5-preview",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gemini-2.5-pro-preview-03-25": {
                    "model_name": "gemini-2.5-pro-preview-03-25",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "o4-mini-2025-04-16": {
                    "model_name": "o4-mini-2025-04-16",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4.1-2025-04-14": {
                    "model_name": "gpt-4.1-2025-04-14",
                    "temperature": 1,
                    "max_tokens": None,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "o3-2025-04-16": {
                    "model_name": "o3-2025-04-16",
                    "temperature": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "claude-sonnet-4-20250514": {
                    "model_name": "claude-sonnet-4-20250514",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "claude-opus-4-20250514": {
                    "model_name": "claude-opus-4-20250514",
                    "temperature": 1,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "us.anthropic.claude-sonnet-4-20250514-v1:0": {
                    "model_name": "us.anthropic.claude-sonnet-4-20250514-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
                "us.anthropic.claude-opus-4-20250514-v1:0": {
                    "model_name": "us.anthropic.claude-opus-4-20250514-v1:0",
                    "temperature": 1,
                    "max_tokens": 8192,
                },
            },
            "model_fallback": {
                "gpt-4o-2024-11-20": ["us.anthropic.claude-3-5-sonnet-20240620-v1:0"],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620"
                ],
                "us.anthropic.claude-3-7-sonnet-20250219-v1:0": [
                    "claude-3-7-sonnet-20250219"
                ],
                "claude-3-5-sonnet-20240620": [
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
                ],
                "claude-3-5-sonnet-20241022": [
                    "us.anthropic.claude-3-5-sonnet-20241022-v2:0"
                ],
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0": [
                    "claude-3-5-sonnet-20241022"
                ],
                "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0": [
                    "claude-3-7-sonnet-20250219-thinking"
                ],
                "claude-3-7-sonnet-20250219": [],
                "claude-3-7-sonnet-20250219-thinking": [],
                "deepseek-chat": [],
                "deepseek-reasoner": [],
                "gemini-2.0-flash-exp": [],
                "o1-2024-12-17": [],
                "o3-mini-2025-01-31": [],
                "gpt-4.5-preview": [],
                "gemini-2.5-pro-preview-03-25": [],
                "o4-mini-2025-04-16": [],
                "gpt-4.1-2025-04-14": [],
                "o3-2025-04-16": [],
                "claude-sonnet-4-20250514": [
                    "us.anthropic.claude-sonnet-4-20250514-v1:0"
                ],
                "claude-opus-4-20250514": ["us.anthropic.claude-opus-4-20250514-v1:0"],
                "us.anthropic.claude-sonnet-4-20250514-v1:0": [
                    "claude-sonnet-4-20250514"
                ],
                "us.anthropic.claude-opus-4-20250514-v1:0": ["claude-opus-4-20250514"],
            },
            "default_model": "gpt-4o-2024-11-20",
            "internal_default_model": "gpt-4o-2024-11-20",
        },
        GenerationGoal.CONNECTED_SOURCE_REFRESH: {
            "budget_limit": 200000,
            "model_config": {
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
                "gpt-4o-mini-2024-07-18": {
                    "model_name": "gpt-4o-mini-2024-07-18",
                    "budget_limit": 128000,
                    "temperature": 0,
                    "n": 1,
                    "max_retries": 3,
                    "max_tokens": None,
                    "request_timeout": 300,
                },
                "us.anthropic.claude-3-5-haiku-20241022-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-haiku-20241022-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
            },
            "model_fallback": {
                "gpt-4o-mini-2024-07-18": [
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                ],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620",
                ],
                "claude-3-5-sonnet-20240620": [],
                "us.anthropic.claude-3-5-haiku-20241022-v1:0": [
                    "gpt-4o-mini-2024-07-18"
                ],
            },
            "default_model": "gpt-4o-mini-2024-07-18",
            "internal_default_model": "gpt-4o-mini-2024-07-18",
        },
        # o1-2024-12-17 only support temperature 1
        GenerationGoal.CONNECTED_SOURCE_SITE_MAP_DISCOVERY: {
            "budget_limit": 200000,
            "model_config": {
                "o1-2024-12-17": {
                    "model_name": "o1-2024-12-17",
                    "temperature": 1,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "gpt-4o-2024-11-20": {
                    "model_name": "gpt-4o-2024-11-20",
                    "temperature": 0,
                    "max_tokens": None,
                    "n": 1,
                    "max_retries": 3,
                    "request_timeout": 300,
                },
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
                    "model_name": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "temperature": 0,
                    "max_tokens": 8192,
                },
                "claude-3-5-sonnet-20240620": {
                    "model_name": "claude-3-5-sonnet-20240620",
                    "temperature": 0,
                    "max_tokens": 8192,
                    "max_tokens_to_sample": 8192,
                },
            },
            "model_fallback": {
                "o1-2024-12-17": [
                    "gpt-4o-2024-11-20",
                    "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
                ],
                "gpt-4o-2024-11-20": ["us.anthropic.claude-3-5-sonnet-20240620-v1:0"],
                "us.anthropic.claude-3-5-sonnet-20240620-v1:0": [
                    "claude-3-5-sonnet-20240620"
                ],
                "claude-3-5-sonnet-20240620": [],
            },
            "default_model": "o1-2024-12-17",
            "internal_default_model": "o1-2024-12-17",
        },
    }

    @classmethod
    def get_task_registry(cls, generation_goal):
        if generation_goal not in cls.task_registry:
            raise ValueError(
                f"Generation goal {generation_goal} not found in task registry"
            )
        return cls.task_registry[generation_goal]
