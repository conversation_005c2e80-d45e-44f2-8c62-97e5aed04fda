import logging

from django.apps import apps


class StatusHandler:
    @classmethod
    def get_model(cls):
        return apps.get_model("api", "Status")

    @classmethod
    def set_playbook_status(cls, playbook_instance, type, status):
        model = cls.get_model()
        model.objects.update_or_create(
            playbook=playbook_instance,
            type=type,
            defaults={"playbook": playbook_instance, "type": type, "status": status},
        )

    @classmethod
    def get_playbook_status_queryset(cls, playbook_instance):
        model = cls.get_model()
        return model.objects.filter(playbook=playbook_instance)
