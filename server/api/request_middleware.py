# middleware.py
import logging
import os
import threading
import uuid

import coverage
from django.conf import settings

from .models import Campaign, Content, ContentGroup, Playbook
from .thread_locals import (
    get_current_user,
    reset_current_campaign,
    reset_current_playbook,
    reset_current_user,
    set_current_campaign,
    set_current_playbook,
    set_current_user,
)

local_storage = threading.local()
global_coverage = None


class RequestMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        global global_coverage

        # RequestMiddleware functionality
        if not get_current_user():
            set_current_user(getattr(request, "user", None))

        object = getattr(request, "object", None)
        if object:
            playbook = None
            campaign = None

            if isinstance(object, Playbook):
                playbook = object
            elif isinstance(object, Campaign):
                campaign = object
                playbook = campaign.playbook
            elif isinstance(object, ContentGroup):
                campaign = object.campaign
                playbook = campaign.playbook
            elif isinstance(object, Content):
                campaign = object.content_group.campaign
                playbook = campaign.playbook

            set_current_playbook(playbook)
            set_current_campaign(campaign)

        coverage_active = False
        if request.headers.get("X-E2E-Test") == "true":
            coverage_flag = request.headers.get("X-Coverage-Flag", "")
            if coverage_flag:
                settings.COVERAGE_ENABLED = True
                settings.COVERAGE_FLAG = coverage_flag

                if global_coverage is None:
                    global_coverage = self.start_coverage(coverage_flag)
                coverage_active = True
            else:
                logging.error("Coverage flag not set when e2e test is enabled")

        try:
            response = self.get_response(request)
        finally:
            if coverage_active and global_coverage:
                self.stop_coverage(global_coverage)
            reset_current_campaign()
            reset_current_playbook()
            reset_current_user()

        return response

    def start_coverage(self, coverage_flag):
        unique_suffix = uuid.uuid4().hex
        cov = coverage.Coverage(data_file=f".coverage.{coverage_flag}.{unique_suffix}")
        cov.start()
        logging.info(f"Started coverage for flag: {coverage_flag}")
        return cov

    def stop_coverage(self, cov):
        if cov:
            try:
                cov.stop()
                cov.save()
                logging.info(f"Stopped and saved coverage")
            except Exception as e:
                logging.error(f"Error stopping coverage: {e}")

    @staticmethod
    def generate_report(coverage_flag):
        try:
            data_files = [
                f
                for f in os.listdir(".")
                if f.startswith(f".coverage.{coverage_flag}.")
            ]

            if not data_files:
                logging.error(f"No coverage data files found for flag: {coverage_flag}")
                return

            cov = coverage.Coverage()
            cov.combine(data_files)
            cov.save()
            cov.xml_report(outfile=f"coverage_{coverage_flag}.xml")

            logging.info(f"Generated coverage report for flag: {coverage_flag}")

            for file in data_files:
                os.remove(file)
            logging.info(
                f"Cleaned up individual coverage files for flag: {coverage_flag}"
            )

        except Exception as e:
            logging.error(f"Error generating coverage report: {e}")
