import logging
import time
import traceback
import uuid

from celery.result import AsyncResult
from django.apps import apps
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone

from .async_tasks import async_campaign_gen
from .campaign_gen import CampaignGenerator, CeleryTaskUpdater
from .content_collection import ContentCollection
from .models import Content, ContentGroup


class CampaignGenWrapper:
    def __init__(self, campaign_instance) -> None:
        self.campaign_instance = campaign_instance

    @classmethod
    def load_from_db(cls, campaign_id):
        model = cls.get_model()
        campaign_instance = model.objects.filter(id=campaign_id).first()
        if not campaign_instance:
            raise Exception(f"Campaign with id {campaign_id} does not exist")
        campaign_handler = cls(campaign_instance)

        return campaign_handler

    @classmethod
    def load_from_db_instance(cls, campaign_instance):
        campaign_handler = cls(campaign_instance)

        return campaign_handler

    @classmethod
    def get_model(cls):
        return apps.get_model("api", "Campaign")

    def get_all_content_collection_ids(self):
        content_groups = ContentGroup.objects.filter(campaign=self.campaign_instance)
        content_collection_ids = []
        for content_group in content_groups:
            content_group_params = content_group.content_group_params
            content_collection = content_group_params.get("content_collection", None)
            if content_collection:
                content_collection_ids.append(content_collection.get("id"))
        return set(content_collection_ids)

    def get_content_collection_group_ids(self, collection_ids):
        if not collection_ids:
            return []

        content_group_ids = []
        for collection_id in set(collection_ids):
            content_collection = ContentCollection(collection_id)
            content_group_ids.extend(
                list(content_collection.content_collection_map.keys())
            )
        content_group_ids = list(set(content_group_ids))
        return content_group_ids

    def pre_process(
        self,
        content_group_ids,
        content_ids,
        collection_ids=[],
        continue_gen=False,
        use_all_contents=False,
    ):  # return a dict from content_group_id to content_ids
        if collection_ids and not use_all_contents:
            content_group_ids = self.get_content_collection_group_ids(collection_ids)

        # Prefetch related Content objects
        content_groups = (
            ContentGroup.objects.filter(
                campaign=self.campaign_instance
            ).prefetch_related("content_set")
            if (not content_group_ids or use_all_contents)
            else ContentGroup.objects.filter(
                id__in=content_group_ids,
            ).prefetch_related("content_set")
        )

        # Create a list to hold objects that need to be updated
        contents_to_update = {}
        # Loop through prefetched content groups
        for content_group in content_groups:
            if content_ids:
                contents = content_group.content_set.filter(pk__in=content_ids)
            else:
                if content_group_ids and content_group.id not in content_group_ids:
                    continue
                contents = content_group.content_set.all()
            if not contents:
                continue

            if continue_gen:
                contents = [
                    content
                    for content in contents
                    if content.content_status.get("gen_status", {}).get("status")
                    != "FINISHED"
                ]
            if contents:
                contents_to_update[content_group.id] = [
                    content.id for content in contents
                ]
        return contents_to_update

    def set_gen_status(self, contents_to_update, task_id):
        content_groups = ContentGroup.objects.filter(id__in=contents_to_update.keys())
        for content_group in content_groups:
            content_group.content_group_status["gen_status"] = {"status": "QUEUED"}
        ContentGroup.objects.bulk_update(content_groups, ["content_group_status"])

        # Flatten the list of content IDs to be updated
        content_ids = [
            content_id
            for nested_ids in contents_to_update.values()
            for content_id in nested_ids
        ]
        contents_to_save = Content.objects.filter(id__in=content_ids)

        current_time = timezone.now().isoformat()
        for content in contents_to_save:
            content.content_status["gen_status"] = {
                "status": "QUEUED",
                "task_id": task_id,
                "update_time": current_time,
            }
        Content.objects.bulk_update(contents_to_save, ["content_status"])
        for content_group_id in contents_to_update.keys():
            CeleryTaskUpdater.add_celery_job_for_content_group(
                content_group_id, task_id
            )

    def submit_job(
        self,
        user,
        content_group_ids,
        content_ids,
        collection_ids,
        continue_gen,
        joint_generation,
        use_all_contents,
    ):
        contents_to_update = self.pre_process(
            content_group_ids=content_group_ids,
            content_ids=content_ids,
            collection_ids=collection_ids,
            continue_gen=continue_gen,
            use_all_contents=use_all_contents,
        )

        task_id = f"campaign_gen_{uuid.uuid4()}"
        self.set_gen_status(contents_to_update, task_id)

        number_of_contents_to_gen = sum(
            len(content_ids) for content_ids in contents_to_update.values()
        )
        soft_time_limit = 60 * 30 * number_of_contents_to_gen  # 30 minutes per content

        cache.set(task_id, {"status": "SCHEDULED"}, timeout=60 * 60 * 24)
        current_user = user if user else self.campaign_instance.creator
        ret = async_campaign_gen.apply_async(
            args=[
                current_user.id if current_user else None,
                self.campaign_instance.id,
                contents_to_update,
                collection_ids,
                task_id,
                joint_generation,
            ],
            task_id=task_id,
            priority=9,
            soft_time_limit=soft_time_limit,
        )

        logging.info(
            f"debug: ret is {ret} and ret.id is {ret.id} and ret.status is {ret.status}"
        )
        return task_id

    def terminate_gen(self, content_group_ids):
        generator = CampaignGenerator(self.campaign_instance)
        generator.terminate_gen(content_group_ids)

    def wait_job(self, task_id, timeout=36000, polling_interval=1):
        """
        Wait for job completion with timeout.

        Args:
            task_id: The Celery task ID
            timeout: Maximum time to wait in seconds (None for no timeout)
            polling_interval: Time between status checks in seconds
        """
        if polling_interval <= 0:
            raise ValueError("polling_interval must be positive")
        if timeout is not None and timeout <= 0:
            raise ValueError("timeout must be positive or None")
        result = AsyncResult(task_id)
        start_time = time.time()

        while True:
            # Check if we've exceeded timeout
            if timeout and time.time() - start_time > timeout:
                logging.error(f"Task {task_id} timed out after {timeout} seconds")
                return False

            # Check cache first since AsyncResult might be unreliable
            cache_result = cache.get(task_id)
            cache_status = (
                None
                if not cache_result or not isinstance(cache_result, dict)
                else cache_result.get("status")
            )

            status = cache_status or result.status
            if status == "SUCCESS":
                return True
            elif status == "FAILURE":
                error_info = result.info
                logging.exception(f"Task {task_id} failed with error: {error_info}")
                return False
            elif status == "REVOKED":
                logging.info(f"Task {task_id} was revoked/terminated")
                return False
            elif status in ["PENDING", "STARTED", "RETRY", "SCHEDULED"]:
                time.sleep(polling_interval)
            else:
                logging.error(f"Task {task_id} in unexpected state: {status}")
                return False
