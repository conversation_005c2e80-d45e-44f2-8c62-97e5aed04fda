from ..prompt_library.prompt_summary import (
    SUMMARIZE_ASSET,
    SUMMARIZE_COMPANY,
    SUMMARIZE_MAP_ASSET,
    SUMMARIZE_MAP_COMPANY,
    SUMMARIZE_MAP_TARGET,
    SUMMARIZE_TARGET,
)
from .base_prompt_assembler import BasePromptDictPromptBuilder


class SummarizePromptBuilder(BasePromptDictPromptBuilder):
    def __init__(self, info_type):
        super().__init__()

        self.info_type = info_type

    def build(self, features):
        self.append_prompt("combine_prompt", self.get_combine_prompt())
        self.append_prompt("map_prompt", self.get_map_prompt())
        # we don't call self.fill(features) due to this would be handled by chain
        return self.prompts

    def get_combine_prompt(self):
        if self.info_type == "target_info":
            return SUMMARIZE_TARGET
        elif self.info_type == "assets":
            return SUMMARIZE_ASSET
        elif self.info_type == "company_info":
            return SUMMARIZE_COMPANY
        else:
            raise ValueError(f"Invalid info type: {self.info_type}")

    def get_map_prompt(self):
        if self.info_type == "target_info":
            return SUMMARIZE_MAP_TARGET
        elif self.info_type == "assets":
            return SUMMARIZE_MAP_ASSET
        elif self.info_type == "company_info":
            return SUMMARIZE_MAP_COMPANY
        else:
            raise ValueError(f"Invalid info type: {self.info_type}")
