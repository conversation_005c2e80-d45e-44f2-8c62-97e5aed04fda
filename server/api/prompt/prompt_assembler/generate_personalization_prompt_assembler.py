import logging

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from ...logger import log_data_wrapper_mismatch
from ..prompt_builder.ai_msg_asset_prompt_builder import AIMsgAssetPromptBuilder
from ..prompt_builder.ai_msg_company_prompt_builder import AIMsgCompanyPromptBuilder
from ..prompt_builder.ai_msg_target_prompt_builder import AIMsgTargetPromptBuilder
from ..prompt_builder.custom_instruct_prompt_builder import CustomInstructionsBuilder
from ..prompt_builder.human_msg_asset_prompt_builder import HumanMsgAssetPromptBuilder
from ..prompt_builder.human_msg_company_prompt_builder import (
    HumanMsgCompanyPromptBuilder,
)
from ..prompt_builder.human_msg_target_prompt_builder import HumanMsgTargetPromptBuilder
from ..prompt_builder.instruct_prompt_builder import InstructPromptBuilder
from ..prompt_builder.preceding_succeeding_context_prompt_builder import (
    PrecedingSucceedingContextPromptBuilder,
)
from ..prompt_builder.reviewed_contents_prompt_builder import (
    ReviewedContentsPromptBuilder,
)
from ..prompt_builder.rules_prompt_builder import RulesPromptBuilder
from ..prompt_builder.sys_msg_prompt_builder import SysMsgPromptBuilder
from ..prompt_builder.template_tone_instruct_prompt_builder import (
    TemplateToneInstructPromptBuilder,
)
from ..prompt_library.instruct_prompt.default_instruct import STATIC_CONTEXT_QUERY
from .base_prompt_assembler import BaseMessengePromptAssembler


class GeneratePersonalizePromptAssembler(BaseMessengePromptAssembler):

    def __init__(
        self,
        gen_env=None,
    ) -> None:
        super().__init__(gen_env=gen_env)

    def build(self, features):
        self.append_message(self.build_sys_msg(features), SystemMessage)
        self.append_message(self.build_ai_company_msg(features), AIMessage)
        self.append_message(self.build_human_company_msg(features), HumanMessage)
        self.append_message(self.build_ai_target_msg(features), AIMessage)
        self.append_message(self.build_human_target_msg(features), HumanMessage)
        preceding_succeeding_context_variations = (
            self.build_preceding_succeeding_variations(features)
        )
        if preceding_succeeding_context_variations:
            self.append_message(preceding_succeeding_context_variations, HumanMessage)
        if "asset_context" in features:
            self.append_message(self.build_ai_asset_msg(features), AIMessage)
            self.append_message(self.build_human_asset_msg(features), HumanMessage)
        self.append_message(self.build_instruct(features), HumanMessage)
        self.append_message(self.build_rules(features), SystemMessage)
        if (
            self.template_settings and self.template_settings.get("tone_reference")
        ) or self.tone_reference_v2:
            tone_reference_msg = self.build_template_instruction_msg(features)
            if tone_reference_msg:
                self.append_message(tone_reference_msg, HumanMessage)
        custom_instruct = self.build_custom_instructions(features)
        if custom_instruct:
            self.append_message(custom_instruct, HumanMessage)

        if (
            "component_reviewed_contents_string" in features
            and features["component_reviewed_contents_string"]
        ):
            self.append_message(
                self.build_reviewed_contents_msg(features), HumanMessage
            )

        return self.fill(features)

    def build_sys_msg(self, features):
        sys_msg_builder = SysMsgPromptBuilder()
        return sys_msg_builder.build(features)

    # keep this in case we need some ensemble of instruct messages
    def build_instruct(self, features):
        instruct_builder = InstructPromptBuilder(
            self.content_type,
            self.content_source_format,
            self.template_settings,
        )
        return instruct_builder.build(features)

    def build_preceding_succeeding_variations(self, features):
        preceding_succeeding_context_builder = PrecedingSucceedingContextPromptBuilder()
        return preceding_succeeding_context_builder.build(features)

    def build_rules(self, features):
        rules_builder = RulesPromptBuilder(
            self.template_settings, self.foundation_model
        )
        return rules_builder.build(features)

    def build_ai_company_msg(self, features):
        ai_msg_company_builder = AIMsgCompanyPromptBuilder()
        return ai_msg_company_builder.build(features)

    def build_ai_target_msg(self, features):
        ai_msg_target_builder = AIMsgTargetPromptBuilder()
        return ai_msg_target_builder.build(features)

    def build_human_company_msg(self, features):
        human_msg_company_builder = HumanMsgCompanyPromptBuilder()
        return human_msg_company_builder.build(features)

    def build_human_target_msg(self, features):
        human_msg_target_builder = HumanMsgTargetPromptBuilder()
        return human_msg_target_builder.build(features)

    def build_custom_instructions(self, features):
        custom_instructions_builder = CustomInstructionsBuilder()
        return custom_instructions_builder.build(features)

    def build_ai_asset_msg(self, features):
        ai_msg_asset_builder = AIMsgAssetPromptBuilder()
        return ai_msg_asset_builder.build(features)

    def build_human_asset_msg(self, features):
        human_msg_asset_builder = HumanMsgAssetPromptBuilder(repurpose=False)
        return human_msg_asset_builder.build(features)

    def build_template_instruction_msg(self, features):
        template_instruction_msg_builder = TemplateToneInstructPromptBuilder()
        return template_instruction_msg_builder.build(features)

    def build_reviewed_contents_msg(self, features):
        reviewed_contents_builder = ReviewedContentsPromptBuilder()
        return reviewed_contents_builder.build(features)
