from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from ...validator.campaign_validator import CampaignGoal
from ..prompt_builder.ai_msg_asset_prompt_builder import AIMsgAssetPromptBuilder
from ..prompt_builder.ai_msg_company_prompt_builder import AIMsgCompanyPromptBuilder
from ..prompt_builder.ai_msg_target_prompt_builder import AIMsgTargetPromptBuilder
from ..prompt_builder.content_collection_gen_prompt_builder import (
    ContentCollectionInstructPromptBuilder,
)
from ..prompt_builder.custom_instruct_prompt_builder import CustomInstructionsBuilder
from ..prompt_builder.human_msg_asset_prompt_builder import HumanMsgAssetPromptBuilder
from ..prompt_builder.human_msg_company_prompt_builder import (
    HumanMsgCompanyPromptBuilder,
)
from ..prompt_builder.human_msg_target_prompt_builder import HumanMsgTargetPromptBuilder
from ..prompt_builder.sys_msg_prompt_builder import SysMsgContentCollectionPromptBuilder
from .base_prompt_assembler import BaseMessengePromptAssembler


class GenerateContentCollectionPlanAssembler(BaseMessengePromptAssembler):

    def __init__(
        self,
        gen_env=None,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self._content_goal = gen_env._data_wrapper.content_goal

    def build(self, features):
        self.append_message(self.build_sys_msg(features), SystemMessage)
        self.append_message(self.build_ai_company_msg(features), AIMessage)
        self.append_message(self.build_human_company_msg(features), HumanMessage)
        if self._content_goal == CampaignGoal.Repurposing or (
            "asset_context" in features and features["asset_context"]
        ):
            self.append_message(self.build_ai_asset_msg(features), AIMessage)
            self.append_message(self.build_human_asset_msg(features), HumanMessage)
        elif self._content_goal == CampaignGoal.SeqPersonalizeTemplate:
            # target
            self.append_message(self.build_ai_target_msg(features), AIMessage)
            self.append_message(self.build_human_target_msg(features), HumanMessage)
        else:
            raise ValueError(f"Invalid content goal: {self._content_goal}")
        self.append_message(
            self.build_content_collection_instruct(features), HumanMessage
        )
        custom_instructions = self.build_custom_instructions(features)
        if custom_instructions:
            self.append_message(custom_instructions, HumanMessage)

        return self.fill(features)

    def build_sys_msg(self, features):
        sys_msg_builder = SysMsgContentCollectionPromptBuilder()
        return sys_msg_builder.build(features)

    def build_ai_company_msg(self, features):
        ai_msg_company_builder = AIMsgCompanyPromptBuilder()
        return ai_msg_company_builder.build(features)

    def build_human_company_msg(self, features):
        human_msg_company_builder = HumanMsgCompanyPromptBuilder()
        return human_msg_company_builder.build(features)

    def build_ai_asset_msg(self, features):
        ai_msg_asset_builder = AIMsgAssetPromptBuilder(
            repurpose=self._data_wrapper.is_repurpose
        )
        return ai_msg_asset_builder.build(features)

    def build_human_asset_msg(self, features):
        human_msg_asset_builder = HumanMsgAssetPromptBuilder(
            repurpose=self._data_wrapper.is_repurpose,
            content_goal=self._content_goal,
        )
        return human_msg_asset_builder.build(features)

    def build_ai_target_msg(self, features):
        ai_msg_target_builder = AIMsgTargetPromptBuilder()
        return ai_msg_target_builder.build(features)

    def build_human_target_msg(self, features):
        human_msg_target_builder = HumanMsgTargetPromptBuilder()
        return human_msg_target_builder.build(features)

    def build_content_collection_instruct(self, features):
        content_type = features.get("content_type")
        instruct_builder = ContentCollectionInstructPromptBuilder(
            content_type, self._content_goal
        )
        return instruct_builder.build(features)

    def build_custom_instructions(self, features):
        custom_instructions_builder = CustomInstructionsBuilder()
        return custom_instructions_builder.build(features)
