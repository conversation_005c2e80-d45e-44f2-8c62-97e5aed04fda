import logging

from langchain_core.messages import HumanMessage

from ..prompt_library.prompt_tone_analysis import TONE_ANALYSIS, TONE_RESPONSE_FORMAT
from .base_prompt_assembler import BaseMessengePromptAssembler


class ToneAnalysisPromptAssembler(BaseMessengePromptAssembler):
    def __init__(self, gen_env) -> None:
        super().__init__(gen_env=gen_env)

    def build(self, features):
        self.append_message(self.get_tone_analysis_prompt(), HumanMessage)
        return self.fill(features)

    def get_tone_analysis_prompt(self):
        return TONE_ANALYSIS + "\n" + TONE_RESPONSE_FORMAT
