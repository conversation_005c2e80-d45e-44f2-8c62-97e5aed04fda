from langchain_core.messages import HumanMessage

from ..prompt_library.brand_guidelines_extract_prompt import (
    brand_guidelines_extract_for_content_type_prompt,
)
from .base_prompt_assembler import BaseMessengePromptAssembler


class BrandGuidelinesPromptAssembler(BaseMessengePromptAssembler):
    def __init__(self, gen_env) -> None:
        super().__init__(gen_env=gen_env)

    def build(self, features):
        self.append_message(self.get_brand_guidelines_prompt(), HumanMessage)
        return self.fill(features)

    def get_brand_guidelines_prompt(self):
        return brand_guidelines_extract_for_content_type_prompt
