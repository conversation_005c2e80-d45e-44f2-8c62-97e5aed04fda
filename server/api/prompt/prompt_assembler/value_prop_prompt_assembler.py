import logging

from langchain_core.messages import HumanMessage

from ..prompt_library.prompt_rules import BANNED_WORDS_RULES
from ..prompt_library.prompt_value_prop import VALUE_PROP, VALUE_PROP_CONTACT
from .base_prompt_assembler import BaseMessengePromptAssembler


class ValuePropPromptAssembler(BaseMessengePromptAssembler):
    def __init__(self, meta_type, gen_env) -> None:
        super().__init__(gen_env=gen_env)
        self.meta_type = meta_type

    def build(self, features):
        # check that company summary and single_target_context are in features
        playbook = self.playbook_instance.id
        target = features["single_target_key1"] + "-" + features["single_target_key2"]

        if "company_summary" not in features or not features["company_summary"]:
            logging.error(
                f"company_summary not found in features for value prop generation for playbook {playbook} and target {target}"
            )
        if (
            "single_target_context" not in features
            or not features["single_target_context"]
        ):
            logging.error(
                f"single_target_context not in features for value prop generation for playbook {playbook} and target {target}"
            )
        self.append_message(self.get_value_prop_prompt(self.meta_type), HumanMessage)
        return self.fill(features)

    def get_value_prop_prompt(self, meta_type):
        if meta_type == "Contact":
            value_prop = VALUE_PROP_CONTACT
        elif meta_type in ("Company", "Industry", "Persona", "Keyword"):
            value_prop = VALUE_PROP
        else:
            logging.error(f"error: meta_type {meta_type} is not supported")
            value_prop = VALUE_PROP
        value_prop += "\n" + BANNED_WORDS_RULES
        return value_prop
