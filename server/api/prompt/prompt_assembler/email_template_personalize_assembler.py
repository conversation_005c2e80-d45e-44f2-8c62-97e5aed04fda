from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from ..prompt_builder.ai_msg_company_prompt_builder import AIMsgCompanyPromptBuilder
from ..prompt_builder.ai_msg_target_prompt_builder import AIMsgTargetPromptBuilder
from ..prompt_builder.human_msg_company_prompt_builder import (
    HumanMsgCompanyPromptBuilder,
)
from ..prompt_builder.human_msg_target_prompt_builder import HumanMsgTargetPromptBuilder
from ..prompt_builder.rules_prompt_builder import (
    EmailTemplatePersonalizationRulesPromptBuilder,
)
from ..prompt_builder.sys_msg_prompt_builder import SysMsgRepurposePromptBuilder
from ..prompt_library.email_template_personalization import (
    EMAIL_TEMPLATE_PERSONALIZATION_PROMPT,
)
from .base_prompt_assembler import BaseMessengePromptAssembler


class EmailTemplatePersonalizeAssembler(BaseMessengePromptAssembler):

    def __init__(self, gen_env) -> None:
        super().__init__(gen_env=gen_env)

    def build(self, features):
        self.append_message(self.build_sys_msg(features), SystemMessage)
        self.append_message(self.build_ai_company_msg(features), AIMessage)
        self.append_message(self.build_human_company_msg(features), HumanMessage)
        self.append_message(self.build_ai_target_msg(features), AIMessage)
        self.append_message(self.build_human_target_msg(features), HumanMessage)
        self.append_message(self.get_email_template_personalize_prompt(), HumanMessage)
        self.append_message(self.build_rules(features), SystemMessage)
        return self.fill(features)

    def build_sys_msg(self, features):
        sys_msg_builder = SysMsgRepurposePromptBuilder()
        return sys_msg_builder.build(features)

    def get_email_template_personalize_prompt(self):
        return EMAIL_TEMPLATE_PERSONALIZATION_PROMPT

    def build_ai_company_msg(self, features):
        ai_msg_company_builder = AIMsgCompanyPromptBuilder()
        return ai_msg_company_builder.build(features)

    def build_ai_target_msg(self, features):
        ai_msg_target_builder = AIMsgTargetPromptBuilder()
        return ai_msg_target_builder.build(features)

    def build_human_company_msg(self, features):
        human_msg_company_builder = HumanMsgCompanyPromptBuilder()
        return human_msg_company_builder.build(features)

    def build_human_target_msg(self, features):
        human_msg_target_builder = HumanMsgTargetPromptBuilder()
        return human_msg_target_builder.build(features)

    def build_rules(self, features):
        rules_builder = EmailTemplatePersonalizationRulesPromptBuilder()
        return rules_builder.build(features)
