import logging

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from ..prompt_builder.ai_msg_asset_prompt_builder import AIMsgAssetPromptBuilder
from ..prompt_builder.ai_msg_company_prompt_builder import AIMsgCompanyPromptBuilder
from ..prompt_builder.ai_msg_target_prompt_builder import AIMsgTargetPromptBuilder
from ..prompt_builder.custom_instruct_prompt_builder import CustomInstructionsBuilder
from ..prompt_builder.human_msg_asset_prompt_builder import HumanMsgAssetPromptBuilder
from ..prompt_builder.human_msg_company_prompt_builder import (
    HumanMsgCompanyPromptBuilder,
)
from ..prompt_builder.human_msg_target_prompt_builder import HumanMsgTargetPromptBuilder
from ..prompt_builder.rules_prompt_builder import TemplateGenerationRulesPromptBuilder
from ..prompt_builder.sys_msg_prompt_builder import SysMsgRepurposePromptBuilder
from ..prompt_builder.template_gen_prompt_builder import (
    TemplateGenerationInstructPromptBuilder,
)
from .base_prompt_assembler import BaseMessengePromptAssembler


class GenerateTemplatePromptAssembler(BaseMessengePromptAssembler):
    def __init__(
        self,
        gen_env=None,
    ) -> None:
        super().__init__(gen_env=gen_env)
        self.is_repurpose = (
            gen_env._data_wrapper.is_repurpose
            if gen_env and gen_env._data_wrapper
            else True
        )
        self.content_goal = gen_env._data_wrapper.content_goal

    def build(self, features):
        self.append_message(self.build_sys_msg(features), SystemMessage)
        self.append_message(self.build_ai_company_msg(features), AIMessage)
        self.append_message(self.build_human_company_msg(features), HumanMessage)
        self.append_message(self.build_ai_target_msg(features), AIMessage)
        self.append_message(self.build_human_target_msg(features), HumanMessage)
        if "asset_context" in features:
            self.append_message(self.build_ai_asset_msg(features), AIMessage)
            self.append_message(self.build_human_asset_msg(features), HumanMessage)
        self.append_message(self.build_instruct(features), HumanMessage)
        self.append_message(self.build_rules(features), SystemMessage)
        custom_instruct = self.build_custom_instructions(features)
        if custom_instruct:
            self.append_message(custom_instruct, HumanMessage)

        return self.fill(features)

    def build_sys_msg(self, features):
        sys_msg_builder = SysMsgRepurposePromptBuilder()
        return sys_msg_builder.build(features)

    def build_ai_company_msg(self, features):
        ai_msg_company_builder = AIMsgCompanyPromptBuilder()
        return ai_msg_company_builder.build(features)

    def build_human_company_msg(self, features):
        human_msg_company_builder = HumanMsgCompanyPromptBuilder()
        return human_msg_company_builder.build(features)

    def build_ai_target_msg(self, features):
        ai_msg_target_builder = AIMsgTargetPromptBuilder()
        return ai_msg_target_builder.build(features)

    def build_human_target_msg(self, features):
        human_msg_target_builder = HumanMsgTargetPromptBuilder()
        return human_msg_target_builder.build(features)

    def build_ai_asset_msg(self, features):
        ai_msg_asset_builder = AIMsgAssetPromptBuilder()
        return ai_msg_asset_builder.build(features)

    def build_human_asset_msg(self, features):
        human_msg_asset_builder = HumanMsgAssetPromptBuilder(
            repurpose=self.is_repurpose,
            content_goal=self.content_goal,
        )
        return human_msg_asset_builder.build(features)

    def build_instruct(self, features):
        instruct_builder = TemplateGenerationInstructPromptBuilder(self.content_type)
        return instruct_builder.build(features)

    def build_rules(self, features):
        rules_builder = TemplateGenerationRulesPromptBuilder(self.foundation_model)
        return rules_builder.build(features)

    def build_custom_instructions(self, features):
        custom_instructions_builder = CustomInstructionsBuilder()
        return custom_instructions_builder.build(features)
