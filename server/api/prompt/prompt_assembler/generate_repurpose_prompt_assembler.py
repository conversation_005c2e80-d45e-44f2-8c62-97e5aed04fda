import logging

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from ...logger import log_data_wrapper_mismatch
from ...shared_types import ContentType
from ..prompt_builder.ai_msg_asset_prompt_builder import AIMsgAssetPromptBuilder
from ..prompt_builder.ai_msg_company_prompt_builder import AIMsgCompanyPromptBuilder
from ..prompt_builder.custom_instruct_prompt_builder import (
    CustomInstructionsBuilder,
    JointCustomInstructionsBuilder,
)
from ..prompt_builder.human_msg_asset_prompt_builder import HumanMsgAssetPromptBuilder
from ..prompt_builder.human_msg_company_prompt_builder import (
    HumanMsgCompanyPromptBuilder,
)
from ..prompt_builder.joint_instruct_prompt_builder import (
    JointInstructRepurposePromptBuilder,
)
from ..prompt_builder.prev_gen_message_prompt_builder import (
    PreviousGenerationPromptBuilder,
)
from ..prompt_builder.repurpose_instruct_prompt_builder import (
    RepurposeInstructPromptBuilder,
    RepurposeSingleComponentInstruct,
    SlideDeckInstructPromptBuilder,
)
from ..prompt_builder.repurpose_template_instruct_prompt_builder import (
    RepurposeTemplateInstructPromptBuilder,
)
from ..prompt_builder.rules_prompt_builder import (
    JointRepurposeRulesPromptBuilder,
    RulesRepurposePromptBuilder,
)
from ..prompt_builder.sys_msg_prompt_builder import SysMsgRepurposePromptBuilder
from .base_prompt_assembler import BaseMessengePromptAssembler


class GenerateRepurposePromptAssembler(BaseMessengePromptAssembler):
    def __init__(
        self,
        gen_env=None,
    ) -> None:
        super().__init__(gen_env=gen_env)

    def build(self, features):
        self.append_message(self.build_sys_msg(features), SystemMessage)
        # do not add company info for quotes & highlights content type.
        if self.content_type != ContentType.QuotesHighlights:
            self.append_message(self.build_ai_company_msg(features), AIMessage)
            self.append_message(self.build_human_company_msg(features), HumanMessage)
        self.append_message(self.build_ai_asset_msg(features), AIMessage)
        self.append_message(self.build_human_asset_msg(features), HumanMessage)
        self.append_message(self.build_instruct(features), HumanMessage)
        repurpose_template = self.build_repurpose_template_instruct(features)
        if repurpose_template:
            self.append_message(repurpose_template, HumanMessage)
        self.append_message(self.build_rules(features), SystemMessage)
        custom_instruct = self.build_custom_instructions(features)
        if custom_instruct:
            self.append_message(custom_instruct, HumanMessage)

        return self.fill(features)

    def build_sys_msg(self, features):
        sys_msg_builder = SysMsgRepurposePromptBuilder()
        return sys_msg_builder.build(features)

    def build_ai_company_msg(self, features):
        ai_msg_company_builder = AIMsgCompanyPromptBuilder()
        return ai_msg_company_builder.build(features)

    def build_human_company_msg(self, features):
        human_msg_company_builder = HumanMsgCompanyPromptBuilder(
            self.use_company_summary
        )
        return human_msg_company_builder.build(features)

    def build_ai_asset_msg(self, features):
        ai_msg_asset_builder = AIMsgAssetPromptBuilder(repurpose=True)
        return ai_msg_asset_builder.build(features)

    def build_human_asset_msg(self, features):
        human_msg_asset_builder = HumanMsgAssetPromptBuilder(repurpose=True)
        return human_msg_asset_builder.build(features)

    def build_repurpose_template_instruct(self, features):
        use_template = (
            "repurpose_template_content" in features
            and features["repurpose_template_content"]
        )
        template_instruct_builder = RepurposeTemplateInstructPromptBuilder(
            use_template=use_template,
            template_settings=self.template_settings,
            tone_reference_v2=self.tone_reference_v2,
        )
        return template_instruct_builder.build(features)

    def build_instruct(self, features):
        instruct_builder = RepurposeInstructPromptBuilder(
            self.content_type,
            self.content_source_format,
        )
        return instruct_builder.build(features)

    def build_rules(self, features):
        use_template = (
            "repurpose_template_content" in features
            and features["repurpose_template_content"]
        )
        rules_builder = RulesRepurposePromptBuilder(
            self.content_source_format,
            use_template=use_template,
            template_settings=self.template_settings,
            foundation_model=self.foundation_model,
        )
        return rules_builder.build(features)

    def build_prev_generation(self, features):
        prev_generation_builder = PreviousGenerationPromptBuilder()
        return prev_generation_builder.build(features)

    def build_custom_instructions(self, features):
        custom_builder = CustomInstructionsBuilder()
        return custom_builder.build(features)


class GenerateRepurposeSalesDeckPromptAssembler(GenerateRepurposePromptAssembler):
    def __init__(
        self,
        gen_env,
    ) -> None:
        super().__init__(gen_env=gen_env)

    def build_instruct(self, features):
        instruct_builder = SlideDeckInstructPromptBuilder(
            self._data_wrapper.should_use_slides_template,
            self._data_wrapper.should_use_manual_placeholder,
        )
        return instruct_builder.build(features)


class GenerateRepurposeSingleComponentPromptAssembler(BaseMessengePromptAssembler):
    def __init__(
        self,
        gen_env,
    ) -> None:
        super().__init__(gen_env=gen_env)

    def build(self, features):
        self.append_message(self.build_sys_msg(features), SystemMessage)
        self.append_message(self.build_ai_company_msg(features), AIMessage)
        self.append_message(self.build_human_company_msg(features), HumanMessage)
        self.append_message(self.build_ai_asset_msg(features), AIMessage)
        self.append_message(self.build_human_asset_msg(features), HumanMessage)
        self.append_message(self.build_instruct(features), HumanMessage)
        self.append_message(self.build_rules(features), SystemMessage)
        prev_gen_message = self.build_prev_generation(features)
        custom_instruct = self.build_custom_instructions(features)
        if custom_instruct:
            if prev_gen_message:
                self.append_message(prev_gen_message, AIMessage)
            self.append_message(custom_instruct, HumanMessage)

        return self.fill(features)

    def build_sys_msg(self, features):
        sys_msg_builder = SysMsgRepurposePromptBuilder()
        return sys_msg_builder.build(features)

    def build_ai_company_msg(self, features):
        ai_msg_company_builder = AIMsgCompanyPromptBuilder()
        return ai_msg_company_builder.build(features)

    def build_human_company_msg(self, features):
        human_msg_company_builder = HumanMsgCompanyPromptBuilder(
            self.use_company_summary
        )
        return human_msg_company_builder.build(features)

    def build_ai_asset_msg(self, features):
        ai_msg_asset_builder = AIMsgAssetPromptBuilder(repurpose=True)
        return ai_msg_asset_builder.build(features)

    def build_human_asset_msg(self, features):
        human_msg_asset_builder = HumanMsgAssetPromptBuilder(repurpose=True)
        return human_msg_asset_builder.build(features)

    def build_instruct(self, features):
        instruct_builder = RepurposeSingleComponentInstruct()
        return instruct_builder.build(features)

    def build_rules(self, features):
        rules_builder = RulesRepurposePromptBuilder(None, False)
        return rules_builder.build(features)

    def build_prev_generation(self, features):
        prev_generation_builder = PreviousGenerationPromptBuilder()
        return prev_generation_builder.build(features)

    def build_custom_instructions(self, features):
        custom_builder = CustomInstructionsBuilder()
        return custom_builder.build(features)


class GenerateJointRepurposePromptAssembler(BaseMessengePromptAssembler):
    def __init__(
        self,
        gen_env=None,
    ) -> None:
        super().__init__(gen_env=gen_env)

    def build(self, features):
        self.append_message(self.build_sys_msg(features), SystemMessage)
        self.append_message(self.build_ai_company_msg(features), AIMessage)
        self.append_message(self.build_human_company_msg(features), HumanMessage)
        self.append_message(self.build_ai_asset_msg(features), AIMessage)
        self.append_message(self.build_human_asset_msg(features), HumanMessage)
        self.append_message(self.build_instruct(features), HumanMessage)
        repurpose_template = self.build_repurpose_template_instruct(features)
        if repurpose_template:
            self.append_message(repurpose_template, HumanMessage)
        self.append_message(self.build_rules(features), SystemMessage)
        custom_instruct = self.build_custom_instructions(features)
        if custom_instruct:
            self.append_message(custom_instruct, HumanMessage)

        return self.fill(features)

    def build_sys_msg(self, features):
        sys_msg_builder = SysMsgRepurposePromptBuilder()
        return sys_msg_builder.build(features)

    def build_ai_company_msg(self, features):
        ai_msg_company_builder = AIMsgCompanyPromptBuilder()
        return ai_msg_company_builder.build(features)

    def build_human_company_msg(self, features):
        human_msg_company_builder = HumanMsgCompanyPromptBuilder(
            self.use_company_summary
        )
        return human_msg_company_builder.build(features)

    def build_ai_asset_msg(self, features):
        ai_msg_asset_builder = AIMsgAssetPromptBuilder(repurpose=True)
        return ai_msg_asset_builder.build(features)

    def build_human_asset_msg(self, features):
        human_msg_asset_builder = HumanMsgAssetPromptBuilder(repurpose=True)
        return human_msg_asset_builder.build(features)

    def build_repurpose_template_instruct(self, features):
        use_template = (
            "repurpose_template_content" in features
            and features["repurpose_template_content"]
        )
        template_instruct_builder = RepurposeTemplateInstructPromptBuilder(
            use_template=use_template,
            template_settings=self.template_settings,
            tone_reference_v2=self.tone_reference_v2,
        )
        return template_instruct_builder.build(features)

    def build_instruct(self, features):
        instruct_builder = JointInstructRepurposePromptBuilder(
            self.content_type,
            self.content_source_format,
            self.is_in_content_group_collection,
        )
        return instruct_builder.build(features)

    def build_rules(self, features):
        use_template = (
            "repurpose_template_content" in features
            and features["repurpose_template_content"]
        )
        rules_builder = JointRepurposeRulesPromptBuilder(
            use_template,
            self.template_settings,
            self.foundation_model,
        )
        return rules_builder.build(features)

    def build_custom_instructions(self, features):
        custom_instructions_builder = JointCustomInstructionsBuilder()
        return custom_instructions_builder.build(features)
