import logging
from abc import ABC, abstractmethod
from typing import Dict, List

from langchain_core.prompts import PromptTemplate

from ...feature.data_wrapper.data_wrapper import DataWrapperHolder
from ..prompt_template.prompt_template_util import get_missing_variables


class BasePromptAssembler(ABC):
    def __init__(self) -> None:
        pass

    @abstractmethod
    def build(self, features):
        pass

    @staticmethod
    def fill_prompt(prompt, features: Dict[str, str]):
        if isinstance(prompt, str):
            prompt = PromptTemplate.from_template(prompt)
        if not prompt.input_variables:
            return prompt.format()

        partial_features = {
            k: v for k, v in features.items() if k in prompt.input_variables
        }
        partial_prompt = prompt.partial(**partial_features)

        # handle missing features
        missing_features = get_missing_variables(partial_prompt)
        missing_values = {}
        if missing_features:
            logging.info(
                f"error: missing features {missing_features} for message {partial_prompt}"
            )
            missing_values = {v: "{" + f"{v}" + "}" for v in missing_features}

        finished_message = partial_prompt.format(**missing_values)
        return finished_message


class BaseMessengePromptAssembler(BasePromptAssembler, DataWrapperHolder):
    def __init__(self, gen_env) -> None:
        BasePromptAssembler.__init__(self)
        DataWrapperHolder.__init__(self, gen_env=gen_env)
        self.messages = []

    def append_message(
        self,
        message_prmpt: PromptTemplate,
        message_type,
        need_fill=True,
        cache_block=False,
    ):
        self.messages.append((message_prmpt, message_type, need_fill, cache_block))

    def fill(self, features: Dict[str, str]):
        filled_messages = []
        for message_prompt, message_type, need_fill, cache_block in self.messages:
            if need_fill:
                finished_message = self.fill_prompt(message_prompt, features)
            else:
                finished_message = str(message_prompt)
            if cache_block:
                filled_messages.append(
                    message_type(
                        content=finished_message, cache_control={"type": "ephemeral"}
                    )
                )
            else:
                filled_messages.append(message_type(content=finished_message))
        return filled_messages


class BasePromptDictPromptBuilder(BasePromptAssembler):
    def __init__(self) -> None:
        super().__init__()
        self.prompts = {}

    def append_prompt(self, prompt_name: str, prompt: PromptTemplate):
        if isinstance(prompt, str):
            prompt = PromptTemplate.from_template(prompt)
        self.prompts[prompt_name] = prompt

    def fill(self, features: Dict[str, str]):
        raise Exception(
            "fill for BasePromptDictPromptBuilder is not implemented by design"
        )
