import logging
from typing import List

from langchain_core.prompts import PipelinePromptTemplate, PromptTemplate


def get_all_partial_variables(prompt_template: PromptTemplate) -> List[str]:
    res = []
    if isinstance(prompt_template, PipelinePromptTemplate):
        logging.error(f"PipelinePromptTemplate is not supposed to be used")
        res.extend(get_all_partial_variables(prompt_template.final_prompt))
        for input_var, sub_prompt in prompt_template.pipeline_prompts:
            res.extend(get_all_partial_variables(sub_prompt))
    else:
        res = prompt_template.partial_variables
    return list(set(res))


def get_all_input_variables(prompt_template: PromptTemplate) -> List[str]:
    res = []
    if isinstance(prompt_template, PipelinePromptTemplate):
        logging.error(f"PipelinePromptTemplate is not supposed to be used")
        res.extend(get_all_input_variables(prompt_template.final_prompt))
        for input_var, sub_prompt in prompt_template.pipeline_prompts:
            res.extend(get_all_input_variables(sub_prompt))
    else:
        res = prompt_template.input_variables
    return list(set(res))


def get_missing_variables(prompt_template: PromptTemplate) -> List[str]:
    return list(
        set(get_all_input_variables(prompt_template))
        - set(get_all_partial_variables(prompt_template))
    )
