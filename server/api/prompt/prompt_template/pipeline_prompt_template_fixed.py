from typing import Any, Callable, Dict, List, Tuple, Union

from langchain_core.prompt_values import PromptValue
from langchain_core.prompts import (
    BaseChatPromptTemplate,
    BasePromptTemplate,
    PipelinePromptTemplate,
)
from pydantic import root_validator


def get_all_input_variables(prompt_template: BasePromptTemplate) -> List[str]:
    all_input_variables = []
    if isinstance(prompt_template, PipelinePromptTemplate):
        all_input_variables.extend(
            get_all_input_variables(prompt_template.final_prompt)
        )
        for _input_var, sub_prompt in prompt_template.pipeline_prompts:
            all_input_variables.extend(get_all_input_variables(sub_prompt))
    else:
        all_input_variables = prompt_template.input_variables
    return list(set(all_input_variables))


def _get_inputs(inputs: dict, prompt_template: BasePromptTemplate) -> dict:
    return {k: inputs[k] for k in get_all_input_variables(prompt_template)}


class FixedPipelinePromptTemplate(PipelinePromptTemplate):
    @root_validator(pre=True)
    def get_input_variables(cls, values: Dict) -> Dict:
        """Get input variables."""
        created_variables = set()
        # Changelog by Tofu: add final_prompt.input_variables to all_variables
        all_variables = set(values["final_prompt"].input_variables)

        for k, prompt in values["pipeline_prompts"]:
            created_variables.add(k)
            all_variables.update(prompt.input_variables)
        values["input_variables"] = list(all_variables.difference(created_variables))
        return values

    def partial(self, **kwargs: Union[str, Callable[[], str]]) -> BasePromptTemplate:
        """Return a partial of the prompt template."""
        prompt_dict = self.__dict__.copy()
        prompt_dict["input_variables"] = list(
            set(self.input_variables).difference(kwargs)
        )
        prompt_dict["partial_variables"] = {**self.partial_variables, **kwargs}

        # Changelog by Tofu: resolve partial for final_prompt and pipeline_prompts
        prompt_dict["pipeline_prompts"] = []
        for var_name, prompt in self.__dict__["pipeline_prompts"]:
            partial_values = {
                k: v for k, v in kwargs.items() if k in prompt.input_variables
            }
            prompt_dict["pipeline_prompts"].append(
                (var_name, prompt.partial(**partial_values))
            )

        partial_values = {
            k: v
            for k, v in kwargs.items()
            if k in self.__dict__["final_prompt"].input_variables
        }
        prompt_dict["final_prompt"] = self.__dict__["final_prompt"].partial(
            **partial_values
        )
        return type(self)(**prompt_dict)
