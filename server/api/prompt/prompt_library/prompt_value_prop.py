from langchain_core.prompts import PromptTemplate

# For retrieving value props from company for specific target

value_prop_template = """
One of the targets {company_name} has is {single_target_key2}.

<{company_name}>
{company_summary}
</{company_name}>

<{single_target_key2}>
{single_target_context}
</{single_target_key2}>

Create pain points that {single_target_key2} faces and also value propositions that {company_name} would provide to {single_target_key2}. Make sure the pain points and value propositions are very detailed. Make sure the title of every pain point and value proposition mentions something specific to {single_target_key2} without mentioning {single_target_key2}.

Include only information that is factually correct based on the products and services that {company_name} provides.

Output in Markdown format without any header
"""

VALUE_PROP = PromptTemplate.from_template(value_prop_template)

value_prop_contact_template = """
One of the targets {company_name} has is {single_target_key2}.

<{company_name}>
{company_summary}
</{company_name}>

<{single_target_key2}>
{single_target_context}
</{single_target_key2}>

Create pain points that {single_target_key2} faces and also value propositions that {company_name} would provide to {single_target_key2}. Make sure the pain points and value propositions are very detailed. Make sure the title of every pain point and value proposition mentions something specific to {single_target_key2}.

Do it for both {single_target_key2} and the company {single_target_key2} works for.

Include only information that is factually correct based on the products and services that {company_name} provides.

Output in Markdown format (without the big header)
"""
VALUE_PROP_CONTACT = PromptTemplate.from_template(value_prop_contact_template)
