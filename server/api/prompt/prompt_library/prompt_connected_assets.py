"""
Prompt library for connected assets functionality.
"""

# Category definitions for content analysis
CATEGORY_DEFINITIONS = """
Target Categories:
1. Blog Posts: Regular articles, news updates, or thought leadership content typically found in sections like '/blog/', '/news/', '/articles/', or locale-specific paths that include country/language codes. Blog sections may have various URL structures depending on the site's internationalization approach.
2. Case Studies: Customer success stories, implementation examples, or detailed scenarios showing how the product solves specific problems. Often found in sections like '/case-studies/', '/customer-stories/', '/use-cases/', '/success-stories/', or company-specific naming patterns.
3. Other: Any other content that does not fit into the above categories. But still has meaningful content that could be used to generate marketing content.
4. Ignore: Any content that does not have meaningful content. Like about us page, contact us page, privacy policy page, etc.

If you are asking to return category name in the response, please use exact category name in the response.

"""

# Common response format for all prompts
COMMON_RESPONSE_FORMAT = """
Please provide a JSON response with the following structure:
{
    "content_sections": [
        {
            "category": "string",
            "url_paths": ["strings (all section URL paths)"],
            "confidence": "string (high/medium/low)",
            "estimated_url_count": "integer (estimated number of urls in this section)",
            "indicators": ["strings (why this appears to be a section URL)"]
        }
    ],
    "suggested_paths": [
        {
            "category": "string",
            "url_paths": ["string (section example URL paths)"],
            "confidence": "string (high/medium/low)",
            "estimated_url_count": "integer (estimated number of urls in this section)"
        }
    ],
    "forbidden_urls_patterns": ["strings (forbidden URLs patterns)"]
}"""

# Pattern analysis prompt for analyzing URLs
PATTERN_ANALYSIS_PROMPT = """Analyze the following URLs to identify potential section URLs for content categories.

Section URLs are URLs that:
- Organize or list multiple content pieces of the same category
- Often have simpler paths (e.g., '/blog/' rather than '/blog/specific-post/')
- Typically serve as entry points or index pages for content categories
- May contain listing pages, archives, or category indexes
- May include locale information with country and language codes in the path structure

The valid content categories are:
{category_definitions}

For each URL, determine:
1. If it's likely a section URL (organizing multiple content pieces)
2. The most likely content category it belongs to (must be one of the defined categories)
3. The confidence level of your assessment
4. Estimate how many content items this section might contain

Analysis Rules:
1. For the content sections, those are the sections that are likely to be part of the target categories.
2. For the suggested paths, those are the paths that are likely to be part of the target categories, but you are not very sure about it. And you would like to crawl and analyze them in the later stage.
3. Please also returns a list of forbidded urls in forbidden_urls_patterns, those url patterns are for sure not part of the target categories. This can help us reduce the number of urls we need to crawl and analyze in the later stage.
4. For the forbidden url patterns, please not return the regular expression patterns, but the exact the section url paths. Example: /legal/privacy-notice.html -> /legal/
5. For the estimated url count, please try to fill it based on the url paths.
6. If you see multiple URLs with the same parent path, mark the parent path as a high-confidence section.
7. Locale-specific paths that include country and language codes are common patterns for international websites and should be recognized as content sections.

Please follow the customer instructions while analyzing the urls:
{customer_instructions}

URLs to analyze:
{example_urls}

Content Preview (if available):
{content_preview}

Please provide a JSON response with the following structure:
{response_format}

Response:"""

# Root analysis prompt for analyzing the root URL
ROOT_ANALYSIS_PROMPT = """You are a marketing analyst tasked with finding section URLs for specific content categories on this website.

Target Categories:
{category_definitions}

Analysis Rules:
1. Only analyze the provided HTML content - do not make assumptions or guess URLs
2. Focus on finding parent/category URLs rather than individual content pages
3. For single category, there might be multiple section urls, you need to find all the section urls
4. Ignore URLs that point to specific posts or individual case studies
5. You don't only do string matching, you need to analyze the content and the url to find the section urls
6. If you don't find any paths, don't make up any paths, just return an empty list
7. If you see multiple URLs with the same parent path (e.g., '/use-cases/1-1-abm', '/use-cases/outbound-prospecting'), infer that the parent path ('/use-cases/') is likely a section URL
8. Please also returns a list of forbidded urls in forbidden_urls_patterns, those url patterns are for sure not part of the target categories. This can help us reduce the number of urls we need to crawl and analyze in the later stage.
9. For the forbidden url patterns, please not return the regular expression patterns, but the exact the section url paths. Example: /legal/privacy-notice.html -> /legal/
10. For the forbidden url patterns, please also return some common url patterns that are for sure not part of the target categories.
11. For the suggested paths, those are the paths that are likely to be part of the target categories, but you are not very sure about it. And you would like to crawl and analyze them in the later stage.
12. For the estimated url count, please fill in 0 since you don't know the exact number of urls in this section.
13. Pay special attention to locale-specific paths that include country and language codes - these are common patterns for international websites and should be recognized as content sections if they contain relevant content.

Please follow the customer instructions while analyzing the root url:
{customer_instructions}

Root URL: {url}
HTML Content related to links:
{content}
Page content:
{page_content}

Please provide a JSON response with the following structure:
{response_format}

Example of good URLs:
- /blog
- /case-studies
- /case-study
- /resources/blog
- /customer-stories
- /use-cases
- /company/casestudies

Example of URLs to forbidden:
- /blog/specific-post-title
- /case-studies/company-name-case-study
- /blog/2023/01/post-title
- /use-cases/specific-example

Example of URL patterns to forbidden:
- /about
- /contact
- /privacy
- /terms
- /login
- /signup
- /support
- /careers


Please only return the JSON response, do not include any other text.
"""

# Metadata extraction prompts
METADATA_SCHEMA = """
Please provide a JSON response with the following structure:
{
    "title": "string (title of the content), not exeeding 200 characters, if there is page title, use it as the title",
    "author": "string (author name if found, empty string if not found)",
    "published_date": "string (ISO format YYYY-MM-DD if found, empty string if not found)",
    "category": "string (category of the content)"
}
"""

METADATA_PROMPT = """
You are a metadata extraction expert.  
Analyze the following content and page URL to extract the **author**, **category**, and **published date**.

Input Context:
- Starting listing page URL: {starting_listing_page_url}
- Starting listing page category: {starting_listing_page_category}
- Page URL to analyze: {page_url}

Category Definitions:  
{category_definitions}

Rules:

1. Category Judgment Priority:
   - Compare `{page_url}` with `{starting_listing_page_url}`.
   - If `{page_url}` is a direct child or related variant of `{starting_listing_page_url}`, then assign the category `{starting_listing_page_category}`.
   - If no clear relationship is found, analyze `{page_url}` independently based on URL patterns and content structure.

2. Author Extraction:
   - Look for author information in:
     - Byline sections
     - Author bio sections
     - Article metadata
     - Social media handles
     - Case study credits
     - Company attribution
     - Publication metadata
   - If no author is found, return an empty string.

3. Published Date Extraction:
   - Look for published date in:
     - Article headers
     - Publication timestamps
     - Article metadata
     - URL patterns (only if date is embedded in the URL)
     - Case study headers
     - Case study metadata
   - If a date is found but not in ISO format, convert it to **YYYY-MM-DD**.
   - If no date is found, return an empty string.

4. Page Type Validation:
   - Reject the page if the URL indicates a category listing or non-content page, such as:
     - /blog, /blog?page=2, /blog/page/3
     - /case-studies, /case-studies?page=1
     - /blog/category/*, /resources/*, /videos, /webinars
   - If the page is detected as a category listing, return an error in the JSON output.

5. Response Requirements:
   - Always return a valid JSON response.
   - Do not include any extra text, comments, or newlines outside of the JSON structure.

Content to analyze:  
{content}

Page URL to analyze:  
{page_url}

Starting listing page URL:  
{starting_listing_page_url}

Starting listing page category:  
{starting_listing_page_category}

Response Format:  
{schema}
"""

URLS_RELEVANCE_PROMPT = """
You are a content relevance expert. Analyze the following URLs and determine which ones are relevant and which ones are not to the category "{category}".

Input Statistics:
- Total URLs in this chunk: {url_count}
- Starting listing page URL: {starting_listing_page_url}

Rules:

1. Relationship-based judgment first:
   - Compare each crawled URL with the provided `{starting_listing_page_url}`.
   - The crawled URL should be considered relevant only if it is:
     - A direct child of `{starting_listing_page_url}`, OR
     - A locale-prefixed variant (e.g., `/en/path`, `/fr/path`, etc.), OR
     - Follows a known semantically related path (e.g., `/podcast` → `/episodes/slug`, `/resource` → `/blog-post-*`).
   - Use flexible pattern matching and normalize for locale or language prefixes at the start of the path.
   - Do NOT rely on content quality or content type to determine relevance.

2. Path-based Relationship Check (Required for Relevance):
   - If the crawled URL is not structurally or semantically related to `{starting_listing_page_url}`, classify it as **unrelevant**.
   - Examples of unrelated paths (and thus unrelevant):
     - `/blog/...`, `/case-studies/...`, `/webinar/...`, `/resources/...`, `/clients/...`, etc., unless they explicitly reflect the intent and structure of `{starting_listing_page_url}`.
   - If unsure, prefer to reject.

3. Special Handling for Listing Sections:
   - If a crawled URL matches exactly the listing section (e.g., `{starting_listing_page_url}`) or is a paginated variant (e.g., `{starting_listing_page_url}?page=2`), reject it.
   - Only individual subpages or articles should be considered for relevance.

Final Requirements:

4. You MUST process every single URL in the input list — no exceptions.

5. Double-check your response to ensure:
   - Every URL appears exactly once in either `relevant_urls` or `unrelevant_urls`.
   - No URL is missed or duplicated.
   - The total number of URLs must match {url_count}.

6. Return a valid JSON object without any extra text or comments.

7. Do not repeat or reanalyze any URL more than once.

Category Definitions:
{category_definitions}

URLs to analyze:
{urls}

Please provide a JSON response with the following structure:
{{
    "relevant_urls": ["url1", "url2", ...],
    "unrelevant_urls": ["url3", "url4", ...],
    "url_counts": {{
        "total_processed": integer,
        "relevant_count": integer,
        "unrelevant_count": integer
    }}
}}"""


URL_SUPPORT_VALIDATION_PROMPT = """You are a content analyst. 

Examine the provided URL and its content to determine if it's suitable for our content extraction system.

URL: {url}
Content: {content}
Title: {title}
Sub-pages content: 
{sub_pages_content}

Please assess the page and its linked subpages and focus only on the following unsupported content types:
1. VIDEO_WEBINAR: Page primarily focused on video content (e.g., multiple embedded videos, video galleries). And the subpages doens't have transcripts or summaries.
2. AUDIO: Page focused on audio content (e.g., podcasts). And the subpages doens't have transcripts or summaries.
4. GATED: Page or subpages where meaningful content is behind a form, login, or registration wall. Also flag as GATED if subpage titles and actual extracted content appear unrelated (indicating access barriers or placeholders).

**Important**:  
- If a page or subpage includes **video or audio content**, but also contains **transcripts, summaries, or detailed writeups**, mark it as **PASS**.  
- Do **not fail** a page solely due to the presence of videos if the surrounding text provides extractable insights.
- If you see meaningful Transcript/Summary/Content in any subpages, please pass the page.
- If the page url self strongly indicate it is a page with blog posts, case studies, or other meaningful & extractable content types, please pass the page.



Do not analyze other aspects of the content - focus only on the above criteria.

Use these exact error messages when applicable:
VIDEO_WEBINAR: {video_webinar_error}
GATED: {gated_error}
AUDIO: {video_webinar_error}

Provide a JSON response in this format:
{{
    "label": "PASS|FAIL",
    "comment": "Brief explanation of why the content passed or failed",
    "reason": "VIDEO_WEBINAR|GATED|AUDIO"
}}

Example responses:
- {{"label": "FAIL", "comment": "{video_webinar_error}", "reason": "VIDEO_WEBINAR"}}
- {{"label": "FAIL", "comment": "{gated_error}", "reason": "GATED"}}
- {{"label": "PASS", "comment": "Page contains accessible text content suitable for extraction", "reason": "PASS"}}"""

URL_CATEGORY_PROMPT = """You are a content categorization expert. Analyze this URL to determine its category.

Target Categories:
{category_definitions}

URL to analyze: {url}

Please provide a JSON response with the following structure:
{{
    "category": "string (must be one of: Blog Posts, Case Studies, Other, Ignore)"
}}

Please only return the JSON response, do not include any other text.
"""
