def get_linkedin_ads_template():
    return """<generation>
    <introductory-text>
    [150 characters max for mobile (desktop max of 600 characters)]
    </introductory-text>
    
    <headline>
    [70 characters max to avoid truncation on mobile devices (desktop max of 200 characters)]
    </headline>
    
    <description>
    [100 characters max to avoid truncation on mobile (desktop max of 300 characters)]
    </description>
    
    <ad-copy>
    [Keep the output short around 80 characters]
    </ad-copy>
    </generation>"""


def get_linkedin_ads_template_w_template():
    return """<generation>
    <introductory-text>
   [introductory-text] 
   </introductory-text>

   <headline>
   [headline]
   </headline>
   
   <description>
   [description]
   </description>
     
   <ad-copy>
   [ad-copy]
   </ad-copy>
   </generation>"""
