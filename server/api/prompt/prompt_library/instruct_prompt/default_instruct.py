from langchain_core.prompts import PromptTemplate

######## For content generation ########

INSTRUCT_MSG_GENERIC_JOINT = """
Here is a series of messages from one of {company_name}'s {content_type_plural_name}:

<content type="{content_type_name}">
{contents_xml}
</content>

Generate one JSON object with the following format:
{json_format}

Tailor your messages for {company_name}'s targeting customers with the following combined characteristics

{targets}

Now generate a list of messages to replace the original messages. Make sure to correctly match the id of each generated message to the id of the original message.
Make sure to personalize the message for {company_name}'s targeting customers: {targets}
It is very important the id of each generated message matches exactly one of the ids of the original messages.
"""

INSTRUCT_MSG_GENERIC_JOINT_LENGTH = """It is extremely important that the generated message length is similar to the original message length. If you manage to get the output length within 10 percent of the original message length, you will get a bonus reward.
It is extremely important that the generated message follow the format, number of paragraphs, length of each paragraph and sentence of the original message."""

REVIEWED_CONTENTS = """
To show you what an ideal output look like, here is one or more examples for some other targets I really like. Each example is surrounded by <example> tags. Analyze these example output(s) and think step by step. I would expect your output follow the same pattern on how each component is personalized in these examples.
Please follow the format of the sentence structure here. Make sure to use the same words if they are being repeatedly used across examples.
{component_reviewed_contents_string}"""

JOINT_REVIEWED_CONTENTS = """
To show you what an ideal output look like, here is one or more examples for some other targets I really like. Each example is surrounded by <example> tags. Analyze these example output(s) and think step by step. I would expect your output follow the same pattern on how each component is personalized in these examples.
Please follow the format of the sentence structure here. Make sure to use the same words if they are being repeatedly used across examples.
{reviewed_contents_string}"""

JOINT_COMPONENT_CUSTOM_PROMPTS = """Here are some component specific instructions you also need to follow when generating your response. If there is reference content, you must use the information from the reference content.
<customInstructions>
{component_level_custom_prompts}
</customInstructions>
You need to apply these component specific instructions to the component with the matching id.
"""

CUSTOM_PROMPTS = """Here are some instructions you also need to follow when generating your response. Analyze the instructions carefully and make sure to incorporate them into your output.
<customInstructions>
{custom_prompts_string}
</customInstructions>

Think deeply step by step about how to make your response follow these instructions closely.
"""

ASSET_CUSTOM_PROMPTS = """Here are some instruction regarding the reference content you need to follow when generating your response. Analyze the instructions carefully and make sure to incorporate them into your output wherever makes sense.
<customInstructions>
{asset_custom_prompts_string}
</customInstructions>

Think deeply step by step about how to make your response follow these instructions closely.
"""

TEMPLATE_GENERATION_LENGTH_INSTRUCTIONS = """<customInstructions>
Make the {content_type_name} {template_length_instructions} long.
</customInstructions>
"""

TEMPLATE_GENERATION_PURPOSE_INSTRUCTIONS = """<customInstructions>
I want to write a new {content_type_name} for {template_purpose_instructions}
</customInstructions>
"""


REF_CONTENT_PROMPTS = """Here is some additional context you should reference when creating content:

<referenceContent>
{asset_context}
</referenceContent>

Your job is to rewrite the entire {content_type_name}, but first you need to rewrite each of the sections in the {content_type_name} one by one. I will provide you with the original text for each section and your goal is to replace the text with information that incorporates the reference content above. The new text should still follow the original structure, length, tone, and overall context.
"""

REF_CONTENT_PROMPT_REPURPOSING = """I want you to generate a {content_type_name} using information within these assets

<anchorContent>
{asset_context}
</anchorContent>
"""

AI_MSG_COMPANY = "Tell me more about the company {company_name}"
AI_MSG_TARGET = (
    "Tell me more about the company {company_name}'s target audiences:\n{targets}"
)
AI_MSG_ASSET = (
    "Is there any additional context you want me to reference when creating content?"
)

AI_MSG_ASSET_REPURPOSING = (
    "What are the reference contents you want my writing to be base upon?"
)
HUMAN_MSG_COMPANY = """<company name="{company_name}">
{company_context}
</company>
"""

HUMAN_MSG_COMPANY_NEW = """<company name="{company_name}">
{company_summary}
</company>"""

HUMAN_MSG_TARGET = """<target name="{targets}">
{target_context}
</target>"""

PREV_GEN_PROMPT = "{previous_generation}"

TONE_SAMPLE_INSTRUCT_PROMPT = """When generating the new content, you should also mimic the tone and style of the writing sample that I will give you.
Specifically, you should take the provided writing sample, study its style and characteristics, and then follow the same writing style in your generated content.

To mimic the writing style effectively, carefully analyze the following elements in the provided sample:
1. Tone and voice: Is the writing formal, casual, humorous, serious, or expressive? Maintain the same tone in your generated content.
2. Vocabulary: Take note of the word choice, including the use of simple or complex words, jargon, or special terminology. Incorporate similar vocabulary in your generated content.
3. Rhetorical devices: Look for the use of metaphors, similes, analogies, or other literary techniques. Apply these devices in your writing when appropriate.
4. Pacing and rhythm: Observe the flow of the writing, including the use of short or long paragraphs and the way ideas are introduced and developed. Maintain a similar pacing in your generated content.

After analyzing the writing style, generate new content as required, ensuring that your output closely mimics the style of the provided sample. Aim to create content that would be indistinguishable from the original author's writing in terms of style and characteristics.

Writing sample:
{tone_reference}
"""

INSTRUCT_TXT_SURROUNDING = """
For context, the preceding and succeeding paragraphs of this text are
Preceding:
{preceding_element}
Succeeding:
{succeeding_element}
"""

INSTRUCT_HTML_SURROUNDING = """
The preceding and succeeding html elements of this message are 
Preceding:
{preceding_element}
Succeeding:
{succeeding_element}
"""

INSTRUCT_PDF_SURROUNDING = """
The preceding and succeeding contents of this message are 
Preceding:
{preceding_element}
Succeeding:
{succeeding_element}
"""

INSTRUCT_ORIG_MSG = """
Replace the following {component_type_section} from one of {company_name}'s {content_type_plural_name} to a new one.
Original message:
{example_text}
"""

HTML_TAG_MSG = """For context, the html tag of this message is {html_tag} and you are generating the inner html of this tag.
"""

PDF_PAGE_NUM_MSG = """
For context, this message is located on page number {pageNum}.
"""

TARGET_GENERATE_LENGTH = """
Now, generate a {num_of_words} words new message but tailored for {company_name}'s targeting customers with the following combined characteristics

{targets}

It is extremely important that the generated message follow the format, number of paragraphs, length of each paragraph and sentence of the original message.
"""

TARGET_GENERATE_LENGTH_PDF = """
Now, generate a {num_of_words} words or shorter new message but tailored for {company_name}'s targeting customers with the following combined characteristics

{targets}
"""

TARGET_GENERATE_NO_LENGTH = """
Now, generate a new message but tailored for {company_name}'s targeting customers with the following combined characteristics

{targets}
"""
######## For context buidling ########

# For retrieving context from index
STATIC_CONTEXT_QUERY = """
key differentiators
value propositions
brand guidelines
style guidelines
brand personalities
personas
target audiences
"""
