REPURPOSE_INSTRUCT_LENGTH_FOLLOWING = """When generating the new content, you should use the provided **example template** as a reference for the length and structure of your output.
Specifically, you should aim to match the length and structure of the example template in your generated content.

To ensure that your output aligns with the example template, pay attention to the following aspects:
1. Length: Match the overall length of your content to the example template. If the example template is concise and to the point, keep your content similarly brief. If the example template is detailed and comprehensive, provide a similar level of detail in your content.
2. Structure: Observe the organization and layout of the example template. Follow the same structure in your generated content, including the use of headings, subheadings, bullet points, hashtags, tables or paragraphs.

Example template:

<template>

{repurpose_template_content}

</template>
"""

REPURPOSE_INSTRUCT_TONE_FOLLOWING = """When generating the new content, you should also mimic the tone and style of the **writing sample** that I will give you below.
Specifically, you should take the provided example template, study its style and characteristics, and then follow the same writing style in your generated content.

To mimic the writing style effectively, carefully analyze the following elements in the provided sample:
1. Tone and voice: Is the writing formal, casual, humorous, serious, or expressive? Maintain the same tone in your generated content.
2. Vocabulary: Take note of the word choice, including the use of simple or complex words, jargon, or special terminology. Incorporate similar vocabulary in your generated content.
3. Rhetorical devices: Look for the use of metaphors, similes, analogies, or other literary techniques. Apply these devices in your writing when appropriate.
4. Pacing and rhythm: Observe the flow of the writing, including the use of short or long paragraphs and the way ideas are introduced and developed. Maintain a similar pacing in your generated content.

After analyzing the writing style, generate new content as required, ensuring that your output closely mimics the style of the provided sample. Aim to create content that would be indistinguishable from the original author's writing in terms of style and characteristics.

Writing sample:

<sample>

{repurpose_template_content}

</sample>
"""

REPURPOSE_INSTRUCT_TONE_REF_FOLLOWING = """When generating the new content, you should also mimic the tone and style of the **writing sample** that I will give you below.
Specifically, you should take the provided example template, study its style and characteristics, and then follow the same writing style in your generated content.

To mimic the writing style effectively, carefully analyze the following elements in the provided sample:
1. Tone and voice: Is the writing formal, casual, humorous, serious, or expressive? Maintain the same tone in your generated content.
2. Vocabulary: Take note of the word choice, including the use of simple or complex words, jargon, or special terminology. Incorporate similar vocabulary in your generated content.
3. Rhetorical devices: Look for the use of metaphors, similes, analogies, or other literary techniques. Apply these devices in your writing when appropriate.
4. Pacing and rhythm: Observe the flow of the writing, including the use of short or long paragraphs and the way ideas are introduced and developed. Maintain a similar pacing in your generated content.

After analyzing the writing style, generate new content as required, ensuring that your output closely mimics the style of the provided sample. Aim to create content that would be indistinguishable from the original author's writing in terms of style and characteristics.

Writing sample:

<sample>

{tone_reference}

</sample>
"""

REPURPOSE_INSTRUCT_MSG = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content."""

REPURPOSE_INSTRUCT_QUOTES_HIGHLIGHTS = """Write a new {content_type_name} using the anchor content provided.
Create two distinct sections:
1. Highlights: Summarize key points or insights from the content. Describe in detail the context of where the highlight comes from. Create a parenthetical of why it is especially interesting or relevant. Don't speak in third person omniscient.
2. Quotes: Extract verbatim passages that are particularly impactful or representative.
If you see timestamps in the transcript, they mark the beginning and end times for each line. For example:
[00:00:00 - 00:01:00] This is a quote from the transcript.
This means that the quote starts at [00:00:00] and ends at [00:01:00].
If timestamps are provided, include them in the format [HH:MM:SS - HH:MM:SS] at the beginning of each highlight or quote. If timestamps are available, every line of highlights and quotes must include timestamps at the beginning of the line.
Make sure to only use information provided in the above anchor content.
Do not pull quotes from other sources, like brand guidelines or company context.
Always provide the context and speaker name for each quote given."""

REPURPOSE_INSTRUCT_MSG_COMPONENT = """Write a new {content_type_name} {component_type} using the anchor content provided.
Make sure to only use information provided in the above anchor content."""

REPURPOSE_INSTRUCT_AD_CAMPAIGN_META = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content.

Guidelines for Meta single photo ad. Include 1 primary text, 1 Headline, and 1 Creative text overlay.

- For Primary Text stay within 50-140 characters. DON'T EXCEED 140 characters.
- For Headline DON'T EXCEED 25 characters
- Creative text overlay (optional): 5-9 characters

Only output the content, don't include character counts."""

REPURPOSE_INSTRUCT_AD_CAMPAIGN_GOOGLE = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content.

Guidelines for Google responsive search ad.
Include 3-15 unique headlines and 2-4 unique descriptions.

For Headlines, DON'T EXCEED 28 characters and Descriptions: DON'T EXCEED 85 characters.
For the unique headlines, write a combination of character counts (eg some headlines could be 20 characters, some shorter or longer and same for descriptions)

Only output the content, don't include character counts."""

REPURPOSE_INSTRUCT_AD_CAMPAIGN_LINKEDIN_DOCUMENT = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content.

Guidelines for Linkedin Document ad. Include introductory text and headline.

- For introductory text don't exceed 142 characters
- For headline don't exceed 66 characters

Only output the content, don't include character counts."""

REPURPOSE_INSTRUCT_AD_CAMPAIGN_LINKEDIN_CAROUSEL = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content.

Guidelines for Linkedin Carousel ad. Include 1 introductory text and 2-10 cards. Each card should have introductory text and card creative copy.
For introductory text don't exceed 242 characters and keep it 1-2 short sentences.
For each card
- Each headline don't exceed 42 characters
- Card creative copy should be a short pithy phrase that will fit in a small image

Only output the content, don't include character counts"""

REPURPOSE_INSTRUCT_SALES_DECK_FLASHDOCS_TEMPLATE = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content.

Separate out the content into individual slides and output in placeholder mapping format. Format the output in this way:
For example, if the original slide 1 has a title and slide 2 has a title and a list, your output should be:

<slide_1>
[{{"placeholder": "[title_1]", "value": "[slide_1_value]"}}]
</slide_1>

<slide_2>
[{{"placeholder": "[title_1]", "value": "[slide_2_value_1]"}}, {{"placeholder": "[list_1]", "value": "[slide_2_value_2]"}}]
</slide_2>

...
Make sure you are only using the placeholders given to you in the original template.
If you are given a template, only put contents for slides that have content in the original template.
For any slide in the template that has no content, put an empty slide placeholder in the output.
You must output the same number of slides in the output as the original template.
If you want to use quotes inside the value string, use an escaped quote.
Use single curly braces for JSON formatting.
Insert numbered and bulleted list marks in the output where it makes sense. Use '-' for bullets and '1.' for numbers.
Keep the length in characters of the output the same as the original template.
Make sure you begin and end each slide placeholders with <slide_X> and </slide_X> tags.
You MUST output no more than the original number of characters and lines of text for each given placeholder. For example, if the given placeholder [TITLE] is given as 3 words, you MUST output 3 words.
"""

REPURPOSE_INSTRUCT_SALES_DECK_FLASHDOCS_TEMPLATE_MANUAL_PLACEHOLDER = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content.

Given the contents of the original slides, create appropriate placeholder replacements for the given placeholders in the slides.
For each slide, if it contains a placeholder, given in brackets ( [EXAMPLE_PLACEHOLDER] ), replace it with the appropriate content.
Return your output in the following format. For example, if Slide 1 has a placeholder [EXAMPLE_PLACEHOLDER], replace it with the content "EXAMPLE_CONTENT":

<slide_1>
[{{"placeholder": "[EXAMPLE_PLACEHOLDER]", "value": "[EXAMPLE_CONTENT]"}}]
</slide_1>

...

Use the following slides placeholder mapping for reference:

{slides_placeholder_mapping}

Note that you only need to generate for the placeholders. Slides that do not have placeholders should be left as is, but you should still output the slide number. For example, if slide N has no placeholders, given as {{'slide_number': 'slide N', 'placeholder_map': []}}, your output should be:
```
<slide_N>
[]
</slide_N>
```
"""

REPURPOSE_INSTRUCT_SALES_DECK_DEFAULT = """Write a new {content_type_name} using the anchor content provided.
Make sure to only use information provided in the above anchor content.

Separate out the content into individual slides and output in placeholder mapping format. Format the output in this way:
For example, if the original slide 1 has a title and slide 2 has a title and a list, your output should be:
<slide_1>
# Title
</slide_1>
<slide_2>
# Title
- Item 1
- Item 2
</slide_2>
...

If you are given additional instructions regarding the length of the output, you MUST follow them.
"""


INSTRUCT_MSG_GENERIC_JOINT_REPURPOSE = """
Create a new {content_type_name} with the following XML format:
{generic_joint_format}

It is very important you only return each XML tag, label and field exactly once and nothing else.
"""

INSTRUCT_MSG_GENERIC_JOINT_REPURPOSE_COLLECTION = """
Create a new {content_type_name} with the following XML format:
<internal thoughts>
This is a series of contents, so think about what you should include and not include in your output
</internal thoughts>
{generic_joint_format}

It is very important you only return each XML tag, label and field exactly once and nothing else.
"""

REPURPOSE_INSTRUCT_HTML = """Here is an existing HTML document {content_type_name}:
{example_text}
Write a new {content_type_name} by rewriting the above HTML document to use the anchor content.
Make sure you keep all the unique id tags the same in the new HTML document.
Return only the html contents and nothing else."""

REPURPOSE_INSTRUCT_HTML_SINGLE_COMPONENT = """Here is a paragraph from an existing HTML document:

{example_text}

Rewrite the above paragraph to use the anchor content.
Limit your reponse to {num_of_words} words or less."""

TEMPLATE_GENERATION_INSTRUCT_MSG = """
Write a new {content_type_name} template for the company {company_name} for the target {targets}.
Keep the output length to 2-3 paragraphs. 
Make it hyperpersonalized to the target, focusing on their specific pain points or goals.
"""

TEMPLATE_GENERATION_EMAIL_INSTRUCT_MSG = """
Write a new {content_type_name} template for the company {company_name} for the target {targets}.
Make it hyperpersonalized to the target, focusing on their specific pain points or goals.
Write the {content_type_name} with the following structure:
- A hook to grab the reader from {targets}. Make this part super personalized for the target {targets}.
- An offer or value proposition from the company {company_name} that is relevant to the target {targets}.
- A call to action that is compelling and relevant to the target {targets}.
Output should be in the following JSON format: {email_json_format}.
"""

TEMPLATE_GENERATION_LINKEDIN_ADS_INSTRUCT_MSG = """
Write a new {content_type_name} template for the company {company_name} for the target {targets}.
Here are the details of components:
- introductory-text: less than 600 characters
- headline: less than 200 characters
- description: less than 300 characters
- ad-copy: less than 200 characters
Make it hyperpersonalized to the target, focusing on their specific pain points or goals.
Output should be in the following JSON format: {linkedin_ads_json_format}.
"""


FREE_GEN_ASK_ORIGINAL_CONTENT = """What content did you generate previously that you'd like me to help you revise?"""

FREE_GEN_ASK_PART_TO_REWRITE = (
    """Which specific part of this content would you like me to rewrite?"""
)

FREE_GEN_PARENT_COMPONENT_CURRENT_VALUE_MSG = """
{parent_component_current_value}
"""

FREE_GEN_MSG = """
Rewrite the text snippet indicated below, unless mentioned otherwise, keep the length roughly equal as the original text chunk. Output the new text without any xml tags. Use canonical markdown format for your output. If there are any bullet points, use '-' for bullets.
<text>
{example_text}
</text>
"""

FREE_GEN_MSG_WITH_INSTRUCTIONS = (
    FREE_GEN_MSG
    + """
Please follow these instructions:
<instructions>
{component_instructions}
</instructions>
"""
)
