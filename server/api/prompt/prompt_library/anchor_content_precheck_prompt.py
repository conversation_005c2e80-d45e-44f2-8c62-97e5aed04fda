ANCHOR_CONTENT_PRECHECK_PROMPT = """
<text>
{anchor_content}
</text>

Is there meaningful information in the text for a content writer to use as reference content in order to create some derived content? Answer in the following JSON format:
{json_format}
Follow these four best practices when writing the reason for the FAIL result:
1. Don't refer to content writer.
2. Write error messages in conversational, 5th grade language without technical jargon. Ensure they are concise, helpful, and friendly. Use active not passive language.
3. For the Format and Structure: Start with a clear indication of the problem, followed by a specific, actionable solution. For example: "[Error Type]: [What went wrong]. [Solution]."
4. Refer to anything in the text as the anchor content. The solution should specifically refer to how to fix the text in the anchor content, eg "Add more specific text to the anchor content". Only if there is no clear way to fix it, include instead "Please contact Tofu customer support to help troubleshoot"
"""
