TONE_ANALYSIS = """Analyze the following example text:

{tone_example}

Assess no less than 10 and up to 20 traits that define the author's writing tone, including general type of writing style, point of view, word choice (including expressions, idioms, and figurative language), sentence structure (punctuation, simple vs elaborate, grammatical structure, informal sentence fragments, sentence length), active vs passive voice, humor, formality (informal, formal, casual, conversational, technical including use of emojis and contractions), vocabulary used, intended audience, and commonly used adjectives or words."""

TONE_RESPONSE_FORMAT = """Return a JSON object with the following keys: 
- "summary": A few keywords that describe the tone of the text in Markdown format.
- "tone_analysis": This should be a Markdown string. This should include an analysis of the tone, the traits that define the style, point of view, word choice, sentence structure, active vs passive voice, humor, formality, and intended audience. Each factor should have the title as the first bullet point, summary as the second bullet point, and concise analysis as the third bullet point. All output should be in Markdown format and refer to the author as author. Do not include a header. Return it as a Markdown string, not an object.

Example of analysis:

- **Type of Writing Style**\n * Promotional and informative\n * The author's style is primarily promotional, focusing on marketing a product (Tofu - AI Sidekick for B2B Marketing) with informative content that explains the features and benefits of the product."""

TONE_MARKDOWN_REWRITE_PROMPT = """This tone analysis is not in Markdown format. Rewrite this content so that 'tone_analysis' is in Markdown format as a string. Return the rewritten content and nothing else. Do not make 'tone_analysis' a list. Do not include ``` or the word markdown in the response."""
