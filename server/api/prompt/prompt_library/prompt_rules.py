RULES_INTRO = "Make sure your response follows these rules:"

CLAUDE_RULES = "Do not start your response with 'Here is the new message' or 'Here is the new content'."

BASE_RULES = """Only output the new message, nothing else.
Do not try to oversell the company. Refrain from talking about our product and more focus on the customer's pain points.
Include only information that is factually correct based off the products and services that {company_name} provides."""

BANNED_WORDS_RULES = """Do not use "in today's", "revolutionize", "landscape", "paramount", "enhance", "innovate", "amplify",  "elevate", "navigate", "streamline", "leverage", "dwell", "supercharge", "fast-paced", or "game-changing"."""

BANNED_SENTENCE_FORMAT_RULES = """Do not write sentences in this format: "Ready to take your ____ to the next level?" """

MARKDOWN_RULES = """Use canonical markdown format for your output. If there are any bullet points, use '-' for bullets. For nested bullet points in markdown, indent using 4 spaces per level."""

TONE_RULES = """Your output should try to match the punctuations, style, and tone of the original text."""

LENGTH_RULES = """Make sure your response is not longer than {num_of_words} words.
It is extremely important that the generated message follow the format, number of paragraphs, length of each paragraph and sentence of the original message."""

CORE_MESSAGE_AND_KEY_POINT_RULES = """The overall flow of the original message should remain the same. Remove any specific details that are not true for the target we are generating for and replace it with information that is specifically true for the target."""

NO_TEMPLATE_FOLLOW_RULES = """Do not use the same sentence format or structure of the original message.
You must change the language and structure of the output so it looks different from the original message."""

RULES_PRECEDING_ELEMENT = """Make sure to match the original message, not the preceding or succeeding contents."""

VALUE_PROP_RULES = """The text that you change should reference very specific pain points, value propositions, or other elements that will resonate with the target
When you do change the text, use specific examples of pain points or value propositions"""

COMPANY_RULES = """Make it extremely personalized for the target company by referencing specific details that is unique to the target such as company name, industry, location, etc.
If the company's name contains a suffice like MD, Inc, LLC, remove that suffix when referencing the company.
Make sure to write the company name using the capitalization provided in the example context."""

PRECEDING_VARIATION_RULES = """Avoid repeating: {preceding_variation}"""

SUCCEEDING_VARIATION_RULES = """Avoid repeating: {succeeding_variation}"""

REPURPOSE_RULES = """Only output the new content, nothing else.
Pull in specific details or excerpts from the anchor content.
Do not try to oversell the company. Refrain from talking about our product and more focus on the customer's pain points.
Include only information that is factually correct based off the products and services that {company_name} provides."""

REPURPOSE_EMAIL_JOINT_RULES = """Make sure to write the email subject line like an experienced marketing professional would.
Start the email with something besides "Hope this email finds you well." """

REPURPOSE_EMAIL_JOINT_RULE_TONE = (
    "Use a casual but professional tone for marketing emails."
)

REPURPOSE_EMAIL_JOINT_RULE_LENGTH = (
    "Make the size of the email body around three paragraphs."
)

REPURPOSE_TEMPLATE_RULE = """DO NOT use any information from the example template. Only use the example template for style and formatting."""

REPURPOSE_TONE_FOLLOWING_RULE = """When generating the new content, you should also try to mimic the tone and style of the example template that I gave you."""

REPURPOSE_LENGTH_FOLLOWING_RULE = """It is extremely important that the generated message follow the format, number of paragraphs, length of each paragraph and sentence of the example template."""

BLOG_POST_RULES = """Each paragraph should start with a unique word.
Do not use "fast-paced", "ever evolving", "rapidly changing" or other cliche hooks. The first sentence (after the title) must directly get into it."""

EMAIL_TEMPLATE_PERSONALIZATION_RULES = """The new email should be a similar length to the original template, no more than 15% longer.
Maintain the same messaging of the original.
Do not use sales cliches or start with anything like "I hope this email finds you well..." """

EMAIL_TEMPLATE_RULES = """Write at a grade 3 reading level
Start the email with something besides "Hope this email finds you well"
Use words focused on the target versus us. For example, don't say "I'd love to" and instead say "Would you be curious to"
Use only 1 question, ideally an interest based call to action
Focus on pain points solved and outcomes delivered rather than the products and features of {company_name}.
Keep the subject line under 5 words long.
Keep the sentence structure and vocabulary simple, avoid using college-level words or complex sentences."""

EMAIL_TEMPLATE_LENGTH_RULES = """Keep emails to 150 words max"""

EMAIL_TEMPLATE_CONTACT_RULES = """Do not mention the target's title verbatim, instead focus on the pain points this target would face in their role."""

REPURPOSING_SDR_EMAIL_RULES = """Write at a grade 8 reading level
Add a greeting and closing to the email. Start with a punchy first sentence.
Use words focused on the target versus us. For example, don't say "I'd love to" and instead say "Would you be curious to"
Use only 1 question, ideally an interest based call to action
Keep the subject line under 5 words long."""

REPURPOSE_MARKETING_EMAIL_RULES = """Write at a grade 8 reading level
Add a greeting and closing to the email. Start with a punchy first sentence."""

EMAIL_SUBJECT_RULES = """Make sure your response includes only the subject line of the email, nothing else.
Personalize the email subject line in a super specific way for the target, including their company where relevant.
Make sure to write the email subject line like an experienced marketing professional would."""

EMAIL_BODY_RULES_PERSONALIZE = """Do not include the header or subject line in your response.
Start the email with something besides "Hope this email finds you well."
Make sure to follow the original structure and tone of the email.
Check if there is a closing or greeting in the original message. If there is not, DO NOT add a closing to your response.
If there is a closing in the succeeding message, DO NOT add a closing to your response."""

EMAIL_BODY_RULES_REPURPOSING = """Make sure your response includes only the main body of the email, without header. Do not include the subject line in your response.
Start the email with something besides "Hope this email finds you well."
Unless told otherwise, keep the size of the email under three paragraphs."""

EMAIL_TONE_RULES = "Use a casual but professional tone for marketing emails."

EMAIL_LENGTH_RULES = """If the original message has newlines, make sure to also use a similar number in your output."""


LINKEDIN_ADS_INTRODUCTORY_TEXT_RULES = """Make sure your response includes only the introductory text of the LinkedIn ad, nothing else.
Personalize the introductory text in a super specific way for the target, including their company where relevant.
Make sure to write the introductory text like an experienced marketing professional would.
The length of the introductory text should be less than 150 characters."""

LINKEDIN_ADS_HEADLINE_RULES = """Make sure your response includes only the headline of the LinkedIn ad, nothing else.
Personalize the headline in a super specific way for the target, including their company where relevant.
Make sure to write the headline like an experienced marketing professional would.
The length of the headline should be around 70 characters."""

LINKEDIN_ADS_DESCRIPTION_RULES = """Make sure your response includes only the description of the LinkedIn ad, nothing else.
Personalize the description in a super specific way for the target, including their company where relevant.
Make sure to write the description like an experienced marketing professional would.
The length of the description should be around 70 characters."""

LINKEDIN_ADS_AD_COPY_RULES = """Make sure your response includes only the ad copy of the LinkedIn ad, nothing else.
Personalize the ad copy in a super specific way for the target, including their company where relevant.
Make sure to write the ad copy like an experienced marketing professional would.
The length of the ad copy should be around 200 characters."""

LINKEDIN_MESSAGE_RULES = """Make it short under 300 characters.
Don't use bullet points or paragraphs, make it all one block of text.
Don't use sign offs.
Add a CTA at the end."""

QUOTES_RULES = """Output quotes verbatim with quotation marks.
Include attribution for the quotes.
List as bullet points."""

STATISTICS_RULES = """Create a comprehensive list of all statistics mentioned.
Include context for the statistics.
Include the source for the statistics.
List as bullet points."""

CONTACT_RULES = """Try to specifiy how the {company_name}'s value propositions impact the target with respect to their role and position."""

REPURPOSE_HTML_RULES = """Avoid changing text used in menus, footers, or other parts of the HTML document that are supposed to be static. If the text is supposed to be static, avoid making even small changes such as capitalization or punctuation."""

JOINT_BASE_RULES = """Only output the new variation for each component, nothing else.
Do not try to oversell the company. Refrain from talking about our product and more focus on the customer's pain points.
Include only information that is factually correct based off the products and services that {company_name} provides.
If you personalize the content very well according to the target I will give you a bonus.
Make sure you personalize the content the way a marketing professional for {company_name} would write to the target {targets}."""

JOINT_LENGTH_RULES = """It is very important that the word count in each output variation matches the word count of the corresponding original component as closely as possible.
It is extremely important that the generated message follow the format, number of paragraphs, length of each paragraph and sentence of the original message.
If the original message has newlines, make sure to also use a similar number in your output."""

JOINT_RULES_REPURPOSE = """Only output the text for each component, nothing else.
Do not try to oversell the company. Refrain from talking about our product and more focus on the customer's pain points.
Include only information that is factually correct based off the products and services that {company_name} provides."""

TEMPLATE_GEN_RULES = """Only output the new template and nothing else.
Do not try to oversell the company. Refrain from talking about our product and more focus on the customer's pain points.
Include only information that is factually correct based off the products and services that {company_name} provides.
Make sure the generated template is super specific to the target.
Do not use any placeholders in the template."""

EMAIL_SDR_PERSONALIZATION_RULES = """Use simple concise language. For example, instead of using "Would you be open to...?", use "Open to...?"
Write at a 8th grade level."""
