from langchain_core.prompts import PromptTemplate

######## For playbook updates ########

# For generating new summaries

SUMMARIZE_TARGET = """Write a {summary_length_words} words summary. Try to use only the information provided and include as many details as possible.

{text}

"""

SUMMARIZE_MAP_TARGET = """Write a summary of the following:

{text}

"""

SUMMARIZE_ASSET = """Write a {summary_length_words} words summary. Try to use only the information provided and include as many details as possible.

{text}

"""

SUMMARIZE_MAP_ASSET = """Write a summary of the following:

{text}

"""

SUMMARIZE_COMPANY = """Write a {summary_length_words} words summary. Try to use only the information provided and include as many details as possible about {company_name} without repeating yourself.

{text}

"""

SUMMARIZE_MAP_COMPANY = """Write a summary of the following text about {company_name}:

{text}

"""

# For updating existing summary given data updates
REFINE_SUMMARY = """Your job is to refine a current summary based on changed context, so it can include the most update to date information.
We have provided the following summary that is generated from some existing context:

"{current_summary}"

Now the context has been changed, we have the opportunity to refine the existing summary (only if needed) with the specific context changes listed below.

{text}

Given the changes to context, refine the original summary and generate a new {summary_length_words} words summary.
If the changed context isn't useful, return the original summary.

Output the content of the new summary (even if it's the same as the original summary), nothing else.
"""


REFINE_SUMMARY_DOCUMENT_FORMAT = "---{change_type}---\n{page_content}"
