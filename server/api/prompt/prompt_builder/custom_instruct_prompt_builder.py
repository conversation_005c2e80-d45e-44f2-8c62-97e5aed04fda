from ..prompt_library.instruct_prompt.default_instruct import (
    ASSET_CUSTOM_PROMPTS,
    CUSTOM_PROMPTS,
    JOINT_COMPONENT_CUSTOM_PROMPTS,
    TEMPLATE_GENERATION_LENGTH_INSTRUCTIONS,
    TEMPLATE_GENERATION_PURPOSE_INSTRUCTIONS,
)
from .base_prompt_builder import BasePromptBuilder


class CustomInstructionsBuilder(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="custom_instructions")

    def build(self, features) -> str:
        rules = ""
        if "custom_prompts_string" in features and features["custom_prompts_string"]:
            custom_prompt = CUSTOM_PROMPTS
            rules += "\n" + custom_prompt
        if (
            "asset_custom_prompts_string" in features
            and features["asset_custom_prompts_string"]
        ):
            asset_custom_prompt = ASSET_CUSTOM_PROMPTS
            rules += "\n" + asset_custom_prompt
        if features.get("template_length_instructions") or features.get(
            "template_purpose_instructions"
        ):
            rules += "Here are some instructions to follow for generating the template:"
        if features.get("template_length_instructions"):
            rules += "\n" + TEMPLATE_GENERATION_LENGTH_INSTRUCTIONS
        if features.get("template_purpose_instructions"):
            rules += "\n" + TEMPLATE_GENERATION_PURPOSE_INSTRUCTIONS
        return rules


class JointCustomInstructionsBuilder(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="custom_instructions")

    def build(self, features) -> str:
        rules = ""
        if "custom_prompts_string" in features and features["custom_prompts_string"]:
            custom_prompt = CUSTOM_PROMPTS
            rules += "\n" + custom_prompt
        if (
            "component_level_custom_prompts" in features
            and features["component_level_custom_prompts"]
        ):
            component_level_custom_prompts = JOINT_COMPONENT_CUSTOM_PROMPTS
            rules += "\n" + component_level_custom_prompts
        if (
            "asset_custom_prompts_string" in features
            and features["asset_custom_prompts_string"]
        ):
            asset_custom_prompt = ASSET_CUSTOM_PROMPTS
            rules += "\n" + asset_custom_prompt
        return rules
