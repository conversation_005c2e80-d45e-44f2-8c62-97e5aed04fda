from ..prompt_library.instruct_prompt.repurpose_instruct import (
    REPURPOSE_INSTRUCT_LENGTH_FOLLOWING,
    REPURPOSE_INSTRUCT_TONE_FOLLOWING,
    REPURPOSE_INSTRUCT_TONE_REF_FOLLOWING,
)
from .base_prompt_builder import BasePromptBuilder


class RepurposeTemplateInstructPromptBuilder(BasePromptBuilder):
    def __init__(
        self, use_template=False, template_settings={}, tone_reference_v2=None
    ):
        super().__init__(name="repurpose_template_instruct_msg")
        self.use_template = use_template
        self.template_settings = template_settings
        self.tone_reference_v2 = tone_reference_v2

    def build(self, features):
        return self.get_instruct_template()

    def get_instruct_template(self):

        instruct_msg = ""
        follow_tone = self.template_settings.get("follow_tone", True)
        follow_length = self.template_settings.get("follow_length", True)
        tone_reference = self.template_settings.get("tone_reference", "")

        if follow_length and self.use_template:
            instruct_msg += REPURPOSE_INSTRUCT_LENGTH_FOLLOWING + "\n"

        if follow_tone and self.use_template:
            instruct_msg += REPURPOSE_INSTRUCT_TONE_FOLLOWING + "\n"

        elif tone_reference or self.tone_reference_v2:
            instruct_msg += REPURPOSE_INSTRUCT_TONE_REF_FOLLOWING + "\n"

        return instruct_msg
