from ...shared_types import ContentSourceFormat, ContentType, ContentTypeDetails
from ..prompt_library.instruct_prompt.repurpose_instruct import (
    REPURPOSE_INSTRUCT_AD_CAMPAIGN_GOOGLE,
    REPURPOSE_INSTRUCT_AD_CAMPAIGN_LINKEDIN_CAROUSEL,
    R<PERSON><PERSON>POSE_INSTRUCT_AD_CAMPAIGN_LINKEDIN_DOCUMENT,
    REPURPOSE_INSTRUCT_AD_CAMPAIGN_META,
    REPURPOSE_INSTRUCT_HTML,
    REPURPOSE_INSTRUCT_HTML_SINGLE_COMPONENT,
    REP<PERSON>POSE_INSTRUCT_MSG,
    REPURPOSE_INSTRUCT_MSG_COMPONENT,
    R<PERSON><PERSON>POSE_INSTRUCT_QUOTES_HIGHLIGHTS,
    REPURPOSE_INSTRUCT_SALES_DECK_DEFAULT,
    REPURPOSE_INSTRUCT_SALES_DECK_FLASHDOCS_TEMPLATE,
    REPURPOSE_INSTRUCT_SALES_DECK_FLASHDOCS_TEMPLATE_MANUAL_PLACEHOLDER,
)
from .base_prompt_builder import BasePromptBuilder


class SlideDeckInstructPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        should_use_slides_template,
        should_use_manual_placeholder,
    ):
        super().__init__(name="slide_deck_instruct_msg")
        self.should_use_slides_template = should_use_slides_template
        self.should_use_manual_placeholder = should_use_manual_placeholder

    def build(self, features):
        if self.should_use_slides_template:
            if self.should_use_manual_placeholder:
                return (
                    REPURPOSE_INSTRUCT_SALES_DECK_FLASHDOCS_TEMPLATE_MANUAL_PLACEHOLDER
                )
            else:
                return REPURPOSE_INSTRUCT_SALES_DECK_FLASHDOCS_TEMPLATE
        else:
            return REPURPOSE_INSTRUCT_SALES_DECK_DEFAULT


class RepurposeInstructPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        content_type,
        content_source_format,
    ):
        super().__init__(
            name="instruct_msg",
        )
        self.content_type = content_type
        self.content_source_format = content_source_format

    def build(self, features):
        return self.get_instruct_template(features)

    def get_instruct_template(self, features):
        if "component_type" in features and features["component_type"] != "message":
            return REPURPOSE_INSTRUCT_MSG_COMPONENT
        if self.content_type == ContentType.QuotesHighlights:
            return REPURPOSE_INSTRUCT_QUOTES_HIGHLIGHTS
        if self.content_type == ContentType.AdCampaignMeta:
            return REPURPOSE_INSTRUCT_AD_CAMPAIGN_META
        if self.content_type == ContentType.AdCampaignGoogle:
            return REPURPOSE_INSTRUCT_AD_CAMPAIGN_GOOGLE
        if self.content_type == ContentType.AdCampaignLinkedinCarousel:
            return REPURPOSE_INSTRUCT_AD_CAMPAIGN_LINKEDIN_CAROUSEL
        if self.content_type == ContentType.AdCampaignLinkedinDocument:
            return REPURPOSE_INSTRUCT_AD_CAMPAIGN_LINKEDIN_DOCUMENT
        if self.content_source_format == ContentSourceFormat.Text:
            repurpose_instruct_msg = REPURPOSE_INSTRUCT_MSG.format(
                content_type_name=ContentTypeDetails.get(self.content_type, {}).get(
                    "name", "marketing message"
                ),
            )
            return repurpose_instruct_msg
        elif self.content_source_format == ContentSourceFormat.Html:
            return REPURPOSE_INSTRUCT_HTML
        else:
            return REPURPOSE_INSTRUCT_MSG


class RepurposeSingleComponentInstruct(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="instruct_msg")

    def build(self, features):
        return REPURPOSE_INSTRUCT_HTML_SINGLE_COMPONENT
