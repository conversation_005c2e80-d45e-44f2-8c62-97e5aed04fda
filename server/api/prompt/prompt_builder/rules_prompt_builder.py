from ...shared_types import ContentType
from ..prompt_library.prompt_rules import (
    BANNED_SENTENCE_FORMAT_RULES,
    BANNED_WORDS_RULES,
    BASE_RULES,
    BLOG_POST_RULES,
    CLAUDE_RULES,
    COMPANY_RULES,
    CONTACT_RULES,
    CORE_MESSAGE_AND_KEY_POINT_RULES,
    EMAIL_BODY_RULES_PERSONALIZE,
    EMAIL_BODY_RULES_REPURPOSING,
    EMAIL_LENGTH_RULES,
    EMAIL_SDR_PERSONALIZATION_RULES,
    EMAIL_SUBJECT_RULES,
    EMAIL_TEMPLATE_CONTACT_RULES,
    EMAIL_TEMPLATE_LENGTH_RULES,
    EMAIL_TEMPLATE_PERSONALIZATION_RULES,
    EMAIL_TEMPLATE_RULES,
    EMAIL_TONE_RULES,
    JOINT_BASE_RULES,
    JOINT_LENGTH_RULES,
    <PERSON><PERSON><PERSON><PERSON>_RU<PERSON>S_REPURPOSE,
    <PERSON>ENGTH_RULES,
    <PERSON><PERSON><PERSON><PERSON>N_ADS_AD_COPY_RULES,
    LINKEDIN_ADS_DESCRIPTION_RULES,
    LINKEDIN_ADS_HEADLINE_RULES,
    LINKEDIN_ADS_INTRODUCTORY_TEXT_RULES,
    LINKEDIN_MESSAGE_RULES,
    MARKDOWN_RULES,
    NO_TEMPLATE_FOLLOW_RULES,
    QUOTES_RULES,
    REPURPOSE_EMAIL_JOINT_RULE_LENGTH,
    REPURPOSE_EMAIL_JOINT_RULE_TONE,
    REPURPOSE_EMAIL_JOINT_RULES,
    REPURPOSE_HTML_RULES,
    REPURPOSE_LENGTH_FOLLOWING_RULE,
    REPURPOSE_MARKETING_EMAIL_RULES,
    REPURPOSE_RULES,
    REPURPOSE_TEMPLATE_RULE,
    REPURPOSE_TONE_FOLLOWING_RULE,
    REPURPOSING_SDR_EMAIL_RULES,
    RULES_INTRO,
    RULES_PRECEDING_ELEMENT,
    STATISTICS_RULES,
    TEMPLATE_GEN_RULES,
    TONE_RULES,
    VALUE_PROP_RULES,
)
from .base_prompt_builder import BasePromptBuilder


class RulesPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        template_settings: dict = None,
        foundation_model=None,
    ):
        super().__init__(name="rules")
        self.template_settings = template_settings
        self.foundation_model = foundation_model

    def build(self, features) -> str:
        rules = self.get_rules(features)

        return rules

    def get_rules(self, features):
        rules_intro = RULES_INTRO
        base_rules = BASE_RULES
        value_prop_rules = VALUE_PROP_RULES
        company_rules = COMPANY_RULES
        preceding_element_rules = RULES_PRECEDING_ELEMENT
        email_subject_rules = EMAIL_SUBJECT_RULES
        email_body_rules = EMAIL_BODY_RULES_PERSONALIZE
        email_tone_rules = EMAIL_TONE_RULES
        email_length_rules = EMAIL_LENGTH_RULES
        linkedin_ads_introductory_text_rules = LINKEDIN_ADS_INTRODUCTORY_TEXT_RULES
        linkedin_ads_headline_rules = LINKEDIN_ADS_HEADLINE_RULES
        linkedin_ads_description_rules = LINKEDIN_ADS_DESCRIPTION_RULES
        linkedin_ads_ad_copy_rules = LINKEDIN_ADS_AD_COPY_RULES
        contact_rules = CONTACT_RULES
        tone_rules = TONE_RULES
        length_rules = LENGTH_RULES
        core_message_and_key_point_rules = CORE_MESSAGE_AND_KEY_POINT_RULES
        no_template_follow_rules = NO_TEMPLATE_FOLLOW_RULES
        claude_rules = CLAUDE_RULES
        blog_post_rules = BLOG_POST_RULES
        banned_words_rules = BANNED_WORDS_RULES
        banned_sentence_format_rules = BANNED_SENTENCE_FORMAT_RULES
        rule_list = base_rules.split("\n")
        if (
            "content_type" in features
            and features["content_type"] != ContentType.QuotesHighlights
        ):
            rule_list.extend(banned_words_rules.split("\n"))
        rule_list.extend(banned_sentence_format_rules.split("\n"))
        follow_tone = self.template_settings.get("follow_tone", True)
        follow_length = self.template_settings.get("follow_length", True)
        follow_core_message_and_key_point = self.template_settings.get(
            "follow_core_message_and_key_point", True
        )

        if follow_tone:
            rule_list.extend(tone_rules.split("\n"))
        if follow_length:
            rule_list.extend(length_rules.split("\n"))
        if follow_core_message_and_key_point:
            rule_list.extend(core_message_and_key_point_rules.split("\n"))

        # if both follow_core_message_and_key_point and follow_length are false, add an additional rule.
        if not follow_core_message_and_key_point and not follow_length:
            rule_list.extend(no_template_follow_rules.split("\n"))

        if "has_value_prop" in features and features["has_value_prop"]:
            rule_list.extend(value_prop_rules.split("\n"))

        if "preceding_element" in features:
            rule_list.extend(preceding_element_rules.split("\n"))

        if (
            "target_meta_types" in features
            and "Company" in features["target_meta_types"]
        ):
            rule_list.extend(company_rules.split("\n"))

        if (
            "target_meta_types" in features
            and "Contact" in features["target_meta_types"]
        ):
            rule_list.extend(contact_rules.split("\n"))

        if "component_type" in features:
            if features["component_type"] == "email subject":
                rule_list.extend(email_subject_rules.split("\n"))
            elif features["component_type"] == "email body":
                rule_list.extend(email_body_rules.split("\n"))
                if self.template_settings.get("follow_tone", True):
                    rule_list.extend(email_tone_rules.split("\n"))
                if self.template_settings.get("follow_length", True):
                    rule_list.extend(email_length_rules.split("\n"))
            elif features["component_type"] == "introductory-text":
                rule_list.extend(linkedin_ads_introductory_text_rules.split("\n"))
            elif features["component_type"] == "headline":
                rule_list.extend(linkedin_ads_headline_rules.split("\n"))
            elif features["component_type"] == "description":
                rule_list.extend(linkedin_ads_description_rules.split("\n"))
            elif features["component_type"] == "ad-copy":
                rule_list.extend(linkedin_ads_ad_copy_rules.split("\n"))
        if self.foundation_model and "claude" in self.foundation_model:
            rule_list.extend(claude_rules.split("\n"))
        if (
            "content_type" in features
            and features["content_type"] == ContentType.BlogPost
        ):
            rule_list.extend(blog_post_rules.split("\n"))

        rules = rules_intro + "\n<rules>\n"
        rules += "\n".join(
            [f"{rule_idx+1}. {rule}" for rule_idx, rule in enumerate(rule_list)]
        )
        rules += "\n</rules>"

        return rules


class RulesRepurposePromptBuilder(BasePromptBuilder):

    def __init__(
        self,
        content_source_format=None,
        full_page_html_gen=True,
        use_template: bool = False,
        template_settings: dict = {},
        foundation_model=None,
    ):
        super().__init__(name="rules_repurpose")
        self.content_source_format = content_source_format
        self.full_page_html_gen = full_page_html_gen
        self.use_template = use_template
        self.template_settings = template_settings
        self.foundation_model = foundation_model

    def build(self, features) -> str:
        rules = self.get_rules(features)
        return rules

    def get_rules(self, features):
        rules_intro = RULES_INTRO
        repurpose_rules = REPURPOSE_RULES
        email_subject_rules = EMAIL_SUBJECT_RULES
        email_body_rules = EMAIL_BODY_RULES_REPURPOSING
        linkedin_ads_introductory_text_rules = LINKEDIN_ADS_INTRODUCTORY_TEXT_RULES
        linkedin_ads_headline_rules = LINKEDIN_ADS_HEADLINE_RULES
        linkedin_ads_description_rules = LINKEDIN_ADS_DESCRIPTION_RULES
        linkedin_ads_ad_copy_rules = LINKEDIN_ADS_AD_COPY_RULES
        repurpose_html_rules = REPURPOSE_HTML_RULES
        repurpose_template_rule = REPURPOSE_TEMPLATE_RULE
        repurpose_tone_following_rule = REPURPOSE_TONE_FOLLOWING_RULE
        repurpose_length_following_rule = REPURPOSE_LENGTH_FOLLOWING_RULE
        claude_rules = CLAUDE_RULES
        blog_post_rules = BLOG_POST_RULES
        banned_words_rules = BANNED_WORDS_RULES
        banned_sentence_format_rules = BANNED_SENTENCE_FORMAT_RULES
        markdown_rules = MARKDOWN_RULES
        rule_list = repurpose_rules.split("\n")
        if (
            "content_type" in features
            and features["content_type"] != ContentType.QuotesHighlights
        ):
            rule_list.extend(banned_words_rules.split("\n"))
        rule_list.extend(banned_sentence_format_rules.split("\n"))
        if self.use_template:
            rule_list.extend(repurpose_template_rule.split("\n"))
            if self.template_settings.get("follow_tone", True):
                rule_list.extend(repurpose_tone_following_rule.split("\n"))
            if self.template_settings.get("follow_length", True):
                rule_list.extend(repurpose_length_following_rule.split("\n"))

        if self.content_source_format == "Html" and self.full_page_html_gen:
            rule_list.extend(repurpose_html_rules.split("\n"))

        if "component_type" in features:
            if features["component_type"] == "email subject":
                rule_list.extend(email_subject_rules.split("\n"))
            elif features["component_type"] != "email subject" and features[
                "content_type"
            ] in (ContentType.EmailMarketing, ContentType.EmailSDR):
                rule_list.extend(email_body_rules.split("\n"))
            elif features["component_type"] == "introductory-text":
                rule_list.extend(linkedin_ads_introductory_text_rules.split("\n"))
            elif features["component_type"] == "headline":
                rule_list.extend(linkedin_ads_headline_rules.split("\n"))
            elif features["component_type"] == "description":
                rule_list.extend(linkedin_ads_description_rules.split("\n"))
            elif features["component_type"] == "ad-copy":
                rule_list.extend(linkedin_ads_ad_copy_rules.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.EmailSDR
        ):
            rule_list.extend(REPURPOSING_SDR_EMAIL_RULES.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.EmailMarketing
        ):
            rule_list.extend(REPURPOSE_MARKETING_EMAIL_RULES.split("\n"))

        if self.foundation_model and "claude" in self.foundation_model:
            rule_list.extend(claude_rules.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.BlogPost
        ):
            rule_list.extend(blog_post_rules.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.MessageLinkedin
        ):
            rule_list.extend(LINKEDIN_MESSAGE_RULES.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.QuotesHighlights
        ):
            rule_list.extend(QUOTES_RULES.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.Statistics
        ):
            rule_list.extend(STATISTICS_RULES.split("\n"))

        # append markdown rules
        rule_list.extend(markdown_rules.split("\n"))
        rules = rules_intro + "\n<rules>\n"
        rules += "\n".join(
            [f"{rule_idx+1}. {rule}" for rule_idx, rule in enumerate(rule_list)]
        )
        rules += "\n</rules>"

        return rules


class JointRulesBuilder(BasePromptBuilder):
    def __init__(
        self,
        template_settings: dict = None,
        foundation_model=None,
    ):
        super().__init__(name="joint_generation_rules")
        self.template_settings = template_settings
        self.foundation_model = foundation_model

    def build(self, features) -> str:
        rules = self.get_rules(features)

        return rules

    def get_rules(self, features):
        rules_intro = RULES_INTRO
        joint_base_rules = JOINT_BASE_RULES
        value_prop_rules = VALUE_PROP_RULES
        company_rules = COMPANY_RULES
        contact_rules = CONTACT_RULES
        tone_rules = TONE_RULES
        length_rules = JOINT_LENGTH_RULES
        core_message_and_key_point_rules = CORE_MESSAGE_AND_KEY_POINT_RULES
        no_template_follow_rules = NO_TEMPLATE_FOLLOW_RULES
        claude_rules = CLAUDE_RULES
        blog_post_rules = BLOG_POST_RULES
        banned_words_rules = BANNED_WORDS_RULES
        banned_sentence_format_rules = BANNED_SENTENCE_FORMAT_RULES
        email_sdr_personalization_rules = EMAIL_SDR_PERSONALIZATION_RULES

        rule_list = joint_base_rules.split("\n")
        if (
            "content_type" in features
            and features["content_type"] != ContentType.QuotesHighlights
        ):
            rule_list.extend(banned_words_rules.split("\n"))
        rule_list.extend(banned_sentence_format_rules.split("\n"))

        follow_tone = self.template_settings.get("follow_tone", True)
        follow_length = self.template_settings.get("follow_length", True)
        follow_core_message_and_key_point = self.template_settings.get(
            "follow_core_message_and_key_point", True
        )

        if follow_tone:
            rule_list.extend(tone_rules.split("\n"))
        if follow_length:
            rule_list.extend(length_rules.split("\n"))
        if follow_core_message_and_key_point:
            rule_list.extend(core_message_and_key_point_rules.split("\n"))

        # if both follow_core_message_and_key_point and follow_length are false, add an additional rule.
        if not follow_core_message_and_key_point and not follow_length:
            rule_list.extend(no_template_follow_rules.split("\n"))

        if "has_value_prop" in features and features["has_value_prop"]:
            rule_list.extend(value_prop_rules.split("\n"))
        if (
            "target_meta_types" in features
            and "Company" in features["target_meta_types"]
        ):
            rule_list.extend(company_rules.split("\n"))

        if (
            "target_meta_types" in features
            and "Contact" in features["target_meta_types"]
        ):
            rule_list.extend(contact_rules.split("\n"))

        if self.foundation_model and "claude" in self.foundation_model:
            rule_list.extend(claude_rules.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.BlogPost
        ):
            rule_list.extend(blog_post_rules.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.EmailSDR
        ):
            rule_list.extend(email_sdr_personalization_rules.split("\n"))

        rules = rules_intro + "\n<rules>\n"
        rules += "\n".join(
            [f"{rule_idx+1}. {rule}" for rule_idx, rule in enumerate(rule_list)]
        )
        rules += "\n</rules>"

        return rules


class EmailTemplatePersonalizationRulesPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
    ):
        super().__init__(name="email_template_personalization_rules")

    def build(self, features) -> str:
        rules = self.get_rules(features)
        return rules

    def get_rules(self, features):
        rules_intro = RULES_INTRO
        banned_words_rules = BANNED_WORDS_RULES
        banned_sentence_format_rules = BANNED_SENTENCE_FORMAT_RULES
        email_template_p13n_rules = EMAIL_TEMPLATE_PERSONALIZATION_RULES

        rule_list = email_template_p13n_rules.split("\n")
        rule_list.extend(banned_words_rules.split("\n"))
        rule_list.extend(banned_sentence_format_rules.split("\n"))
        rules = rules_intro + "\n"
        rules += "\n".join(
            [f"{rule_idx+1}. {rule}" for rule_idx, rule in enumerate(rule_list)]
        )
        return rules


class TemplateGenerationRulesPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        foundation_model=None,
    ):
        super().__init__(name="template_generation_rules")
        self.foundation_model = foundation_model

    def build(self, features) -> str:
        rules = self.get_rules(features)

        return rules

    def get_rules(self, features):
        rules_intro = RULES_INTRO
        template_gen_rules = TEMPLATE_GEN_RULES
        claude_rules = CLAUDE_RULES
        blog_post_rules = BLOG_POST_RULES
        banned_words_rules = BANNED_WORDS_RULES
        banned_sentence_format_rules = BANNED_SENTENCE_FORMAT_RULES

        rule_list = template_gen_rules.split("\n")
        rule_list.extend(banned_words_rules.split("\n"))
        rule_list.extend(banned_sentence_format_rules.split("\n"))

        if self.foundation_model and "claude" in self.foundation_model:
            rule_list.extend(claude_rules.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.BlogPost
        ):
            rule_list.extend(blog_post_rules.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.EmailSDR
        ):
            rule_list.extend(EMAIL_TEMPLATE_RULES.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.EmailSDR
            and not features.get("template_length_instructions")
        ):
            rule_list.extend(EMAIL_TEMPLATE_LENGTH_RULES.split("\n"))

        if (
            "target_meta_types" in features
            and "Contact" in features["target_meta_types"]
            and "content_type" in features
            and features["content_type"]
            in (
                ContentType.EmailMarketing,
                ContentType.EmailSDR,
            )
        ):
            rule_list.extend(EMAIL_TEMPLATE_CONTACT_RULES.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.MessageLinkedin
        ):
            rule_list.extend(LINKEDIN_MESSAGE_RULES.split("\n"))

        rules = rules_intro + "\n<rules>\n"
        rules += "\n".join(
            [f"{rule_idx+1}. {rule}" for rule_idx, rule in enumerate(rule_list)]
        )
        rules += "\n</rules>"

        return rules


class JointRepurposeRulesPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        use_template: bool = False,
        template_settings: dict = {},
        foundation_model=None,
    ):
        super().__init__(name="rules")
        self.use_template = use_template
        self.template_settings = template_settings
        self.foundation_model = foundation_model

    def build(self, features) -> str:
        rules = self.get_rules(features)
        return rules

    def get_rules(self, features):
        rules_intro = RULES_INTRO
        email_joint_repurpose_rules = REPURPOSE_EMAIL_JOINT_RULES
        joint_rules_repurpose = JOINT_RULES_REPURPOSE
        email_joint_rule_length = REPURPOSE_EMAIL_JOINT_RULE_LENGTH
        email_joint_rule_tone = REPURPOSE_EMAIL_JOINT_RULE_TONE
        repurpose_template_rule = REPURPOSE_TEMPLATE_RULE
        repurpose_tone_following_rule = REPURPOSE_TONE_FOLLOWING_RULE
        repurpose_length_following_rule = REPURPOSE_LENGTH_FOLLOWING_RULE
        claude_rules = CLAUDE_RULES
        banned_words_rules = BANNED_WORDS_RULES
        banned_sentence_format_rules = BANNED_SENTENCE_FORMAT_RULES
        markdown_rules = MARKDOWN_RULES
        rule_list = joint_rules_repurpose.split("\n")

        if (
            "content_type" in features
            and features["content_type"] != ContentType.QuotesHighlights
        ):
            rule_list.extend(banned_words_rules.split("\n"))
        rule_list.extend(banned_sentence_format_rules.split("\n"))

        if self.use_template:
            rule_list.extend(repurpose_template_rule.split("\n"))
            if self.template_settings.get("follow_tone", True):
                rule_list.extend(repurpose_tone_following_rule.split("\n"))
            if self.template_settings.get("follow_length", True):
                rule_list.extend(repurpose_length_following_rule.split("\n"))
        no_template = not self.use_template
        if "content_type" in features and features["content_type"] in (
            ContentType.EmailMarketing,
            ContentType.EmailSDR,
        ):

            if no_template:
                rule_list.extend(email_joint_repurpose_rules.split("\n"))

            if no_template or (
                self.template_settings
                and not self.template_settings.get("follow_tone", True)
            ):
                rule_list.extend(email_joint_rule_tone.split("\n"))

            if no_template or (
                self.template_settings
                and not self.template_settings.get("follow_length", True)
            ):
                rule_list.extend(email_joint_rule_length.split("\n"))

        if (
            "content_type" in features
            and features["content_type"] == ContentType.EmailSDR
            and no_template
        ):
            rule_list.extend(REPURPOSING_SDR_EMAIL_RULES.split("\n"))
        if (
            "content_type" in features
            and features["content_type"] == ContentType.MessageLinkedin
        ):
            rule_list.extend(LINKEDIN_MESSAGE_RULES.split("\n"))

        if self.foundation_model and "claude" in self.foundation_model:
            rule_list.extend(claude_rules.split("\n"))

        # append markdown rules
        rule_list.extend(markdown_rules.split("\n"))

        rules = rules_intro + "\n<rules>\n"
        rules += "\n".join(
            [f"{rule_idx+1}. {rule}" for rule_idx, rule in enumerate(rule_list)]
        )
        rules += "\n</rules>"

        return rules
