from ...shared_types import ContentType
from ..prompt_library.instruct_prompt.repurpose_instruct import (
    TEMPLATE_GENERATION_EMAIL_INSTRUCT_MSG,
    TEMPLATE_GENERATION_INSTRUCT_MSG,
    TEMPLATE_GENERATION_LINKEDIN_ADS_INSTRUCT_MSG,
)
from .base_prompt_builder import BasePromptBuilder


class TemplateGenerationInstructPromptBuilder(BasePromptBuilder):
    def __init__(self, content_type):
        super().__init__(name="instruct_msg")
        self.content_type = content_type

    def get_instruct_template(self):
        if (
            self.content_type == ContentType.EmailMarketing
            or self.content_type == ContentType.EmailSDR
        ):
            return TEMPLATE_GENERATION_EMAIL_INSTRUCT_MSG
        elif self.content_type == ContentType.AdCampaignLinkedin:
            return TEMPLATE_GENERATION_LINKEDIN_ADS_INSTRUCT_MSG
        else:
            return TEMPLATE_GENERATION_INSTRUCT_MSG

    def build(self, features):
        return self.get_instruct_template()
