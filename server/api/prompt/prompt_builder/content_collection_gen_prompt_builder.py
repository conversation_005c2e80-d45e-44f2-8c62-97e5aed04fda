import logging

from ...shared_types import ContentType
from ...validator.campaign_validator import CampaignGoal
from ..prompt_library.instruct_prompt.content_collection_instruct import (
    CONTENT_COLLECTION_AD_CAMPAIGN_INSTRUCT_PROMPT_TEMPLATE,
    CONTENT_COLLECTION_BLOG_POST_INSTRUCT_PROMPT_TEMPLATE,
    CONTENT_COLLECTION_EMAIL_MARKETING_INSTRUCT_PROMPT_TEMPLATE,
    CONTENT_COLLECTION_EMAIL_SDR_INSTRUCT_PROMPT_TEMPLATE,
    CONTENT_COLLECTION_INSTRUCT_PROMPT_TEMPLATE,
    CONTENT_COLLECTION_LINKEDIN_ADS_INSTRUCT_PROMPT_TEMPLATE,
)
from ..prompt_library.instruct_prompt.content_collection_seq_template_instruct import (
    CONTENT_COLLECTION_EMAIL_MARKETING_SEQ_TEMPLATE_INSTRUCT_PROMPT_TEMPLATE,
    CONTENT_COLLECTION_EMAIL_SDR_SEQ_TEMPLATE_INSTRUCT_PROMPT_TEMPLATE,
    CONTENT_COLLECTION_SEQ_TEMPLATE_INSTRUCT_PROMPT_TEMPLATE,
)
from .base_prompt_builder import BasePromptBuilder


class ContentCollectionInstructPromptBuilder(BasePromptBuilder):
    def __init__(self, content_type=None, content_goal=None):
        super().__init__(
            name="instruct_msg",
        )
        self.content_type = content_type
        self.content_goal = content_goal

    def build(self, features):
        return self.get_instruct_template()

    def get_instruct_template(self):
        if self.content_goal == CampaignGoal.Repurposing:
            if self.content_type == ContentType.EmailMarketing:
                return CONTENT_COLLECTION_EMAIL_MARKETING_INSTRUCT_PROMPT_TEMPLATE
            elif self.content_type == ContentType.EmailSDR:
                return CONTENT_COLLECTION_EMAIL_SDR_INSTRUCT_PROMPT_TEMPLATE
            elif self.content_type == ContentType.BlogPost:
                return CONTENT_COLLECTION_BLOG_POST_INSTRUCT_PROMPT_TEMPLATE
            elif self.content_type == ContentType.AdCampaignLinkedin:
                return CONTENT_COLLECTION_LINKEDIN_ADS_INSTRUCT_PROMPT_TEMPLATE
            elif self.content_type == ContentType.AdCampaignGeneral:
                return CONTENT_COLLECTION_AD_CAMPAIGN_INSTRUCT_PROMPT_TEMPLATE
            else:
                return CONTENT_COLLECTION_INSTRUCT_PROMPT_TEMPLATE
        elif self.content_goal == CampaignGoal.SeqPersonalizeTemplate:
            if self.content_type == ContentType.EmailMarketing:
                return CONTENT_COLLECTION_EMAIL_MARKETING_SEQ_TEMPLATE_INSTRUCT_PROMPT_TEMPLATE
            elif self.content_type == ContentType.EmailSDR:
                return (
                    CONTENT_COLLECTION_EMAIL_SDR_SEQ_TEMPLATE_INSTRUCT_PROMPT_TEMPLATE
                )
            else:
                return CONTENT_COLLECTION_SEQ_TEMPLATE_INSTRUCT_PROMPT_TEMPLATE
        else:
            logging.error(f"Invalid content goal: {self.content_goal}")
            return CONTENT_COLLECTION_INSTRUCT_PROMPT_TEMPLATE
