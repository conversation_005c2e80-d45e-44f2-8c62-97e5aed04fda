from ...validator.campaign_validator import CampaignGoal
from ..prompt_library.instruct_prompt.default_instruct import (
    REF_CONTENT_PROMPT_REPURPOSING,
    REF_CONTENT_PROMPTS,
)
from .base_prompt_builder import BasePromptBuilder


class HumanMsgAssetPromptBuilder(BasePromptBuilder):
    def __init__(self, repurpose: bool = False, content_goal: CampaignGoal = None):
        super().__init__(name="human_msg_asset")
        self.repurpose = repurpose
        self.content_goal = content_goal

    def build(self, features):
        if self.repurpose:
            if "asset_context" not in features or not features.get("asset_context"):
                raise ValueError(
                    "Anchor content is required for generating a repurposing content. Please add some anchor content before starting the generation."
                )
            return REF_CONTENT_PROMPT_REPURPOSING
        elif self.content_goal == CampaignGoal.SeqPersonalizeTemplate:
            return REF_CONTENT_PROMPT_REPURPOSING
        else:
            return REF_CONTENT_PROMPTS
