from ..prompt_library.instruct_prompt.default_instruct import (
    HUMAN_MSG_COMPANY,
    HUMAN_MSG_COMPANY_NEW,
)
from .base_prompt_builder import BasePromptBuilder


class HumanMsgCompanyPromptBuilder(BasePromptBuilder):
    def __init__(self, use_company_summary=False):
        super().__init__(name="human_msg_company")
        self.use_company_summary = use_company_summary

    def build(self, features):
        if self.use_company_summary:
            return HUMAN_MSG_COMPANY_NEW
        return HUMAN_MSG_COMPANY
