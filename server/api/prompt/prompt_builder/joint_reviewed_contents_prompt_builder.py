from ..prompt_library.instruct_prompt.default_instruct import JOINT_REVIEWED_CONTENTS
from .base_prompt_builder import BasePromptBuilder


class JointReviewedContentsPromptBuilder(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="joint_reviewed_contents_msg")

    def build(self, features):
        return self.get_reviewed_contents_template(features)

    def get_reviewed_contents_template(self, features):
        return JOINT_REVIEWED_CONTENTS
