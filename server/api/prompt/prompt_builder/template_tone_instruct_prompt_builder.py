from ..prompt_library.instruct_prompt.default_instruct import (
    TONE_SAMPLE_INSTRUCT_PROMPT,
)
from .base_prompt_builder import BasePromptBuilder


class TemplateToneInstructPromptBuilder(BasePromptBuilder):

    def __init__(self):
        super().__init__(name="template_tone_instruct_msg")

    def build(self, features):
        return self.get_tone_instruct_template(features)

    def get_tone_instruct_template(self, features):
        template_settings_msg = ""
        if features and "tone_reference" in features:
            template_settings_msg += TONE_SAMPLE_INSTRUCT_PROMPT + "\n"

        return template_settings_msg
