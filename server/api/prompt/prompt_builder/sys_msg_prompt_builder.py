from ..prompt_library.prompt_sys_msg import (
    SYS_MSG,
    SYS_MSG_CONTENT_COLLECTION,
    SYS_MSG_REPURPOSE,
    SYS_MSG_REPURPOSE_W_GUIDELINES,
    SYS_MSG_W_GUIDELINES,
)
from .base_prompt_builder import BasePromptBuilder


class SysMsgPromptBuilder(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="sys_msg")

    def build(self, features):
        if "brand_guidelines" in features and features["brand_guidelines"]:
            return SYS_MSG_W_GUIDELINES
        return SYS_MSG


class SysMsgRepurposePromptBuilder(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="sys_msg_repurpose")

    def build(self, features):
        if "brand_guidelines" in features and features["brand_guidelines"]:
            return SYS_MSG_REPURPOSE_W_GUIDELINES
        return SYS_MSG_REPURPOSE


class SysMsgContentCollectionPromptBuilder(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="sys_msg_content_collection")

    def build(self, features):
        return SYS_MSG_CONTENT_COLLECTION
