from ..prompt_library.instruct_prompt.default_instruct import (
    AI_MSG_ASSET,
    AI_MSG_ASSET_REPURPOSING,
)
from .base_prompt_builder import BasePromptBuilder


class AIMsgAssetPromptBuilder(BasePromptBuilder):
    def __init__(self, repurpose: bool = False):
        super().__init__(name="ai_msg_asset")
        self.repurpose = repurpose

    def build(self, features):
        if self.repurpose:
            return AI_MSG_ASSET_REPURPOSING
        return AI_MSG_ASSET
