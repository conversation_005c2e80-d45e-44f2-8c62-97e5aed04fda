from ..prompt_library.instruct_prompt.default_instruct import (
    INSTRUCT_MSG_GENERIC_JOINT,
    INSTRUCT_MSG_GENERIC_JOINT_LENGTH,
)
from ..prompt_library.instruct_prompt.repurpose_instruct import (
    INSTRUCT_MSG_GENERIC_JOINT_REPURPOSE,
    INSTRUCT_MSG_GENERIC_JOINT_REPURPOSE_COLLECTION,
)
from .base_prompt_builder import BasePromptBuilder


class JointInstructRepurposePromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        content_type,
        content_source_format,
        is_in_content_group_collection=False,
    ):
        super().__init__(name="instruct_msg")
        self.content_type = content_type
        self.content_source_format = content_source_format
        self.is_in_content_group_collection = is_in_content_group_collection

    def build(self, features):
        if self.is_in_content_group_collection:
            return INSTRUCT_MSG_GENERIC_JOINT_REPURPOSE_COLLECTION
        else:
            return INSTRUCT_MSG_GENERIC_JOINT_REPURPOSE


class JointInstructPersonalizationPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        content_type,
        content_source_format,
        template_settings={},
    ):
        super().__init__(name="instruct_msg")
        self.content_type = content_type
        self.content_source_format = content_source_format
        self.template_settings = template_settings

    def build(self, features):
        return self.get_instruct_template(features)

    def get_instruct_template(self, features):
        instruct_msg_generic_joint = INSTRUCT_MSG_GENERIC_JOINT

        instruct_msg_generic_joint_length = INSTRUCT_MSG_GENERIC_JOINT_LENGTH
        if self.template_settings:
            follow_length = self.template_settings.get("follow_length", True)
            if follow_length:
                return instruct_msg_generic_joint + instruct_msg_generic_joint_length
            else:
                return instruct_msg_generic_joint
        else:
            return instruct_msg_generic_joint + instruct_msg_generic_joint_length
