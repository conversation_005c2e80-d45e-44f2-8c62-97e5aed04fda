from ..prompt_library.instruct_prompt.default_instruct import (
    HTML_TAG_MSG,
    INSTRUCT_HTML_SURROUNDING,
    INSTRUCT_ORIG_MSG,
    INSTRUCT_PDF_SURROUNDING,
    INSTRUCT_TXT_SURROUNDING,
    PDF_PAGE_NUM_MSG,
    TARGET_GENERATE_LENGTH,
    TARGET_GENERATE_LENGTH_PDF,
    TARGET_GENERATE_NO_LENGTH,
)
from .base_prompt_builder import BasePromptBuilder


class InstructPromptBuilder(BasePromptBuilder):
    def __init__(
        self,
        content_type,
        content_source_format,
        template_settings,
    ):
        super().__init__(name="instruct_msg")
        self.content_type = content_type
        self.content_source_format = content_source_format
        self.template_settings = template_settings

    def build(self, features):
        return self.get_instruct_template()

    def get_instruct_template(self):
        if self.template_settings:
            follow_length = self.template_settings.get("follow_length", True)
        else:
            follow_length = True

        instruct_msg = ""
        if (
            "Email" not in self.content_type
        ):  # Email content type does not have surrounding context
            if self.content_source_format == "Text":
                instruct_msg += INSTRUCT_TXT_SURROUNDING
            elif self.content_source_format == "Html":
                instruct_msg += INSTRUCT_HTML_SURROUNDING
            elif self.content_source_format == "PDF":
                instruct_msg += INSTRUCT_PDF_SURROUNDING

        instruct_msg += INSTRUCT_ORIG_MSG
        if self.content_source_format == "Html":
            instruct_msg += HTML_TAG_MSG
        if self.content_source_format == "PDF":
            instruct_msg += PDF_PAGE_NUM_MSG
        if follow_length:
            if self.content_source_format == "PDF":
                instruct_msg += TARGET_GENERATE_LENGTH_PDF
            else:
                instruct_msg += TARGET_GENERATE_LENGTH
        else:
            instruct_msg += TARGET_GENERATE_NO_LENGTH

        return instruct_msg
