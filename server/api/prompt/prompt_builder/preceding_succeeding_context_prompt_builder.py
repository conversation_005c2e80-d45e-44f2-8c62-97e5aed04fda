from ...utils import clean_html
from ..prompt_library.prompt_rules import (
    PRECEDING_VARIATION_RULES,
    SUCCEEDING_VARIATION_RULES,
)
from .base_prompt_builder import BasePromptBuilder


class PrecedingSucceedingContextPromptBuilder(BasePromptBuilder):
    def __init__(self):
        super().__init__(name="preceding_suceeding_context")

    def build(self, features) -> str:
        preceding_variation_rules = PRECEDING_VARIATION_RULES

        succeeding_variation_rules = SUCCEEDING_VARIATION_RULES

        rules = []

        if (
            "preceding_element" in features
            and "preceding_variation" in features
            and clean_html(features["preceding_element"])
            != features["preceding_variation"]
        ):
            rules.append(preceding_variation_rules)
        if (
            "succeeding_element" in features
            and "succeeding_variation" in features
            and clean_html(features["succeeding_element"])
            != features["succeeding_variation"]
        ):
            rules.append(succeeding_variation_rules)

        return "\n".join(rules)
