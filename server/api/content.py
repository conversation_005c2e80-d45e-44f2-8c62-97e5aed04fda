import logging
import os
import traceback
import uuid

from django.db import transaction
from django.utils import timezone

from .content_gen.disjoint_content_gen import DisjointContentGenerator
from .content_gen.email_template_personalization_gen import (
    EmailTemplatePersonalizationGenerator,
)
from .content_gen.free_content_gen import Free<PERSON>ontentGenerator
from .content_gen.full_page_html_gen import FullPageHtmlGenerator
from .content_gen.joint_content_gen import JointContentGenerator
from .content_gen.long_form_content_gen import LongFormContentGenerator
from .content_gen.seq_personalize_template_gen import SeqPersonalizeTemplateGenerator
from .content_gen.template_gen import TemplateGenerator
from .feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    ContentGenSettings,
    GenerateEnv,
)
from .langsmith_integration import BaseTracableClass, dynamic_traceable
from .models import Content, ContentVariation
from .playbook_build.playbook_builder import PlaybookBuilder
from .thread_locals import get_current_user
from .utils import TofuLLMCache, measure_latency
from .validator.campaign_validator import CampaignGoal


class ContentGenerator(BaseTracableClass):
    def __init__(self, playbook_handler, content):
        self.playbook_handler = playbook_handler

        self.content_instance = content
        self.content_group = content.content_group
        self.campaign = (
            content.content_group.campaign if content.content_group else None
        )

        self._data_wrapper = BaseContentWrapper.from_data_instance(content)
        self._gen_settings = None
        self._request_id = str(uuid.uuid4())
        self._features = {}

    def get_metadata(self):
        user = (
            self.campaign.creator
            if self.campaign and hasattr(self.campaign, "creator")
            else None
        )
        if not user:
            logging.error("No user found for content gen, use current user")
            user = get_current_user()

        metadata = {
            "content_id": self.content_instance.id,
            "campaign_id": self.campaign.id if self.campaign else None,
            "content_type": self._data_wrapper.content_type,
            "username": user.username if user else None,
            "request_id": self._gen_settings.request_id,
            "campaign_goal": self._data_wrapper.campaign_goal,
            "environment": os.environ.get("TOFU_ENV", default="unknown"),
            "is_campaign_v3": self._data_wrapper.is_campaign_v3,
        }

        def dict_to_str(d):
            # Sort the dictionary by key to ensure consistent order
            sorted_items = sorted(d.items())
            # Join the key-value pairs with ':' and each pair with '_'
            return "_".join(f"{key}:{value}" for key, value in sorted_items)

        metadata["targets"] = dict_to_str(
            self.content_instance.content_params.get("targets", {})
        )

        if "orig_campaign_id" in self.campaign.campaign_params:
            metadata["orig_campaign_id"] = self.campaign.campaign_params[
                "orig_campaign_id"
            ]
        if "orig_content_group_id" in self.content_group.content_group_params:
            metadata["orig_content_group_id"] = self.content_group.content_group_params[
                "orig_content_group_id"
            ]
        if "orig_content_id" in self.content_instance.content_params:
            metadata["orig_content_id"] = self.content_instance.content_params[
                "orig_content_id"
            ]
        metadata.update(self._gen_settings.config_map)
        return metadata

    # just wrapper
    def set_settings(self, **kwargs):
        try:
            self._gen_settings = ContentGenSettings(self._data_wrapper, **kwargs)
        except Exception as e:
            logging.error(f"Error setting content gen settings: {e}")
            self._gen_settings = None

    @measure_latency
    @dynamic_traceable(name="content_gen")
    def gen(self):
        if not self.content_instance:
            raise Exception("Content instance not initialized")
        if not self._gen_settings:
            logging.error("Settings not initialized in content/gen")
            raise Exception("Settings not initialized in content/gen")
        if self._gen_settings.save_variations:
            self._initialize_content_status()

        # pre-gen processing
        gen_env = GenerateEnv(
            data_wrapper=self._data_wrapper,
            gen_settings=self._gen_settings,
        )
        with TofuLLMCache():
            PlaybookBuilder(
                self.playbook_handler.playbook_instance
            ).pre_build_playbook_context(self._data_wrapper.target_params)

            # gen
            generator = self._create_generator(gen_env)
            variations = generator.gen()

        # post-gen processing

        self._features = generator._features
        if self._gen_settings.save_variations:
            ret_variation = self._save_variations(variations)
            return ret_variation
        else:
            return variations

    def merge_variations_to_db(self, db_variations, gen_variations):
        """
        Merge the generated variations into the existing variations in db
        """
        for component_id in gen_variations.keys():
            if component_id in db_variations:
                db_component_variations = (
                    db_variations[component_id].get("meta", {}).get("variations", [])
                )
                db_variations[component_id]["meta"]["current_variation_index"] = len(
                    db_component_variations
                )
                db_variations[component_id]["meta"]["current_version"] = gen_variations[
                    component_id
                ]["meta"]["current_version"]
                db_variations[component_id]["meta"]["variations"].extend(
                    gen_variations[component_id]["meta"]["variations"]
                )
            else:
                db_variations[component_id] = gen_variations[component_id]
        return db_variations

    def _initialize_content_status(self):
        if not isinstance(self.content_instance.content_status, dict):
            if self.content_instance.content_status:
                raise ValueError(
                    f"Invalid content_status for content {self.content_instance.id}"
                )
            self.content_instance.content_status = {}

        if "gen_status" in self.content_instance.content_status:
            self.content_instance.content_status["gen_status"].update(
                {
                    "status": "IN_PROGRESS",
                    "update_time": timezone.now().isoformat(),
                }
            )
            self.content_instance.save()

    def _ensure_content_exists(self):
        if not Content.objects.filter(pk=self.content_instance.pk).exists():
            raise ValueError(
                f"Content {self.content_instance.id} was deleted during generation"
            )

    def _get_locked_content_instance(self):
        return Content.objects.select_for_update().get(pk=self.content_instance.pk)

    def _save_or_update_variation(self, variations):
        ret_variation = ContentVariation.objects.filter(
            content=self.content_instance
        ).first()

        if ret_variation:
            ret_variation.variations = self.merge_variations_to_db(
                ret_variation.variations, variations
            )
            ret_variation.params.pop("is_reviewed", None)
            ret_variation.save()
        else:
            variation_params = self._get_variation_params()
            ret_variation = ContentVariation.objects.create(
                content=self.content_instance,
                params=variation_params,
                variations=variations,
            )
        return ret_variation

    def _get_variation_params(self):
        return {
            "targets": (
                self.content_instance.content_params.get("targets", {})
                if self.content_instance.content_params
                else {}
            ),
            "num_of_variations": self._gen_settings.num_of_variations,
        }

    def _update_generation_status(self, ret_variation):
        if self.content_group.components:
            number_content_components = len(self.content_group.components)
            number_generated_components = len(ret_variation.variations)
            if (
                number_generated_components == number_content_components
                and "gen_status" in self.content_instance.content_status
            ):
                self.content_instance.content_status["gen_status"] = {
                    "status": "FINISHED",
                    "update_time": timezone.now().isoformat(),
                }
            self.content_instance.save()

    def _is_seq_personalize_template(self):
        return (
            self.content_group.content_group_params.get("content_goal")
            == CampaignGoal.SeqPersonalizeTemplate
        )

    def _create_generator(self, gen_env):
        if self._gen_settings.free_gen:
            return FreeContentGenerator(gen_env=gen_env)
        elif self._gen_settings.is_long_form_generation:
            return LongFormContentGenerator(gen_env=gen_env)
        elif self._gen_settings.is_template_personalization:
            if self._data_wrapper.is_email:
                return EmailTemplatePersonalizationGenerator(gen_env=gen_env)
            else:
                raise Exception(
                    "Template personalization is only supported for email content"
                )
        elif self._gen_settings.template_generation:
            return TemplateGenerator(gen_env=gen_env)
        elif self._is_seq_personalize_template():
            return SeqPersonalizeTemplateGenerator(gen_env=gen_env)
        elif self._gen_settings.joint_generation:
            return JointContentGenerator(gen_env=gen_env)
        elif self._gen_settings.full_page_html_gen:
            return FullPageHtmlGenerator(gen_env=gen_env)
        else:
            return DisjointContentGenerator(gen_env=gen_env)

    def _save_variations(self, variations):
        with transaction.atomic():
            self._ensure_content_exists()
            self._get_locked_content_instance()
            ret_variation = self._save_or_update_variation(variations)
            self._update_generation_status(ret_variation)
            return ret_variation
