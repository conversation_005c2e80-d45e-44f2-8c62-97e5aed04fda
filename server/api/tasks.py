# async tasks using celery
# restart the celery worker if there's any code change to the tasks

import logging
import os
import time
import traceback

from server.celery import app

from .models import Playbook, TargetInfoGroup
from .playbook import PlaybookHandler
from .playbook_build.playbook_builder import PlaybookBuilder
from .release_tests.llm_inout_check.llm_check_runner import LLMInOutCheckRunner
from .sync.e2e.hubspot_sync import PlaybookHubspotSyncer
from .sync.e2e.salesforce_sync import PlaybookSalesforceSyncer
from .thread_locals import reset_current_playbook, set_current_playbook


@app.task(bind=True, acks_late=True)
def async_postprocess_playbook(self, playbook_id, changed_fields):
    try:
        time.sleep(5)
        playbook_handler = PlaybookHandler.load_from_db(playbook_id)
        playbook_instance = playbook_handler.playbook_instance

        column_ids = []
        if playbook_instance.settings.get("disablePlaybookPostProcessing", False):
            column_ids = ["company_info", "assets"]
            logging.error(
                f"playbook {playbook_id} is skipped from async_postprocess_playbook due to disablePlaybookPostProcessing"
            )

        set_current_playbook(playbook_instance)

        try:
            playbook_builder = PlaybookBuilder(playbook_instance)
            playbook_builder.update_context(column_ids=column_ids)
        except Exception as e:
            tb_str = traceback.format_exc()
            logging.error(f"Error in new playbook builder: {e}\n{tb_str}")
    except Exception as e:
        tb_str = traceback.format_exc()
        logging.error(
            f"Error in async_postprocess_playbook for playbook {playbook_id}: {e}\n{tb_str}"
        )
    finally:
        reset_current_playbook()
    return self.request.id


@app.task(bind=True, acks_late=True)
def async_refresh_playbook_context(self, playbook_id, column_ids):
    try:
        playbook_handler = PlaybookHandler.load_from_db(playbook_id)
        playbook_instance = playbook_handler.playbook_instance

        set_current_playbook(playbook_instance)

        # new flow
        playbook_builder = PlaybookBuilder(playbook_instance)
        playbook_builder.force_context_full_refresh(column_ids=column_ids)
    except Exception as e:
        logging.error(f"fail to refresh playbook context {e}")
    finally:
        reset_current_playbook()
    return self.request.id


def update_generation_golden_set():
    if os.getenv("TOFU_ENV", default="unknown") != "production":
        return

    try:
        LLMInOutCheckRunner.update_golden_set()
    except Exception as e:
        logging.error(f"Error updating golden set: {e}")


@app.task(bind=True, acks_late=True)
def async_export_tofu_insights(
    self, target_info_group_id, platform, insights_to_crm_field_mapping
):
    """
    Async task to export Tofu insights to CRM platforms for each target in a target group

    Currently supported platforms:
    - hubspot
    - salesforce
    """

    try:
        target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)
        playbook = target_info_group.playbook

        # Save task progress in cache
        task_id = self.request.id
        result = None

        if platform == "hubspot":
            # Initialize the hubspot syncer
            # we don't need to pass campaigns or list_id because we are not syncing any campaigns or lists
            syncer = PlaybookHubspotSyncer(playbook, target_info_group, [], None)

            # Export the insights
            result = syncer.export_tofu_insights(
                task_id=task_id,
                insights_to_crm_field_mapping=insights_to_crm_field_mapping,
            )

        elif platform == "salesforce":
            # Initialize the salesforce syncer
            # we don't need to pass campaigns or list_id because we are not syncing any campaigns or lists
            syncer = PlaybookSalesforceSyncer(playbook, target_info_group, [], None)

            # Export the insights
            result = syncer.export_tofu_insights(
                task_id=task_id,
                insights_to_crm_field_mapping=insights_to_crm_field_mapping,
            )

        else:
            raise ValueError(f"Unsupported platform: {platform}")

        # once the export is done, remember platform, insights_to_crm_field_mapping in target_info_group.meta
        target_info_group.meta["export_tofu_insights"] = {
            "platform": platform,
            "insights_to_crm_field_mapping": insights_to_crm_field_mapping,
        }
        target_info_group.save()

        return result

    except Exception as e:
        logging.exception(f"Error in exporting tofu insights to {platform}: {e}")
        raise
