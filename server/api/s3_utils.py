import base64
import csv
import hashlib
import json
import logging
import os
import tempfile
import time
from urllib.parse import parse_qs, urlparse

import boto3
import requests
from botocore.exceptions import ClientError

from .logger import tofu_axiom_logger


def upload_file(file_name, file_type, bucket="tofu-uploaded-files", object_name=None):
    """Upload a file to an S3 bucket

    :param file_name: File to upload
    :param bucket: Bucket to upload to
    :param object_name: S3 object name. If not specified then file_name is used
    :return: the file path if successful
    :raise: Exception if file cannot be uploaded
    """

    # If S3 object_name was not specified, use file_name
    if object_name is None:
        object_name = os.path.basename(file_name)

    # Upload the file
    s3_client = boto3.client("s3")
    response = s3_client.upload_file(file_name, bucket, object_name)
    file_path = f"/api/web/storage/s3-presigned-url?file=${file_name}&fileType=${file_type}&directory=${bucket}"
    return file_path


def save_json_to_s3(file_key, data, bucket_name="tofu-uploaded-files"):
    try:
        s3_client = boto3.client("s3")
        s3_client.put_object(
            Bucket=bucket_name,
            Key=file_key,
            Body=json.dumps(data),
            ContentType="application/json",
        )
        return True
    except ClientError as e:
        logging.exception(f"Error uploading to S3: {e}")
        return False


def validate_and_download_s3_json(s3_path):
    """
    Validates S3 URL format and downloads JSON data from S3.

    Args:
        s3_path (str): The S3 presigned URL

    Returns:
        dict: The parsed JSON data

    Raises:
        ValueError: If URL format is invalid or file is not JSON
        ClientError: If S3 download fails
    """
    # Validate URL format
    if not s3_path.startswith("/api/web/storage/s3-presigned-url"):
        raise ValueError(f"Invalid S3 URL format: {s3_path}")

    # Parse URL parameters
    parsed_url = urlparse(s3_path)
    query_params = parse_qs(parsed_url.query)

    # Validate required parameters
    required_params = ["file", "fileType", "directory"]
    for param in required_params:
        if param not in query_params:
            raise ValueError(f"Missing required parameter: {param}")

    # Validate file type is JSON
    if query_params["fileType"][0] != "application/json":
        raise ValueError(f"Invalid file type: {query_params['fileType'][0]}")

    # Extract file information
    file_name = query_params["file"][0]
    bucket_name = query_params["directory"][0]
    file_key = (
        file_name  # Don't include directory in the key since it's the bucket name
    )

    try:
        # Initialize S3 client
        s3_client = boto3.client("s3")

        # Download the file
        response = s3_client.get_object(Bucket=bucket_name, Key=file_key)

        # Read and parse JSON
        json_data = response["Body"].read().decode("utf-8")
        try:
            return json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {e}") from e

    except ClientError as e:
        logging.error(
            f"S3 download failed for bucket {bucket_name}, key {file_key}: {str(e)}"
        )
        raise ValueError(f"Failed to download from S3: {str(e)}") from e


def check_file_exists(s3_bucket, file_name):
    s3 = boto3.client("s3")
    try:
        s3.head_object(Bucket=s3_bucket, Key=file_name)
        return True
    except ClientError as e:
        # If a client error is thrown, check if it was a 404 error
        # If it was a 404 error, it means the object does not exist
        if e.response["Error"]["Code"] == "404" or e.response["Error"]["Code"] == "403":
            return False
        else:
            # If it was a different kind of error, re-raise the exception
            logging.error(f"Unexpected error: {e}")
            raise e
    return False


def download_url_content_to_s3(
    url: str, s3_bucket: str, s3_filename: str, content_type: str = None
) -> None:
    """
    Download content from URL and store it in S3 if not exists.

    Args:
        url: Source URL to download content from
        s3_bucket: Target S3 bucket
        s3_filename: Target S3 filename
        content_type: Optional content type for the S3 object
    """
    MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024  # 2GB limit
    CHUNK_SIZE = 1024 * 1024 * 8  # 8MB chunks
    TIMEOUT = 90  # 90 seconds
    MAX_RETRIES = 3
    # Check if file exists in S3
    if check_file_exists(s3_bucket, s3_filename):
        logging.info(f"File already exists in S3: {s3_filename}, skipping downloading")
        return  # File already exists

    # Only download and upload if file doesn't exist, we are not expecting this number to be large
    logging.info(f"Downloading file from {url} to {s3_filename}")
    tofu_axiom_logger.log_axiom(
        event_type="download_url_content_to_s3",
        url=url,
        s3_bucket=s3_bucket,
        s3_filename=s3_filename,
    )

    # Get file size before downloading
    try:
        head_response = requests.head(url, timeout=TIMEOUT)
        content_length = int(head_response.headers.get("content-length", 0))
        if content_length > MAX_FILE_SIZE:
            raise ValueError(
                f"File size {content_length} exceeds maximum allowed size of {MAX_FILE_SIZE}"
            )
    except requests.exceptions.RequestException as e:
        logging.warning(f"Failed to get content length: {e}")
        content_length = 0

    # Download with retries
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.get(url, stream=True, timeout=TIMEOUT)
            response.raise_for_status()
            break
        except requests.exceptions.RequestException as e:
            if attempt == MAX_RETRIES - 1:
                raise
            logging.warning(f"Download attempt {attempt + 1} failed: {e}")
            time.sleep(2**attempt)  # Exponential backoff

    # Create temp file and upload to S3
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        downloaded_size = 0
        for chunk in response.iter_content(chunk_size=CHUNK_SIZE):
            temp_file.write(chunk)
            downloaded_size += len(chunk)
            if downloaded_size > MAX_FILE_SIZE:
                os.unlink(temp_file.name)
                raise ValueError(
                    f"Download exceeded maximum allowed size of {MAX_FILE_SIZE}"
                )
        temp_file.flush()

        # Prepare upload parameters
        upload_args = {}
        if content_type:
            upload_args["ContentType"] = content_type

        # Upload to S3
        s3_client = boto3.client("s3")
        s3_client.upload_file(
            temp_file.name, s3_bucket, s3_filename, ExtraArgs=upload_args
        )

        # Clean up temp file
        os.unlink(temp_file.name)


def download_s3_file(s3_bucket, s3_filename):
    """Download a file from an S3 bucket

    Args:
        s3_bucket (str): Name of the S3 bucket
        s3_filename (str): Name of the file in S3 to download or a presigned URL

    Returns:
        bytes: The contents of the file as bytes

    Raises:
        ClientError: If the file cannot be downloaded or doesn't exist
        Exception: For other unexpected errors
    """
    try:
        # Check if s3_filename is a URL
        if s3_filename.startswith("http"):
            # Extract the key from the URL
            parsed_url = urlparse(s3_filename)
            # Remove the leading '/' if present
            key = parsed_url.path.lstrip("/")
            # Remove the bucket name from the path if present
            if key.startswith(s3_bucket + "/"):
                key = key[len(s3_bucket) + 1 :]
        else:
            key = s3_filename

        s3_client = boto3.client("s3")
        response = s3_client.get_object(Bucket=s3_bucket, Key=key)
        return response["Body"].read()
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            logging.error(f"File {key} not found in bucket {s3_bucket}")
        elif e.response["Error"]["Code"] == "403":
            logging.error(f"Access denied to file {key} in bucket {s3_bucket}")
        elif e.response["Error"]["Code"] == "NoSuchKey":
            logging.error(
                f"The specified key {key} does not exist in bucket {s3_bucket}"
            )
        else:
            logging.error(f"Unexpected error downloading file {key}: {e}")
        raise
    except Exception as e:
        logging.error(f"Error downloading file {key} from bucket {s3_bucket}: {e}")
        raise


def get_s3_image_base64(s3_bucket, s3_filename):
    """Get the base64 image from an S3 bucket

    Args:
        s3_bucket (str): Name of the S3 bucket
        s3_filename (str): Name of the file in S3 to download

    Returns:
        str: Base64 encoded string of the image

    Raises:
        ClientError: If the file cannot be downloaded or doesn't exist
        Exception: For other unexpected errors
    """
    try:
        # Download the image using our existing download function
        image_data = download_s3_file(s3_bucket, s3_filename)

        # Convert to base64
        base64_encoded = base64.b64encode(image_data).decode("utf-8")

        return base64_encoded

    except ClientError as e:
        logging.error(f"Failed to get base64 image from S3: {e}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error getting base64 image: {e}")
        raise


def copy_s3_file(source_path, destination_path, bucket_name=None):
    """
    Copy a file from one location to another within S3 without downloading it.

    Args:
        source_path: The source path/key in S3
        destination_path: The destination path/key in S3
        bucket_name: The S3 bucket name (optional if using the same bucket)

    Returns:
        The URL of the copied file
    """
    s3_client = boto3.client("s3")

    # Extract bucket and key information
    # If source_path is a full URL, parse it
    if source_path.startswith("/api/web/storage/s3-presigned-url"):
        parsed_url = urlparse(source_path)
        query_params = parse_qs(parsed_url.query)
        source_file = query_params.get("file", [""])[0]
        directory = query_params.get("directory", [""])[0]
        source_key = f"{directory}/{source_file}" if directory else source_file
    else:
        # Assume it's already a key
        source_key = source_path

    source_bucket = bucket_name or "tofu-uploaded-files"  # Use default if not specified
    destination_bucket = source_bucket  # Usually copying within the same bucket

    # Perform the copy operation
    s3_client.copy_object(
        CopySource={"Bucket": source_bucket, "Key": source_key},
        Bucket=destination_bucket,
        Key=destination_path,
    )

    # Return the URL format matching your application's convention
    return f"/api/web/storage/s3-presigned-url?file={destination_path.split('/')[-1]}&fileType=application/json&directory={destination_bucket}"


def write_csv_and_upload(data, filename, s3_bucket):
    """
    Write data to a CSV, upload to S3, and return a presigned URL.
    - data: list of dicts (preferred) or list of lists (with header as first row)
    - filename: base filename (without hash or extension)
    - s3_bucket: S3 bucket name
    """
    if not data:
        logging.warning("No data to write to CSV")
        return None
    if not isinstance(data, list):
        logging.error(
            f"Debug: Data is not a valid list for write_csv_and_upload: {data}"
        )
        return None

    # If data is a list of dicts, extract fieldnames and rows
    if isinstance(data, list) and data and isinstance(data[0], dict):
        fieldnames = sorted({k for row in data for k in row.keys()})
        csv_rows = [fieldnames]
        for row in data:
            csv_rows.append([row.get(f, "") for f in fieldnames])
    else:
        # Assume data is already a list of lists with header as first row
        csv_rows = data

    # Hash the data for unique filename
    data_hash = hashlib.md5(json.dumps(csv_rows).encode("utf-8")).hexdigest()
    s3_filename = f"{filename}-{data_hash}.csv"

    # Write CSV to a temporary file and upload to S3
    with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
        with open(temp_file.name, mode="w", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerows(csv_rows)
        upload_file(temp_file.name, "text/csv", s3_bucket, s3_filename)

    return f"/api/web/storage/s3-presigned-url?file={s3_filename}&fileType=text/csv&directory={s3_bucket}"
