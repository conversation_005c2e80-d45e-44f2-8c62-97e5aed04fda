import os
from functools import lru_cache

import jwt
from django.conf import settings
from dotenv import load_dotenv
from rest_framework import authentication, exceptions

from .models import Playbook, TofuUser

load_dotenv("../.env")
jwt_key = os.environ.get("JWT_KEY")
import base64
import datetime
import logging
import traceback
import uuid

import boto3
from botocore.exceptions import ClientError
from django.contrib.auth import authenticate

from .thread_locals import set_current_user


class SwaggerBasicAuthentication(authentication.BasicAuthentication):
    def authenticate(self, request):
        if not request.path.startswith("/api/docs/"):
            return None

        auth = request.META.get("HTTP_AUTHORIZATION", "")
        if not auth or not auth.startswith("Basic "):
            return None

        try:
            auth_parts = auth.split()
            if len(auth_parts) != 2 or auth_parts[0].lower() != "basic":
                return None

            credentials = base64.b64decode(auth_parts[1]).decode("utf-8")
            username, password = credentials.split(":")

            if (
                username == settings.SWAGGER_UI_USERNAME
                and password == settings.SWAGGER_UI_PASSWORD
            ):
                return (TofuUser.objects.get(username=username), None)
        except Exception as e:
            logging.error(f"Swagger UI authentication failed: {e}")
            return None

        return None


class TofuAuthentication(authentication.BaseAuthentication):
    def get_jwt_user(self, auth_parts):
        token = auth_parts[1].encode()
        try:
            data = jwt.decode(
                token,
                jwt_key,
                algorithms="HS256",
                options={"verify_exp": False},
            )
            user = TofuUser.objects.get(username=data["username"])
            return (user, data)
        except jwt.exceptions.DecodeError:
            # Handle JWT decode error
            logging.error(f"Error in decoding JWT token: {auth_parts}")
        except TofuUser.DoesNotExist:
            pass
        return (None, None)

    def get_basic_user(self, auth_parts):
        encoded_credentials = auth_parts[1]
        try:
            decoded_credentials = base64.b64decode(encoded_credentials).decode("utf-8")
            username, password = decoded_credentials.split(":", 1)
            user = authenticate(username=username, password=password)
            return (user, None)
        except (TypeError, ValueError, UnicodeDecodeError):
            # Handle decoding errors or malformed credentials
            logging.error(f"Error in decoding credentials: {auth_parts}")
        return (None, None)

    def authenticate(self, request):
        try:
            auth_header = request.META.get("HTTP_AUTHORIZATION")
            if not auth_header:
                return None

            auth_parts = auth_header.split()
            auth_scheme = auth_parts[0]
            if auth_scheme.lower() == "bearer":
                # Handle as JWT token
                user, data = self.get_jwt_user(auth_parts)
            elif auth_scheme.lower() == "basic":
                # Handle as Basic Authentication token
                user, data = self.get_basic_user(auth_parts)
            else:
                # Unknown or unsupported authentication scheme
                logging.error(
                    f"Unknown or unsupported authentication scheme: {auth_scheme}"
                )
                return None

            if not user:
                return None

            # for new users which is not verified
            if user.context and user.context.get("verified_user") == False:
                logging.error(
                    f"User {user.username} is not verified but trying to access the system"
                )
                return None
            else:
                set_current_user(user)
                return (user, data)
        except Exception as e:
            logging.error(
                f"Authentication failed: {e} with request {str(request)}\n{traceback.format_exc()}"
            )
            return None

        return None


def get_paragon_secret():
    secret_name = "paragon_secret"
    region_name = "us-west-2"

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=region_name)

    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
    except ClientError as e:
        # For a list of exceptions thrown, see
        # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
        raise e

    secret = get_secret_value_response["SecretString"]
    secret = eval(secret)["PARAGON_SECRET"]
    secret = secret.replace(r"\n", "\n")
    return secret


def gen_paragon_token(user, playbook) -> tuple[str, str]:
    if not playbook:
        logging.error(
            f"playbook is not provided for user {user.username} in gen_paragon_token"
        )
    token = {
        "username": user.username,
    }
    # playbook = Playbook.objects.filter(user=user).first()
    if playbook and playbook.company_domain:
        token["company_domain"] = playbook.company_domain
    if "company_domain" not in token:
        logging.warning(
            f"company_domain is not provided for user {user.username} in gen_paragon_token"
        )

    secret = get_paragon_secret()
    max_age = int(os.environ.get("PARAGON_DEFAULT_MAX_AGE", 3600))

    # Determine subject
    subject = token.get("username")
    if token.get("company_domain"):
        subject = token.get("company_domain")

    # Special case for tofuadmin
    if subject and subject.startswith("tofuadmin"):
        subject = "tofuadmin"

    # Prepare JWT payload
    payload = {
        "sub": subject,
        "iat": datetime.datetime.utcnow(),
        "exp": datetime.datetime.utcnow() + datetime.timedelta(seconds=max_age),
        "jti": str(uuid.uuid4()),
        **token,  # Add other token fields to the payload
    }

    # Encode the JWT
    encoded_jwt = jwt.encode(payload, secret, algorithm="RS256")
    return encoded_jwt, subject
