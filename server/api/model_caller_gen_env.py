from .model_caller import ModelCaller


class ModelCallerWithGenEnv:
    def __init__(self, model_config, gen_env):
        self.gen_env = gen_env
        self.model_config = model_config
        self.model_name = None
        self.llm = None

    def get_results_with_fallback(
        self, llm_inputs, json_output=False, num_of_variations=None, components=None
    ):
        model_caller = ModelCaller(self.model_config)
        return model_caller.get_results_with_fallback(
            llm_inputs,
            json_output,
            num_of_variations or self.gen_env._gen_settings.num_of_variations,
            components or self.gen_env._data_wrapper.text_gen_components,
        )
