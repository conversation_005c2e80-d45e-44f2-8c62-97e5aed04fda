import asyncio
import copy
import logging
import threading

from django.db import transaction

from ..models import AssetInfo, AssetInfoGroup, TargetInfo, TargetInfoGroup
from .index_builder import IndexBuilder
from .object_builder import ObjectBuilder
from .target_info_group_wrapper import TargetInfoGroupWrapper


async def background_index_deletion(indexes_to_delete):
    for index in indexes_to_delete:
        try:
            IndexBuilder().delete_index(
                index_name=index["index_name"],
                namespace=index["namespace"],
                column_id=index["column_id"],
                key_ids=index["key_ids"],
            )
        except Exception as e:
            logging.error(f"Error deleting index: {e}")


def run_async_task(indexes_to_delete):
    asyncio.run(background_index_deletion(indexes_to_delete))


class CompanyInfoUpdater:
    def __init__(self, playbook) -> None:
        self.playbook = playbook
        self.has_update = False

    def save_changes(self):
        if self.has_update:
            self.playbook.company_object.save()

    def sync_data(self, validated_data):
        if not isinstance(validated_data, dict):
            raise Exception("Invalid data type, expected a dictionary.")

        company_info_object = self.playbook.company_object

        update_meta = validated_data.get("meta", {})
        update_docs = {}
        for k, v in validated_data.items():
            if k != "meta":
                update_docs[v["data"][0]["id"]] = {
                    **v["data"][0],
                }
                if "meta" in v and "position" in v["meta"]:
                    update_docs[v["data"][0]["id"]]["position"] = v["meta"]["position"]
                if "meta" not in update_docs[v["data"][0]["id"]]:
                    update_docs[v["data"][0]["id"]]["meta"] = {}
                update_docs[v["data"][0]["id"]]["meta"].update({"field_name": k})

        if "brief" in company_info_object.meta:
            update_meta["brief"] = company_info_object.meta["brief"]
        if update_meta != company_info_object.meta:
            company_info_object.meta = update_meta
            self.has_update = True
        if update_docs != company_info_object.docs:
            company_info_object.docs = update_docs
            self.has_update = True


class BaseDataUpdater:
    def __init__(
        self,
        playbook,
        group_model,
        group_key_name,
        related_groups_name,
        group_name_in_info,
        info_model,
        info_key_name,
        related_infos_name,
    ) -> None:
        self.playbook = playbook

        # used for generic purposes
        self.group_model = group_model
        self.group_key_name = group_key_name
        self.related_groups_name = related_groups_name
        self.group_name_in_info = group_name_in_info

        self.info_model = info_model
        self.info_key_name = info_key_name
        self.related_infos_name = related_infos_name

        self.infos_to_create = []
        self.infos_to_update = {}  # key: field, value: list of objects
        self.info_ids_to_delete = []

        self.groups_to_create = []
        self.groups_to_update = {}  # key: field, value: list of objects
        self.group_ids_to_delete = []

        self.all_indice_to_delete = []

        self.updated_group_for_postprocess = []

    def save_changes(self):
        logging.info(
            f"Saving changes for playbook {self.playbook.id} for {self.group_model}"
        )
        logging.info(f"Groups to create: {len(self.groups_to_create)}")
        logging.info(f"Groups to update: {len(self.groups_to_update)}")
        logging.info(f"Groups to delete: {len(self.group_ids_to_delete)}")
        logging.info(f"Infos to create: {len(self.infos_to_create)}")
        logging.info(f"Infos to update: {len(self.infos_to_update)}")
        logging.info(f"Infos to delete: {len(self.info_ids_to_delete)}")
        logging.info(f"Indices to delete: {len(self.all_indice_to_delete)}")

        groups_to_postprocess = self.updated_group_for_postprocess

        # Bulk create groups
        if self.groups_to_create:
            created_groups = self.group_model.objects.bulk_create(self.groups_to_create)
            # Process all newly created groups with their IDs set
            groups_to_postprocess.extend(created_groups)

        # Bulk update groups
        for field, groups in self.groups_to_update.items():
            self.group_model.objects.bulk_update(groups, [field])

        # Delete groups
        if self.group_ids_to_delete:
            self.group_model.objects.filter(id__in=self.group_ids_to_delete).delete()

        # Bulk create infos
        if self.infos_to_create:
            self.info_model.objects.bulk_create(self.infos_to_create)
        # Bulk update infos
        for field, infos in self.infos_to_update.items():
            self.info_model.objects.bulk_update(infos, [field])
        # Delete infos
        if self.info_ids_to_delete:
            self.info_model.objects.filter(id__in=self.info_ids_to_delete).delete()

        logging.info(f"Groups to postprocess: {len(groups_to_postprocess)}")

        for group in groups_to_postprocess:
            self._postprocess_single_group(group)

        # special handling
        threading.Thread(
            target=run_async_task, args=(self.all_indice_to_delete,)
        ).start()

    def sync_data(self, validated_data):
        if not isinstance(validated_data, dict):
            raise ValueError("Invalid data type, expected a dictionary.")

        existing_groups = {
            group.__getattribute__(self.group_key_name): group
            for group in self.playbook.__getattribute__(self.related_groups_name).all()
        }

        for group_key, group_data in validated_data.items():
            if group_key == "meta":
                continue

            group = existing_groups.get(group_key)
            if group:
                if group_data is None:
                    self._delete_single_group(group)
                else:
                    self._update_single_group(group, group_data)
            else:
                self._create_single_group(group_key, group_data)

    def _postprocess_single_group(self, group):
        pass

    def _create_single_group(self, group_key, data):
        if not data:
            return
        # Create a new group instance
        new_group_data = {
            "playbook": self.playbook,
            "meta": data.get("meta", {}),
            self.group_key_name: group_key,
        }
        new_group = self.group_model(**new_group_data)
        self.groups_to_create.append(new_group)

        for k, v in data.items():
            if k == "meta":
                continue
            self._create_single_info(new_group, k, v)

    def _update_single_group(self, group, data):
        # Update group's metadata if it's changed
        new_meta = data.get("meta", {})
        if group.meta != new_meta:
            group.meta = new_meta
            self.groups_to_update.setdefault("meta", []).append(group)

        # Process each info object in the data
        existing_infos = {
            getattr(info, self.info_key_name): info
            for info in getattr(group, self.related_infos_name).all()
        }

        has_info_update = False  # only when there's new or deleted info
        for info_key, info_data in data.items():
            if info_key == "meta":
                continue

            if info_key in existing_infos:
                info = existing_infos[info_key]
                # Update info if necessary
                self._update_single_info(info, info_data)
            else:
                # Create new info
                self._create_single_info(group, info_key, info_data)
                has_info_update = True

        data_infos_keys = set(data.keys()) - {"meta"}
        # Delete infos not present in the new data
        for info_key, info in existing_infos.items():
            if info_key not in data_infos_keys:
                self._delete_single_info(info)
                has_info_update = True

        if has_info_update:
            self.updated_group_for_postprocess.append(group)

    def _delete_single_group(self, group):
        for info in getattr(group, self.related_infos_name).all():
            object_builder = ObjectBuilder.get_builder(info)
            if info.index:
                self.all_indice_to_delete.append(
                    {
                        "index_name": info.index["index_name"],
                        "namespace": info.index["namespace"],
                        "column_id": object_builder.get_column_id(),
                        "key_ids": object_builder.get_key_ids(),
                    }
                )
        # Deletion logic for a single group
        self.group_ids_to_delete.append(group.id)

    def _create_single_info(self, group, info_key, data):
        new_info_data = {
            self.group_name_in_info: group,
            "meta": data.get("meta", {}),
            "docs": {
                item["id"]: {**item, "position": idx}
                for idx, item in enumerate(data.get("data", []))
            },
            self.info_key_name: info_key.strip(),
        }
        new_info = self.info_model(**new_info_data)
        self.infos_to_create.append(new_info)

    def _update_single_info(self, info, data):
        new_meta = data.get("meta", {})
        new_docs = {
            item["id"]: {**item, "position": idx}
            for idx, item in enumerate(data.get("data", []))
        }

        if "brief" in info.meta:
            new_meta["brief"] = info.meta["brief"]
        if info.meta != new_meta:
            info.meta = new_meta
            self.infos_to_update.setdefault("meta", []).append(info)
        if info.docs != new_docs:
            info.docs = new_docs
            self.infos_to_update.setdefault("docs", []).append(info)

    def _delete_single_info(self, info):
        object_builder = ObjectBuilder.get_builder(info)
        if info.index:
            self.all_indice_to_delete.append(
                {
                    "index_name": object_builder.object.index["index_name"],
                    "namespace": object_builder.object.index["namespace"],
                    "column_id": object_builder.get_column_id(),
                    "key_ids": object_builder.get_key_ids(),
                }
            )

        self.info_ids_to_delete.append(info.id)


class TargetDataUpdater(BaseDataUpdater):
    def __init__(self, playbook) -> None:
        super().__init__(
            playbook=playbook,
            group_model=TargetInfoGroup,
            group_key_name="target_info_group_key",
            related_groups_name="target_info_groups",
            group_name_in_info="target_info_group",
            info_model=TargetInfo,
            info_key_name="target_key",
            related_infos_name="targets",
        )

    def _postprocess_single_group(self, group):
        try:
            target_info_group_wrapper = TargetInfoGroupWrapper(group)
            target_info_group_wrapper.post_update_process()
        except Exception as e:
            logging.exception(f"Error in postprocess_single_group for {group.id}: {e}")


class AssetDataUpdater(BaseDataUpdater):
    def __init__(self, playbook) -> None:
        super().__init__(
            playbook=playbook,
            group_model=AssetInfoGroup,
            group_key_name="asset_info_group_key",
            related_groups_name="asset_info_groups",
            group_name_in_info="asset_info_group",
            info_model=AssetInfo,
            info_key_name="asset_key",
            related_infos_name="assets",
        )


class PlaybookObjectsUpdater:
    def __init__(self, playbook) -> None:
        self.playbook = playbook

        self.company_info_updater = CompanyInfoUpdater(playbook)
        self.target_data_updater = TargetDataUpdater(playbook)
        self.asset_data_updater = AssetDataUpdater(playbook)

    def sync_data(self, validated_data):
        if not isinstance(validated_data, dict):
            raise Exception("Invalid data type, expected a dictionary.")

        if "company_info" in validated_data:
            self.company_info_updater.sync_data(validated_data["company_info"])
        if "target_info" in validated_data:
            self.target_data_updater.sync_data(validated_data["target_info"])
        if "assets" in validated_data:
            self.asset_data_updater.sync_data(validated_data["assets"])

        self.save_objects()

    def save_objects(self):
        with transaction.atomic():
            self.company_info_updater.save_changes()
            self.target_data_updater.save_changes()
            self.asset_data_updater.save_changes()


def playbook_sync(playbook, validated_data):
    PlaybookObjectsUpdater(playbook).sync_data(validated_data)


def dict_diff(d1, d2, path=""):
    """Return a list of differences between two dictionaries."""
    differences = []
    for k in d1.keys():
        if k in ["changed", "brief"]:
            continue
        if k not in d2:
            differences.append(
                f"Key present in the first dictionary but missing in the second: {path + str(k)}"
            )
        else:
            if type(d1[k]) is dict:
                if type(d2[k]) is dict:
                    if k in ["changed", "brief"]:
                        continue
                    differences.extend(dict_diff(d1[k], d2[k], path + str(k) + "->"))
                else:
                    differences.append(
                        f"Different types for key {path + str(k)}. First is Dict, second is {type(d2[k])}."
                    )
            else:
                if d1[k] != d2[k]:
                    differences.append(
                        f"Different values for key {path + str(k)}. First is {d1[k]}, second is {d2[k]}."
                    )

    # Check for keys in d2 that aren't in d1
    for k in d2.keys():
        if k in ["changed", "brief"]:
            continue
        if k not in d1:
            differences.append(
                f"Key present in the second dictionary but missing in the first: {path + str(k)}"
            )

    return differences


def company_info_to_representation_for_comparison(instance):
    if not instance:
        return {}
    res_dict = {
        "meta": instance.meta,
    }
    for k, v in instance.docs.items():
        data = copy.deepcopy(v)
        data.pop("position", None)
        data.pop("meta", None)
        if not v.get("meta", {}).get("field_name", None):
            logging.error(
                f"field_name is missing for company info {instance.id} for doc {v}"
            )
            continue
        res_dict[v["meta"]["field_name"]] = {
            "data": [data],
        }
        if "position" in v:
            res_dict[v["meta"]["field_name"]]["meta"] = {"position": v["position"]}
    return res_dict


def target_info_groups_to_representation_for_comparison(instance, is_lite=False):
    res_dict = {
        "meta": instance.meta,
        "target_info_group_key": instance.target_info_group_key,
    }
    res_dict["meta"]["id"] = instance.id
    if not is_lite:
        res_dict["targets"] = {}  # TODO: check with FE side
        for target in instance.targets.all():
            # Sort with items that have no position at the end
            data_list = sorted(
                target.docs.values(), key=lambda x: x.get("position", float("inf"))
            )
            data_status = (
                target.docs_build_status.get("docs", {})
                if target.docs_build_status
                else None
            )
            data_list = [
                {
                    **{k: v for k, v in item.items() if k != "position"},
                    "docs_build_status": (
                        data_status.get(item["id"], None) if data_status else None
                    ),
                }
                for item in data_list
            ]
            res_dict["targets"][target.target_key] = {
                "meta": target.meta,
                "data": data_list,
            }
            res_dict["targets"][target.target_key]["meta"]["id"] = target.id
            res_dict["targets"][target.target_key]["meta"].pop("brief", None)
    return res_dict


def asset_info_groups_to_representation_for_comparison(instance, is_lite=False):
    asset_meta = instance.meta
    if not isinstance(asset_meta, dict):
        # this is not expected. we'd handle the error to make it not fail but report it
        if isinstance(asset_meta, int):
            logging.error(
                f"Meta is int for asset info group and autofixed: {instance.id}: {instance.meta}"
            )
            asset_meta = {"position": asset_meta}
        else:
            logging.error(
                f"Invalid meta for asset info group is ignored: {instance.id}: {instance.meta}"
            )
            asset_meta = {}
    res_dict = {
        "meta": asset_meta,
        "asset_info_group_key": instance.asset_info_group_key,
    }
    res_dict["meta"]["id"] = instance.id
    if not is_lite:
        res_dict["assets"] = {}
        for asset in instance.assets.all():
            data_list = sorted(
                asset.docs.values(), key=lambda x: x.get("position", float("inf"))
            )
            data_list = [
                {k: v for k, v in item.items() if k != "position"} for item in data_list
            ]
            res_dict["assets"][asset.asset_key] = {
                # "meta": asset.meta,
                "data": data_list,
            }
            # TODO: this is for comparison. We shall always set meta even it's empty dict
            if asset.meta and (len(asset.meta) > 1 or "brief" not in asset.meta):
                res_dict["assets"][asset.asset_key]["meta"] = asset.meta
            res_dict["assets"][asset.asset_key]["meta"]["id"] = asset.id
            res_dict["assets"][asset.asset_key]["meta"].pop("brief", None)
    return res_dict


def deserialize_target_info(playbook, is_lite=False):
    # Serialize targets
    target_info_groups = playbook.target_info_groups.all()
    targets_dict = {"meta": {"position": 2}}

    # Iterate over each TargetInfoGroup instance
    for target_info_group in target_info_groups:
        # Call the custom to_representation_for_comparison method
        group_comparison_data = target_info_groups_to_representation_for_comparison(
            target_info_group,
            is_lite=is_lite,
        )

        targets_dict[group_comparison_data["target_info_group_key"]] = {
            "meta": group_comparison_data["meta"],
        }
        targets_dict[group_comparison_data["target_info_group_key"]].update(
            group_comparison_data.get("targets", {})
        )
    return targets_dict


def deserialize_asset_info(playbook, is_lite=False):
    assets_dict = {"meta": {"position": 3}}
    for asset_info_group in playbook.asset_info_groups.all():
        group_comparison_data = asset_info_groups_to_representation_for_comparison(
            asset_info_group, is_lite=is_lite
        )
        assets_dict[group_comparison_data["asset_info_group_key"]] = {
            "meta": group_comparison_data["meta"],
        }
        assets_dict[group_comparison_data["asset_info_group_key"]].update(
            group_comparison_data.get("assets", {})
        )
    return assets_dict


def playbook_data_compare(instance):
    # new_data
    try:
        if not instance.settings.get("disablePlaybookPostProcessing", False):
            # campany_info
            if instance.company_object:
                company_info = company_info_to_representation_for_comparison(
                    instance.company_object
                )
                company_diffs = dict_diff(instance.company_info, company_info)
                if company_diffs:
                    logging.error(
                        f"Company info is different for playbook {instance.id}: {company_diffs}"
                    )
                    logging.error(f"old: {instance.company_info}")
                    logging.error(f"new: {company_info}")

            # Serialize targets

            targets_dict = deserialize_target_info(instance)
            diffs = dict_diff(instance.target_info, targets_dict)
            if diffs:
                logging.error(
                    f"Target info is different for playbook {instance.id}: {len(diffs)}"
                )
                logging.error(f"first diff: {diffs[0]}")

            assets_dict = deserialize_asset_info(instance)
            asset_diffs = dict_diff(instance.assets, assets_dict)
            if asset_diffs:
                logging.error(
                    f"Asset info is different for playbook {instance.id}: {asset_diffs}"
                )
    except Exception as e:
        logging.error(
            f"debug: playbook {instance.id} failed to compare new object data structure: {e}"
        )
