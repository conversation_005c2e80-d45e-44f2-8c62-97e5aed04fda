import logging
import os
import traceback
import uuid
from urllib.parse import quote

import requests
from django.core.cache import cache


class ExternalInfoBuilder:
    def __init__(self) -> None:
        pass

    def build(
        self,
        keyword,
        num_links=5,
        include_sitelinks=False,
    ):
        logging.info(f"building external info: keyword {keyword}")
        try:
            url_results = self.search_by_scraping_bee(
                keyword, num_links, include_sitelinks
            )
        except Exception as e:
            logging.error(
                f"Error while searching by scraping bee: {e}\n{traceback.format_exc()}"
            )
            url_results = []
        return url_results

    def search_by_scraping_bee(self, keyword, num_links, include_sitelinks):
        cache_key_search_result_scraping_bee = f"search_result_scraping_bee_{keyword}"
        try:
            search_result_scraping_bee = cache.get(cache_key_search_result_scraping_bee)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get search result scraping bee cache for {cache_key_search_result_scraping_bee}: {e}"
            )
            search_result_scraping_bee = None
        if not search_result_scraping_bee:
            api_key = os.environ.get("SCRAPING_BEE_API_KEY")

            encoded_keyword = quote(keyword, safe="")
            request_url = f"https://app.scrapingbee.com/api/v1/store/google?api_key={api_key}&search={encoded_keyword}"
            scraping_bee_search = requests.get(request_url)
            logging.info(f"scraping_bee_search: {scraping_bee_search}")

            if scraping_bee_search.status_code != 200:
                logging.error(
                    f"Error while searching by scraping bee: {scraping_bee_search.text}"
                )
                return []

            search_result_scraping_bee = scraping_bee_search.json()
            if not search_result_scraping_bee:
                logging.error(f"No search results for keyword {keyword}")
                return []

            cache.set(
                cache_key_search_result_scraping_bee,
                search_result_scraping_bee,
                60 * 60 * 24,
            )

        organic_results = search_result_scraping_bee.get("organic_results", [])
        if not organic_results:
            logging.error(f"No search results for keyword {keyword}")
            return []

        url_results = []
        for result in organic_results[:num_links]:
            if "url" not in result:
                logging.error(f"Invalid format for url_results: {result}")
                continue
            url_results.append(
                {
                    "url": result["url"],
                    "title": result.get("title", ""),
                    "description": result.get("description", ""),
                    "type": "organic",
                }
            )

            if include_sitelinks and "sitelinks" in result and result["sitelinks"]:
                # sitelinks definition with expanded and inline:
                # https://www.searchenginejournal.com/sitelinks-what-are-they-how-do-you-get-them/474894/
                # expanded and inline render differently as child-result of one organic result
                for sitelink in result["sitelinks"].get("expanded", []):
                    if "link" in sitelink:
                        if not sitelink["link"].startswith("/search?"):
                            logging.error(
                                f"debug: expanded sitelink but not redirect: {sitelink}"
                            )
                    else:
                        logging.error(f"Invalid format for url_results: {sitelink}")
                for sitelink in result["sitelinks"].get("inline", []):
                    if "link" in sitelink:
                        url_results.append(
                            {"url": sitelink["link"], "type": "sitelink_inline"}
                        )
                    else:
                        logging.error(f"Invalid format for url_results: {sitelink}")
        return url_results
