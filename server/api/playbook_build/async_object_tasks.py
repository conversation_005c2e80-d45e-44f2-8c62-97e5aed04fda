import logging
import time

from celery import Celery
from server.celery import app as celery_app

from ..models import AssetInfo, CompanyInfo, Playbook, TargetInfo
from ..thread_locals import (
    reset_current_playbook,
    set_current_playbook,
    set_current_user,
)
from .object_builder import ObjectBuilder


@celery_app.task
def build_document_task(object_id, object_type, rebuild, check_and_rebuild):
    try:
        object = None
        if object_type == "target":
            object = TargetInfo.objects.get(id=object_id)
        elif object_type == "asset":
            object = AssetInfo.objects.get(id=object_id)
        elif object_type == "company":
            object = CompanyInfo.objects.get(id=object_id)
        else:
            raise Exception(f"Unknown object type {object_type}")

        builder = ObjectBuilder.get_builder(object)

        playbook = builder.get_playbook()
        set_current_playbook(playbook)
        user = playbook.get_owner_or_first_user()
        set_current_user(user)

        builder.build_docs(rebuild=rebuild, check_and_rebuild=check_and_rebuild)
        return f"Task completed for object: {object}"
    except Exception as e:
        logging.error(f"Task error for object {object}: {e}")
        raise e
    finally:
        reset_current_playbook()


def parallel_build_docs(
    objects_to_work, rebuild, check_and_rebuild, parallel_threads=8
):
    max_concurrent_tasks = parallel_threads
    task_results = []

    for object in objects_to_work:
        if not rebuild and not ObjectBuilder.get_builder(object).check_need_update():
            logging.info(f"Skipping object {object} as check_need_update is False")
            continue

        logging.info(f"Enqueuing task for object: {object}")

        # Wait until there is capacity to enqueue a new task
        while (
            len([result for result in task_results if not result.ready()])
            >= max_concurrent_tasks
        ):
            time.sleep(1)  # Wait for some tasks to complete

        # Determine object type
        object_type = (
            "target"
            if isinstance(object, TargetInfo)
            else "asset" if isinstance(object, AssetInfo) else "company"
        )

        # Enqueue task
        task_id = f"build_docs_{object_type}_{object.id}"
        result = build_document_task.apply_async(
            args=[object.id, object_type, rebuild, check_and_rebuild],
            priority=8,
            task_id=task_id,
        )
        task_results.append(result)

    # Periodically check if all tasks are complete
    while not all(result.ready() for result in task_results):
        time.sleep(1)  # Wait and check again
        count_done = len([res for res in task_results if res.ready()])
        logging.info(
            f"Waiting for tasks to finish, {count_done} done out of {len(task_results)}"
        )

    logging.info(f"All {len(task_results)} tasks finished")
