import logging

from django.core.cache import cache

from ..feature.data_wrapper.data_wrapper import (
    DummyGenSettings,
    GenerateEnv,
    PlaybookWrapper,
)
from ..model_caller import ModelCaller
from ..prompt.prompt_assembler.brand_guidelines_prompt_assembler import (
    BrandGuidelinesPromptAssembler,
)
from ..shared_types import ContentType, ContentTypeDetails
from ..utils import get_token_count
from .doc_loader import DocLoader
from .object_builder_base import ObjectBuilderBase
from .tone_analysis_builder import ToneAnalysisBuilder

_BRAND_GUIDELINE_BUDGET = 4500


class AssetObjectBuilder(ObjectBuilderBase):

    def get_playbook(self):
        return self.object.asset_info_group.playbook

    def get_column_id(self):
        return "assets"

    def get_key_ids(self):
        return [
            self.object.asset_info_group.asset_info_group_key,
            self.object.asset_key,
        ]

    def get_build_status_key(self):
        return f"playbook_{self.get_playbook().id}_asset_status"

    def _check_healthiness(self):
        if self.is_tone_example():
            return True, []  # not implemented
        if self.is_brand_guideline():
            return self.check_brand_guideline_healthiness()
        return super()._check_healthiness()

    def check_brand_guideline_healthiness(self):
        errors = []
        additional_info = self.object.additional_info
        if not additional_info:
            errors.append("additional_info is missing")
        else:
            if "guideline" not in additional_info:
                errors.append("guideline is missing in additional_info")
            guideline_raw_text = extract_asset_raw_text(self.object)
            # only extract content specific brand guidelines if the original exceeds the budget.
            if get_token_count(guideline_raw_text) > _BRAND_GUIDELINE_BUDGET:
                # check if the content specific brand guidelines are built
                for content_type in ContentType:
                    if content_type not in additional_info["guideline"]:
                        errors.append(f"{content_type} is missing in guideline")
            else:
                # when the original is short enough, there shouldn't be any contents under guideline.
                if additional_info["guideline"] != {}:
                    errors.append("guideline should be empty")
        return len(errors) == 0, errors

    def is_tone_example(self):
        return self.object.meta.get("type", "") == "tone_example"

    def is_brand_guideline(self):
        return self.object.meta.get("type", "") == "brand_guideline"

    def rebuild(self):
        # Clear anchor_content_precheck from additional_info
        additional_info = self.object.additional_info
        if additional_info and "anchor_content_precheck" in additional_info:
            del additional_info["anchor_content_precheck"]
            self.object.additional_info = additional_info
        # tone case
        if self.is_tone_example():
            self.object.summary = None
            self.object.index = None
            self.build_tone_analysis()
        elif self.is_brand_guideline():
            self.build_content_type_brand_guidelines()
        else:
            super().rebuild()

    def update(self):
        if self.check_need_update_no_summary():
            # Clear anchor_content_precheck from additional_info
            additional_info = self.object.additional_info
            if additional_info and "anchor_content_precheck" in additional_info:
                del additional_info["anchor_content_precheck"]
                self.object.additional_info = additional_info
        # tone case
        if self.is_tone_example() and self.check_need_update_no_summary():
            self.build_tone_analysis()
        elif self.is_brand_guideline() and self.check_need_update_no_summary():
            self.build_content_type_brand_guidelines()
        else:
            super().update()

    def check_need_update_no_summary(self):
        if self.object.docs != self.object.docs_last_build:
            return True
        return False

    def build_tone_analysis(self):
        self._update_tone_status("IN_PROGRESS")
        additional_info = self.object.additional_info
        if not additional_info:
            additional_info = {}
        try:
            playbook = self.get_playbook()
            self.docs_build_processed, _, docs_build_status = (
                self.doc_loader.extract_docs(self.object.docs)
            )
            self.docs_build_processed = DocLoader.optimize_docs(
                self.docs_build_processed
            )
            additional_info["tone_analysis"] = ToneAnalysisBuilder(
                playbook, self.docs_build_processed
            ).create()
            self.object.additional_info = additional_info
            # TODO: make status update valid
            self._update_tone_status("COMPLETED")
            logging.info(f"build tone analysis for {self.object}")
        except Exception as e:
            logging.error(f"failed to build tone analysis for {self.object}: {e}")
            self._update_tone_status("FAILED")

    def _update_tone_status(self, status):
        playbook = self.get_playbook()
        status_key = f"playbook_{playbook.id}_tone_status"
        cur_data = cache.get(status_key)
        if not cur_data:
            cur_data = {}
        cur_data[self.object.asset_key] = status
        cache.set(status_key, cur_data, 60 * 60 * 24 * 7)

    def build_content_type_brand_guidelines(self):
        # for every content type, build the content specific brand guidelines and save them to brand guidelines asset additional info.

        additional_info = self.object.additional_info
        if not additional_info:
            additional_info = {}
        try:
            additional_info["guideline"] = {}
            guideline_raw_text = extract_asset_raw_text(self.object)
            # only extract content specific brand guidelines if the original exceeds the budget.
            if get_token_count(guideline_raw_text) > _BRAND_GUIDELINE_BUDGET:
                for content_type in ContentType:
                    data_wrapper = PlaybookWrapper(self.get_playbook())
                    gen_env = GenerateEnv(data_wrapper, DummyGenSettings())
                    additional_info["guideline"][content_type] = (
                        self.summarize_for_content_type(
                            self.object, gen_env, content_type
                        )
                    )
            self.object.additional_info = additional_info

        except Exception as e:
            logging.error(
                f"failed to build content type brand guidelines for {self.object}: {e}"
            )

    def summarize_for_content_type(
        self, brand_guidelines_asset_obj, gen_env, content_type
    ):
        # For the given brand guidelines, pull out the necessary information relevant for the given content_type.
        # And return the newly created content_type specific brand guidelines asset object.

        if not brand_guidelines_asset_obj or not content_type:
            return None

        # Fetch the raw text from the orig asset
        raw_text = extract_asset_raw_text(brand_guidelines_asset_obj)
        company_name = gen_env._data_wrapper.company_name
        content_type_name = ContentTypeDetails.get(content_type, {}).get(
            "name", "marketing message"
        )

        all_features = {
            "company_name": company_name,
            "brand_guidelines_raw_text": raw_text,
            "content_type_name": content_type_name,
        }

        messages = BrandGuidelinesPromptAssembler(gen_env=gen_env).build(all_features)
        # logging.info("Brand guideline extract messages: %s", messages)

        model_caller = ModelCaller(self.model_config)
        response = model_caller.get_results_with_fallback(messages)
        if not isinstance(response, list):
            raise Exception("Response is not a list")
        if not response:
            raise Exception("Response is empty")
        text_response = response[0].text
        if not text_response:
            raise Exception("Generated text is empty")

        return text_response


def extract_asset_raw_text(asset_obj):
    docs = AssetObjectBuilder(asset_obj).extract_docs()
    docs = [doc.page_content for doc in docs]
    raw_text = "\n------\n".join(docs)
    return raw_text
