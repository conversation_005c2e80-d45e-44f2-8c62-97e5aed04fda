from ..models import AssetInfo, CompanyInfo, TargetInfo
from .object_builder_asset import AssetObjectBuilder
from .object_builder_company import CompanyObjectBuilder
from .object_builder_target import TargetObjectBuilder


class ObjectBuilder:

    @staticmethod
    def get_builder(object):
        if isinstance(object, TargetInfo):
            return TargetObjectBuilder(object)
        elif isinstance(object, AssetInfo):
            return AssetObjectBuilder(object)
        elif isinstance(object, CompanyInfo):
            return CompanyObjectBuilder(object)
        else:
            raise Exception(f"Unknown object type: {object.__class__}")
