import logging
import os
import traceback
from enum import Enum
from typing import Any, Dict, List, Optional

import pinecone
from django.db import connection
from django.db.models import Prefetch
from django.db.models.signals import pre_delete
from django.dispatch import receiver
from django.utils import timezone
from pinecone.core.openapi.shared.exceptions import NotFoundException
from server.logging_config import get_json_logger

from ..models import (
    AssetInfo,
    AssetInfoGroup,
    Campaign,
    CompanyInfo,
    Playbook,
    TargetInfo,
    TargetInfoGroup,
)
from ..status import StatusHandler
from ..utils import measure_latency
from .async_object_tasks import parallel_build_docs
from .object_builder import ObjectBuilder


class PlaybookStatus(Enum):
    IN_PROGRESS = "in progress"
    ERROR = "error"
    SUCCESS = "success"


class PlaybookBuilder:
    def __init__(self, playbook_instance):
        # prefetch objects
        # we have to use the id to re-query the object at the end
        # If you have a specific Playbook instance already and want to attach related objects to it
        #   without re-querying it, you would technically have to perform separate queries and manually
        #   set the related objects on the instance. Unfortunately, this manual setting doesn't integrate
        #   seamlessly with Django's ORM caching mechanism, such as accessing objects through related
        #   managers (_set or through the related_name).
        playbook_id = playbook_instance.id

        # Prefetch TargetInfoGroup objects and their related TargetInfo objects
        target_info_groups_prefetch = Prefetch(
            "target_info_groups",
            queryset=TargetInfoGroup.objects.prefetch_related(
                Prefetch("targets", queryset=TargetInfo.objects.all())
            ),
        )

        # Prefetch AssetInfoGroup objects and their related AssetInfo objects
        asset_info_groups_prefetch = Prefetch(
            "asset_info_groups",
            queryset=AssetInfoGroup.objects.prefetch_related(
                Prefetch("assets", queryset=AssetInfo.objects.all())
            ),
        )

        # Fetch the playbook object with all related objects prefetched
        self.playbook_instance = (
            Playbook.objects.filter(pk=playbook_id)
            .select_related(
                "company_object"  # This fetches the related CompanyInfo object, if any
            )
            .prefetch_related(target_info_groups_prefetch, asset_info_groups_prefetch)
            .get()
        )

    def get_all_target_objects(self):
        all_target_infos = []
        for target_info_group in self.playbook_instance.target_info_groups.all():
            for target_info in target_info_group.targets.all():
                all_target_infos.append(target_info)
        return all_target_infos

    def get_all_asset_objects(self):
        all_asset_infos = []
        for asset_info_group in self.playbook_instance.asset_info_groups.all():
            for asset_info in asset_info_group.assets.all():
                all_asset_infos.append(asset_info)
        return all_asset_infos

    def get_all_company_objects(self):
        company_info = self.playbook_instance.company_object
        return [company_info] if company_info else []

    # TODO: address column_ids? do we still need that?
    def get_all_objects(self, column_ids=[]):
        all_objects = []
        if not column_ids or "company_info" in column_ids:
            all_objects += self.get_all_company_objects()
        if not column_ids or "assets" in column_ids:
            all_objects += self.get_all_asset_objects()
        if not column_ids or "target_info" in column_ids:
            all_objects += self.get_all_target_objects()
        return all_objects

    # TODO: make this parallel
    def build_context(
        self,
        rebuild,
        check_and_rebuild,
        column_ids=[],
        objects_to_rebuild=[],
        parallel_threads=8,
    ):
        StatusHandler.set_playbook_status(
            self.playbook_instance, "info_expansion", "in progress"
        )
        logger = get_json_logger(__name__)
        try:
            logger.info(
                "Building playbook context",
                extra={
                    "playbook_id": self.playbook_instance.id,
                    "rebuild": rebuild,
                    "check_and_rebuild": check_and_rebuild,
                    "parallel_threads": parallel_threads,
                },
            )
            objects_to_work = objects_to_rebuild
            if not objects_to_work:
                objects_to_work = self.get_all_objects(column_ids)

            # company object is always built first
            company_object = self.playbook_instance.company_object
            if company_object and company_object in objects_to_work:
                objects_to_work.remove(company_object)

                ObjectBuilder.get_builder(company_object).build_docs(
                    rebuild=rebuild, check_and_rebuild=check_and_rebuild
                )

            run_parallel = parallel_threads > 1
            if run_parallel:
                try:
                    parallel_build_docs(
                        objects_to_work,
                        rebuild=rebuild,
                        check_and_rebuild=check_and_rebuild,
                        parallel_threads=parallel_threads,
                    )
                except Exception as e:
                    logger.error(
                        "Parallel building playbook failed, falling back to sequential",
                        extra={
                            "playbook_id": self.playbook_instance.id,
                            "error": str(e),
                            "traceback": traceback.format_exc(),
                        },
                    )
                    run_parallel = False

            if not run_parallel:
                for object in objects_to_work:
                    logger.info(
                        "Building object",
                        extra={
                            "playbook_id": self.playbook_instance.id,
                            "object_id": object.id,
                            "object_type": object.__class__.__name__,
                        },
                    )
                    ObjectBuilder.get_builder(object).build_docs(
                        rebuild=rebuild, check_and_rebuild=check_and_rebuild
                    )
            # Close existing connection to ensure we get a fresh one, since the previous connection might be stale
            try:
                connection.close()
            except Exception as e:
                logger.exception(
                    "Failed to close connection",
                    extra={"playbook_id": self.playbook_instance.id, "error": str(e)},
                )
        except Exception as e:
            logger.error(
                "Error updating playbook from builder context",
                extra={
                    "playbook_id": self.playbook_instance.id,
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                },
            )
            StatusHandler.set_playbook_status(
                self.playbook_instance, "info_expansion", "error"
            )
        else:
            StatusHandler.set_playbook_status(
                self.playbook_instance, "info_expansion", "success"
            )

    def update_context(
        self, column_ids: List[str] = [], objects_to_rebuild=[], parallel_threads=8
    ):
        """
        Generate summaries and update index
        column_ids: company_info, target_info, or both
        """
        self.build_context(
            rebuild=False,
            check_and_rebuild=False,
            column_ids=column_ids,
            objects_to_rebuild=objects_to_rebuild,
        )

    @measure_latency
    def check_and_rebuild_context(self, change_threshold=10):
        return
        self.build_context(rebuild=False, check_and_rebuild=True)

    @measure_latency
    def force_context_full_refresh(self, column_ids: List[str], objects_to_rebuild=[]):
        """
        Force to re-generate summaries and re-build index for all the data
        """
        self.build_context(
            rebuild=True,
            check_and_rebuild=False,
            column_ids=column_ids,
            objects_to_rebuild=objects_to_rebuild,
        )

    def check_healthiness_status(self, rebuild=False):
        errors = []

        status_queryset = StatusHandler.get_playbook_status_queryset(
            self.playbook_instance
        )
        if len(status_queryset) > 1:
            self.build_context(rebuild=True, check_and_rebuild=False)
            errors.append(
                f"Playbook {self.playbook_instance.id} has multiple status records"
            )
            return PlaybookStatus.ERROR, errors
        if not status_queryset:
            # quick fix since we don't actually rely on this
            StatusHandler.set_playbook_status(
                self.playbook_instance, "info_expansion", "idle"
            )
            return PlaybookStatus.SUCCESS, errors

        # check status healthiness
        status = status_queryset[0]
        if status.status == "error":
            errors.append(
                f"Playbook {self.playbook_instance.id} has status {status.status}"
            )
            return PlaybookStatus.ERROR, errors
        elif status.status == "in progress":
            process_time = (timezone.now() - status.updated_at).total_seconds()
            if process_time > 60 * 60 * 2:  # 2 hours
                if rebuild:
                    self.build_context(rebuild=False, check_and_rebuild=True)
                    return self.check_healthiness_status(rebuild=False)
                else:
                    errors.append(
                        f"Playbook {self.playbook_instance.id} has status {status.status} for too long"
                    )
                    return PlaybookStatus.ERROR, errors
            else:
                # still building
                return PlaybookStatus.IN_PROGRESS, errors

        return PlaybookStatus.SUCCESS, errors

    # TODO: add a rebuild option
    def check_healthiness(self, rebuild=False):
        errors = []

        # check objects
        objects_to_build = []
        column_ids = ["company_info", "assets"]
        if (
            not self.playbook_instance.settings
            or not self.playbook_instance.settings.get(
                "disablePlaybookPostProcessing", False
            )
        ):
            column_ids.append("target_info")
        for object in self.get_all_objects(column_ids=column_ids):
            object_healthiness, object_errors = ObjectBuilder.get_builder(
                object
            ).check_healthiness(rebuild=rebuild)
            if not object_healthiness:
                errors += object_errors
                objects_to_build.append(object)

        status_healthiness, status_errors = self.check_healthiness_status(rebuild)
        errors += status_errors
        # TODO: make it consistent with object status
        if status_healthiness == PlaybookStatus.IN_PROGRESS:
            if len(status_errors) > 0:
                logging.error(
                    f"Playbook {self.playbook_instance.id} has status {PlaybookStatus.IN_PROGRESS} but errors: {status_errors}"
                )
            return status_healthiness, errors, []

        return (
            PlaybookStatus.SUCCESS if len(errors) == 0 else PlaybookStatus.ERROR,
            errors,
            objects_to_build,
        )

    def pre_build_playbook_context(self, target_params):
        # targets
        objects_to_rebuild = []
        for l1_key, l2_key in target_params.items():
            target = TargetInfo.objects.filter(
                target_key=l2_key,
                target_info_group__target_info_group_key=l1_key,
                target_info_group__playbook=self.playbook_instance,
            )
            if not target:
                logging.error(
                    f"Target {l1_key} {l2_key} not found for content gen pre-build"
                )
                raise Exception(
                    f"Target {l1_key} {l2_key} not found for content gen pre-build"
                )
            target = target.first()
            objects_to_rebuild.append(target)

        if objects_to_rebuild:
            self.update_context(
                objects_to_rebuild=objects_to_rebuild, parallel_threads=1
            )

    def cleanup_before_delete(self):
        try:
            pinecone_index = pinecone.Pinecone().Index(
                host=os.environ.get("PINECONE_INDEX_HOST")
            )
            namespace = f"{self.playbook_instance.id}_serverless"
            pinecone_index.delete(namespace=namespace, delete_all=True)
        except NotFoundException:
            # Ignore if the namespace is not found
            pass
        except Exception as e:
            logging.error(
                f"Failed to delete pinecone namespace: {e}\n{traceback.format_exc()}"
            )


@receiver(pre_delete, sender=Playbook)
def playbook_pre_delete(sender, instance, **kwargs):
    PlaybookBuilder(instance).cleanup_before_delete()
