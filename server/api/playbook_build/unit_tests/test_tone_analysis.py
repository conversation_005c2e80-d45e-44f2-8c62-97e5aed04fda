from unittest.mock import MagicMock, patch

import pytest
from api.models import CompanyInfo, Playbook
from api.playbook_build.tone_analysis_builder import ToneAnalysisBuilder
from langchain_core.messages import HumanMessage
from langchain_core.outputs import Generation, LLMResult


@pytest.fixture
def tone_analysis_builder():
    # Create a mock Company object
    mock_company = MagicMock(spec=CompanyInfo)
    mock_company._state = MagicMock()
    mock_company._state.db = None

    # Create a Playbook instance with the mock Company
    mock_playbook = Playbook(company_object=mock_company)

    return ToneAnalysisBuilder(mock_playbook, MagicMock())


@patch("api.playbook_build.tone_analysis_builder.ToneAnalysisFeatureAssembler")
@patch("api.playbook_build.tone_analysis_builder.ToneAnalysisPromptAssembler")
@patch("api.playbook_build.tone_analysis_builder.ModelCaller")
@patch("api.model_caller.get_llm_for_model_name")
def test_create(
    mock_get_llm,
    mock_model_caller,
    mock_prompt_assembler,
    mock_feature_assembler,
    tone_analysis_builder,
):
    # Set up feature assembler mock
    mock_features = MagicMock()
    mock_feature_assembler.return_value.build.return_value = mock_features

    # Set up prompt assembler mock
    mock_messages = [HumanMessage(content="Test message")]
    mock_prompt_assembler.return_value.build.return_value = mock_messages

    # Set up model caller mock
    mock_model_caller_instance = mock_model_caller.return_value
    mock_model_caller_instance.model_config.model_params_list = [MagicMock()]
    mock_model_caller_instance.model_params = (
        mock_model_caller_instance.model_config.model_params_list[0]
    )
    mock_model_caller_instance.model_name = "mock_model"
    mock_model_caller_instance.kwargs = {}

    # Mock the LLM
    mock_llm = MagicMock()
    mock_generation = Generation(
        text='{"tone_analysis": "Test tone analysis", "summary": "Test summary"}'
    )
    mock_llm.generate.return_value = LLMResult(generations=[[mock_generation]])
    mock_get_llm.return_value = mock_llm

    # Call the method
    result = tone_analysis_builder.create()

    # Updated assertions
    assert isinstance(result, dict)
    assert "tone_analysis" in result
    assert "tone_analysis_summary" in result
    assert result["tone_analysis"] == "Test tone analysis"
    assert result["tone_analysis_summary"] == "Test summary"

    mock_llm.generate.assert_called_once()


@patch("api.playbook_build.tone_analysis_builder.ModelCaller")
@patch("api.model_caller.get_llm_for_model_name")
def test_rewrite_markdown(mock_get_llm, mock_model_caller, tone_analysis_builder):
    tone_analysis = "Test tone analysis"
    mock_model_caller_instance = mock_model_caller.return_value
    mock_model_caller_instance.get_results_with_fallback.return_value = [
        Generation(text="# Rewritten tone analysis")
    ]

    # Mock the LLM
    mock_llm = MagicMock()
    mock_generation = Generation(text="# Rewritten tone analysis")
    mock_llm.generate.return_value = LLMResult(generations=[[mock_generation]])
    mock_get_llm.return_value = mock_llm

    # Call the method
    result = tone_analysis_builder.rewrite_markdown(tone_analysis)

    # Assertions
    assert result == "# Rewritten tone analysis"


# Add more tests for edge cases and error handling
@patch("api.playbook_build.tone_analysis_builder.ToneAnalysisFeatureAssembler")
@patch("api.playbook_build.tone_analysis_builder.ToneAnalysisPromptAssembler")
@patch("api.playbook_build.tone_analysis_builder.ModelCaller")
@patch("api.model_caller.get_llm_for_model_name")
def test_create_empty_response(
    mock_get_llm,
    mock_model_caller,
    mock_prompt_assembler,
    mock_feature_assembler,
    tone_analysis_builder,
):
    # Set up feature assembler mock
    mock_features = MagicMock()
    mock_feature_assembler.return_value.build.return_value = mock_features

    # Set up prompt assembler mock
    mock_messages = [HumanMessage(content="Test message")]
    mock_prompt_assembler.return_value.build.return_value = mock_messages

    # Mock the LLM to return an empty response
    mock_llm = MagicMock()
    mock_llm.generate.return_value = LLMResult(generations=[[Generation(text="")]])
    mock_get_llm.return_value = mock_llm

    # Call the method and check for the exception
    with pytest.raises(
        Exception,
        match="Generated text is empty",
    ):
        tone_analysis_builder.create()


@patch("api.playbook_build.tone_analysis_builder.ToneAnalysisFeatureAssembler")
@patch("api.playbook_build.tone_analysis_builder.ToneAnalysisPromptAssembler")
@patch("api.playbook_build.tone_analysis_builder.ModelCaller")
@patch("api.model_caller.get_llm_for_model_name")
def test_create_invalid_json(
    mock_get_llm,
    mock_model_caller,
    mock_prompt_assembler,
    mock_feature_assembler,
    tone_analysis_builder,
):
    # Set up feature assembler mock
    mock_features = MagicMock()
    mock_feature_assembler.return_value.build.return_value = mock_features

    # Set up prompt assembler mock
    mock_messages = [HumanMessage(content="Test message")]
    mock_prompt_assembler.return_value.build.return_value = mock_messages

    # Set up model caller mock
    mock_model_caller_instance = mock_model_caller.return_value
    mock_model_caller_instance.model_config.model_params_list = [MagicMock()]
    mock_model_caller_instance.model_params = (
        mock_model_caller_instance.model_config.model_params_list[0]
    )
    mock_model_caller_instance.model_name = "mock_model"
    mock_model_caller_instance.kwargs = {}

    # Mock the LLM to return an invalid JSON response
    mock_llm = MagicMock()
    mock_generation = Generation(text="Invalid JSON")
    mock_llm.generate.return_value = LLMResult(generations=[[mock_generation]])
    mock_get_llm.return_value = mock_llm

    # Call the method
    result = tone_analysis_builder.create()

    # Assertions
    assert result == {"tone_analysis": "", "tone_analysis_summary": ""}
    mock_llm.generate.assert_called_once()


@patch("api.model_caller.get_llm_for_model_name")
def test_rewrite_markdown_empty_response(mock_get_llm, tone_analysis_builder):

    # Mock the LLM to return an empty response
    mock_llm = MagicMock()
    mock_llm.generate.return_value = LLMResult(generations=[[Generation(text="")]])
    mock_get_llm.return_value = mock_llm

    # Call the method and check for the exception
    with pytest.raises(Exception, match="Generated text is empty"):
        tone_analysis_builder.rewrite_markdown("Test tone analysis")
