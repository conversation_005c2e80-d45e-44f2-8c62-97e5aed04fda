from unittest.mock import MagicMock, patch

from api.playbook_build.transcription_postprocess import postprocess_transcript_text
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.outputs import Generation


@patch("api.playbook_build.transcription_postprocess.ModelConfigResolver")
@patch("api.playbook_build.transcription_postprocess.ModelCaller")
@patch("api.playbook_build.transcription_postprocess.SentenceSplitter")
def test_postprocess_transcript_text(
    mock_sentence_splitter, mock_model_caller, mock_model_config_resolver
):
    # Mock input
    transcript = "This is a test transcript."
    chunk_size = 1000
    model_name = "gpt-4o-mini-2024-07-18"

    # Set up ModelConfigResolver mock
    mock_model_config = MagicMock()
    mock_model_config_resolver.resolve.return_value = mock_model_config

    # Set up ModelCaller mock
    mock_model_caller_instance = mock_model_caller.return_value
    mock_model_caller_instance.get_results_with_fallback.return_value = [
        Generation(text="Processed chunk 1"),
    ]

    # Set up SentenceSplitter mock
    mock_sentence_splitter_instance = mock_sentence_splitter.return_value
    mock_sentence_splitter_instance.split_text.return_value = ["This is chunk 1."]

    # Call the function
    result = postprocess_transcript_text(transcript, chunk_size, model_name)

    # Assertions
    mock_model_config_resolver.resolve.assert_called_once()
    mock_model_caller.assert_called_once_with(mock_model_config)
    mock_sentence_splitter.assert_called_once_with(
        chunk_size=chunk_size, chunk_overlap=0
    )
    mock_sentence_splitter_instance.split_text.assert_called_once_with(transcript)

    assert result == "Processed chunk 1"
