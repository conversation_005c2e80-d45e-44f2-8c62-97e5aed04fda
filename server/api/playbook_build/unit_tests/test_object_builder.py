from unittest.mock import MagicMock, patch

import pytest
from api.playbook_build.object_builder_asset import AssetO<PERSON><PERSON>uilder, ContentType
from api.shared_types import ContentTypeDetails


@pytest.fixture
def asset_object_builder():
    return AssetObjectBuilder(MagicMock())


@patch("api.playbook_build.object_builder_asset.extract_asset_raw_text")
@patch("api.playbook_build.object_builder_asset.BrandGuidelinesPromptAssembler")
@patch("api.playbook_build.object_builder_asset.ModelCaller")
def test_summarize_for_content_type(
    mock_model_caller, mock_assembler, mock_extract, asset_object_builder
):

    # Mock the necessary objects and methods
    brand_guidelines_asset_obj = MagicMock()
    gen_env = MagicMock()
    gen_env._data_wrapper.company_name = "Test Company"
    content_type = ContentType.LandingPage

    mock_extract.return_value = "Mocked raw text"

    mock_assembler_instance = mock_assembler.return_value
    mock_assembler_instance.build.return_value = "Mocked messages"

    mock_model_caller_instance = mock_model_caller.return_value
    mock_response = MagicMock()
    mock_response.text = "Mocked summary for landing page"
    mock_model_caller_instance.get_results_with_fallback.return_value = [mock_response]

    # Call the method
    result = asset_object_builder.summarize_for_content_type(
        brand_guidelines_asset_obj, gen_env, content_type
    )

    # Assertions
    assert result == "Mocked summary for landing page"
    mock_extract.assert_called_once_with(brand_guidelines_asset_obj)
    mock_assembler.assert_called_once_with(gen_env=gen_env)
    mock_assembler_instance.build.assert_called_once_with(
        {
            "company_name": "Test Company",
            "brand_guidelines_raw_text": "Mocked raw text",
            "content_type_name": ContentTypeDetails.get(
                ContentType.LandingPage, {}
            ).get("name", "marketing message"),
        }
    )
    mock_model_caller.assert_called_once_with(asset_object_builder.model_config)
    mock_model_caller_instance.get_results_with_fallback.assert_called_once_with(
        "Mocked messages"
    )


@patch("api.playbook_build.object_builder_asset.extract_asset_raw_text")
@patch("api.playbook_build.object_builder_asset.BrandGuidelinesPromptAssembler")
@patch("api.playbook_build.object_builder_asset.ModelCaller")
def test_summarize_for_content_type_empty_response(
    mock_model_caller, mock_assembler, mock_extract, asset_object_builder
):
    mock_model_caller_instance = mock_model_caller.return_value
    mock_model_caller_instance.get_results_with_fallback.return_value = []

    with pytest.raises(Exception, match="Response is empty"):
        asset_object_builder.summarize_for_content_type(
            MagicMock(), MagicMock(), ContentType.LandingPage
        )


def test_summarize_for_content_type_none_inputs(asset_object_builder):
    result = asset_object_builder.summarize_for_content_type(None, MagicMock(), None)
    assert result is None
