from unittest.mock import MagicMock, patch

import pytest
from api.models import CompanyInfo, Playbook
from api.playbook_build.value_prop_builder import ValuePropBuilder
from langchain_core.messages import HumanMessage
from langchain_core.outputs import Generation, LLMResult


@pytest.fixture
def value_prop_builder():
    # Create a mock Company object
    mock_company = MagicMock(spec=CompanyInfo)
    mock_company._state = MagicMock()
    mock_company._state.db = None

    # Create a Playbook instance with the mock Company
    mock_playbook = Playbook(company_object=mock_company)

    # Create the main mock object
    mock_object = MagicMock()
    mock_object.target_info_group.playbook = mock_playbook
    mock_object.id = "mock_id"
    mock_object.meta = {"duplicate": False}
    mock_object.docs = {"doc1": {"meta": "meta1", "type": "type1", "value": "value1"}}

    return ValuePropBuilder(mock_object)


@patch("api.playbook_build.value_prop_builder.ValuePropFeatureAssembler")
@patch("api.playbook_build.value_prop_builder.ValuePropPromptAssembler")
@patch("api.playbook_build.value_prop_builder.ModelCaller")
@patch("api.model_caller.get_llm_for_model_name")
@patch("api.playbook_build.value_prop_builder.BaseContentWrapper")
def test_create(
    mock_base_content_wrapper,
    mock_get_llm,
    mock_model_caller,
    mock_prompt_assembler,
    mock_feature_assembler,
    value_prop_builder,
):
    # Set up feature assembler mock
    mock_features = {
        "feature1": "value1",
        "feature2": "value2",
    }
    mock_feature_assembler.return_value.build.return_value = mock_features

    # Set up prompt assembler mock
    mock_messages = [HumanMessage(content="Test message")]
    mock_prompt_assembler.return_value.build.return_value = mock_messages

    # Set up model caller mock
    mock_model_caller_instance = mock_model_caller.return_value
    mock_model_caller_instance.model_config.model_params_list = [MagicMock()]
    mock_model_caller_instance.model_params = (
        mock_model_caller_instance.model_config.model_params_list[0]
    )
    mock_model_caller_instance.model_name = "mock_model"
    mock_model_caller_instance.kwargs = {}

    # Mock the LLM
    mock_llm = MagicMock()
    mock_generation = Generation(text="Test value proposition")
    mock_llm.generate.return_value = LLMResult(generations=[[mock_generation]])
    mock_get_llm.return_value = mock_llm

    # Set up get_results_with_fallback mock
    mock_model_caller_instance.get_results_with_fallback.return_value = [
        mock_generation
    ]

    # Set up BaseContentWrapper mock
    mock_base_content_wrapper.from_data_instance.return_value = MagicMock()

    # Call the method
    result = value_prop_builder.create("Test summary", "key1", "key2", "meta_type")

    # Assertions
    assert result == "Test value proposition"


@patch("api.playbook_build.value_prop_builder.ValuePropFeatureAssembler")
@patch("api.playbook_build.value_prop_builder.ValuePropPromptAssembler")
@patch("api.playbook_build.value_prop_builder.BaseContentWrapper")
def test_create_invalid_response(
    mock_base_content_wrapper,
    mock_prompt_assembler,
    mock_feature_assembler,
    value_prop_builder,
):
    # Set up feature assembler mock
    mock_features = MagicMock()
    mock_feature_assembler.return_value.build.return_value = mock_features

    # Set up prompt assembler mock
    mock_messages = [HumanMessage(content="Test message")]
    mock_prompt_assembler.return_value.build.return_value = mock_messages

    # Set up BaseContentWrapper mock
    mock_base_content_wrapper.from_data_instance.return_value = MagicMock()

    # Create a single MagicMock for get_results_with_fallback
    mock_get_results = MagicMock()
    value_prop_builder.model_caller.get_results_with_fallback = mock_get_results

    # Test case 1: Response is not a list
    mock_get_results.return_value = "Not a list"
    with pytest.raises(Exception, match="Response is not a list"):
        value_prop_builder.create("Test summary", "key1", "key2", "meta_type")

    # Reset the mock
    mock_get_results.reset_mock()

    # Test case 2: Response is an empty list
    mock_get_results.return_value = []
    with pytest.raises(Exception, match="Response is empty"):
        value_prop_builder.create("Test summary", "key1", "key2", "meta_type")

    # Reset the mock
    mock_get_results.reset_mock()

    # Test case 3: Response is a list with an empty text
    mock_get_results.return_value = [Generation(text="")]
    with pytest.raises(Exception, match="Generated text is empty"):
        value_prop_builder.create("Test summary", "key1", "key2", "meta_type")


def test_clean_value_prop(value_prop_builder):
    input_text = "```markdown\nTest value prop\n```"
    expected_output = "Test value prop"
    assert value_prop_builder.clean_value_prop(input_text) == expected_output


def test_get_object_docs(value_prop_builder):
    expected_output = "meta1 type1 value1"
    assert value_prop_builder.get_object_docs() == expected_output
