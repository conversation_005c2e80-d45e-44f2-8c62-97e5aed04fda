import logging

from .object_builder_base import ObjectBuilderBase


class CompanyObjectBuilder(ObjectBuilderBase):
    def get_playbook(self):
        return self.object.playbook

    def get_column_id(self):
        return "company_info"

    def get_key_ids(self):
        return []

    # only override default value
    def is_eligible_for_expansion(self, eligible_size: int = 1600):
        return super().is_eligible_for_expansion(eligible_size)

    def get_build_status_key(self):
        return f"playbook_{self.get_playbook().id}_company_status"

    def _check_healthiness(self):
        errors = []
        if (
            not self.object.summary
            and self.object.docs
            and any(doc.get("value") for doc in self.object.docs.values())
        ):
            errors.append("summary is missing")
        return (False, errors) if errors else super()._check_healthiness()
