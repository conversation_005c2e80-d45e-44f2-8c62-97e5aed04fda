import logging

from ..feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    DummyGenSettings,
    GenerateEnv,
)
from ..feature.feature_assembler.value_prop_feature_assembler import (
    ValuePropFeatureAssembler,
)
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..prompt.prompt_assembler.value_prop_prompt_assembler import (
    ValuePropPromptAssembler,
)
from ..task_registry import GenerationGoal
from ..utils import get_display_l2_key


class ValuePropBuilder:
    def __init__(self, object) -> None:
        self.object = object
        self.model_config = ModelConfigResolver.resolve(
            GenerationGoal.EXPANSION,
            foundation_model="us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        )
        self.model_caller = ModelCaller(self.model_config)

    def create(self, target_summary, key1, key2, meta_type):
        logging.info(f"build value_prop for target {self.object.id}: {key1}-{key2}")

        gen_env = GenerateEnv(
            data_wrapper=BaseContentWrapper.from_data_instance(
                self.object.target_info_group.playbook
            ),
            gen_settings=DummyGenSettings(),
        )
        feature_assembler = ValuePropFeatureAssembler(
            model_budget=self.model_config.model_budget,
            gen_env=gen_env,
        )
        all_features = feature_assembler.build()
        object_docs = self.get_object_docs()
        remove_l2_key_suffix = self.object.meta.get("duplicate", False)
        all_features.update(
            {
                "single_target_context": object_docs + "\n" + target_summary,
                "single_target_key1": key1,
                "single_target_key2": get_display_l2_key(key2, remove_l2_key_suffix),
            }
        )
        messages = ValuePropPromptAssembler(meta_type, gen_env=gen_env).build(
            all_features
        )
        # logging.info("Value prop messages: %s", messages)

        response = self.model_caller.get_results_with_fallback(messages)
        if not isinstance(response, list):
            raise Exception("Response is not a list")
        if not response:
            raise Exception("Response is empty")
        text_response = response[0].text
        if not text_response:
            raise Exception("Generated text is empty")
        return self.clean_value_prop(text_response)

    def clean_value_prop(self, text):
        return text.replace("```markdown", "").replace("```", "").strip()

    def get_object_docs(self):
        object_docs = []
        for doc in self.object.docs.values():
            object_docs.append(
                f"{doc.get('meta', '')} {doc.get('type', '')} {doc.get('value', '')}"
            )
        return "\n".join(object_docs)
