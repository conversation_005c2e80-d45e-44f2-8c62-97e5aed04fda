import json
import logging

from langchain_core.messages import AIMessage, HumanMessage

from ..feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    DummyGenSettings,
    GenerateEnv,
)
from ..feature.feature_assembler.tone_analysis_feature_assembler import (
    ToneAnalysisFeatureAssembler,
)
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..prompt.prompt_assembler.tone_analysis_prompt_assembler import (
    ToneAnalysisPromptAssembler,
)
from ..prompt.prompt_library.prompt_tone_analysis import TONE_MARKDOWN_REWRITE_PROMPT
from ..task_registry import GenerationGoal


class ToneAnalysisBuilder:
    def __init__(self, playbook, processed_docs) -> None:
        self.playbook = playbook
        self.processed_docs = processed_docs
        self.model_config = ModelConfigResolver.resolve(GenerationGoal.EXPANSION)
        self.model_caller = ModelCaller(self.model_config)

    def create(self):
        logging.info(f"build tone analysis...")
        gen_env = GenerateEnv(
            data_wrapper=BaseContentWrapper.from_data_instance(self.playbook),
            gen_settings=DummyGenSettings(),
        )
        object_docs = self.processed_docs
        feature_assembler = ToneAnalysisFeatureAssembler(
            object_docs,
            gen_env=gen_env,
        )
        all_features = feature_assembler.build()

        messages = ToneAnalysisPromptAssembler(gen_env=gen_env).build(all_features)
        # logging.info("Tone analysis messages: %s", messages)

        response = self.model_caller.get_results_with_fallback(
            messages, json_output=True
        )
        if not isinstance(response, list):
            raise Exception("Response is not a list")
        if not response:
            raise Exception("Response is empty")
        tone_analysis_json = response[0].text
        if not tone_analysis_json:
            raise Exception("Generated text is empty")

        # try to parse the tone analysis response as JSON
        try:
            tone_analysis_response = json.loads(tone_analysis_json)
        except json.JSONDecodeError as e:
            logging.error(
                "Failed to parse tone analysis response as JSON: %s",
                tone_analysis_json,
            )
            return {"tone_analysis": "", "tone_analysis_summary": ""}

        # check that tone_analysis has both "summary" and "tone_analysis" keys
        if "summary" not in tone_analysis_response:
            logging.error(
                "tone_analysis does not have a 'summary' key: %s", tone_analysis
            )
            tone_analysis_summary = ""

        else:
            tone_analysis_summary = tone_analysis_response["summary"]
            # if summary is a list, make it a string.
            if isinstance(tone_analysis_summary, list):
                tone_analysis_summary = " ".join(tone_analysis_summary)
        if "tone_analysis" not in tone_analysis_response:
            logging.error(
                "tone_analysis does not have a 'tone_analysis' key: %s", tone_analysis
            )
            tone_analysis = ""
        else:
            tone_analysis = tone_analysis_response["tone_analysis"]
            # Check that the tone analysis is a Markdown string, and rewrite it if it is not
            if not isinstance(tone_analysis, str):
                logging.error(
                    "Tone analysis is not in Markdown format: %s", tone_analysis
                )
                tone_analysis = self.rewrite_markdown(str(tone_analysis))
        return {
            "tone_analysis": tone_analysis,
            "tone_analysis_summary": tone_analysis_summary,
        }

    def rewrite_markdown(self, tone_analysis):

        messages = [
            AIMessage(content=tone_analysis),
            HumanMessage(content=TONE_MARKDOWN_REWRITE_PROMPT),
        ]
        response = self.model_caller.get_results_with_fallback(messages)
        if not isinstance(response, list):
            raise Exception("Response is not a list")
        if not response:
            raise Exception("Response is empty")
        text_response = response[0].text
        if not text_response:
            raise Exception("Generated text is empty")

        return text_response
