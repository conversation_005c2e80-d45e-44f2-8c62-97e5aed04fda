import copy
import hashlib
import logging
import re

from django.core.cache import cache
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..prompt.prompt_library.transcription_postprocess_prompt import (
    TRANSCRIPTION_POSTPROCESS_HUMAN_PROMPT,
    TRANSCRIPTION_POSTPROCESS_PREPROCESS_PROMPT,
    TRANSCRIPTION_POSTPROCESS_SYS_PROMPT,
)
from ..task_registry import GenerationGoal
from ..tokenizer import SentenceSplitter


def postprocess_transcription(docs):
    new_docs = copy.deepcopy(docs)
    # Postprocess transcription to remove filler words, add speaker tags, etc.
    for doc in new_docs:
        transcript = doc.page_content
        # cache postprocess
        transcript_hashkey = hashlib.md5(transcript.encode()).hexdigest()
        cache_key = f"postprocess_transcription_{transcript_hashkey}"
        cached_transcript = cache.get(cache_key)
        if cached_transcript:
            doc.page_content = cached_transcript
            continue
        try:
            postprocessed_transcript = postprocess_transcript_text(transcript)
        except Exception as e:
            logging.error(f"Failed to postprocess transcript: {e}")
            continue
        cache.set(cache_key, postprocessed_transcript, timeout=60 * 60 * 24 * 365)
        doc.page_content = postprocessed_transcript

    return new_docs


def postprocess_transcript_text(
    transcript, chunk_size=14400, model_name="gpt-4.1-mini-2025-04-14"
):
    model_config = ModelConfigResolver.resolve(
        GenerationGoal.TRANSCRIPTION_POSTPROCESSING, foundation_model=model_name
    )
    model_caller = ModelCaller(model_config)
    # default chunk size is 14400 tokens, 90% of the input window of gpt-4o-2024-11-20
    text_splitter = SentenceSplitter(chunk_size=chunk_size, chunk_overlap=0)
    transcript_chunks = text_splitter.split_text(transcript)
    # First preprocess the transcript to extract the speaker names.
    # we only need the first chunk of the transcript, assuming it contains the speaker names.
    try:
        additional_info = preprocess_transcript(model_caller, transcript_chunks[0])
    except Exception as e:
        logging.error(f"Failed to preprocess transcript: {e}")
        additional_info = ""
    response_chunks = []

    for chunk in transcript_chunks:
        all_features = {
            "transcription_text": chunk,
            "additional_info": additional_info,
        }
        messages = [
            SystemMessage(content=TRANSCRIPTION_POSTPROCESS_SYS_PROMPT),
            HumanMessage(
                content=TRANSCRIPTION_POSTPROCESS_HUMAN_PROMPT.format(**all_features),
            ),
        ]
        logging.info("Starting transcription postprocessing")
        results = model_caller.get_results_with_fallback(messages)
        if not isinstance(results, list):
            raise ValueError("Results are not a list")
        if not results:
            raise ValueError("Results are empty")
        response = results[0].text
        if not response:
            raise ValueError("Response is empty")
        # Clean up duplicate newlines
        response = re.sub(r"\n+", "\n", response)
        response_chunks.append(response)

    return "\n".join(response_chunks)


def preprocess_transcript(model_caller, transcription_text):
    messages = [
        SystemMessage(
            content=TRANSCRIPTION_POSTPROCESS_PREPROCESS_PROMPT.format(
                transcription_text=transcription_text
            )
        )
    ]
    results = model_caller.get_results_with_fallback(messages)
    if not isinstance(results, list):
        raise ValueError("Results are not a list")
    if not results:
        raise ValueError("Results are empty")
    response = results[0].text
    if not response:
        raise ValueError("Response is empty")
    return response
