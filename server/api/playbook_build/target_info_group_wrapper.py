import logging
import traceback
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List

from celery import shared_task
from django.core.cache import cache
from django.db import transaction
from server.celery import app

from ..integrations.crustdata_converter import CrustdataAdapterForConversion
from ..models import TargetInfo, TargetInfoGroup
from ..s3_utils import write_csv_and_upload
from ..utils import run_with_retry
from .async_object_tasks import parallel_build_docs
from .object_builder import ObjectBuilder
from .target_info_group_enricher import TargetInfoGroupEnricher


@shared_task
def async_tofu_research(query_id, group_id, query, target_ids, preview):
    try:
        logging.info(f"async_tofu_research for target_info_group {group_id}")
        group = TargetInfoGroup.objects.get(id=group_id)
        wrapper = TargetInfoGroupWrapper(group)
        # Change this line to use the correct relationship or query method
        targets = TargetInfo.objects.filter(target_info_group=group, id__in=target_ids)
        sync_result = wrapper._sync_tofu_research(
            query_id=query_id, query=query, targets=targets, preview=preview
        )
        logging.info(f"async_tofu_research done with result: {sync_result}")
        return sync_result
    except Exception as e:
        logging.error(
            f"Error in async_tofu_research for target_info_group {group_id}: {e}\n{traceback.format_exc()}"
        )
        raise e


class TargetInfoGroupWrapper:
    LIMIT_TARGET_SIZE_ENRICH = 600

    def __init__(self, target_info_group) -> None:
        self.target_info_group = target_info_group

    def bulk_create(self, target_infos_data, build=True):
        with transaction.atomic():
            instances_to_create = []

            for object_data in target_infos_data:
                key = object_data.get("target_key")
                key = key.strip()
                if not key:
                    logging.error(
                        f"Error creating instance {object_data}: target_key is required"
                    )
                    continue

                try:
                    other_fields = object_data.copy()
                    other_fields.pop("target_key")
                    instance = TargetInfo(
                        target_key=key,
                        target_info_group=self.target_info_group,
                        **other_fields,
                    )
                    instances_to_create.append(instance)
                except Exception as e:
                    # Handle validation errors
                    logging.error(f"Error creating instance {object_data}: {e}")
                    continue

            logging.info(f"number of instances to create: {len(instances_to_create)}")
            # Perform bulk create
            target_objects = TargetInfo.objects.bulk_create(instances_to_create)

        if build:
            # we set rebuild=True since these are new targets and reduce the efforts to check
            parallel_build_docs(target_objects, rebuild=True, check_and_rebuild=False)
        return target_objects

    def bulk_update(self, target_infos_data, build=True):
        with transaction.atomic():
            fields_to_update = set()
            instances_to_update = []

            for object_id, object_data in target_infos_data.items():
                # Fetch the instance
                instance = TargetInfo.objects.get(id=object_id)

                try:
                    for field, value in object_data.items():
                        setattr(instance, field, value)
                        fields_to_update.add(field)
                    instances_to_update.append(instance)
                except Exception as e:
                    # Handle validation errors
                    logging.exception(f"Error updating instance {object_id}: {e}")
                    continue
            # Convert set to list for bulk_update
            fields_to_update = list(fields_to_update)

            # Perform bulk update
            TargetInfo.objects.bulk_update(instances_to_update, fields_to_update)
        if build:
            for target_info in instances_to_update:
                target_builder = ObjectBuilder.get_builder(target_info)
                target_builder.build_docs(rebuild=True)

        return instances_to_update

    def bulk_delete(self, target_info_ids):
        TargetInfo.objects.filter(id__in=target_info_ids).delete()

    def set_domain_field(self, domain_field):
        self.target_info_group.meta["domain_field"] = domain_field
        self.target_info_group.save(update_fields=["meta"])

        all_target_infos = TargetInfo.objects.filter(
            target_info_group=self.target_info_group
        )

        for target_info in all_target_infos:
            try:
                target_builder = ObjectBuilder.get_builder(target_info)
                target_builder.extract_company_domain(rebuild=True)
                if not target_info.additional_info.get("company_domain"):
                    logging.error(
                        f"Error extracting company domain for {target_info.id}"
                    )
                    continue
            except Exception as e:
                logging.error(
                    f"Error extracting additional info for {target_info.id}: {e}"
                )
                continue

        # batch update
        TargetInfo.objects.bulk_update(all_target_infos, ["additional_info"])

    def _init_tofu_research_status(self, query_id, query):
        if not self.target_info_group.status:
            self.target_info_group.status = {}
        if "tofu_research" not in self.target_info_group.status:
            self.target_info_group.status["tofu_research"] = {}
        self.target_info_group.status["tofu_research"][query_id] = {
            "query": query,
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "method": "perplexity",
            "status": "PENDING",
        }
        self.target_info_group.save(update_fields=["status"])

    def _update_tofu_research_query(self, query_id, query):
        if "tofu_research" not in self.target_info_group.meta or not isinstance(
            self.target_info_group.meta["tofu_research"], dict
        ):
            self.target_info_group.meta["tofu_research"] = {}
        self.target_info_group.meta["tofu_research"][query_id] = {
            "query": query,
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "method": "perplexity",
        }
        if "tofu_research" not in self.target_info_group.status:
            self.target_info_group.status["tofu_research"] = {}
        if query_id not in self.target_info_group.status["tofu_research"]:
            self.target_info_group.status["tofu_research"][query_id] = {}
        self.target_info_group.status["tofu_research"][query_id].update(
            {
                "status": "FINISHED",
                "updated_at": datetime.now(timezone.utc).isoformat(),
            }
        )
        self.target_info_group.save(update_fields=["meta", "status"])

    def _sync_tofu_research(self, query_id, query, targets, preview=True):
        if not targets:
            return {}

        results = {}
        if not preview:
            self._init_tofu_research_status(query_id, query)

        for target in targets:
            try:
                # Apply retry directly to the ObjectBuilder.get_builder operation
                # which is likely where the TargetInfoGroup.DoesNotExist error occurs
                @run_with_retry(
                    max_retries=3,
                    retry_exceptions=(TargetInfoGroup.DoesNotExist,),
                    operation_name=f"Tofu research for target {target.id} for query {query_id}",
                )
                def process_target(target):
                    target_builder = ObjectBuilder.get_builder(target)
                    return target_builder.tofu_research(
                        query_id=query_id, query=query, preview=preview
                    )

                search_result = process_target(target=target)
                if search_result:
                    results[target.id] = search_result

            except Exception as e:
                logging.exception(
                    f"Error tofu research for {target.id} for query {query_id}: {e}"
                )
                continue

        # update the update_stamp
        if not preview:
            try:
                self._update_tofu_research_query(query_id, query)
            except Exception as e:
                logging.exception(
                    f"Error updating tofu research query for {self.target_info_group.id} for query {query_id}: {e}"
                )

        return results

    def _async_tofu_research(self, query_id, query, targets, preview=False):
        target_ids = [target.id for target in targets]
        # submit a task for self._sync_tofu_research right now
        celery_task_id = f"tofu_research:{self.target_info_group.id}:{query_id}:{uuid.uuid4()}"  # need a lock so we don't need uuid here
        async_tofu_research.delay(
            query_id, self.target_info_group.id, query, target_ids, preview
        )
        return celery_task_id

    def tofu_research(self, query_id, query, target_ids=None, preview=False):
        if not query or not isinstance(query, str):
            raise ValueError("query is required")
        if not preview and (not query_id or not isinstance(query_id, str)):
            raise ValueError("query_id is required in non-preview mode")

        if not target_ids:
            # Use the correct related name or query method
            targets = TargetInfo.objects.filter(
                target_info_group=self.target_info_group
            )
            if preview:
                targets = targets[:2]
        else:
            targets = TargetInfo.objects.filter(
                target_info_group=self.target_info_group, id__in=target_ids
            )
        if not targets:
            raise ValueError("targets are required for tofu research")

        if preview:
            # sync mode
            return self._sync_tofu_research(query_id, query, targets, preview)
        else:
            # update the query to target_info
            self._update_tofu_research_query(query_id, query)

            # async mode
            return self._async_tofu_research(query_id, query, targets, preview)

    def delete_tofu_research(self, query_id):
        if not query_id or not isinstance(query_id, str):
            logging.error(f"Invalid query_id: {query_id}")
            return False

        # the reason we always try to delete even if the query_id is not found in target_info_group,
        # is because we want to make it safe that if we failed somewhere in the  middle, we don't want to leave the target_info_group in a bad state
        # and this shall be rare case that a random query_id is passed

        with transaction.atomic():
            fields_to_update = set()
            if (
                "tofu_research" in self.target_info_group.meta
                and query_id in self.target_info_group.meta["tofu_research"]
            ):
                del self.target_info_group.meta["tofu_research"][query_id]
                fields_to_update.add("meta")
            if (
                "tofu_research" in self.target_info_group.status
                and query_id in self.target_info_group.status["tofu_research"]
            ):
                del self.target_info_group.status["tofu_research"][query_id]
                fields_to_update.add("status")

            if fields_to_update:
                self.target_info_group.save(update_fields=list(fields_to_update))

            # update all the targets
            targets = TargetInfo.objects.filter(
                target_info_group=self.target_info_group
            )
            targets_to_update = []
            for target in targets:
                try:
                    # delete the tofu research from the target
                    target_builder = ObjectBuilder.get_builder(target)
                    if target_builder.delete_tofu_research(query_id, save=False):
                        targets_to_update.append(target)
                except Exception as e:
                    logging.error(
                        f"Error deleting tofu research for {target.id}: {e}\n{traceback.format_exc()}"
                    )
                    continue
            # batch save
            TargetInfo.objects.bulk_update(
                targets_to_update, ["additional_info", "docs_build_status"]
            )

        return True

    def enrich_data(self, enrich_field_name, task_id=None):
        targets = TargetInfo.objects.filter(target_info_group=self.target_info_group)
        result = {}
        for target_info in targets:
            try:
                target_builder = ObjectBuilder.get_builder(target_info)
                target_enrich_result = target_builder.enrich_data(enrich_field_name)
                if not target_enrich_result:
                    continue
                result[target_info.id] = target_enrich_result
                if task_id:
                    cache.set(
                        task_id, {"task_return": result}, timeout=60 * 60 * 24 * 30
                    )
            except Exception as e:
                logging.exception(f"Error enriching data for {target_info.id}: {e}")
                continue
        return result

    def post_update_process(self):
        # async auto enrich for preview
        enricher = TargetInfoGroupEnricher(self.target_info_group)
        enricher.submit_auto_enrich_data_preview()

    def convert_titles_to_contact_list(
        self, titles, company_name_field=None, company_linkedin_profile_url_field=None
    ):
        """
        Convert a list of titles to contact list objects.

        Args:
            titles (list): List of job title strings to convert to contacts

        Returns:
            dict: Dictionary containing contact list information
        """
        try:
            logging.info(
                f"Converting {len(titles)} titles to contacts for target group {self.target_info_group.id}"
            )

            # if not company list we shall report error
            if not self.target_info_group.meta.get("type", "").lower() == "company":
                raise ValueError(
                    f"Target type is not company for target group {self.target_info_group.id}"
                )

            targets = TargetInfo.objects.filter(
                target_info_group=self.target_info_group
            )
            company_data = []
            # convert targets to key-value pairs from docs
            for target in targets:
                single_company_data = {target.meta.get("field_name"): target.target_key}
                for doc in target.docs.values():
                    field_name = doc.get("meta", {}).get("field_name")
                    value = doc.get("value")
                    single_company_data[field_name] = value
                company_data.append(single_company_data)

            crustdata_adapter = CrustdataAdapterForConversion(
                titles=titles,
                company_name_field=company_name_field,
                company_linkedin_profile_url_field=company_linkedin_profile_url_field,
            )
            contacts = crustdata_adapter.convert_company_data_to_contact_data(
                company_data
            )

            csv_file_name = f"{self.target_info_group.playbook.id}-{self.target_info_group.id}-{self.target_info_group.target_info_group_key}-converted_contacts-{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            csv_url = write_csv_and_upload(
                data=contacts,  # list of dicts
                filename=csv_file_name,
                s3_bucket="tofu-uploaded-files",
            )

            return csv_url

        except Exception as e:
            logging.exception(f"Error converting titles to contact list: {e}")
            raise e
