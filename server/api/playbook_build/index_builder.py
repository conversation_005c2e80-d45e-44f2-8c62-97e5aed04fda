import logging
import os
import threading
from typing import Any, Dict, List, Optional

import pinecone
from langchain_core.documents import Document
from langchain_pinecone import Pinecone

from ..llms import get_llm_for_embeddings
from ..tokenizer import SentenceSplitter
from .doc_loader import Doc<PERSON>oader


class IndexBuilder:
    _pinecone_instance: Optional[pinecone.Pinecone] = None
    _lock = threading.Lock()

    @classmethod
    def _get_pinecone_client(cls) -> pinecone.Pinecone:
        if cls._pinecone_instance is None:
            with cls._lock:
                if cls._pinecone_instance is None:  # Double-check pattern
                    try:
                        cls._pinecone_instance = pinecone.Pinecone()
                    except Exception as e:
                        logging.error(f"Failed to create Pinecone client: {e}")
                        raise
        return cls._pinecone_instance

    def __init__(self) -> None:
        host = os.environ.get("PINECONE_INDEX_HOST")
        if not host:
            raise ValueError("PINECONE_INDEX_HOST environment variable is not set")

        client = self._get_pinecone_client()
        try:
            self.pinecone_index = client.Index(host=host)
        except Exception as e:
            logging.error(f"Failed to initialize Pinecone index: {e}")
            raise

    def delete_index(
        self,
        index_name: str,
        namespace: str,
        column_id: str,
        key_ids: List[str],
        data_id: str = "",
    ):
        try:
            # given we migrated to serverless index, it doesn't support delete by metadata filter
            # now we have to hack it by querying the index and then delete by id
            if data_id:
                filter = {
                    "$and": [
                        {"column_id": {"$eq": column_id}},
                        {"$and": [{"key_ids": {"$eq": key_id}} for key_id in key_ids]},
                        {"data_id": {"$eq": data_id}},
                    ]
                }
            else:
                filter = {
                    "$and": [
                        {"column_id": {"$eq": column_id}},
                        {"$and": [{"key_ids": {"$eq": key_id}} for key_id in key_ids]},
                    ]
                }

            vectors_to_delete = self.pinecone_index.query(
                vector=[0.1] * 3072,  # dummy vector to get all matches
                namespace=namespace,
                filter=filter,
                top_k=1000,
                include_values=False,
                include_metadata=False,
            )
            ids_to_delete = [match["id"] for match in vectors_to_delete["matches"]]
            if len(ids_to_delete) > 0:
                self.pinecone_index.delete(
                    namespace=namespace,
                    ids=ids_to_delete,
                )
        except Exception as e:
            logging.error(
                f"failed to delete index for index_name: {index_name}, namespace: {namespace}, column_id: {column_id}, key_ids: {key_ids}: {e}"
            )

    def create_index(
        self,
        docs: List[Document],
        index_name: str,
        namespace: str,
        column_id: str,
        key_ids: List[str],
        **kwargs,
    ):
        """
        Create vector store index given documents
        """
        logging.info("Create index")
        try:
            self.delete_index(
                index_name=index_name,
                namespace=namespace,
                column_id=column_id,
                key_ids=key_ids,
            )
        except Exception as e:
            logging.error(
                f"failed to delete index for index_name: {index_name}, namespace: {namespace}, column_id: {column_id}, key_ids: {key_ids}"
            )
            return ""

        logging.info("Creating new index...")
        if not docs:
            logging.info("No documents to index. Skipping...")
            return ""

        MODEL_BUDGET, llm = get_llm_for_embeddings()

        # number of tokens to include for each embedding
        index_token_length = kwargs.get("index_token_length", 250)

        text_splitter = SentenceSplitter(
            chunk_size=index_token_length,
            chunk_overlap=5,
        )
        chunks = text_splitter.split_documents(docs)
        optimized_chunks = DocLoader.optimize_docs(chunks, remove_low_signal_docs=True)
        logging.info(f"Total {len(optimized_chunks)} chuncks to add to index")

        Pinecone.from_documents(
            documents=optimized_chunks,
            embedding=llm,
            text_key="text",
            index_name=index_name,
            namespace=namespace,
        )
        logging.info(
            f"New index created: index_name: {index_name}, namespace: {namespace}, column_id: {column_id}, key_ids: {key_ids}"
        )
        return {"index_name": index_name, "namespace": namespace}

    def update_index(
        self,
        changed_docs: List[Document],
        index_name: str,
        namespace: str,
        **kwargs,
    ):
        """
        Update vector store index given chnaged documents
        """
        logging.info("Updating index...")
        MODEL_BUDGET, llm = get_llm_for_embeddings()

        # number of tokens to include for each embedding
        index_token_length = kwargs.get("index_token_length", 250)

        docs_to_add = []
        for doc in changed_docs:
            change_type = doc.metadata.get("change_type", "")
            column_id = doc.metadata.get("column_id", "")
            key_ids = doc.metadata.get("key_ids", [])
            data_id = doc.metadata.get("data_id", "")
            if change_type in (
                "data_added",
                "data_deleted",
                "data_modified",
            ):  # we include added here to handle race conditions on updates
                logging.info(
                    f"Deleting document from index from index builder: {column_id} - {key_ids} - {data_id}"
                )
                self.delete_index(
                    index_name=index_name,
                    namespace=namespace,
                    column_id=column_id,
                    key_ids=key_ids,
                    data_id=data_id,
                )
            if change_type in ("data_added", "data_modified"):
                logging.info(
                    f"Adding document to index: {column_id} - {key_ids} - {data_id}"
                )
                docs_to_add.append(doc)

        if docs_to_add:
            text_splitter = SentenceSplitter(
                chunk_size=index_token_length,
                chunk_overlap=5,
            )
            chunks = text_splitter.split_documents(docs_to_add)
            optimized_chunks = DocLoader.optimize_docs(
                chunks, remove_low_signal_docs=True
            )
            logging.info(f"Total {len(optimized_chunks)} chunks to add to index")

            Pinecone.from_documents(
                documents=optimized_chunks,
                embedding=llm,
                text_key="text",
                index_name=index_name,
                namespace=namespace,
            )
        logging.info(
            f"Index updated with index_name: {index_name}, namespace: {namespace}"
        )
        return {"index_name": index_name, "namespace": namespace}
