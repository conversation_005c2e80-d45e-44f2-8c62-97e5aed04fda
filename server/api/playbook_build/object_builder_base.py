import logging
import os
from abc import ABC, abstractmethod
from typing import List

from django.conf import settings
from django.core.cache import cache
from django.db import transaction

from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..model_config import ModelConfigResolver
from ..models import CompanyInfo
from ..task_registry import GenerationGoal
from ..thread_locals import get_current_playbook, get_current_user, set_current_playbook
from ..utils import TofuLLMCache, get_token_count
from .doc_loader import DocLoader
from .index_builder import IndexBuilder
from .summarizer import Summarizer


class ObjectBuilderBase(ABC, BaseTracableClass):
    def __init__(self, object) -> None:
        self.object = object
        self.docs_build_processed = None
        doc_metadata = {
            "playbook_id": str(self.get_playbook().id),
            "column_id": self.get_column_id(),
            "key_ids": self.get_key_ids(),
        }
        self.doc_loader = DocLoader(doc_metadata)
        self.model_config = ModelConfigResolver.resolve(GenerationGoal.EXPANSION)

    def get_metadata(self):
        # TODO: use context manager
        user = get_current_user()
        return {
            "username": user.username if user else None,
            "playbook_id": str(self.get_playbook().id),
            "column_id": self.get_column_id(),
            "key_ids": self.get_key_ids(),
            "object_id": self.object.id,
        }

    @abstractmethod
    def get_playbook(self):
        raise NotImplementedError

    @abstractmethod
    def get_column_id(self):
        raise NotImplementedError

    @abstractmethod
    def get_key_ids(self):
        raise NotImplementedError

    def get_build_status_key(self):
        raise NotImplementedError

    def update_build_status(self, errors: List[str] = [], docs_build_status={}):
        status_key = self.get_build_status_key()

        key = ":".join(self.get_key_ids())

        if False:  # for local only
            import os

            import requests

            url = "http://api.tofuhq.com/api/data/delete_cache/"
            headers = {
                "accept": "application/json",
                "Authorization": f"Bearer {os.environ.get('TOFU_ADMIN_TOKEN')}",
                "Content-Type": "application/json",
            }
            data = {"key": status_key}

            response = requests.post(url, headers=headers, json=data)

            logging.error(response.status_code)
            logging.error(response.json())

        object_status = cache.get(status_key, {})
        if errors:
            object_status[key] = ". ".join(errors)
        else:
            if key in object_status:
                del object_status[key]

        cache.set(
            status_key,
            object_status,
            60 * 60 * 24 * 30,
        )

        if docs_build_status:
            if not self.object.docs_build_status:
                self.object.docs_build_status = {}
            if "docs" not in self.object.docs_build_status:
                self.object.docs_build_status["docs"] = {}
            self.object.docs_build_status["docs"].update(docs_build_status)
            self.object.save()

    def check_need_build(self):
        meta_type = self.object.meta.get("type", "text")
        return meta_type in ["tone_example", "brand_guideline", "text"]

    # interface, please don't override this method
    @dynamic_traceable(name="object_build")
    def build_docs(self, rebuild=False, check_and_rebuild=False):
        if not self.check_need_build():
            return
        if not get_current_playbook():
            set_current_playbook(self.get_playbook())

        # Implement row-level lock on self.object to prevent concurrent builds
        obj_type = type(self.object)
        with transaction.atomic():
            # Get a fresh instance with a row lock to prevent concurrent modifications
            locked_obj = obj_type.objects.select_for_update().get(id=self.object.id)
            # Update our instance with the locked version
            self.object = locked_obj

            with TofuLLMCache():
                if rebuild:
                    (
                        self.docs_build_processed,
                        docs_extract_errors,
                        docs_build_status,
                    ) = self.doc_loader.extract_docs(self.object.docs)
                    self.docs_build_processed = DocLoader.optimize_docs(
                        self.docs_build_processed
                    )

                    self.update_build_status(docs_extract_errors, docs_build_status)

                    self.rebuild()
                elif check_and_rebuild:
                    logging.error("check_and_rebuild is not implemented")
                    return
                else:
                    logging.info(f"extract_incrementally for: {self.object}")

                    # TODO: check if we need to rebuild if the docs are the same
                    docs_delta, docs_extract_errors, docs_build_status = (
                        self.doc_loader.extract_docs_delta(
                            self.object.docs_last_build, self.object.docs
                        )
                    )

                    self.update_build_status(docs_extract_errors, docs_build_status)

                    self.docs_build_processed = docs_delta
                    self.docs_build_processed = DocLoader.optimize_docs(
                        self.docs_build_processed
                    )
                    self.update()

                # save the object at the end
                self.object.docs_last_build = self.object.docs
                self.object.save()

    def is_eligible_for_expansion(self, eligible_size: int = 1100):
        """
        Check if the contents are short enough, we just want to use them as they are
        eligible_size: number of tokens
        """
        # company object is always eligible
        if isinstance(self.object, CompanyInfo):
            return True
        total_num_tokens = get_token_count(
            "\n".join([doc.page_content for doc in self.docs_build_processed])
        )
        if total_num_tokens <= eligible_size:
            return False
        return True

    def build_brief(self):
        optimized_docs = self.docs_build_processed
        summary = "\n".join([doc.page_content for doc in optimized_docs])
        summary = summary.replace("\x00", "")
        self.object.meta["brief"] = summary

        # clean up
        self.object.summary = None
        try:
            IndexBuilder().delete_index(
                index_name=settings.PINECONE_PLAYBOOK_INDEX_NAME,
                namespace=f"{self.get_playbook().id}_serverless",
                column_id=self.get_column_id(),
                key_ids=self.get_key_ids(),
            )
        except Exception as e:
            logging.error(f"failed to delete index for {self.object}: {e}")
            return
        self.object.index = None

    def check_need_update(self):
        if self.object.docs != self.object.docs_last_build:
            return True
        if not (self.object.summary or "brief" in self.object.meta):
            return True
        return False

    # internal usage
    def rebuild(self):
        if self.is_eligible_for_expansion():
            self.object.summary = self.create_summary()
            self.object.index = self.create_index()
            self.object.meta.pop("brief", None)
        else:
            self.build_brief()

    def update(self):
        if not self.docs_build_processed and not self.check_need_update():
            logging.info(
                f"no docs_delta for: {self.object.__class__.__name__} {self.object.id}"
            )
            return

        if self.is_eligible_for_expansion():  # eligible now
            self.object.meta.pop("brief", None)  # clean up brief if there is
            docs, errors, docs_build_status = self.doc_loader.extract_docs(
                self.object.docs
            )
            self.update_build_status(errors, docs_build_status)
            self.docs_build_processed = DocLoader.optimize_docs(docs)
            self.object.summary = self.create_summary()
            self.object.index = self.create_index()
        else:
            self.build_brief()

    def create_summary(self):
        # TODO: refactor this
        data = self.get_playbook().company_info.get("Company Name", {}).get("data", [])
        company_name = ""
        if len(data) > 0:
            company_name = data[0].get("value", "")
        if company_name == "":
            company_name = "[MyCompany]"

        return Summarizer().create_summary(
            self.docs_build_processed,
            self.get_column_id(),
            company_name,
        )

    def update_summary(self):
        if not self.object.summary:
            logging.error(
                f"summary should not be None for {self.object} for update_summary"
            )
        return Summarizer().update_summary(
            self.docs_build_processed, self.object.summary
        )

    def create_index(self):
        try:
            return IndexBuilder().create_index(
                docs=self.docs_build_processed,
                index_name=settings.PINECONE_PLAYBOOK_INDEX_NAME,
                namespace=f"{self.get_playbook().id}_serverless",
                column_id=self.get_column_id(),
                key_ids=self.get_key_ids(),
            )
        except Exception as e:
            logging.error(f"failed to create index for {self.object}: {e}")
            return

    def update_index(self):
        try:
            return IndexBuilder().update_index(
                changed_docs=self.docs_build_processed,
                index_name=settings.PINECONE_PLAYBOOK_INDEX_NAME,
                namespace=f"{self.get_playbook().id}_serverless",
            )
        except Exception as e:
            logging.error(f"failed to update index for {self.object}: {e}")
            return

    def delete_index(self):
        if self.object.index:
            try:
                return IndexBuilder().delete_index(
                    index_name=self.object.index["index_name"],
                    namespace=self.object.index["namespace"],
                    column_id=self.get_column_id(),
                    key_ids=self.get_key_ids(),
                )
            except Exception as e:
                logging.error(f"failed to delete index for {self.object}: {e}")
                return

    # with_change_type is for refining summary case.
    # This function shall never be used for that purpose
    def extract_docs(self, with_change_type=False):
        extracted_docs, _errors, _docs_build_status = self.doc_loader.extract_docs(
            self.object.docs, with_change_type=False
        )
        return extracted_docs

    def _check_healthiness(self):
        # skip if not need build
        if not self.check_need_build():
            return True, []
        # either summary/index or brief should exist
        has_summary = self.object.summary is not None
        has_index = self.object.index is not None
        has_brief = self.object.meta.get("brief", None) is not None

        errors = []
        # summary and index should exist or not exist together
        if has_summary and not has_index or has_index and not has_summary:
            errors.append(
                f"summary and index should both exist or not exist for {self.object}"
            )
        # summary and brief should not both exist
        if has_summary and has_brief:
            errors.append(f"summary and brief should not both exist for {self.object}")
        # summary and brief should have one exist
        if not has_summary and not has_brief:
            errors.append(f"summary and brief should one exist for {self.object}")
        return len(errors) == 0, errors

    def check_healthiness(self, rebuild=False):
        is_health, errors = self._check_healthiness()
        if errors and rebuild:
            self.build_docs(rebuild=True)
            is_health, errors = self._check_healthiness()
        return is_health, errors

    def check_docs(self):
        docs, errors, doc_build_status = self.doc_loader.extract_docs(self.object.docs)
        return docs, errors, doc_build_status
