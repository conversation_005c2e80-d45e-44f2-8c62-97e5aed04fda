import logging

from celery import shared_task
from google.protobuf.json_format import <PERSON><PERSON><PERSON><PERSON><PERSON>, ParseDict

from ..actions.tofu_data_wrapper import TofuDataListHandler
from ..models import AssetInfo, ContentTemplate
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuTemplate,
)
from .doc_loader import DocLoader
from .object_builder_asset import ToneAnalysisBuilder


class TemplateToneAnalysisBuilder:
    def __init__(self, content_template) -> None:
        self.content_template = content_template

    def create(self):
        playbook = self.content_template.playbook

        template_data = self.content_template.template_data
        try:
            tofu_template_data = ParseDict(template_data, TofuTemplate())
        except Exception as e:
            logging.exception(f"Failed to parse template data: {e}")
            return None

        # Get assets from tone_v2 if available
        if not tofu_template_data.tone_reference_v2.tone_assets:
            logging.error(f"No tone_reference_v2 found in template data")
            return None

        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(
            tofu_template_data.tone_reference_v2.tone_assets
        )
        if asset_group_ids:
            logging.error(
                f"asset_group_ids is not allowed in tone_reference_v2 and will be ignored"
            )
            asset_group_ids = []

        if not asset_ids:
            logging.error(f"No assets found in tone_reference_v2")
            return None

        assets = AssetInfo.objects.filter(id__in=asset_ids)
        if not assets:
            logging.error(f"No assets found with the specified IDs")
            return None

        tone_docs = []
        for asset in assets:
            doc_metadata = {
                "playbook_id": str(playbook.id),
                "column_id": "assets",
                "key_ids": [
                    asset.asset_info_group.asset_info_group_key,
                    asset.asset_key,
                ],
            }
            doc_loader = DocLoader(doc_metadata)

            docs_build_processed, _, _ = doc_loader.extract_docs(asset.docs)
            docs_build_processed = DocLoader.optimize_docs(docs_build_processed)
            tone_docs.extend(docs_build_processed)

        # result format: {
        #     "tone_analysis": tone_analysis,
        #     "tone_analysis_summary": tone_analysis_summary,
        # }
        try:
            result = ToneAnalysisBuilder(playbook, tone_docs).create()
        except Exception as e:
            logging.exception(f"Failed to create tone analysis: {e}")
            return {}

        if result:
            self._save_tone_analysis(tofu_template_data, result)

        return result or {}

    def _save_tone_analysis(self, tofu_template_data, result):

        tone_analysis = result.get("tone_analysis", "")
        tone_analysis_summary = result.get("tone_analysis_summary", "")

        # Update the TofuTemplate object with tone analysis results
        if not tofu_template_data.tone_reference_v2.HasField("tone_analysis"):
            tofu_template_data.tone_reference_v2.tone_analysis.MergeFromString(
                b""
            )  # Initialize if not exists

        # Set the analysis fields in the proper protobuf structure
        tofu_template_data.tone_reference_v2.tone_analysis.tone_analysis = tone_analysis
        tofu_template_data.tone_reference_v2.tone_analysis.tone_analysis_summary = (
            tone_analysis_summary
        )

        # Convert the updated protobuf object back to dictionary
        updated_template_data = MessageToDict(
            tofu_template_data, preserving_proto_field_name=True
        )

        # Save the properly structured template data back to content_template
        self.content_template.template_data = updated_template_data
        self.content_template.save()


# define the async function for celery task
@shared_task
def async_create_tone_analysis(content_template_id, task_id):
    logging.info(f"Starting tone analysis for content template {content_template_id}")
    content_template = ContentTemplate.objects.filter(id=content_template_id).first()
    if not content_template:
        logging.error(f"Content template not found with id: {content_template_id}")
        return {"error": "Content template not found"}
    try:
        builder = TemplateToneAnalysisBuilder(content_template)
        result = builder.create()
        logging.info(
            f"Tone analysis completed for content template {content_template_id}"
        )
        return {"status": "success", "result_length": len(result)}
    except Exception as e:
        logging.exception(f"Failed to create tone analysis: {e}")
        return {"error": str(e)}
