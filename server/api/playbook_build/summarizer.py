import logging

from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..playbook_build.doc_loader import <PERSON><PERSON>oa<PERSON>
from ..prompt.prompt_assembler.summary_prompt_assembler import SummarizePromptBuilder
from ..task_registry import GenerationGoal
from ..tokenizer import <PERSON><PERSON><PERSON><PERSON>plitter
from ..utils import get_token_count, rewrite_length_limit


class Summarizer:
    def __init__(self) -> None:
        self.model_config = ModelConfigResolver.resolve(
            GenerationGoal.REFINE_SUMMARIZATION
        )
        self.model_caller = ModelCaller(self.model_config)
        self.model_budget = self.model_config.model_budget

    def create_summary(self, docs, column_id, company_name, **kwargs):
        """
        Generate new summary
        summary_length: number of tokens in the summary
        """
        if not docs:
            logging.warning("No docs to summarize")
            return ""

        logging.info("build summary")

        # load configs
        summary_length_tokens = kwargs.get("summary_length", 600)
        buffer_budget = kwargs.get("buffer_budget", 500)
        upper_rewrite_limit = kwargs.get("upper_rewrite_limit", 1.2)

        # calculate budget
        context_budget = self.model_budget - summary_length_tokens - buffer_budget
        chunk_size = context_budget - 100

        total_num_tokens = get_token_count(
            "\n".join([doc.page_content for doc in docs])
        )
        logging.info(f"Total {total_num_tokens} tokens to summarize")

        # TODO: double check this
        optimized_docs = docs

        summarize_with_map_reduce = total_num_tokens > context_budget
        if summarize_with_map_reduce:
            logging.info("Too long. Using map reduce chain to generate summary...")
            text_splitter = SentenceSplitter(
                chunk_size=chunk_size,
                chunk_overlap=20,
            )
            text_chunks = text_splitter.split_documents(docs)
            optimized_docs = DocLoader.optimize_docs(text_chunks)
        else:
            logging.info("Short enough. Using single chain to generate summary...")

        prompt_assembler = SummarizePromptBuilder(column_id)
        prompt_dict = prompt_assembler.build(features={})
        map_prompt = prompt_dict["map_prompt"]
        combine_prompt = prompt_dict["combine_prompt"]
        summary = ""
        try:
            summary = self.model_caller.get_summary_with_fallback(
                optimized_docs,
                summary_length_words=int(0.75 * summary_length_tokens),
                map_prompt=map_prompt,
                combine_prompt=combine_prompt,
                company_name=company_name,
                summarize_with_map_reduce=summarize_with_map_reduce,
                upper_rewrite_limit=upper_rewrite_limit,
            )
        except Exception as e:
            if "repetitive patterns" in str(e):
                logging.info("Repetitive patterns detected. Optimizing docs...")
                cleaned_repetitive_optimized_docs = DocLoader.optimize_docs(
                    docs, remove_non_text=True
                )
                summary = self.model_caller.get_summary_with_fallback(
                    cleaned_repetitive_optimized_docs,
                    summary_length_words=int(0.75 * summary_length_tokens),
                    map_prompt=map_prompt,
                    combine_prompt=combine_prompt,
                    company_name=company_name,
                    summarize_with_map_reduce=summarize_with_map_reduce,
                    upper_rewrite_limit=upper_rewrite_limit,
                )
            else:
                raise e

        if not summary:
            raise ValueError("No summary generated")

        if len(summary.split()) > upper_rewrite_limit * summary_length_tokens:
            logging.info("Summary too long. Truncating...")
            logging.info(f"Original summary length: {len(summary.split())}")
            summary = rewrite_length_limit(
                self.model_caller, [], summary, summary_length_tokens
            )
            logging.info(f"New summary length: {len(summary.split())}")

        return summary
