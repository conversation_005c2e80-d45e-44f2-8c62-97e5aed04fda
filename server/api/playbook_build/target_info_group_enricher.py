import logging
import time
import uuid
from datetime import datetime, timezone

from celery.result import AsyncResult
from django.core.cache import cache
from server.celery import app

from ..models import TargetInfo, TargetInfoGroup
from .object_builder import Object<PERSON>uilder


def set_enrichment_preview_status(target_info_group_id, task_id):
    cache_key = f"enrichment_preview_status:{target_info_group_id}"
    cache.set(cache_key, task_id, timeout=60 * 60 * 24 * 30)


def reset_enrichment_preview_status(target_info_group_id):
    cache_key = f"enrichment_preview_status:{target_info_group_id}"
    cache.delete(cache_key)


def _ensure_single_task_execution(
    check_key, task_id, max_wait_seconds, wait_interval=30
):
    """
    Ensures only one task of a given type is running for a target group at a time.

    Args:
        check_key: Full cache key used to identify this specific task type
        task_id: Current task ID
        timeout: Cache timeout in seconds

    Returns:
        str: The check_key used for cache operations
    """
    # Check if a task is already running for this key
    prev_task_id = cache.get(check_key)
    if prev_task_id:
        logging.info(
            f"Task with key {check_key} already running with task ID {prev_task_id}. Waiting for it to finish..."
        )

        # Check the status of the previous task
        task_result = AsyncResult(prev_task_id)

        total_waited = 0

        # Wait loop with timeout
        while not task_result.ready() and total_waited < max_wait_seconds:
            time.sleep(wait_interval)
            total_waited += wait_interval
            logging.info(
                f"Still waiting for task {prev_task_id}... ({total_waited} seconds elapsed)"
            )

            # Refresh task status
            task_result = AsyncResult(prev_task_id)

        if total_waited >= max_wait_seconds:
            logging.error(
                f"Timed out waiting for previous task {prev_task_id} after {total_waited} seconds. Proceeding with new task."
            )
            app.control.revoke(prev_task_id, terminate=True)
        else:
            logging.info(
                f"Previous task {prev_task_id} completed. Proceeding with new task."
            )
    return check_key


class TargetInfoGroupEnricher:
    LIMIT_TARGET_SIZE_ENRICH = 600
    LIMIT_TARGET_SIZE_ENRICH_PREVIEW = 10

    def __init__(self, target_info_group) -> None:
        self.target_info_group = target_info_group
        self.target_info_group.refresh_from_db()

    def is_eligible_for_enrichment(self):
        target_size = TargetInfo.objects.filter(
            target_info_group=self.target_info_group
        ).count()
        if target_size > self.LIMIT_TARGET_SIZE_ENRICH:
            return False

        # check type
        target_type = self.target_info_group.meta.get("type", "")
        if target_type not in ["Company", "Contact"]:
            return False

        return True

    ### start of full enrichment ###
    def process_data_enrichment(self, fields):
        if not self.is_eligible_for_enrichment():
            logging.error(
                f"Data enrichment is not eligible for target group {self.target_info_group.id}"
            )
            return

        targets = TargetInfo.objects.filter(target_info_group=self.target_info_group)
        if len(targets) < 10:
            return self._sync_process_data_enrichment(targets, fields)
        else:
            task_id = f"process_data_enrichment:{self.target_info_group.id}"
            return self._async_process_data_enrichment(targets, fields, task_id)

    def _sync_process_data_enrichment(self, target_infos, fields):
        processed_target_ids = []
        for target_info in target_infos:
            try:
                target_builder = ObjectBuilder.get_builder(target_info)
                target_builder.process_data_enrichment(fields)
                processed_target_ids.append(target_info.id)
            except Exception as e:
                logging.exception(
                    f"Error processing data enrichment for {target_info.id}: {e}"
                )
                continue
        return processed_target_ids

    def _async_process_data_enrichment(self, target_infos, fields, task_id):
        """
        Process data enrichment asynchronously in batches.
        Follows the pattern from campaign_gen.py's submit_batches_to_celery method.

        Args:
            target_infos: List of target info objects to process
            fields: Fields to enrich
            task_id: Parent task ID for tracking

        Returns:
            list: IDs of all processed target infos
        """
        BATCH_SIZE = 10
        MAX_CONCURRENT_JOBS = 3

        # Get the target group ID from the first target info
        target_group_id = None
        if target_infos:
            target_group_id = target_infos[0].target_info_group_id
        else:
            logging.warning("No target infos provided for enrichment")
            return []

        # Convert target_infos to IDs for serialization
        target_info_ids = [info.id for info in target_infos]

        def batch_targets(target_ids, batch_size):
            for i in range(0, len(target_ids), batch_size):
                batch_ids = target_ids[i : i + batch_size]
                yield batch_ids

        processed_batches = [0]
        processed_target_ids = []

        target_batches = list(batch_targets(target_info_ids, BATCH_SIZE))
        total_batches = len(target_batches)
        total_targets = len(target_info_ids)

        def create_job(target_batch):
            batch_task_id = f"enrich_batch_{task_id}_{uuid.uuid4()}"
            soft_time_limit = 60 * 5 * len(target_batch)
            return process_enrichment_batch.s(
                target_batch, fields, target_group_id, batch_task_id
            ).set(task_id=batch_task_id, priority=5, soft_time_limit=soft_time_limit)

        def submit_job(target_batch):
            job = create_job(target_batch)
            result = job.apply_async()
            processed_batches[0] += 1
            return result, target_batch

        # Submit initial batch of jobs
        active_jobs = []
        for _ in range(min(MAX_CONCURRENT_JOBS, len(target_batches))):
            if target_batches:  # Check to avoid pop from empty list
                active_jobs.append(submit_job(target_batches.pop(0)))

        progress_logging_cnt = 0
        while active_jobs or target_batches:
            # Make a copy of the list to avoid modification during iteration
            for i, (job, batch) in enumerate(list(active_jobs)):
                if job.ready():
                    try:
                        # Important: We don't call job.get() here because we're in a Celery task
                        # Instead, we just check if the job is ready and assume it was processed
                        logging.info(
                            f"Enrichment job {job.id} completed. Batch size: {len(batch)}"
                        )
                        processed_target_ids.extend(batch)
                    except Exception as e:
                        logging.exception(
                            f"Error processing result from job {job.id}: {e}"
                        )

                    # Modify active_jobs correctly
                    if target_batches:
                        active_jobs[i] = submit_job(target_batches.pop(0))
                    else:
                        # Use proper list removal
                        active_jobs.pop(i)
                        break  # Exit loop to avoid index issues after removal

            progress_logging_cnt += 1
            if progress_logging_cnt % 10 == 0:
                targets_processed = len(processed_target_ids)
                logging.info(
                    f"Enrichment progress: {targets_processed}/{total_targets} targets processed ({processed_batches[0]}/{total_batches} batches)"
                )
                progress_logging_cnt = 0
            time.sleep(1)  # Avoid busy waiting

        logging.info(f"All enrichment jobs completed for task {task_id}")
        return processed_target_ids

    ### end of full enrichment ###

    ### start of enrichment preview ###
    def _check_processed_enrich_preview(self):

        existing_data = (
            self.target_info_group.meta.get("auto_enrich_data_preview", {}) or {}
        )
        existing_target_ids = existing_data.get("target_ids", []) or []
        existing_targets = TargetInfo.objects.filter(
            target_info_group=self.target_info_group, id__in=existing_target_ids
        )
        updated_target_ids = [target.id for target in existing_targets]
        return updated_target_ids

    def _get_current_status(self):
        status = self.target_info_group.meta.get("auto_enrich_data_preview", {}).get(
            "status", None
        )
        try:
            # Retrieve and return the results
            target_ids = self.target_info_group.meta.get(
                "auto_enrich_data_preview", {}
            ).get("target_ids", [])
            targets = TargetInfo.objects.filter(
                target_info_group=self.target_info_group, id__in=target_ids
            )
            result = {}
            for target in targets:
                try:
                    target_builder = ObjectBuilder.get_builder(target)
                    target_result = target_builder.get_data_enrichment_preview()
                    if not target_result:
                        continue
                    result[target.id] = target_result
                except Exception as e:
                    logging.exception(
                        f"Error getting data enrichment preview for {target.id}: {e}"
                    )
                    continue
            return {
                "status": status,
                "updated_at": self.target_info_group.meta.get(
                    "auto_enrich_data_preview", {}
                ).get("updated_at", None),
                "result": result,
                "target_ids": target_ids,
            }
        except Exception as e:
            logging.exception(
                f"Error getting data enrichment preview for target group {self.target_info_group.id}: {e}"
            )
            return {}

    # get the data enrichment preview
    # resubmit if we don't find the job
    def get_data_enrichment_preview(self):
        if not self.is_eligible_for_enrichment():
            logging.error(
                f"Data enrichment preview is not eligible for target group {self.target_info_group.id}"
            )
            return {}

        # Helper function to get current status
        def get_current_status():
            return self.target_info_group.meta.get("auto_enrich_data_preview", {}).get(
                "status", None
            )

        # Check initial status
        status = get_current_status()

        # If not running or finished, try to start the job and wait for it
        if status not in ["RUNNING", "FINISHED"]:
            # Submit job
            try:
                updated_status = self.submit_auto_enrich_data_preview()
                return updated_status
            except Exception as e:
                logging.exception(
                    f"Error auto enrich data preview for target group {self.target_info_group.id}: {e}"
                )
                return {}
        else:
            return self._get_current_status()

    def submit_auto_enrich_data_preview(self):
        if not self.is_eligible_for_enrichment():
            return {}

        MIN_TARGET_SIZE = 10
        existing_target_ids = self._check_processed_enrich_preview()
        leftover_target_size = max(MIN_TARGET_SIZE - len(existing_target_ids), 0)
        if leftover_target_size <= 0:
            return self._get_current_status()

        leftover_targets = (
            TargetInfo.objects.filter(target_info_group=self.target_info_group)
            .exclude(id__in=existing_target_ids)
            .order_by("target_key")[:leftover_target_size]
        )
        all_target_ids = existing_target_ids + [
            target.id for target in leftover_targets
        ]
        if not all_target_ids:
            return {
                "status": "NOT_STARTED",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "message": "No targets to enrich",
            }

        if not self.target_info_group.meta:
            self.target_info_group.meta = {}
        self.target_info_group.meta["auto_enrich_data_preview"] = {
            "status": "RUNNING",
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "target_ids": all_target_ids,
        }
        self.target_info_group.save(update_fields=["meta"])

        async_auto_target_enrich_task_id = f"auto_target_enrich_preview:{self.target_info_group.id}_{datetime.now(timezone.utc).isoformat()}"
        leftover_target_ids = [target.id for target in leftover_targets]
        async_auto_target_enrich_preview.apply_async(
            args=[
                self.target_info_group.id,
                leftover_target_ids,
                async_auto_target_enrich_task_id,
            ],
            task_id=async_auto_target_enrich_task_id,
        )

        return {
            "status": "RUNNING",
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "target_ids": all_target_ids,
        }

    ### end of enrichment preview ###


@app.task(bind=True, acks_late=True)
def async_auto_target_enrich_preview(
    self, target_info_group_id, target_ids_to_enrich, task_id
):
    """
    Asynchronously enrich data for a target info group.

    Args:
        target_info_group_id: ID of the TargetInfoGroup to enrich
        target_ids_to_enrich: List of target IDs to enrich
    """
    check_key = f"auto_target_enrich_preview_task:{target_info_group_id}"
    try:
        check_key = _ensure_single_task_execution(
            check_key, task_id, max_wait_seconds=60 * 20
        )

        set_enrichment_preview_status(target_info_group_id, task_id)
    except Exception as e:
        logging.exception(f"Error in auto_target_enrich_preview: {e}")
        return {
            "status": "FAILED",
            "message": str(e),
        }

    try:
        # since we call this also when it's created, we need to wait for the target_info_group to be created
        max_retries = 3
        retry_interval = 10
        target_info_group = None
        for _ in range(max_retries):
            target_info_group = TargetInfoGroup.objects.filter(
                id=target_info_group_id
            ).first()
            if target_info_group:
                break
            time.sleep(retry_interval)

        if not target_info_group:
            logging.warning(f"TargetInfoGroup with id {target_info_group_id} not found")
            return_code = {
                "status": "FAILED",
                "message": f"TargetInfoGroup with id {target_info_group_id} not found",
            }
            return return_code

        targets_to_enrich = TargetInfo.objects.filter(id__in=target_ids_to_enrich)
        for target in targets_to_enrich:
            try:
                target_builder = ObjectBuilder.get_builder(target)
                target_builder.auto_enrich_data_preview()
            except TargetInfoGroup.DoesNotExist:
                logging.warning(
                    f"TargetInfoGroup with id {target_info_group_id} not found"
                )
                return_code = {
                    "status": "FAILED",
                    "message": f"TargetInfoGroup with id {target_info_group_id} not found",
                }
                return return_code
            except Exception as e:
                logging.exception(f"Error auto enriching data for {target.id}: {e}")
                continue

        logging.info(
            f"Auto enrich data preview for target group {target_info_group_id} finished"
        )

        target_info_group.refresh_from_db()
        if "auto_enrich_data_preview" not in target_info_group.meta:
            logging.error(
                f"Auto enrich data preview for target group {target_info_group_id} not found, will backfill"
            )
            if not target_info_group.meta:
                target_info_group.meta = {}
            target_info_group.meta["auto_enrich_data_preview"] = {
                "status": "FINISHED",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "target_ids": target_ids_to_enrich,
            }
        else:
            target_info_group.meta["auto_enrich_data_preview"].update(
                {
                    "status": "FINISHED",
                    "updated_at": datetime.now(timezone.utc).isoformat(),
                }
            )
        target_info_group.save(update_fields=["meta"])

        return_code = {
            "status": "FINISHED",
        }
    except Exception as e:
        logging.exception(
            f"Error in auto_enrich_data_preview for {target_info_group_id}: {e}"
        )
        return_code = {
            "status": "FAILED",
            "message": str(e),
        }
    finally:
        # Remove the task ID from the cache
        reset_enrichment_preview_status(target_info_group_id)
        logging.info(f"auto_enrich_data_preview for {target_info_group_id} is done")
        return return_code


@app.task(bind=True, acks_late=True)
def async_process_data_enrichment(self, target_info_group_id, fields, task_id):
    check_key = f"process_data_enrichment_task:{target_info_group_id}"
    try:
        check_key = _ensure_single_task_execution(
            check_key, task_id, max_wait_seconds=60 * 40
        )
        cache.set(check_key, task_id, timeout=60 * 60 * 24 * 30)
    except Exception as e:
        logging.exception(f"Error in process_data_enrichment: {e}")
        return {
            "status": "FAILED",
            "message": str(e),
        }

    try:
        target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)
        target_info_group_enricher = TargetInfoGroupEnricher(target_info_group)

        # Process data enrichment with limited concurrent batches
        processed_ids = target_info_group_enricher.process_data_enrichment(
            fields=fields,
        )

        return_code = {
            "status": "FINISHED",
            "message": f"Processed {len(processed_ids)} targets",
        }
    except TargetInfoGroup.DoesNotExist:
        logging.warning(f"TargetInfoGroup with id {target_info_group_id} not found")
        return_code = {
            "status": "FAILED",
            "message": f"TargetInfoGroup with id {target_info_group_id} not found",
        }
    except Exception as e:
        logging.exception(f"Error in data enrichment task: {e}")
        return_code = {
            "status": "FAILED",
            "message": str(e),
        }
    finally:
        # Remove the task ID from the cache
        cache.delete(check_key)
        logging.info(f"process_data_enrichment for {target_info_group_id} is done")
        return return_code


@app.task(bind=True, acks_late=True)
def process_enrichment_batch(self, target_ids, fields, target_group_id, batch_task_id):
    """
    Process a batch of data enrichment targets.

    Args:
        target_ids: List of target info IDs to process
        fields: Fields to enrich
        target_group_id: Target info group ID
        batch_task_id: This batch's task ID

    Returns:
        list: IDs of successfully processed targets
    """
    try:
        logging.info(
            f"Processing enrichment batch {batch_task_id} with {len(target_ids)} items"
        )

        # Load target info objects
        from ..models import TargetInfo

        target_infos = TargetInfo.objects.filter(id__in=target_ids)

        # Track successfully processed targets
        processed_target_ids = []

        for target_info in target_infos:
            try:
                target_builder = ObjectBuilder.get_builder(target_info)
                target_builder.process_data_enrichment(fields)

                processed_target_ids.append(target_info.id)
            except Exception as e:
                logging.exception(f"Error enriching target {target_info.id}: {e}")
                continue

        logging.info(f"Completed enrichment batch {batch_task_id}")
        return processed_target_ids

    except Exception as e:
        logging.exception(f"Error processing enrichment batch {batch_task_id}: {e}")
        return []
