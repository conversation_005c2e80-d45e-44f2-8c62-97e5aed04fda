import hashlib
import logging
import os
import time
import traceback
import uuid
from datetime import datetime, timezone

import requests
import tldextract
from django.core.cache import cache
from django.db import transaction
from pinecone import Pinecone

from ..integrations.crustdata_adapter import CrustdataAdapter
from ..integrations.crustdata_client import CrustdataConfig
from ..llms import get_llm_for_embeddings
from ..prompt.prompt_library.prompt_perplexity_research import (
    PERPLEXITY_RESEARCH_SYSTEM_PROMPT,
    PERPLEXITY_RESEARCH_USER_PROMPT,
)
from ..utils import tofu_rate_limit
from .external_info_builder import ExternalInfoBuilder
from .object_builder_base import ObjectBuilderBase
from .value_prop_builder import ValuePropBuilder


class TargetObjectBuilder(ObjectBuilderBase):
    @property
    def target_type(self):
        return self.object.target_info_group.meta.get("type", "")

    def get_playbook(self):
        return self.object.target_info_group.playbook

    def get_column_id(self):
        return "target_info"

    def get_key_ids(self):
        return [
            self.object.target_info_group.target_info_group_key,
            self.object.target_key,
        ]

    def is_eligible_for_value_prop(self):
        target_type = self.object.target_info_group.meta.get("type", "")
        return target_type in (
            "Company",
            "Industry",
            "Persona",
            "Keyword",
            "Contact",
        )

    def create_value_prop(self):
        if not self.is_eligible_for_value_prop():
            self.object.value_prop = None
            return
        # if no doc updated and value_prop already exists
        if not self.docs_build_processed and self.object.value_prop:
            return

        target_summary = self.object.summary
        if not target_summary:
            target_summary = self.object.meta.get("brief", "")
        key1, key2 = self.get_key_ids()
        self.object.value_prop = ValuePropBuilder(self.object).create(
            target_summary,
            key1,
            key2,
            self.object.target_info_group.meta.get("type", ""),
        )
        logging.info(f"build value prop for {key1}-{key2}")

    def rebuild(self):
        super().rebuild()
        self.create_value_prop()
        self.extract_additional_info(rebuild=True)

    def update(self):
        super().update()
        self.create_value_prop()
        self.extract_additional_info(rebuild=False)
        try:
            self.trigger_full_tofu_research()
        except Exception as e:
            logging.exception(
                f"Failed to trigger full tofu research for {self.object}: {e}"
            )

    def _check_healthiness(self):
        errors = []
        target_type = self.object.target_info_group.meta.get("type", "")
        has_value_prop = self.object.value_prop is not None
        is_eligible_for_value_prop = self.is_eligible_for_value_prop()
        if is_eligible_for_value_prop and not has_value_prop:
            errors.append(
                f"target_type {target_type} should have value_prop for {self.object.id}"
            )
        if not is_eligible_for_value_prop and has_value_prop:
            errors.append(
                f"target_type {target_type} should not have value_prop for {self.object.id}"
            )
        if errors:
            return False, errors
        return super()._check_healthiness()

    def check_need_update(self):
        if super().check_need_update():
            return True
        if not self.object.value_prop and self.is_eligible_for_value_prop():
            return True
        return False

    def get_build_status_key(self):
        return f"playbook_{self.get_playbook().id}_target_status"

    def extract_company_domain(self, rebuild=False):
        if not self.object.additional_info:
            self.object.additional_info = {}

        domain_field = self.object.target_info_group.meta.get("domain_field")
        if not domain_field:
            return
        if "company_domain" in self.object.additional_info and not rebuild:
            return

        # find any docs have url
        website_doc = None
        for doc_data in self.object.docs.values():
            try:
                if doc_data.get("meta", {}).get("field_name") == domain_field:
                    if doc_data.get("type") != "url":
                        logging.error(f"Invalid doc type for domain field: {doc_data}")
                        continue
                    website_doc = doc_data
                    break
            except Exception as e:
                logging.error(
                    f"Failed to extract company domain for doc data: {doc_data} {e} {traceback.format_exc()}"
                )
                continue
        if not website_doc:
            logging.error(f"Failed to find website doc for {self.object}")
            return

        # extract domain from url
        try:
            url = website_doc.get("value", "")

            extracted_url = tldextract.extract(url)
            campany_domain = f"{extracted_url.domain}.{extracted_url.suffix}"
            if not campany_domain:
                logging.error(f"Failed to extract company domain for {self.object}")
                return
            self.object.additional_info["company_domain"] = campany_domain
        except Exception as e:
            logging.error(
                f"Failed to extract company domain for {self.object}: {e} {traceback.format_exc()}"
            )

    def extract_industry_info(self, rebuild=False):
        if "industry" in self.object.additional_info and not rebuild:
            return

        pinecone_key = os.environ.get("PINECONE_API_KEY")
        pinecone_host = os.environ.get("PINECONE_INDEX_HOST")

        if not pinecone_key:
            logging.error("Pinecone API key is missing")
            return
        if not pinecone_host:
            logging.error("Pinecone host is missing")
            return

        index_name = "inbound"

        pc = Pinecone(api_key=pinecone_key)
        index = pc.Index(index_name)

        # getting industry from index query
        _, llm = get_llm_for_embeddings()
        target_key_embedding = llm.embed_documents([self.object.target_key])[0]
        results = index.query(
            namespace="industries",
            vector=target_key_embedding,
            top_k=5,
            include_metadata=True,
        )
        if not results or not isinstance(results, dict) or "matches" not in results:
            logging.error(
                f"Failed to get industry info for {self.object} with result {results}"
            )
        matches = results.get("matches", [])
        matches = [match for match in matches if match.get("score", 0) >= 0.5]
        industries = [match.get("metadata", {}).get("label", "") for match in matches]
        industries = [industry for industry in industries if industry]
        self.object.additional_info["industries"] = industries

    def extract_additional_info(self, rebuild=False):
        if not self.object.additional_info:
            self.object.additional_info = {}
        if self.target_type == "Company":
            self.extract_company_domain(rebuild=rebuild)
        elif self.target_type == "Industry" and False:  # not enable for now
            self.extract_industry_info(rebuild=rebuild)

    def build_external_info(
        self,
        keyword="company introduction and products",
        num_links=5,
        include_sitelinks=False,
    ):
        # TODO: fetch company name in a better way
        if self.object.target_info_group.meta.get("type", "") != "Company":
            return [], ""
        company_name = self.object.target_key

        if not keyword:
            keyword = f"company introduction and products"
        company_keyword = f"{company_name} {keyword}"

        try:
            search_urls = ExternalInfoBuilder().build(
                company_keyword,
                num_links,
                include_sitelinks,
            )
        except Exception as e:
            logging.error(
                f"Failed to build external info for {self.object}: {e}\n{traceback.format_exc()}"
            )
            return [], ""
        return search_urls

    def _tofu_research_full_query(self, query):
        details = []
        if self.object.meta and self.object.meta.get("field_name"):
            details.append(
                f"{self.object.meta.get('field_name')}: {self.object.target_key}"
            )
        for _, doc_data in self.object.docs.items():
            doc_field_name = doc_data.get("meta", {}).get("field_name", "")
            details.append(f"{doc_field_name}: {doc_data.get('value', '')}")
        details_str = "\n".join(details)

        full_query = PERPLEXITY_RESEARCH_USER_PROMPT.format(
            query=query,
            target_name=self.object.target_key,
            target_details=details_str,
        )
        return full_query

    @tofu_rate_limit(1000, 60)
    def _tofu_research_impl(self, full_query):  # this may throw exception
        url = "https://api.perplexity.ai/chat/completions"

        perplexity_token = os.environ.get("PERPLEXITY_AI_API_KEY")
        perplexity_model = "sonar"
        system_message = PERPLEXITY_RESEARCH_SYSTEM_PROMPT

        payload = {
            "model": perplexity_model,
            "messages": [
                {
                    "role": "system",
                    "content": system_message,
                },
                {"role": "user", "content": full_query},
            ],
            "max_tokens": 10000,
            "temperature": 0.2,
            "top_p": 0.9,
            "return_citations": True,
            # "search_domain_filter": [],
            "return_images": False,
            "return_related_questions": False,
            "search_recency_filter": "month",
            "top_k": 0,
            "stream": False,
            "presence_penalty": 0,
            "frequency_penalty": 1,
        }
        headers = {
            "Authorization": f"Bearer {perplexity_token}",
            "Content-Type": "application/json",
        }
        max_retries = 2
        base_delay = 1  # Initial delay in seconds

        for attempt in range(max_retries + 1):
            try:
                response = requests.request("POST", url, json=payload, headers=headers)
                if response.status_code != 200:
                    logging.warning(
                        f"Failed to tofu research for {self.object}: {response.text}"
                    )
                    raise Exception(
                        f"Failed to tofu research for {self.object}: {response.text}"
                    )
                else:
                    break
            except Exception as e:
                if attempt == max_retries:
                    logging.error(
                        f"Failed to tofu research for {self.object}: {e}\n{traceback.format_exc()}"
                    )
                    raise e
                time.sleep(base_delay * (2**attempt))
        response_payload = response.json()
        # the payload is like this:
        # {
        # "result": {
        #     "id": "1642b05f-7bc8-4927-ba0e-bc02675942a9",
        #     "model": "llama-3.1-sonar-small-128k-online",
        #     "created": 1728585794,
        #     "usage": {
        #     "prompt_tokens": 64,
        #     "completion_tokens": 290,
        #     "total_tokens": 354
        #     },
        #     "object": "chat.completion",
        #     "choices": [
        #     {
        #         "index": 0,
        #         "finish_reason": "stop",
        #         "message": {
        #         "role": "assistant",
        #         "content": "The latest financial report for Boeing is scheduled to be released on October 23, 2024. Here are the details:\n\n- **Release Date**: October 23, 2024.\n- **Event Details**: The financial results for the third quarter of 2024 will be discussed by President and Chief Executive Officer Kelly Ortberg and Executive Vice President and Chief Financial Officer Brian West during a conference call at 10:30 a.m. ET.\n- **Webcast and Dial-in Information**: The event webcast link will be available on the Events and Presentations section of www.boeing.com/investors. The event can also be accessed by dialing 1-877-692-8955 within the U.S. and by dialing 234-720-6979 outside of the U.S. The passcode for both is 3728271.\n\nFor historical financial data and recent news, you can refer to the following sources:\n\n- **GuruFocus**: Provides a summary of Boeing's stock price, trades, and news, including financial strength metrics and analyst estimates.\n- **Yahoo Finance**: Offers a company profile and facts about Boeing, including its ISS Governance QualityScore.\n\nFor the most up-to-date financial information, you should check the official Boeing investor relations website or the press releases provided by the company on October 23, 2024."
        #         },
        #         "delta": {
        #         "role": "assistant",
        #         "content": ""
        #         }
        #     }
        # }
        # we want to log the token usage and return the result only

        usage = response_payload.get("usage", {})
        prompt_tokens = usage.get("prompt_tokens", 0)
        completion_tokens = usage.get("completion_tokens", 0)
        total_tokens = usage.get("total_tokens", 0)
        logging.info(
            f"TOFU research for {self.object} used {prompt_tokens} prompt tokens, {completion_tokens} completion tokens, and {total_tokens} total tokens"
        )

        try:
            search_content = (
                response_payload.get("choices", [{}])[0]
                .get("message", {})
                .get("content", "")
            )
            if not search_content:
                raise Exception(
                    f"Failed to tofu research for {self.object}: {response_payload}"
                )
            citations = response_payload.get("citations", [])
            citations_str = ""
            if not citations:
                logging.warning(
                    f"Failed to get citations for {self.object}: {response_payload}"
                )
            else:
                index = 1
                for citation in citations:
                    citations_str += f"{index}. {citation}\n"
                    index += 1
        except Exception as e:
            logging.error(
                f"Failed to tofu research for {self.object}: {e}\n{traceback.format_exc()}"
            )
            raise e
        return (search_content + "\n\n" + citations_str).strip()

    def trigger_full_tofu_research(self):
        target_info_group = self.object.target_info_group
        if not target_info_group:
            # though this should not happen
            logging.error(f"Target info group not found for {self.object}")
            return

        tofu_research_queries = target_info_group.meta.get("tofu_research", {})
        if not tofu_research_queries:
            logging.info(f"No tofu research queries for {target_info_group}")
            return
        if not isinstance(tofu_research_queries, dict):
            logging.error(f"Invalid tofu research queries for {target_info_group}")
            return

        for query_id, query_data in tofu_research_queries.items():
            logging.info(f"Tofu research for {self.object}: {query_id}")
            # query_status
            try:
                query_status = self.object.docs_build_status.get(
                    "tofu_research", {}
                ).get(query_id, {})
                if query_status == "success":
                    continue
                query = query_data.get("query", "")
                if not query:
                    logging.error(f"Query is empty for {self.object}: {query_data}")
                    continue

                self.tofu_research(
                    query_id=query_id,
                    query=query_data.get("query", ""),
                    preview=False,
                    disable_cache=False,
                )
            except Exception as e:
                logging.exception(f"Failed to tofu research for {self.object}: {e}")
                continue

    # this may throw exception
    def tofu_research(self, query_id, query, preview=False, disable_cache=False):
        logging.info(f"Tofu research for {self.object}: {query}")
        try:
            full_query = self._tofu_research_full_query(query)
            full_query_key = hashlib.md5(full_query.encode()).hexdigest()
            cache_key = f"tofu_research:{self.object.id}:{full_query_key}"

            research_result = None
            if not disable_cache:
                research_result = cache.get(cache_key)
            if not research_result:
                research_result = self._tofu_research_impl(full_query)

            if not preview:
                update_fields = set()
                try:
                    if not self.object.additional_info or not isinstance(
                        self.object.additional_info, dict
                    ):
                        self.object.additional_info = {}
                    if "tofu_research" not in self.object.additional_info:
                        self.object.additional_info["tofu_research"] = {}
                    self.object.additional_info["tofu_research"][query_id] = {
                        "result": research_result,
                        "updated_at": datetime.now(timezone.utc).isoformat(),
                        "method": "perplexity",
                    }
                    update_fields.add("additional_info")
                except Exception as e:
                    logging.error(
                        f"Failed to update tofu research for {self.object}: {e}\n{traceback.format_exc()}"
                    )
                try:
                    if not self.object.docs_build_status:
                        self.object.docs_build_status = {}
                    if "tofu_research" not in self.object.docs_build_status:
                        self.object.docs_build_status["tofu_research"] = {}
                    self.object.docs_build_status["tofu_research"][query_id] = "success"
                    update_fields.add("docs_build_status")
                except Exception as e:
                    logging.error(
                        f"Failed to update tofu research for {self.object}: {e}\n{traceback.format_exc()}"
                    )

                if update_fields:
                    self.object.save(update_fields=list(update_fields))

            try:
                cache.set(
                    cache_key, research_result, timeout=60 * 60 * 24 * 7
                )  # 1 week
            except Exception as cache_error:
                logging.warning(f"Cache setting failed: {str(cache_error)}")

            return research_result
        except Exception as e:
            logging.error(
                f"Failed to tofu research for {self.object}: {e}\n{traceback.format_exc()}"
            )
            raise e

    def delete_tofu_research(self, query_id, save=True):
        if not query_id or not isinstance(query_id, str):
            logging.error(f"Invalid query_id: {query_id}")
            return False

        fields_to_update = set()
        if (
            self.object.additional_info
            and isinstance(self.object.additional_info, dict)
            and "tofu_research" in self.object.additional_info
            and query_id in self.object.additional_info["tofu_research"]
        ):
            del self.object.additional_info["tofu_research"][query_id]
            fields_to_update.add("additional_info")

        if (
            self.object.docs_build_status
            and isinstance(self.object.docs_build_status, dict)
            and "tofu_research" in self.object.docs_build_status
            and query_id in self.object.docs_build_status["tofu_research"]
        ):
            del self.object.docs_build_status["tofu_research"][query_id]
            fields_to_update.add("docs_build_status")

        if save and fields_to_update:
            self.object.save(update_fields=list(fields_to_update))
        return bool(fields_to_update)

    def enrich_data(self, enrich_field_name):
        try:
            crustdata_integration = CrustdataAdapter(self.object)
            data = crustdata_integration.enrich_data(enrich_field_name)
            if not data:
                return None
            return {enrich_field_name: data}
        except Exception as e:
            logging.exception(
                f"Failed to enrich data for {self.object}: {e}\n{traceback.format_exc()}"
            )
            return None

    def auto_enrich_data_preview(self):
        try:
            crustdata_adapter = CrustdataAdapter(self.object)
            data = crustdata_adapter.enrich_data_for_all_fields()
            if not data:
                logging.error(f"Failed to enrich data for {self.object}")
                return None
            flattened_data = crustdata_adapter.flatten_data(data, remove_null=True)
            # lock on self.object
            obj_type = type(self.object)
            with transaction.atomic():
                # Get a fresh instance with a row lock to prevent concurrent modifications
                locked_obj = obj_type.objects.select_for_update().get(id=self.object.id)
                # Update our instance with the locked version
                self.object = locked_obj

                # save it to target_info.additional_info (safely handle None case)
                if self.object.additional_info is None:
                    self.object.additional_info = {}

                self.object.additional_info = {
                    **self.object.additional_info,
                    "auto_enrich_data_preview": {
                        "status": "FINISHED",
                        "data": flattened_data,
                    },
                }
                self.object.save(update_fields=["additional_info"])
            return data
        except Exception as e:
            logging.exception(f"Failed to auto enrich data for {self.object}: {e}")
            return None

    def get_data_enrichment_preview(self):
        if not self.object.additional_info:
            return {}
        data = self.object.additional_info.get("auto_enrich_data_preview", {}).get(
            "data", {}
        )
        return data

    def process_data_enrichment(self, fields):
        try:
            crustdata_integration = CrustdataAdapter(self.object)
            data = crustdata_integration.enrich_data_for_fields(fields)
            if not data:
                return
            if not isinstance(data, dict):
                logging.error(f"Invalid data for {self.object}: {data}")
                return

            # save to docs
            max_position = 0
            for doc in self.object.docs.values():
                max_position = max(max_position, doc.get("meta", {}).get("position", 0))

            existing_field_to_id_map = {}
            for doc_id, doc in self.object.docs.items():
                field_name = doc.get("meta", {}).get("field_name", "")
                if field_name:
                    existing_field_to_id_map[field_name] = doc_id

            for field, value in data.items():
                field_name = CrustdataConfig().get_field_label(field)
                if not field_name:
                    logging.error(f"Invalid crustdata field: {field}")
                    continue
                if field_name in existing_field_to_id_map:
                    doc_id = existing_field_to_id_map[field_name]
                    self.object.docs[doc_id]["value"] = str(value)
                else:
                    doc_id = str(uuid.uuid4())
                    self.object.docs[doc_id] = {
                        "value": str(value),
                        "type": "text",
                        "id": doc_id,
                        "meta": {
                            "field_name": field_name,
                            "position": max_position + 1,
                        },
                    }
                max_position += 1
            self.object.save(update_fields=["docs"])

            # trigger re-processing
            self.build_docs()
        except Exception as e:
            logging.exception(
                f"Failed to process data enrichment for {self.object}: {e}"
            )
