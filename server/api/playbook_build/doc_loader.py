import copy
import json
import logging
import os
import re
from typing import List

import requests
from django.core.cache import cache
from langchain_core.documents import Document

from ..data_loaders.s3_file_loader import TofuS3FileLoader
from ..data_loaders.url_loader import TofuURLLoader
from ..thread_locals import get_enable_crawl_api
from ..utils import (
    count_sentences,
    get_s3_file,
    is_s3_url,
    parse_s3_presigned_url,
    to_camel_case,
)


class DocLoader:
    def __init__(self, metadata={}) -> None:
        # fields of metadata
        #   - playbook_id
        #   - column_id
        #   - key_ids
        self.metadata = metadata

    # interface
    def extract_docs(self, docs, with_change_type=True):
        documents = []
        errors = []

        docs_added_processed, errors_for_added, docs_build_status = (
            self.extract_docs_with_change_type(
                docs, "data_added" if with_change_type else None
            )
        )
        documents.extend(docs_added_processed)
        errors.extend(errors_for_added)

        return documents, errors, docs_build_status

    # interface
    def extract_docs_delta(self, old_docs, new_docs):
        # resolve None
        old_docs = old_docs or {}
        new_docs = new_docs or {}
        documents = []
        errors = []
        docs_build_status = {}

        # Compare keys instead of full dictionaries
        old_keys = set(old_docs.keys())
        new_keys = set(new_docs.keys())

        # Added: keys in new but not in old
        docs_added = {k: new_docs[k] for k in new_keys - old_keys}
        docs_added_processed, errors_for_added, docs_build_status_for_new = (
            self.extract_docs_with_change_type(docs_added, "data_added")
        )
        documents.extend(docs_added_processed)
        errors.extend(errors_for_added)
        docs_build_status.update(docs_build_status_for_new)

        # Deleted: keys in old but not in new
        docs_deleted = {k: old_docs[k] for k in old_keys - new_keys}
        docs_deleted_processed, errors_for_deleted, docs_build_status_for_delete = (
            self.extract_docs_with_change_type(docs_deleted, "data_deleted")
        )
        documents.extend(docs_deleted_processed)
        errors.extend(errors_for_deleted)
        docs_build_status.update(docs_build_status_for_delete)

        # Modified: keys in both but values different
        docs_modified = {
            k: new_docs[k] for k in old_keys & new_keys if new_docs[k] != old_docs[k]
        }
        (
            docs_modified_processed,
            errors_for_modified,
            docs_build_status_for_modified,
        ) = self.extract_docs_with_change_type(docs_modified, "data_modified")
        documents.extend(docs_modified_processed)
        errors.extend(errors_for_modified)
        docs_build_status.update(docs_build_status_for_modified)

        return documents, errors, docs_build_status

    def extract_docs_with_change_type(self, docs_dict, change_type):
        documents = []
        errors = []
        build_status = {}
        for doc_key, data_entry in docs_dict.items():
            loaded_docs, errors_in_load = self.extract_single_doc(
                data_entry, change_type
            )
            documents.extend(loaded_docs)
            errors.extend(errors_in_load)
            if errors_in_load:
                build_status[doc_key] = {
                    "status": "error",
                    "errors": errors_in_load,
                }
            else:
                build_status[doc_key] = {"status": "success"}
        return documents, errors, build_status

    def extract_single_doc(self, data_entry, change_type):
        errors_in_load = []
        try:
            loaded_docs = self.extract_single_doc_from_loader(
                data_entry.get("type", ""),
                data_entry.get("value", ""),
            )
        except Exception as e:
            loaded_docs = []
            errors_in_load = [str(e)]

        # empty list should return failure only for non-text
        if not loaded_docs and data_entry.get("type", "") != "text":
            errors_in_load.append("Failed to extract documents from the given doc.")

        for doc in loaded_docs:
            doc.metadata.update(self.metadata)
            field_name = data_entry.get("meta", {}).get("label", "")
            if not field_name:
                field_name = data_entry.get("meta", {}).get("field_name", "")
            doc.metadata.update(
                {
                    "data_id": data_entry.get("id", ""),
                    "data_type": data_entry.get("type", ""),
                    "field_name": field_name,
                }
            )
            if change_type:
                doc.metadata.update({"change_type": change_type})

            # remove all the None values from doc metadata (otherwise pinecone will complain)
            doc.metadata = {k: v for k, v in doc.metadata.items() if v is not None}
        return loaded_docs, errors_in_load

    def extract_single_doc_from_loader(
        self,
        type,
        value,
        deep_crawl=False,
        disable_cache=False,
        update_cache=False,
        fast_return=False,
    ):
        if not type or not value:
            return []
        documents = []
        if type == "text":
            documents.append(
                Document(
                    page_content=value,
                    metadata={
                        "mime_file_type": "text/plain",
                    },
                )
            )
        elif type == "url":
            if (
                get_enable_crawl_api()
                and os.environ.get("ENVIRONMENT") != "development"
            ):
                try:
                    return self.crawl_docs_from_api(
                        type=type,
                        value=value,
                        disable_cache=disable_cache,
                        update_cache=True,
                    )
                except Exception as e:
                    logging.error(f"Failed to extract documents from API. Error: {e}")
                    # try to get the data from cache first
                    cache_key = "{}:{}".format(value, deep_crawl)
                    serialized_e = Exception(
                        str(e)
                    )  # some exceptions are not serializable
                    cache.set(cache_key, serialized_e, 60 * 60 * 24 * 3)
                    return []

            loader = TofuURLLoader(value)
            loaded_docs = loader.load(
                deep_crawl=deep_crawl,
                disable_cache=disable_cache,
                update_cache=update_cache,
            )
            documents.extend(loaded_docs)
        elif type == "file":
            # if value is not dict, throw error
            if not isinstance(value, dict):
                raise Exception(f"Invalid value for file type: {value}")
            if (
                get_enable_crawl_api()
                and os.environ.get("ENVIRONMENT") != "development"
            ):
                try:
                    return self.crawl_docs_from_api(
                        type=type,
                        value=value,
                        disable_cache=disable_cache,
                        update_cache=True,
                        fast_return=fast_return,
                    )
                except Exception as e:
                    logging.error(f"Failed to fetch s3 docs from API. Error: {e}")
                    # try to get the data from cache first
                    cache_key = "{}:{}".format(value, deep_crawl)
                    serialized_e = Exception(
                        str(e)
                    )  # some exceptions are not serializable
                    cache.set(cache_key, serialized_e, 60 * 60 * 24 * 3)
                    return []
            else:
                loader = TofuS3FileLoader(value)
                loaded_docs = loader.load(
                    disable_cache=disable_cache,
                    update_cache=update_cache,
                    fast_return=fast_return,
                )
                documents.extend(loaded_docs)

            # TODO: also log the status of transcript finished?
        elif type == "web_search":
            # Deprecated 02/03/2025.
            pass
            # urls = [
            #     result["url"]
            #     for result in value
            #     if result.get("url") and result.get("checked") == True
            # ]
            # if not urls:
            #     return []
            # documents = []
            # errors = []
            # for url in urls:
            #     try:
            #         documents.extend(
            #             self.extract_single_doc_from_loader("url", url, deep_crawl)
            #         )
            #     except Exception as e:
            #         errors.append(str(e))
            # if errors:
            #     logging.error(f"Errors in web_search processing: {errors}")
            #     raise Exception(f"Errors in web_search processing: {errors}")
        else:
            logging.error(f"Unsupported type {type}")
            raise Exception(f"Unsupported type {type}")
        if type == "text" or type == "file":
            documents = self.try_extract_transcript(documents)
        # truncate all docs to 1 million chars
        for doc in documents:
            doc.page_content = doc.page_content[:1000000]
        return documents

    @staticmethod
    def crawl_docs_from_api(
        type, value, disable_cache=False, update_cache=False, fast_return=False
    ):
        # URL for the API endpoint
        url = "https://dev.api.tofuhq.com/api/playbook/extract_doc/"

        # Headers including Basic Auth, Content-Type, CSRF token, and Accept
        headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {os.environ.get('TOFU_ADMIN_TOKEN')}",
            "Content-Type": "application/json",
        }

        # Data payload for the POST request
        payload = {
            "doc_type": type,
            "doc_value": value,
            "disable_cache": disable_cache,
            "update_cache": update_cache,
            "fast_return": fast_return,
        }

        # Make the POST request
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            # Parse the response data
            response_data = response.json()
            if not response_data.get("content"):
                logging.error(
                    f"Failed to extract documents from API for {payload}. Response: {response_data}"
                )
            documents = [
                Document(
                    page_content=response_data.get("content", ""),
                    metadata=response_data.get("metadata", {}),
                )
            ]
            return documents
        else:
            raise Exception(
                f"Failed to extract documents from API for {payload}. Status code: {response.status_code}, Response: {response.text}"
            )

    # TODO: optimize_docs shall be checked where it's used
    @staticmethod
    def optimize_docs(
        original_docs: List[Document],
        remove_empty_docs: bool = True,
        remove_low_signal_docs: bool = False,
        remove_low_info_web_docs: bool = True,
        add_metadata_to_content: bool = True,
        remove_whitespace: bool = True,
        remove_non_text: bool = False,
    ):
        """
        Optimize the docs before processing
        This function will return a copy of the docs instead of modifying the original docs
        add_metadata_to_content: whether to add metadata as part of page content for documents
        """
        docs = copy.deepcopy(original_docs)
        if remove_whitespace:
            for doc in docs:
                doc.page_content = doc.page_content.replace(chr(10240), " ")
                doc.page_content = re.sub(r" +", " ", doc.page_content)
                doc.page_content = re.sub(r"\t+", "\t", doc.page_content)
                doc.page_content = re.sub(r"\n+", "\n", doc.page_content)
                doc.page_content = re.sub(r"\n+\s*", "\n", doc.page_content)
                doc.page_content = re.sub(
                    r"[\x00-\x08\x0B\x0C\x0E-\x1F]", "", doc.page_content
                )
        if remove_empty_docs:
            docs = [doc for doc in docs if doc.page_content.strip() != ""]
        if remove_low_signal_docs:
            # this is pretty arbitrary, but it's good for not indexing sections such as company name
            docs = [doc for doc in docs if len(doc.page_content.split()) > 5]
        if remove_low_info_web_docs:
            # Only check docs crawled from the web
            # TODO check this if we change the meta logic.
            for doc in docs:
                if "source" in doc.metadata and "title" in doc.metadata:
                    if (
                        count_sentences(doc.page_content) < 2
                        or len(doc.page_content) < 25
                    ):
                        docs.remove(doc)
        if remove_non_text:
            # WARNING: this will remove all non-text chars and newlines.
            for doc in docs:
                doc.page_content = re.sub(r"[^\w\s]", "", doc.page_content)
                # remove newlines
                doc.page_content = doc.page_content.replace("\n", " ")

        if add_metadata_to_content:
            for doc in docs:
                # check metadata is not empty and a dict
                if doc.metadata and isinstance(doc.metadata, dict):
                    # only include a subset of keys defined here to reduce token usage
                    keys_to_include = {
                        "key_ids",
                        "source",
                        "title",
                        "description",
                        "field_name",
                    }
                    metadata_to_include = {
                        k: v for k, v in doc.metadata.items() if k in keys_to_include
                    }
                    meta_xml_string = ""
                    if metadata_to_include:
                        for key, value in metadata_to_include.items():
                            if value:
                                value = str(value)
                                xml_key = to_camel_case(key)
                                meta_xml_string += f"<{xml_key}>{value}</{xml_key}>\n"
                    # Remove the appreances of xml tags such as <context>, </context>, <content>, </content> from doc.page_content
                    doc.page_content = re.sub(
                        r"<context>|</context>|<content>|</content>",
                        "",
                        doc.page_content,
                    )

                    doc.page_content = f"<context>\n<meta>\n{meta_xml_string}</meta>\n<content>\n{doc.page_content}\n</content>\n</context>"
        return docs

    @staticmethod
    def try_extract_transcript(docs):
        def check_list_schema(list_of_dicts, required_keys):
            # Check if the input is a list
            if not isinstance(list_of_dicts, list):
                return False

            # Check if all items in the list are dictionaries
            for obj in list_of_dicts:
                if not isinstance(obj, dict):
                    return False

                # Check if the dictionary contains all required keys
                if not all(key in obj for key in required_keys):
                    return False
            return True

        for doc in docs:
            if doc.metadata.get("mime_file_type", "text/plain") in (
                "application/json",
                "text/plain",
            ):
                try:
                    content = json.loads(doc.page_content)
                    required_keys = [
                        "speaker_id",
                        "speaker_name",
                        "startTime",
                        "endTime",
                        "sentence",
                    ]
                    if not content or not check_list_schema(content, required_keys):
                        continue

                    whole_doc = []
                    # now we know it's a transcript from fireflies
                    cur_speaker = content[0]["speaker_name"]
                    cur_speaker_id = content[0]["speaker_id"]
                    cur_sentences = [content[0]["sentence"]]

                    for item in content[1:]:

                        nxt_speaker = item.get("speaker_name", cur_speaker)
                        nxt_speaker_id = item.get("speaker_id", cur_speaker_id)
                        if nxt_speaker != cur_speaker:
                            whole_doc.append(
                                f"{cur_speaker}: {' '.join(cur_sentences)}"
                            )
                            cur_speaker = nxt_speaker
                            cur_speaker_id = nxt_speaker_id
                            cur_sentences = []
                        cur_sentences.append(item["sentence"])
                    whole_doc.append(f"{cur_speaker}: {' '.join(cur_sentences)}")
                    doc.page_content = "\n".join(whole_doc)
                except Exception as e:
                    continue
        return docs


def get_template(template_content_source_copy, enable_multimodal=True):

    try:
        if is_s3_url(template_content_source_copy):
            source_file_name, file_type, s3_bucket = parse_s3_presigned_url(
                template_content_source_copy
            )
            s3_data = {
                "s3_filename": source_file_name,
                "s3_bucket": s3_bucket,
                "mime_file_type": file_type,
            }
            if file_type == "application/json":
                try:
                    template_str = get_s3_file(template_content_source_copy)
                    template = json.loads(template_str)
                    return template
                except Exception as e:
                    logging.error(
                        "Failed to load template_content_source_copy as JSON. Error: {e}"
                    )
                    return {}
            else:
                loader = TofuS3FileLoader(s3_data)
        else:
            loader = TofuURLLoader(template_content_source_copy)

        if isinstance(loader, TofuURLLoader):
            loaded_docs = loader.load()
        else:
            loaded_docs = loader.load(enable_multimodal=enable_multimodal)
        text = "\n".join([doc.page_content for doc in loaded_docs])
        template = {"text": text}
    except Exception as e:
        logging.error(
            f"Failed to load template_content_source_copy {template_content_source_copy}. Error: {e}"
        )
        template = {}

    return template
