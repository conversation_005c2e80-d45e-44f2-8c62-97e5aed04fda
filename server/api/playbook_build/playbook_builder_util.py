import copy
import csv
import json
import logging
import os
from typing import Any, Dict, List, Optional

from ..models import AssetInfo, CompanyInfo, TargetInfo


def playbook_object_compare(playbook):
    has_mismatch = False

    # company info
    company_info = playbook.company_info
    if not company_info:
        logging.error(f"debug: playbook {playbook.id} has empty company_info")
        return True
    company_object = playbook.company_object
    if not company_object:
        logging.error(f"debug: playbook {playbook.id} has empty company_object")
        return True
    for k, v in company_info.items():
        if k == "meta":
            continue
        if len(v["data"]) != 1:
            logging.error(f"company_info {k} has more than one data")
            has_mismatch = True
            continue
        data = v["data"][0]
        data_id = data["id"]
        if data_id not in company_object.docs:
            logging.error(f"company_object {data_id} not found")
            has_mismatch = True
            continue
        object_data = company_object.docs[data_id]
        # logging.error(f"debug: comparing {data} with {object_data}")
        if data["type"] != object_data["type"]:
            logging.error(f"company_object {data_id} type not match")
            has_mismatch = True
        if data["value"] != object_data["value"]:
            logging.error(f"company_object {data_id} value not match")
            has_mismatch = True
        # check field name
        if k != object_data["meta"]["field_name"]:
            logging.error(f"company_object {data_id} field_name not match")
            has_mismatch = True
        # check position
        if "meta" not in v:
            pass  # legacy data don't have this for old playbooks
        elif v["meta"]["position"] != object_data["position"]:
            logging.error(f"company_object {data_id} position not match")
            has_mismatch = True
        if has_mismatch:
            logging.error(f"debug: mismatch - comparing {data} with {object_data}")

    # targets part
    target_info = playbook.target_info
    if not target_info:
        logging.error(f"debug: playbook {playbook.id} has empty target_info")
        return True

    target_info_groups = playbook.target_info_groups.all()

    for target_key1, target_key1_info in target_info.items():
        if target_key1 == "meta":
            continue
        target_info_group = target_info_groups.filter(target_info_group_key=target_key1)
        if not target_info_group:
            logging.error(f"target_info_group not found for {target_key1}")
            has_mismatch = True
            continue

        target_info_group = target_info_group[0]
        for target_key2, target_key2_info in target_key1_info.items():
            if target_key2 == "meta":
                continue
            target = target_info_group.targets.filter(target_key=target_key2)
            if not target:
                logging.error(f"target not found for {target_key1} - {target_key2}")
                has_mismatch = True

        for targets in target_info_group.targets.all():
            if targets.target_key not in target_key1_info:
                logging.error(f"target {targets.target_key} not found in new data")
                has_mismatch = True

    for target_info_group in target_info_groups:
        if target_info_group.target_info_group_key not in target_info:
            logging.error(
                f"target_info_group {target_info_group.target_info_group_key} not found in new data"
            )
            has_mismatch = True

    # check asset
    asset_info = playbook.assets
    asset_info_groups = playbook.asset_info_groups.all()

    for asset_key1, asset_key1_info in asset_info.items():
        if asset_key1 == "meta":
            continue
        asset_info_group = asset_info_groups.filter(asset_info_group_key=asset_key1)
        if not asset_info_group:
            logging.error(f"asset_info_group not found for {asset_key1}")
            has_mismatch = True
            continue
        asset_info_group = asset_info_group[0]
        for asset_key2, asset_key2_info in asset_key1_info.items():
            if asset_key2 == "meta":
                continue
            asset = asset_info_group.assets.filter(asset_key=asset_key2)
            if not asset:
                logging.error(
                    f"playbook {playbook.id}: asset not found for {asset_key1} - {asset_key2}"
                )
                has_mismatch = True
        for assets in asset_info_group.assets.all():
            if assets.asset_key not in asset_key1_info:
                logging.error(f"asset {assets.asset_key} not found in new data")
                has_mismatch = True

    for asset_info_group in asset_info_groups:
        if asset_info_group.asset_info_group_key not in asset_info:
            logging.error(
                f"asset_info_group {asset_info_group.asset_info_group_key} not found in new data"
            )
            has_mismatch = True

    return has_mismatch


from openai import OpenAI


class LLMOnParityEvaluator:
    def __init__(self, model_name) -> None:
        self.model_name = model_name
        self.client = OpenAI()

    def get_output(self, llm_inputs):
        # logging.error(f"inputs to llm is {llm_inputs}")
        response = self.client.chat.completions.create(
            model="gpt-4o-2024-11-20",
            messages=[
                {"role": "user", "content": llm_inputs},
            ],
        )
        logging.error(f"response: {response}")
        logging.error(f"content: {response.choices[0].message.content}")
        return response.choices[0].message.content

    def evaluate(self, data_1, data_2):
        instructions = """The following two summaries are generated both by LLM, please compare them if they are generated based on the same inputs, and return the results in YES/NO. Please only return either YES or NO.

-- summary one:
{data_1}
-- summary two:
{data_2}
        """.format(
            data_1=data_1, data_2=data_2
        )
        llm_output = self.get_output(instructions)
        return llm_output

    def give_reason(self, data_1, data_2):
        instructions = """The following two summaries are generated both by LLM. You said they are not generated based on the same inputs, please explain why.

-- summary one:
{data_1}
-- summary two:
{data_2}
        """.format(
            data_1=data_1, data_2=data_2
        )
        llm_output = self.get_output(instructions)
        return llm_output


def on_parity(new_text, old_text):
    evaluator = LLMOnParityEvaluator("gpt-4o-2024-11-20")
    eval_res = evaluator.evaluate(new_text, old_text)
    return eval_res.startswith("YES")


def on_parity_reason(new_text, old_text):
    evaluator = LLMOnParityEvaluator("gpt-4o-2024-11-20")
    eval_res = evaluator.give_reason(new_text, old_text)
    return eval_res
