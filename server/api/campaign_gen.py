import logging
import os
import time
import traceback
import uuid
from typing import List

import psutil

# redis
import redis
from celery import shared_task
from celery.exceptions import SoftTimeLimitExceeded
from django.apps import apps
from django.core.cache import cache
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from server.celery import app as celery_app

from .content import ContentGenerator
from .content_collection import ContentCollectionHandler
from .feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    ContentGenSettings,
    DataWrapperHolder,
    GenerateEnv,
)
from .gen_status import ContentGenStatusUpdater
from .models import Content, ContentGroup, TofuUser
from .playbook import PlaybookHandler
from .thread_locals import get_redis_conn, set_current_user
from .tofu_lite import add_credit_adjustment_to_user, check_available_credits
from .utils import get_user_from_id_or_object, measure_latency


def mark_leftover_contents_killed(content_ids: List[int], kill_reason: str) -> None:
    try:
        contents = Content.objects.filter(id__in=content_ids)

        content_to_update = []
        for content in contents:
            current_status = content.content_status.get("gen_status", {}).get("status")
            if current_status in {"QUEUED", "IN_PROGRESS"}:
                content.content_status["gen_status"] = {
                    "status": "ERROR",
                    "error": kill_reason,
                    "update_time": timezone.now().isoformat(),
                }
                content_to_update.append(content)
        Content.objects.bulk_update(content_to_update, ["content_status"])
    except Exception as e:
        logging.error(f"Failed to mark leftover contents killed: {e} for {content_ids}")


class CeleryTaskUpdater:
    @staticmethod
    def add_celery_job_for_content_group(content_group_id, task_id):
        redis_mapping_key = f"content_group_{content_group_id}"
        conn = get_redis_conn()
        with conn.pipeline() as pipe:
            while True:
                try:
                    pipe.watch(redis_mapping_key)
                    # Get the current set from the cache
                    current_set = pipe.get(redis_mapping_key)
                    if current_set is None:
                        current_set = set()
                    else:
                        current_set = set(current_set.decode("utf-8").split(","))

                    # Add the new value to the set
                    current_set.add(task_id)

                    # Save the updated set back to the cache
                    pipe.multi()
                    pipe.set(redis_mapping_key, ",".join(current_set))
                    pipe.execute()
                    break
                except redis.WatchError:
                    # If a WatchError is raised, another client must have changed the key
                    continue

    @staticmethod
    def terminate_celery_job_for_content_group(content_group_id):
        redis_mapping_key = f"content_group_{content_group_id}"
        conn = get_redis_conn()
        with conn.pipeline() as pipe:
            while True:
                try:
                    pipe.watch(redis_mapping_key)
                    # Get the current set from the cache
                    current_set = pipe.get(redis_mapping_key)
                    if current_set is None:
                        current_set = set()
                    else:
                        current_set = set(current_set.decode("utf-8").split(","))

                    logging.info(
                        f"Terminating celery jobs in set: {current_set} for {content_group_id}"
                    )
                    # Terminate all jobs in the set
                    for task_id in current_set:
                        celery_app.control.revoke(task_id, terminate=True)

                    # Save the updated set back to the cache
                    pipe.multi()
                    pipe.delete(redis_mapping_key)
                    pipe.execute()
                    break
                except redis.WatchError:
                    # If a WatchError is raised, another client must have changed the key
                    continue


def gen_content(
    playbook_handler,
    content,
    model_name,
    num_of_variations,
    enable_custom,
    joint_generation,
    template_generation,
    session_id,
    session_tag,
    cache_key,
):
    try:
        content_generator = ContentGenerator(playbook_handler, content)
        content_generator.set_settings(
            foundation_model=model_name,
            num_of_variations=num_of_variations,
            enable_custom=enable_custom,
            joint_generation=joint_generation,
            template_generation=template_generation,
            session_id=session_id,
            session_tag=session_tag,
            save_variations=True,
        )
        content_generator.gen()
    except Exception as e:
        logging.error(
            f"Campaign gen: content {content.id} generation failed: {e} with traceback: {traceback.format_exc()}"
        )
        ContentGenStatusUpdater.mark_content_gen_error(
            content,
            f"Content {content.id} failed to generate due to task {cache_key} failure: {str(e)}",
        )
        return False
    else:
        ContentGenStatusUpdater.mark_content_gen_finished(content)
        logging.info(f"Campaign gen: {content.id} generation finished")
        return True


class CampaignGenerator:
    CONTENTS_PER_JOB = 20
    MAX_CONCURRENT_JOBS = 3

    def __init__(self, campaign_instance) -> None:
        self.campaign_instance = campaign_instance

    @classmethod
    def load_from_db(cls, campaign_id):
        model = cls.get_model()
        campaign_instance = model.objects.filter(id=campaign_id).first()
        if not campaign_instance:
            raise Exception(f"Campaign with id {campaign_id} does not exist")
        campaign_handler = cls(campaign_instance)

        return campaign_handler

    @classmethod
    def load_from_db_instance(cls, campaign_instance):
        campaign_handler = cls(campaign_instance)

        return campaign_handler

    @classmethod
    def get_model(cls):
        return apps.get_model("api", "Campaign")

    @shared_task(bind=True)
    def process_content_batch(
        self,
        user_id,
        playbook_id,
        content_ids,
        model_name,
        num_of_variations,
        enable_custom,
        joint_generation,
        template_generation,
        session_id,
        session_tag,
        cache_key,
    ):
        start_time = time.time()
        pid = os.getpid()

        # Get the process handle
        process = psutil.Process(pid)

        initial_memory = process.memory_info().rss / (1024 * 1024)  # Convert to MB
        logging.info(
            f"Starting process_content_batch for content IDs: {content_ids} | PID: {pid} | Start time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))} | Initial Memory Usage: {initial_memory:.2f} MB"
        )
        try:
            contents = Content.objects.filter(id__in=content_ids)
            if not contents:
                logging.error(f"No content found for batch generation, using None")
                return []

            try:
                current_user = get_user_from_id_or_object(user_id, contents.first())
                if not current_user:
                    logging.error(f"No valid user found for content {contents.first()}")
                    return []
            except Exception as e:
                logging.exception(
                    f"Error getting user for batch generation with user_id {user_id} for {content_ids}: {e}"
                )
                return []

            set_current_user(current_user)
            ContentGenStatusUpdater.batch_mark_content_gen_queued(contents, cache_key)

            playbook_handler = PlaybookHandler.load_from_db(playbook_id)
            if not playbook_handler.playbook_instance:
                logging.error(
                    f"Playbook with id {playbook_id} does not exist for batch generation"
                )
                return []

            generated_content_ids = []

            for content in contents:
                ContentGenStatusUpdater.mark_content_gen_in_progress(content, cache_key)
                if gen_content(
                    playbook_handler,
                    content,
                    model_name,
                    num_of_variations,
                    enable_custom,
                    joint_generation,
                    template_generation,
                    session_id,
                    session_tag,
                    cache_key,
                ):
                    generated_content_ids.append(content.id)

            cache.set(cache_key, {"task_return": generated_content_ids}, 3600 * 24 * 30)
        except SoftTimeLimitExceeded:
            logging.exception(
                f"Time limit exceeded for process_content_batch {content_ids}"
            )
            mark_leftover_contents_killed(content_ids, "Time limit exceeded")
        except Exception as e:
            logging.error(f"Error in process_content_batch: {e} for {content_ids}")
            mark_leftover_contents_killed(content_ids, str(e))
        finally:
            # Final memory usage
            final_memory = process.memory_info().rss / (1024 * 1024)  # Convert to MB
            stop_time = time.time()
            duration = stop_time - start_time

            logging.info(
                f"Completed process_content_batch for content IDs: {content_ids} | PID: {pid} | Stop time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stop_time))} | Duration: {duration:.2f} seconds | Final Memory Usage: {final_memory:.2f} MB"
            )

        return generated_content_ids

    def run_gen_in_sequeue(
        self,
        content_group_id,
        content_ids,
        model_name,
        num_of_variations,
        enable_custom,
        joint_generation,
        template_generation,
        session_id,
        session_tag,
        cache_key,
    ):
        playbook_handler = PlaybookHandler.load_from_db(
            self.campaign_instance.playbook.id
        )
        generated_content_ids = []
        contents = Content.objects.filter(id__in=content_ids)
        for content in contents:
            if gen_content(
                playbook_handler,
                content,
                model_name,
                num_of_variations,
                enable_custom,
                joint_generation,
                template_generation,
                session_id,
                session_tag,
                cache_key,
            ):
                generated_content_ids.append(content.id)

        cache.set(cache_key, {"task_return": generated_content_ids}, 3600 * 24 * 30)
        return generated_content_ids

    def submit_batches_to_celery(
        self,
        user_id,
        content_group_id,
        content_ids,
        model_name,
        num_of_variations,
        enable_custom,
        joint_generation,
        template_generation,
        session_id,
        session_tag,
        cache_key,
    ):
        def batch_contents(content_ids, batch_size):
            for i in range(0, len(content_ids), batch_size):
                batch_ids = content_ids[i : i + batch_size]
                yield batch_ids

        processed_batches = [0]
        processed_content_ids = []

        playbook_id = self.campaign_instance.playbook.id

        content_batches = list(batch_contents(content_ids, self.CONTENTS_PER_JOB))
        total_batches = len(content_batches)
        total_contents = len(content_ids)

        def create_job(content_batch):
            task_id = f"campaign_gen_process_content_batch_{uuid.uuid4()}"
            CeleryTaskUpdater.add_celery_job_for_content_group(
                content_group_id, task_id
            )
            soft_time_limit = 60 * 30 * len(content_batch)
            return self.process_content_batch.s(
                user_id,
                playbook_id,
                content_batch,
                model_name,
                num_of_variations,
                enable_custom,
                joint_generation,
                template_generation,
                session_id,
                session_tag,
                task_id,
            ).set(task_id=task_id, priority=9, soft_time_limit=soft_time_limit)

        def submit_job(content_batch):
            job = create_job(content_batch)
            result = job.apply_async()
            processed_batches[0] += 1
            return result, content_batch

        # Submit initial batch of jobs
        active_jobs = [
            submit_job(content_batches.pop(0))
            for _ in range(min(self.MAX_CONCURRENT_JOBS, len(content_batches)))
        ]

        progress_logging_cnt = 0
        while active_jobs or content_batches:
            for i, (job, batch) in enumerate(active_jobs):
                if job.ready():
                    processed_content_ids.extend(batch)
                    logging.info(f"Job {job.id} completed. Batch size: {len(batch)}")
                    if content_batches:
                        active_jobs[i] = submit_job(content_batches.pop(0))
                    else:
                        active_jobs.pop(i)

            progress_logging_cnt += 1
            if progress_logging_cnt % 10 == 0:
                contents_processed = len(processed_content_ids)
                logging.info(
                    f"Progress: {contents_processed}/{total_contents} contents processed ({processed_batches[0]}/{total_batches} batches)"
                )
                progress_logging_cnt = 0
            time.sleep(1)  # Avoid busy waiting

        logging.info("All jobs completed")
        return processed_content_ids

    @measure_latency
    def gen_runner(
        self,
        user_id,
        content_group_id,
        content_ids,
        cache_key,
        model_name="",
        num_of_variations=None,
        enable_custom=True,
        joint_generation=False,
        template_generation=False,
        session_id=None,
        session_tag=None,
    ):

        parallel_run = len(content_ids) > self.CONTENTS_PER_JOB
        if parallel_run:
            try:
                generated_content_ids = self.submit_batches_to_celery(
                    user_id=user_id,
                    content_group_id=content_group_id,
                    content_ids=content_ids,
                    model_name=model_name,
                    num_of_variations=num_of_variations,
                    enable_custom=enable_custom,
                    joint_generation=joint_generation,
                    template_generation=template_generation,
                    session_id=session_id,
                    session_tag=session_tag,
                    cache_key=cache_key,
                )
            except Exception as e:
                logging.error(
                    f"Failed for run submit_batches_to_celery for {content_group_id}: {e}"
                )
                error_msg = f"Failed to generate content for {content_group_id} due to: {str(e)}"
                ContentGenStatusUpdater.batch_mark_content_gen_error(
                    content_ids, error_msg
                )
        else:
            generated_content_ids = self.run_gen_in_sequeue(
                content_group_id=content_group_id,
                content_ids=content_ids,
                model_name=model_name,
                num_of_variations=num_of_variations,
                enable_custom=enable_custom,
                joint_generation=joint_generation,
                template_generation=template_generation,
                session_id=session_id,
                session_tag=session_tag,
                cache_key=cache_key,
            )
        return generated_content_ids

    def gen_content_collection(
        self, collection_ids, model_name, session_id, session_tag
    ):
        if not collection_ids:
            return

        # TODO: unify with content.py foundation_model logic
        foundation_model = model_name
        if not foundation_model:
            foundation_model = self.campaign_instance.campaign_params.get(
                "foundation_model", ""
            )
        if not foundation_model:
            creator = self.campaign_instance.creator
            # creator might be None
            foundation_model = (getattr(creator, "context", None) or {}).get(
                "model", ""
            )
            # if repurpose model is set, use it to overwrite user level model
            model_for_repurpose = (getattr(creator, "context", None) or {}).get(
                "model_for_repurpose", ""
            )
            # we allow overwrite when the campaign goal is repurposing or it's doing template generation
            if model_for_repurpose:
                foundation_model = model_for_repurpose
        if not foundation_model:
            foundation_model = "gpt-4o-2024-11-20"

        for content_collection_id in collection_ids:
            content_collection_content_group = ContentGroup.objects.filter(
                content_group_params__content_collection__id=content_collection_id
            ).first()
            if not content_collection_content_group:
                logging.error(
                    f"Content collection content group not found for {content_collection_id}"
                )
                continue
            collection_data_wrapper = BaseContentWrapper.from_data_instance(
                content_collection_content_group
            )

            collection_gen_settings = ContentGenSettings(
                collection_data_wrapper,
                num_of_variations=1,
                foundation_model=foundation_model,
                content_collection_plan_gen=True,
                save_variations=False,
                session_id=session_id,
                session_tag=session_tag,
            )
            collection_gen_env = GenerateEnv(
                collection_data_wrapper, collection_gen_settings
            )
            content_collection_handler = ContentCollectionHandler(
                collection_gen_env,
            )
            content_collection_handler.update_content_group_collections_param(
                content_collection_id
            )

    @measure_latency
    def gen(
        self,
        user_id,
        contents_to_update,
        collection_ids,
        cache_key,
        joint_generation,
        model_name="",
        num_of_variations=None,
        enable_custom=True,
        template_generation=False,
        session_id=None,
        session_tag=None,
    ):
        self.gen_content_collection(collection_ids, model_name, session_id, session_tag)

        # tofu lite check
        user = TofuUser.objects.get(id=user_id)
        if user.customer_type == TofuUser.CustomerType.LITE:
            number_of_contents_to_gen = sum(
                len(content_ids) for content_ids in contents_to_update.values()
            )
            if check_available_credits(user_id) < number_of_contents_to_gen:
                raise Exception("Insufficient credits for this content generation.")

        all_generated_content_ids = []
        for content_group_id, content_ids in contents_to_update.items():
            generated_content_ids = self.gen_runner(
                user_id=user_id,
                content_group_id=content_group_id,
                content_ids=content_ids,
                cache_key=cache_key,
                model_name=model_name,
                num_of_variations=num_of_variations,
                enable_custom=enable_custom,
                joint_generation=joint_generation,
                template_generation=template_generation,
                session_id=session_id,
                session_tag=session_tag,
            )
            all_generated_content_ids.extend(generated_content_ids)

        # deduct tofu lite credits.
        with transaction.atomic():
            if user.customer_type == TofuUser.CustomerType.LITE:
                add_credit_adjustment_to_user(
                    user_id,
                    -1 * len(all_generated_content_ids),
                    additional_info={"content_gen": all_generated_content_ids},
                )

        return all_generated_content_ids

    def terminate_gen(self, content_group_ids):
        for content_group_id in content_group_ids:
            CeleryTaskUpdater.terminate_celery_job_for_content_group(content_group_id)

        contents = Content.objects.filter(
            content_group_id__in=content_group_ids
        ).filter(
            Q(content_status__gen_status__status="QUEUED")
            | Q(content_status__gen_status__status="IN_PROGRESS")
        )
        for content in contents:
            ContentGenStatusUpdater.mark_content_gen_terminated(content, is_save=False)
        Content.objects.bulk_update(contents, ["content_status"])
