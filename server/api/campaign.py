import logging
import time
import traceback
import uuid

from django.apps import apps
from django.core.cache import cache
from django.db import transaction
from django.forms.models import model_to_dict
from django.utils import timezone
from server.celery import app as celery_app

from .actions.action_copier import ActionCopier
from .content import ContentGenerator
from .content_collection import ContentCollection, ContentCollectionHandler
from .content_group import ContentGroupHandler, ContentGroupStatus
from .content_group_metric import ContentGroupMetricPuller
from .feature.data_wrapper.data_wrapper import (
    BaseContentWrapper,
    ContentGenSettings,
    GenerateEnv,
)
from .gen_status import GenStatusUpdater
from .inbound_handler import get_matching_criteria
from .models import (
    Action,
    Campaign,
    Content,
    ContentGroup,
    ContentVariation,
    InboundTypeChoices,
    Playbook,
    PublicContent,
    TargetInfoGroup,
    TofuUser,
)
from .playbook import PlaybookHandler
from .playbook_build.target_info_group_wrapper import TargetInfoGroupWrapper
from .thread_locals import get_current_user
from .utils import copy_s3_file, fix_content_collection_pointers, measure_latency


class CampaignHandler:
    def __init__(self, campaign_instance) -> None:
        self.campaign_instance = campaign_instance

    @classmethod
    def load_from_db(cls, campaign_id):
        model = cls.get_model()
        campaign_instance = model.objects.filter(id=campaign_id).first()
        if not campaign_instance:
            raise Exception(f"Campaign with id {campaign_id} does not exist")
        campaign_handler = cls(campaign_instance)

        return campaign_handler

    @classmethod
    def load_from_db_instance(cls, campaign_instance):
        campaign_handler = cls(campaign_instance)

        return campaign_handler

    @classmethod
    def get_model(cls):
        return apps.get_model("api", "Campaign")

    def get_all_content_collection_ids(self):
        content_groups = ContentGroup.objects.filter(campaign=self.campaign_instance)
        content_collection_ids = []
        for content_group in content_groups:
            content_group_params = content_group.content_group_params
            content_collection = content_group_params.get("content_collection", None)
            if content_collection:
                content_collection_ids.append(content_collection.get("id"))
        return set(content_collection_ids)

    def get_content_collection_group_ids(self, collection_ids):
        if not collection_ids:
            return []

        content_group_ids = []
        for collection_id in set(collection_ids):
            content_collection = ContentCollection(collection_id)
            content_group_ids.extend(
                list(content_collection.content_collection_map.keys())
            )
        content_group_ids = list(set(content_group_ids))
        return content_group_ids

    def gen_content_collection(
        self, collection_ids, model_name, session_id, session_tag
    ):
        if not collection_ids:
            return []

        collection_data_wrapper = BaseContentWrapper.from_data_instance(
            self.campaign_instance
        )
        # TODO: unify with content.py foundation_model logic
        foundation_model = model_name
        if not foundation_model:
            foundation_model = self.campaign_instance.campaign_params.get(
                "foundation_model", ""
            )
        if not foundation_model:
            creator = self.campaign_instance.creator
            # creator might be None
            foundation_model = (getattr(creator, "context", None) or {}).get(
                "model", ""
            )
            # if repurpose model is set, use it to overwrite user level model
            model_for_repurpose = (getattr(creator, "context", None) or {}).get(
                "model_for_repurpose", ""
            )
            # we allow overwrite when the campaign goal is repurposing or it's doing template generation
            if model_for_repurpose:
                foundation_model = model_for_repurpose
        if not foundation_model:
            foundation_model = "gpt-4o-2024-11-20"
        collection_gen_settings = ContentGenSettings(
            collection_data_wrapper,
            num_of_variations=1,
            foundation_model=foundation_model,
            content_collection_plan_gen=True,
            save_variations=False,
            session_id=session_id,
            session_tag=session_tag,
        )
        collection_gen_env = GenerateEnv(
            collection_data_wrapper, collection_gen_settings
        )
        content_collection_handler = ContentCollectionHandler(
            collection_gen_env,
        )
        for collection_id in collection_ids:
            content_collection_handler.update_content_group_collections_param(
                collection_id
            )

    @measure_latency
    def gen(
        self,
        content_group_ids,
        content_ids,
        cache_key,
        model_name="",
        num_of_variations=None,
        enable_custom=True,
        continue_gen=False,
        joint_generation=False,
        template_generation=False,
        collection_ids=[],
        session_id=None,
        session_tag=None,
        use_all_contents=False,
    ):
        if collection_ids and not use_all_contents:
            content_group_ids = self.get_content_collection_group_ids(collection_ids)

        # Prefetch related Content objects
        content_groups = (
            ContentGroup.objects.filter(
                campaign=self.campaign_instance
            ).prefetch_related("content_set")
            if (not content_group_ids or use_all_contents)
            else ContentGroup.objects.filter(
                id__in=content_group_ids,
            ).prefetch_related("content_set")
        )

        # Create a list to hold objects that need to be updated
        contents_to_update = []
        # Loop through prefetched content groups
        for content_group in content_groups:
            if content_ids:
                contents = content_group.content_set.filter(pk__in=content_ids)
            else:
                if content_group_ids and content_group.id not in content_group_ids:
                    continue
                contents = content_group.content_set.all()

            if not contents:
                continue

            content_group_has_content = False
            # Modify each content object and append it to the update list
            for content in contents:
                # Skip if continue_gen is True and the content is already generated
                if (
                    continue_gen
                    and content.content_status.get("gen_status", {}).get("status")
                    == "FINISHED"
                ):
                    continue
                content.content_status["gen_status"] = {
                    "status": "QUEUED",
                    "update_time": timezone.now().isoformat(),
                    "task_id": cache_key,
                }
                contents_to_update.append(content)
                content_group_has_content = True

            if content_group_has_content:
                content_group.content_group_status["gen_status"] = {"status": "QUEUED"}
                ContentGroup.objects.filter(pk=content_group.id).update(
                    content_group_status=content_group.content_group_status
                )

        # Perform a bulk update
        Content.objects.bulk_update(contents_to_update, ["content_status"])

        self.gen_content_collection(collection_ids, model_name, session_id, session_tag)

        playbook_handler = PlaybookHandler.load_from_db(
            self.campaign_instance.playbook.id
        )

        # refresh data from db
        content_ids_to_update = [content.id for content in contents_to_update]
        contents_to_update = list(
            Content.objects.filter(id__in=content_ids_to_update).select_related(
                "content_group"
            )
        )

        generated_content_ids = []
        for content in contents_to_update:
            try:
                content_generator = ContentGenerator(playbook_handler, content)
                content_generator.set_settings(
                    foundation_model=model_name,
                    num_of_variations=num_of_variations,
                    enable_custom=enable_custom,
                    joint_generation=joint_generation,
                    template_generation=template_generation,
                    session_id=session_id,
                    session_tag=session_tag,
                    save_variations=True,
                )
                content_generator.gen()
            except Exception as e:
                logging.error(
                    f"Campaign gen: content {content.id} generation failed: {e} with traceback: {traceback.format_exc()}"
                )
                content.content_status["gen_status"].update(
                    {
                        "status": "ERROR",
                        "error": f"Content {content.id} failed to generate due to task {cache_key} failure: {str(e)}",
                    }
                )
                Content.objects.filter(pk=content.id).update(
                    content_status=content.content_status
                )
            else:
                # TODO: this shall be done in the content generator
                # check if we need this
                content.content_status["gen_status"] = {
                    "status": "FINISHED",
                }
                Content.objects.filter(pk=content.id).update(
                    content_status=content.content_status
                )

            logging.info(f"Campaign gen: {content.id} generation finished")
            # update cache
            generated_content_ids.append(content.id)
            # TODO: fix the failed one
            cache.set(cache_key, {"task_return": generated_content_ids}, 3600 * 24 * 30)
        return generated_content_ids

    def terminate_gen(self, content_group_ids):
        content_groups = ContentGroup.objects.filter(
            campaign=self.campaign_instance,
            id__in=content_group_ids,
        ).prefetch_related("content_set")

        jobs_killed = set()
        contents_to_update = []
        for content_group in content_groups:
            for content in content_group.content_set.all():
                content_status = content.content_status.get("gen_status", {})
                if content_status.get("status") in ("QUEUED", "IN_PROGRESS"):
                    if "task_id" in content_status:
                        task_id = content_status.get("task_id")
                        if task_id not in jobs_killed:
                            celery_app.control.revoke(task_id, terminate=True)
                            jobs_killed.add(task_id)
                    content.content_status["gen_status"] = {
                        "status": "NOT_STARTED",
                        "comment": "Terminated by user",
                        "update_time": timezone.now().isoformat(),
                    }
                    contents_to_update.append(content)
        Content.objects.bulk_update(contents_to_update, ["content_status"])

    # Delete all the generated results of a campaign or content groups and reset gen status
    def delete_results(self, content_group_ids=[]):
        if not content_group_ids:
            content_groups = ContentGroup.objects.filter(
                campaign=self.campaign_instance
            )
        else:
            content_groups = ContentGroup.objects.filter(id__in=content_group_ids)

        for content_group in content_groups:
            content_group_handler = ContentGroupHandler(content_group)
            content_group_handler.delete_results()

    @staticmethod
    @celery_app.task(bind=False)
    def async_clone(
        source_campaign_id: int, dest_campaign_id: int, task_id: str
    ) -> None:
        try:
            cloned_campaign = Campaign.objects.filter(id=dest_campaign_id).first()
            if not cloned_campaign:
                raise Exception(
                    f"Destination campaign with id {dest_campaign_id} does not exist"
                )

            # query source campaign with all content_group and content data in one query
            source_campaign_instance = (
                Campaign.objects.filter(id=source_campaign_id)
                .prefetch_related("contentgroup_set__content_set")
                .first()
            )
            if not source_campaign_instance:
                raise Exception(
                    f"Source campaign with id {source_campaign_id} does not exist"
                )

            cloned_campaign.campaign_status["clone_status"].update(
                {
                    "status": "IN_PROGRESS",
                    "update_time": timezone.now().isoformat(),
                    "task_id": task_id,
                }
            )
            cloned_campaign.save(update_fields=["campaign_status"])

            is_campaign_v3 = source_campaign_instance.campaign_params.get(
                "is_campaign_v3", False
            )
            if is_campaign_v3:
                action_copier = ActionCopier()
                action_copier.copy_actions_for_campaign(
                    source_campaign_instance, cloned_campaign
                )

                cloned_campaign.campaign_status["clone_status"] = {
                    "status": "FINISHED",
                    "update_time": timezone.now().isoformat(),
                }
                cloned_campaign.save(update_fields=["campaign_status"])
            else:
                cloned_content_groups = []
                with transaction.atomic():
                    # Clone ContentGroups and their related objects
                    source_content_groups = (
                        source_campaign_instance.contentgroup_set.all()
                    )

                    for content_group in source_content_groups:
                        content_group_data = model_to_dict(content_group)
                        content_group_data.pop("id")
                        content_group_data.pop("actions", None)
                        content_group_data["campaign"] = cloned_campaign
                        content_group_data["creator"] = TofuUser.objects.get(
                            id=content_group_data["creator"]
                        )

                        new_content_source_copy = None
                        if content_group_data.get("content_group_params"):
                            new_content_source_copy = content_group_data[
                                "content_group_params"
                            ].get("content_source_copy")
                            if new_content_source_copy:
                                new_content_source_copy = copy_s3_file(
                                    new_content_source_copy
                                )
                                content_group_data["content_group_params"][
                                    "content_source_copy"
                                ] = new_content_source_copy

                        content_group_data["content_group_params"].pop(
                            "export_response", None
                        )
                        content_group_data["content_group_params"].pop(
                            "export_settings", None
                        )
                        content_group_data["content_group_params"].pop(
                            "reviewed_content_list", None
                        )
                        content_group_data["content_group_params"][
                            "orig_content_group_id"
                        ] = content_group.id

                        cloned_content_group = ContentGroup.objects.create(
                            **content_group_data
                        )
                        cloned_content_groups.append(cloned_content_group)

                    for content_group in cloned_content_groups:
                        CampaignHandler.fix_linked_content_group(content_group)

                    GenStatusUpdater().get_campaign_gen_status(cloned_campaign)
                    CampaignHandler(cloned_campaign).update_content_collection_params()

                    cloned_campaign.campaign_status["clone_status"] = {
                        "status": "FINISHED",
                        "update_time": timezone.now().isoformat(),
                    }
                    cloned_campaign.save(update_fields=["campaign_status"])

                with transaction.atomic():
                    for cloned_content_group in cloned_content_groups:
                        ContentGroupHandler(cloned_content_group).bulk_create_content()

        except Exception as e:
            logging.error(
                f"Failed to clone campaign {source_campaign_id} to {dest_campaign_id}: {e}\n{traceback.format_exc()}"
            )
            # set cloned campaign status to error
            cloned_campaign.campaign_status["clone_status"] = {
                "status": "ERROR",
                "update_time": timezone.now().isoformat(),
                "error": str(e),
            }
            cloned_campaign.save(update_fields=["campaign_status"])

    def sync_clone(
        self, source_campaign_id: int, dest_campaign_id: int, task_id: str
    ) -> None:
        try:
            cloned_campaign = Campaign.objects.filter(id=dest_campaign_id).first()
            if not cloned_campaign:
                raise Exception(
                    f"Destination campaign with id {dest_campaign_id} does not exist"
                )

            # query source campaign with all content_group and content data in one query
            source_campaign_instance = (
                Campaign.objects.filter(id=source_campaign_id)
                .prefetch_related("contentgroup_set__content_set")
                .first()
            )
            if not source_campaign_instance:
                raise Exception(
                    f"Source campaign with id {source_campaign_id} does not exist"
                )

            cloned_campaign.campaign_status["clone_status"].update(
                {
                    "status": "IN_PROGRESS",
                    "update_time": timezone.now().isoformat(),
                    "task_id": task_id,
                }
            )
            cloned_campaign.save(update_fields=["campaign_status"])

            is_campaign_v3 = source_campaign_instance.campaign_params.get(
                "is_campaign_v3", False
            )
            if is_campaign_v3:
                action_copier = ActionCopier()
                action_copier.copy_actions_for_campaign(
                    source_campaign_instance, cloned_campaign
                )

                cloned_campaign.campaign_status["clone_status"] = {
                    "status": "FINISHED",
                    "update_time": timezone.now().isoformat(),
                }
                cloned_campaign.save(update_fields=["campaign_status"])
            else:
                cloned_content_groups = []
                with transaction.atomic():
                    # Clone ContentGroups and their related objects
                    source_content_groups = (
                        source_campaign_instance.contentgroup_set.all()
                    )

                    for content_group in source_content_groups:
                        content_group_data = model_to_dict(content_group)
                        content_group_data.pop("id")
                        content_group_data.pop("actions", None)
                        content_group_data["campaign"] = cloned_campaign
                        content_group_data["creator"] = TofuUser.objects.get(
                            id=content_group_data["creator"]
                        )

                        new_content_source_copy = None
                        if content_group_data.get("content_group_params"):
                            new_content_source_copy = content_group_data[
                                "content_group_params"
                            ].get("content_source_copy")
                            if new_content_source_copy:
                                new_content_source_copy = copy_s3_file(
                                    new_content_source_copy
                                )
                                content_group_data["content_group_params"][
                                    "content_source_copy"
                                ] = new_content_source_copy

                        content_group_data["content_group_params"].pop(
                            "export_response", None
                        )
                        content_group_data["content_group_params"].pop(
                            "export_settings", None
                        )
                        content_group_data["content_group_params"].pop(
                            "reviewed_content_list", None
                        )
                        content_group_data["content_group_params"][
                            "orig_content_group_id"
                        ] = content_group.id

                        cloned_content_group = ContentGroup.objects.create(
                            **content_group_data
                        )
                        cloned_content_groups.append(cloned_content_group)

                    for content_group in cloned_content_groups:
                        CampaignHandler.fix_linked_content_group(content_group)

                    GenStatusUpdater().get_campaign_gen_status(cloned_campaign)
                    CampaignHandler(cloned_campaign).update_content_collection_params()

                    cloned_campaign.campaign_status["clone_status"] = {
                        "status": "FINISHED",
                        "update_time": timezone.now().isoformat(),
                    }
                    cloned_campaign.save(update_fields=["campaign_status"])

        except Exception as e:
            logging.error(
                f"Failed to clone campaign {source_campaign_id} to {dest_campaign_id}: {e}\n{traceback.format_exc()}"
            )
            # set cloned campaign status to error
            cloned_campaign.campaign_status["clone_status"] = {
                "status": "ERROR",
                "update_time": timezone.now().isoformat(),
                "error": str(e),
            }
            cloned_campaign.save(update_fields=["campaign_status"])

    # clone a campaign
    def clone(self, blocking=False) -> Campaign:
        def get_one_target(targets, targets_concat):
            if not targets:
                return []
            first_target_sets = targets[0]
            one_target = {}
            for l1_key, l2_keys in first_target_sets.items():
                one_target[l1_key] = [l2_keys[0]]
                if targets_concat:
                    break
            return [one_target]

        clone_task_id = f"clone_campaign_{self.campaign_instance.id}_{uuid.uuid4()}"
        # Clone Campaign
        campaign_data = model_to_dict(self.campaign_instance)
        campaign_data.pop("id")
        campaign_data["creator"] = TofuUser.objects.get(id=campaign_data["creator"])
        campaign_data["playbook"] = Playbook.objects.get(id=campaign_data["playbook"])
        campaign_data["campaign_name"] = f"Copy of {campaign_data['campaign_name']}"
        campaign_data["campaign_params"].pop("enable_auto_sync", None)
        campaign_data["campaign_params"]["allSelectedTargets"] = []
        campaign_data["campaign_params"]["targets"] = get_one_target(
            campaign_data["campaign_params"].get("targets", []),
            campaign_data["campaign_params"].get("targets_concat", False),
        )
        campaign_data["campaign_params"]["orig_campaign_id"] = self.campaign_instance.id
        campaign_data["campaign_status"]["clone_status"] = {
            "status": "SUBMITTED",
            "update_time": timezone.now().isoformat(),
            "task_id": clone_task_id,
        }

        with transaction.atomic():
            cloned_campaign = Campaign.objects.create(**campaign_data)
            cloned_campaign.save()

            if not cloned_campaign:
                raise Exception("Failed to clone campaign")
        if blocking:
            self.sync_clone(
                self.campaign_instance.id, cloned_campaign.id, clone_task_id
            )
        else:
            self.async_clone.apply_async(
                args=[self.campaign_instance.id, cloned_campaign.id, clone_task_id],
                task_id=clone_task_id,
            )

        return cloned_campaign

    def fix_linked_content_group(content_group):
        components = content_group.components
        if not components:
            return
        for key, value in components.items():
            if value.get("meta", {}).get("type", "") == "link":
                linked_contentGroupId = value.get("link", {}).get("contentGroupId", "")
                if linked_contentGroupId:
                    # check the content groups of this campaign to see if any have orig_content_group_id in content group params the same as this linked_contentGroupId
                    linked_content_group = ContentGroup.objects.filter(
                        campaign=content_group.campaign,
                        content_group_params__orig_content_group_id=linked_contentGroupId,
                    ).first()
                    if linked_content_group:
                        content_group.components[key]["link"][
                            "contentGroupId"
                        ] = linked_content_group.id
                        content_group.save()

    def gen_fake_metrics(self):
        # scale
        target_size = 0
        is_targets_concat = self.campaign_instance.campaign_params.get(
            "targets_concat", False
        )
        for target_list in self.campaign_instance.campaign_params.get("targets", []):
            sub_target_size = 1 if not is_targets_concat else 0
            for k, v in target_list.items():
                if is_targets_concat:
                    sub_target_size += len(v)
                else:
                    sub_target_size *= len(v)
            target_size += sub_target_size

        if target_size == 0:
            logging.warning(
                f"Target size is 0 for campaign {self.campaign_instance.id} in fake metrics generation"
            )
            return {}

        metric_tiles = {}

        for content_group in ContentGroup.objects.filter(
            campaign=self.campaign_instance
        ):
            metric = ContentGroupMetricPuller(content_group).gen_fake_metrics(
                target_size
            )
            if metric:
                metric_tiles[content_group.id] = metric
        return metric_tiles

    def pull_metrics(self):
        user = (
            self.campaign_instance.creator
            if self.campaign_instance and hasattr(self.campaign_instance, "creator")
            else None
        )
        if not user:
            logging.error("No user found for campaign metric pull, use current user")
            user = get_current_user()
        if not user:
            return {}

        metric_tiles = {}

        # Prefetch related Content objects
        content_groups = ContentGroup.objects.filter(
            campaign=self.campaign_instance
        ).prefetch_related("content_set")
        for content_group in content_groups:
            try:
                content_group_metrics = ContentGroupMetricPuller(content_group).pull()
                if not content_group_metrics:
                    continue
                metric_tiles[content_group.id] = content_group_metrics
            except Exception as e:
                logging.exception(
                    f"Failed to pull metrics for content group {content_group.id}: {e}"
                )
        return metric_tiles

    def get_metric_tiles(self):
        # cache campaign performance metrics for 1 day
        cache_key = f"campaign_metric_tiles_v2_{self.campaign_instance.id}"
        fake_metrics = self.campaign_instance.playbook.settings.get(
            "enableFakeMetrics", False
        )
        if fake_metrics:  # we need a dedicated key in case people switch
            cache_key = f"fake_{cache_key}"

        try:
            metric_tiles = cache.get(cache_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get metric tiles for campaign {self.campaign_instance.id}: {e}"
            )
            metric_tiles = {}
        if metric_tiles:
            return metric_tiles

        try:
            if self.campaign_instance.playbook.settings.get("enableFakeMetrics", False):
                metric_tiles = self.gen_fake_metrics()
            else:
                metric_tiles = self.pull_metrics()

            cache.set(cache_key, metric_tiles, 3600 * 24)
        except Exception as e:
            logging.error(
                f"Failed to get metric tiles for campaign {self.campaign_instance.id}: {e}\n{traceback.format_exc()}"
            )
            metric_tiles = {}

        return metric_tiles

    def update_content_collection_params(self, is_campaign_v3=False):
        # get the content groups for this campaign.
        collection_content_groups = ContentGroup.objects.filter(
            campaign=self.campaign_instance
        ).filter(content_group_params__content_collection__isnull=False)
        if is_campaign_v3:
            content_group_ids = [cg.id for cg in collection_content_groups]
            fix_content_collection_pointers(content_group_ids)
            return
        content_group_map = self.get_copy_content_group_map(collection_content_groups)
        content_collection_id_map = {}
        for content_group in collection_content_groups:
            # check if it has a content collection
            content_group_params = content_group.content_group_params
            content_collection = content_group_params.get("content_collection", None)
            if not content_collection:
                continue
            content_collection_id = content_collection.get("id")
            if content_collection_id not in content_collection_id_map:
                content_collection_id_map[content_collection_id] = str(uuid.uuid4())
            content_collection["id"] = content_collection_id_map[content_collection_id]
            self.remap_content_group(content_collection, content_group_map, "next")
            self.remap_content_group(content_collection, content_group_map, "prev")
            content_group.save()

    def get_copy_content_group_map(self, content_groups):
        content_group_map = {}
        for content_group in content_groups:
            # assume the content_group_params has orig_content_group_id
            orig_content_group_id = content_group.content_group_params.get(
                "orig_content_group_id", None
            )
            if orig_content_group_id:
                content_group_map[orig_content_group_id] = content_group
        return content_group_map

    def remap_content_group(self, content_collection, content_group_map, param_name):
        content_collection_content_groups = content_collection.get(param_name, [])
        new_content_groups = []
        if content_collection_content_groups:
            for content_group_id in content_collection_content_groups:
                if content_group_id in content_group_map:
                    new_content_groups.append(content_group_map[content_group_id].id)
                else:
                    content_collection_id = content_collection.get("id", None)
                    logging.error(
                        f"Next content group {content_group_id} not found for content collection {content_collection_id} with content group map {content_group_map}"
                    )
        content_collection[param_name] = (
            new_content_groups if new_content_groups else None
        )

    # inbound handling
    def update_exported_inbound_data(
        self, current_inbound_enabled: bool, prev_inbound_enabled: bool
    ):
        if current_inbound_enabled == prev_inbound_enabled:
            return

        all_contents = Content.objects.filter(
            content_group__campaign=self.campaign_instance
        )
        content_dict = {content.id: content for content in all_contents}

        try:
            all_public_contents = PublicContent.objects.filter(
                content_id__in=content_dict.keys()
            )
            for public_content in all_public_contents:
                content = content_dict.get(public_content.content_id, None)
                if not content:
                    logging.error(
                        f"content not found for public content {public_content.id}"
                    )
                    continue
                if current_inbound_enabled:
                    public_content.inbound_type = InboundTypeChoices.PARTIAL_INBOUND
                    public_content.matching_criteria = get_matching_criteria(content)
                else:
                    public_content.inbound_type = InboundTypeChoices.OUTBOUND
                    public_content.matching_criteria = []
            PublicContent.objects.bulk_update(
                all_public_contents,
                fields=["inbound_type", "matching_criteria"],
            )
        except Exception as e:
            logging.error(
                f"Failed to update public content {e}\n{traceback.format_exc()}"
            )

    def set_inbound_domain_field(self, domain_field: str):
        is_targets_concat = self.campaign_instance.campaign_params.get(
            "targets_concat", False
        )
        if is_targets_concat:
            logging.error(
                f"Inbound domain field setting is not supported for concatenation mode"
            )
            return
        for target_dict in self.campaign_instance.campaign_params.get("targets", []):
            for target_info_group_key in target_dict.keys():
                try:
                    target_info_group = TargetInfoGroup.objects.filter(
                        target_info_group_key=target_info_group_key,
                        playbook=self.campaign_instance.playbook,
                    ).first()
                    if not target_info_group:
                        logging.error(
                            f"target info group not found for {target_info_group_key}"
                        )
                        continue
                    TargetInfoGroupWrapper(target_info_group).set_domain_field(
                        domain_field
                    )
                except Exception as e:
                    logging.error(
                        f"Failed to set domain field {domain_field} for target info group {target_info_group_key}: {e}\n{traceback.format_exc()}"
                    )
