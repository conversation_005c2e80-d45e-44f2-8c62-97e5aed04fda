# Generated by Django 4.1.7 on 2023-03-23 00:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0005_content_remove_company_owner_remove_target_owner_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Playbook",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("company_info", models.JSONField()),
                ("target_info", models.J<PERSON><PERSON>ield()),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
            ],
        ),
        migrations.RemoveField(model_name="target", name="company",),
        migrations.RemoveField(model_name="target", name="creator",),
        migrations.RemoveField(model_name="content", name="company",),
        migrations.RemoveField(model_name="tofuuser", name="company",),
        migrations.DeleteModel(name="Company",),
        migrations.DeleteModel(name="Target",),
        migrations.AddField(
            model_name="playbook",
            name="creator",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="playbook_creator",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="content",
            name="playbook",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="content_playbook",
                to="api.playbook",
            ),
        ),
        migrations.AddField(
            model_name="tofuuser",
            name="playbook",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.playbook",
            ),
        ),
    ]
