# Generated by Django 4.2.1 on 2024-10-17 05:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0059_tag"),
    ]

    operations = [
        migrations.AlterUniqueTogether(name="tag", unique_together=set(),),
        migrations.AlterField(
            model_name="tag",
            name="creator",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AlterUniqueTogether(
            name="tag", unique_together={("creator", "name")},
        ),
        migrations.RemoveField(model_name="tag", name="playbook",),
    ]
