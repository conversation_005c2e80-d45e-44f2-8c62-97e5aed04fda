# Generated by Django 4.2.1 on 2024-01-05 20:15

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0039_eventlogs"),
    ]

    operations = [
        migrations.CreateModel(
            name="OffsiteEventLogs",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("event_type", models.Char<PERSON>ield(max_length=255)),
                ("playbook_id", models.IntegerField(blank=True, null=True)),
                ("campaign_id", models.IntegerField(blank=True, null=True)),
                ("content_id", models.IntegerField()),
                ("content_variation_index", models.IntegerField(blank=True, null=True)),
                ("tofu_slug", models.Char<PERSON>ield(blank=True, max_length=255)),
                ("ip_address", models.Char<PERSON>ield(blank=True, max_length=255)),
                ("user_agent", models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255)),
                ("payload", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
            ],
        ),
    ]
