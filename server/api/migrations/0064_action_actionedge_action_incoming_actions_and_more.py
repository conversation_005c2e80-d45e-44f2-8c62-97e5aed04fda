# Generated by Django 4.2.1 on 2024-12-02 23:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0063_alter_tofuuser_credits_last_updated"),
    ]

    operations = [
        migrations.CreateModel(
            name="Action",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("action_name", models.Char<PERSON>ield(max_length=255)),
                ("action_category", models.Char<PERSON>ield(max_length=255)),
                ("inputs", models.J<PERSON><PERSON>ield(blank=True)),
                ("outputs", models.J<PERSON><PERSON>ield(blank=True)),
                ("status", models.J<PERSON><PERSON>ield(blank=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "campaign",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.campaign"
                    ),
                ),
                (
                    "creator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ActionEdge",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("config", models.JSONField(blank=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "from_action",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outgoing_edges",
                        to="api.action",
                    ),
                ),
                (
                    "to_action",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incoming_edges",
                        to="api.action",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="action",
            name="incoming_actions",
            field=models.ManyToManyField(
                related_name="outgoing_actions",
                through="api.ActionEdge",
                to="api.action",
            ),
        ),
        migrations.AddField(
            model_name="action",
            name="playbook",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.playbook"
            ),
        ),
        migrations.AddIndex(
            model_name="actionedge",
            index=models.Index(
                fields=["from_action"], name="api_actione_from_ac_08f58a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="actionedge",
            index=models.Index(
                fields=["to_action"], name="api_actione_to_acti_a0bc6f_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="actionedge",
            unique_together={("from_action", "to_action")},
        ),
        migrations.AddIndex(
            model_name="action",
            index=models.Index(
                fields=["campaign"], name="api_action_campaig_0100e9_idx"
            ),
        ),
    ]
