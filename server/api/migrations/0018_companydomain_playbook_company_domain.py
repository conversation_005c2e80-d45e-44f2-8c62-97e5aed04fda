# Generated by Django 4.2.1 on 2023-05-08 21:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0017_tofuuser_context"),
    ]

    operations = [
        migrations.CreateModel(
            name="CompanyDomain",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "link_playbook_domain",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                (
                    "allow_register_domain",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
            ],
        ),
        migrations.AddField(
            model_name="playbook",
            name="company_domain",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
    ]
