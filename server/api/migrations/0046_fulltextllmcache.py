# Generated by Django 4.2.1 on 2024-04-02 18:52

import django.contrib.postgres.indexes
import django.contrib.postgres.search
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0045_alter_offsiteeventlogs_user_agent"),
    ]

    operations = [
        migrations.CreateModel(
            name="FulltextLLMCache",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("prompt", models.J<PERSON>NField()),
                ("llm", models.TextField()),
                ("idx", models.IntegerField(blank=True, null=True)),
                ("response", models.TextField(blank=True, null=True)),
                (
                    "prompt_tsv",
                    django.contrib.postgres.search.SearchVectorField(null=True),
                ),
            ],
            options={
                "indexes": [
                    django.contrib.postgres.indexes.GinIndex(
                        fields=["prompt_tsv"], name="idx_fulltext_prompt_tsv"
                    )
                ],
            },
        ),
    ]
