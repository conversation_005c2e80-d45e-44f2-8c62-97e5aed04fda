# Generated by Django 4.2.1 on 2024-09-18 00:12

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0057_publiccontent_content_id_publiccontent_inbound_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="FeatureAnnouncement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("NF", "New Features"),
                            ("TT", "Tips and Tutorials"),
                            ("WN", "Whats New"),
                        ],
                        default="NF",
                        max_length=16,
                    ),
                ),
                ("is_shown", models.BooleanField(default=False)),
                ("position", models.IntegerField(default=0)),
                ("tag", models.CharField(blank=True, max_length=255, null=True)),
                ("headline", models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("link", models.URLField(blank=True, null=True)),
                ("image_url", models.URLField(blank=True, null=True)),
                ("video_url", models.URLField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
            ],
        ),
    ]
