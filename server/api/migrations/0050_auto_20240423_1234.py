from django.db import migrations
import uuid


def generate_uuid(apps, schema_editor):
    OffsiteEventLogs = apps.get_model("api", "OffsiteEventLogs")
    for log in OffsiteEventLogs.objects.all():
        log.session_id = str(uuid.uuid4())  # Using hex to get a simple string format
        log.save(update_fields=["session_id"])


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0049_offsiteeventlogs_session_id"),
    ]
    operations = [
        migrations.RunPython(generate_uuid),
    ]
