# Generated by Django 4.2.1 on 2024-08-28 18:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0055_debuglogentry_publiccontent_matching_criteria'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=255)),
                ('template_data', models.JSONField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('creator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('playbook', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api.playbook')),
            ],
        ),
    ]
