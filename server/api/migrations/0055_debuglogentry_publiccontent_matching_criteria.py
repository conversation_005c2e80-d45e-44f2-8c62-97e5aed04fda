# Generated by Django 4.2.1 on 2024-08-28 17:35

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0054_urlrecord"),
    ]

    operations = [
        migrations.CreateModel(
            name="DebugLogEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("entry_type", models.CharField(max_length=255)),
                ("payload", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name="publiccontent",
            name="matching_criteria",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.<PERSON>r<PERSON><PERSON>(max_length=1023),
                blank=True,
                default=list,
                null=True,
                size=None,
            ),
        ),
    ]
