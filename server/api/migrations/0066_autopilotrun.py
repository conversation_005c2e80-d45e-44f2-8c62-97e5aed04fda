# Generated by Django 4.2.1 on 2024-12-06 23:25

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0065_offsiteeventlogs_content_group_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AutopilotRun',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('session_id', models.CharField(blank=True, max_length=255, null=True)),
                ('autopilot_action_type', models.CharField(choices=[('crm_sync', 'CRM Sync'), ('target_creation', 'Target Creation'), ('content_generation', 'Content Generation'), ('export', 'Export')], max_length=255)),
                ('status', models.JSONField(blank=True, default=dict, null=True)),
                ('additional_info', models.JSONField(blank=True, default=dict, null=True)),
                ('campaign', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.campaign')),
                ('playbook', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.playbook')),
                ('target_info_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.targetinfogroup')),
            ],
        ),
    ]
