# Generated by Django 4.1.7 on 2023-03-22 03:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0003_tofuuser_full_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="ContentFactory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content_name", models.CharField(blank=True, max_length=255)),
                ("content_params", models.J<PERSON>NField()),
                ("generated_contents", models.JSO<PERSON>ield()),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="target",
            name="company",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="target_info_company_owner",
                to="api.company",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="tofuuser",
            name="company",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.company",
            ),
        ),
        migrations.AlterField(
            model_name="company",
            name="owner",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="company_info_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="target",
            name="owner",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="target_info_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.DeleteModel(name="GeneratedContent",),
        migrations.AddField(
            model_name="contentfactory",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="content_company_ownder",
                to="api.company",
            ),
        ),
        migrations.AddField(
            model_name="contentfactory",
            name="owner",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="content_owner",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
