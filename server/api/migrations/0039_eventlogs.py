# Generated by Django 4.2.1 on 2023-12-18 02:17

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0038_alter_campaign_campaign_status_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="EventLogs",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("event_type", models.CharField(max_length=255)),
                ("user_id", models.IntegerField(blank=True, null=True)),
                ("playbook_id", models.IntegerField(blank=True, null=True)),
                ("campaign_id", models.IntegerField(blank=True, null=True)),
                ("payload", models.JSONField(blank=True, null=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
            ],
        ),
    ]
