# Generated by Django 4.1.7 on 2023-03-22 21:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0004_contentfactory_target_company_tofuuser_company_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Content",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content_name", models.CharField(blank=True, max_length=255)),
                ("content_params", models.JSONField()),
                ("generated_contents", models.<PERSON><PERSON><PERSON>ield()),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
            ],
        ),
        migrations.RemoveField(model_name="company", name="owner",),
        migrations.RemoveField(model_name="target", name="owner",),
        migrations.AddField(
            model_name="company",
            name="creator",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="company_info_creator",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="target",
            name="creator",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="target_info_creator",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.DeleteModel(name="ContentFactory",),
        migrations.AddField(
            model_name="content",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="content_company_ownder",
                to="api.company",
            ),
        ),
        migrations.AddField(
            model_name="content",
            name="creator",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="content_creator",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
