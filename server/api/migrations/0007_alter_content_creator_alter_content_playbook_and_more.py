# Generated by Django 4.1.7 on 2023-03-23 01:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0006_playbook_remove_target_company_remove_target_creator_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="content",
            name="creator",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="content_creator",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="content",
            name="playbook",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="content_playbook",
                to="api.playbook",
            ),
        ),
        migrations.AlterField(
            model_name="playbook",
            name="creator",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="playbook_creator",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
