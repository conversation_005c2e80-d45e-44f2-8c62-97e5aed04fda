# Generated by Django 4.2.1 on 2023-10-13 20:53

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0029_longtext"),
    ]

    operations = [
        migrations.CreateModel(
            name="TargetGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("target_group_key", models.CharField(max_length=255)),
                ("meta", models.JSONField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "playbook",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="target_groups",
                        to="api.playbook",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TargetInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("docs", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ("summary", models.TextField(blank=True, null=True)),
                ("index", models.JSONField(blank=True, null=True)),
                ("docs_last_build", models.JSONField(blank=True, null=True)),
                ("docs_build_status", models.JSONField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("meta", models.JSONField(blank=True, null=True)),
                ("target_key", models.CharField(max_length=255)),
                ("value_prop", models.TextField(blank=True, null=True)),
                (
                    "target_group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="targets",
                        to="api.targetgroup",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
