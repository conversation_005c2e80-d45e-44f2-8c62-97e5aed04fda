# Generated by Django 4.2.1 on 2023-12-06 21:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0034_rename_targetgroup_targetinfogroup"),
    ]

    operations = [
        migrations.RenameField(
            model_name="targetinfo",
            old_name="target_group",
            new_name="target_info_group",
        ),
        migrations.RenameField(
            model_name="targetinfogroup",
            old_name="target_group_key",
            new_name="target_info_group_key",
        ),
        migrations.AlterField(
            model_name="targetinfogroup",
            name="playbook",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="target_info_groups",
                to="api.playbook",
            ),
        ),
    ]
