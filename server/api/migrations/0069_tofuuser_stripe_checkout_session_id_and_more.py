# Generated by Django 4.2.1 on 2024-12-13 23:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0068_remove_contentgroup_actions'),
    ]

    operations = [
        migrations.AddField(
            model_name='tofuuser',
            name='stripe_checkout_session_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='tofuuser',
            name='stripe_customer_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='featureannouncement',
            name='type',
            field=models.CharField(choices=[('NF', 'New Features'), ('TT', 'Tips and Tutorials'), ('WN', 'Whats New'), ('IC', 'Incident')], default='NF', max_length=16),
        ),
    ]
