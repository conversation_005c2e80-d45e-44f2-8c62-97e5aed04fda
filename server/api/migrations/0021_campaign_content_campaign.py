# Generated by Django 4.2.1 on 2023-08-04 05:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0020_rename_original_content_content_components'),
    ]

    operations = [
        migrations.CreateModel(
            name='Campaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('campaign_name', models.Char<PERSON>ield(blank=True, max_length=255)),
                ('campaign_params', models.JSONField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('creator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('playbook', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.playbook')),
            ],
        ),
        migrations.AddField(
            model_name='content',
            name='campaign',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api.campaign'),
        ),
    ]
