# Generated by Django 4.2.1 on 2023-08-10 20:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0022_alter_content_campaign'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='content',
            name='campaign',
        ),
        migrations.CreateModel(
            name='ContentGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_group_name', models.CharField(blank=True, max_length=255)),
                ('content_group_params', models.J<PERSON>NField(blank=True, null=True)),
                ('components', models.JSONField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('campaign', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api.campaign')),
                ('creator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='content',
            name='content_group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='api.contentgroup'),
        ),
    ]
