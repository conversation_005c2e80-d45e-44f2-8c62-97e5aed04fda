# Generated by Django 4.2.1 on 2023-12-11 23:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0035_rename_target_group_targetinfo_target_info_group_and_more"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="assetinfo",
            constraint=models.UniqueConstraint(
                fields=("asset_info_group", "asset_key"),
                name="unique_asset_key_for_group",
            ),
        ),
        migrations.AddConstraint(
            model_name="assetinfogroup",
            constraint=models.UniqueConstraint(
                fields=("playbook", "asset_info_group_key"),
                name="unique_asset_info_group_key_for_playbook",
            ),
        ),
        migrations.AddConstraint(
            model_name="targetinfo",
            constraint=models.UniqueConstraint(
                fields=("target_info_group", "target_key"),
                name="unique_target_key_for_group",
            ),
        ),
        migrations.AddConstraint(
            model_name="targetinfogroup",
            constraint=models.UniqueConstraint(
                fields=("playbook", "target_info_group_key"),
                name="unique_target_info_group_key_for_playbook",
            ),
        ),
    ]
