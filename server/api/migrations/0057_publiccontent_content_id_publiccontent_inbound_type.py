# Generated by Django 4.2.1 on 2024-09-03 23:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0056_contenttemplate"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="publiccontent",
            name="content_id",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="publiccontent",
            name="inbound_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("outbound", "Outbound"),
                    ("partial_inbound", "Partial Inbound"),
                    ("full_inbound", "Full Inbound"),
                ],
                max_length=255,
                null=True,
            ),
        ),
    ]
