# Generated by Django 4.2.1 on 2024-11-21 00:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0061_targetinfogroup_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='tofuuser',
            name='credits_available',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='tofuuser',
            name='credits_last_updated',
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name='tofuuser',
            name='customer_type',
            field=models.CharField(choices=[('admin', 'Admin'), ('normal', 'Normal'), ('lite', 'Lite')], default='normal', max_length=150),
        ),
        migrations.CreateModel(
            name='UserCreditAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('credit_adjustment', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('payment_info', models.JSONField(blank=True, null=True)),
                ('additional_info', models.JSONField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
