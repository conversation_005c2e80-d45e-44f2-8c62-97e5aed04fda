# Generated by Django 4.2.1 on 2024-05-14 18:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0051_remove_offsiteeventlogs_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ChatHistory",
            fields=[
                (
                    "key",
                    models.CharField(max_length=255, primary_key=True, serialize=False),
                ),
                ("model", models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255)),
                ("json", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "creator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
