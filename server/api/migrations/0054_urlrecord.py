# Generated by Django 4.2.1 on 2024-06-12 21:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0053_alter_campaign_playbook"),
    ]

    operations = [
        migrations.CreateModel(
            name="UrlRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "source_url",
                    models.CharField(blank=True, max_length=2047, unique=True),
                ),
                (
                    "redirect_urls",
                    models.JSONField(blank=True, default=list, null=True),
                ),
                (
                    "crawling_status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("AF", "Always Fails"),
                            ("AS", "Always Succeeds"),
                            ("SS", "Succeeds Sometimes"),
                            ("NR", "No Record"),
                        ],
                        default="NR",
                        max_length=16,
                    ),
                ),
                ("errors", models.<PERSON><PERSON><PERSON>ield(blank=True, default=list, null=True)),
                ("docs", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, null=True)),
            ],
        ),
    ]
