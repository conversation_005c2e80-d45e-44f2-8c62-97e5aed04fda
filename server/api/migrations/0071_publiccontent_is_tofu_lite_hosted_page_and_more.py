# Generated by Django 4.2.1 on 2025-01-06 22:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0070_alter_autopilotrun_autopilot_action_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='publiccontent',
            name='is_tofu_lite_hosted_page',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='publiccontent',
            name='last_hosted_page_deduction_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='tofuuser',
            name='stripe_customer_id',
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
    ]
