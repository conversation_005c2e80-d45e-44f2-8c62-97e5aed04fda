# Generated by Django 4.2.1 on 2023-11-15 19:56

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0031_campaign_campaign_status_content_content_status_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CompanyInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("docs", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ("meta", models.<PERSON><PERSON><PERSON>ield(blank=True, null=True)),
                ("summary", models.TextField(blank=True, null=True)),
                ("index", models.JSONField(blank=True, null=True)),
                ("docs_last_build", models.J<PERSON>NField(blank=True, null=True)),
                ("docs_build_status", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ("updated_at", models.DateTime<PERSON>ield(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="AssetInfoGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("asset_info_group_key", models.CharField(max_length=255)),
                ("meta", models.JSONField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "playbook",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="asset_info_groups",
                        to="api.playbook",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="AssetInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("docs", models.JSONField(blank=True, null=True)),
                ("meta", models.JSONField(blank=True, null=True)),
                ("summary", models.TextField(blank=True, null=True)),
                ("index", models.JSONField(blank=True, null=True)),
                ("docs_last_build", models.JSONField(blank=True, null=True)),
                ("docs_build_status", models.JSONField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("asset_key", models.CharField(max_length=255)),
                (
                    "asset_info_group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assets",
                        to="api.assetinfogroup",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="playbook",
            name="company_object",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="api.companyinfo",
            ),
        ),
    ]
