# Generated by Django 4.2.1 on 2025-04-14 22:35

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0076_alter_autopilotrun_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="contenttemplate",
            name="content_types",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100),
                blank=True,
                default=list,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="contenttemplate",
            name="default_content_types",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100),
                blank=True,
                default=list,
                size=None,
            ),
        ),
    ]
