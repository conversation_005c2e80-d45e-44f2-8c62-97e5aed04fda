# Generated by Django 4.1.7 on 2023-03-24 03:08

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0011_alter_playbook_company_info_and_more"),
    ]

    operations = [
        migrations.RemoveField(model_name="content", name="generated_contents",),
        migrations.AddField(
            model_name="content",
            name="latest_variation_tag",
            field=models.CharField(blank=True, max_length=64, null=True),
        ),
        migrations.AddField(
            model_name="content",
            name="original_content",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="content",
            name="content_params",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="ContentVariation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("params", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ("variation", models.J<PERSON>NField(blank=True, null=True)),
                ("tag", models.CharField(blank=True, max_length=64, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "content",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.content"
                    ),
                ),
            ],
        ),
    ]
