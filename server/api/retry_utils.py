import logging
import time
from typing import Callable, TypeVar

T = TypeVar("T")


def retry_with_exponential_backoff(
    func: Callable[[], T],
    max_retries: int = 3,
    base_delay: float = 2.0,
    operation_name: str = "operation",
) -> T:
    """
    Retry a function with exponential backoff.

    Args:
        func: Function to retry (should take no arguments)
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds (will be multiplied by 2^attempt)
        operation_name: Name of the operation for logging

    Returns:
        Result of the function call

    Raises:
        Exception: The last exception encountered after all retries are exhausted
    """
    last_exception = None

    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            last_exception = e
            if attempt == max_retries - 1:
                logging.error(
                    f"Failed {operation_name} after {max_retries} attempts: {e}"
                )
                raise e

            delay = base_delay * (2**attempt)
            logging.warning(
                f"Attempt {attempt + 1} failed for {operation_name}, retrying in {delay} seconds: {e}"
            )
            time.sleep(delay)

    # This should never be reached, but just in case
    if last_exception:
        raise last_exception
    raise RuntimeError(f"Failed {operation_name} after all retry attempts")


def retry_operation(
    operation: Callable[[], T],
    max_retries: int = 3,
    base_delay: float = 2.0,
    operation_name: str = "operation",
    log_success: bool = True,
    success_message: str = None,
) -> T:
    """
    Retry an operation with exponential backoff and optional success logging.

    Args:
        operation: Function to retry
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds
        operation_name: Name of the operation for logging
        log_success: Whether to log successful operations
        success_message: Custom success message to log

    Returns:
        Result of the operation
    """
    result = retry_with_exponential_backoff(
        operation, max_retries, base_delay, operation_name
    )

    if log_success:
        message = success_message or f"Successfully completed {operation_name}"
        logging.info(message)

    return result
