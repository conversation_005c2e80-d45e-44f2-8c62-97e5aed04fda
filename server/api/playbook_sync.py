import logging
import traceback
from datetime import datetime, timedelta, timezone

from celery import shared_task
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist

from .models import (
    Campaign,
    Content,
    ContentGroup,
    Playbook,
    TargetInfo,
    TargetInfoGroup,
)
from .sync.e2e.hubspot_sync import PlaybookHubspotSyncer
from .sync.e2e.marketo_sync import PlaybookMarketoSyncer
from .sync.e2e.salesforce_sync import PlaybookSalesforceSyncer
from .thread_locals import force_set_current_playbook


@shared_task
def sync_list_task(playbook_id, target_info_group_id, campaign_ids, list_id, source):
    try:

        playbook = Playbook.objects.get(id=playbook_id)
        target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)
        campaigns = Campaign.objects.filter(id__in=campaign_ids)
        if not playbook or not target_info_group or not campaigns:
            logging.error(
                f"debug - playbook {playbook_id} or target_info_group {target_info_group_id} or campaigns {campaign_ids} not found, skipping"
            )
            return

        list_lock_key = f"autopilot:list_lock:target_info_group:{target_info_group_id}"
        if not cache.add(
            list_lock_key, True, timeout=3600 * 10
        ):  # 10 hour for max running time
            return

        target_info_group.meta["autopilot_last_synced_at"] = datetime.now(
            timezone.utc
        ).isoformat()
        target_info_group.save(update_fields=["meta"])

        if source == "hubspot":
            syncer = PlaybookHubspotSyncer(
                playbook, target_info_group, campaigns, list_id
            )
        elif source == "marketo":
            syncer = PlaybookMarketoSyncer(
                playbook, target_info_group, campaigns, list_id
            )
        elif source == "salesforce":
            syncer = PlaybookSalesforceSyncer(
                playbook, target_info_group, campaigns, list_id
            )
        else:
            cache.delete(list_lock_key)
            return

        syncer.sync()
        cache.delete(list_lock_key)
    except Exception as e:
        logging.exception(
            f"Error syncing {source} list {list_id} linked to {target_info_group} and used in {campaigns}: {e}"
        )
        cache.delete(list_lock_key)


class PlaybookSyncerManager:
    def __init__(self, playbook_instance) -> None:
        self._playbook_instance = playbook_instance
        force_set_current_playbook(playbook_instance)

    def _should_log_error(self, error_key, error_message):
        try:
            saved_error = cache.get(error_key)
        except Exception as e:
            logging.exception(f"debug: Failed to get autopilot error: {e}")
            saved_error = None
        if saved_error and saved_error["message"] == error_message:
            return False
        cache.set(
            error_key,
            {
                "message": error_message,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
            timeout=3600 * 24,  # 1 day
        )
        return True

    def get_lists_to_sync(self):
        # filter if the list is used in given campaign
        campaigns = Campaign.objects.filter(
            playbook=self._playbook_instance, campaign_params__enable_auto_sync=True
        )

        target_info_group_map = {}
        for campaign in campaigns:
            campaign_target_info_group_names = campaign.campaign_params.get(
                "allSelectedTargets", []
            )
            for target_info_group_name in campaign_target_info_group_names:
                if target_info_group_name not in target_info_group_map:
                    target_info_group_map[target_info_group_name] = []
                target_info_group_map[target_info_group_name].append(campaign)

        for target_info_group_key, campaign_list in target_info_group_map.items():
            try:
                target_info_group = TargetInfoGroup.objects.get(
                    target_info_group_key=target_info_group_key,
                    playbook=self._playbook_instance,
                )
                if not target_info_group:
                    logging.error(
                        f"target_info_group {target_info_group_key} not found"
                    )
                    continue
                duration = target_info_group.meta.get("autopilot_duration", 20)
                last_synced_at = target_info_group.meta.get(
                    "autopilot_last_synced_at", None
                )
                if not duration:
                    logging.error(
                        f"autopilot_duration is not valid in {target_info_group.meta}"
                    )
                    continue
                duration = int(duration)
                # check if current time is ready to sync
                if last_synced_at and datetime.fromisoformat(
                    last_synced_at
                ) + timedelta(minutes=duration) > datetime.now(timezone.utc):
                    continue

                # hubspot now has listId and marketo has id
                list_id = target_info_group.meta.get("importListSettings", {}).get(
                    "listId", ""
                ) or target_info_group.meta.get("importListSettings", {}).get("id", "")
                if not list_id:
                    logging.error(f"listId not found in {target_info_group.meta}")
                    continue
                source = target_info_group.meta.get("importListSettings", {}).get(
                    "syncFrom", ""
                )
                if not source:
                    logging.error(f"syncFrom not found in {target_info_group.meta}")
                    continue

                logging.info(
                    f"syncing {target_info_group} with {campaign_list} from {source}"
                )

                yield target_info_group, campaign_list, list_id, source
            except ObjectDoesNotExist as e:
                error_key = f"autopilot:error:{target_info_group_key}"
                error_message = (
                    f"target_info_group {target_info_group_key} not found: {e}"
                )
                if self._should_log_error(error_key, error_message):
                    logging.error(error_message)
                else:
                    logging.warning(error_message)
                continue
            except Exception as e:
                error_key = f"autopilot:error:{target_info_group_key}"
                error_message = (
                    f"Error syncing target_info_group {target_info_group_key}: {e}"
                )
                if self._should_log_error(error_key, error_message):
                    logging.error(f"{error_message}\n{traceback.format_exc()}")
                else:
                    logging.warning(error_message)
                continue

    def sync(self):
        logging.info(f"Checking syncs for playbook {self._playbook_instance}")
        for list_to_sync in self.get_lists_to_sync():
            target_info_group, campaigns, list_id, source = list_to_sync

            # Create campaign IDs list for serialization
            campaign_ids = [campaign.id for campaign in campaigns]

            # Dispatch celery task
            sync_list_task.delay(
                playbook_id=self._playbook_instance.id,
                target_info_group_id=target_info_group.id,
                campaign_ids=campaign_ids,
                list_id=list_id,
                source=source,
            )
