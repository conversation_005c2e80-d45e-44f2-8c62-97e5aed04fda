import copy
import logging
import uuid
from abc import ABC, abstractmethod
from typing import <PERSON>, Di<PERSON>, <PERSON><PERSON>

from django.db import transaction
from google.protobuf.json_format import ParseDict

from ..content_group import ContentGroupHandler
from ..models import Action
from ..playbook_build.doc_loader import get_template
from ..s3_utils import save_json_to_s3, validate_and_download_s3_json
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionExecutionParams,
    ActionStatus,
    ActionStatusType,
    TofuComponentType,
    TofuData,
    TofuDataList,
    TofuTemplate,
    TofuTemplateField,
)
from ..shared_types import ContentType
from ..utils import copy_s3_file
from .action_data_wrapper import ActionDataWrapper
from .tofu_data_wrapper import TofuDataListHandler


# TODO: refactor: move most of the logic here to ActionHandler
class ActionHandlerBase(ABC):
    """Handles execution and management of actions"""

    def __init__(self, action_instance: Action):
        self._action_data_wrapper = ActionDataWrapper(action_instance)
        self._updated_fields = []

    @property
    def action_instance(self):
        return self._action_data_wrapper.action_instance

    @property
    def action_definition(self):
        return self._action_data_wrapper.action_definition

    @property
    def content_groups(self):
        return self._action_data_wrapper.content_groups

    @property
    def content_type(self):
        return self.content_groups.first().content_group_params.get("content_type", "")

    # might be overridden
    @transaction.atomic
    def update_action_name(self, new_action_name):
        self._action_data_wrapper._action_instance.action_name = new_action_name
        self._action_data_wrapper._action_instance.save(update_fields=["action_name"])

    @transaction.atomic
    def update_inputs(self, inputs, partial_update=True, update_status=True):
        action_definition = self._action_data_wrapper.action_definition
        if not action_definition:
            raise ValueError(
                f"Action definition not found for action {self._action_data_wrapper._action_instance.id}"
            )

        inputs_converted = {}
        inputs_to_delete = []
        for key, value in inputs.items():
            input_definition = action_definition.required_inputs.get(
                key
            ) or action_definition.optional_inputs.get(key)
            if not input_definition:
                raise ValueError(f"Input definition not found for key: {key}")
            value_converted = value
            if value is None:
                inputs_to_delete.append(key)
                continue
            elif isinstance(value, dict):
                value_converted = TofuDataListHandler.parse_json_to_tofu_data(value)
            elif not isinstance(value, TofuDataList):
                raise ValueError(
                    f"Invalid input value type as not TofuDataList: {type(value)}: {value}"
                )

            # if not input_definition.is_user_editable:
            #     continue

            if not TofuDataListHandler.is_valid_tofu_data_definition(
                input_definition, value_converted
            ):
                raise ValueError(f"Invalid input value type: {type(value)}: {value}")

            if input_definition.is_saved_to_action:
                inputs_converted[key] = TofuDataListHandler.convert_tofu_data_to_json(
                    value_converted
                )
            if input_definition.need_special_handling:
                self._handle_special_input(key, value_converted)

        # Check that inputs to delete are not in predefined_inputs
        if inputs_to_delete and action_definition.predefined_inputs:
            predefined_input_keys = {
                predefined.value_type
                for predefined in action_definition.predefined_inputs
            }
            forbidden_deletes = [
                key for key in inputs_to_delete if key in predefined_input_keys
            ]
            if forbidden_deletes:
                raise ValueError(
                    f"Cannot delete predefined inputs: {forbidden_deletes}. "
                    f"These inputs are required by the action definition."
                )

        if inputs_to_delete and "template" in inputs_to_delete:
            self._updated_fields.append("meta")
            self._action_data_wrapper._action_instance.meta.pop(
                "applied_content_template_id", None
            )

        if inputs_converted or inputs_to_delete:
            self._updated_fields.append("inputs")
            if not partial_update:
                self._action_data_wrapper._action_instance.inputs = inputs_converted
            else:
                inputs = self._action_data_wrapper._action_instance.inputs or {}
                inputs.update(inputs_converted)
                self._action_data_wrapper._action_instance.inputs = inputs
            for key in inputs_to_delete:
                self._action_data_wrapper._action_instance.inputs.pop(key, None)

        if self._updated_fields:
            self._action_data_wrapper._action_instance.save(
                update_fields=list(set(self._updated_fields))
            )

    @transaction.atomic
    def update_targets(self):
        pass

    @abstractmethod
    def _handle_special_input(self, key, value):
        pass

    @property
    def action_definition(self):
        return self._action_data_wrapper.action_definition

    def validate(self) -> bool:
        return self._validate_inputs()

    def _validate_inputs(self) -> bool:
        return True

    @abstractmethod
    def _execute_impl(self, execution_params: ActionExecutionParams):
        raise NotImplementedError("Subclasses must implement _execute_impl()")

    @abstractmethod
    def _terminate_impl(self):
        raise NotImplementedError("Subclasses must implement _terminate_impl()")

    @abstractmethod
    def _get_post_ready_status_details(self) -> ActionStatus:
        raise NotImplementedError(
            "Subclasses must implement _get_post_ready_status_details()"
        )

    def _delete_special_handling(self):
        # by default we don't need any operation
        pass

    def _get_status_type_based_stats(
        self,
        total_succ,
        total_fail,
        total_running,
    ):
        if total_running > 0:
            return ActionStatusType.ACTION_STATUS_TYPE_RUNNING
        elif total_succ > 0:
            return ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        elif total_fail > 0:
            return ActionStatusType.ACTION_STATUS_TYPE_FAIL
        else:
            return ActionStatusType.ACTION_STATUS_TYPE_READY

    def get_should_overwrite(self):
        return False

    def _prepare_inputs_for_pull_inputs_on_edge(
        self, incoming_inputs, overwrite
    ) -> Tuple[Dict[str, Any], bool, bool]:
        """
        Prepare inputs for update based on overwrite flag.
        Args:
            incoming_inputs: Dictionary of input key-value pairs
            overwrite: Whether to overwrite existing inputs
        Returns:
            Tuple[Dict[str, Any], bool, bool]: Inputs to be updated, need_reset, need_execute
        """
        if overwrite:
            return copy.deepcopy(incoming_inputs), False, False

        # Get existing inputs
        all_existing_inputs = self._action_data_wrapper.get_all_user_inputs()
        # Return only the inputs that don't already exist
        return (
            {k: v for k, v in incoming_inputs.items() if k not in all_existing_inputs},
            False,
            False,
        )

    # ================ Execution ================
    def _is_executable(self) -> bool:
        """Check if the action is executable.
        Returns:
            bool: True if the action is executable, False otherwise
        """
        current_status = self._action_data_wrapper.get_action_status()
        # Skip execution for non-ready states
        if (
            current_status == ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
            or current_status == ActionStatusType.ACTION_STATUS_TYPE_UNSPECIFIED
            or current_status == ActionStatusType.ACTION_STATUS_TYPE_RUNNING
        ):
            # Future enhancement: terminate job or block from updating while running
            return False
        return True

    # TODO: move most of this to a template handler
    def convert_content_template_to_template(self, content_template):
        # what we need to do:
        # 0. make sure content_template is TofuTemplate
        try:
            content_template_data = ParseDict(content_template, TofuTemplate())
        except Exception as e:
            raise ValueError(f"Invalid content template type: {e}")

        # 0. get current custom_instructions, template and tone
        current_inputs = self._action_data_wrapper.get_all_user_inputs()
        current_instructions = current_inputs.get("custom_instructions", {})
        if not isinstance(current_instructions, TofuDataList):
            current_instructions = ParseDict(current_instructions, TofuDataList())

        current_template = current_inputs.get("template", None)
        if current_template is None:
            current_template_tofu_data = TofuDataList()
        elif not isinstance(current_template, TofuDataList):
            current_template_tofu_data = ParseDict(current_template, TofuDataList())
        else:
            current_template_tofu_data = current_template

        try:
            current_template_data = TofuDataListHandler.get_template(
                current_template_tofu_data
            )
        except Exception as e:
            current_template_data = TofuTemplate()

        inputs_to_update = {}

        # 1.0 move custom_instructions in content_template to a dedicated field
        template_custom_instructions = (
            content_template_data.template_custom_instructions
        )

        # 1.1. delete custom_instructions in content_template
        content_template_data.ClearField("template_custom_instructions")

        # 1.2. save custom_instructions
        # Merge existing custom_instructions with new ones from the template
        if template_custom_instructions:
            # Create a merged TofuDataList with all custom instructions
            merged_instructions = TofuDataList()
            # Add existing custom instructions
            for instruction in current_instructions.data:
                merged_instructions.data.append(instruction)
            # Add new custom instructions from the template
            for instruction in template_custom_instructions:
                # Create a TofuData wrapper for the instruction
                tofu_data = TofuData()
                tofu_data.custom_instruction.CopyFrom(instruction)
                merged_instructions.data.append(tofu_data)
            inputs_to_update["custom_instructions"] = merged_instructions
        else:
            # we don't need to update custom_instructions
            pass

        update_template = False
        update_tone = False

        # 2.0 check if current_format and incoming format
        if content_template_data.template_fields:
            # overwrite current format
            update_template = True

        # 3.0 check if current_tone and incoming tone
        if content_template_data.tone_reference_v2:
            # overwrite current tone
            update_tone = True

        # 4. merge template and tone
        if update_template or update_tone:
            # Create a new TofuTemplate message
            template_data = TofuData()
            template_data.template.CopyFrom(current_template_data)

            if update_template:
                # Clear the existing map first
                template_data.template.template_fields.clear()

                if (
                    False
                    and self.action_instance.action_category
                    == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_REPURPOSE)
                    and self._is_non_text_template(
                        content_template_data.template_fields
                    )
                ):
                    # convert to text for this case
                    try:
                        template_fields = self._convert_template_to_text(
                            content_template_data.template_fields
                        )
                    except Exception as e:
                        logging.exception(f"Error converting template to text: {e}")
                        # TODO: handle this gracefully
                        return {}
                    # Add each key-value pair individually to the map
                    for key, value in template_fields.items():
                        template_data.template.template_fields[key].CopyFrom(value)
                else:
                    # Add each key-value pair individually to the map
                    for key, value in content_template_data.template_fields.items():
                        template_data.template.template_fields[key].CopyFrom(value)

                        if (
                            self.action_instance.action_category
                            == ActionCategory.Name(
                                ActionCategory.ACTION_CATEGORY_PERSONALIZE
                            )
                            and value.content_source_format.lower() == "text"
                        ):
                            # we need to overwrite content_source_copy with slate_content_source_copy
                            template_data.template.template_fields[
                                key
                            ].content_source_copy = value.slate_content_source_copy

                # copy s3 file inside content_source_copy and slate_content_source_copy
                for key, value in template_data.template.template_fields.items():
                    if (
                        value.content_source_copy
                        and value.content_source_copy.startswith(
                            "/api/web/storage/s3-presigned-url"
                        )
                    ):
                        try:
                            # Copy the file using copy_s3_file
                            value.content_source_copy = copy_s3_file(
                                value.content_source_copy
                            )
                        except Exception as e:
                            logging.exception(
                                f"Error copying s3 file from content_source_copy: {value.content_source_copy}, will use original file: {e}"
                            )

                    if (
                        value.slate_content_source_copy
                        and value.slate_content_source_copy.startswith(
                            "/api/web/storage/s3-presigned-url"
                        )
                    ):
                        try:
                            # Copy the file using copy_s3_file
                            value.slate_content_source_copy = copy_s3_file(
                                value.slate_content_source_copy
                            )
                        except Exception as e:
                            logging.exception(
                                f"Error copying s3 file from slate_content_source_copy: {value.slate_content_source_copy}, will use original file: {e}"
                            )

            if update_tone:
                template_data.template.tone_reference_v2.CopyFrom(
                    content_template_data.tone_reference_v2
                )

            inputs_to_update["template"] = TofuDataList(data=[template_data])

        return inputs_to_update

    def _is_non_text_template(self, template_fields):
        for value in template_fields.values():
            if value.content_source_format.lower() != "text":
                return True
        return False

    def _convert_template_to_text(self, template_fields):
        if self._action_data_wrapper._get_str_content_type() in [
            ContentType.EmailMarketing,
            ContentType.EmailSDR,
        ]:
            subject_line_data = ""
            # Convert enum to string key
            subject_key = "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT"
            body_key = "TOFU_COMPONENT_TYPE_EMAIL_BODY"

            subject_content_source_copy = template_fields[
                subject_key
            ].content_source_copy
            try:
                subject_content_source_copy_data = validate_and_download_s3_json(
                    subject_content_source_copy
                )
                subject_line_data = subject_content_source_copy_data["subjectLine"]
            except Exception as e:
                raise ValueError(
                    f"Error downloading subject content source copy: {e}"
                ) from e

            body_data = ""
            try:
                body_content_source_copy = template_fields[body_key].content_source_copy
                body_content_source_copy_data = get_template(body_content_source_copy)
                body_data = body_content_source_copy_data["text"]
            except Exception as e:
                raise ValueError(
                    f"Error downloading body content source copy: {e}"
                ) from e

            email_template_data = {"subjectLine": subject_line_data, "body": body_data}

            email_template_data_slate_format_body_data_children = []
            for body_data_line in body_data.split("\n"):
                email_template_data_slate_format_body_data_children.append(
                    {"type": "paragraph", "children": [{"text": body_data_line}]}
                )
            email_template_data_slate_format = [
                {
                    "type": "subject-line",
                    "label": "Subject",
                    "children": [
                        {"type": "paragraph", "children": [{"text": subject_line_data}]}
                    ],
                },
                {
                    "type": "body",
                    "label": "Body",
                    "children": email_template_data_slate_format_body_data_children,
                },
            ]

            text_content_source_copy_file_name = f"{uuid.uuid4()}.json"
            text_slate_content_source_copy_file_name = f"{uuid.uuid4()}.json"

            if not save_json_to_s3(
                text_content_source_copy_file_name, email_template_data
            ):
                raise ValueError(
                    f"Error saving template to s3 for email template field text_content_source_copy: {text_content_source_copy_file_name}"
                )
            if not save_json_to_s3(
                text_slate_content_source_copy_file_name,
                email_template_data_slate_format,
            ):
                raise ValueError(
                    f"Error saving template to s3 for email template field text_slate_content_source_copy: {text_slate_content_source_copy_file_name}"
                )

            text_content_source_copy = f"/api/web/storage/s3-presigned-url?file={text_content_source_copy_file_name}&fileType=application/json&directory=tofu-uploaded-files"
            text_slate_content_source_copy = f"/api/web/storage/s3-presigned-url?file={text_slate_content_source_copy_file_name}&fileType=application/json&directory=tofu-uploaded-files"

            # Create a new TofuTemplateField for the text content
            text_template_field = TofuTemplateField()
            text_template_field.template_component_type = (
                TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
            )
            text_template_field.content_source = text_content_source_copy_file_name
            text_template_field.content_source_copy = text_content_source_copy
            text_template_field.slate_content_source = (
                text_slate_content_source_copy_file_name
            )
            text_template_field.content_source_format = "Text"
            text_template_field.slate_content_source_copy = (
                text_slate_content_source_copy
            )
            text_template_field.content_source_upload_method = "Text"

            # Create the template fields map with the new field
            template_fields = {"TOFU_COMPONENT_TYPE_UNSPECIFIED": text_template_field}
            return template_fields
        else:
            content_source_copy = template_fields[
                "TOFU_COMPONENT_TYPE_UNSPECIFIED"
            ].content_source_copy
            content_source_copy_data = get_template(content_source_copy)
            text_data = content_source_copy_data["text"]
            text_content_source_copy_data_children = []
            for text_data_line in text_data.split("\n"):
                text_content_source_copy_data_children.append(
                    {"type": "paragraph", "children": [{"text": text_data_line}]}
                )
            text_content_source_copy_data_slate_format = [
                {
                    "type": "text",
                    "label": "",
                    "children": text_content_source_copy_data_children,
                }
            ]
            text_content_source_copy_data = {"text": text_data}
            text_content_source_copy_file_name = f"{uuid.uuid4()}.json"
            text_content_source_copy_file_name_slate_format = f"{uuid.uuid4()}.json"
            if not save_json_to_s3(
                text_content_source_copy_file_name, text_content_source_copy_data
            ):
                raise ValueError(
                    f"Error saving template to s3 for field text_content_source_copy: {text_content_source_copy_file_name}"
                )
            if not save_json_to_s3(
                text_content_source_copy_file_name_slate_format,
                text_content_source_copy_data_slate_format,
            ):
                raise ValueError(
                    f"Error saving template to s3 for field text_content_source_copy_slate_format: {text_content_source_copy_file_name_slate_format}"
                )

            text_content_source_copy = f"/api/web/storage/s3-presigned-url?file={text_content_source_copy_file_name}&fileType=application/json&directory=tofu-uploaded-files"
            text_content_source_copy_slate_format = f"/api/web/storage/s3-presigned-url?file={text_content_source_copy_file_name_slate_format}&fileType=application/json&directory=tofu-uploaded-files"

            # Create a new TofuTemplateField for the text content
            text_template_field = TofuTemplateField()
            text_template_field.template_component_type = (
                TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
            )
            text_template_field.content_source = text_content_source_copy_file_name
            text_template_field.content_source_copy = text_content_source_copy
            text_template_field.slate_content_source = (
                text_content_source_copy_file_name_slate_format
            )
            text_template_field.content_source_format = "Text"
            text_template_field.slate_content_source_copy = (
                text_content_source_copy_slate_format
            )
            text_template_field.content_source_upload_method = "Text"

            # Create the template fields map with the new field
            template_fields = {"TOFU_COMPONENT_TYPE_UNSPECIFIED": text_template_field}
            return template_fields
