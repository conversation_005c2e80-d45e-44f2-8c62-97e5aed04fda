import logging
from typing import List, Optional, Union

from ..shared_definitions.protobuf.action_system_config import (
    ACTION_CATEGORY_TO_ACTION_TYPES,
    ACTION_SYSTEM_CONFIG,
    CATEGORY_INPUTS_NEEDED_FOR_DEFINITION,
)
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionDefinition,
    ActionType,
    PlatformType,
    TofuDataList,
)
from ..shared_types import ContentType
from .tofu_data_wrapper import TofuDataListHandler


class ActionSystemConfigLoader:
    def __init__(self):
        self._action_definitions = ACTION_SYSTEM_CONFIG.action_definitions
        self._all_eligible_actions = ACTION_SYSTEM_CONFIG.all_eligible_actions

    def get_definition(
        self, action_type: Union[str, ActionType]
    ) -> Optional[ActionDefinition]:
        str_action_type = ""
        if isinstance(action_type, str):
            str_action_type = action_type
        elif isinstance(action_type, (int, ActionType)):
            str_action_type = ActionType.Name(action_type)
        else:
            raise ValueError(f"Unsupported action type: {action_type}")
        action_definition = self._action_definitions.get(str_action_type, None)
        if not action_definition:
            raise ValueError(
                f"Action definition not found for action type: {str_action_type}"
            )
        return action_definition

    def get_definition_by_action_category_and_inputs(
        self, action_category: ActionCategory, inputs: dict[str, TofuDataList]
    ) -> Optional[ActionDefinition]:
        if isinstance(action_category, str):
            action_category = ActionCategory.Value(action_category)
        inputs_needed = CATEGORY_INPUTS_NEEDED_FOR_DEFINITION[action_category]
        converted_inputs = {}
        for key in inputs_needed:
            if key not in inputs:
                raise ValueError(f"Missing input: {key}")

            value = inputs[key]
            if isinstance(value, dict):
                converted_inputs[key] = TofuDataListHandler.parse_json_to_tofu_data(
                    value
                )
            elif isinstance(value, TofuDataList):
                converted_inputs[key] = value
            else:
                raise ValueError(
                    f"Invalid input type for {key}: {type(value)} with value: {value}"
                )

        content_type = None
        content_type_data = converted_inputs.get("content_type", None)
        if content_type_data:
            content_type = TofuDataListHandler.get_content_type(content_type_data)

        platform_type = None
        platform_type_data = converted_inputs.get("platform_type", None)
        if platform_type_data:
            platform_type = TofuDataListHandler.get_platform_type(platform_type_data)

        return self._get_definition_by_action_predefined_fields(
            action_category=action_category,
            content_type=content_type,
            platform_type=platform_type,
        )

    # TODO: we shall refactor this to be more systematic
    def _get_definition_by_action_predefined_fields(
        self,
        action_category: ActionCategory,
        content_type: ContentType = None,
        platform_type: PlatformType = None,
    ) -> Optional[ActionDefinition]:
        # convert if action_category is string
        if isinstance(action_category, str):
            action_category = ActionCategory.Value(action_category)
        action_type = ActionType.ACTION_TYPE_UNSPECIFIED
        if action_category == ActionCategory.ACTION_CATEGORY_USER_INPUT:
            action_type = ActionType.ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT
        elif action_category == ActionCategory.ACTION_CATEGORY_REPURPOSE:
            if content_type == ContentType.EmailSDR:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR
            elif content_type == ContentType.EmailMarketing:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_EMAIL_MARKETING
            elif content_type == ContentType.LandingPage:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_LANDING_PAGE
            elif content_type == ContentType.Whitepaper:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_WHITEPAPER
            elif content_type == ContentType.EBook:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_EBOOK
            elif content_type == ContentType.CaseStudy:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_CASE_STUDY
            elif content_type == ContentType.SalesDeck:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_SALES_DECK
            elif content_type == ContentType.SlideDeck:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_SLIDE_DECK
            elif content_type == ContentType.Webinar:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_WEBINAR
            elif content_type == ContentType.BlogPost:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_BLOG_POST
            elif content_type == ContentType.QuotesHighlights:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_QUOTES_HIGHLIGHTS
            elif content_type == ContentType.Statistics:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_STATISTICS
            elif content_type == ContentType.AdCampaignGeneral:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GENERAL
            elif content_type == ContentType.AdCampaignLinkedin:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN
            elif content_type == ContentType.AdCampaignLinkedinCarousel:
                action_type = (
                    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_CAROUSEL
                )
            elif content_type == ContentType.AdCampaignLinkedinDocument:
                action_type = (
                    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_DOCUMENT
                )
            elif content_type == ContentType.AdCampaignMeta:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_META
            elif content_type == ContentType.AdCampaignGoogle:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GOOGLE
            elif content_type == ContentType.SocialGeneral:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_GENERAL
            elif content_type == ContentType.SocialLinkedin:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_LINKEDIN
            elif content_type == ContentType.MessageLinkedin:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_MESSAGE_LINKEDIN
            elif content_type == ContentType.Other:
                action_type = ActionType.ACTION_TYPE_REPURPOSE_OTHER
            else:
                raise ValueError(
                    f"Unsupported content type '{content_type}' for repurpose action"
                )
        elif action_category == ActionCategory.ACTION_CATEGORY_PERSONALIZE:
            if content_type == ContentType.EmailSDR:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_SDR
            elif content_type == ContentType.EmailMarketing:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_MARKETING
            elif content_type == ContentType.LandingPage:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_LANDING_PAGE
            elif content_type == ContentType.Whitepaper:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_WHITEPAPER
            elif content_type == ContentType.EBook:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_EBOOK
            elif content_type == ContentType.AdCampaignGeneral:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GENERAL
            elif content_type == ContentType.AdCampaignLinkedin:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN
            elif content_type == ContentType.AdCampaignMeta:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_META
            elif content_type == ContentType.AdCampaignGoogle:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GOOGLE
            elif content_type == ContentType.AdCampaignLinkedinCarousel:
                action_type = (
                    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_CAROUSEL
                )
            elif content_type == ContentType.AdCampaignLinkedinDocument:
                action_type = (
                    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_DOCUMENT
                )
            elif content_type == ContentType.MessageLinkedin:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_MESSAGE_LINKEDIN
            elif content_type == ContentType.SalesDeck:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_SALES_DECK
            elif content_type == ContentType.SocialGeneral:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_GENERAL
            elif content_type == ContentType.SocialLinkedin:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_LINKEDIN
            elif content_type == ContentType.BlogPost:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_BLOG_POST
            elif content_type == ContentType.Other:
                action_type = ActionType.ACTION_TYPE_PERSONALIZE_OTHER
            else:
                raise ValueError(
                    f"Unsupported content type '{content_type}' for personalize action"
                )
        elif action_category == ActionCategory.ACTION_CATEGORY_EXPORT:
            if platform_type == PlatformType.PLATFORM_TYPE_HUBSPOT:
                if content_type in [ContentType.EmailSDR, ContentType.EmailMarketing]:
                    action_type = ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL
                elif content_type == ContentType.LandingPage:
                    action_type = ActionType.ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE
                else:
                    raise ValueError(
                        f"Unsupported content type '{content_type}' for HubSpot export"
                    )
            elif platform_type == PlatformType.PLATFORM_TYPE_MARKETO:
                if content_type in [ContentType.EmailSDR, ContentType.EmailMarketing]:
                    action_type = ActionType.ACTION_TYPE_EXPORT_MARKETO_EMAIL
                elif content_type == ContentType.LandingPage:
                    action_type = ActionType.ACTION_TYPE_EXPORT_MARKETO_LANDING_PAGE
                else:
                    raise ValueError(
                        f"Unsupported content type '{content_type}' for platform type: {platform_type}"
                    )
            elif platform_type == PlatformType.PLATFORM_TYPE_SALESFORCE:
                if content_type in [ContentType.EmailSDR, ContentType.EmailMarketing]:
                    action_type = ActionType.ACTION_TYPE_EXPORT_SALESFORCE_EMAIL
                else:
                    raise ValueError(
                        f"Unsupported content type '{content_type}' for platform type: {platform_type}"
                    )
            elif platform_type == PlatformType.PLATFORM_TYPE_TOFU:
                if content_type == ContentType.LandingPage:
                    action_type = ActionType.ACTION_TYPE_EXPORT_TOFU_LANDING_PAGE
                else:
                    raise ValueError(
                        f"Unsupported content type '{content_type}' for platform type: {platform_type}"
                    )
            else:
                raise ValueError(
                    f"Unsupported platform type '{platform_type}' for export action"
                )
        elif action_category == ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE:
            if content_type == ContentType.EmailSDR:
                action_type = ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_SDR
            elif content_type == ContentType.EmailMarketing:
                action_type = (
                    ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_MARKETING
                )
            elif content_type == ContentType.MessageLinkedin:
                action_type = (
                    ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_LINKEDIN_MESSAGE
                )
            else:
                raise ValueError(
                    f"Unsupported content type '{content_type}' for sequence personalize template action"
                )
        else:
            raise ValueError(f"Unsupported action category '{action_category}'")

        return self.get_definition(action_type)

    def get_all_eligible_actions(self):
        return self._all_eligible_actions

    def get_all_action_types_str_in_order(self) -> List[str]:
        """Get ordered list of all action types."""
        action_types = []
        for action_category in ACTION_CATEGORY_TO_ACTION_TYPES:
            action_types.extend(ACTION_CATEGORY_TO_ACTION_TYPES[action_category])
        return [ActionType.Name(action_type) for action_type in action_types]
