import heapq
import logging
from collections import defaultdict, deque

from django.forms.models import model_to_dict

from ..actions.tofu_data_wrapper import TofuDataListHandler
from ..models import Action, ActionEdge, Campaign, ContentGroup
from ..paragon_wrapper import ParagonWrapper
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    PlatformType,
    TofuDataList,
)
from ..utils import copy_s3_file, parse_s3_presigned_url
from .action_creator import ActionCreator
from .action_data_wrapper import ActionDataWrapper


class ActionCopier:
    def __init__(self):
        self.export_integrations_enabled = {}
        self.paragon_integrations = None

    # @transaction.atomic
    def copy_actions_for_campaign(
        self,
        source_campaign: Campaign,
        target_campaign: Campaign,
        pop_assets=False,
        pop_template=False,
        copy_components=False,
        actions_to_copy=None,
    ):
        self._source_campaign = source_campaign
        self._target_campaign = target_campaign
        self._playbook = target_campaign.playbook
        self._action_mapping = {}
        ordered_actions = self._get_ordered_actions(actions_to_copy)
        for source_action_id in ordered_actions:
            source_action = Action.objects.get(id=source_action_id)
            if ActionCategory.Value(
                source_action.action_category
            ) == ActionCategory.ACTION_CATEGORY_EXPORT and not self._has_export_integration_enabled(
                source_action
            ):
                continue
            new_action = self._copy_action(source_action, pop_assets, pop_template)
            self._action_mapping[source_action.id] = (source_action, new_action)

        if copy_components:
            for source_action, new_action in self._action_mapping.values():
                source_content_groups = ContentGroup.objects.filter(
                    action=source_action
                )
                new_action_content_groups = ContentGroup.objects.filter(
                    action=new_action
                )
                for source_content_group in source_content_groups:
                    action_index = source_content_group.content_group_params.get(
                        "action_index", None
                    )
                    if action_index is None:
                        if len(source_content_groups) > 1:
                            raise Exception(
                                f"Action index is not set for content group {source_content_group.id}"
                            )
                        else:
                            new_content_group = new_action_content_groups.first()
                            new_content_group.content_group_params["action_index"] = 0
                    else:
                        new_content_group = new_action_content_groups.filter(
                            content_group_params__action_index=action_index
                        ).first()
                    new_content_group.components = source_content_group.components
                    new_content_group.save()

    def _copy_action(self, source_action: Action, pop_assets=False, pop_template=False):
        source_action_data_dict = model_to_dict(source_action)

        fields_to_remove = [
            "id",
            "created_at",
            "updated_at",
            "incoming_edges",
            "incoming_actions",
            "inputs",
            "outputs",
            "status",
        ]
        for field in fields_to_remove:
            source_action_data_dict.pop(field, None)
        source_action_data_dict["campaign"] = self._target_campaign
        source_action_data_dict["playbook"] = self._target_campaign.playbook
        source_action_data_dict["inputs"] = ActionDataWrapper(
            source_action
        ).get_all_user_inputs()
        incoming_actions_data = []
        source_incoming_actions = ActionEdge.objects.filter(to_action=source_action)
        for source_incoming_action in source_incoming_actions:
            incoming_actions_data.append(
                {
                    "from_action": self._action_mapping[
                        source_incoming_action.from_action.id
                    ][1].id,
                    "config": source_incoming_action.config,
                }
            )
        source_action_data_dict["incoming_edges"] = incoming_actions_data

        if pop_assets:
            if (
                "inputs" in source_action_data_dict
                and "assets" in source_action_data_dict["inputs"]
            ):
                del source_action_data_dict["inputs"]["assets"]
            if (
                "inputs" in source_action_data_dict
                and "anchor_content" in source_action_data_dict["inputs"]
            ):
                del source_action_data_dict["inputs"]["anchor_content"]
            if (
                "outputs" in source_action_data_dict
                and "assets" in source_action_data_dict["outputs"]
            ):
                del source_action_data_dict["outputs"]["assets"]
        if pop_template:
            if (
                "inputs" in source_action_data_dict
                and "template" in source_action_data_dict["inputs"]
            ):
                del source_action_data_dict["inputs"]["template"]
        # we'd also pop "targets"
        if "targets" in source_action_data_dict:
            del source_action_data_dict["targets"]
        # If it's a SEQ_PERSONALIZE_TEMPLATE action, we should also pop "targets" from inputs
        if source_action.action_category == ActionCategory.Name(
            ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE
        ) or source_action.action_category == ActionCategory.Name(
            ActionCategory.ACTION_CATEGORY_PERSONALIZE
        ):
            if (
                "inputs" in source_action_data_dict
                and "targets" in source_action_data_dict["inputs"]
            ):
                del source_action_data_dict["inputs"]["targets"]

        # special handling for "content_source_copy" in inputs["template"]: we need to use copy_s3_file.
        if "template" in source_action_data_dict["inputs"]:
            # get the tofu data list for the template.
            template_data = source_action_data_dict["inputs"]["template"]
            if not isinstance(template_data, TofuDataList):
                raise ValueError(f"Invalid tofu data list type for template")
            tofu_template = TofuDataListHandler.get_template(template_data)
            for field in tofu_template.template_fields.values():
                if field.content_source_copy:
                    try:
                        parse_s3_presigned_url(field.content_source_copy)
                        field.content_source_copy = copy_s3_file(
                            field.content_source_copy
                        )
                    except Exception as e:
                        pass

            # save the tofu data list back to the template data. It should be a TofuDataList.
            source_action_data_dict["inputs"]["template"] = (
                TofuDataListHandler.convert_template_to_tofu_data(tofu_template)
            )
        new_action = ActionCreator(
            self._target_campaign.creator, source_action_data_dict
        ).create()
        return new_action

    def _get_ordered_actions(self, actions_to_copy=None):
        campaign = self._source_campaign
        # Create a graph representation
        graph = defaultdict(list)
        in_degree = defaultdict(int)
        reverse_graph = defaultdict(list)  # To track dependencies

        # Populate the graph and in-degree count
        for edge in ActionEdge.objects.filter(to_action__campaign=campaign):
            graph[edge.from_action.id].append(edge.to_action.id)
            reverse_graph[edge.to_action.id].append(edge.from_action.id)
            in_degree[edge.to_action.id] += 1

        # If actions_to_copy is provided, we need to find all dependencies
        all_required_actions = None
        if actions_to_copy is not None:
            # Convert actions_to_copy to a set of IDs if they aren't already
            actions_to_copy_ids = {
                action.id if hasattr(action, "id") else action
                for action in actions_to_copy
            }

            # Find all dependencies using BFS
            all_required_actions = set(actions_to_copy_ids)
            queue = deque(actions_to_copy_ids)
            while queue:
                action_id = queue.popleft()
                for dependency in reverse_graph[action_id]:
                    if dependency not in all_required_actions:
                        all_required_actions.add(dependency)
                        queue.append(dependency)

            # Filter the graph to only include required actions
            filtered_graph = defaultdict(list)
            filtered_in_degree = defaultdict(int)
            for action_id in all_required_actions:
                for neighbor in graph[action_id]:
                    if neighbor in all_required_actions:
                        filtered_graph[action_id].append(neighbor)
                        filtered_in_degree[neighbor] += 1

            graph = filtered_graph
            in_degree = filtered_in_degree

        # Initialize a priority queue with actions having no incoming edges
        pq = [
            action.id
            for action in Action.objects.filter(campaign=campaign)
            if in_degree[action.id] == 0
            and (actions_to_copy is None or action.id in all_required_actions)
        ]
        heapq.heapify(pq)

        ordered_actions = []

        while pq:
            action_id = heapq.heappop(pq)
            ordered_actions.append(action_id)

            # Decrease the in-degree of neighboring actions
            for neighbor in graph[action_id]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    heapq.heappush(pq, neighbor)

        # Check if there was a cycle
        if actions_to_copy is None:
            if len(ordered_actions) != Action.objects.filter(campaign=campaign).count():
                raise ValueError("The graph has at least one cycle")
        else:
            if len(ordered_actions) != len(all_required_actions):
                raise ValueError("The graph has at least one cycle")

        return ordered_actions

    def _has_export_integration_enabled(self, action: Action):
        platform_type_map = {
            PlatformType.PLATFORM_TYPE_HUBSPOT: "hubspot",
            PlatformType.PLATFORM_TYPE_MARKETO: "marketo",
            PlatformType.PLATFORM_TYPE_SALESFORCE: "salesforce",
            PlatformType.PLATFORM_TYPE_LINKEDIN: "linkedin",
            PlatformType.PLATFORM_TYPE_TOFU: "tofu",
        }
        platform_type_data = ActionDataWrapper(action).get_input_by_name(
            "platform_type"
        )
        platform_type = TofuDataListHandler.get_platform_type(platform_type_data)
        platform_type_str = platform_type_map[platform_type]
        if platform_type_str == "tofu":
            return True
        if platform_type_str not in self.export_integrations_enabled:
            paragon_wrapper = ParagonWrapper(self._playbook)
            try:
                if not self.paragon_integrations:
                    self.paragon_integrations = paragon_wrapper.get_integrations()
                integrated_platforms = [
                    key
                    for key in self.paragon_integrations
                    if self.paragon_integrations[key].get("allCredentials")
                ]
            except Exception as e:
                logging.error(f"Error retrieving user integrations: {e}")
                # Default to False if we can't determine integration status
                self.export_integrations_enabled[platform_type_str] = False
                return False
            self.export_integrations_enabled[platform_type_str] = (
                platform_type_str in integrated_platforms
            )

        return self.export_integrations_enabled[platform_type_str]
