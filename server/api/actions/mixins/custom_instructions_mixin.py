import copy
import logging
from typing import List

from django.db import transaction

from ...models import ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import TofuDataList
from ..legacy_converter.legacy_custom_instruction_converter import (
    convert_custom_instructions_v3_to_v2,
)


class CustomInstructionsMixin:
    """
    Mixin for action handlers that need to update custom instructions.
    Provides common functionality for validating and updating custom instructions
    across different action handler types.
    """

    def _update_custom_instructions(self, custom_instructions):
        """
        Updates the custom instructions for content groups.

        Args:
            custom_instructions: The custom instructions to update (TofuDataList)

        Raises:
            ValueError: If custom_instructions is not a TofuDataList

        Returns:
            bool: True if content group was updated, False if no changes were made
        """
        if not isinstance(custom_instructions, TofuDataList):
            raise ValueError(
                f"Invalid custom instructions type: {type(custom_instructions)}"
            )

        # Convert custom instructions from v3 to v2 format
        custom_instructions_v2_data = convert_custom_instructions_v3_to_v2(
            custom_instructions
        )

        content_group_params_to_update = []

        # Determine if we're dealing with a collection
        is_collection = len(self.content_groups) > 1

        for content_group in self.content_groups:
            is_updated = self._apply_custom_instructions_to_content_group(
                content_group, custom_instructions_v2_data, is_collection
            )
            if is_updated:
                content_group_params_to_update.append(content_group)

        if content_group_params_to_update:
            ContentGroup.objects.bulk_update(
                content_group_params_to_update, ["content_group_params"]
            )
        return bool(content_group_params_to_update)

    def _apply_custom_instructions_to_content_group(
        self,
        content_group: ContentGroup,
        custom_instructions_data: dict,
        is_collection: bool = False,
    ):
        """
        Apply custom instructions to a content group based on whether it's a collection.
        Override this method in subclasses for custom application logic.

        Args:
            content_group: The content group to update
            custom_instructions_data: The prepared custom instructions data
            is_collection: Whether we're dealing with a collection

        Returns:
            bool: True if content group was updated, False if no changes were made
        """
        if is_collection:
            # Collection case - update within content_collection
            content_collection = content_group.content_group_params.get(
                "content_collection", {}
            )
            existing_instructions = content_collection.get("custom_instructions", {})
            if existing_instructions == custom_instructions_data:
                return False

            content_collection["custom_instructions"] = copy.deepcopy(
                custom_instructions_data
            )
            content_group.content_group_params["content_collection"] = (
                content_collection
            )
        else:
            # Non-collection case - update directly
            existing_instructions = content_group.content_group_params.get(
                "custom_instructions", {}
            )
            if existing_instructions == custom_instructions_data:
                return False

            content_group.content_group_params["custom_instructions"] = copy.deepcopy(
                custom_instructions_data
            )
        return True
