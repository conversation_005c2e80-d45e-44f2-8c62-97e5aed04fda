import copy
import logging
import time
from typing import Any, Dict, <PERSON><PERSON>

from django.core.cache import cache
from google.protobuf.json_format import MessageToDict, ParseDict

from ..evaluator.evaluators.pregen_evaluators.anchor_content_precheck import (
    AnchorContentPrecheckEvaluator,
)
from ..models import AssetInfo
from ..playbook_build.object_builder import ObjectBuilder
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionExecutionParams,
    ActionStatus,
    ActionStatusDetails,
    ActionStatusDetailsAnchorPrecheck,
    ActionStatusStats,
    ActionStatusType,
    AnchorPrecheckDocResult,
    AnchorPrecheckResult,
    AnchorPrecheckResultLabel,
)
from ..thread_locals import get_current_playbook, set_current_playbook
from .action_handler_base import ActionHandlerBase
from .tofu_data_wrapper import TofuDataListHandler

TRANSCRIPTION_WAITING_MSG = "wait for the transcription to finish"


def has_transcription_waiting(result):
    """
    Check if any assets in the result are waiting for transcription.

    Args:
        result (dict): Dictionary mapping asset IDs to precheck results

    Returns:
        bool: True if any asset is waiting for transcription, False otherwise
    """
    for _, precheck_result in result.items():
        if "label" in precheck_result and precheck_result["label"] == "FAIL":
            if (
                "comment" in precheck_result
                and precheck_result["comment"]
                and TRANSCRIPTION_WAITING_MSG in precheck_result["comment"]
            ):
                return True
        elif "label" not in precheck_result:
            # check individual doc case.
            for _, value in precheck_result.items():
                if "label" in value and value["label"] == "FAIL":
                    if (
                        "comment" in value
                        and TRANSCRIPTION_WAITING_MSG in value["comment"]
                    ):
                        return True
    return False


class UserInputActionHandler(ActionHandlerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._evaluation_task = None
        self._lock_cache_key = f"user_input_action_handler_lock_{self._action_data_wrapper.action_instance.id}"

    def _wait_on_lock(self):
        # wait for the lock to be released
        while cache.get(self._lock_cache_key):
            time.sleep(1)

    def _set_lock(self):
        cache.set(self._lock_cache_key, True, 60 * 2)

    def _release_lock(self):
        cache.delete(self._lock_cache_key)

    @classmethod
    def from_action_id(cls, action_id):
        # Factory method to create handler from action_id
        from ..models import Action  # Import here to avoid circular import

        action_instance = Action.objects.get(id=action_id)
        return cls(action_instance)

    def _handle_special_input(self, key, value):
        if key == "assets":
            self._update_assets(value)
        else:
            raise ValueError(f"Invalid input key: {key}")

    def _update_assets(self, assets):
        self._action_data_wrapper._action_instance.outputs["assets"] = (
            TofuDataListHandler.convert_tofu_data_to_json(assets)
        )
        self._action_data_wrapper._action_instance.save(update_fields=["outputs"])
        self._updated_fields.append("outputs")

    def _execute_impl(self, execution_params: ActionExecutionParams):
        self._wait_on_lock()
        try:
            self._set_lock()
            inputs = self._action_data_wrapper.get_all_user_inputs()
            # collect asset ids
            if "assets" not in inputs:
                self.mark_status_complete()
                return

            # Set playbook id
            playbook = self._action_data_wrapper._action_instance.playbook
            if not get_current_playbook() and playbook:
                set_current_playbook(playbook)

            # mark the status
            self.mark_status_running()
            asset_ids, asset_group_ids = TofuDataListHandler.get_assets(
                inputs["assets"]
            )

            evaluator = AnchorContentPrecheckEvaluator()
            evaluator.set_assets_anchor_precheck_status(
                asset_ids, asset_group_ids, "QUEUED"
            )
            all_assets_in_group = AssetInfo.objects.filter(
                asset_info_group__in=asset_group_ids
            )
            all_direct_assets = AssetInfo.objects.filter(id__in=asset_ids)
            for asset in all_direct_assets:
                builder = ObjectBuilder.get_builder(asset)
                builder.build_docs(rebuild=False, check_and_rebuild=False)
            for asset in all_assets_in_group:
                builder = ObjectBuilder.get_builder(asset)
                builder.build_docs(rebuild=False, check_and_rebuild=False)

            result = evaluator.evaluate_asset_ids(asset_ids, asset_group_ids)

            # Get the action instance from the cache
            if result:
                if has_transcription_waiting(result):
                    self.mark_status_running(
                        "The transcription for some of the assets is still being processed. You'll need to wait for the transcription to finish to use it. You can check the transcription status in playbook or refresh this page."
                    )
                else:
                    self.mark_status_result(result)
            else:
                self.mark_status_complete()
        finally:
            self._release_lock()

    def _terminate_impl(self):
        # no sub jobs to terminate
        self.mark_status_ready()

    def _get_post_ready_status_details(self):
        inputs = self._action_data_wrapper.get_all_user_inputs()
        # get the asset_ids
        if "assets" not in inputs:
            return ActionStatus(
                status_type=ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT,
                message="Waiting for assets input",
            )
        asset_ids, asset_group_ids = TofuDataListHandler.get_assets(inputs["assets"])
        evaluator = AnchorContentPrecheckEvaluator()
        status = evaluator.get_eval_status(asset_ids, asset_group_ids)
        if status == "READY":
            return ActionStatus(
                status_type=ActionStatusType.ACTION_STATUS_TYPE_READY,
                message="Action is ready",
            )
        elif status == "RUNNING":
            return ActionStatus(
                status_type=ActionStatusType.ACTION_STATUS_TYPE_RUNNING,
                message="Action is running",
            )
        else:
            # compile the result from the individual assets.
            try:
                result = evaluator.get_eval_results(asset_ids, asset_group_ids)
                return self._resolve_status(result)
            except ValueError as e:
                logging.warning(
                    f"Failed to get evaluation results or resolve status for action {self._action_data_wrapper.action_instance.id}: {str(e)}"
                )
                # Return READY status as a safe fallback when evaluation fails
                return ActionStatus(
                    status_type=ActionStatusType.ACTION_STATUS_TYPE_READY,
                    message="Action is ready (evaluation status unavailable)",
                )

    def mark_status_complete(self):
        action_status = ActionStatus(
            status_type=ActionStatusType.ACTION_STATUS_TYPE_COMPLETE,
            message="Action is complete",
        )
        self._action_data_wrapper.action_instance.status = MessageToDict(
            action_status, preserving_proto_field_name=True
        )
        self._action_data_wrapper.action_instance.save(update_fields=["status"])

    def mark_status_ready(self):
        self._action_data_wrapper.action_instance.status = MessageToDict(
            ActionStatus(
                status_type=ActionStatusType.ACTION_STATUS_TYPE_READY,
                message="Action is ready",
            ),
            preserving_proto_field_name=True,
        )
        self._action_data_wrapper.action_instance.save(update_fields=["status"])

    def mark_status_running(self, message=None):
        if message is None:
            message = "Action is running"
        self._action_data_wrapper.action_instance.status = MessageToDict(
            ActionStatus(
                status_type=ActionStatusType.ACTION_STATUS_TYPE_RUNNING,
                message=message,
            ),
            preserving_proto_field_name=True,
        )
        self._action_data_wrapper.action_instance.save(update_fields=["status"])

    def _resolve_status(self, result):
        if not result:
            return ActionStatus(
                status_type=ActionStatusType.ACTION_STATUS_TYPE_COMPLETE,
                message="Action is complete",
            )

        # result is dict of asset_id to precheck result
        # precheck result is dict of label and comment
        asset_precheck_results = {}
        cnts_succ = 0
        cnts_fail = 0
        for asset_id, precheck_result in result.items():
            if "label" not in precheck_result:  # multiple doc case
                anchor_precheck_doc_results = {}
                label = AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS
                message = ""
                for doc_key, value in precheck_result.items():
                    # check that "label" and "comment" are in the value
                    if "label" in value and "comment" in value:
                        if value["label"] == "PASS":
                            doc_label = (
                                AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS
                            )
                        else:
                            doc_label = (
                                AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL
                            )
                            label = (
                                AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL
                            )
                            message = "We failed to process some contents you have uploaded to this asset. Please correct the failed ones or reach out to Tofu team for help."
                        doc_comment = value["comment"]
                        anchor_precheck_doc_results[doc_key] = AnchorPrecheckDocResult(
                            label=doc_label, message=doc_comment
                        )
                    else:
                        raise Exception(
                            "Expected label and comment in precheck result, got: "
                            + str(precheck_result)
                        )
                if label == AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS:
                    cnts_succ += 1
                else:
                    cnts_fail += 1
                anchor_precheck_result = AnchorPrecheckResult(
                    label=label,
                    message=message,
                    anchor_precheck_doc_results=anchor_precheck_doc_results,
                )
                asset_precheck_results[int(asset_id)] = anchor_precheck_result
            else:
                if "label" in precheck_result and "comment" in precheck_result:
                    if precheck_result["label"] == "PASS":
                        label = (
                            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS
                        )
                        cnts_succ += 1
                    else:
                        label = (
                            AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_FAIL
                        )
                        cnts_fail += 1
                    anchor_precheck_result = AnchorPrecheckResult(
                        label=label, message=precheck_result["comment"]
                    )
                    asset_precheck_results[int(asset_id)] = anchor_precheck_result
                else:
                    raise Exception(
                        "Expected label and comment in precheck result, got: "
                        + str(precheck_result)
                    )

        # Modified to always mark as FAIL if any failures are detected
        status_type = self._get_status_type_based_stats(
            total_succ=cnts_succ, total_fail=cnts_fail, total_running=0
        )
        message = (
            "Some documents failed validation. Please fix all issues to proceed."
            if cnts_fail > 0
            else "Action is complete"
        )

        # Create ActionStatusDetailsAnchorPrecheck instance properly
        anchor_precheck_details = ActionStatusDetailsAnchorPrecheck()
        # Add each asset_precheck_result to the map field
        for asset_id, precheck_result in asset_precheck_results.items():
            anchor_precheck_details.asset_precheck_results[asset_id].CopyFrom(
                precheck_result
            )

        return ActionStatus(
            status_type=status_type,
            message=message,
            details=ActionStatusDetails(
                stats=ActionStatusStats(
                    cnts_succ=cnts_succ,
                    cnts_fail=cnts_fail,
                ),
                anchor_precheck_details=anchor_precheck_details,
            ),
        )

    def mark_status_result(self, result):
        if not result:
            self.mark_status_complete()
            return

        logging.info(f"Result: {result}")
        action_status = self._resolve_status(result)
        self._action_data_wrapper.action_instance.status = MessageToDict(
            action_status, preserving_proto_field_name=True
        )
        self._action_data_wrapper.action_instance.save(update_fields=["status"])

    def get_should_overwrite(self):
        return True

    def _get_status_type_based_stats(
        self,
        total_succ,
        total_fail,
        total_running,
    ):
        # NOTE: we return FAIL if there are any failures, not as other types of actions
        if total_running > 0:
            return ActionStatusType.ACTION_STATUS_TYPE_RUNNING
        elif total_fail > 0:
            return ActionStatusType.ACTION_STATUS_TYPE_FAIL
        elif total_succ > 0:
            return ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        else:
            return ActionStatusType.ACTION_STATUS_TYPE_READY

    def _prepare_inputs_for_pull_inputs_on_edge(
        self, incoming_inputs, overwrite
    ) -> Tuple[Dict[str, Any], bool, bool]:
        """
        Prepare inputs for update based on overwrite flag.
        Args:
            incoming_inputs: Dictionary of input key-value pairs
            overwrite: Whether to overwrite existing inputs
        Returns:
            Tuple[Dict[str, Any], bool, bool]: Inputs to be updated, need_reset, need_execute
        """
        # available_input_keys
        available_input_keys = ["assets"]
        if not set(incoming_inputs.keys()).issubset(available_input_keys):
            raise ValueError(
                f"Invalid input keys for UserInputActionHandler: {set(incoming_inputs.keys()) - set(available_input_keys)}"
            )

        need_reset = False
        need_execute = "assets" in incoming_inputs

        if overwrite:
            return copy.deepcopy(incoming_inputs), need_reset, need_execute
        else:
            # Get existing inputs
            all_existing_inputs = self._action_data_wrapper.get_all_user_inputs()
            # Return only the inputs that don't already exist
            return (
                {
                    k: v
                    for k, v in incoming_inputs.items()
                    if k not in all_existing_inputs
                },
                need_reset,
                need_execute,
            )
