import copy
import logging
import os
from typing import Any, Dict, <PERSON><PERSON>

from celery import shared_task
from django.db import transaction

from ..campaign_gen_wrapper import CampaignGenWrapper
from ..content_group import ContentGroupHandler
from ..gen_status import GenStatusUpdater
from ..models import TargetInfo
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionExecutionParams,
    ActionStatus,
    ActionStatusDetails,
    ActionStatusDetailsGenPersonalize,
    ActionStatusType,
    GenerationStatus,
    GenerationStatusType,
    TofuComponentMetaType,
    TofuDataList,
    TofuTemplate,
)
from .action_handler_base import ActionHandlerBase
from .legacy_converter.legacy_components_converter import convert_components_v3_to_v2
from .legacy_converter.legacy_custom_instruction_converter import (
    convert_custom_instructions_v3_to_v2,
)
from .legacy_converter.legacy_template_converter import convert_template_v3_to_v2
from .mixins import CustomInstructionsMixin
from .repurpose_to_p13n import copy_template_file
from .tofu_data_wrapper import TofuDataList<PERSON>andler


# Add a Celery task at module level
@shared_task
def bulk_create_content_task(content_group_ids):
    """Celery task to bulk create content for content groups."""
    from ..models import ContentGroup  # Import here to avoid circular imports

    results = []
    for cg_id in content_group_ids:
        try:
            content_group = ContentGroup.objects.get(id=cg_id)
            result = ContentGroupHandler(content_group).bulk_create_content()
            results.append(
                {
                    "content_group_id": cg_id,
                    "success": True,
                    "result": f"Created {len(result)} contents",
                }
            )
        except Exception as e:
            logging.exception(
                f"Failed to create content for content group {cg_id}: {e}"
            )
            results.append(
                {"content_group_id": cg_id, "success": False, "error": str(e)}
            )

    return results


class PersonalizationActionHandler(ActionHandlerBase, CustomInstructionsMixin):
    """Handles execution of personalization actions"""

    # Update: action name
    @transaction.atomic
    def update_action_name(self, new_action_name):
        for content_group in self.content_groups:
            content_group.content_group_name = new_action_name
            content_group.save(update_fields=["content_group_name"])
        self._action_data_wrapper._action_instance.action_name = new_action_name
        self._action_data_wrapper._action_instance.save(update_fields=["action_name"])

    # Update: input handling
    def _handle_special_input(self, key, value):
        if key == "components":
            self._update_components(value)
        elif key == "template":
            self._update_template(value)
        elif key == "custom_instructions":
            self._update_custom_instructions(value)
        else:
            raise ValueError(f"Invalid input key: {key}")

    def _check_input_fulfilled(self, key, value):
        try:
            if key == "template":
                return self._check_template_fulfilled(value)
            elif key == "components":
                return self._check_components_fulfilled(value)
            else:
                return value is not None
        except Exception as e:
            logging.exception(f"Failed to check input fulfilled: {e}")
            return value is not None

    def _check_components_fulfilled(self, value):
        if not value:
            return False
        if not isinstance(value, TofuDataList):
            raise ValueError(f"Invalid components type: {type(value)}")

        # Check if components container is not empty
        try:
            components = TofuDataListHandler.get_components(value)
            if not components.components:
                return False

            # Check if at least one component has meta type TEXT
            if not any(
                component.component_meta_type
                == TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_TEXT
                for component in components.components.values()
            ):
                return False
        except ValueError:
            return False

        return True

    def _check_template_fulfilled(self, value):
        if not value:
            return False
        if not isinstance(value, TofuDataList):
            raise ValueError(f"Invalid template type: {type(value)}")

        try:
            template_data = TofuDataListHandler.get_template(value)
            if not isinstance(template_data, TofuTemplate):
                raise ValueError(f"Invalid template type: {type(template_data)}")
        except Exception as e:
            # sometimes it's expected to be empty
            return False

        # check if any field has content_source_copy or slate_content_source_copy filled
        is_any_field_filled = False
        for field in template_data.template_fields.values():
            if field.content_source_copy or field.slate_content_source_copy:
                is_any_field_filled = True
                break
        return is_any_field_filled

    def _update_components(self, components):
        if not isinstance(components, TofuDataList):
            raise ValueError(f"Invalid components type: {type(components)}")

        components_v2_data = convert_components_v3_to_v2(components)
        for content_group in self.content_groups:
            content_group.components = components_v2_data
            content_group.save(update_fields=["components"])

    def _update_template(self, template):
        if not isinstance(template, TofuDataList):
            raise ValueError(f"Invalid template type: {type(template)}")
        for content_group in self.content_groups:
            template_v2_data = convert_template_v3_to_v2(
                template, content_group_params=content_group.content_group_params
            )
            content_group.content_group_params = template_v2_data
            content_group.save(update_fields=["content_group_params"])

    def _async_content_bulk_creation(self):
        # create first target
        targets_data = self._action_data_wrapper.get_input_by_name("targets")
        if not targets_data:
            return {"status": "error", "message": "No targets found in inputs"}

        target_ids = TofuDataListHandler.get_targets(targets_data)
        if not target_ids:
            logging.error(
                f"no target ids: {target_ids} for action {self._action_data_wrapper.action_instance.id}"
            )
            return {"status": "error", "message": "Invalid targets data format"}

        # if the number of targets is less than 200, we can create content synchronously
        if len(target_ids) <= 200:
            return self._sync_content_bulk_creation()

        # Here we create content from the first target for content settings
        # we don't return error here, because we want to continue the execution
        try:
            first_target = TargetInfo.objects.filter(id=target_ids[0]).first()
            if first_target:
                formatted_targets = {
                    first_target.target_info_group.target_info_group_key: first_target.target_key
                }
                for content_group in self.content_groups:
                    try:
                        ContentGroupHandler(content_group).create_content_from_group(
                            target_dict=formatted_targets
                        )
                    except Exception as e:
                        logging.exception(f"Error updating targets: {str(e)}")
                        continue
            else:
                logging.error(
                    f"first target not found for action {self._action_data_wrapper.action_instance.id}"
                )
        except Exception as e:
            logging.exception(f"Error getting targets: {str(e)}")

        content_group_ids = [cg.id for cg in self.content_groups]
        # Call the task asynchronously
        task = bulk_create_content_task.delay(content_group_ids)
        logging.info(f"Submitted bulk content creation task with ID: {task.id}")
        return task.id

    def _sync_content_bulk_creation(self):
        """
        Execute content creation synchronously for testing purposes
        """
        content_group_ids = [cg.id for cg in self.content_groups]
        # Call the task function directly instead of .delay()
        result = bulk_create_content_task(content_group_ids)
        logging.info(f"Executed synchronous bulk content creation: {result}")
        return result

    # TODO: refactor: move this to input handling
    def update_targets(self):
        """
        Submit bulk content creation as an asynchronous Celery task.
        Returns the async task ID for tracking.
        """
        run_sync = os.environ.get("TOFU_ENV").lower() in [
            "unit_test",
        ]
        if run_sync:
            return self._sync_content_bulk_creation()
        return self._async_content_bulk_creation()

    # Execution
    def _validate_execution_params(
        self, execution_params: ActionExecutionParams
    ) -> Dict:
        if not isinstance(execution_params, ActionExecutionParams):
            raise ValueError(
                f"execution_params {execution_params} is not type of ActionExecutionParams but {type(execution_params)}"
            )
        default_params = {
            "continue_gen": False,
            "content_ids": [],
            "joint_generation": True,
        }

        # Check if any oneof field is set
        selected_params = (
            execution_params.WhichOneof("params") if execution_params else None
        )
        # Validate if a field is selected but it's not personalization_params
        if selected_params and selected_params != "personalization_params":
            logging.error(
                f"Expected personalization_params in execution_params {execution_params}"
            )
            raise ValueError(
                f"Expected personalization_params in execution_params {execution_params}"
            )
        if not selected_params or not execution_params.personalization_params:
            logging.error(
                f"No personalization_params in execution_params {execution_params}"
            )
            return default_params

        # Extract values from the params
        params = execution_params.personalization_params
        return {
            "continue_gen": params.continue_gen,  # Use the value directly from params
            "content_ids": list(params.content_ids),  # Convert repeated field to list
            "joint_generation": params.joint_generation,
        }

    def _execute_impl(self, execution_params: ActionExecutionParams) -> Dict:
        campaign = self._action_data_wrapper.action_instance.campaign
        # TODO: check length to be 1
        content_groups = self._action_data_wrapper.content_groups
        if len(content_groups) != 1:
            raise ValueError(f"Expected 1 content group, got {len(content_groups)}")
        content_group = content_groups[0]

        parsed_execution_params = self._validate_execution_params(execution_params)

        gen_wrapper = CampaignGenWrapper(campaign)
        # TODO: fix users
        task_id = gen_wrapper.submit_job(
            user=self._action_data_wrapper.action_instance.creator,
            content_group_ids=[content_group.id],
            content_ids=parsed_execution_params["content_ids"],
            collection_ids=[],
            continue_gen=parsed_execution_params["continue_gen"],
            joint_generation=parsed_execution_params["joint_generation"],
            use_all_contents=False,
        )
        gen_wrapper.wait_job(task_id=task_id)

    def _terminate_impl(self):
        # follow the same as run
        campaign = self._action_data_wrapper.action_instance.campaign
        content_groups = self._action_data_wrapper.content_groups
        if len(content_groups) != 1:
            raise ValueError(f"Expected 1 content group, got {len(content_groups)}")
        content_group = content_groups[0]
        gen_wrapper = CampaignGenWrapper(campaign)
        gen_wrapper.terminate_gen([content_group.id])

    def _get_post_ready_status_details(self):
        default_status = ActionStatus(
            status_type=ActionStatusType.ACTION_STATUS_TYPE_READY,
            message="Action is ready",
        )
        try:
            content_group = self._action_data_wrapper.content_group
            status_result = GenStatusUpdater().get_content_group_gen_status_v3(
                content_group
            )
            return self._get_status_details_from_gen_status(status_result)
        except ValueError as e:
            logging.exception(f"Failed to get content group gen status: {e}")
            return default_status

    def _get_status_details_from_gen_status(
        self,
        status_result: Dict,
    ) -> ActionStatus:
        stats = status_result["stats"]

        status_type = self._get_status_type_based_stats(
            total_succ=stats.cnts_succ,
            total_fail=stats.cnts_fail,
            total_running=stats.cnts_running,
        )
        # Create ActionStatus with personalization details
        return ActionStatus(
            status_type=status_type,
            details=ActionStatusDetails(
                stats=stats,
                gen_personalize_details=ActionStatusDetailsGenPersonalize(
                    gen_status=status_result["content_group_gen_status"]
                ),
            ),
        )

    def _prepare_inputs_for_pull_inputs_on_edge(
        self, incoming_inputs, overwrite
    ) -> Tuple[Dict[str, Any], bool, bool]:
        """
        Prepare inputs for update based on overwrite flag.
        Args:
            incoming_inputs: Dictionary of input key-value pairs
            overwrite: Whether to overwrite existing inputs
        Returns:
            Dict: Inputs to be updated
        """
        available_input_keys = ["template"]
        # we take only template from this code path
        if not set(incoming_inputs.keys()).issubset(available_input_keys):
            raise ValueError(
                f"Invalid input keys for PersonalizationActionHandler: {set(incoming_inputs.keys()) - set(available_input_keys)}"
            )

        inputs_to_save = {}
        need_reset = False
        need_execute = False

        if overwrite:
            inputs_to_save = copy.deepcopy(incoming_inputs)
            inputs_to_save["components"] = TofuDataList()
            need_reset = True
            need_execute = False
        else:

            # Get existing inputs
            all_existing_inputs = self._action_data_wrapper.get_all_user_inputs()
            all_fulfilled = {
                key: value
                for key, value in all_existing_inputs.items()
                if self._check_input_fulfilled(key, value)
            }

            if "components" not in all_fulfilled:
                # if components are not fulfilled, it's ok to update template then
                all_fulfilled.pop("template", None)

            inputs_to_save = {
                k: v for k, v in incoming_inputs.items() if k not in all_fulfilled
            }

            need_reset = False
            need_execute = False

        if not inputs_to_save:
            return {}, False, False

        # post process
        if "template" in inputs_to_save:
            try:
                # copy template file
                template_data = TofuDataListHandler.get_template(
                    inputs_to_save["template"]
                )
                updated_template_data = copy_template_file(
                    self._action_data_wrapper._get_str_content_type(),
                    template_data,
                    self._action_data_wrapper._action_instance,
                )
                inputs_to_save["template"] = (
                    TofuDataListHandler.convert_template_to_tofu_data(
                        updated_template_data
                    )
                )
            except Exception as e:
                logging.exception(f"Failed to copy template file: {e}")
                inputs_to_save.pop("template", None)

        return inputs_to_save, need_reset, need_execute
