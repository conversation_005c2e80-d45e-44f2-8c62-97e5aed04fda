import copy
import json
import logging
import tempfile
import uuid
from urllib.parse import parse_qs, urlparse

from django.core.cache import cache

from ..models import Content
from ..s3_utils import copy_s3_file, upload_file
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuComponentType,
    TofuTemplate,
    TofuTemplateField,
)
from ..shared_types import ContentType
from ..utils import replace_with_content_variations


class RepurposeToP13nTemplateConverter:
    output_type = "template"

    def __init__(self, content_group, to_action):
        self.content_group = content_group
        self.to_action = to_action
        self.tofu_template = None

    def process(self):

        content_group_params = self.content_group.content_group_params
        content_type = content_group_params.get("content_type")
        if content_type == ContentType.AdCampaignLinkedin:
            self.tofu_template = self._convert_linkedin_ad_template()
        elif (
            content_type == ContentType.EmailMarketing
            or content_type == ContentType.EmailSDR
        ):
            self.tofu_template = self._convert_email_template()
        else:
            self.tofu_template = self._convert_generic_template()

        if not isinstance(self.tofu_template, TofuTemplate):
            raise ValueError(f"Expected tofu template, got {type(self.tofu_template)}")

        return self.tofu_template

    def _convert_linkedin_ad_template(self):
        template = TofuTemplate()
        # linkedin ad template currently does not use specific component types.
        template_field_type = TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
        field = TofuTemplateField()
        field.template_component_type = template_field_type
        field.content_source = self._get_content_source()  # not used.
        field.content_source_copy = self._get_content_source_copy_linkedin()
        field.content_source_format = self._get_content_source_format()
        field.content_source_upload_method = self._get_content_source_upload_method()

        template.template_fields[
            TofuComponentType.Name(field.template_component_type)
        ].CopyFrom(field)

        return template

    def _convert_email_template(self):
        template = TofuTemplate()

        field_subject = TofuTemplateField()
        field_subject.template_component_type = (
            TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
        )
        field_subject.content_source = self._get_content_source()  # not used.
        field_subject.content_source_copy = ""  # Not used for text template.
        field_subject.content_source_format = ""  # Not used for text template.
        field_subject.content_source_upload_method = ""  # Not used for text template.

        field_body = TofuTemplateField()
        field_body.template_component_type = (
            TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
        )
        field_body.content_source = self._get_content_source()  # not used.
        field_body.content_source_copy = self._get_content_source_copy_email()
        field_body.content_source_format = self._get_content_source_format()
        field_body.content_source_upload_method = (
            self._get_content_source_upload_method()
        )

        template.template_fields[
            TofuComponentType.Name(field_subject.template_component_type)
        ].CopyFrom(field_subject)
        template.template_fields[
            TofuComponentType.Name(field_body.template_component_type)
        ].CopyFrom(field_body)

        return template

    def _convert_generic_template(self):
        template = TofuTemplate()
        template_field_type = TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED

        field = TofuTemplateField()
        field.template_component_type = template_field_type

        field.content_source = self._get_content_source()  # not used.
        field.content_source_copy = self._get_content_source_copy_generic()
        field.content_source_format = self._get_content_source_format()
        field.content_source_upload_method = self._get_content_source_upload_method()

        template.template_fields[
            TofuComponentType.Name(field.template_component_type)
        ].CopyFrom(field)

        return template

    def _get_content_source(self):
        return ""

    def _get_components(self):
        # Assume only one content in the content group.
        content = Content.objects.filter(content_group=self.content_group).first()
        if content is None:
            raise ValueError("No Content found for the provided content group.")
        components_data = replace_with_content_variations(
            self.content_group.components, content.id, cache_enabled=False
        )
        return components_data

    def _get_content_source_copy_generic(self):
        components_data = self._get_components()
        if len(components_data) != 1:
            raise ValueError(
                f"Only one component is expected. Found {len(components_data)} from {components_data}"
            )
        text = next(iter(components_data.values()))["text"]
        slate_text = self._get_slate_text(text)
        s3_path = self._upload_to_s3(slate_text)
        return s3_path

    def _get_content_source_copy_email(self):
        components_data = self._get_components()
        if len(components_data) != 2:
            raise ValueError(
                f"Only two components are expected. Found {len(components_data)} from {components_data}"
            )
        subject_line = self._get_component_text(components_data, "email subject")
        body = self._get_component_text(components_data, "email body")

        slate_text = self._get_slate_text_email(subject_line, body)
        s3_path = self._upload_to_s3(slate_text)
        return s3_path

    def _get_content_source_copy_linkedin(self):
        components_data = self._get_components()
        if len(components_data) != 4:
            raise ValueError(
                f"Only four components are expected. Found {len(components_data)} from {components_data}"
            )
        headline = self._get_component_text(components_data, "headline")
        description = self._get_component_text(components_data, "description")
        introductory_text = self._get_component_text(
            components_data, "introductory-text"
        )
        ad_copy = self._get_component_text(components_data, "ad-copy")

        slate_text = self._get_slate_text_linkedin(
            headline, description, introductory_text, ad_copy
        )
        s3_path = self._upload_to_s3(slate_text)
        return s3_path

    def _get_component_text(self, components_data, component_name):
        for component_key, component in components_data.items():
            if component.get("meta", {}).get("component_type") == component_name:
                return component.get("text", "")
        raise ValueError(f"Required component '{component_name}' not found")

    def _get_slate_text(self, text):
        return json.dumps(
            self._get_slate_json_format([{"type": "text", "raw_text": text or ""}])
        )

    def _upload_to_s3(self, slate_text):
        file_name = f"{self._get_template_converter_file_name()}.json"
        with tempfile.TemporaryDirectory() as temp_dir:
            file_location = f"{temp_dir}/{file_name}"
            with open(file_location, "w") as f:
                f.write(slate_text)

            # upload file to s3
            upload_file(
                file_location,
                "application/json",
                "tofu-uploaded-files",
                file_name,
            )
            return f"/api/web/storage/s3-presigned-url?file={file_name}&fileType=application/json&directory=tofu-uploaded-files"

    def _get_content_source_format(self):
        return "Text"

    def _get_content_source_upload_method(self):
        return "Text"

    def _get_slate_text_email(self, subject_line, body):
        return json.dumps(
            self._get_slate_json_format(
                [
                    {"type": "subject-line", "raw_text": subject_line or ""},
                    {"type": "body", "raw_text": body or ""},
                ]
            )
        )

    def _get_slate_text_linkedin(
        self, headline, description, introductory_text, ad_copy
    ):
        return json.dumps(
            self._get_slate_json_format(
                [
                    {"type": "headline", "raw_text": headline or ""},
                    {"type": "description", "raw_text": description or ""},
                    {"type": "introductory-text", "raw_text": introductory_text or ""},
                    {"type": "ad-copy", "raw_text": ad_copy or ""},
                ]
            )
        )

    def _get_slate_json_format(self, slate_data):
        # Convert list of raw text entries to Slate JSON format
        return [
            {
                "type": entry["type"],
                "children": [
                    {"type": "paragraph", "children": [{"text": entry["raw_text"]}]}
                ],
            }
            for entry in slate_data
        ]

    def _get_template_converter_file_name(self):
        return f"repurpose_to_p13n_converter_template_{self.content_group.id}"


class RepurposeToP13nTemplateCopier:
    def __init__(self, content_type, template_data, dest_action):
        self.content_type = content_type
        self.template_data = template_data
        self.dest_action = dest_action

    def copy_template_file(self):
        field_to_update = None
        if self.content_type == ContentType.AdCampaignLinkedin:
            field_to_update = TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
        elif (
            self.content_type == ContentType.EmailMarketing
            or self.content_type == ContentType.EmailSDR
        ):
            field_to_update = TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
        else:
            field_to_update = TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED

        updated_template_data = copy.deepcopy(self.template_data)
        updated_template_data.template_fields[
            TofuComponentType.Name(field_to_update)
        ].content_source_copy = self._copy_content_source_copy(field_to_update)
        return updated_template_data

    def _copy_content_source_copy(self, field_to_update):
        original_content_source_copy = self.template_data.template_fields[
            TofuComponentType.Name(field_to_update)
        ].content_source_copy

        # Parse the file name from the URL query string
        parsed_url = urlparse(original_content_source_copy)
        query_params = parse_qs(parsed_url.query)
        current_name = query_params.get("file", [""])[0]
        directory = query_params.get("directory", ["tofu-uploaded-files"])[0]

        # Generate the new file name by inserting action ID before extension
        if "." in current_name:
            name_parts = current_name.rsplit(".", 1)
            new_name = f"{name_parts[0]}_{self.dest_action.id}.{name_parts[1]}"
        else:
            new_name = f"{current_name}_{self.dest_action.id}"

        try:
            copy_s3_file(current_name, new_name, bucket_name=directory)
        except Exception as e:
            logging.exception(f"Error copying content source copy file: {e}")
            raise e

        # Return the same URL format but with the new filename
        return f"/api/web/storage/s3-presigned-url?file={new_name}&fileType=application/json&directory={directory}"


def copy_template_file(content_type, template_data, dest_action):
    converter = RepurposeToP13nTemplateCopier(content_type, template_data, dest_action)
    return converter.copy_template_file()
