import copy
import logging
from typing import Any, Dict, List

from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction

from ..models import Action, ActionEdge, ContentGroup
from ..shared_definitions.protobuf.gen.action_define_pb2 import ActionCategory
from .repurpose_to_anchor import RepurposeToAnchorConverter
from .tofu_data_wrapper import TofuDataListHandler


class ActionEdgeCreator:
    @transaction.atomic()
    def create(
        self, outgoing_action: Action, incoming_edges: List[Dict[str, Any]]
    ) -> bool:
        if not incoming_edges:
            return True
        if not outgoing_action:
            raise ValueError("Outgoing action is required")

        for edge_data in incoming_edges:
            from_action_id = edge_data.get("from_action")
            config = edge_data.get("config", {})
            try:
                self._create_edge(from_action_id, outgoing_action, config)
            except ObjectDoesNotExist as e:
                raise ValueError(
                    f"from_action_id: {from_action_id} does not exist"
                ) from e
            except Exception as e:
                raise ValueError(f"Failed to create edge: {e}") from e
        return True

    @transaction.atomic()
    def update(self, action_edge_instance: ActionEdge, config: Dict[str, Any]) -> None:
        action_edge_instance.config = config
        action_edge_instance.save(update_fields=["config"])

    def _create_edge(
        self, from_action_id: int, outgoing_action: Action, config: Dict[str, Any]
    ) -> None:
        from_action = Action.objects.get(id=from_action_id)
        if (
            not config
            or "input_name_to_outgoing_action" not in config
            or "output_name_from_incoming_action" not in config
        ):
            raise ValueError(
                f"config is missing input_name_to_outgoing_action or output_name_from_incoming_action as {config}"
            )
        edge_data = {
            "from_action": from_action,
            "to_action": outgoing_action,
            "config": config,
        }
        ActionEdge.objects.create(**edge_data)
        from_action_category = ActionCategory.Value(from_action.action_category)
        to_action_category = ActionCategory.Value(outgoing_action.action_category)
        if (
            from_action_category == ActionCategory.ACTION_CATEGORY_PERSONALIZE
            and to_action_category == ActionCategory.ACTION_CATEGORY_EXPORT
        ):
            self._handle_create_personalize_to_export(from_action, outgoing_action)
        elif (
            from_action_category == ActionCategory.ACTION_CATEGORY_REPURPOSE
            and to_action_category == ActionCategory.ACTION_CATEGORY_EXPORT
        ):
            self._handle_create_repurpose_to_export(from_action, outgoing_action)
        elif (
            from_action_category == ActionCategory.ACTION_CATEGORY_REPURPOSE
            and to_action_category == ActionCategory.ACTION_CATEGORY_USER_INPUT
        ):
            self._handle_create_repurpose_to_user_input(from_action, outgoing_action)

    def _handle_create_personalize_to_export(
        self, from_action: Action, outgoing_action: Action
    ) -> None:
        content_group_data = from_action.outputs.get("content_group", None)
        if content_group_data is None:
            raise ValueError("Content group is required for personalize action")

        content_group_ids = TofuDataListHandler.get_content_group_ids(
            content_group_data
        )
        if len(content_group_ids) != 1:
            raise ValueError("Only one content group is allowed for personalize action")

        outgoing_action.inputs["content_group"] = copy.deepcopy(content_group_data)
        outgoing_action.outputs["content_group"] = copy.deepcopy(content_group_data)
        outgoing_action.save(update_fields=["inputs", "outputs"])

    def _handle_create_repurpose_to_export(
        self, from_action: Action, outgoing_action: Action
    ) -> None:
        content_group_data = from_action.outputs.get("content_group", None)
        if content_group_data is None:
            raise ValueError("Content group is required for repurpose action")
        outgoing_action.inputs["content_group"] = copy.deepcopy(content_group_data)
        outgoing_action.outputs["content_group"] = copy.deepcopy(content_group_data)
        outgoing_action.save(update_fields=["inputs", "outputs"])

    def _handle_create_repurpose_to_user_input(
        self, from_action: Action, outgoing_action: Action
    ) -> None:
        content_group_data = from_action.outputs.get("content_group", None)
        if content_group_data is None:
            raise ValueError("Content group is required for repurpose action")
        content_group_ids = TofuDataListHandler.get_content_group_ids(
            content_group_data
        )
        content_groups = ContentGroup.objects.filter(id__in=content_group_ids)

        asset_ids = []
        for content_group in content_groups:
            converter = RepurposeToAnchorConverter(content_group, outgoing_action)
            asset = converter.process()
            if asset is not None:
                asset_ids.append(asset.asset_id)
            else:
                logging.error(
                    f"No asset found for repurpose to user input action {outgoing_action.id}"
                )
        if not asset_ids:
            logging.error(
                f"No asset ids found for repurpose to user input action {outgoing_action.id} from action {from_action.id}"
            )
            return

        asset_data_new = TofuDataListHandler.convert_asset_to_tofu_data(
            asset_ids=asset_ids, asset_group_ids=[]
        )
        asset_data_before_setup = outgoing_action.inputs.get("assets", None)
        asset_data = TofuDataListHandler.merge_asset_data(
            asset_data_before_setup, asset_data_new
        )

        if asset_data is not None:
            outgoing_action.inputs["assets"] = (
                TofuDataListHandler.convert_tofu_data_to_json(asset_data)
            )
            outgoing_action.save(update_fields=["inputs"])
