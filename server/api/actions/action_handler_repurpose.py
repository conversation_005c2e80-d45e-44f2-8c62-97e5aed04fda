import copy
import logging
from typing import Any, Dict, Tuple

from django.db import transaction
from google.protobuf.json_format import ParseDict

from ..campaign_gen_wrapper import CampaignGenWrapper
from ..content_group import ContentGroupHandler
from ..gen_status import GenStatusUpdater
from ..models import ContentGroup
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionExecutionParams,
    ActionStatus,
    ActionStatusDetails,
    ActionStatusDetailsGenRepurpose,
    ActionStatusStats,
    ActionStatusType,
    ContentGroupGenerationStatus,
    TofuData,
    TofuDataList,
    TofuTemplate,
    TofuTemplateField,
)
from .action_handler_base import ActionHandlerBase
from .legacy_converter.legacy_repurpose_template_converter import (
    convert_repurpose_template_v3_to_v2,
)
from .mixins import CustomInstructionsMixin
from .tofu_data_wrapper import TofuDataListHandler


class RepurposingActionHandler(ActionHandlerBase, CustomInstructionsMixin):
    """Handles execution of repurpose actions"""

    @transaction.atomic
    def update_action_name(self, new_action_name):
        content_groups_to_update = []
        for content_group in self.content_groups:
            if "action_index" not in content_group.content_group_params:
                raise ValueError(
                    f"action_index field not found for content group: {content_group.id} with params: {content_group.content_group_params}"
                )
            idx = content_group.content_group_params["action_index"]
            content_group_name = (
                f"{new_action_name} - {idx}"
                if len(self.content_groups) > 1
                else new_action_name
            )
            content_group.content_group_name = content_group_name
            content_groups_to_update.append(content_group)

        # Perform batch update if there are content groups to update
        if content_groups_to_update:
            ContentGroup.objects.bulk_update(
                content_groups_to_update, ["content_group_name"]
            )

        self._action_data_wrapper._action_instance.action_name = new_action_name
        self._action_data_wrapper._action_instance.save(update_fields=["action_name"])

    def _handle_special_input(self, key, value):
        if key == "num_outputs":
            self._update_num_outputs(value)
        elif key == "custom_instructions":
            self._update_custom_instructions(value)
        elif key == "template":
            self._update_repurpose_template(value)
        else:
            raise ValueError(f"Invalid input key: {key}")

    def _update_content_groups(self, content_groups):
        content_group_ids = [content_group.id for content_group in content_groups]
        self._action_data_wrapper._content_groups = content_groups
        self._action_data_wrapper._action_instance.outputs["content_group"] = (
            TofuDataListHandler.convert_tofu_data_to_json(
                TofuDataListHandler.convert_content_group_ids_to_tofu_data(
                    content_group_ids
                ),
            )
        )
        self._action_data_wrapper._action_instance.save(update_fields=["outputs"])

    def _update_num_outputs(self, num_outputs):
        if not self.content_groups:
            raise ValueError(
                f"No content groups found for action: {self.action_instance}"
            )
        int_num_outputs = TofuDataListHandler.get_int_value(num_outputs)
        # TODO: move this to config
        if int_num_outputs <= 0 or int_num_outputs > 10:
            raise ValueError(f"Invalid num_outputs: {int_num_outputs}")
        if len(self.content_groups) == int_num_outputs:
            return False

        updated_content_groups = []
        if len(self.content_groups) > int_num_outputs:
            # Delete content groups with index >= num_outputs
            for content_group in self.content_groups:
                if "action_index" not in content_group.content_group_params:
                    raise ValueError(
                        f"action_index field not found for content group: {content_group.id} with params: {content_group.content_group_params}"
                    )
                idx = content_group.content_group_params["action_index"]
                if idx is None:
                    raise ValueError(
                        f"action_index field not found for content group: {content_group.id} with params: {content_group.content_group_params}"
                    )
                if int(idx) >= int_num_outputs:
                    content_group.delete()
                else:
                    updated_content_groups.append(content_group)
        else:
            updated_content_groups = list(self.content_groups)
            first_existing_content_group = self.content_groups[0]
            for i in range(len(self.content_groups), int_num_outputs):
                content_group_name = (
                    f"{self.action_instance.action_name} - {i}"
                    if int_num_outputs > 1
                    else self.action_instance.action_name
                )
                content_group_params = copy.deepcopy(
                    first_existing_content_group.content_group_params
                )
                content_group_params["action_index"] = i
                content_group = ContentGroup.objects.create(
                    creator=first_existing_content_group.creator,
                    campaign=first_existing_content_group.campaign,
                    action=self.action_instance,
                    content_group_name=content_group_name,
                    content_group_params=content_group_params,
                )
                ContentGroupHandler(content_group=content_group).bulk_create_content()
                updated_content_groups.append(content_group)
        if len(updated_content_groups) != int_num_outputs:
            raise ValueError(
                f"Failed to update content groups: {updated_content_groups} with input: {num_outputs} for action: {self.action_instance}"
            )
        self._update_content_groups(updated_content_groups)

        return True

    def _update_repurpose_template(self, template):
        if not isinstance(template, TofuDataList):
            raise ValueError(f"Invalid template type: {type(template)}")
        content_group_params_to_update = []
        for content_group in self.content_groups:
            template_v2_data = convert_repurpose_template_v3_to_v2(
                template, content_group_params=content_group.content_group_params
            )
            content_group.content_group_params = template_v2_data
            content_group_params_to_update.append(content_group)
        if content_group_params_to_update:
            ContentGroup.objects.bulk_update(
                content_group_params_to_update, ["content_group_params"]
            )

    def _execute_impl(self, execution_params: ActionExecutionParams) -> Dict:
        campaign = self._action_data_wrapper.action_instance.campaign
        # TODO: check length to be 1
        content_groups = self._action_data_wrapper.content_groups
        if not content_groups:
            raise ValueError(
                f"No content groups found for action: {self.action_instance}"
            )

        collection_ids = []
        if len(content_groups) > 1:
            collection_ids = [f"action_{self.action_instance.id}"]
        gen_wrapper = CampaignGenWrapper(campaign)
        # TODO: fix users
        task_id = gen_wrapper.submit_job(
            user=self._action_data_wrapper.action_instance.creator,
            content_group_ids=[content_group.id for content_group in content_groups],
            content_ids=[],
            collection_ids=collection_ids,
            continue_gen=False,
            joint_generation=True,
            use_all_contents=False,
        )
        gen_wrapper.wait_job(task_id=task_id)

    def _terminate_impl(self):
        # follow the same as run
        campaign = self._action_data_wrapper.action_instance.campaign
        content_groups = self._action_data_wrapper.content_groups
        gen_wrapper = CampaignGenWrapper(campaign)
        gen_wrapper.terminate_gen(
            [content_group.id for content_group in content_groups]
        )

    def _get_post_ready_status_details(self):
        default_status = ActionStatus(
            status_type=ActionStatusType.ACTION_STATUS_TYPE_READY,
            message="Action is ready",
        )
        try:
            content_groups = self._action_data_wrapper.content_groups
            gen_status = {}

            # Track overall stats
            total_not_started = 0
            total_succ = 0
            total_fail = 0
            total_running = 0

            for content_group in content_groups:
                status_result = GenStatusUpdater().get_content_group_gen_status_v3(
                    content_group
                )

                # Add stats to totals
                stats = status_result["stats"]
                total_not_started += stats.cnts_not_started
                total_succ += stats.cnts_succ
                total_fail += stats.cnts_fail
                total_running += stats.cnts_running

                # Create ContentGroupGenerationStatus for this content group
                gen_status[content_group.id] = ContentGroupGenerationStatus(
                    content_group_gen_status=status_result["content_group_gen_status"]
                )

            status_type = self._get_status_type_based_stats(
                total_succ=total_succ,
                total_fail=total_fail,
                total_running=total_running,
            )
            return ActionStatus(
                status_type=status_type,
                details=ActionStatusDetails(
                    stats=ActionStatusStats(
                        cnts_not_started=total_not_started,
                        cnts_succ=total_succ,
                        cnts_fail=total_fail,
                        cnts_running=total_running,
                    ),
                    gen_repurpose_details=ActionStatusDetailsGenRepurpose(
                        gen_status=gen_status
                    ),
                ),
            )
        except Exception as e:
            logging.exception(f"Failed to get content group gen status: {e}")
            return default_status

    def _prepare_inputs_for_pull_inputs_on_edge(
        self, incoming_inputs, overwrite
    ) -> Tuple[Dict[str, Any], bool, bool]:
        """
        Prepare inputs for update based on overwrite flag.
        Args:
            incoming_inputs: Dictionary of input key-value pairs
            overwrite: Whether to overwrite existing inputs
        Returns:
            Dict: Inputs to be updated
        """

        # behavior expected:
        # when anchor_content is updated,
        # for result: we will not reset anything
        # for execution: we don't execute automatically
        available_input_keys = ["anchor_content"]
        if not set(incoming_inputs.keys()).issubset(available_input_keys):
            raise ValueError(
                f"Invalid input keys for RepurposingActionHandler: {set(incoming_inputs.keys()) - set(available_input_keys)}"
            )

        if overwrite:
            return copy.deepcopy(incoming_inputs), False, False

        # Get existing inputs
        all_existing_inputs = self._action_data_wrapper.get_all_user_inputs()
        # Return only the inputs that don't already exist
        return (
            {k: v for k, v in incoming_inputs.items() if k not in all_existing_inputs},
            False,
            False,
        )
