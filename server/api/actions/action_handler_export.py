import logging
from typing import Dict

from ..gen_status import GenStatusUpdater
from ..models import ContentGroup
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionExecutionParams,
    ActionStatus,
    ActionStatusDetails,
    ActionStatusType,
    ExportParams,
    TofuDataList,
)
from ..sync.crm_export.crm_export_hubspot import EmailTemplateInput
from ..sync.crm_export.export_factory import get_export_handler
from ..sync.utils.export_settings_wrapper import ExportSettingsWrapper
from ..utils import CloudWatchMetrics
from .action_handler_base import ActionHandlerBase
from .legacy_converter.legacy_export_response_converter import (
    convert_export_response_v2_to_v3,
)
from .legacy_converter.legacy_export_settings_converter import (
    ExportSettingsV3ToV2Converter,
    convert_export_settings_v3_to_v2,
    is_export_settings_v2_equal,
)
from .tofu_data_wrapper import TofuDataListHandler


class ExportActionHandler(ActionHandlerBase):
    def get_platform_type(self):
        platform_type_data = self._action_data_wrapper.get_input_by_name(
            "platform_type"
        )
        platform_type = TofuDataListHandler.get_platform_type(platform_type_data)
        return platform_type

    def _handle_special_input(self, key, value):
        if key == "export_settings":
            self._update_export_settings(value)
        else:
            logging.error(
                f"debug: special inputs for export is not implemented: key: {key} and value: {value}"
            )

    def _update_export_settings(self, export_settings):
        if not isinstance(export_settings, TofuDataList):
            raise ValueError(f"Invalid export_settings type: {type(export_settings)}")

        content_groups_to_update = []
        tofu_export_settings = (
            ExportSettingsV3ToV2Converter.extract_tofu_export_settings(export_settings)
        )
        is_shadow_testing = (
            tofu_export_settings.is_shadow_testing if tofu_export_settings else False
        )

        # we only update export settings when shadow testing is enabled
        if not is_shadow_testing:
            logging.info(f"shadow testing is disabled, skipping update export settings")
            return
        try:
            for content_group in self.content_groups:
                existing_export_settings = content_group.content_group_params.get(
                    "export_settings", {}
                )
                export_settings_v2_data = convert_export_settings_v3_to_v2(
                    export_settings, existing_export_settings, content_group
                )
                if is_shadow_testing:
                    if not is_export_settings_v2_equal(
                        export_settings_v2_data, existing_export_settings
                    ):
                        logging.error(
                            f"[Export Shadow Testing] {content_group.id}: export settings from action is not equal to export settings from content group. from action: {export_settings_v2_data}, from content group: {existing_export_settings}"
                        )
                        CloudWatchMetrics.put_metric(
                            "ExportSettingsMismatch",
                            1,
                            [
                                {
                                    "Name": "content_group_id",
                                    "Value": str(content_group.id),
                                }
                            ],
                        )
                else:
                    content_group.content_group_params["export_settings"] = (
                        export_settings_v2_data
                    )
                    content_groups_to_update.append(content_group)
        except Exception as e:
            logging.exception(f"Error updating export settings: {e}")
            return

        if not is_shadow_testing:
            ContentGroup.objects.bulk_update(
                content_groups_to_update, ["content_group_params"]
            )

    def _execute_impl(self, execution_params: ActionExecutionParams):

        content_groups = self._action_data_wrapper.content_groups
        if len(content_groups) != 1:
            raise ValueError(f"Expected 1 content group, got {len(content_groups)}")
        content_group = content_groups[0]

        export_setting_wrapper = ExportSettingsWrapper(content_group)
        if not export_setting_wrapper.has_settings():
            raise ValueError(
                f"No export settings found for content group {content_group.id}"
            )

        platform_type = self.get_platform_type()

        export_handler = get_export_handler(platform_type, content_group=content_group)
        if not export_handler:
            raise ValueError(
                f"No export handler found for content group {content_group.id}"
            )
        export_params = self._validate_execution_params(execution_params)
        if export_params["is_new_export"]:
            export_handler.export_new(content_group, export_params["params"])
        else:
            export_handler.export_existing(content_group)

    def _terminate_impl(self):
        raise NotImplementedError("Not implemented")

    def _get_post_ready_status_details(self):
        # check prev actions
        content_groups = self._action_data_wrapper.content_groups
        if not content_groups:
            logging.error(f"no content_groups found for export action")

        has_any_generated_content = False
        for content_group in content_groups:
            try:
                content_group_status = (
                    GenStatusUpdater().get_content_group_gen_status_v3(content_group)
                )
                stats = content_group_status.get("stats", None)
                if stats and stats.cnts_succ > 0:
                    has_any_generated_content = True
                    break
            except Exception as e:
                logging.exception(
                    f"Fail to check the status for content group {content_group}: {str(e)}"
                )
                continue

        if not has_any_generated_content:
            return ActionStatus(
                status_type=ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT,
            )

        platform_type = self.get_platform_type()
        action_status_details = convert_export_response_v2_to_v3(
            platform_type, self._action_data_wrapper.content_group.content_group_params
        )
        if not isinstance(action_status_details, ActionStatusDetails):
            raise ValueError(f"Invalid export response: {action_status_details}")
        action_status = ActionStatus(
            status_type=self._get_status_type_based_stats(
                total_fail=action_status_details.stats.cnts_fail,
                total_succ=action_status_details.stats.cnts_succ,
                total_running=action_status_details.stats.cnts_running,
            ),
            details=action_status_details,
        )
        return action_status

    def _delete_special_handling(self):
        # clean up export_settings and export_response fields from content_group
        content_group = self._action_data_wrapper.content_group
        old_export_settings = content_group.content_group_params.pop(
            "export_settings", None
        )
        old_export_response = content_group.content_group_params.pop(
            "export_response", None
        )
        if old_export_settings is not None or old_export_response is not None:
            content_group.save(update_fields=["content_group_params"])

    def _validate_execution_params(
        self, execution_params: ActionExecutionParams
    ) -> Dict:
        if not isinstance(execution_params, ActionExecutionParams):
            raise ValueError(f"Invalid execution_params type: {type(execution_params)}")
        # Check which oneof is set in ActionExecutionParams
        params_field = execution_params.WhichOneof("params")
        if params_field != "export_params":
            raise ValueError(
                f"Expected export_params in execution_params, got {params_field}"
            )
        export_params = execution_params.export_params
        if not export_params:
            raise ValueError("export_params is not set in execution_params")
        # Check which oneof is set in ExportParams
        export_params_field = export_params.WhichOneof("params")
        if export_params_field != "email_params":
            logging.error(
                f"Expected email_params in export_params, got {export_params_field}, will use BE generated params"
            )
            return {
                "is_new_export": export_params.is_new_export,
                "params": {},
            }
        email_params = export_params.email_params
        if not email_params:
            logging.error(
                "email_params is not set in export_params, will use BE generated params"
            )
            return {
                "is_new_export": export_params.is_new_export,
                "params": {},
            }
        return {
            "is_new_export": export_params.is_new_export,
            "params": {
                "html_content": email_params.html_content,
                "email_file_name": email_params.email_file_name,
                "email_subject": email_params.email_subject,
            },
        }
