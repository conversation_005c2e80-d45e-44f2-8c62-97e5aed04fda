import logging

from google.protobuf.json_format import MessageToDict, ParseDict

from ..models import TargetInfo
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionInputOutputDefinition,
    TofuComponent,
    TofuComponents,
    TofuContentGroup,
    TofuContentType,
    TofuCustomInstruction,
    TofuData,
    TofuDataList,
    TofuDataType,
    TofuExportSettings,
    TofuPlatformType,
    TofuTarget,
    TofuTemplate,
)


class TofuDataListHandler:
    @staticmethod
    def parse_json_to_tofu_data(json_data):
        if not isinstance(json_data, dict):
            raise ValueError(
                f"Expected dict for parse_json_to_tofu_data, got: {type(json_data)} as {json_data}"
            )
        return ParseDict(json_data, TofuDataList())

    @staticmethod
    def convert_tofu_data_to_json(tofu_data_list):
        if not isinstance(tofu_data_list, TofuDataList):
            logging.error(
                f"debug: tofu_data_list is not a TofuDataList: {tofu_data_list}"
            )
            raise ValueError(
                f"Expected TofuDataList for convert_tofu_data_to_json, got: {type(tofu_data_list)} as {tofu_data_list}"
            )
        return MessageToDict(tofu_data_list, preserving_proto_field_name=True)

    @staticmethod
    def prepare_tofu_data_list(tofu_data_list, is_array=False):
        if isinstance(tofu_data_list, dict):
            tofu_data_list = TofuDataListHandler.parse_json_to_tofu_data(tofu_data_list)
        if not tofu_data_list or not isinstance(tofu_data_list, TofuDataList):
            raise ValueError(f"Invalid tofu_data_list: {tofu_data_list}")
        if len(tofu_data_list.data) == 0 and not is_array:
            raise ValueError(
                f"Invalid tofu_data_list: {tofu_data_list}, expected non empty tofu_data_list"
            )
        return tofu_data_list

    # Integer values (TOFU_DATA_TYPE_INT = 1)
    @staticmethod
    def get_int_value(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        return tofu_data_list.data[0].int_value.value

    @staticmethod
    def convert_int_to_tofu_data(value):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.int_value.value = value
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # String values (TOFU_DATA_TYPE_STRING = 2)
    @staticmethod
    def get_string_value(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        return tofu_data_list.data[0].string_value.value

    @staticmethod
    def convert_string_to_tofu_data(value):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.string_value.value = value
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Boolean values (TOFU_DATA_TYPE_BOOL = 3)
    @staticmethod
    def get_bool_value(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        return tofu_data_list.data[0].bool_value.value

    @staticmethod
    def convert_bool_to_tofu_data(value):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.bool_value.value = value
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Content type (TOFU_DATA_TYPE_CONTENT_TYPE = 4)
    @staticmethod
    def get_content_type(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        if not tofu_data_list.data[0].HasField("content_type"):
            raise ValueError("content_type is not set")
        return tofu_data_list.data[0].content_type.content_type

    @staticmethod
    def convert_content_type_to_tofu_data(content_type):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.content_type.CopyFrom(TofuContentType(content_type=content_type))
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Content group (TOFU_DATA_TYPE_CONTENT_GROUP = 5)
    @staticmethod
    def get_content_group_ids(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(
            tofu_data_list, is_array=True
        )
        content_group_ids = []
        for tofu_data in tofu_data_list.data:
            content_group_ids.append(tofu_data.content_group.content_group_id)
        return content_group_ids

    @staticmethod
    def convert_content_group_ids_to_tofu_data(content_group_ids):
        tofu_data_list = TofuDataList()
        for content_group_id in content_group_ids:
            tofu_data = TofuData()
            tofu_data.content_group.CopyFrom(
                TofuContentGroup(content_group_id=content_group_id)
            )
            tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Asset (TOFU_DATA_TYPE_ASSET = 6)
    @staticmethod
    def get_assets(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(
            tofu_data_list, is_array=True
        )
        asset_ids = []
        asset_group_ids = []
        for asset_data in tofu_data_list.data:
            if asset_data.asset.asset_id:
                asset_ids.append(asset_data.asset.asset_id)
            elif asset_data.asset.asset_group_id:
                asset_group_ids.append(asset_data.asset.asset_group_id)
        return asset_ids, asset_group_ids

    @staticmethod
    def convert_asset_to_tofu_data(asset_ids, asset_group_ids):
        tofu_data_list = TofuDataList()
        if asset_ids:
            for asset_id in asset_ids:
                if not isinstance(asset_id, int):
                    raise ValueError(f"Invalid asset_id: {asset_id}")
                tofu_data = TofuData()
                tofu_data.asset.asset_id = asset_id
                tofu_data_list.data.append(tofu_data)
        if asset_group_ids:
            for asset_group_id in asset_group_ids:
                if not isinstance(asset_group_id, int):
                    raise ValueError(f"Invalid asset_group_id: {asset_group_id}")
                tofu_data = TofuData()
                tofu_data.asset.asset_group_id = asset_group_id
                tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Target (TOFU_DATA_TYPE_TARGET = 7)
    @staticmethod
    def get_targets(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(
            tofu_data_list, is_array=True
        )
        target_ids = []
        for tofu_data in tofu_data_list.data:
            target_ids.append(tofu_data.target.target_id)
        return target_ids

    @staticmethod
    def convert_targets_to_tofu_data(targets):
        tofu_data_list = TofuDataList()
        for target_data in targets:
            if isinstance(target_data, int):
                tofu_data = TofuData()
                tofu_data.target.CopyFrom(TofuTarget(target_id=target_data))
                tofu_data_list.data.append(tofu_data)
            elif isinstance(target_data, dict):
                for l1_key, l2_keys in target_data.items():
                    l2_targets = TargetInfo.objects.filter(
                        target_info_group__target_info_group_key=l1_key,
                        target_key__in=l2_keys,
                    )
                    for target in l2_targets:
                        tofu_data = TofuData()
                        tofu_data.target.CopyFrom(TofuTarget(target_id=target.id))
                        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Components (TOFU_DATA_TYPE_COMPONENTS = 8)
    @staticmethod
    def get_components(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        if not tofu_data_list.data[0].HasField("components"):
            raise ValueError("components is not set")
        return tofu_data_list.data[0].components

    @staticmethod
    def convert_components_to_tofu_data(components_dict: TofuComponents):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.components.CopyFrom(components_dict)
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Template (TOFU_DATA_TYPE_TEMPLATE = 9)
    @staticmethod
    def get_template(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        if not tofu_data_list.data[0].HasField("template"):
            raise ValueError("template is not set")
        return tofu_data_list.data[0].template

    @staticmethod
    def convert_template_to_tofu_data(template: TofuTemplate):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        # Copy the entire template message to the tofu_data's template field
        tofu_data.template.CopyFrom(template)
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Export settings (TOFU_DATA_TYPE_EXPORT_SETTING = 11)
    @staticmethod
    def get_export_settings(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        if not tofu_data_list.data[0].HasField("export_settings"):
            raise ValueError("export_settings is not set")
        return tofu_data_list.data[0].export_settings

    @staticmethod
    def convert_export_settings_to_tofu_data(export_settings):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.export_settings.CopyFrom(export_settings)
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    # Platform type (not in TofuDataType enum but related to export settings)
    @staticmethod
    def get_platform_type(tofu_data_list):
        tofu_data_list = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list)
        if not tofu_data_list.data[0].HasField("platform_type"):
            raise ValueError("platform_type is not set")
        return tofu_data_list.data[0].platform_type.platform_type

    @staticmethod
    def convert_platform_type_to_tofu_data(platform_type):
        tofu_data_list = TofuDataList()
        tofu_data = TofuData()
        tofu_data.platform_type.platform_type = platform_type
        tofu_data_list.data.append(tofu_data)
        return tofu_data_list

    @staticmethod
    def merge_asset_data(tofu_data_list1, tofu_data_list2):
        if tofu_data_list1 is None:
            return tofu_data_list2
        if tofu_data_list2 is None:
            return tofu_data_list1

        tofu_data_list1 = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list1)
        tofu_data_list2 = TofuDataListHandler.prepare_tofu_data_list(tofu_data_list2)

        asset_ids1, asset_group_ids1 = TofuDataListHandler.get_assets(tofu_data_list1)
        asset_ids2, asset_group_ids2 = TofuDataListHandler.get_assets(tofu_data_list2)

        asset_ids = list(set(asset_ids1 + asset_ids2))
        asset_group_ids = list(set(asset_group_ids1 + asset_group_ids2))

        result_tofu_data_list = TofuDataListHandler.convert_asset_to_tofu_data(
            asset_ids, asset_group_ids
        )
        return result_tofu_data_list

    @staticmethod
    def is_valid_tofu_data_definition(
        data_definition: ActionInputOutputDefinition, tofu_data_list: TofuDataList
    ) -> bool:
        """
        Validate if all items in tofu_data_list match the expected data_type.

        Args:
            data_type: TofuDataType enum value
            tofu_data_list: TofuDataList message
        Returns:
            bool: True if all items match the expected type, False otherwise
        """
        if not isinstance(data_definition, ActionInputOutputDefinition):
            raise ValueError(
                f"Expected ActionInputOutputDefinition for is_valid_tofu_data_definition, got: {type(data_definition)} as {data_definition}"
            )
        if tofu_data_list is None:
            return False
        if not isinstance(tofu_data_list, TofuDataList):
            logging.error(
                f"debug: tofu_data_list is not a TofuDataList: {tofu_data_list}"
            )
            return False

        if not data_definition.is_array:
            if len(tofu_data_list.data) > 1:
                logging.error(
                    f"debug: tofu_data_list has more than one item: {tofu_data_list} for data_definition: {data_definition}"
                )
                return False

        return TofuDataListHandler.is_valid_tofu_data_type(
            data_definition.data_type, tofu_data_list
        )

    @staticmethod
    def is_valid_tofu_data_type(
        tofu_data_type: TofuDataType, tofu_data_list: TofuDataList
    ) -> bool:
        """
        Validate if all items in tofu_data_list match the expected data_type.

        Args:
            data_type: TofuDataType enum value
        Returns:
            bool: True if all items match the expected type, False otherwise
        """

        for data in tofu_data_list.data:
            which_oneof = data.WhichOneof("data")

            match tofu_data_type:
                case TofuDataType.TOFU_DATA_TYPE_INT:
                    if which_oneof != "int_value":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_STRING:
                    if which_oneof != "string_value":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_BOOL:
                    if which_oneof != "bool_value":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_CONTENT_TYPE:
                    if which_oneof != "content_type":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_PLATFORM_TYPE:
                    if which_oneof != "platform_type":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_CONTENT_GROUP:
                    if which_oneof != "content_group":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_ASSET:
                    if which_oneof != "asset":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_TARGET:
                    if which_oneof != "target":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_COMPONENTS:
                    if which_oneof != "components":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_TEMPLATE:
                    if which_oneof != "template":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_CUSTOM_INSTRUCTION:
                    if which_oneof != "custom_instruction":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_EXPORT_SETTING:
                    if which_oneof != "export_settings":
                        return False
                case TofuDataType.TOFU_DATA_TYPE_UNSPECIFIED:
                    raise ValueError(f"Invalid tofu_data_type: {tofu_data_type}")
                case _:
                    raise ValueError(f"Invalid tofu_data_type: {tofu_data_type}")

        return True
