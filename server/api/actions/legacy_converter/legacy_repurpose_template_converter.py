import logging
from typing import Any, Dict, List, Optional

from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuAsset,
    TofuComponentType,
    TofuDataList,
    TofuTemplate,
    TofuTemplateField,
)
from .legacy_custom_instruction_converter import (
    AssetsV2ToV3Converter,
    AssetsV3ToV2Converter,
)


class RepurposeTemplateV2ToV3Converter:
    def __init__(self, playbook_id: int, content_group_params: Dict[str, Any]):
        self.playbook_id = playbook_id
        self.content_group_params = content_group_params

    def convert_repurpose_template_v2_to_v3(self) -> Optional[TofuTemplate]:

        content_type = self.content_group_params.get("content_type", "")
        is_email = content_type in ["Email - Marketing", "Email - SDR"]

        template = TofuTemplate()

        template_component_type = (
            TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
            if not is_email
            else TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
        )
        field = TofuTemplateField()
        field.template_component_type = template_component_type
        if (
            "content_source" in self.content_group_params
            and self.content_group_params["content_source"] is not None
        ):
            field.content_source = self.content_group_params["content_source"]
        if (
            "repurpose_template_content_source_copy" in self.content_group_params
            and self.content_group_params["repurpose_template_content_source_copy"]
            is not None
        ):
            field.content_source_copy = self.content_group_params[
                "repurpose_template_content_source_copy"
            ]
        if (
            "slate_repurpose_template_content_source_copy" in self.content_group_params
            and self.content_group_params[
                "slate_repurpose_template_content_source_copy"
            ]
            is not None
        ):
            field.slate_content_source_copy = self.content_group_params[
                "slate_repurpose_template_content_source_copy"
            ]
        if (
            "slate_repurpose_template_content_source" in self.content_group_params
            and self.content_group_params["slate_repurpose_template_content_source"]
            is not None
        ):
            field.slate_content_source = self.content_group_params[
                "slate_repurpose_template_content_source"
            ]
        if (
            "content_source_format" in self.content_group_params
            and self.content_group_params["content_source_format"] is not None
        ):
            field.content_source_format = self.content_group_params[
                "content_source_format"
            ]
        if (
            "content_source_upload_method" in self.content_group_params
            and self.content_group_params["content_source_upload_method"] is not None
        ):
            field.content_source_upload_method = self.content_group_params[
                "content_source_upload_method"
            ]
        template.template_fields[
            TofuComponentType.Name(field.template_component_type)
        ].CopyFrom(field)

        if is_email:
            field = TofuTemplateField()
            field.template_component_type = (
                TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
            )
            if "subject_line_only_content_source" in self.content_group_params:
                field.content_source = self.content_group_params[
                    "subject_line_only_content_source"
                ]
            if "subject_line_only_content_source_copy" in self.content_group_params:
                field.content_source_copy = self.content_group_params[
                    "subject_line_only_content_source_copy"
                ]
            if "slate_subject_line_only_content_source" in self.content_group_params:
                field.slate_content_source = self.content_group_params[
                    "slate_subject_line_only_content_source"
                ]
            if (
                "slate_subject_line_only_content_source_copy"
                in self.content_group_params
            ):
                field.slate_content_source_copy = self.content_group_params[
                    "slate_subject_line_only_content_source_copy"
                ]
            template.template_fields[
                TofuComponentType.Name(field.template_component_type)
            ].CopyFrom(field)

        # Handle template settings if they exist
        if (
            "template_settings" in self.content_group_params
            and self.content_group_params["template_settings"] is not None
        ):
            settings = self.content_group_params["template_settings"]
            if settings:
                template.follow_template_tone = settings.get("follow_tone", False)
                template.follow_template_length = settings.get("follow_length", False)
                template.follow_template_core_message_and_key_point = settings.get(
                    "follow_core_message_and_key_point", False
                )

                # Handle tone reference
                if settings.get("tone_reference"):
                    for ref in settings["tone_reference"]:
                        if ref.get("assets"):
                            assets_data = AssetsV2ToV3Converter(
                                playbook_id=self.playbook_id, assets_v2=ref["assets"]
                            ).convert_assets_v2_to_v3()
                            template.tone_reference.CopyFrom(assets_data)
                            break

        return template


class RepurposeTemplateV3ToV2Converter:
    def __init__(self, template: TofuTemplate, content_group_params: Dict[str, Any]):
        # Check if template is a TofuDataList
        if isinstance(template, TofuDataList):
            self.template = template.data[0].template
        elif isinstance(template, TofuTemplate):
            self.template = template
        else:
            raise ValueError("Template is not a valid TofuTemplate or TofuDataList")

        self.content_group_params = content_group_params

    def convert_repurpose_template_v3_to_v2(self) -> Dict[str, Any]:
        # Remove existing template-related fields
        template_fields = [
            "content_source",
            "content_source_copy",
            "content_source_format",
            "content_source_upload_method",
            "subject_line_only_content_source",
            "subject_line_only_content_source_copy",
            "slate_subject_line_only_content_source",
            "slate_subject_line_only_content_source_copy",
            "email_template_content_source",
            "email_template_content_source_copy",
            "repurpose_template_content_source_copy",
            "slate_repurpose_template_content_source",
            "slate_repurpose_template_content_source_copy",
            "template_settings",
        ]
        for field in template_fields:
            self.content_group_params.pop(field, None)

        # Convert template fields
        for field_key, field in self.template.template_fields.items():
            if field.template_component_type in [
                TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED,
                TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY,
            ]:
                if field.content_source:
                    self.content_group_params["content_source"] = field.content_source
                if field.content_source_copy:
                    self.content_group_params[
                        "repurpose_template_content_source_copy"
                    ] = field.content_source_copy
                if field.content_source_format:
                    self.content_group_params["content_source_format"] = (
                        field.content_source_format
                    )
                if field.content_source_upload_method:
                    self.content_group_params["content_source_upload_method"] = (
                        field.content_source_upload_method
                    )
                if field.slate_content_source:
                    self.content_group_params[
                        "slate_repurpose_template_content_source"
                    ] = field.slate_content_source
                if field.slate_content_source_copy:
                    self.content_group_params[
                        "slate_repurpose_template_content_source_copy"
                    ] = field.slate_content_source_copy

            elif (
                field.template_component_type
                == TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
            ):
                self.content_group_params["subject_line_only_content_source"] = (
                    field.content_source
                )
                self.content_group_params["subject_line_only_content_source_copy"] = (
                    field.content_source_copy
                )
                self.content_group_params["slate_subject_line_only_content_source"] = (
                    field.slate_content_source
                )
                self.content_group_params[
                    "slate_subject_line_only_content_source_copy"
                ] = field.slate_content_source_copy
                self.content_group_params["subject_line_only_content_source_format"] = (
                    field.content_source_format
                )
                self.content_group_params[
                    "subject_line_only_content_source_upload_method"
                ] = field.content_source_upload_method
            else:
                logging.error(
                    f"Unsupported template component type: {field.template_component_type}"
                )
                raise ValueError(
                    f"Unsupported template component type: {field.template_component_type}"
                )

        # Convert template settings
        template_settings = {}
        if self.template.HasField("tone_reference"):
            assets_data = AssetsV3ToV2Converter(
                self.template.tone_reference
            ).convert_assets_v3_to_v2()
            template_settings["tone_reference"] = [{"assets": assets_data}]

        template_settings.update(
            {
                "follow_tone": self.template.follow_template_tone,
                "follow_length": self.template.follow_template_length,
                "follow_core_message_and_key_point": self.template.follow_template_core_message_and_key_point,
            }
        )

        if any(template_settings.values()):
            self.content_group_params["template_settings"] = template_settings

        return self.content_group_params


class RepurposeTemplateConvertComparisor:
    def compare(
        self,
        content_group_params: Dict[str, Any],
        content_group_params_converted: Dict[str, Any],
    ) -> bool:
        differences = self._compare_template_fields(
            content_group_params, content_group_params_converted
        )
        if differences:
            for diff in differences:
                logging.error(diff)
            return False
        return True

    def _compare_template_fields(
        self, original: Dict[str, Any], converted: Dict[str, Any]
    ) -> List[str]:
        differences = []
        template_fields = [
            "content_source",
            "content_source_copy",
            "content_source_format",
            "content_source_upload_method",
            "subject_line_only_content_source",
            "subject_line_only_content_source_copy",
            "email_template_content_source",
            "email_template_content_source_copy",
            "repurpose_template_content_source_copy",
            "slate_repurpose_template_content_source",
            "slate_repurpose_template_content_source_copy",
            "template_settings",
        ]

        for field in template_fields:
            orig_value = original.get(field)
            conv_value = converted.get(field)

            # Skip comparison if both values are empty/None
            if not orig_value and not conv_value:
                continue

            # Special handling for template_settings
            if field == "template_settings" and orig_value and conv_value:
                # Remove None values from original dict
                orig_settings = {k: v for k, v in orig_value.items() if v is not None}
                conv_settings = {k: v for k, v in conv_value.items()}

                if orig_settings != conv_settings:
                    differences.append(f"Field '{field}': {orig_value} vs {conv_value}")
                continue

            if orig_value != conv_value:
                differences.append(f"Field '{field}': {orig_value} vs {conv_value}")

        return differences


def convert_repurpose_template_v2_to_v3(
    playbook_id: int, content_group_params: Dict[str, Any]
) -> Optional[TofuTemplate]:
    """Convert v2 template format to v3."""
    converter = RepurposeTemplateV2ToV3Converter(
        playbook_id=playbook_id, content_group_params=content_group_params
    )
    return converter.convert_repurpose_template_v2_to_v3()


def convert_repurpose_template_v3_to_v2(
    template: TofuTemplate, content_group_params: Dict[str, Any]
) -> Dict[str, Any]:
    """Convert v3 template format to v2."""
    converter = RepurposeTemplateV3ToV2Converter(
        template=template, content_group_params=content_group_params
    )
    return converter.convert_repurpose_template_v3_to_v2()
