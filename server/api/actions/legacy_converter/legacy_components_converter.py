import logging
from typing import Any, Dict

from pydantic import ValidationError

from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuComponent,
    TofuComponentContextData,
    TofuComponentContextDataWithFormatForHtml,
    TofuComponentContextDataWithFormatForPdf,
    TofuComponentDataImage,
    TofuComponentDataLink,
    TofuComponentDataText,
    TofuComponentDataVideo,
    TofuComponentMetaType,
    TofuComponents,
    TofuComponentType,
    TofuDataList,
)
from ...validator.content_group_validator import (
    Assets,
    ComponentMetaType,
    ComponentParams,
    ComponentType,
    ContentGroupComponent,
    ContentGroupComponentMeta,
    ContentGroupImageComponent,
    ContentGroupLinkComponent,
    ContentGroupVideoComponent,
    CustomInstruction,
)
from .legacy_custom_instruction_converter import (
    CustomInstructionConvertComparisor,
    CustomInstructionV2ToV3Converter,
    CustomInstructionV3ToV2Converter,
)


class ComponentV2ToV3Converter:
    def __init__(self, playbook_id: int, components_v2: dict):
        self.playbook_id = playbook_id
        self.components_v2 = components_v2

    def _convert_component_type(
        self, component_type: ComponentType
    ) -> TofuComponentType:
        """Convert validator ComponentType to protobuf TofuComponentType."""
        mapping = {
            ComponentType.UNSPECIFIED: TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED,
            ComponentType.EMAIL_SUBJECT: TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT,
            ComponentType.EMAIL_BODY: TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY,
            ComponentType.HEADLINE: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_HEADLINE,
            ComponentType.DESCRIPTION: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_DESCRIPTION,
            ComponentType.INTRODUCTORY_TEXT: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_INTRODUCTORY_TEXT,
            ComponentType.AD_COPY: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_AD_COPY,
            ComponentType.HTML_EDIT: TofuComponentType.TOFU_COMPONENT_TYPE_HTML_EDIT,
        }
        if component_type not in mapping:
            return TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
        return mapping[component_type]

    def _convert_meta_type(
        self, meta_type: ComponentMetaType | None
    ) -> TofuComponentMetaType:
        if meta_type is None:
            return TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_TEXT

        """Convert validator ComponentMetaType to protobuf TofuComponentMetaType."""
        mapping = {
            ComponentMetaType.TEXT: TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_TEXT,
            ComponentMetaType.IMAGE: TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_IMAGE,
            ComponentMetaType.LINK: TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_LINK,
            ComponentMetaType.ANCHOR: TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_ANCHOR,
            ComponentMetaType.VIDEO: TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_VIDEO,
        }
        if meta_type not in mapping:
            raise ValueError(f"Invalid meta type: {meta_type}")
        return mapping[meta_type]

    def _convert_component_v2_to_v3(self, component_v2: Dict[str, Any]):
        # Create new component with meta fields directly
        new_component = TofuComponent(
            time_added=component_v2["meta"].get("time_added") or 0,
            component_type=self._convert_component_type(
                component_v2["meta"].get("component_type", ComponentType.UNSPECIFIED)
            ),
            component_meta_type=self._convert_meta_type(
                component_v2["meta"].get("type", None)
            ),
            component_context_data=TofuComponentContextData(
                selected_element=component_v2["meta"].get("selected_element", "")
            ),
        )

        # Add context data
        preceding_element = component_v2["meta"].get(
            "precedingContent", ""
        ) or component_v2["meta"].get("preceding_element", "")
        if preceding_element:
            if not isinstance(preceding_element, str):
                raise ValueError(
                    f"preceding_element is not a string: {preceding_element} for {component_v2}"
                )
            new_component.component_context_data.preceding_element = preceding_element

        succeeding_element = component_v2["meta"].get(
            "succeedingContent", ""
        ) or component_v2["meta"].get("succeeding_element", "")
        if succeeding_element:
            if not isinstance(succeeding_element, str):
                raise ValueError(
                    f"succeeding_element is not a string: {succeeding_element}"
                )
            new_component.component_context_data.succeeding_element = succeeding_element

        # Handle HTML and PDF context data
        if "html_tag" in component_v2["meta"]:
            new_component.component_context_data.html.html_tag = component_v2["meta"][
                "html_tag"
            ]
            # Add html_tag_index if it exists
            if "html_tag_index" in component_v2["meta"]:
                html_tag_index = component_v2["meta"]["html_tag_index"]
                if html_tag_index is not None:
                    new_component.component_context_data.html.html_tag_index = (
                        html_tag_index
                    )
        if "pageNum" in component_v2["meta"]:
            # Check if pdf attribute exists
            if hasattr(new_component.component_context_data, "pdf"):
                pdf_data = new_component.component_context_data.pdf
                if (
                    "pageNum" in component_v2["meta"]
                    and component_v2["meta"]["pageNum"] is not None
                ):
                    pdf_data.page_num = component_v2["meta"]["pageNum"]
                if (
                    "numLines" in component_v2["meta"]
                    and component_v2["meta"]["numLines"] is not None
                ):
                    pdf_data.num_lines = component_v2["meta"]["numLines"]
                if (
                    "boundingBox" in component_v2["meta"]
                    and component_v2["meta"]["boundingBox"] is not None
                ):
                    if (
                        "top" in component_v2["meta"]["boundingBox"]
                        and component_v2["meta"]["boundingBox"]["top"] is not None
                    ):
                        pdf_data.bounding_box.top = component_v2["meta"]["boundingBox"][
                            "top"
                        ]
                    if (
                        "left" in component_v2["meta"]["boundingBox"]
                        and component_v2["meta"]["boundingBox"]["left"] is not None
                    ):
                        pdf_data.bounding_box.left = component_v2["meta"][
                            "boundingBox"
                        ]["left"]
                    if (
                        "right" in component_v2["meta"]["boundingBox"]
                        and component_v2["meta"]["boundingBox"]["right"] is not None
                    ):
                        pdf_data.bounding_box.right = component_v2["meta"][
                            "boundingBox"
                        ]["right"]
                    if (
                        "width" in component_v2["meta"]["boundingBox"]
                        and component_v2["meta"]["boundingBox"]["width"] is not None
                    ):
                        pdf_data.bounding_box.width = component_v2["meta"][
                            "boundingBox"
                        ]["width"]
                    if (
                        "bottom" in component_v2["meta"]["boundingBox"]
                        and component_v2["meta"]["boundingBox"]["bottom"] is not None
                    ):
                        pdf_data.bounding_box.bottom = component_v2["meta"][
                            "boundingBox"
                        ]["bottom"]
                    if (
                        "height" in component_v2["meta"]["boundingBox"]
                        and component_v2["meta"]["boundingBox"]["height"] is not None
                    ):
                        pdf_data.bounding_box.height = component_v2["meta"][
                            "boundingBox"
                        ]["height"]
                if (
                    "avgCharWidth" in component_v2["meta"]
                    and component_v2["meta"]["avgCharWidth"] is not None
                ):
                    pdf_data.avg_char_width = component_v2["meta"]["avgCharWidth"]
                if (
                    "avgLineSpace" in component_v2["meta"]
                    and component_v2["meta"]["avgLineSpace"] is not None
                ):
                    pdf_data.avg_line_space = component_v2["meta"]["avgLineSpace"]
                if (
                    "charCapacity" in component_v2["meta"]
                    and component_v2["meta"]["charCapacity"] is not None
                ):
                    pdf_data.char_capacity = component_v2["meta"]["charCapacity"]
                if (
                    "avgCharHeight" in component_v2["meta"]
                    and component_v2["meta"]["avgCharHeight"] is not None
                ):
                    pdf_data.avg_char_height = component_v2["meta"]["avgCharHeight"]
            else:
                raise AttributeError(
                    "pdf attribute is missing in component_context_data"
                )

        # Add custom instructions if present
        component_params = component_v2["meta"].get("component_params")
        if (
            component_params is not None
            and component_params.get("custom_instructions") is not None
        ):
            if "custom_instructions" in component_params:
                custom_instructions_v2 = component_params["custom_instructions"]
                custom_instructions_v3 = CustomInstructionV2ToV3Converter(
                    self.playbook_id, custom_instructions_v2
                ).convert_custom_instructions_v2_to_v3()

                # Use CopyFrom to assign the value
                new_component.component_custom_instructions.CopyFrom(
                    custom_instructions_v3
                )

        # Set component data based on type
        if "image" in component_v2:
            image = component_v2["image"]
            new_component.image.width = image["width"]
            new_component.image.height = image["height"]
            new_component.image.url = image["url"]
            new_component.image.alt = image.get("alt", "")
            new_component.image.srcset = image.get("srcset", "")
            new_component.image.loading = image.get("loading", "")
            new_component.image.styles.update(image.get("styles", {}))
        elif "link" in component_v2:
            link = component_v2["link"]
            new_component.link.text = link.get("text") or component_v2.get("text", "")
            new_component.link.href = link.get("href", "")
            new_component.link.path = link.get("path", "")
            new_component.link.type = link.get("type", "")
            new_component.link.label = link.get("label", "")
            if "contentGroupId" in link:
                new_component.link.content_group_id = link["contentGroupId"]
            new_component.link.url = link.get("url", "")
        elif "video" in component_v2:
            video = component_v2["video"]
            new_component.video.url = video["url"]
            new_component.video.title = video.get("title", "")
        elif "anchor" in component_v2:  # New condition for anchor
            anchor = component_v2["anchor"]
            new_component.anchor.href = anchor["href"]
            new_component.anchor.text = anchor["text"]
        elif "text" in component_v2:
            new_component.text.text = component_v2["text"]
        else:
            raise ValueError("Unknown component type")
        return new_component

    def convert_components_v2_to_v3(self):
        # Validate components_v2 using the validator
        if not isinstance(self.components_v2, dict):
            logging.error("components_v2 must be a dictionary")
            return TofuComponents()

        for key, component in self.components_v2.items():
            try:
                # Validate each component using the ContentGroupComponent validator
                ContentGroupComponent.model_validate(component)
            except ValidationError as e:
                logging.error(f"Invalid component for key '{key}': {str(e)}")
                # raise ValueError(f"Invalid component for key '{key}': {str(e)}")

        # Create new TofuComponents
        components_v3 = TofuComponents()
        for key, component in self.components_v2.items():
            new_component = self._convert_component_v2_to_v3(component)
            components_v3.components[key].CopyFrom(new_component)
        return components_v3


class ComponentV3ToV2Converter:
    def __init__(self, components_v3: TofuComponents):
        self.components_v3 = components_v3

    def _convert_component_type(
        self, component_type: TofuComponentType
    ) -> ComponentType:
        """Convert protobuf TofuComponentType to validator ComponentType."""
        mapping = {
            TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED: ComponentType.UNSPECIFIED,
            TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT: ComponentType.EMAIL_SUBJECT,
            TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY: ComponentType.EMAIL_BODY,
            TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_HEADLINE: ComponentType.HEADLINE,
            TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_DESCRIPTION: ComponentType.DESCRIPTION,
            TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_INTRODUCTORY_TEXT: ComponentType.INTRODUCTORY_TEXT,
            TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_AD_COPY: ComponentType.AD_COPY,
            TofuComponentType.TOFU_COMPONENT_TYPE_HTML_EDIT: ComponentType.HTML_EDIT,
        }
        if component_type not in mapping:
            return ComponentType.UNSPECIFIED
        return mapping[component_type]

    def _convert_meta_type(self, meta_type: TofuComponentMetaType) -> ComponentMetaType:
        """Convert protobuf TofuComponentMetaType to validator ComponentMetaType."""
        mapping = {
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_TEXT: ComponentMetaType.TEXT,
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_IMAGE: ComponentMetaType.IMAGE,
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_LINK: ComponentMetaType.LINK,
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_ANCHOR: ComponentMetaType.ANCHOR,
            TofuComponentMetaType.TOFU_COMPONENT_META_TYPE_VIDEO: ComponentMetaType.VIDEO,
        }
        if meta_type not in mapping:
            raise ValueError(f"Invalid meta type: {meta_type}")
        return mapping[meta_type]

    def _convert_component_v3_to_v2(self, component: TofuComponent):
        # Convert meta information
        meta_dict = {
            "time_added": component.time_added,
            "component_type": self._convert_component_type(
                component.component_type
            ).value,  # Get string value
            "type": self._convert_meta_type(
                component.component_meta_type
            ).value,  # Get string value
            "isEmailSubject": component.component_type
            == TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT,
            "preceding_element": (
                component.component_context_data.preceding_element
                if component.HasField("component_context_data")
                else ""
            ),
            "succeeding_element": (
                component.component_context_data.succeeding_element
                if component.HasField("component_context_data")
                else ""
            ),
            "html_tag": (
                component.component_context_data.html.html_tag
                if component.HasField("component_context_data")
                and component.component_context_data.HasField("html")
                else ""
            ),
            "html_tag_index": (
                component.component_context_data.html.html_tag_index
                if component.HasField("component_context_data")
                and component.component_context_data.HasField("html")
                else None
            ),
            "selected_element": (
                component.component_context_data.selected_element
                if component.HasField("component_context_data")
                else ""
            ),
            # Add missing fields
            "pageNum": (
                component.component_context_data.pdf.page_num
                if component.component_context_data.HasField("pdf")
                else None
            ),
            "numLines": (
                component.component_context_data.pdf.num_lines
                if component.component_context_data.HasField("pdf")
                else None
            ),
            "avgCharWidth": (
                component.component_context_data.pdf.avg_char_width
                if component.component_context_data.HasField("pdf")
                else None
            ),
            "avgLineSpace": (
                component.component_context_data.pdf.avg_line_space
                if component.component_context_data.HasField("pdf")
                else None
            ),
            "charCapacity": (
                component.component_context_data.pdf.char_capacity
                if component.component_context_data.HasField("pdf")
                else None
            ),
            "avgCharHeight": (
                component.component_context_data.pdf.avg_char_height
                if component.component_context_data.HasField("pdf")
                else None
            ),
            "boundingBox": (
                {
                    "top": component.component_context_data.pdf.bounding_box.top,
                    "left": component.component_context_data.pdf.bounding_box.left,
                    "right": component.component_context_data.pdf.bounding_box.right,
                    "width": component.component_context_data.pdf.bounding_box.width,
                    "bottom": component.component_context_data.pdf.bounding_box.bottom,
                    "height": component.component_context_data.pdf.bounding_box.height,
                }
                if component.component_context_data.HasField("pdf")
                else None
            ),
        }
        custom_instructions_v2 = CustomInstructionV3ToV2Converter(
            component.component_custom_instructions
        ).convert_custom_instructions_v3_to_v2()
        meta_dict["component_params"] = {"custom_instructions": custom_instructions_v2}

        # Create component dictionary based on type
        component_dict = {"meta": meta_dict}

        if component.HasField("text"):
            component_dict["text"] = component.text.text
        elif component.HasField("image"):
            component_dict["image"] = {
                "width": component.image.width,
                "height": component.image.height,
                "url": component.image.url,
                "alt": component.image.alt,
                "srcset": component.image.srcset,
                "loading": component.image.loading,
                "styles": (
                    dict(component.image.styles) if component.image.styles else {}
                ),
            }
        elif component.HasField("link"):
            component_dict["link"] = {
                "href": component.link.href,
                "path": component.link.path,
                "type": component.link.type,
                "label": component.link.label,
                "url": component.link.url,
                "text": component.link.text,
            }
            if component.link.content_group_id:
                component_dict["link"][
                    "contentGroupId"
                ] = component.link.content_group_id
            if component.link.text:
                component_dict["text"] = component.link.text
        elif component.HasField("video"):
            component_dict["video"] = {
                "url": component.video.url,
                "title": component.video.title,
            }
        elif component.HasField("anchor"):
            component_dict["anchor"] = {
                "href": component.anchor.href,
                "text": component.anchor.text,
            }
            if component.anchor.text:
                component_dict["text"] = component.anchor.text

        return component_dict

    def convert_components_v3_to_v2(self):
        if not isinstance(self.components_v3, TofuComponents):
            raise ValueError("components_v3 must be a TofuComponents object")

        components_v2 = {}

        for key, component in self.components_v3.components.items():
            components_v2[key] = self._convert_component_v3_to_v2(component)

        return components_v2


class ComponentConvertComparisor:
    def compare(self, components_v2, components_v2_converted):
        differences = self._compare_components(components_v2, components_v2_converted)
        if differences:
            logging.error(f"differences found between v2: {components_v2}")
            logging.error(f"and v2_converted: {components_v2_converted}")
            for diff in differences:
                logging.error(diff)
            return False
        return True

    def _compare_components(self, components_v2, components_v2_converted):
        if not components_v2 and not components_v2_converted:
            return []
        if not components_v2:
            logging.error(
                "components_v2 is empty but components_v2_converted is not as {}".format(
                    components_v2_converted
                )
            )
            return ["components_v2 is empty but components_v2_converted is not"]
        if not components_v2_converted:
            logging.error(
                "components_v2_converted is empty but components_v2 is not as {}".format(
                    components_v2
                )
            )
            return ["components_v2_converted is empty but components_v2 is not"]
        differences = []
        for key in components_v2:
            if key not in components_v2_converted:
                differences.append(f"Missing key '{key}' in converted components")
                continue

            comp_v2 = components_v2[key]
            comp_v2_conv = components_v2_converted[key]

            # Compare meta fields
            if "meta" in comp_v2 and "meta" in comp_v2_conv:
                meta_diffs = self._compare_meta_fields(
                    comp_v2["meta"], comp_v2_conv["meta"]
                )
                if meta_diffs:
                    differences.extend(
                        [f"Component '{key}': {diff}" for diff in meta_diffs]
                    )

            # Compare link fields
            if "link" in comp_v2 or "link" in comp_v2_conv:
                link_diffs = self._compare_link_fields(
                    comp_v2.get("link", {}), comp_v2_conv.get("link", {})
                )
                if link_diffs:
                    differences.extend(
                        [f"Component '{key}': {diff}" for diff in link_diffs]
                    )
                continue

            # Compare other fields
            other_diffs = self._compare_other_fields(comp_v2, comp_v2_conv)
            if other_diffs:
                differences.extend(
                    [f"Component '{key}': {diff}" for diff in other_diffs]
                )

        return differences

    def _compare_meta_fields(self, meta_v2, meta_v2_conv):
        differences = []
        v2_meta = meta_v2.copy()
        v2_conv_meta = meta_v2_conv.copy()

        # Handle special cases
        if "type" not in v2_meta and v2_conv_meta.get("type") == "text":
            v2_conv_meta.pop("type", None)

        if (
            v2_meta.get("component_type") == "body"
            and v2_conv_meta.get("component_type") == "unspecified"
        ):
            v2_conv_meta["component_type"] = "body"

        # New condition to treat None and unspecified as equivalent
        if (
            v2_meta.get("component_type") is None
            and v2_conv_meta.get("component_type") == "unspecified"
        ) or (
            v2_meta.get("component_type") == "unspecified"
            and v2_conv_meta.get("component_type") is None
        ):
            v2_meta["component_type"] = "unspecified"
            v2_conv_meta["component_type"] = "unspecified"

        # Special case for boundingBox
        if "boundingBox" in v2_meta or "boundingBox" in v2_conv_meta:
            bbox1 = v2_meta.get("boundingBox")
            bbox2 = v2_conv_meta.get("boundingBox")

            if (bbox1 is None and self._is_zero_bounding_box(bbox2)) or (
                bbox2 is None and self._is_zero_bounding_box(bbox1)
            ):
                # Remove both to prevent further comparison
                v2_meta.pop("boundingBox", None)
                v2_conv_meta.pop("boundingBox", None)

        if "precedingContent" in v2_meta:
            v2_meta["preceding_element"] = v2_meta["precedingContent"]
            v2_meta.pop("precedingContent", None)
        if "succeedingContent" in v2_meta:
            v2_meta["succeeding_element"] = v2_meta["succeedingContent"]
            v2_meta.pop("succeedingContent", None)

        if "component_params" not in v2_meta:
            v2_meta["component_params"] = {}
        if "custom_instructions" not in v2_meta["component_params"]:
            v2_meta["component_params"]["custom_instructions"] = []
        if "component_params" not in v2_conv_meta:
            v2_conv_meta["component_params"] = {}
        if "custom_instructions" not in v2_conv_meta["component_params"]:
            v2_conv_meta["component_params"]["custom_instructions"] = []
        custom_instructions_diffs = self._compare_component_params(
            v2_meta["component_params"], v2_conv_meta["component_params"]
        )
        if custom_instructions_diffs:
            differences.extend(custom_instructions_diffs)

        # Compare all meta fields
        for meta_key in set(v2_meta.keys()) | set(v2_conv_meta.keys()):
            if meta_key == "component_params":
                continue
            v2_value = v2_meta.get(meta_key) or None
            v2_conv_value = v2_conv_meta.get(meta_key) or None

            # Treat None and empty dictionaries as equivalent
            if v2_value in (None, {}) and v2_conv_value in (None, {}):
                continue

            # Allow for small differences in floating-point values
            if isinstance(v2_value, float) and isinstance(v2_conv_value, float):
                if abs(v2_value - v2_conv_value) < 1e-3:
                    continue

            # Handle nested dictionaries like boundingBox
            if isinstance(v2_value, dict) and isinstance(v2_conv_value, dict):
                nested_diffs = self._compare_nested_dicts(
                    v2_value, v2_conv_value, f"meta.{meta_key}"
                )
                differences.extend(nested_diffs)
                continue

            if v2_value != v2_conv_value:
                differences.append(f"meta.{meta_key}: {v2_value} vs {v2_conv_value}")

        return differences

    def _compare_component_params(
        self, component_params_v2, component_params_v2_converted
    ):
        differences = []
        # there shall not be other key in component_params_v2 and component_params_v2_converted except custom_instructions
        for key in component_params_v2:
            if key == "assets":
                if len(component_params_v2[key]) > 0:
                    differences.append(f"component_params_v2 has extra key {key}")
            elif key != "custom_instructions":
                differences.append(f"component_params_v2 has extra key {key}")
        for key in component_params_v2_converted:
            if key != "custom_instructions":
                differences.append(f"component_params_v2_converted has extra key {key}")
        custom_instruction_comparisor = CustomInstructionConvertComparisor()
        differences.extend(
            custom_instruction_comparisor.compare(
                component_params_v2.get("custom_instructions", []),
                component_params_v2_converted.get("custom_instructions", []),
            )
        )
        return differences

    def _compare_link_fields(self, link_v2, link_v2_conv):
        if "text" not in link_v2 and "text" in link_v2_conv:
            link_v2["text"] = link_v2_conv["text"]
        differences = []
        # Remove empty strings from converted link
        link_v2_clean = {k: v for k, v in link_v2.items() if v}
        link_v2_conv_clean = {k: v for k, v in link_v2_conv.items() if v != ""}

        if link_v2_clean != link_v2_conv_clean:
            differences.append(f"link: {link_v2_clean} vs {link_v2_conv_clean}")
        return differences

    def _compare_other_fields(self, comp_v2, comp_v2_conv):
        differences = []
        for field in set(comp_v2.keys()) | set(comp_v2_conv.keys()):
            if field in ("meta", "link"):
                continue
            v2_value = comp_v2.get(field)
            v2_conv_value = comp_v2_conv.get(field)

            # Recursively compare nested dictionaries
            if isinstance(v2_value, dict) and isinstance(v2_conv_value, dict):
                differences.extend(
                    self._compare_nested_dicts(v2_value, v2_conv_value, field)
                )
                continue

            # Treat missing fields and empty values as equivalent
            if self._are_equivalent(v2_value, v2_conv_value):
                continue

            if v2_value != v2_conv_value:
                differences.append(f"{field}: {v2_value} vs {v2_conv_value}")
        return differences

    def _is_zero_bounding_box(self, bbox):
        """Check if a bounding box is all zeros or very close to zero values."""
        if not isinstance(bbox, dict):
            return False

        expected_keys = ["top", "left", "right", "width", "bottom", "height"]
        # Check if all expected keys exist
        if not all(key in bbox for key in expected_keys):
            return False

        # Check if all values are zero or very close to zero
        return all(abs(float(bbox.get(key, 1.0))) < 1e-5 for key in expected_keys)

    def _compare_nested_dicts(self, dict1, dict2, parent_field):
        differences = []

        # Special case for boundingBox
        if parent_field == "meta.boundingBox":
            # If one is None and the other is an all-zero bounding box, consider them equivalent
            if (dict1 is None and self._is_zero_bounding_box(dict2)) or (
                dict2 is None and self._is_zero_bounding_box(dict1)
            ):
                return differences

        for key in set(dict1.keys()) | set(dict2.keys()):
            value1 = dict1.get(key)
            value2 = dict2.get(key)

            # Allow for small differences in floating-point values
            if isinstance(value1, float) and isinstance(value2, float):
                if abs(value1 - value2) < 1e-3:  # Adjust the tolerance as needed
                    continue
            if value1 in (None, {}) and value2 in (None, {}):
                continue

            if value1 != value2:
                differences.append(f"{parent_field}.{key}: {value1} vs {value2}")
        return differences

    def _are_equivalent(self, value1, value2):
        # Treat missing fields and empty dictionaries/strings as equivalent
        if (value1 is None and value2 in ({}, "")) or (
            value1 in ({}, "") and value2 is None
        ):
            return True

        # Check if one is None and the other is an all-zero bounding box
        if value1 is None and self._is_zero_bounding_box(value2):
            return True
        if value2 is None and self._is_zero_bounding_box(value1):
            return True

        return False


def convert_components_v2_to_v3(playbook_id: int, components_v2, compare=False):
    converter = ComponentV2ToV3Converter(playbook_id, components_v2)
    components_v3 = converter.convert_components_v2_to_v3()
    if compare:
        reverse_converter = ComponentV3ToV2Converter(components_v3)
        components_v2_converted = reverse_converter.convert_components_v3_to_v2()
        comparisor = ComponentConvertComparisor()
        comparisor.compare(components_v2, components_v2_converted)
    return components_v3


def convert_components_v3_to_v2(components_v3: TofuComponents):
    if not components_v3:
        return {}
    if isinstance(components_v3, TofuDataList):
        if len(components_v3.data) == 0:
            return {}
        components_v3 = components_v3.data[0].components
    if not isinstance(components_v3, TofuComponents):
        raise ValueError(f"Invalid components type: {type(components_v3)}")
    converter = ComponentV3ToV2Converter(components_v3)
    return converter.convert_components_v3_to_v2()


def test_convert_components_v2_to_v3(components_v2):
    components_v3 = convert_components_v2_to_v3(components_v2)
    components_v2_converted = convert_components_v3_to_v2(components_v3)
    comparisor = ComponentConvertComparisor()
    return comparisor.compare(components_v2, components_v2_converted)
