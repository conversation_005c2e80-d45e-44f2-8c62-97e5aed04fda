import copy
import logging
from typing import Any, Dict, List, Optional

from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuAsset,
    TofuComponentType,
    TofuDataList,
    TofuTemplate,
    TofuTemplateField,
)
from ...shared_types import ContentType
from ...validator.content_group_validator import ComponentType
from .legacy_custom_instruction_converter import (
    AssetsV2ToV3Converter,
    AssetsV3ToV2Converter,
)


class TemplateV2ToV3Converter:
    def __init__(self, playbook_id: int, content_group_params: Dict[str, Any]):
        self.playbook_id = playbook_id
        self.content_group_params = content_group_params

    def _convert_component_type(self, component_type: str) -> TofuComponentType:
        """Convert string component type to protobuf TofuComponentType."""
        mapping = {
            ComponentType.EMAIL_SUBJECT: TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT,
            ComponentType.EMAIL_BODY: TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY,
            ComponentType.HEADLINE: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_HEADLINE,
            ComponentType.DESCRIPTION: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_DESCRIPTION,
            ComponentType.INTRODUCTORY_TEXT: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_INTRODUCTORY_TEXT,
            ComponentType.AD_COPY: TofuComponentType.TOFU_COMPONENT_TYPE_LINKEDIN_AD_AD_COPY,
        }
        return mapping.get(
            component_type, TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
        )

    def _convert_linkedin_ad_template(self) -> TofuTemplate:
        return self._convert_generic_template()

    def _convert_email_template(self) -> TofuTemplate:
        template = TofuTemplate()

        template_field_type_subject = (
            TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
        )
        field_subject = TofuTemplateField()
        field_subject.template_component_type = (
            TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
        )
        if (
            "subject_line_only_content_source" in self.content_group_params
            and self.content_group_params["subject_line_only_content_source"]
            is not None
        ):
            field_subject.content_source = self.content_group_params.get(
                "subject_line_only_content_source", ""
            )
        if (
            "subject_line_only_content_source_copy" in self.content_group_params
            and self.content_group_params["subject_line_only_content_source_copy"]
            is not None
        ):
            field_subject.content_source_copy = self.content_group_params.get(
                "subject_line_only_content_source_copy", ""
            )
        if (
            "subject_line_only_content_source_format" in self.content_group_params
            and self.content_group_params["subject_line_only_content_source_format"]
            is not None
        ):
            field_subject.content_source_format = self.content_group_params.get(
                "subject_line_only_content_source_format", ""
            )
        if (
            "subject_line_only_content_source_upload_method"
            in self.content_group_params
            and self.content_group_params[
                "subject_line_only_content_source_upload_method"
            ]
            is not None
        ):
            field_subject.content_source_upload_method = self.content_group_params.get(
                "subject_line_only_content_source_upload_method", ""
            )

        template_field_type_body = TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
        field_body = TofuTemplateField()
        field_body.template_component_type = template_field_type_body
        if (
            "content_source" in self.content_group_params
            and self.content_group_params["content_source"] is not None
        ):
            field_body.content_source = self.content_group_params.get(
                "content_source", ""
            )
        if (
            "content_source_copy" in self.content_group_params
            and self.content_group_params["content_source_copy"] is not None
        ):
            field_body.content_source_copy = self.content_group_params.get(
                "content_source_copy", ""
            )
        if (
            "content_source_format" in self.content_group_params
            and self.content_group_params["content_source_format"] is not None
        ):
            field_body.content_source_format = self.content_group_params.get(
                "content_source_format", ""
            )
        if (
            "content_source_upload_method" in self.content_group_params
            and self.content_group_params["content_source_upload_method"] is not None
        ):
            field_body.content_source_upload_method = self.content_group_params.get(
                "content_source_upload_method", ""
            )

        template.template_fields[
            TofuComponentType.Name(field_subject.template_component_type)
        ].CopyFrom(field_subject)
        template.template_fields[
            TofuComponentType.Name(field_body.template_component_type)
        ].CopyFrom(field_body)

        return template

    def _convert_generic_template(self) -> TofuTemplate:
        template = TofuTemplate()
        template_field_type = TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED

        field = TofuTemplateField()
        field.template_component_type = template_field_type
        if (
            "content_source" in self.content_group_params
            and self.content_group_params["content_source"] is not None
        ):
            field.content_source = self.content_group_params.get("content_source", "")
        if (
            "content_source_copy" in self.content_group_params
            and self.content_group_params["content_source_copy"] is not None
        ):
            field.content_source_copy = self.content_group_params.get(
                "content_source_copy", ""
            )
        if (
            "content_source_format" in self.content_group_params
            and self.content_group_params["content_source_format"] is not None
        ):
            field.content_source_format = self.content_group_params.get(
                "content_source_format", ""
            )
        if (
            "content_source_upload_method" in self.content_group_params
            and self.content_group_params["content_source_upload_method"] is not None
        ):
            field.content_source_upload_method = self.content_group_params.get(
                "content_source_upload_method", ""
            )

        template.template_fields[
            TofuComponentType.Name(field.template_component_type)
        ].CopyFrom(field)

        return template

    def convert_template_v2_to_v3(self) -> Optional[TofuTemplate]:

        template = None
        content_type = self.content_group_params.get("content_type")
        if content_type == ContentType.AdCampaignLinkedin:
            template = self._convert_linkedin_ad_template()
        elif (
            content_type == ContentType.EmailMarketing
            or content_type == ContentType.EmailSDR
        ):
            template = self._convert_email_template()
        else:
            template = self._convert_generic_template()

        if template is None:
            raise ValueError(f"Invalid content type: {content_type}")

        # Handle template settings if they exist
        if "template_settings" in self.content_group_params:
            settings = self.content_group_params["template_settings"]
            if settings:
                template.follow_template_tone = settings.get("follow_tone", False)
                template.follow_template_length = settings.get("follow_length", False)
                template.follow_template_core_message_and_key_point = settings.get(
                    "follow_core_message_and_key_point", False
                )

                # Handle tone reference
                if settings.get("tone_reference"):
                    for ref in settings["tone_reference"]:
                        if ref.get("assets"):
                            assets_data = AssetsV2ToV3Converter(
                                playbook_id=self.playbook_id, assets_v2=ref["assets"]
                            ).convert_assets_v2_to_v3()
                            template.tone_reference.CopyFrom(assets_data)
                            break

        return template


class TemplateV3ToV2Converter:
    def __init__(self, template: TofuTemplate, content_group_params: Dict[str, Any]):
        if isinstance(template, TofuDataList):
            self.template = template.data[0].template
        else:
            self.template = template
        self.content_group_params = content_group_params

    def convert_template_v3_to_v2(self) -> Dict[str, Any]:
        # Remove existing template-related fields
        template_fields = [
            "content_source",
            "content_source_copy",
            "content_source_format",
            "content_source_upload_method",
            "subject_line_only_content_source",
            "subject_line_only_content_source_copy",
        ]
        for field in template_fields:
            self.content_group_params.pop(field, None)

        # Convert template fields
        for field_key, field in self.template.template_fields.items():
            if (
                field.template_component_type
                == TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_SUBJECT
            ):
                self.content_group_params["subject_line_only_content_source"] = (
                    field.content_source
                )
                self.content_group_params["subject_line_only_content_source_copy"] = (
                    field.content_source_copy
                )
                self.content_group_params["subject_line_only_content_source_format"] = (
                    field.content_source_format
                )
                self.content_group_params[
                    "subject_line_only_content_source_upload_method"
                ] = field.content_source_upload_method
            elif (
                field.template_component_type
                == TofuComponentType.TOFU_COMPONENT_TYPE_EMAIL_BODY
            ):
                self.content_group_params["content_source"] = field.content_source
                self.content_group_params["content_source_copy"] = (
                    field.content_source_copy
                )
                self.content_group_params["content_source_format"] = (
                    field.content_source_format
                )
                self.content_group_params["content_source_upload_method"] = (
                    field.content_source_upload_method
                )
            elif (
                field.template_component_type
                == TofuComponentType.TOFU_COMPONENT_TYPE_UNSPECIFIED
            ):
                self.content_group_params["content_source"] = field.content_source
                self.content_group_params["content_source_copy"] = (
                    field.content_source_copy
                )
                self.content_group_params["content_source_format"] = (
                    field.content_source_format
                )
                self.content_group_params["content_source_upload_method"] = (
                    field.content_source_upload_method
                )

        # Convert template settings
        template_settings = {}
        if self.template.HasField("tone_reference"):
            assets_data = AssetsV3ToV2Converter(
                self.template.tone_reference
            ).convert_assets_v3_to_v2()
            template_settings["tone_reference"] = [{"assets": [assets_data]}]

        template_settings.update(
            {
                "follow_tone": self.template.follow_template_tone,
                "follow_length": self.template.follow_template_length,
                "follow_core_message_and_key_point": self.template.follow_template_core_message_and_key_point,
            }
        )

        if any(template_settings.values()):
            self.content_group_params["template_settings"] = template_settings

        return self.content_group_params


class TemplateConvertComparisor:
    def compare(
        self,
        content_group_params: Dict[str, Any],
        content_group_params_converted: Dict[str, Any],
    ) -> bool:
        differences = self._compare_template_fields(
            content_group_params, content_group_params_converted
        )
        if differences:
            for diff in differences:
                logging.error(diff)
            return False
        return True

    def _compare_template_fields(
        self, original: Dict[str, Any], converted: Dict[str, Any]
    ) -> List[str]:
        differences = []
        template_fields = [
            "content_source",
            "content_source_copy",
            "content_source_format",
            "content_source_upload_method",
            "subject_line_only_content_source",
            "subject_line_only_content_source_copy",
        ]

        for field in template_fields:
            orig_value = original.get(field)
            conv_value = converted.get(field)

            # Skip comparison if both values are empty/None
            if not orig_value and not conv_value:
                continue

            if orig_value != conv_value:
                differences.append(f"Field '{field}': {orig_value} vs {conv_value}")

        return differences


def convert_template_v2_to_v3(
    playbook_id: int, content_group_params: Dict[str, Any]
) -> Optional[TofuTemplate]:
    """Convert v2 template format to v3."""
    converter = TemplateV2ToV3Converter(
        playbook_id=playbook_id, content_group_params=content_group_params
    )
    return converter.convert_template_v2_to_v3()


def convert_template_v3_to_v2(
    template: TofuTemplate, content_group_params: Dict[str, Any]
) -> Dict[str, Any]:
    """Convert v3 template format to v2."""
    if isinstance(template, TofuDataList):
        template = template.data[0].template
    if not isinstance(template, TofuTemplate):
        raise ValueError(f"Invalid template type: {type(template)}")
    # Make a deep copy to avoid circular references
    params_copy = copy.deepcopy(content_group_params)
    converter = TemplateV3ToV2Converter(
        template=template, content_group_params=params_copy
    )
    return converter.convert_template_v3_to_v2()
