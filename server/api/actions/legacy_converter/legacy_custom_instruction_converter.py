import logging
from typing import Any, Dict, List, Optional, Tuple

from ...models import AssetInfo, AssetInfoGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuAsset,
    TofuCustomInstruction,
    TofuData,
    TofuDataList,
)
from ..tofu_data_wrapper import TofuDataListHandler


class AssetsV2ToV3Converter:
    def __init__(self, playbook_id: int, assets_v2: Dict[str, Any]):
        self.playbook_id = playbook_id
        self.assets_v2 = assets_v2 or {}
        if isinstance(self.assets_v2, list):
            # this data doesn't follow prev standard and we need to merge that
            assets_merged = {}
            for assets in self.assets_v2:
                if not isinstance(assets, dict):
                    logging.error(f"invalid assets: {assets}")
                    continue
                assets_merged.update(assets)
            self.assets_v2 = assets_merged

    def convert_assets_v2_to_v3(self) -> TofuDataList:
        asset_ids = []
        asset_group_ids = []
        for asset_group_key, asset_keys in self.assets_v2.items():
            if asset_keys:
                try:
                    assets = AssetInfo.objects.filter(
                        asset_info_group__playbook_id=self.playbook_id,
                        asset_info_group__asset_info_group_key=asset_group_key,
                        asset_key__in=asset_keys,
                    )
                    if len(assets) != len(asset_keys):
                        found_asset_keys = [asset.asset_key for asset in assets]
                        missing_assets = set(asset_keys) - set(found_asset_keys)
                        logging.error(f"Assets {missing_assets} do not exist")
                    asset_ids.extend([asset.id for asset in assets])
                except AssetInfo.DoesNotExist:
                    logging.error(f"Asset {asset_keys} does not exist")
            else:
                try:
                    asset_group = AssetInfoGroup.objects.get(
                        playbook_id=self.playbook_id,
                        asset_info_group_key=asset_group_key,
                    )
                    asset_group_ids.append(asset_group.id)
                except AssetInfoGroup.DoesNotExist:
                    logging.error(f"Asset group {asset_group_key} does not exist")
        return TofuDataListHandler.convert_asset_to_tofu_data(
            asset_ids, asset_group_ids
        )


class AssetsV3ToV2Converter:
    def __init__(self, tofu_data_list: TofuDataList):
        self.asset_ids, self.asset_group_ids = TofuDataListHandler.get_assets(
            tofu_data_list
        )

    def convert_assets_v3_to_v2(self) -> Dict[str, Any]:
        assets_v2 = {}
        assets = AssetInfo.objects.filter(id__in=self.asset_ids)
        asset_groups = AssetInfoGroup.objects.filter(id__in=self.asset_group_ids)
        for asset in assets:
            if asset.asset_info_group.asset_info_group_key not in assets_v2:
                assets_v2[asset.asset_info_group.asset_info_group_key] = []
            assets_v2[asset.asset_info_group.asset_info_group_key].append(
                asset.asset_key
            )
        for asset_group in asset_groups:
            if asset_group.asset_info_group_key not in assets_v2:
                assets_v2[asset_group.asset_info_group_key] = None
            else:
                logging.error(
                    f"Asset group {asset_group.asset_info_group_key} already in assets_v2: {assets_v2} for key {asset_group.asset_info_group_key}"
                )
        if not assets_v2:
            return None
        return assets_v2


class CustomInstructionV2ToV3Converter:
    def __init__(self, playbook_id: int, custom_instructions_v2: List[Dict[str, Any]]):
        self.playbook_id = playbook_id
        self.custom_instructions_v2 = custom_instructions_v2

    def convert_custom_instructions_v2_to_v3(self) -> TofuDataList:
        custom_instructions_v3 = TofuDataList()

        for instruction_v2 in self.custom_instructions_v2:
            tofu_custom_instruction = TofuCustomInstruction()

            # Convert assets
            if "assets" in instruction_v2:
                assets_v3 = AssetsV2ToV3Converter(
                    self.playbook_id, instruction_v2["assets"]
                ).convert_assets_v2_to_v3()
                tofu_custom_instruction.assets.CopyFrom(assets_v3)

            # Convert instruction
            if "instruction" in instruction_v2 and instruction_v2["instruction"]:
                tofu_custom_instruction.instruction = instruction_v2["instruction"]

            # Wrap tofu_custom_instruction in TofuData
            tofu_data = TofuData(custom_instruction=tofu_custom_instruction)

            custom_instructions_v3.data.append(tofu_data)  # Append the wrapped data

        return custom_instructions_v3


class CustomInstructionV3ToV2Converter:
    def __init__(self, custom_instructions_v3: TofuDataList):
        self.custom_instructions_v3 = custom_instructions_v3

    def convert_custom_instructions_v3_to_v2(self) -> List[Dict[str, Any]]:
        custom_instructions_v2 = []

        for instruction_v3 in self.custom_instructions_v3.data:
            instruction_v2 = {}

            # Convert assets
            assets_v2 = AssetsV3ToV2Converter(
                instruction_v3.custom_instruction.assets
            ).convert_assets_v3_to_v2()
            instruction_v2["assets"] = assets_v2
            # Convert instruction
            if instruction_v3.custom_instruction.instruction:
                instruction_v2["instruction"] = (
                    instruction_v3.custom_instruction.instruction
                )
            else:
                instruction_v2["instruction"] = None

            custom_instructions_v2.append(instruction_v2)

        return custom_instructions_v2


class AssetsConvertComparator:
    def compare(
        self,
        assets_v2: Dict[str, Any],
        assets_v2_converted: Dict[str, Any],
    ) -> List[str]:
        differences = []
        if assets_v2 == assets_v2_converted:
            return differences
        if assets_v2 in (None, {}) or assets_v2_converted in (None, {}):
            return differences
        # ignore the order of the assets
        if set(assets_v2.keys()) != set(assets_v2_converted.keys()):
            differences.append(
                f"assets keys mismatch: {assets_v2.keys()} vs {assets_v2_converted.keys()}"
            )
        for key in assets_v2.keys():
            if assets_v2[key] is None or assets_v2_converted[key] is None:
                if assets_v2[key] != assets_v2_converted[key]:
                    differences.append(
                        f"assets mismatch: {assets_v2[key]} vs {assets_v2_converted[key]}"
                    )
                continue

            if set(assets_v2[key]) != set(assets_v2_converted[key]):
                differences.append(
                    f"assets {key} mismatch: {assets_v2[key]} vs {assets_v2_converted[key]}"
                )
        return differences


class CustomInstructionConvertComparisor:
    def compare(
        self,
        custom_instruction_params: List[Dict[str, Any]],
        custom_instruction_params_converted: List[Dict[str, Any]],
    ) -> List[str]:
        differences = []
        if len(custom_instruction_params) != len(custom_instruction_params_converted):
            differences.append("custom_instruction_params length mismatch")
            return differences
        for custom_instruction_param, custom_instruction_param_converted in zip(
            custom_instruction_params, custom_instruction_params_converted
        ):
            asset_diffs = AssetsConvertComparator().compare(
                custom_instruction_param["assets"],
                custom_instruction_param_converted["assets"],
            )
            if asset_diffs:
                differences.extend(asset_diffs)
            if (
                custom_instruction_param["instruction"]
                != custom_instruction_param_converted["instruction"]
            ):
                differences.append(
                    f"instruction mismatch: {custom_instruction_param['instruction']} vs {custom_instruction_param_converted['instruction']}"
                )
        return differences


def convert_assets_v2_to_v3(
    playbook_id: int, assets_v2: Dict[str, Any]
) -> TofuDataList:
    converter = AssetsV2ToV3Converter(playbook_id, assets_v2)
    return converter.convert_assets_v2_to_v3()


def convert_assets_v3_to_v2(tofu_data_list: TofuDataList) -> Dict[str, Any]:
    converter = AssetsV3ToV2Converter(tofu_data_list)
    return converter.convert_assets_v3_to_v2()


def convert_custom_instructions_v2_to_v3(
    playbook_id: int, custom_instructions_v2: List[Dict[str, Any]]
) -> TofuDataList:
    """Convert v2 custom instructions format to v3."""
    converter = CustomInstructionV2ToV3Converter(
        playbook_id=playbook_id, custom_instructions_v2=custom_instructions_v2
    )
    return converter.convert_custom_instructions_v2_to_v3()


def convert_custom_instructions_v3_to_v2(
    custom_instructions_v3: TofuDataList,
) -> List[Dict[str, Any]]:
    """Convert v3 custom instructions format to v2."""
    converter = CustomInstructionV3ToV2Converter(
        custom_instructions_v3=custom_instructions_v3
    )
    return converter.convert_custom_instructions_v3_to_v2()
