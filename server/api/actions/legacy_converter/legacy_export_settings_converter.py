import copy
import logging
from typing import Any, Dict

from ...models import ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ExportEmailAdvancedSetting,
    ExportEmailSetting,
    ExportEmailTargetSetting,
    ExportEmailType,
    ExportPageSetting,
    ExportPageTargetSetting,
    ExportPageType,
    ExportPageUrlSetting,
    PlatformType,
    TofuDataList,
    TofuExportSettings,
)
from ...shared_types import ContentType
from ...sync.crm_export.export_factory import get_export_handler
from ...sync.crm_export.export_handler import BaseExportHandler
from ..tofu_data_wrapper import TofuDataListHandler


class ExportSettingsV2ToV3Converter:
    def __init__(self, content_group_params: Dict[str, Any]):
        self.content_type = content_group_params.get("content_type")
        self.export_settings_v2 = content_group_params.get("export_settings", {})

    def _convert_email_settings(self) -> ExportEmailSetting:
        export_detailed_settings = (
            self.export_settings_v2.get(
                self.export_settings_v2.get("exportDestination", ""), {}
            )
            .get("email", {})
            .get(self.export_settings_v2.get("exportType", ""), {})
        )

        email_setting = ExportEmailSetting()
        if self.export_settings_v2.get("exportType") == "dynamic":
            email_setting.export_type = ExportEmailType.EXPORT_EMAIL_TYPE_DYNAMIC
        else:
            email_setting.export_type = ExportEmailType.EXPORT_EMAIL_TYPE_STATIC

        for target in export_detailed_settings.get("targetsSetting", []):
            content_id = target.get("contentId")
            if content_id is not None:
                if "draftURL" in target and target.get("draftURL") is not None:
                    email_setting.targets_setting[content_id].draft_URL = target.get(
                        "draftURL"
                    )
                if "emailName" in target and target.get("emailName") is not None:
                    email_setting.targets_setting[content_id].email_name = target.get(
                        "emailName"
                    )
                if (
                    "isExportTarget" in target
                    and target.get("isExportTarget") is not None
                ):
                    email_setting.targets_setting[content_id].is_export_target = (
                        target.get("isExportTarget")
                    )

        for component_id, platform_token in export_detailed_settings.get(
            "componentsSetting", {}
        ).items():
            email_setting.components_setting[component_id] = platform_token

        email_setting.advanced_setting.CopyFrom(ExportEmailAdvancedSetting())
        if "advancedSetting" in export_detailed_settings:
            advanced_setting = export_detailed_settings.get("advancedSetting", {})
            if advanced_setting.get("emailType"):
                email_setting.advanced_setting.email_type = (
                    advanced_setting.get("emailType") or ""
                )
            if advanced_setting.get("emailFooter"):
                email_setting.advanced_setting.email_footer = (
                    advanced_setting.get("emailFooter") or False
                )
        return email_setting

    def _convert_page_settings(self) -> ExportPageSetting:
        export_detailed_settings = (
            self.export_settings_v2.get(
                self.export_settings_v2.get("exportDestination", ""), {}
            )
            .get("page", {})
            .get(self.export_settings_v2.get("exportType", ""), {})
        )

        page_setting = ExportPageSetting()
        if self.export_settings_v2.get("exportType") == "embed":
            page_setting.export_type = ExportPageType.EXPORT_PAGE_TYPE_EMBED
        elif self.export_settings_v2.get("exportType") == "dynamic":
            page_setting.export_type = ExportPageType.EXPORT_PAGE_TYPE_DYNAMIC
        else:
            page_setting.export_type = ExportPageType.EXPORT_PAGE_TYPE_STATIC

        for target in export_detailed_settings.get("targetsSetting", []):
            content_id = target.get("contentId")
            if content_id is not None:
                target_setting = ExportPageTargetSetting(
                    draft_URL=target.get("draftURL"),
                    page_slug=target.get("pageSlug"),
                    page_title=target.get("pageTitle"),
                )
                page_setting.targets_setting[content_id].CopyFrom(target_setting)

        for component_id, platform_token in export_detailed_settings.get(
            "componentsSetting", {}
        ).items():
            page_setting.components_setting[component_id] = platform_token

        url_setting = export_detailed_settings.get("urlSetting", {})
        page_url_setting = ExportPageUrlSetting(
            domain=url_setting.get("domain"),
            group_slug=url_setting.get("groupSlug"),
            URL_token=url_setting.get("hubspotURLToken")
            or url_setting.get("marketoURLToken")
            or url_setting.get("salesforceURLToken"),
            additional_URL_params=url_setting.get("additionalURLParams"),
        )
        page_setting.url_setting.CopyFrom(page_url_setting)
        return page_setting

    def convert_export_settings_v2_to_v3(self) -> TofuDataList:
        if not self.export_settings_v2:
            return TofuDataList()

        export_settings_v3 = TofuExportSettings()

        if self.export_settings_v2.get("exportDestination") == "other":
            export_settings_v3.platform_type = PlatformType.PLATFORM_TYPE_TOFU
        elif self.export_settings_v2.get("exportDestination") == "hubspot":
            export_settings_v3.platform_type = PlatformType.PLATFORM_TYPE_HUBSPOT
        elif self.export_settings_v2.get("exportDestination") == "marketo":
            export_settings_v3.platform_type = PlatformType.PLATFORM_TYPE_MARKETO
        elif self.export_settings_v2.get("exportDestination") == "salesforce":
            export_settings_v3.platform_type = PlatformType.PLATFORM_TYPE_SALESFORCE
        else:
            logging.error(
                f"Invalid export destination: {self.export_settings_v2.get('exportDestination')} in {self.export_settings_v2}"
            )
            return TofuDataList()

        if (
            self.content_type == ContentType.EmailMarketing
            or self.content_type == ContentType.EmailSDR
        ):
            export_settings_v3.email.CopyFrom(self._convert_email_settings())
        elif self.content_type == ContentType.LandingPage:
            export_settings_v3.page.CopyFrom(self._convert_page_settings())
        else:
            raise ValueError(f"Invalid content type: {self.content_type}")

        export_settings_v3_tofu_data = (
            TofuDataListHandler.convert_export_settings_to_tofu_data(export_settings_v3)
        )
        return export_settings_v3_tofu_data


class ExportSettingsV3ToV2Converter:
    def __init__(
        self,
        export_settings_v3: TofuDataList,
        existing_export_settings: Dict[str, Any],
        content_group: ContentGroup,
    ):
        self.export_settings_v3 = export_settings_v3
        self.existing_export_settings = existing_export_settings
        self.content_group = content_group

    def convert_export_settings_v3_to_v2(self) -> Dict[str, Any]:
        export_settings_v2 = copy.deepcopy(self.existing_export_settings)

        # Extract the TofuExportSettings from the TofuDataList
        tofu_export_settings = (
            ExportSettingsV3ToV2Converter.extract_tofu_export_settings(
                self.export_settings_v3
            )
        )

        # Map platform type
        platform_type_map = {
            PlatformType.PLATFORM_TYPE_HUBSPOT: "hubspot",
            PlatformType.PLATFORM_TYPE_MARKETO: "marketo",
            PlatformType.PLATFORM_TYPE_SALESFORCE: "salesforce",
            PlatformType.PLATFORM_TYPE_TOFU: "other",
        }
        platform_key = platform_type_map.get(tofu_export_settings.platform_type)
        export_settings_v2["exportDestination"] = platform_key

        if not platform_key:
            raise ValueError(
                f"Unsupported platform type: {tofu_export_settings.platform_type}"
            )

        # Initialize the platform-specific settings if not present
        if platform_key not in export_settings_v2:
            export_settings_v2[platform_key] = {}

        # Convert email settings if present
        if tofu_export_settings.HasField("email"):
            try:
                export_handler: BaseExportHandler = get_export_handler(
                    tofu_export_settings.platform_type, self.content_group
                )
            except Exception as e:
                logging.error(f"Error getting export handler: {e}")
                raise e
            email_settings = tofu_export_settings.email
            export_settings = export_handler.create_or_update_export_settings(
                self.content_group,
                save_to_db=False,
                **self._convert_email_settings(email_settings),
            )
            export_settings_v2.update(export_settings)
        # Convert page settings if present
        if tofu_export_settings.HasField("page"):
            page_settings = tofu_export_settings.page
            export_type = (
                "embed"
                if page_settings.export_type == ExportPageType.EXPORT_PAGE_TYPE_EMBED
                else (
                    "dynamic"
                    if page_settings.export_type
                    == ExportPageType.EXPORT_PAGE_TYPE_DYNAMIC
                    else "static"
                )
            )
            export_settings_v2["exportType"] = export_type
            if "page" not in export_settings_v2[platform_key]:
                export_settings_v2[platform_key]["page"] = {}
            if export_type not in export_settings_v2[platform_key]["page"]:
                export_settings_v2[platform_key]["page"][export_type] = {}
            export_settings_v2[platform_key]["page"][export_type] = (
                self._convert_page_settings(page_settings)
            )

        return export_settings_v2

    @staticmethod
    def extract_tofu_export_settings(
        tofu_data_list: TofuDataList,
    ) -> TofuExportSettings:
        for tofu_data in tofu_data_list.data:
            if tofu_data.HasField("export_settings"):
                return tofu_data.export_settings
        raise ValueError("No export settings found in TofuDataList")

    def _convert_email_settings(
        self, email_settings: ExportEmailSetting
    ) -> Dict[str, Any]:
        return {
            "exportType": (
                "dynamic"
                if email_settings.export_type
                == ExportEmailType.EXPORT_EMAIL_TYPE_DYNAMIC
                else "static"
            ),
            "targetsSetting": [
                {
                    "contentId": content_id,
                    "draftURL": target_setting.draft_URL,
                    "emailName": target_setting.email_name,
                    "isExportTarget": target_setting.is_export_target,
                }
                for content_id, target_setting in email_settings.targets_setting.items()
            ],
            "componentsSetting": dict(email_settings.components_setting),
            "advancedSetting": {
                "emailType": email_settings.advanced_setting.email_type,
                "emailFooter": email_settings.advanced_setting.email_footer,
            },
        }

    def _convert_page_settings(
        self, page_settings: ExportPageSetting
    ) -> Dict[str, Any]:
        return {
            "exportType": (
                "embed"
                if page_settings.export_type == ExportPageType.EXPORT_PAGE_TYPE_EMBED
                else (
                    "dynamic"
                    if page_settings.export_type
                    == ExportPageType.EXPORT_PAGE_TYPE_DYNAMIC
                    else "static"
                )
            ),
            "targetsSetting": [
                {
                    "contentId": content_id,
                    "draftURL": target_setting.draft_URL,
                    "pageSlug": target_setting.page_slug,
                    "pageTitle": target_setting.page_title,
                }
                for content_id, target_setting in page_settings.targets_setting.items()
            ],
            "componentsSetting": dict(page_settings.components_setting),
            "urlSetting": {
                "domain": page_settings.url_setting.domain,
                "groupSlug": page_settings.url_setting.group_slug,
                "additionalURLParams": page_settings.url_setting.additional_URL_params,
            },
        }


class ExportSettingsConvertComparisor:
    def __init__(
        self,
        existing_export_settings: Dict[str, Any],
        export_settings_converted: Dict[str, Any],
    ):
        self.existing_export_settings = existing_export_settings
        self.export_settings_converted = export_settings_converted

    def compare_targets_setting(self, t1, t2):
        if not isinstance(t1, list) or not isinstance(t2, list):
            return False
        if len(t1) != len(t2):
            return False
        # Sort by contentId for order-insensitive comparison
        t1_sorted = sorted(t1, key=lambda x: x.get("contentId"))
        t2_sorted = sorted(t2, key=lambda x: x.get("contentId"))
        for a, b in zip(t1_sorted, t2_sorted):
            # Compare only relevant fields
            fields = set(a.keys()) | set(b.keys())
            for field in fields:
                # For nested dicts (like hubIds), compare recursively
                if field == "hubIds":
                    if a.get(field) != b.get(field):
                        return False
                elif (
                    field == "targetNames"
                    or field == "targetLabels"
                    or field == "targetListNames"
                ):
                    # Compare lists as sets (order-insensitive)
                    if set(a.get(field, [])) != set(b.get(field, [])):
                        return False
                else:
                    if a.get(field) != b.get(field):
                        return False
        return True

    def compare_components_setting(self, c1, c2):
        if not isinstance(c1, dict) or not isinstance(c2, dict):
            return False
        # Compare each key and value
        if set(c1.keys()) != set(c2.keys()):
            return False
        for k in c1:
            if c1[k] != c2[k]:
                return False
        return True

    def compare_advanced_setting(self, a1, a2):
        if not isinstance(a1, dict) or not isinstance(a2, dict):
            return False
        # Only compare relevant fields
        relevant_fields = set(a1.keys()) | set(a2.keys())
        for k in relevant_fields:
            if a1.get(k) != a2.get(k):
                return False
        return True

    def compare_email_settings(self, e1, e2):
        # Compare exportType
        if e1.get("exportType") != e2.get("exportType"):
            return False
        # Compare destination if present
        if e1.get("destination") != e2.get("destination"):
            return False
        # Compare targetsSetting
        if not self.compare_targets_setting(
            e1.get("targetsSetting", []), e2.get("targetsSetting", [])
        ):
            return False
        # Compare componentsSetting
        if not self.compare_components_setting(
            e1.get("componentsSetting", {}), e2.get("componentsSetting", {})
        ):
            return False
        # Compare advancedSetting
        if not self.compare_advanced_setting(
            e1.get("advancedSetting", {}), e2.get("advancedSetting", {})
        ):
            return False
        return True

    def compare_dict(self, dict1, dict2):
        import logging

        # Step 1: Check top-level keys and extract export destination/type
        if set(dict1.keys()) != set(dict2.keys()):
            return False
        export_destination = dict1.get("exportDestination")
        export_type = None
        if export_destination and export_destination in dict1:
            destination_dict1 = dict1[export_destination]
            destination_dict2 = dict2[export_destination]
            type_keys = set(destination_dict1.keys()) & set(destination_dict2.keys())
            if not type_keys:
                logging.info(
                    f"No common type keys found under destination '{export_destination}'"
                )
                return False
            for type_key in type_keys:
                export_type = type_key
                # Step 2: Only compare for hubspot/email, otherwise log and skip
                if export_destination == "hubspot" and export_type == "email":
                    # Compare all subtypes (e.g., 'dynamic', 'static')
                    email_dict1 = destination_dict1[export_type]
                    email_dict2 = destination_dict2[export_type]
                    if set(email_dict1.keys()) != set(email_dict2.keys()):
                        return False
                    for subtype in email_dict1:
                        if not self.compare_email_settings(
                            email_dict1[subtype], email_dict2[subtype]
                        ):
                            return False
                else:
                    logging.info(
                        f"Skipping comparison for destination '{export_destination}' and type '{export_type}' (not implemented)"
                    )
                    continue
        for field in ("exportType", "exportDestination"):
            if dict1.get(field) != dict2.get(field):
                return False
        return True

    def compare(self) -> bool:
        return self.compare_dict(
            self.existing_export_settings, self.export_settings_converted
        )


# Usage
def convert_export_settings_v2_to_v3(
    content_group_params: Dict[str, Any],
) -> TofuDataList:
    converter = ExportSettingsV2ToV3Converter(content_group_params)
    return converter.convert_export_settings_v2_to_v3()


# Usage
def convert_export_settings_v3_to_v2(
    export_settings_v3: TofuDataList,
    existing_export_settings: Dict[str, Any],
    content_group: ContentGroup,
) -> Dict[str, Any]:
    converter = ExportSettingsV3ToV2Converter(
        export_settings_v3, existing_export_settings, content_group
    )
    return converter.convert_export_settings_v3_to_v2()


# Usage
def is_export_settings_v2_equal(
    export_settings_v2_1: Dict[str, Any],
    export_settings_v2_2: Dict[str, Any],
) -> bool:
    comparator = ExportSettingsConvertComparisor(
        export_settings_v2_1, export_settings_v2_2
    )
    try:
        return comparator.compare()
    except Exception as e:
        logging.error(f"Error comparing export settings: {e}")
        return False
