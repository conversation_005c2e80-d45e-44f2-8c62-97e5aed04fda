import logging
import traceback
from typing import List

from django.db import transaction

from ...campaign import CampaignHandler
from ...content_group import ContentGroupHandler
from ...models import Action, ActionEdge, AssetInfo, Campaign, ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionExecutionParams,
    PlatformType,
)
from ...validator.campaign_validator import CampaignGoal
from ..action_creator import ActionCreator
from ..action_handler import ActionHandler
from ..tofu_data_wrapper import TofuDataListHandler
from .legacy_components_converter import convert_components_v2_to_v3
from .legacy_custom_instruction_converter import AssetsV2ToV3Converter
from .legacy_template_converter import convert_template_v2_to_v3


class LegacyCampaignConverter:
    def __init__(self, campaign: Campaign):
        self._campaign = campaign
        self._copied_campaign = None

    def convert_to_v3(self):
        # v2 campaign should not have actions.
        if Action.objects.filter(campaign=self._campaign).exists():
            raise ValueError("Campaign has actions, cannot convert to v3")

        # Copy the campaign.
        self._copied_campaign = CampaignHandler(self._campaign).clone(blocking=True)

        self._copied_campaign.campaign_name = (
            f"Copy campaign builder - {self._campaign.campaign_name}"
        )

        try:

            content_groups = ContentGroup.objects.filter(campaign=self._copied_campaign)

            campaign_goal = self._copied_campaign.campaign_params.get(
                "campaign_goal_v2"
            ) or self._copied_campaign.campaign_params.get("campaign_goal")
            if campaign_goal == CampaignGoal.Personalization:
                # every content group should be one action
                for content_group in content_groups:
                    self._convert_content_group_to_p13n_action(content_group)
            elif campaign_goal == CampaignGoal.Repurposing:
                self._convert_content_group_to_repurpose_action(content_groups)

            # bulk create content
            for content_group in content_groups:
                ContentGroupHandler(content_group).bulk_create_content()

            self._copied_campaign.campaign_params["campaign_goal_v2"] = campaign_goal
            self._copied_campaign.campaign_params["campaign_goal"] = "Workflow"
            self._copied_campaign.campaign_params["is_campaign_v3"] = True
            self._copied_campaign.save(
                update_fields=["campaign_params", "campaign_name"]
            )

        except Exception as e:
            logging.error(
                f"Failed to convert campaign {self._campaign.id} to v3: {e}\n{traceback.format_exc()}"
            )
            # clean up the copied campaign
            self._copied_campaign.delete()
            raise e

        return self._copied_campaign

    def _convert_content_group_to_p13n_action(self, content_group: ContentGroup):
        action_data = {
            "creator": self._copied_campaign.creator,
            "playbook": self._copied_campaign.playbook,
            "campaign": self._copied_campaign,
            "action_name": content_group.content_group_name,
            "action_category": ActionCategory.Name(
                ActionCategory.ACTION_CATEGORY_PERSONALIZE
            ),
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data(
                        content_group.content_group_params.get("content_type")
                    )
                ),
                "template": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_template_to_tofu_data(
                        convert_template_v2_to_v3(
                            self._copied_campaign.playbook.id,
                            content_group.content_group_params,
                        )
                    )
                ),
                "components": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_components_to_tofu_data(
                        convert_components_v2_to_v3(
                            self._copied_campaign.playbook.id,
                            content_group.components,
                        )
                    )
                ),
            },
            "outputs": {
                "content_group": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_group_ids_to_tofu_data(
                        [content_group.id]
                    )
                ),
            },
            "meta": {},
        }
        p13n_action = Action.objects.create(**action_data)
        content_group.action = p13n_action
        content_group.save(update_fields=["action"])

        export_destination = content_group.content_group_params.get(
            "export_settings", {}
        ).get("exportDestination")
        if export_destination:

            platform_type = (
                PlatformType.PLATFORM_TYPE_HUBSPOT
                if export_destination == "hubspot"
                else (
                    PlatformType.PLATFORM_TYPE_MARKETO
                    if export_destination == "marketo"
                    else (
                        PlatformType.PLATFORM_TYPE_SALESFORCE
                        if export_destination == "salesforce"
                        else PlatformType.PLATFORM_TYPE_OTHER
                    )
                )
            )

            export_action_data = {
                "creator": self._copied_campaign.creator,
                "playbook": self._copied_campaign.playbook,
                "campaign": self._copied_campaign,
                "action_name": f"Export for {content_group.content_group_name}",
                "action_category": ActionCategory.Name(
                    ActionCategory.ACTION_CATEGORY_EXPORT
                ),
                "inputs": {
                    "platform_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_platform_type_to_tofu_data(
                            platform_type
                        )
                    ),
                    "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_content_type_to_tofu_data(
                            content_group.content_group_params.get("content_type")
                        )
                    ),
                },
                "incoming_edges": [
                    {
                        "from_action": p13n_action.id,
                        "config": {
                            "output_name_from_incoming_action": "content_group",
                            "input_name_to_outgoing_action": "content_group",
                        },
                    }
                ],
                "meta": {},
            }
            ActionCreator(self._copied_campaign.creator, export_action_data).create()

        return p13n_action

    def _create_repurpose_action(
        self,
        content_groups,
        action_name,
        incoming_edges_data,
        anchor_content_asset_data,
    ):
        if not content_groups:
            raise ValueError("Content groups are required for repurpose action")

        num_outputs = len(content_groups)
        action_data = {
            "creator": self._copied_campaign.creator,
            "playbook": self._copied_campaign.playbook,
            "campaign": self._copied_campaign,
            "action_name": action_name,
            "action_category": ActionCategory.Name(
                ActionCategory.ACTION_CATEGORY_REPURPOSE
            ),
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data(
                        content_groups[0].content_group_params.get("content_type")
                    )
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
                "anchor_content": TofuDataListHandler.convert_tofu_data_to_json(
                    anchor_content_asset_data
                ),
            },
            "outputs": {
                "content_group": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_group_ids_to_tofu_data(
                        [cg.id for cg in content_groups]
                    )
                ),
            },
            "meta": {},
        }
        action = Action.objects.create(**action_data)
        if incoming_edges_data:
            for edge_data in incoming_edges_data:
                edge_data_copy = edge_data.copy()
                edge_data_copy["to_action"] = action
                if (
                    not edge_data_copy.get("config")
                    or not edge_data_copy.get("config").get(
                        "output_name_from_incoming_action"
                    )
                    or not edge_data_copy.get("config").get(
                        "input_name_to_outgoing_action"
                    )
                ):
                    raise ValueError(
                        f"Edge config is missing output_name_from_incoming_action or input_name_to_outgoing_action as {edge_data_copy.get('config')}"
                    )
                ActionEdge.objects.create(**edge_data_copy)

        # tie content_group to action
        for content_group in content_groups:
            content_group.action = action
        ContentGroup.objects.bulk_update(content_groups, ["action"])

        # update the content collection id for num_outputs > 1
        if num_outputs > 1:
            for content_group in content_groups:
                content_group.content_group_params["content_collection"][
                    "id"
                ] = f"action_{action.id}"
                content_group.save(update_fields=["content_group_params"])
        return action

    def _convert_content_group_to_repurpose_action(
        self, content_groups: List[ContentGroup]
    ):
        anchor_content = self._copied_campaign.campaign_params.get("assets", {})
        anchor_content_asset_data = AssetsV2ToV3Converter(
            self._copied_campaign.playbook, anchor_content
        ).convert_assets_v2_to_v3()

        incoming_edges_data = []
        if anchor_content_asset_data:
            anchor_content_action_data = {
                "action_category": ActionCategory.Name(
                    ActionCategory.ACTION_CATEGORY_USER_INPUT
                ),
                "action_name": "Anchor Content",
                "creator": self._copied_campaign.creator,
                "campaign": self._copied_campaign,
                "playbook": self._copied_campaign.playbook,
                "inputs": {
                    "assets": TofuDataListHandler.convert_tofu_data_to_json(
                        anchor_content_asset_data
                    ),
                },
                "outputs": {
                    "assets": TofuDataListHandler.convert_tofu_data_to_json(
                        anchor_content_asset_data
                    ),
                },
                "meta": {},
            }
            anchor_content_action = Action.objects.create(**anchor_content_action_data)

            # execute the anchor content action
            ActionHandler(anchor_content_action).execute(ActionExecutionParams())
            action_edge_data = {
                "from_action": anchor_content_action,
                "config": {
                    "output_name_from_incoming_action": "assets",
                    "input_name_to_outgoing_action": "anchor_content",
                },
            }
            incoming_edges_data.append(action_edge_data)

        content_collections = {}
        non_content_collection_groups = []
        for content_group in content_groups:
            if content_group.content_group_params.get("content_collection"):
                content_collection_id = content_group.content_group_params.get(
                    "content_collection", {}
                ).get("id")
                if not content_collection_id:
                    raise ValueError(
                        f"Content collection id is required for repurpose action"
                    )
                if content_collection_id not in content_collections:
                    content_collections[content_collection_id] = []
                content_collections[content_collection_id].append(content_group)
            else:
                non_content_collection_groups.append(content_group)

        for content_collection_id, content_groups in content_collections.items():
            if not content_groups:
                raise ValueError(f"Content groups are required for repurpose action")

            # TODO: this is a temporary solution, we need to find a better way to get the content collection name
            content_collection_name = content_groups[0].content_group_name[
                :-1
            ]  # assume the length of extension is 1
            action = self._create_repurpose_action(
                content_groups,
                content_collection_name,
                incoming_edges_data,
                anchor_content_asset_data,
            )

        for content_group in non_content_collection_groups:
            content_group_name = content_group.content_group_name
            action = self._create_repurpose_action(
                [content_group],
                content_group_name,
                incoming_edges_data,
                anchor_content_asset_data,
            )
