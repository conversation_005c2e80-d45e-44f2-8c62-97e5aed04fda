import copy
import logging
import operator
from functools import reduce
from typing import Any, Dict, List, Optional

from django.db.models import Q

from ...models import TargetInfo, TargetInfoGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    TofuAsset,
    TofuComponentType,
    TofuData,
    TofuDataList,
    TofuTarget,
    TofuTemplate,
    TofuTemplateField,
)


class CampaignTargetsV2ToV3Converter:
    def __init__(self, campaign_targets: List[Dict[str, List[str]]], playbook_id: int):
        self.campaign_targets = campaign_targets
        self.playbook_id = playbook_id

    def convert_campaign_targets_v2_to_v3(self) -> Optional[TofuDataList]:
        targets_v3 = TofuDataList()

        try:
            # Get all unique target group keys
            target_group_keys = set(
                group_key
                for target_group_dict in self.campaign_targets
                for group_key in target_group_dict.keys()
            )

            # Fetch all relevant target groups in one query
            target_groups = {
                group.target_info_group_key: group
                for group in TargetInfoGroup.objects.filter(
                    playbook_id=self.playbook_id,
                    target_info_group_key__in=target_group_keys,
                )
            }

            # Get all target keys and their group keys
            target_keys_by_group = {}
            for target_group_dict in self.campaign_targets:
                for group_key, target_keys in target_group_dict.items():
                    target_keys_by_group[group_key] = target_keys

            # Create a list of Q objects for each group's targets
            group_filters = [
                Q(target_info_group=target_groups[group_key])
                & Q(target_key__in=target_keys)
                for group_key, target_keys in target_keys_by_group.items()
                if group_key in target_groups
            ]

            # Add check for empty filters
            if not group_filters:
                return targets_v3

            # Combine Q objects with OR
            targets = TargetInfo.objects.filter(
                reduce(operator.or_, group_filters)
            ).select_related("target_info_group")

            # Create lookup dictionary for faster access
            targets_lookup = {
                (t.target_info_group.target_info_group_key, t.target_key): t
                for t in targets
            }

            # Process targets
            for target_group_dict in self.campaign_targets:
                for group_key, target_keys in target_group_dict.items():
                    if group_key not in target_groups:
                        logging.warning(f"Target group not found: {group_key}")
                        continue

                    for target_key in target_keys:
                        target = targets_lookup.get((group_key, target_key))
                        if not target:
                            logging.warning(
                                f"Target not found: {target_key} in group {group_key}"
                            )
                            continue

                        # Create TofuData with TofuTarget
                        tofu_target = TofuTarget()
                        tofu_target.target_id = target.id
                        tofu_data = TofuData()
                        tofu_data.target.CopyFrom(tofu_target)
                        targets_v3.data.append(tofu_data)

            return targets_v3

        except Exception as e:
            logging.exception(f"Error converting targets v2 to v3: {e}")
            return None


class CampaignTargetsV3ToV2Converter:
    def __init__(self, campaign_targets: TofuDataList):
        self.campaign_targets = campaign_targets

    def convert_campaign_targets_v3_to_v2(self) -> List[Dict[str, List[str]]]:
        try:
            # Group targets by target_info_group_key
            grouped_targets: Dict[str, List[str]] = {}

            # Get all target IDs from TofuData list
            target_ids = [
                data.target.target_id
                for data in self.campaign_targets.data
                if data.HasField("target")
            ]

            # Fetch all targets in a single query
            targets = TargetInfo.objects.select_related("target_info_group").filter(
                id__in=target_ids
            )

            # Process targets
            for target in targets:
                group_key = target.target_info_group.target_info_group_key
                target_key = target.target_key

                if group_key not in grouped_targets:
                    grouped_targets[group_key] = []

                grouped_targets[group_key].append(target_key)

            # Log any missing targets
            found_ids = {target.id for target in targets}
            missing_ids = set(target_ids) - found_ids
            if missing_ids:
                logging.error(f"Targets not found for ids: {missing_ids}")

            # Convert to v2 format: [{group_key: [target_keys]}]
            return [
                {group_key: target_keys}
                for group_key, target_keys in grouped_targets.items()
            ]

        except Exception as e:
            logging.exception(f"Error converting targets v3 to v2: {e}")
            return []


def convert_campaign_targets_v2_to_v3(
    campaign_targets: List[Dict[str, List[str]]], playbook_id: int
) -> Optional[TofuTarget]:
    """Convert v2 template format to v3."""
    converter = CampaignTargetsV2ToV3Converter(
        campaign_targets=campaign_targets, playbook_id=playbook_id
    )
    return converter.convert_campaign_targets_v2_to_v3()


def convert_campaign_targets_v3_to_v2(
    campaign_targets: TofuDataList, playbook_id: int
) -> List[Dict[str, List[str]]]:
    """Convert v3 template format to v2."""
    logging.info(f"campaign_targets: {campaign_targets}")
    if not isinstance(campaign_targets, TofuDataList):
        raise ValueError(f"Invalid campaign_targets type: {type(campaign_targets)}")
    # Make a deep copy to avoid circular references
    converter = CampaignTargetsV3ToV2Converter(
        campaign_targets=campaign_targets,
    )
    return converter.convert_campaign_targets_v3_to_v2()
