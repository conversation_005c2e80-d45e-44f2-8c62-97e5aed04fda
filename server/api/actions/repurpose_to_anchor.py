import json
import tempfile
import uuid

from django.core.cache import cache

from ..models import AssetInfo, AssetInfoGroup, Content
from ..s3_utils import upload_file
from ..shared_definitions.protobuf.gen.action_define_pb2 import TofuAsset
from ..shared_types import ContentType
from ..utils import replace_with_content_variations


class RepurposeToAnchorConverter:
    output_type = "asset"

    def __init__(self, content_group, to_action):
        self.content_group = content_group
        self.playbook = content_group.campaign.playbook
        self.to_action = to_action

    def process(self):
        asset_id = self._create_and_upload_asset()
        self.asset = TofuAsset(asset_id=asset_id)

        return self.asset

    def _create_and_upload_asset(self):
        # returns the asset id of the uploaded asset
        components = self._get_components()
        repurpose_content = self._get_repurpose_content(components)
        asset_filename = self._get_anchor_converter_file_name()
        asset_key = self.content_group.content_group_name
        filename = f"{asset_filename}.txt"
        with tempfile.TemporaryDirectory() as temp_dir:
            file_location = f"{temp_dir}/{filename}"
            with open(file_location, "w") as f:
                f.write(repurpose_content)

            # upload file to s3
            file_type = "text/plain"
            s3_presigned_path = self._upload_to_s3(
                repurpose_content, filename, file_type
            )
            s3_file_value = {
                "s3_bucket": "tofu-uploaded-files",
                "s3_filename": filename,
                "mime_file_type": file_type,
                "original_filename": filename,
                "s3_presigned_path": s3_presigned_path,
            }
        asset_group_key = "[TOFU Internal] Repurpose to Anchor"
        asset_info_group = AssetInfoGroup.objects.filter(
            asset_info_group_key=asset_group_key, playbook=self.playbook
        ).first()
        if not asset_info_group:
            asset_info_group = AssetInfoGroup.objects.create(
                asset_info_group_key=asset_group_key,
                playbook=self.playbook,
                meta={"position": 0},
            )
        asset_doc_id = str(uuid.uuid4())
        docs = {
            asset_doc_id: {
                "id": asset_doc_id,
                "type": "file",
                "value": s3_file_value,
                "position": 0,
            }
        }
        # first check if the asset info already exists with that asset key, if so just update it, otherwise create a new one.
        from django.db import transaction

        with transaction.atomic():
            asset_info = AssetInfo.objects.filter(
                asset_info_group=asset_info_group, asset_key=asset_key
            ).first()
            if not asset_info:
                asset_info = AssetInfo.objects.create(
                    asset_info_group=asset_info_group,
                    asset_key=asset_key,
                    docs=docs,
                    meta={"position": 0, "type": "repurpose_to_anchor"},
                )
            else:
                asset_info.docs = docs
                asset_info.save()
            return asset_info.id

    def _get_components(self):
        # Assume only one content in the content group.
        content = Content.objects.filter(content_group=self.content_group).first()
        if content is None:
            raise ValueError("No Content found for the provided content group.")
        components_data = replace_with_content_variations(
            self.content_group.components, content.id, cache_enabled=False
        )
        return components_data

    def _get_repurpose_content(self, components):
        if not components:
            return ""
        content_group_params = self.content_group.content_group_params
        content_type = content_group_params.get("content_type")
        if content_type == ContentType.AdCampaignLinkedin:
            return self._get_linkedin_ad_content(components)
        elif (
            content_type == ContentType.EmailMarketing
            or content_type == ContentType.EmailSDR
        ):
            return self._get_email_content(components)
        else:
            return self._get_generic_content(components)

    def _get_linkedin_ad_content(self, components):
        # we expect to get headline, description, introductory text, and ad copy.
        headline = ""
        description = ""
        introductory_text = ""
        ad_copy = ""
        for component_key, component in components.items():
            if component.get("meta", {}).get("component_type") == "headline":
                headline = component.get("text", "")
            elif component.get("meta", {}).get("component_type") == "description":
                description = component.get("text", "")
            elif component.get("meta", {}).get("component_type") == "introductory-text":
                introductory_text = component.get("text", "")
            elif component.get("meta", {}).get("component_type") == "ad-copy":
                ad_copy = component.get("text", "")
        return f"headline: {headline}\ndescription: {description}\nintroductory_text: {introductory_text}\nad_copy: {ad_copy}"

    def _get_email_content(self, components):
        # we expect to get email subject and email body.
        subject_line = ""
        body = ""
        for component_key, component in components.items():
            if component.get("meta", {}).get("component_type") == "email subject":
                subject_line = component.get("text", "")
            elif component.get("meta", {}).get("component_type") == "email body":
                body = component.get("text", "")
        return f"subject_line: {subject_line}\nbody: {body}"

    def _get_generic_content(self, components):
        # we expect to get a generic text.
        text = ""
        for component_key, component in components.items():
            text += component.get("text", "")
        return text

    def _upload_to_s3(self, slate_text, file_name, file_type="text/plain"):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_location = f"{temp_dir}/{file_name}"
            with open(file_location, "w") as f:
                f.write(slate_text)

            # upload file to s3
            upload_file(
                file_location,
                file_type,
                "tofu-uploaded-files",
                file_name,
            )
            return f"/api/web/storage/s3-presigned-url?file={file_name}&fileType={file_type}&directory=tofu-uploaded-files"

    def _get_anchor_converter_file_name(self):
        return f"repurpose_to_anchor_converter_{self.content_group.id}"
