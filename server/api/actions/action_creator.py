import copy
import logging

from django.db import transaction

from ..content_group import ContentGroupHandler
from ..models import Action, ActionEdge, ContentGroup
from ..shared_definitions.protobuf.action_system_config import (
    CATEGORY_INPUTS_NEEDED_FOR_DEFINITION,
)
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionOutputs,
    TofuDataList,
)
from ..utils import fix_content_collection_pointers
from ..validator.campaign_validator import CampaignGoal
from .action_edge_creator import ActionEdgeCreator
from .action_handler import ActionHandler
from .action_system_config_loader import ActionSystemConfigLoader
from .tofu_data_wrapper import TofuDataListHandler


class ActionCreator:
    def __init__(self, creator, validated_data):
        self._creator = creator
        self._validated_data = validated_data

        self._inputs = {}
        self._outputs = {}
        self._action_instance = None

    # TODO: make this as a util function
    def _convert_inputs(self, json_inputs):
        inputs_converted = {}
        for key, value in json_inputs.items():
            if isinstance(value, TofuDataList):
                inputs_converted[key] = value
            else:
                try:
                    inputs_converted[key] = TofuDataListHandler.parse_json_to_tofu_data(
                        value
                    )
                except Exception as e:
                    logging.exception(
                        f"Failed to convert input {key} to tofu data: {e} for value {value}"
                    )
                    raise ValueError(
                        f"Failed to convert input {key} to tofu data: {e} for value {value}"
                    ) from e
        return inputs_converted

    def _create_action_user_input(self):
        self._action_instance.refresh_from_db()
        # check inputs
        asset_data_from_inputs = self._inputs.get("assets", None)
        asset_data_from_incoming_edges = self._action_instance.inputs.get(
            "assets", None
        )

        asset_data = TofuDataListHandler.merge_asset_data(
            asset_data_from_inputs, asset_data_from_incoming_edges
        )
        if asset_data is not None:
            self._inputs["assets"] = asset_data

        # pass all inputs to the action instance
        action_handler = ActionHandler(self._action_instance)
        action_handler.update_inputs(self._inputs)

    def _create_action_repurpose(self):
        def _get_legacy_default_content_collection_data(action_instance, num_outputs):
            return {
                "id": f"action_{action_instance.id}",
                "name": action_instance.action_name,
                "next": None,
                "prev": None,
                "numOfContent": num_outputs,
                "custom_instructions": [],
                "content_collection_instructions": [],
            }

        # step 2: create content groups
        num_outputs = self._inputs.get("num_outputs", None)
        if num_outputs is None:
            raise ValueError("num_outputs is required for repurpose action")
        num_outputs = TofuDataListHandler.get_int_value(num_outputs)
        if num_outputs <= 0:
            raise ValueError("num_outputs must be greater than 0")

        # TODO: refactor this code that passing content_type in a better organized manner
        content_type = self._inputs.get("content_type", None)
        if content_type is None:
            raise ValueError("content_type is required for repurpose action")
        content_type = TofuDataListHandler.get_content_type(content_type)
        if content_type is None:
            raise ValueError("Invalid content_type format")

        content_group_ids = []
        common_content_group_params = {
            "content_type": content_type,
            "content_goal": CampaignGoal.Repurposing,
        }
        if num_outputs > 1:
            common_content_group_params["content_collection"] = (
                _get_legacy_default_content_collection_data(
                    self._action_instance, num_outputs
                )
            )
        for i in range(num_outputs):
            content_group_params = copy.deepcopy(common_content_group_params)
            content_group_params["action_index"] = i
            content_group_name = f"{self._action_instance.action_name} {i+1}"
            content_group = ContentGroup.objects.create(
                creator=self._creator,
                campaign=self._campaign,
                action=self._action_instance,
                content_group_name=content_group_name,
                content_group_params=content_group_params,
            )
            content_group_ids.append(content_group.id)
            ContentGroupHandler(content_group=content_group).bulk_create_content()

        if num_outputs > 1:
            fix_content_collection_pointers(content_group_ids)

        # step 3: create action outputs
        action_outputs = {}
        data_list = TofuDataListHandler.convert_content_group_ids_to_tofu_data(
            content_group_ids
        )
        action_outputs["content_group"] = TofuDataListHandler.convert_tofu_data_to_json(
            data_list
        )
        self._action_instance.outputs = action_outputs
        self._action_instance.save(update_fields=["outputs"])

        # update inputs
        # manipulations:
        inputs_params_to_save = copy.deepcopy(self._inputs)

        # TODO: make this modulized
        inputs_params_to_save.pop("content_type", None)

        # step 1: update inputs
        self._action_instance.inputs = {
            key: TofuDataListHandler.convert_tofu_data_to_json(value)
            for key, value in inputs_params_to_save.items()
        }
        action_handler = ActionHandler(self._action_instance)
        action_handler.update_inputs(inputs_params_to_save)

    def _create_action_personalize(self):
        # step2: create content group
        content_type = self._inputs.get("content_type", None)
        if content_type is None:
            raise ValueError("content_type is required for personalize action")
        content_type = TofuDataListHandler.get_content_type(content_type)
        if content_type is None:
            raise ValueError("Invalid content_type format")

        content_group_params = {
            "content_type": content_type,
            "content_goal": CampaignGoal.Personalization,
        }
        content_group = ContentGroup.objects.create(
            creator=self._creator,
            campaign=self._campaign,
            action=self._action_instance,
            content_group_name=self._action_instance.action_name,
            content_group_params=content_group_params,
        )

        # step3: update action outputs
        tofu_data = TofuDataListHandler.convert_content_group_ids_to_tofu_data(
            [content_group.id]
        )
        self._action_instance.outputs = {
            "content_group": TofuDataListHandler.convert_tofu_data_to_json(tofu_data)
        }
        self._action_instance.save(update_fields=["outputs"])

        # step1: update inputs
        # we don't save them to the action instance
        inputs_params_to_save = copy.deepcopy(self._inputs)
        inputs_params_to_save.pop("content_type", None)
        inputs_params_to_save.pop("targets", None)

        self._action_instance.inputs = {
            key: TofuDataListHandler.convert_tofu_data_to_json(value)
            for key, value in inputs_params_to_save.items()
        }
        action_handler = ActionHandler(self._action_instance)
        action_handler.update_inputs(inputs_params_to_save)

    def _post_process_personalize(self):
        action_handler = ActionHandler(self._action_instance)
        action_handler.update_targets()

    def _create_action_export(self):
        # step1: update inputs
        inputs_params_to_save = copy.deepcopy(self._inputs)
        self._action_instance.inputs = {
            key: TofuDataListHandler.convert_tofu_data_to_json(value)
            for key, value in inputs_params_to_save.items()
        }
        action_handler = ActionHandler(self._action_instance)
        action_handler.update_inputs(inputs_params_to_save, update_status=False)

    def _create_action_seq_personalize_template(self):
        def _get_legacy_default_content_collection_data(action_instance, num_outputs):
            return {
                "id": f"action_{action_instance.id}",
                "name": action_instance.action_name,
                "next": None,
                "prev": None,
                "numOfContent": num_outputs,
                "custom_instructions": [],
                "content_collection_instructions": [],
            }

        # step 1: create content groups
        num_outputs = self._inputs.get("num_outputs", None)
        if num_outputs is None:
            raise ValueError("num_outputs is required for sequence personalize action")
        num_outputs = TofuDataListHandler.get_int_value(num_outputs)
        if num_outputs <= 1:
            raise ValueError("num_outputs must be greater than 1")

        # Get content type
        content_type = self._inputs.get("content_type", None)
        if content_type is None:
            raise ValueError("content_type is required for sequence personalize action")
        content_type = TofuDataListHandler.get_content_type(content_type)
        if content_type is None:
            raise ValueError("Invalid content_type format")

        content_group_ids = []
        common_content_group_params = {
            "content_type": content_type,
            "content_goal": CampaignGoal.SeqPersonalizeTemplate,
        }
        common_content_group_params["content_collection"] = (
            _get_legacy_default_content_collection_data(
                self._action_instance, num_outputs
            )
        )
        for i in range(num_outputs):
            content_group_params = copy.deepcopy(common_content_group_params)
            content_group_params["action_index"] = i
            content_group_name = f"{self._action_instance.action_name} {i+1}"
            content_group = ContentGroup.objects.create(
                creator=self._creator,
                campaign=self._campaign,
                action=self._action_instance,
                content_group_name=content_group_name,
                content_group_params=content_group_params,
            )
            content_group_ids.append(content_group.id)
            ContentGroupHandler(content_group=content_group).bulk_create_content()

        fix_content_collection_pointers(content_group_ids)

        # step 2: create action outputs
        action_outputs = {}
        data_list = TofuDataListHandler.convert_content_group_ids_to_tofu_data(
            content_group_ids
        )
        action_outputs["content_group"] = TofuDataListHandler.convert_tofu_data_to_json(
            data_list
        )
        self._action_instance.outputs = action_outputs
        self._action_instance.save(update_fields=["outputs"])

        # update inputs
        inputs_params_to_save = copy.deepcopy(self._inputs)
        inputs_params_to_save.pop("content_type", None)
        inputs_params_to_save.pop("targets", None)

        # step 3: update inputs
        self._action_instance.inputs = {
            key: TofuDataListHandler.convert_tofu_data_to_json(value)
            for key, value in inputs_params_to_save.items()
        }
        action_handler = ActionHandler(self._action_instance)
        action_handler.update_inputs(inputs_params_to_save)

    def _post_process_seq_personalize_template(self):
        action_handler = ActionHandler(self._action_instance)
        action_handler.update_targets()

    def _validate_data(self, input_fields):
        action_category = self._validated_data.get("action_category")
        action_definition = (
            ActionSystemConfigLoader().get_definition_by_action_category_and_inputs(
                action_category,
                input_fields,
            )
        )
        if not action_definition:
            raise ValueError(
                f"Failed to get action definition from {action_category} and inputs {input_fields}"
            )

        # Consolidated input validation
        for input_name, input_value in self._inputs.items():
            input_def = action_definition.required_inputs.get(
                input_name
            ) or action_definition.optional_inputs.get(input_name)
            if not input_def:
                raise ValueError(
                    f"Invalid input: {input_name} for action {action_category}"
                )
            if not TofuDataListHandler.is_valid_tofu_data_definition(
                input_def, input_value
            ):
                raise ValueError(
                    f"type doesn't match: {input_name} for definition {input_def} and value {input_value}"
                )

    def create(self):
        # must have fields
        fields_required = [
            "creator",
            "campaign",
            "playbook",
            "action_name",
            "action_category",
            "inputs",
        ]
        # optional fields
        fields_optional = ["incoming_edges", "meta"]

        for field in fields_required:
            if field not in self._validated_data:
                logging.error(f"Missing field: {field} in validated data")
                raise ValueError(f"{field} is required to create action")
        # validate all fields
        for key in self._validated_data.keys():
            if key not in fields_required + fields_optional:
                raise ValueError(f"Invalid attribute: {key} to create action")

        # TODO: remove this when we integrate with FE
        action_category = self._validated_data["action_category"]
        self._creator = self._validated_data.get("campaign").creator
        self._playbook = self._validated_data.get("playbook")
        self._campaign = self._validated_data.get("campaign")
        self._inputs = self._convert_inputs(self._validated_data.get("inputs"))

        category_enum = ActionCategory.Value(action_category)

        with transaction.atomic():

            input_fields = {}
            for input_field in CATEGORY_INPUTS_NEEDED_FOR_DEFINITION[category_enum]:
                input_data = self._inputs.get(input_field, None)
                if input_data is None:
                    raise ValueError(f"Missing input field: {input_field}")
                input_fields[input_field] = (
                    TofuDataListHandler.convert_tofu_data_to_json(input_data)
                )

            try:
                self._validate_data(input_fields)
            except Exception as e:
                raise ValueError(
                    f"Failed to validate data for creating action: {e}"
                ) from e

            self._action_instance = Action.objects.create(
                creator=self._creator,
                playbook=self._playbook,
                campaign=self._campaign,
                action_name=self._validated_data.get("action_name"),
                action_category=self._validated_data.get("action_category"),
                inputs=input_fields,
                outputs={},
                meta={},
            )

            # we need to create the action edges first
            incoming_edges = self._validated_data.get("incoming_edges", [])
            ActionEdgeCreator().create(self._action_instance, incoming_edges)

            try:
                if category_enum == ActionCategory.ACTION_CATEGORY_USER_INPUT:
                    self._create_action_user_input()
                elif category_enum == ActionCategory.ACTION_CATEGORY_REPURPOSE:
                    self._create_action_repurpose()
                elif category_enum == ActionCategory.ACTION_CATEGORY_PERSONALIZE:
                    self._create_action_personalize()
                elif category_enum == ActionCategory.ACTION_CATEGORY_EXPORT:
                    self._create_action_export()
                elif (
                    category_enum
                    == ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE
                ):
                    self._create_action_seq_personalize_template()
                else:
                    raise ValueError(f"Invalid action category: {action_category}")
            except Exception as e:
                logging.exception(f"Failed to create action: {e}")
                raise ValueError(f"Failed to create action: {e}") from e

        action_handler = ActionHandler(self._action_instance)
        action_handler.pull_results_from_incoming_actions(overwrite=False)

        # post process
        with transaction.atomic():
            self._action_instance.refresh_from_db()
            try:
                if category_enum == ActionCategory.ACTION_CATEGORY_PERSONALIZE:
                    self._post_process_personalize()
                elif (
                    category_enum
                    == ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE
                ):
                    self._post_process_seq_personalize_template()
                else:
                    pass

                action_handler = ActionHandler(self._action_instance)
                action_handler.apply_default_template()
                action_handler.update_status(save=True)
            except Exception as e:
                logging.exception(f"Failed to post process action: {e}")
                raise ValueError(f"Failed to post process action: {e}") from e

        return self._action_instance
