import copy
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict

from celery import chain, current_app, shared_task
from django.core.cache import cache
from django.db import transaction
from google.protobuf.json_format import MessageToDict, ParseDict

from ..component_selection.content_group_component_selector import (
    ContentGroupComponentSelector,
)
from ..content_group import ContentGroupHandler
from ..models import Action, ActionEdge, ContentTemplate
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionExecutionParams,
    ActionStatus,
    ActionStatusType,
    TofuComponents,
    TofuData,
    TofuDataList,
)
from .action_data_wrapper import ActionDataWrapper
from .action_handler_export import ExportActionHandler
from .action_handler_personalize import PersonalizationActionHandler
from .action_handler_repurpose import RepurposingActionHandler
from .action_handler_seq_personalize_template import SeqPersonalizeTemplateActionHandler
from .action_handler_user_input import UserInputAction<PERSON><PERSON><PERSON>


@shared_task
def execute_action_task(task_id: str, action_id: int, execution_params_dict: dict):
    try:
        action_instance = Action.objects.get(id=action_id)
        handler = ActionHandler(action_instance)
        # Only execute the main implementation, post-processing moved to separate task
        execution_params = ParseDict(execution_params_dict, ActionExecutionParams())
        return handler._action_handler_impl._execute_impl(execution_params)
    except Exception as e:
        logging.exception(f"Failed to execute action with id {action_id}: {str(e)}")
        return None


@shared_task
def post_execute_task(previous_result, action_id: int):
    try:
        action_instance = Action.objects.get(id=action_id)
        handler = ActionHandler(action_instance)
        handler.post_execute_process()
    except Exception as e:
        logging.exception(f"Failed post-execute for action {action_id}: {str(e)}")
    return previous_result


class ActionHandler:
    def __init__(self, action_instance: Action):
        self._action_handler_impl = self.get_action_handler_impl(action_instance)

    def get_action_handler_impl(self, action_instance: Action):
        action_category = action_instance.action_category
        action_category_enum = ActionCategory.Value(action_category)

        if action_category_enum == ActionCategory.ACTION_CATEGORY_PERSONALIZE:
            return PersonalizationActionHandler(action_instance)
        elif action_category_enum == ActionCategory.ACTION_CATEGORY_EXPORT:
            return ExportActionHandler(action_instance)
        elif action_category_enum == ActionCategory.ACTION_CATEGORY_REPURPOSE:
            return RepurposingActionHandler(action_instance)
        elif action_category_enum == ActionCategory.ACTION_CATEGORY_USER_INPUT:
            return UserInputActionHandler(action_instance)
        elif (
            action_category_enum
            == ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE
        ):
            return SeqPersonalizeTemplateActionHandler(action_instance)
        else:
            raise ValueError(f"Unknown action category: {action_category}")

    def execute(self, execution_params: ActionExecutionParams) -> Dict:
        # check status: has to be ready
        current_status = (
            self._action_handler_impl._action_data_wrapper.action_instance.status.get(
                "status_type"
            )
        )
        if isinstance(current_status, str):
            current_status = ActionStatusType.Value(current_status)
        if (
            current_status == ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
            and self._action_handler_impl._action_data_wrapper._action_instance.action_category
            == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_USER_INPUT)
        ):
            logging.warning(
                f"Action {self._action_handler_impl._action_data_wrapper._action_instance.action_name} is missing input for user input action and we will consider it as executed"
            )
            self.post_execute_process()
            return {
                "status": "success",
                "message": "Action is missing input for user input action and we will consider it as executed",
            }
        elif (
            current_status == ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
            or current_status == ActionStatusType.ACTION_STATUS_TYPE_UNSPECIFIED
        ):
            raise ValueError(f"Action status is not ready: {current_status}")
        elif current_status == ActionStatusType.ACTION_STATUS_TYPE_RUNNING:
            if (
                self._action_handler_impl._action_data_wrapper.action_instance.action_category
                == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_USER_INPUT)
            ):
                # we can terminate the job and then continue
                self.terminate()
            else:
                str_current_status = ActionStatusType.Name(current_status)
                str_category = (
                    self._action_handler_impl._action_data_wrapper.action_instance.action_category
                )
                logging.error(
                    f"Action status is running: {str_current_status} and cannot be executed right now for action {self._action_handler_impl.action_instance} with category {str_category}"
                )
                return {
                    "status": "error",
                    "error": f"Action status is running: {str_current_status} and cannot be executed right now for action {self._action_handler_impl.action_instance} with category {str_category}",
                }

        # Convert execution_params to dict for serialization
        execution_params_dict = MessageToDict(
            execution_params, preserving_proto_field_name=True
        )

        action_id = self._action_handler_impl._action_data_wrapper.action_instance.id
        timestamp = int(time.time() * 1000)  # Millisecond timestamp
        task_id = f"action_execution:{action_id}:{timestamp}"

        # Store task_id in cache with action_id as key
        cache_key = f"action_task:{action_id}"
        cache.set(cache_key, task_id, timeout=86400)  # Cache for 24 hours

        # Create and execute the chain of tasks
        task_chain = chain(
            execute_action_task.s(task_id, action_id, execution_params_dict),
            post_execute_task.s(action_id),
        )
        result = task_chain.apply_async(task_id=task_id)
        return {"status": "task_submitted", "task_id": result.id}

    def post_execute_process(self):
        self.update_status(save=True)
        self._action_handler_impl.action_instance.refresh_from_db()
        should_overwrite = self._action_handler_impl.get_should_overwrite()
        try:
            self.push_results_to_outgoing_actions(overwrite=should_overwrite)
        except Exception as e:
            logging.exception(
                f"error in pushing results for action: {self._action_handler_impl.action_instance} due to {str(e)}"
            )
            # TODO: put more error handling in the future

        # Clean up the cache entry for this action
        action_id = self._action_handler_impl._action_data_wrapper.action_instance.id
        cache_key = f"action_task:{action_id}"
        cache.delete(cache_key)

    # TODO: enrich the returns for terminate
    def terminate(self):
        current_status = (
            self._action_handler_impl._action_data_wrapper.action_instance.status.get(
                "status_type"
            )
        )
        if isinstance(current_status, str):
            current_status = ActionStatusType.Value(current_status)
        if current_status != ActionStatusType.ACTION_STATUS_TYPE_RUNNING:
            str_current_status = ActionStatusType.Name(current_status)
            str_category = (
                self._action_handler_impl._action_data_wrapper.action_instance.action_category
            )
            logging.error(
                f"Action status is not running: {str_current_status} and cannot be terminated for action {self._action_handler_impl.action_instance} with category {str_category}"
            )
            return

        self._action_handler_impl._terminate_impl()
        action_id = self._action_handler_impl._action_data_wrapper.action_instance.id

        # Get the latest task_id from cache
        cache_key = f"action_task:{action_id}"
        task_id = cache.get(cache_key)

        # Fallback to a default task_id format if not found in cache
        if task_id:
            # Revoke both the main task and its callback
            current_app.control.revoke(task_id, terminate=True)

        # Clear the cache entry after termination
        cache.delete(cache_key)

    # TODO: refactor: optimize if the data is not changed
    def update_status(self, save=False):
        # Check both required and optional inputs
        required_inputs = {}
        if hasattr(self._action_handler_impl.action_definition, "required_inputs"):
            required_inputs = (
                self._action_handler_impl.action_definition.required_inputs
            )
        else:
            logging.error(
                f"action_definition: {self.action_definition} does not have required_inputs"
            )
            required_inputs = {}

        optional_inputs = {}
        if hasattr(self._action_handler_impl.action_definition, "optional_inputs"):
            optional_inputs = (
                self._action_handler_impl.action_definition.optional_inputs
            )
        else:
            logging.error(
                f"action_definition: {self._action_handler_impl.action_definition} does not have optional_inputs"
            )
            optional_inputs = {}

        # Check required inputs
        missing_required_fields = []
        missing_optional_fields = []
        provided_required_fields = []
        provided_optional_fields = []

        inputs = (
            self._action_handler_impl._action_data_wrapper.get_all_user_inputs_with_completed_setup()
        )
        for input_name in required_inputs:
            input_value = inputs.get(input_name)
            if input_value:
                provided_required_fields.append(input_name)
            else:
                missing_required_fields.append(input_name)
        for input_name in optional_inputs:
            input_value = inputs.get(input_name)
            if input_value:
                provided_optional_fields.append(input_name)
            else:
                missing_optional_fields.append(input_name)
        updated_status = ActionStatus()

        # Add overall status type
        if missing_required_fields:
            updated_status.status_type = (
                ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
            )
            updated_status.message = f"Missing required inputs: {missing_required_fields} vs already provided inputs: {provided_required_fields}"
        else:
            updated_status = self._action_handler_impl._get_post_ready_status_details()

        # Add missing required inputs that aren't already present
        for field in missing_required_fields:
            if field not in updated_status.details.input_check.missing_required_inputs:
                updated_status.details.input_check.missing_required_inputs.append(field)

        # Add missing optional inputs that aren't already present
        for field in missing_optional_fields:
            if field not in updated_status.details.input_check.missing_optional_inputs:
                updated_status.details.input_check.missing_optional_inputs.append(field)

        # Add provided required inputs that aren't already present
        for field in provided_required_fields:
            if field not in updated_status.details.input_check.provided_required_inputs:
                updated_status.details.input_check.provided_required_inputs.append(
                    field
                )

        # Add provided optional inputs that aren't already present
        for field in provided_optional_fields:
            if field not in updated_status.details.input_check.provided_optional_inputs:
                updated_status.details.input_check.provided_optional_inputs.append(
                    field
                )

        if save:
            self._action_handler_impl._action_data_wrapper.action_instance.status = (
                MessageToDict(updated_status, preserving_proto_field_name=True)
            )
            self._action_handler_impl._action_data_wrapper.action_instance.save(
                update_fields=["status"]
            )

        return updated_status

    def reset_results_and_status(self):
        current_status = (
            self._action_handler_impl._action_data_wrapper.action_instance.status
        )
        if current_status["status_type"] == ActionStatusType.Name(
            ActionStatusType.ACTION_STATUS_TYPE_RUNNING
        ):
            logging.error(
                f"try to reset when action is running for {self._action_handler_impl.action_instance}"
            )
            return

        # content_group
        content_groups = self._action_handler_impl._action_data_wrapper.content_groups
        for content_group in content_groups:
            content_group_handler = ContentGroupHandler(content_group)
            content_group_handler.delete_results()

        self.update_status(save=True)

    def reset_outgoing_actions(self):
        # there are 2 scenarios:
        # 1. the current action status is changed from complete to ready; if the next action is complete or ready, it shall be updated to missing inputs;
        # 2. the current action status is changed from ready to complete; if the next action is missing inputs, it shall update the inputs;
        action_instance = self._action_handler_impl._action_data_wrapper.action_instance

        # Find all ActionEdges where the given action is the 'from_action'
        outgoing_edges = ActionEdge.objects.filter(from_action=action_instance)
        # Retrieve the 'to_action' from each edge to get the outgoing actions
        outgoing_actions = [edge.to_action for edge in outgoing_edges]

        for action in outgoing_actions:
            action_handler = ActionHandler(action)
            action_handler.reset_results_and_status()
            action_handler.reset_outgoing_actions()

    def update_action_name(self, new_action_name):
        return self._action_handler_impl.update_action_name(new_action_name)

    def update_inputs(self, inputs, partial_update=True, update_status=True):
        res = self._action_handler_impl.update_inputs(
            inputs, partial_update, update_status
        )
        self.update_status(save=True)
        return res

    def update_targets(self):
        return self._action_handler_impl.update_targets()

    @transaction.atomic()
    def delete(self):
        # do cascading deletion of actions based on the edges
        all_outgoing_edges = ActionEdge.objects.filter(
            from_action=self._action_handler_impl._action_data_wrapper.action_instance
        )
        for edge in all_outgoing_edges:
            self._delete_edge(edge)
        self._action_handler_impl._delete_special_handling()
        self._action_handler_impl._action_data_wrapper._action_instance.delete()

    def _delete_edge(self, edge: ActionEdge):
        # delete the outgoing action
        outgoing_action = edge.to_action
        outgoing_action_updater = ActionHandler(outgoing_action)
        outgoing_action_updater.delete()
        # delete the edge
        edge.delete()

    def _auto_select_components(self):
        try:
            action_instance = (
                self._action_handler_impl._action_data_wrapper.action_instance
            )
            # TODO: @zaicheng update this to be the context
            component_selector = ContentGroupComponentSelector(
                self, action_instance.creator
            )
            if component_selector.is_eligible_for_auto_selection():
                component_selector.auto_select_components(select_parameters={})
        except Exception as e:
            logging.exception(
                f"Failed to auto select components for action {self._action_handler_impl.action_instance} due to {str(e)}"
            )

    @transaction.atomic
    def _pull_inputs_from_incoming_edges(self, incoming_inputs, overwrite=False):
        """
        Update inputs with option to overwrite existing ones.

        This method follows a template pattern where common logic is handled here
        and specific logic is delegated to handler implementations.

        Args:
            incoming_inputs: Dictionary of input key-value pairs
            overwrite: Whether to overwrite existing inputs

        Returns:
            None
        """
        if not incoming_inputs:
            return

        # Prepare inputs based on overwrite flag
        inputs_to_update, is_need_reset, is_need_execute = (
            self._action_handler_impl._prepare_inputs_for_pull_inputs_on_edge(
                incoming_inputs, overwrite
            )
        )

        # Apply the inputs update
        self.update_inputs(inputs=inputs_to_update)

        # Reset status if needed by either handler
        if is_need_reset:
            self.reset_results_and_status()
        if is_need_execute and self._action_handler_impl._is_executable():
            # Execute the action
            try:
                self.execute(ActionExecutionParams())
            except Exception as e:
                logging.exception(
                    f"Failed to execute action {self._action_handler_impl.action_instance} due to {str(e)}"
                )
        if "template" in inputs_to_update:
            self._auto_select_components()

    def pull_results_from_incoming_actions(self, overwrite=False):
        all_incoming_inputs = (
            self._action_handler_impl._action_data_wrapper.get_all_inputs_from_incoming_edges()
        )
        self._pull_inputs_from_incoming_edges(all_incoming_inputs, overwrite=overwrite)

    @transaction.atomic
    def push_results_to_outgoing_actions(self, overwrite=False):
        action_instance = self._action_handler_impl._action_data_wrapper.action_instance

        # Find all ActionEdges where the given action is the 'from_action'
        outgoing_edges = ActionEdge.objects.filter(from_action=action_instance)
        for outgoing_edge in outgoing_edges:
            try:
                to_action = outgoing_edge.to_action
                to_action_data_wrapper = ActionDataWrapper(to_action)
                to_action_handler = ActionHandler(to_action)

                outgoing_inputs = to_action_data_wrapper._get_input_from_incoming_edge(
                    outgoing_edge
                )
                to_action_handler._pull_inputs_from_incoming_edges(
                    outgoing_inputs, overwrite=overwrite
                )
            except Exception as e:
                logging.exception(
                    f"Fail to push data to action {to_action} due to {str(e)}"
                )
                continue

    def apply_content_template(self, content_template):
        template_data = content_template.template_data

        inputs_to_save = self._action_handler_impl.convert_content_template_to_template(
            template_data
        )
        if inputs_to_save:
            self.update_inputs(
                inputs=inputs_to_save, partial_update=True, update_status=True
            )
        self._action_handler_impl._action_data_wrapper.update_action_meta(
            "applied_content_template_id", content_template.id, save=True
        )
        self._auto_select_components()

    def apply_default_template(self):
        try:
            action_category = (
                self._action_handler_impl.action_definition.action_category
            )
            action_category = ActionCategory.Name(action_category)
            if action_category in [
                ActionCategory.Name(ActionCategory.ACTION_CATEGORY_PERSONALIZE),
                ActionCategory.Name(ActionCategory.ACTION_CATEGORY_REPURPOSE),
            ]:
                # check if there's incoming edges
                if action_category == ActionCategory.Name(
                    ActionCategory.ACTION_CATEGORY_PERSONALIZE
                ):
                    incoming_edges = (
                        self._action_handler_impl._action_data_wrapper.action_instance.incoming_edges
                    )
                    if incoming_edges.exists():
                        logging.warning(
                            f"Default template for action {self._action_handler_impl.action_instance} is not applied due to incoming edges"
                        )
                        return

                content_type = self._action_handler_impl.content_type
                content_templates = ContentTemplate.objects.filter(
                    playbook=self._action_handler_impl._action_data_wrapper.action_instance.playbook,
                    default_content_types__contains=[content_type],
                )
                if content_templates.exists():
                    default_template = content_templates.first()
                    if default_template:
                        self.apply_content_template(default_template)
        except Exception as e:
            logging.exception(
                f"Failed to apply default template for action {self._action_handler_impl.action_instance} due to {str(e)}"
            )
