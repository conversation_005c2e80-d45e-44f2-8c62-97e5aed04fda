import copy
import logging
from typing import Any, Dict, <PERSON><PERSON>

from ..campaign_gen_wrapper import CampaignGenWrapper
from ..content_group import ContentGroupHandler
from ..gen_status import GenStatusUpdater
from ..models import Action, ContentGroup, TargetInfo
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionEdgeOutputListFetchMethod,
    ActionExecutionParams,
    ActionStatus,
    ActionStatusDetails,
    ActionStatusDetailsGenRepurpose,
    ActionStatusStats,
    ActionStatusType,
    ActionType,
    CampaignConfig,
    ContentGroupGenerationStatus,
    GenerationStatus,
    GenerationStatusType,
    TofuComponent,
    TofuComponents,
    TofuData,
    TofuDataList,
)
from .action_handler_base import ActionHandlerBase
from .mixins import CustomInstructionsMixin
from .tofu_data_wrapper import TofuDataListHandler


# TODO: refactor this to reduce duplicate code
class SeqPersonalizeTemplateActionHandler(ActionHandlerBase, CustomInstructionsMixin):
    def __init__(self, action_instance: Action = None):
        super().__init__(action_instance)

        if self._action_data_wrapper:
            self._content_groups = self._action_data_wrapper.content_groups
        else:
            self._content_groups = []

    def _delete_special_handling(self):
        for content_group in self._content_groups:
            content_group.delete()

    def _execute_impl(self, execution_params: ActionExecutionParams) -> Dict:
        campaign = self._action_data_wrapper.action_instance.campaign
        content_groups = self._action_data_wrapper.content_groups
        if not content_groups:
            raise ValueError(
                f"No content groups found for action: {self.action_instance}"
            )

        collection_ids = [f"action_{self.action_instance.id}"]
        gen_wrapper = CampaignGenWrapper(campaign)
        task_id = gen_wrapper.submit_job(
            user=self._action_data_wrapper.action_instance.creator,
            content_group_ids=[content_group.id for content_group in content_groups],
            content_ids=[],
            collection_ids=collection_ids,
            continue_gen=False,
            joint_generation=True,
            use_all_contents=False,
        )
        gen_wrapper.wait_job(task_id=task_id)

    def _terminate_impl(self):
        # follow the same as run
        campaign = self._action_data_wrapper.action_instance.campaign
        content_groups = self._action_data_wrapper.content_groups
        gen_wrapper = CampaignGenWrapper(campaign)
        gen_wrapper.terminate_gen(
            [content_group.id for content_group in content_groups]
        )

    def get_should_overwrite(self):
        return False

    def _handle_special_input(self, key, value):
        # Handle special inputs for sequence personalize template
        if key == "num_outputs":
            self._update_num_outputs(value)
        elif key == "custom_instructions":
            self._update_custom_instructions(value)
        elif key == "targets":
            self.update_targets()

    def update_action_name(self, new_action_name: str):
        self.action_instance.action_name = new_action_name
        self.action_instance.save(update_fields=["action_name"])
        for content_group in self._content_groups:
            content_group.content_group_name = new_action_name
            content_group.save(update_fields=["content_group_name"])
        return {"status": "success", "action_id": self.action_instance.id}

    def update_targets(self):
        targets_data = self._action_data_wrapper.get_input_by_name("targets")
        if not targets_data:
            return {"status": "error", "message": "No targets found in inputs"}

        target_ids = TofuDataListHandler.get_targets(targets_data)
        if not target_ids:
            return {"status": "error", "message": "Invalid targets data format"}

        # Get the actual target data
        targets = []
        try:
            targets = list(TargetInfo.objects.filter(id__in=target_ids))
            # Format targets into the expected format for bulk_create_content
            formatted_targets = {}
            for target in targets:
                group_key = target.target_info_group.target_info_group_key
                if group_key not in formatted_targets:
                    formatted_targets[group_key] = []
                formatted_targets[group_key].append(target.target_key)

            # Format as expected by bulk_create_content: [{group_key: [target_keys]}]
            targets_for_bulk = [
                {group_key: target_keys}
                for group_key, target_keys in formatted_targets.items()
            ]
        except Exception as e:
            return {"status": "error", "message": f"Error getting targets: {str(e)}"}

        for content_group in self._content_groups:
            try:
                # Use ContentGroupHandler to handle targets instead of content_group.targets.set
                ContentGroupHandler(content_group).bulk_create_content(
                    targets=targets_for_bulk
                )
            except Exception as e:
                return {
                    "status": "error",
                    "message": f"Error updating targets: {str(e)}",
                }

        return {"status": "success", "message": "Targets updated"}

    def _update_content_groups(self, content_groups):
        content_group_ids = [content_group.id for content_group in content_groups]
        self._action_data_wrapper._content_groups = content_groups
        self._action_data_wrapper._action_instance.outputs["content_group"] = (
            TofuDataListHandler.convert_tofu_data_to_json(
                TofuDataListHandler.convert_content_group_ids_to_tofu_data(
                    content_group_ids
                ),
            )
        )
        self._action_data_wrapper._action_instance.save(update_fields=["outputs"])

    def _update_num_outputs(self, num_outputs):
        if not self.content_groups:
            raise ValueError(
                f"No content groups found for action: {self.action_instance}"
            )
        int_num_outputs = TofuDataListHandler.get_int_value(num_outputs)
        # TODO: move this to config
        if int_num_outputs <= 1 or int_num_outputs > 10:
            raise ValueError(f"Invalid num_outputs: {int_num_outputs}")
        if len(self.content_groups) == int_num_outputs:
            return False

        updated_content_groups = []
        if len(self.content_groups) > int_num_outputs:
            # Delete content groups with index >= num_outputs
            for content_group in self.content_groups:
                if "action_index" not in content_group.content_group_params:
                    raise ValueError(
                        f"action_index field not found for content group: {content_group.id} with params: {content_group.content_group_params}"
                    )
                idx = content_group.content_group_params["action_index"]
                if idx is None:
                    raise ValueError(
                        f"action_index field not found for content group: {content_group.id} with params: {content_group.content_group_params}"
                    )
                if int(idx) >= int_num_outputs:
                    content_group.delete()
                else:
                    updated_content_groups.append(content_group)
        else:
            updated_content_groups = list(self.content_groups)
            first_existing_content_group = self.content_groups[0]
            for i in range(len(self.content_groups), int_num_outputs):
                content_group_name = (
                    f"{self.action_instance.action_name} - {i}"
                    if int_num_outputs > 1
                    else self.action_instance.action_name
                )
                content_group_params = copy.deepcopy(
                    first_existing_content_group.content_group_params
                )
                content_group_params["action_index"] = i
                content_group = ContentGroup.objects.create(
                    creator=first_existing_content_group.creator,
                    campaign=first_existing_content_group.campaign,
                    action=self.action_instance,
                    content_group_name=content_group_name,
                    content_group_params=content_group_params,
                )
                ContentGroupHandler(content_group=content_group).bulk_create_content()
                updated_content_groups.append(content_group)
        if len(updated_content_groups) != int_num_outputs:
            raise ValueError(
                f"Failed to update content groups: {updated_content_groups} with input: {num_outputs} for action: {self.action_instance}"
            )
        self._update_content_groups(updated_content_groups)

        return True

    def _get_post_ready_status_details(self):
        default_status = ActionStatus(
            status_type=ActionStatusType.ACTION_STATUS_TYPE_READY,
            message="Action is ready",
        )
        try:
            content_groups = self._action_data_wrapper.content_groups
            gen_status = {}

            # Track overall stats
            total_not_started = 0
            total_succ = 0
            total_fail = 0
            total_running = 0

            for content_group in content_groups:
                status_result = GenStatusUpdater().get_content_group_gen_status_v3(
                    content_group
                )
                # Add stats to totals
                stats = status_result["stats"]
                total_not_started += stats.cnts_not_started
                total_succ += stats.cnts_succ
                total_fail += stats.cnts_fail
                total_running += stats.cnts_running

                # Create ContentGroupGenerationStatus for this content group
                gen_status[content_group.id] = ContentGroupGenerationStatus(
                    content_group_gen_status=status_result["content_group_gen_status"]
                )

            status_type = self._get_status_type_based_stats(
                total_succ=total_succ,
                total_fail=total_fail,
                total_running=total_running,
            )
            return ActionStatus(
                status_type=status_type,
                details=ActionStatusDetails(
                    stats=ActionStatusStats(
                        cnts_not_started=total_not_started,
                        cnts_succ=total_succ,
                        cnts_fail=total_fail,
                        cnts_running=total_running,
                    ),
                    gen_repurpose_details=ActionStatusDetailsGenRepurpose(
                        gen_status=gen_status
                    ),
                ),
            )
        except Exception as e:
            logging.exception(f"Failed to get content group gen status: {e}")
            return default_status

    def _prepare_inputs_for_pull_inputs_on_edge(
        self, incoming_inputs, overwrite
    ) -> Tuple[Dict[str, Any], bool, bool]:
        """
        Prepare inputs for update based on overwrite flag.
        Args:
            incoming_inputs: Dictionary of input key-value pairs
            overwrite: Whether to overwrite existing inputs
        Returns:
            Tuple[Dict[str, Any], bool, bool]: Inputs to be updated, need_reset, need_execute
        """
        raise NotImplementedError(
            "_prepare_inputs_for_pull_inputs_on_edge() shall not be called for this action"
        )
