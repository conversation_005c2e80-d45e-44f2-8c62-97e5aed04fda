import copy
import logging

from google.protobuf.json_format import MessageToDict, ParseDict

from ..models import Action, ContentGroup
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionInputOutputDefinition,
    ActionMetaData,
    ActionStatusType,
    TofuComponents,
    TofuData,
    TofuDataList,
    TofuDataType,
    TofuExportSettings,
    TofuTemplate,
)
from .action_system_config_loader import ActionSystemConfigLoader
from .legacy_converter.legacy_campaign_targets_converter import (
    convert_campaign_targets_v2_to_v3,
)
from .legacy_converter.legacy_components_converter import convert_components_v2_to_v3
from .legacy_converter.legacy_custom_instruction_converter import (
    convert_custom_instructions_v2_to_v3,
)
from .legacy_converter.legacy_export_settings_converter import (
    convert_export_settings_v2_to_v3,
)
from .legacy_converter.legacy_repurpose_template_converter import (
    convert_repurpose_template_v2_to_v3,
)
from .legacy_converter.legacy_template_converter import convert_template_v2_to_v3
from .repurpose_to_anchor import RepurposeToAnchorConverter
from .repurpose_to_p13n import RepurposeToP13nTemplateConverter
from .tofu_data_wrapper import TofuDataListHandler


class ActionDataWrapper:
    def __init__(self, action_instance: Action):
        self._action_instance = action_instance
        self._action_instance.refresh_from_db()

        # preprocessing
        self._content_groups = self._get_content_groups()
        # self._all_user_inputs = self.get_all_user_inputs()
        self._action_definition = None

    @property
    def action_definition(self):
        if not self._action_definition:
            self._action_definition = self._get_action_definition()
        return self._action_definition

    @property
    def action_instance(self):
        return self._action_instance

    def get_input_definition(self, input_name):
        action_definition = self._get_action_definition()
        if input_name in action_definition.required_inputs:
            return action_definition.required_inputs[input_name]
        elif input_name in action_definition.optional_inputs:
            return action_definition.optional_inputs[input_name]
        else:
            logging.error(
                f"Input definition for {input_name} not found in action {self._action_instance.id}"
            )
            return None

    # return all content groups
    @property
    def content_groups(self):
        return self._content_groups

    # return one content group, raise error if there is no content group or more than one content group
    # used for p13n or export action
    @property
    def content_group(self):
        if len(self._content_groups) != 1:
            raise ValueError(
                f"Expected 1 content group, got {len(self._content_groups)}"
            )
        return self._content_groups[0]

    def _get_content_groups(self):
        try:
            content_group_data = self._action_instance.outputs.get("content_group", [])
        except Exception as e:
            content_group_data = []
        if not content_group_data:
            return []
        content_group_ids = TofuDataListHandler.get_content_group_ids(
            content_group_data
        )
        return ContentGroup.objects.filter(id__in=content_group_ids)

    def _get_action_definition(self):
        if self._action_definition is not None:
            return self._action_definition

        action_inputs = self._action_instance.inputs
        action_inputs_converted = {
            k: TofuDataListHandler.parse_json_to_tofu_data(v)
            for k, v in action_inputs.items()
        }
        action_definition = (
            ActionSystemConfigLoader().get_definition_by_action_category_and_inputs(
                self._action_instance.action_category,
                action_inputs_converted,
            )
        )
        if not action_definition:
            raise ValueError(
                f"Action definition not found for action {self._action_instance.id}"
            )
        self._action_definition = action_definition
        return action_definition

    def _check_template_is_valid(self, template_tofu_data):
        if isinstance(template_tofu_data, TofuDataList):
            if len(template_tofu_data.data) > 1:
                raise ValueError(
                    f"Expected 1 template, got {len(template_tofu_data.data)}"
                )
            if len(template_tofu_data.data) == 0:
                logging.error(f"template_tofu_data is empty")
                return False
            template_tofu_data = template_tofu_data.data[0]
        if not isinstance(template_tofu_data, TofuTemplate):
            raise ValueError(f"Invalid template type: {type(template_tofu_data)}")
        # check if some fields are not empty
        if not template_tofu_data.template_fields:
            logging.error(f"template_tofu_data.template_fields is empty")
            return False

        return True

    def _check_custom_instructions_is_valid(self, custom_instructions_v3):
        if not isinstance(custom_instructions_v3, TofuDataList):
            raise ValueError(
                f"Invalid custom instructions type: {type(custom_instructions_v3)}"
            )
        return bool(custom_instructions_v3.data)

    def _check_export_settings_is_valid(self, export_settings_v3):
        if not isinstance(export_settings_v3, TofuDataList):
            raise ValueError(
                f"Invalid export settings type: {type(export_settings_v3)}"
            )
        if len(export_settings_v3.data) > 1:
            raise ValueError(
                f"Expected 1 export settings, got {len(export_settings_v3.data)}"
            )
        if len(export_settings_v3.data) == 0:
            return False

        # Extract TofuExportSettings from TofuData
        export_settings_data = export_settings_v3.data[0].export_settings
        if not isinstance(export_settings_data, TofuExportSettings):
            raise ValueError(
                f"Invalid export settings type: {type(export_settings_data)}"
            )
        return True

    def _get_content_type_data(self):
        content_type = self._get_str_content_type()
        return TofuDataListHandler.convert_content_type_to_tofu_data(content_type)

    def _get_str_content_type(self):
        first_content_group = self.content_group
        content_type = first_content_group.content_group_params["content_type"]
        return content_type

    def _check_components_is_valid(self, components):
        if not isinstance(components, TofuComponents):
            raise ValueError(f"Invalid components type: {type(components)}")
        if len(components.components) == 0:
            return False
        return True

    def _get_components_data(self):
        first_content_group = self.content_group
        components = first_content_group.components
        if not components:
            return None

        tofu_components = convert_components_v2_to_v3(
            self._action_instance.playbook.id, components
        )
        if not self._check_components_is_valid(tofu_components):
            return None
        if isinstance(tofu_components, TofuComponents):
            # Wrap TofuComponents in TofuData before adding to TofuDataList
            tofu_data = TofuData(components=tofu_components)
            tofu_components = TofuDataList(data=[tofu_data])
        return tofu_components

    def _get_template_data(self):
        first_content_group = self.content_group
        template_tofu_data = convert_template_v2_to_v3(
            self._action_instance.playbook.id,
            first_content_group.content_group_params,
        )
        if not template_tofu_data:
            return None
        if not self._check_template_is_valid(template_tofu_data):
            return None
        return TofuDataListHandler.convert_template_to_tofu_data(template_tofu_data)

    def _get_repurpose_template_data(self):
        if not self.content_groups:
            logging.error(
                f"No content groups found for action: {self._action_instance}"
            )
            return None
        first_content_group = self.content_groups[0]
        template_tofu_data = convert_repurpose_template_v2_to_v3(
            self._action_instance.playbook.id,
            first_content_group.content_group_params,
        )
        if not template_tofu_data:
            return None
        if not self._check_template_is_valid(template_tofu_data):
            return None
        return TofuDataListHandler.convert_template_to_tofu_data(template_tofu_data)

    def _get_campaign_targets_data(self):
        tofu_data_campaign_targets = self._action_instance.campaign.campaign_params.get(
            "tofu_target_data", None
        )
        if tofu_data_campaign_targets is not None:
            try:
                converted_tofu_data_campaign_targets = (
                    TofuDataListHandler.parse_json_to_tofu_data(
                        tofu_data_campaign_targets
                    )
                )
                if not isinstance(converted_tofu_data_campaign_targets, TofuDataList):
                    raise ValueError(
                        f"Invalid campaign targets type: {type(converted_tofu_data_campaign_targets)}"
                    )
                return converted_tofu_data_campaign_targets
            except Exception as e:
                logging.exception(
                    f"Error converting campaign targets for tofu data format: {e}"
                )

        # fallback
        campaign_targets = self._action_instance.campaign.campaign_params.get("targets")
        if not campaign_targets:
            return None
        campaign_targets_tofu_data = convert_campaign_targets_v2_to_v3(
            campaign_targets, self._action_instance.playbook_id
        )
        if not isinstance(campaign_targets_tofu_data, TofuDataList):
            raise ValueError(
                f"Invalid campaign targets type: {type(campaign_targets_tofu_data)}"
            )
        return campaign_targets_tofu_data

    def _get_custom_instructions_data_single_group(self):
        """Get custom instructions from a single content group."""
        if not self.content_groups:
            logging.error(
                f"No content groups found for action: {self._action_instance}"
            )
            return None
        first_content_group = self.content_groups[0]
        custom_instructions = first_content_group.content_group_params.get(
            "custom_instructions", []
        )
        if not custom_instructions:
            return None
        return convert_custom_instructions_v2_to_v3(
            self._action_instance.playbook.id, custom_instructions
        )

    def _get_custom_instructions_data(
        self, consider_content_group_level_instructions=False
    ):
        """Get custom instructions based on action category and content groups.

        For repurpose actions with collection:
        - Always includes collection level instructions
        - Optionally includes content group level instructions based on the parameter

        For other cases:
        - Only includes instructions from the first content group
        """
        if not self.content_groups:
            logging.error(
                f"No content groups found for action: {self._action_instance}"
            )
            return None

        is_collection = len(self.content_groups) > 1
        # TODO: add a unittest for sequence personalize template
        is_repurpose_or_seq_personalize_template = (
            self._action_instance.action_category
            == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_REPURPOSE)
            or self._action_instance.action_category
            == ActionCategory.Name(
                ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE
            )
        )

        # For repurpose action with collection
        if is_repurpose_or_seq_personalize_template and is_collection:
            return self._get_custom_instructions_data_for_collection(
                consider_content_group_level_instructions
            )

        # For all other cases, use single group logic
        return self._get_custom_instructions_data_single_group()

    def _get_custom_instructions_data_for_collection(
        self, consider_content_group_level_instructions=False
    ):
        """Get custom instructions for repurpose actions with collection (multiple content groups).

        Args:
            consider_content_group_level_instructions: If True, include instructions from all content groups.
                                                     If False, only include collection level instructions.
        """
        all_custom_instructions = []

        # Get collection level instructions
        first_content_group = self.content_groups[0]
        content_collection = first_content_group.content_group_params.get(
            "content_collection", {}
        )
        collection_instructions = content_collection.get("custom_instructions", [])
        if collection_instructions:
            all_custom_instructions.extend(collection_instructions)

        # Get content group level instructions if requested
        if consider_content_group_level_instructions:
            for content_group in self.content_groups:
                group_instructions = content_group.content_group_params.get(
                    "custom_instructions", []
                )
                if group_instructions:
                    all_custom_instructions.extend(group_instructions)

        if not all_custom_instructions:
            return None

        custom_instructions_v3 = convert_custom_instructions_v2_to_v3(
            self._action_instance.playbook.id,
            all_custom_instructions,
        )
        if not self._check_custom_instructions_is_valid(custom_instructions_v3):
            return None
        return custom_instructions_v3

    def _get_export_settings_data(self):
        first_content_group = self.content_group
        content_group_params = first_content_group.content_group_params
        if not content_group_params:
            return None
        export_settings_v3 = convert_export_settings_v2_to_v3(
            content_group_params,
        )
        if not self._check_export_settings_is_valid(export_settings_v3):
            return None
        return export_settings_v3

    def _list_specific_inputs(self):
        return [
            "components",
            "targets",
            "export_settings",
            "custom_instructions",
        ]

    def _get_specific_input(self, input_name):
        if input_name not in self._list_specific_inputs():
            return None
        if input_name == "components":
            return self._get_components_data()
        elif input_name == "targets":
            return self._get_campaign_targets_data()
        elif input_name == "export_settings":
            return self._get_export_settings_data()
        elif input_name == "custom_instructions":
            return self._get_custom_instructions_data()
        else:
            logging.error(f"Input field doesn't need special handling: {input_name}")
        return None

    def _is_valid_tofu_data_list(self, tofu_data_list, input_name):
        """
        Validates if the input is a valid TofuDataList.
        Returns True if valid, False otherwise.
        """
        if not tofu_data_list:
            return False
        if not isinstance(tofu_data_list, TofuDataList):
            logging.error(
                f"Invalid tofu data list type: {type(tofu_data_list)} for key {input_name}"
            )
            return False
        return True

    def _get_input_with_completed_setup(self, input_name, tofu_data_list):

        if input_name == "template":
            # Check if any template field has a content source copy
            if not self._is_valid_tofu_data_list(tofu_data_list, input_name):
                return None
            tofuTemplate = TofuDataListHandler.get_template(tofu_data_list)
            has_content_source_copy = any(
                field.content_source_copy
                for field in tofuTemplate.template_fields.values()
            )
            return tofu_data_list if has_content_source_copy else None
        elif input_name == "assets" or input_name == "anchor_content":
            if not self._is_valid_tofu_data_list(tofu_data_list, input_name):
                return None
            asset_ids, asset_group_ids = TofuDataListHandler.get_assets(tofu_data_list)
            return (
                tofu_data_list
                if (len(asset_ids) > 0 or len(asset_group_ids) > 0)
                else None
            )
        elif input_name == "custom_instructions":
            return self._combine_custom_instructions()
        else:
            return tofu_data_list

    def get_input_by_name(self, input_name):
        if input_name in self._list_specific_inputs():
            return self._get_specific_input(input_name)
        elif input_name in self._action_instance.inputs:
            return self._action_instance.inputs.get(input_name, None)
        # else:
        #     all_inputs_from_incoming_edges = self.get_all_inputs_from_incoming_edges()
        #     if input_name in all_inputs_from_incoming_edges:
        #         return all_inputs_from_incoming_edges.get(input_name, None)
        logging.error(f"Input field doesn't exist: {input_name}")
        return None

    def get_all_user_inputs(self):
        all_user_inputs = {}
        inputs = self._action_instance.inputs or {}

        for key, value in inputs.items():
            tofu_data_list = TofuDataList()
            try:
                if isinstance(value, TofuDataList):
                    tofu_data_list = value
                elif isinstance(value, dict):
                    tofu_data_list = TofuDataListHandler.parse_json_to_tofu_data(value)
                else:
                    logging.error(f"Unexpected input type for key {key}: {type(value)}")
                    continue
            except Exception as e:
                logging.exception(
                    f"Failed to parse TofuDataList for key {key} for value: {value}: {e}"
                )
                continue
            all_user_inputs[key] = tofu_data_list

        action_definition = self._get_action_definition()
        inputs_needed = list(action_definition.required_inputs.keys()) + list(
            action_definition.optional_inputs.keys()
        )

        specific_user_inputs = {}
        auto_pull_enabled_inputs = [
            "template",
        ]

        for input_name in inputs_needed:
            if (
                input_name not in all_user_inputs
                and input_name in auto_pull_enabled_inputs
            ):
                # we only need to check for specific inputs when it's provided by user input
                continue
            specific_input = self._get_specific_input(input_name)
            if specific_input:
                specific_user_inputs[input_name] = specific_input
            elif specific_input is None and input_name in self._list_specific_inputs():
                logging.warning(
                    f"Specific input {input_name} is invalid for action {self._action_instance.id}"
                )
                # The specific from content group is the source of truth, so if it's not found, we remove it
                specific_user_inputs[input_name] = None
        all_user_inputs.update(specific_user_inputs)
        # remove None values
        all_user_inputs = {k: v for k, v in all_user_inputs.items() if v is not None}

        # some validation
        for key, value in all_user_inputs.items():
            if not isinstance(value, TofuDataList):
                raise ValueError(
                    f"Expected tofu data list, got {type(value)} for key {key}"
                )

        return all_user_inputs

    def get_all_user_inputs_with_completed_setup(self):
        all_user_inputs = self.get_all_user_inputs()
        all_user_inputs_with_completed_setup = {}
        action_definition = self._get_action_definition()
        inputs_needed = list(action_definition.required_inputs.keys()) + list(
            action_definition.optional_inputs.keys()
        )
        for key in inputs_needed:
            value = all_user_inputs.get(key)
            value_with_completed_setup = self._get_input_with_completed_setup(
                key, value
            )
            if value_with_completed_setup:
                all_user_inputs_with_completed_setup[key] = value_with_completed_setup
        return all_user_inputs_with_completed_setup

    def _get_output_from_incoming_action(
        self,
        edge_config: dict,
        input_name_to_outgoing_action: str,
        output_from_incoming_action: TofuDataList,
        to_action: Action,
    ):
        if isinstance(output_from_incoming_action, dict):
            output_from_incoming_action = TofuDataListHandler.parse_json_to_tofu_data(
                output_from_incoming_action
            )
        if not isinstance(output_from_incoming_action, TofuDataList):
            raise ValueError(
                f"Expected tofu data list, got {type(output_from_incoming_action)} for key {input_name_to_outgoing_action}"
            )

        output_tofu_data_list = None
        if "index" in edge_config:
            index_data = edge_config["index"]

            def process_index(idx):
                try:
                    result_index = int(idx)
                except ValueError:
                    error_msg = f"Index is not a valid integer for action {self._action_instance.id} for edge config {edge_config}"
                    logging.error(error_msg)
                    return None

                if result_index < 0 or result_index >= len(
                    output_from_incoming_action.data
                ):
                    error_msg = f"Index {result_index} is out of bounds for action {self._action_instance.id}, max is {len(output_from_incoming_action.data) - 1}"
                    logging.error(error_msg)
                    return None

                return output_from_incoming_action.data[result_index]

            output_data_list = []
            if isinstance(index_data, int):
                output_data = process_index(index_data)
                if output_data is None:
                    raise ValueError(
                        f"Invalid index {index_data} for action {self._action_instance.id}"
                    )
                output_data_list.append(output_data)
            elif isinstance(index_data, list):
                for index in index_data:
                    output_data = process_index(index)
                    if output_data is not None:
                        output_data_list.append(output_data)
            else:
                logging.error(
                    f"Unexpected index_data type: {type(index_data)} for action {self._action_instance.id}"
                )
            output_tofu_data_list = TofuDataList(data=output_data_list)
        else:  # pass everything
            output_tofu_data_list = output_from_incoming_action

        # if need conversion

        input_definition = self.get_input_definition(input_name_to_outgoing_action)
        if not input_definition:
            raise ValueError(
                f"Input definition for {input_name_to_outgoing_action} not found in action {self._action_instance.id}"
            )
        if not isinstance(input_definition, ActionInputOutputDefinition):
            raise ValueError(
                f"Input definition for {input_name_to_outgoing_action} is not a tofu data definition in action {self._action_instance.id}"
            )
        if TofuDataListHandler.is_valid_tofu_data_definition(
            input_definition, output_tofu_data_list
        ):
            return output_tofu_data_list
        else:
            return self._convert_tofu_data_type(
                input_definition, output_tofu_data_list, to_action
            )

    def _convert_tofu_data_type(self, input_definition, tofu_data_list, to_action):
        if (
            TofuDataListHandler.is_valid_tofu_data_type(
                TofuDataType.TOFU_DATA_TYPE_CONTENT_GROUP, tofu_data_list
            )
            and input_definition.data_type == TofuDataType.TOFU_DATA_TYPE_TEMPLATE
        ):
            return self._convert_content_group_to_template(tofu_data_list, to_action)
        elif (
            TofuDataListHandler.is_valid_tofu_data_type(
                TofuDataType.TOFU_DATA_TYPE_CONTENT_GROUP, tofu_data_list
            )
            and input_definition.data_type == TofuDataType.TOFU_DATA_TYPE_ASSET
        ):
            return self._convert_content_group_to_asset(tofu_data_list, to_action)
        else:
            logging.error(
                f"debug: Conversion from tofu data type {type(tofu_data_list)} for {tofu_data_list} to {input_definition.data_type} is not implemented for action {self._action_instance.id}"
            )
            return tofu_data_list

    def _convert_content_groups(self, tofu_data_list, converter_class, to_action):
        if not isinstance(tofu_data_list, TofuDataList):
            raise ValueError(f"Expected tofu data list, got {type(tofu_data_list)}")
        content_group_ids = TofuDataListHandler.get_content_group_ids(tofu_data_list)
        if not content_group_ids:
            raise ValueError("Expected 1 or more content group ids, got 0")

        # Fetch all content groups in one query first
        content_groups = ContentGroup.objects.filter(id__in=content_group_ids)
        content_groups_dict = {cg.id: cg for cg in content_groups}

        # Sort content_group_ids using the already fetched content groups
        content_group_ids.sort(
            key=lambda x: content_groups_dict[x].content_group_params.get(
                "action_index", 0
            )
        )

        data = []
        for content_group_id in content_group_ids:
            content_group = content_groups_dict[content_group_id]
            converted = converter_class(content_group, to_action).process()
            tofu_data = TofuData(**{converter_class.output_type: converted})
            data.append(tofu_data)

        return TofuDataList(data=data)

    def _convert_content_group_to_template(self, tofu_data_list, to_action):
        return self._convert_content_groups(
            tofu_data_list, RepurposeToP13nTemplateConverter, to_action
        )

    def _convert_content_group_to_asset(self, tofu_data_list, to_action):
        return self._convert_content_groups(
            tofu_data_list, RepurposeToAnchorConverter, to_action
        )

    def _get_input_from_incoming_edge(self, edge):
        inputs = {}

        from_action = edge.from_action
        to_action = edge.to_action

        # Skip if incoming action is not ready
        status_type = from_action.status.get("status_type")
        if not status_type or status_type not in ActionStatusType.keys():
            logging.error(
                f"Invalid status type '{status_type}' for action {from_action.id}"
            )
            return {}
        if status_type != ActionStatusType.Name(
            ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
        ):
            logging.warning(
                f"Incoming action {from_action.id} is not complete with status {from_action.status}"
            )
            if (
                status_type
                == ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_FAIL)
                or status_type
                == ActionStatusType.Name(
                    ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                )
                and self._action_instance.action_category
                == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_REPURPOSE)
                and from_action.action_category
                == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_USER_INPUT)
            ):
                return {"anchor_content": {}}
            return {}

        # Get edge config and output mapping
        edge_config = edge.config
        if (
            not edge_config
            or "input_name_to_outgoing_action" not in edge_config
            or "output_name_from_incoming_action" not in edge_config
        ):
            raise ValueError(
                f"Edge config for action {self._action_instance.id} is missing input_name_to_outgoing_action or output_name_from_incoming_action as {edge_config}"
            )

        input_name_to_outgoing_action = edge_config["input_name_to_outgoing_action"]
        output_name_from_incoming_action = edge_config[
            "output_name_from_incoming_action"
        ]
        output_from_incoming_action = from_action.outputs.get(
            output_name_from_incoming_action, None
        )
        if not output_from_incoming_action:
            logging.error(f"Output from incoming action {from_action.id} is not valid")
            return {}

        input_from_incoming_action = self._get_output_from_incoming_action(
            edge_config,
            input_name_to_outgoing_action,
            output_from_incoming_action,
            to_action,
        )
        if not input_from_incoming_action:
            raise ValueError(
                f"Input from incoming action {from_action.id} is not valid"
            )
        inputs[input_name_to_outgoing_action] = input_from_incoming_action
        return inputs

    def get_all_inputs_from_incoming_edges(self):
        all_inputs = {}
        # Get all incoming edges for this action
        incoming_edges = self._action_instance.incoming_edges.select_related(
            "from_action"
        ).all()
        # Iterate through incoming edges
        for edge in incoming_edges:
            action_inputs = self._get_input_from_incoming_edge(edge)
            all_inputs.update(action_inputs)

        return all_inputs

    def get_all_inputs(self):
        all_inputs = copy.deepcopy(self.get_all_user_inputs())

        # all_inputs_from_incoming_edges = self._get_all_inputs_from_incoming_edges()
        # all_inputs.update(all_inputs_from_incoming_edges)

        return all_inputs

    def get_action_status(self):
        status_type = self._action_instance.status.get("status_type")
        if not status_type or status_type not in ActionStatusType.keys():
            logging.error(
                f"Invalid status type '{status_type}' for action {self._action_instance.id}"
            )
            return None
        if isinstance(status_type, str):
            status_type = ActionStatusType.Value(status_type)
        return status_type

    def _combine_custom_instructions(self):
        """
        Combines custom instructions from components and standalone custom instructions.
        Returns a TofuDataList if there are any instructions, otherwise None.
        """
        combined_instructions = TofuDataList()

        # we only check components level custom instructions if components is a valid input keys
        action_definition = self._get_action_definition()
        inputs_needed = list(action_definition.required_inputs.keys()) + list(
            action_definition.optional_inputs.keys()
        )
        if "components" in inputs_needed:
            # Get instructions from components
            components_data = self._get_components_data()
            if components_data:
                components = TofuDataListHandler.get_components(components_data)
                if components:
                    for component in components.components.values():
                        if component.component_custom_instructions.data:
                            combined_instructions.data.extend(
                                component.component_custom_instructions.data
                            )

        # Add standalone custom instructions from all content groups
        custom_instructions = self._get_custom_instructions_data(
            consider_content_group_level_instructions=True
        )
        if custom_instructions and custom_instructions.data:
            combined_instructions.data.extend(custom_instructions.data)

        return combined_instructions if combined_instructions.data else None

    @property
    def action_meta(self):
        if not self._action_instance.meta:
            return None
        # convert to ActionMetaData
        return ParseDict(self._action_instance.meta, ActionMetaData())

    def update_action_meta(self, key, value, save=True):
        try:
            current_meta = self.action_meta
            if not current_meta:
                current_meta = ActionMetaData()
            # Validate that the field exists in the protobuf schema
            field_names = [field.name for field in current_meta.DESCRIPTOR.fields]
            if key not in field_names:
                raise ValueError(
                    f"Field '{key}' does not exist in ActionMetaData schema. Available fields: {field_names}"
                )
            setattr(current_meta, key, value)
            self._action_instance.meta = MessageToDict(
                current_meta, preserving_proto_field_name=True
            )
            if save:
                self._action_instance.save(update_fields=["meta"])
                self._action_instance.refresh_from_db()
        except Exception as e:
            logging.exception(
                f"Failed to update action meta for action {self._action_instance.id} due to {str(e)}"
            )
