import hashlib
import json
import logging
import re
from enum import Enum
from typing import Any, Dict, Optional
from urllib.parse import urlparse

from django.core.cache import cache
from langchain_core.messages import HumanMessage
from pydantic import BaseModel

from ..data_loaders.web_page_loader import TofuWebPageLoader
from ..evaluator.evaluators.pregen_evaluators.anchor_content_precheck import (
    AnchorContentPrecheckEvaluator,
)
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..prompt.prompt_library.prompt_connected_assets import (
    URL_SUPPORT_VALIDATION_PROMPT,
)
from ..task_registry import GenerationGoal
from ..utils import CloudWatchMetrics, get_token_count
from .connected_assets_utils import verify_url_exists


class ValidationStatus(str, Enum):
    OK = "ok"
    WARNING = "warning"
    ERROR = "error"


class ValidationError:
    """Centralized error messages for URL validation"""

    VIDEO_WEBINAR = "Looks like might be a webinar page—we can't download the videos. If you'd still like to use it in Tofu, we recommend manually uploading it in an assets list."
    GATED = "Looks like this might be a page with gated assets and we don't have access to the content. If you'd still like to use it in Tofu, we recommend manually uploading it in an assets list."
    NOTION = "We can't connect to Notion. If you'd still like to include it, please upload the files manually as an asset list."
    ROOT_URL = "Connected sources does best when you give a specific page on your website like company.com/casestudies or company.com/blog"


class ConnectedAssetsUrlValidationResult(BaseModel):
    url: str
    error_message: Optional[str]
    status: ValidationStatus


class ConnectedAssetsUrlValidator:
    """Validates URLs for connected assets by checking various conditions"""

    def __init__(self, url: str, cache_enabled: bool = True):
        self.url = self.add_schema_if_missing(url)
        self.content_evaluator = AnchorContentPrecheckEvaluator()
        model_config = ModelConfigResolver.resolve(
            GenerationGoal.CONNECTED_SOURCE_REFRESH,
        )
        self.model_caller = ModelCaller(model_config)
        # Define a cache timeout (30 days)
        self.cache_timeout = 60 * 60 * 24 * 30  # Changed TTL to 30 days
        self.cache_enabled = cache_enabled

    def _create_valid_result(self) -> ConnectedAssetsUrlValidationResult:
        """Creates a valid validation result"""
        return ConnectedAssetsUrlValidationResult(
            url=self.url, error_message=None, status=ValidationStatus.OK
        )

    def _create_invalid_result(
        self, error_message: str
    ) -> ConnectedAssetsUrlValidationResult:
        """Creates an invalid validation result with the given error message"""
        return ConnectedAssetsUrlValidationResult(
            url=self.url, error_message=error_message, status=ValidationStatus.ERROR
        )

    def _create_warning_result(
        self, error_message: str
    ) -> ConnectedAssetsUrlValidationResult:
        """Creates a warning validation result with the given error message"""
        return ConnectedAssetsUrlValidationResult(
            url=self.url, error_message=error_message, status=ValidationStatus.WARNING
        )

    def _is_notion_url(self) -> bool:
        """Checks if the URL points to a Notion page"""
        return "notion.so" in self.url

    def _is_gated_url(self) -> bool:
        """Checks if the URL points to a gated page"""
        return not verify_url_exists(self.url)

    def _is_root_url(self) -> bool:
        """
        Checks if the URL is just a root domain without a path.
        Returns True for URLs like 'example.com' or 'example.com/'
        Returns False for URLs like 'example.com/page' or 'example.com/path/to/page'
        """
        parsed_url = urlparse(self.url.strip("/"))
        return not parsed_url.path

    def _content_meaningfulness_cache_key(self) -> str:
        """Generates a cache key for the content meaningfulness check."""
        # Use a hash of the URL and the prompt template structure
        prompt_hash = hashlib.md5(
            URL_SUPPORT_VALIDATION_PROMPT.encode("utf-8")
        ).hexdigest()[:8]
        url_hash = hashlib.md5(self.url.encode("utf-8")).hexdigest()[:16]
        return f"connected_assets_meaningfulness:{url_hash}:prompt_{prompt_hash}"

    def _generate_sub_pages_content(self) -> str:
        """
        Generates content for sub-pages to determine if the site is generally accessible
        """
        # Check a few sub-pages to determine if the site is generally accessible
        sub_page_token_budget = self.model_caller.model_config.model_budget * 0.6
        web_page_loader = TofuWebPageLoader(
            url=self.url,
            deep_crawl=True,
            crawler_max_depth=2,
            crawler_max_page=5,
        )
        sub_docs = web_page_loader.load_deep(
            deep_crawl_only_same_folder=True, always_crawl_next_page=False
        )
        sub_docs = web_page_loader.clean_text(sub_docs)

        # pick up based on get_token_count
        sub_page_contents = []
        total_token_count = 0
        for doc in sub_docs:
            if doc.metadata.get("source", "") == self.url:
                continue
            token_count = get_token_count(doc.page_content)
            if total_token_count + token_count > sub_page_token_budget:
                break
            sub_page_contents.append(
                f"Page URL: {doc.metadata.get('source', '')}\n"
                f"Page Content: {doc.page_content}\n"
            )
            total_token_count += token_count
        return "\n---\n".join(sub_page_contents)

    # TODO: move this to utils since it is being used offten
    def _truncate_content_by_tokens(self, content: str, token_budget: int) -> str:
        """Truncates content to fit within token budget."""
        content_tokens = get_token_count(content)
        if content_tokens <= token_budget:
            return content

        # we assume each token is 4 characters
        truncated_content = content[: 4 * token_budget]

        logging.warning(
            f"Content truncated from {content_tokens} to {get_token_count(truncated_content)} tokens"
        )
        CloudWatchMetrics.put_metric(
            "connected_assets_url_validator_content_truncated",
            1,
            [],
        )

        return truncated_content

    def _is_meaningful_content(self) -> Dict[str, Any]:
        # Check cache first
        cache_key = self._content_meaningfulness_cache_key()
        cached_result = cache.get(cache_key)
        if cached_result and self.cache_enabled:
            try:
                logging.info(
                    f"Using cached content meaningfulness result for URL {self.url}"
                )
                return json.loads(cached_result)
            except Exception as e:
                logging.exception(f"Failed to parse cached result for {self.url}: {e}")

        try:
            # First check the main URL
            main_docs = TofuWebPageLoader(
                url=self.url,
                deep_crawl=False,
            ).load()

            if not main_docs:
                return {
                    "label": "FAIL",
                    "comment": "No content could be extracted from the URL. We will pass this URL",
                    "reason": "ERROR",
                }

            # Combine main document content with 30% budget
            main_content = "\n".join(doc.page_content for doc in main_docs)
            main_content_budget = int(self.model_caller.model_config.model_budget * 0.3)
            main_content = self._truncate_content_by_tokens(
                main_content, main_content_budget
            )

            # Get sub-pages content (using 60% budget as defined in _generate_sub_pages_content)
            sub_page_contents = self._generate_sub_pages_content()

            prompt = URL_SUPPORT_VALIDATION_PROMPT.format(
                url=self.url,
                content=main_content,
                sub_pages_content=sub_page_contents,
                title=main_docs[0].metadata.get("title", ""),
                root_url_error=ValidationError.ROOT_URL,
                video_webinar_error=ValidationError.VIDEO_WEBINAR,
                gated_error=ValidationError.GATED,
            )

            # Get the analysis result from the model
            result = self.model_caller.get_llm_dict_response(
                [HumanMessage(content=prompt)]
            )

            # Result validation
            if (
                "label" not in result
                or "comment" not in result
                or "reason" not in result
            ):
                raise ValueError("Invalid result from LLM")

            # Cache the successful result
            try:
                cache.set(cache_key, json.dumps(result), timeout=self.cache_timeout)
            except Exception as e:
                logging.exception(f"Failed to set cache for {self.url}: {e}")

            return {
                "label": result["label"],
                "comment": result["comment"],
                "reason": result["reason"],
            }
        except Exception as e:
            logging.exception(
                f"Error checking content meaningfulness for URL {self.url}: {str(e)}, We will bypass this URL"
            )
            CloudWatchMetrics.put_metric(
                "connected_assets_url_validator_content_meaningfulness_error",
                1,
                [
                    {
                        "Name": "url",
                        "Value": self.url,
                    },
                ],
            )
            return {
                "label": "FAIL",
                "comment": f"Error analyzing content: {str(e)}, We will bypass this URL",
                "reason": "ERROR",
            }

    def validate(self) -> ConnectedAssetsUrlValidationResult:
        """
        Validates the URL by checking multiple conditions in sequence.
        Returns the first validation failure or a success result.
        """
        try:
            # Check if URL is just a root domain (not allowed)
            if self._is_root_url():
                return self._create_result_from_reason("ROOT_URL")

            # Check for Notion URLs, currently we don't support them
            if self._is_notion_url():
                return self._create_result_from_reason("NOTION")

            # Check for gated URLs, currently we don't support them
            if self._is_gated_url():
                return self._create_result_from_reason("GATED")

            # Check content meaningfulness including sub-pages
            content_result = self._is_meaningful_content()
            if content_result["label"] != "PASS":
                return self._create_result_from_content_result(content_result)

            return self._create_valid_result()
        except Exception as e:
            logging.exception(
                f"Error validating URL {self.url}: {str(e)}, We will bypass this URL"
            )
            CloudWatchMetrics.put_metric(
                "connected_assets_url_validator_error",
                1,
                [
                    {
                        "Name": "url",
                        "Value": self.url,
                    },
                ],
            )
            return self._create_valid_result()

    def _create_result_from_content_result(
        self, content_result: Dict[str, Any]
    ) -> ConnectedAssetsUrlValidationResult:
        """Creates a result from a content result"""
        reason = content_result["reason"]
        if not reason or reason == "ERROR":
            return self._create_warning_result(
                "Failed to validate the URL, content checking failed"
            )
        return self._create_result_from_reason(reason)

    def _create_result_from_reason(
        self, reason: str
    ) -> ConnectedAssetsUrlValidationResult:
        """Creates a result from a reason"""
        if reason == "ROOT_URL":
            return self._create_warning_result(ValidationError.ROOT_URL)
        elif reason == "GATED":
            return self._create_warning_result(ValidationError.GATED)
        elif reason == "NOTION":
            return self._create_invalid_result(ValidationError.NOTION)
        elif reason == "AUDIO":
            return self._create_warning_result(ValidationError.VIDEO_WEBINAR)
        elif reason == "VIDEO_WEBINAR":
            return self._create_warning_result(ValidationError.VIDEO_WEBINAR)
        else:
            logging.error(f"Unknown reason for URL {self.url}: {reason}")
            return self._create_warning_result(
                "Failed to validate the URL, unknown reason"
            )

    def add_schema_if_missing(self, url):
        url = url.strip()  # strip white spaces
        if url.startswith("http://"):
            return "https://" + url[7:]
        if not url.startswith("http://") and not url.startswith("https://"):
            return "https://" + url
        return url
