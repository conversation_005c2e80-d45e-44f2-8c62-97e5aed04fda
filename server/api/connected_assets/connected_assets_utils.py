import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse

import validators
from bs4 import BeautifulSoup

from ..data_loaders.web_page_loader import TofuWebPageLoader
from ..models import AssetInfo, Playbook
from ..shared_types import ConnectedAssetsSourceType
from ..utils import CloudWatchMetrics


def join_url(base_url: str, path: str) -> str:
    """
    Join a base URL and a path.

    Args:
        base_url: The base URL
        path: The path to join

    Returns:
        str: The joined URL
    """
    # Normalize the base URL
    if base_url.endswith("/"):
        base_url = base_url[:-1]

    # Normalize the path
    if path.endswith("/"):
        path = path[:-1]

    if path.startswith("/"):
        path = path[1:]

    # Create the joined URL with base path preserved
    base_parsed = urlparse(base_url)
    base_path = base_parsed.path.strip("/")
    if base_path:
        path = f"{base_path}/{path}"

    # Use urljoin to properly handle relative paths and ensure URL is well-formed
    return urljoin(base_url, path)


def is_tofu_connected_assets_group(asset_info_group) -> bool:
    """
    Check if an asset group is managed by Tofu by examining its metadata.

    Args:
        asset_info_group: The AssetInfoGroup object to check

    Returns:
        bool: True if the asset group is managed by Tofu, False otherwise
    """

    if not asset_info_group.meta:
        logging.warning(f"Asset group {asset_info_group.id} has no meta data")
        return False
    if not isinstance(asset_info_group.meta, dict):
        logging.error(
            f"Asset group {asset_info_group.id} meta is not a dict: {type(asset_info_group.meta)}"
        )
        return False

    meta_data = asset_info_group.meta.get("tofu_connected_assets_metadata", {})

    if not meta_data:
        logging.warning(f"Asset group {asset_info_group.id} has no meta data")
        return False

    if not meta_data.get("tofu_connected_source", False):
        logging.warning(
            f"Asset group {asset_info_group.id} is not tofu connected, skipping"
        )
        return False

    tofu_connection_type = meta_data.get("tofu_connected_source_type")
    logging.info(
        f"Asset group {asset_info_group.id} tofu_connection_type: {tofu_connection_type}"
    )

    # Check if the type matches any of the valid enum values
    if tofu_connection_type not in [e.value for e in ConnectedAssetsSourceType]:
        logging.warning(
            f"Asset group {asset_info_group.id} has invalid tofu connection type: {tofu_connection_type}"
        )
        return False

    return True


def get_normalized_url_from_connected_source(asset_info_group) -> str:
    """Get the normalized URL from a connected source."""
    if not is_tofu_connected_assets_group(asset_info_group):
        return None
    meta_data = asset_info_group.meta.get("tofu_connected_assets_metadata", {})
    tofu_connection_info = meta_data.get("tofu_connected_source_type_info", {})
    urls = tofu_connection_info.get("urls", [])
    if not urls:
        return None
    return normalize_url(urls[0])


def normalize_url(url: str) -> str:
    """
    Normalize a URL by removing protocol and www prefix.
    Example: https://www.tofuhq.com/blog -> tofuhq.com/blog

    Args:
        url: The URL to normalize

    Returns:
        str: The normalized URL
    """
    # Add https:// if no protocol is present
    if not url.startswith(("http://", "https://")):
        url = "https://" + url
    # Remove trailing .html, .htm
    if url.endswith(".html"):
        url = url[:-5]
    elif url.endswith(".htm"):
        url = url[:-4]

    parsed = urlparse(url)
    # Remove www. if present and handle malformed URLs
    netloc = parsed.netloc.replace("www.", "")
    if netloc.startswith("ww."):  # Handle malformed www
        netloc = netloc[3:]  # Remove 'ww.'
    # Combine netloc and path, removing leading/trailing slashes
    path = parsed.path.strip("/")
    return f"{netloc}/{path}" if path else netloc


def verify_url_path(base_url: str, path: str) -> bool:
    """Verify if a URL path is valid by attempting to crawl it.

    Args:
        base_url: The base URL to join with the path
        path: The URL path to verify

    Returns:
        bool: True if the URL is valid and returns content, False otherwise
    """
    full_url = urljoin(base_url, path)
    return verify_url_exists(full_url)


def verify_url_exists(url: str) -> bool:
    """Verify if a URL exists by attempting to crawl it.

    Args:
        url: The URL to verify

    Returns:
        bool: True if the URL is valid and returns content, False otherwise
    """
    try:
        if not validators.url(url):
            logging.warning(f"Invalid URL {url}")
            return False
        loader = TofuWebPageLoader(
            url=url,
            deep_crawl=False,
        )
        documents = loader.load_shallow()
        return len(documents) > 0
    except Exception as e:
        logging.warning(f"Error verifying URL {url}: {str(e)}")
        return False


# Todo: move to a centralized place
def get_company_website(playbook: Playbook) -> Optional[str]:
    """Get the company website from the playbook.

    Args:
        playbook: The playbook to get the company website from

    Returns:
        str: The company website
    """
    if playbook is None or not playbook.company_object:
        return None
    company_website = next(
        (
            item["value"]
            for item in playbook.company_object.docs.values()
            if item["meta"]["field_name"] == "Company Website"
        ),
        None,
    )
    return company_website


def extract_links_info_from_soup(soup: BeautifulSoup, base_url: str) -> Dict:
    """Extract comprehensive link information from BeautifulSoup HTML.

    Args:
        soup: BeautifulSoup object containing the HTML
        base_url: The base URL for resolving relative links

    Returns:
        Dict: Dictionary containing page title, base URL, and link information
    """
    links_data = {
        "page_title": soup.title.text.strip() if soup.title else "",
        "base_url": base_url,
        "links": [],
    }

    # Extract all links
    for a in soup.find_all("a"):
        href = a.get("href")
        if not href or href.startswith("javascript:") or href == "#":
            continue

        try:
            full_url = urljoin(base_url, href)
            parsed = urlparse(full_url)

            # Skip external links
            if parsed.netloc and parsed.netloc not in base_url:
                continue

            # Get text and clean it
            text = a.text.strip()
            if not text:
                text = a.get("title", "") or a.get("aria-label", "")

            # Collect parent elements
            parent_elements = []
            for parent in a.parents:
                if parent.name in ["html", "[document]"]:
                    break

                if parent.name:  # Skip NavigableString objects
                    parent_elements.append(
                        {
                            "name": parent.name,
                            "id": parent.get("id", ""),
                            "classes": parent.get("class", []),
                        }
                    )

                # Limit to 3 parent levels
                if len(parent_elements) >= 3:
                    break

            links_data["links"].append(
                {
                    "text": text,
                    "href": href,
                    "path": parsed.path,
                    "parent_elements": parent_elements,
                }
            )
        except Exception as e:
            logging.warning(f"Error processing link {href}: {str(e)}")

    return links_data


@dataclass
class AssetCreationResult:
    """Result of asset creation attempt."""

    created_assets: List[AssetInfo]  # Successfully created assets
    created_asset_keys: List[str]  # Successfully created asset keys
    failures: List[
        Tuple[str, str]
    ]  # List of (asset_key, error_message) for failed creations


def bulk_create_assets(assets: List[AssetInfo]) -> AssetCreationResult:
    """
    Bulk create assets with logging, metrics, and fallback to individual creation on failure.

    Args:
        assets: List of AssetInfo objects to create

    Returns:
        AssetCreationResult: Object containing lists of successfully created assets and failures
    """
    created_assets = []
    created_asset_keys = []
    failures = []

    if not assets:
        return AssetCreationResult(created_assets, created_asset_keys, failures)

    logging.info(f"Bulk creating {len(assets)} assets")
    remaining_assets = []

    try:
        created_in_bulk = AssetInfo.objects.bulk_create(assets, ignore_conflicts=True)
        logging.info(f"Bulk created {len(created_in_bulk)} assets successfully.")

        created_keys_in_bulk = {asset.asset_key for asset in created_in_bulk}
        # we need to query the assets to get the full objects
        created_assets.extend(
            list(AssetInfo.objects.filter(asset_key__in=created_keys_in_bulk))
        )
        created_asset_keys.extend(list(created_keys_in_bulk))

        remaining_assets = [
            asset for asset in assets if asset.asset_key not in created_keys_in_bulk
        ]

    except Exception as e:
        logging.exception(
            f"Bulk creation failed: {e}, will fallback to individual creation"
        )
        CloudWatchMetrics.put_metric(
            "bulk_asset_creation_failure",
            1,
            [],
        )
        remaining_assets = assets

    # Fallback to individual creation for remaining assets
    for asset in remaining_assets:
        try:
            asset.save()
            created_assets.append(asset)
            created_asset_keys.append(asset.asset_key)
            logging.info(f"Individually created asset {asset.asset_key}.")
        except Exception as e:
            logging.exception(f"Failed to create asset {asset.asset_key}: {str(e)}")
            failures.append((asset.asset_key, str(e)))
            CloudWatchMetrics.put_metric(
                "bulk_asset_creation_failure_single_asset_creation",
                1,
                [{"Name": "asset_key", "Value": asset.asset_key}],
            )

    logging.info(
        f"Asset creation complete: {len(created_assets)} succeeded, {len(failures)} failed."
    )

    return AssetCreationResult(created_assets, created_asset_keys, failures)
