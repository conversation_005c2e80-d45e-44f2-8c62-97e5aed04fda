import json
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

from django.db import transaction

from ..data_loaders.url_loader import TofuURLLoader
from ..models import AssetInfoGroup, Playbook
from ..shared_types import ConnectedAssetsSourceType
from ..utils import CloudWatchMetrics, TofuLLMCache
from .connected_assets_refresher import (
    ConnectedAssetsGroupRefreshStatus,
    update_connected_asset_group_metadata,
)
from .connected_assets_utils import join_url, normalize_url
from .site_map_discover import SiteMapDiscover, SiteMapResult, SiteMapSection


@dataclass
class ConnectedAssetInfoGroup:
    """Data class for connected asset info group."""

    name: str
    description: str
    urls: List[str]
    confidence: str


class ConnectedAssetInfoGroupsHandler:
    """
    Handler for connected asset discovery and storage.
    This class uses SiteMapDiscover to find content sections and suggested paths,
    then stores them as asset info groups.
    """

    def __init__(
        self,
        playbook_id: int,
        customer_instructions: List[str] = [],
        max_depth: int = 2,
        max_pages: int = 1000,
    ):
        """
        Initialize the ConnectedAssetsHandler with a playbook ID.

        Args:
            playbook_id: The ID of the playbook to store assets in
            customer_instructions: The instructions to use for the SiteMapDiscover
            max_depth: The maximum depth to crawl when discovering the site structure
            max_pages: The maximum number of pages to crawl when discovering the site structure
        """
        self.playbook_id = playbook_id
        self.playbook = Playbook.objects.get(id=playbook_id)
        self.customer_instructions = customer_instructions
        self.max_depth = max_depth
        self.max_pages = max_pages

    def discover_and_store(
        self,
        url: str,
    ) -> Tuple[SiteMapResult, List[AssetInfoGroup]]:
        """
        Discover content sections from a URL and store them as asset info groups.

        Args:
            url: The URL to discover content sections from
            max_depth: The maximum depth to crawl
            max_pages: The maximum number of pages to crawl

        Returns:
            Tuple[Dict, List[AssetInfoGroup]]: The discovered structure and created asset info groups
        """

        CloudWatchMetrics.put_metric(
            "ConnectedAssetsDiscovery",
            1,
            dimensions=[
                {"Name": "PlaybookId", "Value": str(self.playbook_id)},
            ],
        )
        url = TofuURLLoader.add_schema_if_missing(url)

        # Normalize URLs for comparison
        normalized_url = normalize_url(url)
        processed_url = self.playbook.company_object.meta.get(
            "site_map_discovering_processed_url"
        )
        if processed_url:
            normalized_processed_url = normalize_url(processed_url)
            if normalized_url == normalized_processed_url:
                logging.info(
                    f"URL {url} has already been processed (normalized: {normalized_url}), skipping discovery"
                )
                CloudWatchMetrics.put_metric(
                    "ConnectedAssetsDiscovery_Skipped_AlreadyProcessed",
                    1,
                    dimensions=[
                        {"Name": "PlaybookId", "Value": str(self.playbook_id)},
                    ],
                )
                return None, []

        # Discover content sections
        structure = self._discover_content_sections(url)

        # Store content sections
        asset_groups = self._store_content_sections(structure, url)
        logging.info(f"Stored {len(asset_groups)} asset groups for url {url}")

        # Update the processed URL in company metadata
        self.playbook.company_object.meta["site_map_discovering_processed_url"] = (
            normalized_url
        )
        self.playbook.company_object.save()

        CloudWatchMetrics.put_metric(
            "ConnectedAssetsDiscovery_Discovered",
            len(asset_groups),
            dimensions=[
                {"Name": "PlaybookId", "Value": str(self.playbook_id)},
            ],
        )

        CloudWatchMetrics.put_metric(
            "ConnectedAssetsDiscovery_Completed",
            1,
            dimensions=[
                {"Name": "PlaybookId", "Value": str(self.playbook_id)},
            ],
        )

        return structure, asset_groups

    def _discover_content_sections(
        self,
        url: str,
    ) -> SiteMapResult:
        """
        Discover content sections from a URL using SiteMapDiscover.

        Args:
            url: The URL to discover content sections from
            max_depth: The maximum depth to crawl
            max_pages: The maximum number of pages to crawl

        Returns:
            Dict: The discovered content sections
        """
        logging.info(f"Discovering content sections from {url}")
        site_map_discover = SiteMapDiscover(
            playbook_id=self.playbook_id,
            url=url,
            max_depth=self.max_depth,
            max_pages=self.max_pages,
            customer_instructions=self.customer_instructions,
        )

        # Discover the site structure
        with TofuLLMCache():
            structure = site_map_discover.discover_structure()
            logging.info(f"Discovered structure: {structure} for url {url}")
            return structure

    def _store_content_sections(
        self, structure: SiteMapResult, source_url: str
    ) -> List[AssetInfoGroup]:
        """
        Store content sections as asset info groups.

        Args:
            structure: The structure returned by discover_content_sections
            source_url: The source URL

        Returns:
            List[AssetInfoGroup]: The created asset info groups
        """
        created_groups = []

        # Get high confidence content sections and suggested paths
        content_sections = structure.content_sections
        # Todo: in the future, we should do a deep crawl for the suggested paths
        suggested_paths = structure.suggested_paths

        # Filter for high confidence sections
        high_confidence_sections = [
            section
            for section in content_sections
            if section.confidence == "high" and section.category.lower() != "other"
        ]

        # Filter for high confidence suggested paths
        high_confidence_suggested = [
            path
            for path in suggested_paths
            if path.confidence == "high" and path.category.lower() != "other"
        ]

        # Combine high confidence sections and suggested paths
        all_high_confidence = high_confidence_sections + high_confidence_suggested

        # Store each category as an asset info group
        for section in all_high_confidence:
            category = section.category
            url_paths = section.url_paths

            if not category or not url_paths:
                logging.error(
                    f"Skipping section {section} as it has no category or url_paths"
                )
                continue

            if category.lower() == "other":
                logging.info(f"Skipping section {section} as it has category 'other'")
                continue

            # Create full URLs from paths
            full_urls = [join_url(source_url, path) for path in url_paths]

            # Create asset info group
            # For the results from SiteMapDiscover, we always treat them as suggested sources, so user can review them
            asset_groups = self._create_or_update_asset_info_group(
                category=category,
                urls=full_urls,
                source_url=source_url,
                is_suggested=True,
                max_depth=self.max_depth,
                max_pages=self.max_pages,
            )

            if asset_groups:
                created_groups.extend(asset_groups)

        return created_groups

    @transaction.atomic
    def _create_or_update_asset_info_group(
        self,
        category: str,
        urls: List[str],
        source_url: str,
        is_suggested: bool = False,
        max_depth: Optional[int] = None,
        max_pages: Optional[int] = None,
    ) -> List[AssetInfoGroup]:
        """
        Create asset info groups for each URL in a category.

        Args:
            category: The category name
            urls: The URLs for this category
            is_suggested: Whether this is a suggested source
            max_depth: The maximum depth to crawl
            max_pages: The maximum number of pages to crawl

        Returns:
            List[AssetInfoGroup]: The created asset info groups
        """
        created_groups = []
        try:
            for url in urls:
                # Create a unique key for each asset info group using the normalized URL
                normalized_url = normalize_url(url)
                group_key = f"{normalized_url}"

                # Check if the group already exists
                existing_group = AssetInfoGroup.objects.filter(
                    playbook=self.playbook, asset_info_group_key=group_key
                ).first()

                if existing_group:
                    logging.info(
                        f"Asset info group {group_key} already exists, updating"
                    )
                    asset_info_group = existing_group
                    # Update the meta field
                    new_meta = self._create_meta_data(
                        category, [url], source_url, is_suggested, max_depth, max_pages
                    )
                    update_connected_asset_group_metadata(asset_info_group, new_meta)
                else:
                    # Create a new asset info group
                    asset_info_group = AssetInfoGroup.objects.create(
                        playbook=self.playbook,
                        asset_info_group_key=group_key,
                        meta=self._create_meta_data(
                            category,
                            [url],
                            source_url,
                            is_suggested,
                            max_depth,
                            max_pages,
                        ),
                    )

                created_groups.append(asset_info_group)

            return created_groups
        except Exception as e:
            logging.error(f"Error creating asset info groups: {e}")
            raise e

    def _create_meta_data(
        self,
        category_type: str,
        urls: List[str],
        source_url: str,
        is_suggested: bool,
        max_depth: Optional[int],
        max_pages: Optional[int],
    ) -> Dict:
        """
        Create metadata for an asset info group.

        Args:
            category_type: The standardized category type
            urls: The URLs for this category
            is_suggested: Whether this is a suggested source
            max_depth: The maximum depth to crawl
            max_pages: The maximum number of pages to crawl

        Returns:
            Dict: The metadata
        """

        meta_data = {
            "tofu_suggested_connected_source": is_suggested,
            "tofu_connected_source": True,
            "tofu_connected_source_type": ConnectedAssetsSourceType.Url,
            "instructions": [],
            "category": category_type,
            "tofu_connected_source_type_info": {
                "urls": urls,
                "source_url": source_url,
            },
            "last_synced_at": None,
            "last_synced_status": ConnectedAssetsGroupRefreshStatus.NOT_STARTED,
        }

        # Only add max_depth and max_pages if they are not None
        if max_depth is not None:
            meta_data["tofu_connected_source_type_info"]["max_depth"] = str(max_depth)

        if max_pages is not None:
            meta_data["tofu_connected_source_type_info"]["max_pages"] = str(max_pages)

        return {"tofu_connected_assets_metadata": meta_data}
