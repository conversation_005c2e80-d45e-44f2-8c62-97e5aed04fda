import hashlib
import json
import logging
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Union

from django.core.cache import cache
from langchain_core.messages import HumanMessage, SystemMessage

from ..data_loaders.web_page_loader import TofuWebPageLoader
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..prompt.prompt_library.prompt_connected_assets import (
    CATEGORY_DEFINITIONS,
    COMMON_RESPONSE_FORMAT,
    PATTERN_ANALYSIS_PROMPT,
    ROOT_ANALYSIS_PROMPT,
)
from ..task_registry import GenerationGoal
from ..utils import CloudWatchMetrics, get_token_count
from .connected_assets_utils import (
    extract_links_info_from_soup,
    normalize_url,
    verify_url_path,
)


@dataclass
class SiteMapSection:
    category: str
    url_paths: List[str]
    confidence: str


@dataclass
class SiteMapResult:
    root_url: str
    content_sections: List[SiteMapSection]
    suggested_paths: List[SiteMapSection]
    execution_time: float


class SiteMapDiscover(BaseTracableClass):
    """A class for discovering and analyzing website structure.

    This class is organized into four main sections:
    1. Public Interface Methods: Methods that external code should use
    2. Core Analysis Methods: Main business logic for analyzing URLs and content
    3. Helper Methods: Utility functions for token counting, LLM calls, etc.
    4. Private Processing Methods: Internal methods for processing and validating data
    """

    # 1. Public Interface Methods
    def __init__(
        self,
        url: str,
        playbook_id: int,
        max_depth: int = 2,
        max_pages: int = 1000,
        customer_instructions: List[str] = [],
    ):
        self.url = url
        self.playbook_id = playbook_id
        self.max_depth = max_depth
        self.max_pages = max_pages
        self.customer_instructions = customer_instructions

        model_config = ModelConfigResolver.resolve(
            GenerationGoal.CONNECTED_SOURCE_SITE_MAP_DISCOVERY,
        )
        self.model_caller = ModelCaller(model_config)
        self.model_config = model_config

    def get_metadata(self) -> Dict[str, str]:
        """Get metadata about the site map discovery process.

        Returns:
            Dict[str, str]: Dictionary containing playbook_id and url
        """
        return {
            "playbook_id": self.playbook_id,
            "url": self.url,
        }

    def discover_structure(self) -> SiteMapResult:
        """Main method to discover website structure."""
        start_time = time.time()

        try:
            # First, analyze the root URL
            logging.info("Starting root URL analysis...")
            root_analysis = self._analyze_root_url()
            logging.info(f"Root URL analysis completed: {root_analysis}")

            # we only take high confidence content sections
            high_confidence_sections = [
                section
                for section in root_analysis.get("content_sections", [])
                if section["confidence"] == "high"
            ]
            forbidden_patterns = list(
                set(root_analysis.get("forbidden_urls_patterns", []))
            )
            logging.info(
                f"Found {len(high_confidence_sections)} high confidence content sections"
            )
            logging.info(f"High confidence sections: {high_confidence_sections}")

            # Flatten the list of URL paths from high confidence sections
            known_url_paths = []
            for section in high_confidence_sections:
                known_url_paths.extend(section["url_paths"])

            # Combine with forbidden patterns
            skipped_url_patterns = forbidden_patterns + known_url_paths
            skipped_url_patterns = [normalize_url(url) for url in skipped_url_patterns]
            logging.info(f"Skipping URL patterns: {skipped_url_patterns}")

            level_result = self._analyze_urls(
                self.url, self.max_depth, skipped_url_patterns
            )

            logging.info(f"Level result: {level_result}")

            # Generate the structured result
            structure = self._generate_structured_result(
                root_analysis, level_result, start_time
            )

            # metric execution time
            if structure.execution_time > 0:
                CloudWatchMetrics.put_metric(
                    "site_map_discover_execution_time",
                    structure.execution_time,
                    [{"Name": "url", "Value": self.url}],
                )

            return structure

        except Exception as e:
            logging.exception(f"Error during structure discovery: {str(e)}")
            CloudWatchMetrics.put_metric(
                "site_map_discover_error",
                1,
                [
                    {"Name": "url", "Value": self.url},
                ],
            )
            raise e

    # 2. Core Analysis Methods
    @dynamic_traceable(name="analyze_root_url")
    def _analyze_root_url(self) -> Dict:
        """Analyze the root URL to understand site structure."""
        try:
            # Check cache first
            cache_key = self._root_url_analysis_cache_key()
            try:
                cached_result = cache.get(cache_key)
                if cached_result:
                    logging.info("Using cached root URL analysis")
                    CloudWatchMetrics.put_metric(
                        "site_map_discover_cache_hit",
                        1,
                        [
                            {"Name": "url", "Value": self.url},
                            {"Name": "Step", "Value": "root_url_analysis"},
                        ],
                    )
                    return json.loads(cached_result)
            except Exception as e:
                logging.exception(f"debug: Failed to get root URL analysis cache: {e}")
                cached_result = None

            # Create loader for root URL analysis
            loader = TofuWebPageLoader(
                url=self.url,
                deep_crawl=True,
                crawler_max_depth=self.max_depth,
                crawler_max_page=self.max_pages,
            )

            # Load root URL content
            soup = loader.scrape()
            links_data = extract_links_info_from_soup(soup, self.url)
            content = json.dumps(links_data)
            page_documents = loader.load_shallow()
            page_content = "\n".join([doc.page_content for doc in page_documents])

            # Construct prompt
            messages = self._construct_analysis_prompt(
                content, page_content, self.model_config.model_budget
            )

            result = self.model_caller.get_llm_dict_response(messages)

            CloudWatchMetrics.put_metric(
                "site_map_discover_llm_call",
                1,
                [
                    {
                        "Name": "Model",
                        "Value": self.model_caller.model_name or "unknown",
                    },
                    {"Name": "url", "Value": self.url},
                    {"Name": "Step", "Value": "root_url_analysis"},
                ],
            )

            total_tokens = sum(get_token_count(message.content) for message in messages)
            CloudWatchMetrics.put_metric(
                "site_map_discover_llm_call_token",
                total_tokens,
                [
                    {
                        "Name": "Model",
                        "Value": self.model_caller.model_name or "unknown",
                    },
                    {"Name": "url", "Value": self.url},
                    {"Name": "Step", "Value": "root_url_analysis"},
                ],
            )

            if result and self._validate_result_format(result):
                logging.info("Root URL analysis completed")
                # Cache the result as JSON string for 30 days
                cache.set(cache_key, json.dumps(result), timeout=60 * 60 * 24 * 30)
                return result
            else:
                logging.error("Invalid result format from LLM")
                CloudWatchMetrics.put_metric(
                    "site_map_discover_error_root_url_analysis",
                    1,
                    [
                        {"Name": "url", "Value": self.url},
                        {"Name": "reason", "Value": "invalid_result_format"},
                    ],
                )
                return {
                    "content_sections": [],
                    "suggested_paths": [],
                    "forbidden_urls_patterns": [],
                }

        except Exception as e:
            logging.exception(f"Error in root URL analysis: {str(e)}")
            CloudWatchMetrics.put_metric(
                "site_map_discover_error_root_url_analysis",
                1,
                [
                    {"Name": "url", "Value": self.url},
                    {"Name": "reason", "Value": "exception"},
                ],
            )
            return {
                "content_sections": [],
                "suggested_paths": [],
                "forbidden_urls_patterns": [],
            }

    @dynamic_traceable(name="analyze_urls")
    def _analyze_urls(self, url: str, level: int, skipped_patterns: List[str]) -> Dict:
        """Analyze the URL in the given level and return documents and URLs for that level."""
        try:
            logging.info(f"Analyzing URL at level {level}: {url}")

            # Check cache first
            cache_key = self._pattern_analysis_cache_key(skipped_patterns)
            try:
                cached_result = cache.get(cache_key)
                if cached_result:
                    logging.info("Using cached URL pattern analysis")
                    CloudWatchMetrics.put_metric(
                        "site_map_discover_cache_hit",
                        1,
                        [
                            {"Name": "url", "Value": self.url},
                            {"Name": "Step", "Value": "url_level_analysis"},
                        ],
                    )
                    return json.loads(cached_result)
            except Exception as e:
                logging.exception(
                    f"debug: Failed to get URL pattern analysis cache: {e}"
                )
                cached_result = None

            # Create a loader specifically for this URL with a depth of 1
            level_loader = TofuWebPageLoader(
                url=url,
                deep_crawl=True,
                crawler_max_depth=level,
                crawler_max_page=self.max_pages,
            )

            # Load documents from this level
            documents = level_loader.load_deep(skipped_patterns=skipped_patterns)
            logging.info(
                f"Found {len(documents)} documents at level {level} from {url}"
            )

            # Collect unique URLs for pattern analysis
            urls_to_analyze = {
                doc.metadata.get("source")
                for doc in documents
                if doc.metadata.get("source") and doc.metadata.get("source") != url
            }
            logging.info(
                f"Found {len(urls_to_analyze)} unique URLs for pattern analysis"
            )

            # Construct prompt with URLs
            messages = self._construct_pattern_analysis_prompt(
                list(urls_to_analyze), self.model_config.model_budget
            )

            CloudWatchMetrics.put_metric(
                "site_map_discover_llm_call",
                1,
                [
                    {
                        "Name": "Model",
                        "Value": self.model_caller.model_name or "unknown",
                    },
                    {"Name": "url", "Value": self.url},
                    {"Name": "Step", "Value": "url_level_analysis"},
                ],
            )
            result = self.model_caller.get_llm_dict_response(messages)

            if result and self._validate_result_format(result):
                # Cache the result as JSON string for 30 days
                cache.set(cache_key, json.dumps(result), timeout=60 * 60 * 24 * 30)
                return result

            logging.error("Invalid result format from LLM")
            CloudWatchMetrics.put_metric(
                "site_map_discover_error_url_level_analysis",
                1,
                [
                    {"Name": "url", "Value": self.url},
                    {"Name": "reason", "Value": "invalid_result_format"},
                ],
            )
            return {
                "content_sections": [],
                "suggested_paths": [],
                "forbidden_urls_patterns": [],
            }

        except Exception as e:
            logging.exception(f"Error analyzing URL {url} at level {level}: {str(e)}")
            CloudWatchMetrics.put_metric(
                "site_map_discover_error_url_level_analysis",
                1,
                [
                    {"Name": "url", "Value": self.url},
                    {"Name": "reason", "Value": "exception"},
                ],
            )
            return {
                "content_sections": [],
                "suggested_paths": [],
                "forbidden_urls_patterns": [],
            }

    def _root_url_analysis_cache_key(self) -> str:
        # Create hashes for the prompt components
        prompt_hash = hashlib.md5(ROOT_ANALYSIS_PROMPT.encode("utf-8")).hexdigest()[:8]
        cat_defs_hash = hashlib.md5(CATEGORY_DEFINITIONS.encode("utf-8")).hexdigest()[
            :8
        ]
        format_hash = hashlib.md5(COMMON_RESPONSE_FORMAT.encode("utf-8")).hexdigest()[
            :8
        ]
        instructions_hash = hashlib.md5(
            "\n".join(self.customer_instructions).encode("utf-8")
        ).hexdigest()[:8]

        return f"site_map_discover_root_url_analysis:{self.url}:prompt_{prompt_hash}:catdefs_{cat_defs_hash}:format_{format_hash}:instructions_{instructions_hash}"

    def _construct_analysis_prompt(
        self, content: str, page_content: str, token_budget: int
    ) -> List[SystemMessage | HumanMessage]:
        """Construct the prompt for root URL analysis.

        Args:
            content: The HTML content to analyze
            page_content: The page content from documents
            token_budget: The token budget for the prompt

        Returns:
            List[SystemMessage | HumanMessage]: List of messages for LLM
        """
        system_message = "You are a website structure analysis expert. Analyze the root URL to understand the site's content organization."
        prompt_template = ROOT_ANALYSIS_PROMPT.format(
            category_definitions=CATEGORY_DEFINITIONS,
            url=self.url,
            content="{content}",
            page_content="{page_content}",
            response_format=COMMON_RESPONSE_FORMAT,
            customer_instructions="\n".join(self.customer_instructions),
        )

        # Calculate tokens for fixed parts
        fixed_tokens = get_token_count(system_message) + get_token_count(
            prompt_template.replace("{content}", "").replace("{page_content}", "")
        )

        # Allocate remaining tokens to content (90% of budget)
        content_token_budget = int((token_budget * 0.9) - fixed_tokens)

        # Truncate HTML content if needed
        content_tokens = get_token_count(content)
        if content_tokens > content_token_budget:
            # Simple character-based truncation
            ratio = content_token_budget / content_tokens
            char_limit = int(len(content) * ratio * 0.9)  # 10% safety margin
            truncated_content = content[:char_limit]
            logging.info(
                f"HTML content truncated from {len(content)} chars to {len(truncated_content)} chars"
            )
        else:
            truncated_content = content

        # Prepare prompt with truncated HTML content
        prompt = ROOT_ANALYSIS_PROMPT.format(
            category_definitions=CATEGORY_DEFINITIONS,
            url=self.url,
            content=truncated_content,
            page_content=page_content,
            response_format=COMMON_RESPONSE_FORMAT,
            customer_instructions="\n".join(self.customer_instructions),
        )

        # Log token usage
        total_tokens = get_token_count(system_message) + get_token_count(prompt)
        logging.info(
            f"Root URL analysis using {total_tokens} tokens ({total_tokens/token_budget:.1%} of budget)"
        )

        return [
            SystemMessage(content=system_message),
            HumanMessage(content=prompt),
        ]

    def _pattern_analysis_cache_key(self, skipped_patterns: List[str]) -> str:
        # Create hashes for the prompt components
        prompt_hash = hashlib.md5(PATTERN_ANALYSIS_PROMPT.encode("utf-8")).hexdigest()[
            :8
        ]
        cat_defs_hash = hashlib.md5(CATEGORY_DEFINITIONS.encode("utf-8")).hexdigest()[
            :8
        ]
        format_hash = hashlib.md5(COMMON_RESPONSE_FORMAT.encode("utf-8")).hexdigest()[
            :8
        ]
        instructions_hash = hashlib.md5(
            "\n".join(self.customer_instructions).encode("utf-8")
        ).hexdigest()[:8]
        # Include level and skipped_patterns parameters in the cache key
        level_hash = str(self.max_depth)
        patterns_hash = hashlib.md5(
            str(sorted(skipped_patterns)).encode("utf-8")
        ).hexdigest()[:8]

        return f"site_map_discover_pattern_analysis:{self.url}:level_{level_hash}:patterns_{patterns_hash}:prompt_{prompt_hash}:catdefs_{cat_defs_hash}:format_{format_hash}:instructions_{instructions_hash}"

    def _construct_pattern_analysis_prompt(
        self, urls: List[str], token_budget: int
    ) -> List[HumanMessage]:
        """Construct the prompt for URL pattern analysis.

        Args:
            urls: List of URLs to analyze
            token_budget: The token budget for the prompt

        Returns:
            List[HumanMessage]: List of messages for LLM
        """
        # Calculate token budget
        prompt_template = PATTERN_ANALYSIS_PROMPT.format(
            category_definitions=CATEGORY_DEFINITIONS,
            example_urls="{example_urls}",
            content_preview="",
            response_format=COMMON_RESPONSE_FORMAT,
            customer_instructions="\n".join(self.customer_instructions),
        )

        # Calculate tokens for fixed parts
        fixed_tokens = get_token_count(prompt_template.replace("{example_urls}", ""))

        # Allocate remaining tokens to URL list (90% of budget)
        urls_token_budget = int((token_budget * 0.9) - fixed_tokens)

        # Handle URL list truncation
        urls_to_include = []
        current_tokens = 0

        for url in urls:
            url_tokens = get_token_count(url + "\n")
            if current_tokens + url_tokens <= urls_token_budget:
                urls_to_include.append(url)
                current_tokens += url_tokens
            else:
                break

        urls_text = "\n".join(urls_to_include)
        logging.info(
            f"URL analysis including {len(urls_to_include)} of {len(urls)} URLs ({current_tokens} tokens)"
        )

        # Prepare prompt with URLs
        prompt = PATTERN_ANALYSIS_PROMPT.format(
            category_definitions=CATEGORY_DEFINITIONS,
            example_urls=urls_text,
            content_preview="",
            response_format=COMMON_RESPONSE_FORMAT,
            customer_instructions="\n".join(self.customer_instructions),
        )

        # Log token usage
        total_tokens = get_token_count(prompt)
        logging.info(
            f"URL level analysis using {total_tokens} tokens ({total_tokens/token_budget:.1%} of budget)"
        )
        CloudWatchMetrics.put_metric(
            "site_map_discover_llm_call_token",
            total_tokens,
            [
                {"Name": "Model", "Value": self.model_caller.model_name or "unknown"},
                {"Name": "url", "Value": self.url},
                {"Name": "Step", "Value": "url_level_analysis"},
            ],
        )

        return [HumanMessage(content=prompt)]

    def _validate_result_format(self, result: Dict) -> bool:
        """Validate that the result matches the expected format.

        Args:
            result: The result to validate

        Returns:
            bool: True if the result is valid, False otherwise
        """
        # Validate content_sections
        content_sections = result.get("content_sections", [])
        if not isinstance(content_sections, list):
            logging.error("content_sections is not a list")
            return False

        for section in content_sections:
            if not isinstance(section, dict):
                logging.error("content section is not a dictionary")
                return False

            # Required fields
            required_fields = ["category", "url_paths", "confidence"]
            for field in required_fields:
                if field not in section:
                    logging.error(
                        f"Missing required field '{field}' in content section"
                    )
                    return False

            # Validate field types
            if not isinstance(section["category"], str):
                logging.error("category must be a string")
                return False
            if not isinstance(section["url_paths"], list):
                logging.error("url_paths must be a list")
                return False
            if not isinstance(section["confidence"], str):
                logging.error("confidence must be a string")
                return False

            # Validate confidence value
            if section["confidence"] not in ["high", "medium", "low"]:
                logging.error(f"Invalid confidence value: {section['confidence']}")
                return False

        # Validate suggested_paths
        suggested_paths = result.get("suggested_paths", [])
        if not isinstance(suggested_paths, list):
            logging.error("suggested_paths is not a list")
            return False

        for path in suggested_paths:
            if not isinstance(path, dict):
                logging.error("suggested path is not a dictionary")
                return False

            # Required fields
            required_fields = ["category", "url_paths", "confidence"]
            for field in required_fields:
                if field not in path:
                    logging.error(f"Missing required field '{field}' in suggested path")
                    return False

            # Validate field types
            if not isinstance(path["category"], str):
                logging.error("category must be a string")
                return False
            if not isinstance(path["url_paths"], list):
                logging.error("url_paths must be a list")
                return False
            if not isinstance(path["confidence"], str):
                logging.error("confidence must be a string")
                return False

            # Validate confidence value
            if path["confidence"] not in ["high", "medium", "low"]:
                logging.error(f"Invalid confidence value: {path['confidence']}")
                return False

        # Validate forbidden_urls_patterns
        forbidden_patterns = result.get("forbidden_urls_patterns", [])
        if not isinstance(forbidden_patterns, list):
            logging.error("forbidden_urls_patterns is not a list")
            return False

        for pattern in forbidden_patterns:
            if not isinstance(pattern, str):
                logging.error("forbidden URL pattern must be a string")
                return False

        return True

    def _verify_url_paths(self, paths: List[str]) -> List[str]:
        """Verify a list of URL paths and return only valid ones."""
        return [
            path
            for path in paths
            if verify_url_path(self.url, path)
            or logging.info(f"Removing invalid URL path: {path}")
        ]

    def _process_content_section(self, section: Dict) -> Optional[Dict]:
        """Process a single content section by verifying its URL paths and normalizing the URL paths.

        Args:
            section: Content section dictionary to process

        Returns:
            Optional[Dict]: Processed section with verified paths, or None if no valid paths
        """
        category = section.get("category", "")
        if not category:
            return None
        # normalize the url paths
        verified_paths = [normalize_url(path) for path in section.get("url_paths", [])]
        verified_paths = self._verify_url_paths(verified_paths)
        if not verified_paths:
            return None

        section_copy = section.copy()
        section_copy["url_paths"] = verified_paths
        return section_copy

    def _merge_content_sections(self, sections: List[Dict]) -> List[Dict]:
        """Merge content sections by category, combining URL paths.

        Args:
            sections: List of content sections to merge

        Returns:
            List[Dict]: Merged sections with combined URL paths
        """
        category_to_section = {}

        for section in sections:
            processed_section = self._process_content_section(section)
            if not processed_section:
                continue

            category = processed_section["category"]
            if category not in category_to_section:
                category_to_section[category] = processed_section
            else:
                existing_paths = set(category_to_section[category]["url_paths"])
                new_paths = set(processed_section["url_paths"])
                category_to_section[category]["url_paths"] = list(
                    existing_paths.union(new_paths)
                )
                # if any of the confidence is high, then the confidence of the merged section should be high
                if (
                    processed_section["confidence"] == "high"
                    or category_to_section[category]["confidence"] == "high"
                ):
                    category_to_section[category]["confidence"] = "high"

        return list(category_to_section.values())

    def _process_suggested_paths(self, paths: List[Dict]) -> List[Dict]:
        """Process and verify suggested paths.

        Args:
            paths: List of suggested path dictionaries

        Returns:
            List[Dict]: List of verified suggested paths
        """
        processed_paths = []
        for suggestion in paths:
            verified_paths = self._verify_url_paths(suggestion.get("url_paths", []))
            if verified_paths:
                suggestion_copy = suggestion.copy()
                suggestion_copy["url_paths"] = verified_paths
                processed_paths.append(suggestion_copy)
        return processed_paths

    def _generate_structured_result(
        self, root_analysis: Dict, level_result: Dict, start_time: float
    ) -> SiteMapResult:
        """Generate the structured result by combining and deduplicating data.

        Args:
            root_analysis: The analysis result from the root URL
            level_result: The analysis result from the level crawl
            start_time: The start time of the discovery process

        Returns:
            SiteMapResult: The structured result with deduplicated content sections and suggested paths
        """
        # Combine content sections from both analyses
        all_content_sections = []
        all_content_sections.extend(root_analysis.get("content_sections", []))
        if level_result:
            all_content_sections.extend(level_result.get("content_sections", []))

        # Process and merge content sections
        deduplicated_sections = self._merge_content_sections(all_content_sections)

        # Convert content sections to SiteMapSection objects
        content_sections = [
            SiteMapSection(
                category=section["category"],
                url_paths=section["url_paths"],
                confidence=section["confidence"],
            )
            for section in deduplicated_sections
        ]

        # Get suggested paths from root analysis
        suggested_paths = root_analysis.get("suggested_paths", [])

        # Add and process suggested paths from level result
        if level_result and "suggested_paths" in level_result:
            suggested_paths.extend(level_result.get("suggested_paths", []))

        # Process all suggested paths
        processed_paths = self._process_suggested_paths(suggested_paths)

        # Convert suggested paths to SiteMapSection objects
        suggested_paths = [
            SiteMapSection(
                category=path.get("category", "suggested"),
                url_paths=path["url_paths"],
                confidence=path.get("confidence", "medium"),
            )
            for path in processed_paths
        ]

        return SiteMapResult(
            root_url=self.url,
            content_sections=content_sections,
            suggested_paths=suggested_paths,
            execution_time=time.time() - start_time,
        )
