import hashlib
import json
import logging
from datetime import datetime
from typing import Dict, List

from django.core.cache import cache
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage

from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..model_caller import ModelCaller
from ..model_config import ModelConfigResolver
from ..prompt.prompt_library.prompt_connected_assets import (
    CATEGORY_DEFINITIONS,
    METADATA_PROMPT,
    METADATA_SCHEMA,
    URL_CATEGORY_PROMPT,
    URLS_RELEVANCE_PROMPT,
)
from ..task_registry import GenerationGoal
from ..utils import CloudWatchMetrics, get_token_count


class ConnectedAssetMetadataExtractor(BaseTracableClass):
    """Extracts metadata from connected assets based on their category."""

    def __init__(self):
        # resolve the model config for a smaller model
        model_config = ModelConfigResolver.resolve(
            GenerationGoal.CONNECTED_SOURCE_REFRESH,
        )
        self.model_caller = ModelCaller(model_config)
        self.model_config = model_config
        self.CACHE_TTL = 3600 * 24 * 30  # 30 days
        self.model_budget = model_config.model_budget

    def _create_metadata_cache_key(self, doc: Document) -> str:
        """Create an explicit, readable cache key for metadata extraction.

        Format: connected_source_metadata:{url}:{prompt_version}:{category_defs_version}
        """
        url = doc.metadata.get("source", "")
        prompt_version = hashlib.md5(METADATA_PROMPT.encode("utf-8")).hexdigest()[:8]
        cat_defs_version = hashlib.md5(
            CATEGORY_DEFINITIONS.encode("utf-8")
        ).hexdigest()[:8]

        cache_key = f"connected_source_metadata:{url}:prompt_{prompt_version}:catdefs_{cat_defs_version}"

        return cache_key

    def _truncate_content_by_tokens_if_needed(
        self, content: str, max_tokens: int
    ) -> str:
        """Truncate content to fit within a token budget if needed.

        Args:
            content: The content to truncate
            max_tokens: Maximum number of tokens allowed

        Returns:
            Truncated content that fits within the token budget
        """
        content_tokens = get_token_count(content)
        if content_tokens <= max_tokens:
            return content

        # Calculate the truncation ratio based on token budget
        truncation_ratio = max_tokens / content_tokens
        # Apply the same ratio to the string length
        new_length = int(len(content) * truncation_ratio)
        truncated_content = content[:new_length]

        if len(truncated_content) < len(content):
            logging.warning(
                f"Content was truncated from {content_tokens} to {get_token_count(truncated_content)} tokens."
            )
            CloudWatchMetrics.put_metric(
                "connected_asset_metadata_extractor_content_truncated", 1, []
            )

        return truncated_content

    def deduce_category_from_url(self, url: str) -> str:
        """Deduce the category from a URL."""
        try:
            prompt = URL_CATEGORY_PROMPT.format(
                category_definitions=CATEGORY_DEFINITIONS,
                url=url,
            )

            result = self.model_caller.get_llm_dict_response(
                [HumanMessage(content=prompt)]
            )

            return result.get("category", "Other")

        except Exception as e:
            logging.exception(f"Error deducing category from URL: {str(e)}")
            CloudWatchMetrics.put_metric(
                "connected_asset_metadata_extractor_url_category_deduction_error", 1, []
            )
            return "Other"

    @dynamic_traceable(name="extract_metadata")
    def extract_metadata(
        self,
        doc: Document,
        starting_listing_page_url: str,
        starting_listing_page_category: str,
    ) -> Dict:
        """Extract metadata from a document based on its category.

        Args:
            doc: The document to analyze
            category: The content category ("Blog Post" or "Case Study")

        Returns:
            Dict containing extracted metadata
        """
        url = doc.metadata.get("source", "")
        try:
            if not url:
                logging.error(f"No source URL found for document: {doc.metadata}")
                raise ValueError("No source URL found for document")

            cache_key = self._create_metadata_cache_key(doc)
            cached_result = cache.get(cache_key)
            if cached_result:
                try:
                    logging.info(f"Returning cached metadata for {url}")
                    CloudWatchMetrics.put_metric(
                        metric_name="ConnectedAssetMetadataExtractorCacheHit",
                        value=1,
                        dimensions=[],
                    )
                    return json.loads(cached_result)
                except Exception as e:
                    logging.exception(f"Error parsing cached metadata: {str(e)}")
                    # continue to extract metadata

            CloudWatchMetrics.put_metric(
                metric_name="ConnectedAssetMetadataExtractorCacheMiss",
                value=1,
                dimensions=[],
            )

            token_budget_for_content = int(self.model_budget * 0.8)
            page_content = self._truncate_content_by_tokens_if_needed(
                doc.page_content, token_budget_for_content
            )

            prompt = METADATA_PROMPT.format(
                starting_listing_page_url=starting_listing_page_url,
                starting_listing_page_category=starting_listing_page_category,
                category_definitions=CATEGORY_DEFINITIONS,
                content=page_content,
                schema=METADATA_SCHEMA,
                page_url=doc.metadata.get("source", ""),
            )

            # Calculate token usage
            total_tokens = get_token_count(prompt)
            logging.info(
                f"Metadata extraction using {total_tokens} tokens ({total_tokens/self.model_config.model_budget:.1%} of budget)"
            )
            CloudWatchMetrics.put_metric(
                "connected_asset_metadata_extractor_llm_call_token",
                total_tokens,
                [
                    {
                        "Name": "Model",
                        "Value": self.model_caller.model_name or "unknown",
                    },
                ],
            )

            CloudWatchMetrics.put_metric(
                "connected_asset_metadata_extractor_llm_call",
                1,
                [
                    {
                        "Name": "Model",
                        "Value": self.model_caller.model_name or "unknown",
                    },
                ],
            )
            # Call LLM
            result = self.model_caller.get_llm_dict_response(
                [HumanMessage(content=prompt)]
            )

            if not result:
                return {
                    "author": "",
                    "published_date": "",
                    "category": "Other",
                    "title": doc.metadata.get("title", ""),
                }

            # Validate and clean the result
            metadata = {
                "author": result.get("author", ""),
                "published_date": result.get("published_date", ""),
                "category": result.get("category", "Other"),
                "title": result.get("title", doc.metadata.get("title", "")),
            }

            # Validate date format if present
            if metadata["published_date"]:
                try:
                    datetime.strptime(metadata["published_date"], "%Y-%m-%d")
                except ValueError:
                    logging.warning(
                        f"Invalid date format: {metadata['published_date']}"
                    )
                    metadata["published_date"] = ""
            if cache_key:
                cache.set(cache_key, json.dumps(metadata), self.CACHE_TTL)

            return metadata

        except Exception as e:
            logging.error(f"Error extracting metadata: {str(e)}")
            CloudWatchMetrics.put_metric(
                metric_name="ConnectedAssetMetadataExtractorError",
                value=1,
                dimensions=[
                    {
                        "Name": "url",
                        "Value": url or "unknown",
                    },
                ],
            )
            return {
                "author": "",
                "published_date": "",
                "category": "Other",
                "title": doc.metadata.get("title", ""),
            }

    @dynamic_traceable(name="verify_urls_relevance")
    def verify_urls_relevance(
        self, listing_page_url: str, urls: List[str], category: str
    ) -> List[str]:
        """Verify multiple URLs and return those relevant to the desired category using URL patterns.

        Args:
            urls: List of URLs to analyze
            category: The desired content category

        Returns:
            List[str]: List of URLs that are relevant to the category
        """
        try:
            chunk_size = 75
            all_relevant_urls = []

            # Split URLs into chunks
            for i in range(0, len(urls), chunk_size):
                chunk = urls[i : i + chunk_size]

                # Prepare the prompt for this chunk
                prompt = URLS_RELEVANCE_PROMPT.format(
                    starting_listing_page_url=listing_page_url,
                    category=category,
                    category_definitions=CATEGORY_DEFINITIONS,
                    urls="\n".join(chunk),
                    url_count=len(chunk),
                )

                # Call LLM for this chunk
                result = self.model_caller.get_llm_dict_response(
                    [HumanMessage(content=prompt)]
                )
                if not result:
                    logging.error(f"No result returned for chunk {i//chunk_size + 1}")
                    continue

                # Verify the counts match for this chunk
                total_processed = result.get("url_counts", {}).get("total_processed", 0)
                if total_processed != len(chunk):
                    logging.warning(
                        f"Count mismatch in chunk {i//chunk_size + 1}: "
                        f"expected {len(chunk)}, got {total_processed}"
                    )
                    CloudWatchMetrics.put_metric(
                        "connected_asset_metadata_extractor_url_count_mismatch", 1, []
                    )

                # Extend the results
                relevant_urls = result.get("relevant_urls", [])
                all_relevant_urls.extend(relevant_urls)

                logging.info(
                    f"Processed chunk {i//chunk_size + 1}/{(len(urls) + chunk_size - 1)//chunk_size}: "
                    f"found {len(relevant_urls)} relevant URLs"
                )

            return all_relevant_urls

        except Exception as e:
            logging.error(f"Error verifying URLs relevance: {str(e)}")
            return []
