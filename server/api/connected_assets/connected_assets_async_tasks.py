import logging

from django.core.cache import cache
from langchain_core.documents import Document
from server.celery import app as celery_app

from ..models import AssetInfoGroup, Playbook
from ..utils import CloudWatchMetrics
from .connected_asset_info_groups_handler import (
    ConnectedAssetInfoGroupsHandler,
    ConnectedAssetsGroupRefreshStatus,
)
from .connected_assets_refresher import (
    refresh_connected_asset_group,
    update_connected_asset_group_refresh_status,
    update_connected_asset_group_refresh_task_id,
)
from .connected_assets_utils import (
    get_company_website,
    is_tofu_connected_assets_group,
    verify_url_exists,
)


@celery_app.task
def async_generated_suggested_connected_asset_group(
    playbook_id, customer_instructions=[], max_depth=2, max_pages=500
):
    """
    Asynchronously generate a suggested connected asset group for a playbook.
    Args:
        playbook_id: The ID of the playbook to generate assets for
        customer_instructions: Optional instructions from the customer
        max_depth: Maximum depth to crawl for site map discovery
        max_pages: Maximum number of pages to crawl
    Returns:
        dict: Result information including status and generated assets
    """
    try:
        playbook = Playbook.objects.get(id=playbook_id)
        company_website = get_company_website(playbook)
        if not company_website:
            logging.info(f"No company website found for playbook {playbook_id}")
            return
        if not verify_url_exists(company_website):
            logging.warning(
                f"Company website {company_website} does not exist or not accessible, skipping"
            )
            CloudWatchMetrics.put_metric(
                "site_map_discover_website_not_accessible",
                1,
                dimensions=[{"Name": "PlaybookId", "Value": str(playbook_id)}],
            )
            return

        handler = ConnectedAssetInfoGroupsHandler(
            playbook_id=playbook_id,
            customer_instructions=customer_instructions,
            max_depth=max_depth,
            max_pages=max_pages,
        )
        handler.discover_and_store(
            url=company_website,
        )

    except Playbook.DoesNotExist:
        logging.error(f"Playbook not found for playbook {playbook_id}")
        return
    except Exception as e:
        logging.exception(
            f"Error in generate_suggested_connected_asset_group_task: {e}"
        )
        return


@celery_app.task
def async_refresh_connected_asset_group(asset_group_id, task_id):
    """Asynchronously refresh a connected asset group."""
    # This async task is used to refresh the connected asset group
    try:
        asset_info_group = AssetInfoGroup.objects.get(id=asset_group_id)
        update_connected_asset_group_refresh_task_id(asset_info_group, task_id)
    except AssetInfoGroup.DoesNotExist:
        logging.error(f"Asset group {asset_group_id} not found")
        # Can't update status on non-existent asset group
        return

    if not is_tofu_connected_assets_group(asset_info_group):
        logging.warning(f"Asset group {asset_group_id} is not tofu managed, skipping")
        update_connected_asset_group_refresh_status(
            asset_info_group, ConnectedAssetsGroupRefreshStatus.ERROR
        )
        return

    # Validate metadata structure
    meta_data = asset_info_group.meta.get("tofu_connected_assets_metadata")
    if not meta_data:
        logging.error(
            f"Asset group {asset_group_id} has no tofu_connected_assets_metadata, skipping"
        )
        update_connected_asset_group_refresh_status(
            asset_info_group, ConnectedAssetsGroupRefreshStatus.ERROR
        )
        return

    # Log connection type info for debugging
    tofu_connection_info = meta_data.get("tofu_connected_source_type_info", {})
    logging.info(
        f"Asset group {asset_group_id} tofu_connected_source_type_info: {tofu_connection_info}"
    )

    # Validate connection type
    tofu_connection_type = meta_data.get("tofu_connected_source_type")
    if not tofu_connection_type:
        logging.error(f"No connection type found for asset group {asset_group_id}")
        update_connected_asset_group_refresh_status(
            asset_info_group, ConnectedAssetsGroupRefreshStatus.ERROR
        )
        return

    refresh_connected_asset_group(asset_info_group, tofu_connection_type)
