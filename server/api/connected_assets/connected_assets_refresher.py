import logging
import time
import uuid
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from django.core.cache import cache
from django.db import transaction
from langchain_core.documents import Document

from ..data_loaders.url_loader import TofuURLLoader
from ..data_loaders.web_page_loader import TofuWebPageLoader
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..models import AssetInfo, AssetInfoGroup
from ..playbook_build.async_object_tasks import parallel_build_docs
from ..shared_types import ConnectedAssetsSourceType
from ..utils import CloudWatchMetrics, TofuLLMCache
from .connected_asset_metadata_extractor import ConnectedAssetMetadataExtractor
from .connected_assets_utils import (
    AssetCreationResult,
    bulk_create_assets,
    get_normalized_url_from_connected_source,
    normalize_url,
    verify_url_exists,
)


class ConnectedAssetsGroupRefresher(ABC):
    """Base class for connected assets group refreshers."""

    @abstractmethod
    def validate_metadata(self, meta_data: Dict[str, Any]) -> bool:
        """Validate that the metadata contains all required fields for this connection type."""
        pass

    @abstractmethod
    def refresh_assets(self, asset_group: AssetInfoGroup) -> Dict[str, List[str]]:
        """Refresh assets for the given asset group.

        Returns:
            Dict with keys 'new_assets', 'updated_assets', and 'deleted_assets', each containing a list of asset keys.
        """
        pass


class ConnectedAssetsGroupRefreshStatus(str, Enum):
    """Enum for the connected assets group refresh status."""

    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    SUCCESS_NO_DATA = "success_no_data"
    ERROR = "error"


class URLAssetsGroupRefresher(ConnectedAssetsGroupRefresher, BaseTracableClass):
    """Handles URL-based connected assets."""

    def __init__(self, metadata_extractor=None, perform_delete=False):
        self.metadata_extractor = (
            metadata_extractor or ConnectedAssetMetadataExtractor()
        )
        self.perform_delete = perform_delete

    def validate_metadata(self, meta_data: Dict[str, Any]) -> bool:
        """Validate URL-specific metadata requirements."""

        # Validate connection info exists
        tofu_connection_info = meta_data.get("tofu_connected_source_type_info", {})

        # Validate URLs exist
        urls = []
        if tofu_connection_info and "urls" in tofu_connection_info:
            urls = tofu_connection_info.get("urls", [])

        if not urls:
            logging.error(f"No URLs found for asset group {meta_data}")
            return False

        return True

    def refresh_assets(self, asset_group: AssetInfoGroup) -> Dict[str, List[str]]:
        """Refresh URL-based assets for the given asset group."""
        result = {"new_assets": [], "updated_assets": [], "deleted_assets": []}
        start_time = time.time()

        # get the current refresh status
        current_status = get_connected_asset_group_refresh_status(asset_group)
        if current_status == ConnectedAssetsGroupRefreshStatus.IN_PROGRESS.value:
            logging.error(f"Asset group {asset_group.id} is already in progress")
            return result

        # update the refresh status
        update_connected_asset_group_refresh_status(
            asset_group, ConnectedAssetsGroupRefreshStatus.IN_PROGRESS
        )

        try:
            meta_data = asset_group.meta.get("tofu_connected_assets_metadata", {})

            tofu_connection_info = meta_data.get("tofu_connected_source_type_info", {})
            category = meta_data.get("category")

            if not category:
                # we should deduce the category from the asset group url
                logging.info(f"Deducing category for asset group {asset_group.id}")
                # Get the first URL from the connected source info
                urls = tofu_connection_info.get("urls", [])
                if not urls:
                    logging.error(
                        f"No URLs found in asset group {asset_group.id} metadata"
                    )
                    return result
                category = self._deduce_category_from_url(urls[0], asset_group.id)
                logging.info(
                    f"Deduced category {category} for asset group {asset_group.id}"
                )
                update_connected_asset_group_category(asset_group, category)

            # Get URLs from metadata
            urls = []
            if tofu_connection_info and "urls" in tofu_connection_info:
                urls = tofu_connection_info.get("urls", [])

            # Get crawl parameters from metadata
            max_depth = int(tofu_connection_info.get("max_depth", 2))
            max_pages = int(tofu_connection_info.get("max_pages", 1000))

            # Process all URLs and collect documents
            all_docs = self._crawl_urls_for_asset_group(
                urls, max_depth, max_pages, category, asset_group.id
            )

            if not all_docs:
                logging.warning(
                    f"No documents were loaded for asset group {asset_group.id}"
                )
                update_connected_asset_group_refresh_status(
                    asset_group, ConnectedAssetsGroupRefreshStatus.SUCCESS_NO_DATA
                )
                return result

            # get all existing asset info under this asset group
            asset_infos = AssetInfo.objects.filter(asset_info_group=asset_group)

            # Get URLs from existing assets that are not excluded from sync
            existing_urls = {}
            for asset in asset_infos:
                url_doc = next(
                    (
                        doc
                        for doc in asset.docs.values()
                        if doc.get("type") == "url" and doc.get("value")
                    ),
                    None,
                )
                if url_doc:
                    existing_urls[url_doc["value"]] = asset

            excluded_urls = get_excluded_urls(asset_group)
            # Get URLs from new docs
            urls_to_doc_map = {
                doc.metadata.get("source"): doc
                for doc in all_docs
                if doc.metadata.get("source")
                and doc.metadata.get("source") not in excluded_urls
            }
            # Find URLs to delete (exist in existing but not in new)
            normalized_new_urls = {normalize_url(url) for url in urls_to_doc_map.keys()}
            urls_to_delete = {
                url
                for url in existing_urls.keys()
                if normalize_url(url) not in normalized_new_urls
            }

            logging.info(
                f"For connected asset group {asset_group.id}, found {len(urls_to_doc_map)} new URLs; {len(existing_urls)} existing URLs, {len(excluded_urls)} excluded URLs, {len(urls_to_delete)} URLs to delete"
            )

            for url in urls_to_delete:
                asset = existing_urls[url]
                is_url_exists = verify_url_exists(url)
                logging.info(
                    f"Performing delete: {self.perform_delete} Deleting asset {asset.asset_key} as URL {url} no longer exists, is_url_exists: {is_url_exists}"
                )
                try:
                    if self.perform_delete:
                        result["deleted_assets"].append(asset.asset_key)
                        # We actually do not delete the asset, because we want to keep it as a record
                        # Todo: evaluate if we should delete the asset
                        asset.delete()
                except Exception as e:
                    logging.exception(
                        f"Error deleting asset {asset.asset_key} for URL {url}: {e}"
                    )

            # Find URLs to create (exist in new but not in existing)
            normalized_existing_urls = {
                normalize_url(url) for url in existing_urls.keys()
            }
            urls_to_create = {
                url
                for url in urls_to_doc_map.keys()
                if normalize_url(url) not in normalized_existing_urls
            }

            logging.info(
                f"For connected asset group {asset_group.id}, found {len(urls_to_create)} URLs to create"
            )

            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_urls_to_create",
                len(urls_to_create),
                [
                    {"Name": "AssetGroup", "Value": str(asset_group.id)},
                ],
            )
            time_before_create_assets = time.time()
            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_time_process_urls",
                time_before_create_assets - start_time,
                [
                    {"Name": "AssetGroup", "Value": str(asset_group.id)},
                ],
            )
            # Collect all new assets to build in parallel
            new_assets = []

            time_before_build_assets = time.time()

            creation_result = self._create_assets_in_parallel(
                urls_to_create,
                asset_group,
                urls_to_doc_map,
                urls[0],
                category,
            )
            new_assets.extend(creation_result.created_assets)
            result["new_assets"].extend(creation_result.created_asset_keys)

            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_time_create_assets",
                time_before_build_assets - time_before_create_assets,
                [
                    {"Name": "AssetGroup", "Value": str(asset_group.id)},
                ],
            )
            logging.info(
                f"Created {len(creation_result.created_assets)} assets in parallel, {len(creation_result.created_asset_keys)} asset keys"
            )
            # Find URLs to update (exist in both existing and new), We actually do nothing here,
            # because we will keep it as a record
            urls_to_update = set(existing_urls.keys()) & set(urls_to_doc_map.keys())
            for url in urls_to_update:
                asset = existing_urls[url]
                result["updated_assets"].append(asset.asset_key)

            # Build all new assets in parallel
            if new_assets:
                logging.info(f"Building {len(new_assets)} new assets in parallel")
                parallel_build_docs(
                    new_assets, rebuild=True, check_and_rebuild=True, parallel_threads=8
                )
            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_time_build_assets",
                time.time() - time_before_build_assets,
                [
                    {"Name": "AssetGroup", "Value": str(asset_group.id)},
                ],
            )
            update_connected_asset_group_refresh_status(
                asset_group, ConnectedAssetsGroupRefreshStatus.SUCCESS
            )
            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_total_time",
                time.time() - start_time,
                [
                    {"Name": "AssetGroup", "Value": str(asset_group.id)},
                    {
                        "Name": "Status",
                        "Value": ConnectedAssetsGroupRefreshStatus.SUCCESS.value,
                    },
                ],
            )
            return result
        except Exception as e:
            logging.exception(f"Error refreshing asset group {asset_group.id}: {e}")
            update_connected_asset_group_refresh_status(
                asset_group, ConnectedAssetsGroupRefreshStatus.ERROR
            )
            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_total_time",
                time.time() - start_time,
                [
                    {"Name": "AssetGroup", "Value": str(asset_group.id)},
                    {
                        "Name": "Status",
                        "Value": ConnectedAssetsGroupRefreshStatus.ERROR.value,
                    },
                ],
            )
            return result

    def _crawl_with_same_folder(
        self, url: str, max_depth: int, max_pages: int
    ) -> List[Document]:
        """Crawl URLs within the same folder structure."""
        try:
            tofu_web_page_loader = TofuWebPageLoader(
                url,
                deep_crawl=True,
                crawler_max_depth=max_depth,
                crawler_max_page=max_pages,
            )
            # Todo: consider using prefix based crawing
            docs = tofu_web_page_loader.load_deep(deep_crawl_only_same_folder=True)
            return docs
        except Exception as e:
            logging.exception(f"Error loading URL {url} with same folder crawl: {e}")
            return []

    def _crawl_with_llm_verification(
        self, url: str, max_depth: int, max_pages: int, category: str
    ) -> List[Document]:
        """Crawl all URLs and verify content relevance using LLM."""
        try:
            tofu_web_page_loader = TofuWebPageLoader(
                url,
                deep_crawl=True,
                crawler_max_depth=max_depth,
                crawler_max_page=max_pages,
            )
            start_time = time.time()
            docs = tofu_web_page_loader.load_deep(
                deep_crawl_only_same_folder=False,
                always_crawl_next_page=True,
            )
            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_deep_crawl_time",
                time.time() - start_time,
                [
                    {"Name": "URL", "Value": url},
                    {"Name": "Category", "Value": category},
                    {"Name": "MaxDepth", "Value": str(max_depth)},
                    {"Name": "MaxPages", "Value": str(max_pages)},
                ],
            )
            # Get all URLs from documents
            urls = [
                normalize_url(doc.metadata.get("source"))
                for doc in docs
                if doc.metadata.get("source")
            ]
            # Filter docs based on LLM verification of URLs
            relevant_urls = self.metadata_extractor.verify_urls_relevance(
                listing_page_url=url, urls=urls, category=category
            )

            # Return only documents with relevant URLs
            return [
                doc
                for doc in docs
                if normalize_url(doc.metadata.get("source")) in relevant_urls
            ]
        except Exception as e:
            logging.error(f"Error loading URL {url} with full crawl: {e}")
            return []

    def _crawl_urls_for_asset_group(
        self,
        urls: List[str],
        max_depth: int,
        max_pages: int,
        category: str,
        asset_group_id: str,
    ) -> List[Document]:
        """Crawl multiple URLs for an asset group, using both same-folder and full crawl strategies."""
        all_docs = []
        for url in urls:
            logging.info(f"Processing URL: {url} for asset group {asset_group_id}")
            url_to_crawl = TofuURLLoader.add_schema_if_missing(url)
            # We use the LLM to verify the URLs relevance
            llm_verified_docs = self._crawl_with_llm_verification(
                url_to_crawl, max_depth, max_pages, category
            )
            all_docs.extend(llm_verified_docs)

        CloudWatchMetrics.put_metric(
            "connected_assets_refresher_total_docs",
            len(all_docs),
            [
                {"Name": "AssetGroup", "Value": str(asset_group_id)},
            ],
        )

        return all_docs

    def _deduce_category_from_url(self, url: str, asset_group_id: str) -> str:
        """Deduce the category from the URL."""
        category = self.metadata_extractor.deduce_category_from_url(url)
        if category == "Ignore":
            logging.warning(f"Deduced category {category} for URL {url}")
            CloudWatchMetrics.put_metric(
                "connected_assets_refresher_url_category_deduction_ignored",
                1,
                [
                    {
                        "Name": "AssetGroup",
                        "Value": asset_group_id,
                    }
                ],
            )
        return category

    def _create_assets_in_parallel(
        self,
        urls_to_create: set[str],
        asset_group: AssetInfoGroup,
        urls_to_doc_map: Dict[str, Document],
        starting_listing_page_url: str,
        starting_listing_page_category: str,
        num_workers: int = 4,
    ) -> AssetCreationResult:
        """
        Entry point for parallel asset creation. Splits URLs into batches and processes them in parallel.

        Args:
            urls_to_create: Set of URLs to process
            asset_group: The asset group to which the assets belong
            urls_to_doc_map: Dictionary mapping URLs to Document objects
            category: The category of the assets
            num_workers: Number of parallel workers

        Returns:
            AssetCreationResult: Object containing lists of successfully created assets and failures
        """
        if not urls_to_create:
            return AssetCreationResult([], [], [])

        # Calculate batch size and create batches of URLs
        total_urls = len(urls_to_create)
        batch_size = max(total_urls // num_workers, 1)
        url_batches = [
            list(urls_to_create)[i : i + batch_size]
            for i in range(0, total_urls, batch_size)
        ]
        logging.info(f"Creating {len(url_batches)} batches of {batch_size} URLs")

        all_created_assets = []
        all_created_asset_keys = []
        all_failures = []

        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            future_to_batch = {
                executor.submit(
                    self._do_process_and_create_assets,
                    url_batch,
                    asset_group,
                    urls_to_doc_map,
                    starting_listing_page_url,
                    starting_listing_page_category,
                ): url_batch
                for url_batch in url_batches
            }

            for future in as_completed(future_to_batch):
                try:
                    result: AssetCreationResult = future.result()
                    all_created_assets.extend(result.created_assets)
                    all_created_asset_keys.extend(result.created_asset_keys)
                    all_failures.extend(result.failures)
                except Exception as e:
                    CloudWatchMetrics.put_metric(
                        "connected_assets_creation_failure",
                        1,
                        [{"Name": "asset_group_id", "Value": str(asset_group.id)}],
                    )
                    batch = future_to_batch[future]
                    logging.exception(f"Error processing batch of URLs: {e}")
                    all_failures.extend([(url, str(e)) for url in batch])

        logging.info(
            f"Created {len(all_created_assets)} assets in parallel, {len(all_failures)} failures"
        )
        CloudWatchMetrics.put_metric(
            "connected_assets_creation_success_assets",
            len(all_created_assets),
            [{"Name": "asset_group_id", "Value": str(asset_group.id)}],
        )
        if all_failures:
            logging.error(f"Failed to create assets {all_failures}")
            CloudWatchMetrics.put_metric(
                "connected_assets_creation_failed_assets",
                len(all_failures),
                [{"Name": "asset_group_id", "Value": str(asset_group.id)}],
            )

        return AssetCreationResult(
            all_created_assets, all_created_asset_keys, all_failures
        )

    @dynamic_traceable(name="process_and_create_assets")
    def _do_process_and_create_assets(
        self,
        urls_batch: List[str],
        asset_group: AssetInfoGroup,
        urls_to_doc_map: Dict[str, Document],
        starting_listing_page_url: str,
        starting_listing_page_category: str,
    ) -> AssetCreationResult:
        """
        Process a batch of URLs: extract metadata using LLM and bulk create assets.

        Args:
            urls_batch: List of URLs to process
            asset_group: The asset group to which the assets belong
            urls_to_doc_map: Dictionary mapping URLs to Document objects
            category: The category of the assets

        Returns:
            AssetCreationResult: Object containing lists of successfully created assets and failures
        """
        assets_to_create = []
        current_date = datetime.now(timezone.utc).isoformat()

        for url in urls_batch:
            try:
                doc = urls_to_doc_map[url]
                metadata = self.metadata_extractor.extract_metadata(
                    doc, starting_listing_page_url, starting_listing_page_category
                )

                # Validate category
                extracted_category = metadata.get("category")
                if not extracted_category or extracted_category.lower() == "ignore":
                    logging.warning(
                        f"Deduced category {extracted_category} for URL {url} is ignored"
                    )
                    CloudWatchMetrics.put_metric(
                        "connected_assets_refresher_url_asset_creation_skipped",
                        1,
                        [
                            {
                                "Name": "AssetGroup",
                                "Value": str(asset_group.id),
                            }
                        ],
                    )
                    continue

                # Prepare asset
                asset_key = metadata.get("title", url)
                doc_id = str(uuid.uuid4())

                assets_to_create.append(
                    AssetInfo(
                        asset_info_group=asset_group,
                        asset_key=asset_key,
                        docs={
                            doc_id: {
                                "id": doc_id,
                                "type": "url",
                                "value": url,
                                "position": 0,
                            }
                        },
                        meta={
                            "created_time": current_date,
                            "published_date": metadata.get("published_date"),
                            "category": starting_listing_page_category,
                            "exclude_from_sync": False,
                            "blog_author": metadata.get("author"),
                            "position": 0,
                            "tofu_connected_source": True,
                        },
                        docs_build_status={"connected_source_build_status": "started"},
                    )
                )
            except Exception as e:
                logging.exception(f"Error preparing asset for URL {url}: {e}")

        # Use the bulk_create_assets function to handle asset creation
        return bulk_create_assets(assets_to_create)


def get_connected_asset_group_refresher(
    connection_type: ConnectedAssetsSourceType,
) -> ConnectedAssetsGroupRefresher:
    """Factory function to get the appropriate connected asset group refresher."""
    connection_types = {
        ConnectedAssetsSourceType.Url: URLAssetsGroupRefresher,
        # Add more connection types here as needed
    }

    handler_class = connection_types.get(connection_type)
    if not handler_class:
        raise ValueError(f"Unsupported connection type: {connection_type}")

    return handler_class()


def refresh_connected_asset_group(
    asset_group: AssetInfoGroup, connection_type: ConnectedAssetsSourceType
) -> Dict[str, List[str]]:
    """Refresh a connected asset group based on its connection type.

    Returns:
        Dict with keys 'new_assets', 'updated_assets', and 'deleted_assets', each containing a list of asset keys.
    """

    # Before refreshing, we check if there is a suggested connected source with the same URL
    # If there is, we delete the suggested connected source
    delete_duplicated_suggested_connected_sources(asset_group)
    try:
        connection_handler = get_connected_asset_group_refresher(connection_type)
        with TofuLLMCache():
            return connection_handler.refresh_assets(asset_group)
    except Exception as e:
        logging.error(f"Error refreshing asset group {asset_group.id}: {e}")
        CloudWatchMetrics.put_metric(
            "connected_assets_refresh_failure",
            1,
            [{"Name": "asset_group_id", "Value": str(asset_group.id)}],
        )
        update_connected_asset_group_refresh_status(
            asset_group, ConnectedAssetsGroupRefreshStatus.ERROR
        )


@transaction.atomic
def delete_duplicated_suggested_connected_sources(asset_group: AssetInfoGroup):
    """Delete duplicated suggested connected sources."""
    try:
        start_time = time.time()
        all_suggested_connected_sources = AssetInfoGroup.objects.filter(
            playbook_id=asset_group.playbook_id,
            meta__tofu_connected_assets_metadata__tofu_connected_source=True,
            meta__tofu_connected_assets_metadata__tofu_suggested_connected_source=True,
        ).exclude(id=asset_group.id)
        logging.info(
            f"Found {len(all_suggested_connected_sources)} suggested connected sources to delete"
        )
        CloudWatchMetrics.put_metric(
            "connected_assets_refresher_scan_duplicated_suggested_time",
            time.time() - start_time,
            [{"Name": "AssetGroup", "Value": str(asset_group.id)}],
        )
        logging.info(
            f"Using {time.time() - start_time} seconds to scan {len(all_suggested_connected_sources)} suggested connected sources"
        )
        for suggested_connected_source in all_suggested_connected_sources:
            if get_normalized_url_from_connected_source(
                suggested_connected_source
            ) == get_normalized_url_from_connected_source(asset_group):
                logging.info(
                    f"Deleting suggested connected source {suggested_connected_source.id} because it has the same URL as the asset group {asset_group.id}"
                )
                CloudWatchMetrics.put_metric(
                    "connected_assets_refresher_delete_duplicated_suggested_success",
                    1,
                    [{"Name": "AssetGroup", "Value": str(asset_group.id)}],
                )
                suggested_connected_source.delete()
    except Exception as e:
        logging.exception(f"Error deleting duplicated suggested connected source: {e}")
        CloudWatchMetrics.put_metric(
            "connected_assets_refresher_delete_duplicated_suggested_failure",
            1,
            [{"Name": "AssetGroup", "Value": str(asset_group.id)}],
        )


# we are expecting the status field to be
@transaction.atomic
def update_connected_asset_group_refresh_status(
    asset_group: AssetInfoGroup, status: ConnectedAssetsGroupRefreshStatus
):
    """Update the asset group refresh status."""
    try:
        asset_group.refresh_from_db()
        current_meta = asset_group.meta
        connected_assets_metadata = current_meta.get(
            "tofu_connected_assets_metadata", {}
        )
        task_id = get_connected_asset_group_refresh_task_id(asset_group)
        if task_id:
            cache.set(task_id, {"status": status.value}, timeout=60 * 60 * 6 + 60 * 10)
        if status.value in {
            ConnectedAssetsGroupRefreshStatus.SUCCESS.value,
            ConnectedAssetsGroupRefreshStatus.SUCCESS_NO_DATA.value,
            ConnectedAssetsGroupRefreshStatus.ERROR.value,
        }:
            connected_assets_metadata["last_synced_at"] = datetime.now(
                timezone.utc
            ).isoformat()
        connected_assets_metadata["last_synced_status"] = status.value
        update_connected_asset_group_metadata(
            asset_group, {"tofu_connected_assets_metadata": connected_assets_metadata}
        )
    except Exception as e:
        logging.exception(f"Failed to update asset group refresh status: {e}")
        CloudWatchMetrics.put_metric(
            "connected_assets_refresh_status_update_failure",
            1,
            [{"Name": "asset_group_id", "Value": str(asset_group.id)}],
        )
        if task_id:
            cache.set(
                task_id,
                {"status": ConnectedAssetsGroupRefreshStatus.ERROR.value},
                timeout=60 * 60 * 6 + 60 * 10,
            )
        raise e


def get_connected_asset_group_refresh_status(
    asset_group: AssetInfoGroup,
) -> ConnectedAssetsGroupRefreshStatus:
    """Get the asset group refresh status."""
    asset_group.refresh_from_db()
    task_id = get_connected_asset_group_refresh_task_id(asset_group)
    if task_id and cache.get(task_id) and cache.get(task_id).get("status"):
        return ConnectedAssetsGroupRefreshStatus(cache.get(task_id)["status"])
    else:
        # fall back to the meta data if the task id is not set
        # TODO: remove this fallback once we have a task id for all asset groups
        CloudWatchMetrics.put_metric(
            "connected_assets_refresh_status_no_task_id",
            1,
            [{"Name": "asset_group_id", "Value": str(asset_group.id)}],
        )
        logging.warning(
            f"No task id found for asset group {asset_group.id}, falling back to meta data"
        )
        current_meta = asset_group.meta
        connected_assets_metadata = current_meta.get(
            "tofu_connected_assets_metadata", {}
        )
        return connected_assets_metadata.get(
            "last_synced_status", ConnectedAssetsGroupRefreshStatus.NOT_STARTED.value
        )


def update_connected_asset_group_refresh_task_id(
    asset_group: AssetInfoGroup, task_id: str
):
    """Update the asset group refresh task id."""
    current_meta = asset_group.meta
    connected_assets_metadata = current_meta.get("tofu_connected_assets_metadata", {})
    connected_assets_metadata["refresh_task_id"] = task_id
    update_connected_asset_group_metadata(
        asset_group, {"tofu_connected_assets_metadata": connected_assets_metadata}
    )


def get_connected_asset_group_refresh_task_id(
    asset_group: AssetInfoGroup,
) -> Optional[str]:
    """Get the asset group refresh task id."""
    current_meta = asset_group.meta
    connected_assets_metadata = current_meta.get("tofu_connected_assets_metadata", {})
    return connected_assets_metadata.get("refresh_task_id", None)


@transaction.atomic
def update_connected_asset_group_metadata(
    asset_group: AssetInfoGroup, metadata: Dict[str, Any]
):
    """Update the asset group metadata"""
    try:
        current_meta = asset_group.meta
        current_meta.update(metadata)
        AssetInfoGroup.objects.filter(id=asset_group.id).update(meta=current_meta)
    except Exception as e:
        logging.exception(f"Failed to update asset group metadata: {e}")
        raise e


@transaction.atomic
def update_connected_asset_group_category(asset_group: AssetInfoGroup, category: str):
    """Update the asset group category."""
    try:
        current_meta = asset_group.meta
        current_meta["tofu_connected_assets_metadata"]["category"] = category
        AssetInfoGroup.objects.filter(id=asset_group.id).update(meta=current_meta)
    except Exception as e:
        logging.exception(f"Failed to update asset group category: {e}")
        raise e


def get_excluded_urls(asset_group: AssetInfoGroup) -> List[str]:
    """Get the excluded URLs for the asset group."""
    current_meta = asset_group.meta
    connected_assets_metadata = current_meta.get("tofu_connected_assets_metadata", {})
    tofu_connected_source_type_info = connected_assets_metadata.get(
        "tofu_connected_source_type_info", {}
    )
    return tofu_connected_source_type_info.get("excluded_urls", [])
