import logging
import traceback
from datetime import timedelta

import psutil
from celery import current_task
from celery.exceptions import SoftTimeLimitExceeded
from django.core.cache import cache
from django.db.models import Count
from django.utils import timezone
from server.celery import app

from .integrations.slack import send_slack_message
from .models import Campaign, FulltextLLMCache, Playbook, TofuUser
from .playbook_health import CheckOption, PlaybookCheckManager, TofuUserCheckManager
from .playbook_sync import PlaybookSyncerManager
from .utils import log_memory_usage


@app.task(name="health_check", acks_late=True)
def health_check(
    test_user=True,
    test_playbook=True,
    check_options=None,
):
    log_memory_usage("health_check", "start")
    if check_options is None:
        check_options = CheckOption.all_enabled()

    if test_user:
        try:
            tofu_user_checker = TofuUserCheckManager()
            tofu_user_checker.run_health_check()
            log_memory_usage("health_check", "after tofu_user check")
        except Exception as e:
            logging.exception(f"Failed to run health check for tofu_user: {e}")

    if test_playbook:
        try:
            playbook_checker = PlaybookCheckManager()
            playbook_checker.run_health_check(check_options)
            log_memory_usage("health_check", "after playbook check")
        except Exception as e:
            logging.exception(f"Failed to run health check for playbook: {e}")

    log_memory_usage("health_check", "end")


@app.task(
    name="export_auto_sync",
    acks_late=True,
    soft_time_limit=60 * 5,
    time_limit=int(60 * 5 * 1.1),
)
def export_auto_sync():
    try:
        auto_sync_campaigns = Campaign.objects.filter(
            campaign_params__enable_auto_sync=True
        )
        # Now, get the Playbooks that are related to these Campaigns
        playbooks = Playbook.objects.filter(
            id__in=auto_sync_campaigns.values("playbook_id")
        )
        current_time = timezone.now().strftime("%Y-%m-%d:%H:%M")
        for playbook in playbooks:
            task_id = f"autopilot_sync_single_playbook:{playbook.id}:parent_task_id:{current_task.request.id}:{current_time}"
            autopilot_sync_single_playbook.apply_async(
                args=[playbook.id], task_id=task_id
            )
    except Exception as e:
        export_task_id = current_task.request.id
        logging.exception(
            f"Failed to queue auto sync jobs for task {export_task_id}: {e}"
        )


@app.task(
    name="autopilot_sync_single_playbook",
    acks_late=True,
    soft_time_limit=3600 * 12,  # 2 hours timeout per playbook
    time_limit=int(3600 * 12 * 1.1),
)
def autopilot_sync_single_playbook(playbook_id):
    try:
        logging.info(f"Started syncing playbook {playbook_id}")
        playbook = Playbook.objects.get(id=playbook_id)
        syncer = PlaybookSyncerManager(playbook)
        syncer.sync()
        logging.info(f"Finished syncing playbook {playbook_id}")
    except SoftTimeLimitExceeded:
        logging.exception(
            f"Playbook sync {playbook_id} exceeded time limit and will be revoked."
        )
        current_task.revoke(terminate=True)
    except Exception as e:
        logging.exception(f"Error syncing playbook {playbook_id}: {e}")


@app.task(name="clean_llm_cache", acks_late=True)
def clean_llm_cache():
    try:
        threshold_date = timezone.now() - timedelta(days=30)
        FulltextLLMCache.objects.filter(created_at__lt=threshold_date).delete()
    except Exception as e:
        logging.exception(f"Failed to clean LLM cache: {e}")


@app.task(name="clean_e2e_test_accounts", acks_late=True)
def clean_e2e_test_accounts():
    PREFIX_OF_E2E_TEST_USERNAME = "tofuadmin-e2etest-use-only-"
    E2E_TEST_EMAIL = "<EMAIL>"

    try:
        # Query TofuUser with name pattern PREFIX_OF_E2E_TEST_USERNAME + ${uuid()} and created more than 1 hour ago
        e2e_test_users_to_delete = TofuUser.objects.filter(
            username__startswith=PREFIX_OF_E2E_TEST_USERNAME,
            date_joined__lt=timezone.now() - timedelta(hours=1),
            email__iexact=E2E_TEST_EMAIL,
        )

        playbooks_to_delete = Playbook.objects.annotate(
            user_count=Count("users")
        ).filter(user_count=1, users__in=e2e_test_users_to_delete)

        user_count = e2e_test_users_to_delete.count()
        playbook_count = playbooks_to_delete.count()
        logging.info(f"Found {user_count} E2E test users to delete")
        logging.info(f"Found {playbook_count} playbooks to delete")

        if user_count != playbook_count:
            # just for safety check
            logging.error(
                f"User count and playbook count do not match: {user_count} != {playbook_count}"
            )
            # check the data one by one and print which one is not match
            for user in e2e_test_users_to_delete:
                if not playbooks_to_delete.filter(users__in=[user]).exists():
                    logging.error(f"User {user.username} has no playbook to delete")

        all_user_names = [user.username for user in e2e_test_users_to_delete]
        try:
            playbooks_to_delete.delete()
            e2e_test_users_to_delete.delete()
        except Exception as e:
            logging.exception(f"Failed to delete E2E test users: {e}")
        else:
            message = f"Deleted {playbook_count} playbooks and {user_count} E2E test users\nUsers deleted:\n\t"
            message += "\n\t".join(all_user_names)
            send_slack_message("#bot_tests", message)

    except Exception as e:
        logging.exception(f"Failed to clean E2E test accounts: {e}")
