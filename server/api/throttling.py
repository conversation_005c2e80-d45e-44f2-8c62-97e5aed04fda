import logging
from functools import wraps

from django_ratelimit.decorators import ratelimit
from django_ratelimit.exceptions import Ratelimited

from .models import TofuUser


def tofu_lite_ratelimit(rate="20/m"):
    """
    Rate limit decorator for Tofu Lite users.
    Applies a rate limit per user per endpoint.
    Only affects Tofu Lite users, other users bypass the rate limit.

    Args:
        rate: Rate string (e.g., '30/h', '100/m', '1000/d')
                    Defaults to 20 requests per minute
    """

    def rate_limit_class_method(view_func):
        """A decorator specifically designed for class-based view methods."""

        def wrapped_method(view_instance, request, *args, **kwargs):
            user = request.user

            if not user or user.is_anonymous:
                logging.info(f"No user found or anonymous user in request {request}")
                return view_func(view_instance, request, *args, **kwargs)

            if user.customer_type != TofuUser.CustomerType.LITE:
                return view_func(view_instance, request, *args, **kwargs)

            def cache_key(group, request):
                return f"tofu_lite_ratelimit:{user.id}:{view_instance.__class__.__name__}:{view_func.__name__}"

            try:
                # Apply Django's rate limiter to the view function
                return ratelimit(key=cache_key, rate=rate, block=True)(view_func)(
                    view_instance, request, *args, **kwargs
                )
            except Ratelimited:
                raise
            except Exception as e:
                logging.error(f"Error in tofu_lite_ratelimit: {e}")
                return view_func(view_instance, request, *args, **kwargs)

        return wraps(view_func)(wrapped_method)

    # Since we're always dealing with class-based views,
    # directly return a decorated function
    return rate_limit_class_method


def public_api_ratelimit(rate="20/m"):
    """
    Rate limit decorator for public APIs.
    Applies a rate limit per IP address per endpoint.
    This is used for endpoints that don't require authentication.

    Args:
        rate: Rate string (e.g., '30/h', '100/m', '1000/d')
                    Defaults to 20 requests per minute
    """

    def rate_limit_class_method(view_func):
        """A decorator specifically designed for class-based view methods."""

        def wrapped_method(view_instance, request, *args, **kwargs):

            try:
                # Get the client's IP address
                x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
                if x_forwarded_for:
                    ip = x_forwarded_for.split(",")[0]
                    logging.debug(f"Using X-Forwarded-For IP: {ip}")
                else:
                    ip = request.META.get("REMOTE_ADDR")
                    logging.debug(f"Using REMOTE_ADDR IP: {ip}")

                if not ip:
                    logging.warning("Could not determine client IP address")
                    ip = "unknown"

                def cache_key(group, request):
                    return f"public_api_ratelimit:{ip}:{view_instance.__class__.__name__}:{view_func.__name__}"

                # Apply Django's rate limiter to the view function
                return ratelimit(key=cache_key, rate=rate, block=True)(view_func)(
                    view_instance, request, *args, **kwargs
                )

            except Ratelimited:
                logging.warning(
                    f"Rate limit exceeded for IP on {view_func.__name__} - Bypassing for testing"
                )
                # Bypass rate limit for testing
                return view_func(view_instance, request, *args, **kwargs)
            except Exception as e:
                logging.exception(f"Error in rate limiting: {str(e)}")
                return view_func(view_instance, request, *args, **kwargs)

        return wraps(view_func)(wrapped_method)

    return rate_limit_class_method
