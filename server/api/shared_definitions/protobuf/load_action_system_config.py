import json
import os
import sys
import uuid
from pathlib import Path

from google.protobuf.json_format import Parse, ParseError


def setup_imports():
    # Get the absolute path to the backend directory
    current_dir = Path(__file__).resolve().parent
    backend_dir = current_dir
    while backend_dir.name != "backend":
        backend_dir = backend_dir.parent
        if backend_dir == backend_dir.parent:
            raise RuntimeError("Could not find backend directory")

    # Add backend directory to Python path
    backend_path = str(backend_dir)
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)

    # Temporarily remove __init__.py from the import path to avoid celery import
    api_init = os.path.join(backend_dir, "tofu", "server", "api", "__init__.py")
    temp_name = None
    if os.path.exists(api_init):
        try:
            # Use a unique temporary name to avoid conflicts
            temp_name = f"{api_init}.{uuid.uuid4()}.tmp"
            os.rename(api_init, temp_name)
        except OSError as e:
            raise RuntimeError(f"Failed to rename __init__.py: {e}") from e

    return temp_name, api_init


def cleanup_imports(temp_name, api_init):
    # Restore __init__.py if it was renamed
    if temp_name and os.path.exists(temp_name):
        try:
            os.rename(temp_name, api_init)
        except OSError as e:
            raise RuntimeError(f"Failed to restore __init__.py: {e}") from e


if __name__ == "__main__":
    # Setup imports
    temp_name, api_init = setup_imports()

    try:
        # Now we can safely import
        import importlib.util

        action_system_config_path = os.path.join(
            Path(__file__).parent, "action_system_config.py"
        )
        spec = importlib.util.spec_from_file_location(
            "action_system_config", action_system_config_path
        )
        action_system_config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(action_system_config_module)

        # Print the action_system_config for debugging
        print(action_system_config_module.ACTION_SYSTEM_CONFIG)

    finally:
        # Cleanup
        cleanup_imports(temp_name, api_init)
