#!/usr/bin/env python3
import os
import sys
from pathlib import Path

if __name__ == "__main__":
    # Add the server directory to Python path and set up package context
    current_dir = Path(__file__).parent
    server_dir = current_dir.parent.parent.parent  # Navigate up to the server directory
    sys.path.append(str(server_dir))

    # Set up the package context
    package_name = "api.shared_definitions.protobuf"
    __package__ = package_name

    tofu_root_dir = server_dir.parent.parent.parent
    export_path = os.path.join(tofu_root_dir, "protobuf")

from .action_system_config import ACTION_SYSTEM_CONFIG


def generate_action_system_config_json():
    """Generate a JSON representation of all action configs."""
    import json

    from google.protobuf.json_format import MessageToDict

    action_system_config_dict = MessageToDict(
        ACTION_SYSTEM_CONFIG, preserving_proto_field_name=True
    )
    return json.dumps(action_system_config_dict, indent=2)


# Function to write definitions to a file
def write_action_system_config_to_file(output_path: str = "action_system_config.json"):
    """Write app definitions to a JSON file."""
    with open(output_path, "w") as f:
        f.write(generate_action_system_config_json())


def generate_all_definitions():
    """Generate all definition files for FE/BE consumption."""
    # Get the directory where definition files should be stored

    if "export_path" in globals():
        definitions_dir = export_path
    else:
        current_dir = Path(__file__).parent
        definitions_dir = os.path.join(current_dir, "definitions")

        # Create the definitions directory if it doesn't exist
        os.makedirs(definitions_dir, exist_ok=True)

    # Generate app definitions
    action_system_config_path = os.path.join(
        definitions_dir, "action_system_config.json"
    )
    write_action_system_config_to_file(action_system_config_path)

    print(f"Generated app definitions at: {action_system_config_path}")


if __name__ == "__main__":
    generate_all_definitions()
