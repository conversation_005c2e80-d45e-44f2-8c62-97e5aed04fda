import sys
from pathlib import Path
from typing import List

# Setup path for direct imports
current_dir = Path(__file__).resolve().parent
protobuf_dir = current_dir
if str(protobuf_dir) not in sys.path:
    sys.path.insert(0, str(protobuf_dir))

try:
    from tofu.server.api.shared_definitions.protobuf.gen.action_define_pb2 import (
        ActionCategory,
        ActionDefinition,
        ActionEdgeDefinition,
        ActionEdgeOutputFetchConfigByName,
        ActionEdgeOutputFetchMethod,
        ActionEdgeOutputListFetchMethod,
        ActionInputOutputDefinition,
        ActionStatusType,
        ActionSystemConfig,
        ActionType,
        PlatformType,
        PredefinedValue,
        StringList,
        TofuAsset,
        TofuBool,
        TofuComponent,
        TofuComponents,
        TofuContentGroup,
        TofuContentType,
        TofuData,
        TofuDataList,
        TofuDataType,
        TofuInt,
        TofuPlatformType,
        TofuString,
        TofuTemplate,
    )
except ImportError:
    from .gen.action_define_pb2 import (
        ActionCategory,
        ActionDefinition,
        ActionEdgeDefinition,
        ActionEdgeOutputFetchConfigByName,
        ActionEdgeOutputFetchMethod,
        ActionEdgeOutputListFetchMethod,
        ActionInputOutputDefinition,
        ActionStatusType,
        ActionSystemConfig,
        ActionType,
        PlatformType,
        PredefinedValue,
        StringList,
        TofuAsset,
        TofuBool,
        TofuComponent,
        TofuComponents,
        TofuContentGroup,
        TofuContentType,
        TofuCustomInstruction,
        TofuData,
        TofuDataList,
        TofuDataType,
        TofuInt,
        TofuPlatformType,
        TofuString,
        TofuTemplate,
    )


# Absolute imports
try:
    from tofu.server.api.shared_types import ContentType, ContentTypeDetails
except ImportError:
    from ...shared_types import ContentType, ContentTypeDetails

ACTION_CATEGORY_TO_ACTION_TYPES = {
    ActionCategory.ACTION_CATEGORY_USER_INPUT: [
        ActionType.ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT,
    ],
    ActionCategory.ACTION_CATEGORY_REPURPOSE: [
        ActionType.ACTION_TYPE_REPURPOSE_LANDING_PAGE,
        ActionType.ACTION_TYPE_REPURPOSE_EMAIL_MARKETING,
        ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR,
        ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_GENERAL,
        ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_LINKEDIN,
        ActionType.ACTION_TYPE_REPURPOSE_MESSAGE_LINKEDIN,
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GENERAL,
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN,
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_CAROUSEL,
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_DOCUMENT,
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_META,
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GOOGLE,
        ActionType.ACTION_TYPE_REPURPOSE_WHITEPAPER,
        ActionType.ACTION_TYPE_REPURPOSE_EBOOK,
        ActionType.ACTION_TYPE_REPURPOSE_BLOG_POST,
        ActionType.ACTION_TYPE_REPURPOSE_CASE_STUDY,
        ActionType.ACTION_TYPE_REPURPOSE_SALES_DECK,
        ActionType.ACTION_TYPE_REPURPOSE_SLIDE_DECK,
        ActionType.ACTION_TYPE_REPURPOSE_WEBINAR,
        ActionType.ACTION_TYPE_REPURPOSE_QUOTES_HIGHLIGHTS,
        ActionType.ACTION_TYPE_REPURPOSE_STATISTICS,
        ActionType.ACTION_TYPE_REPURPOSE_OTHER,
    ],
    ActionCategory.ACTION_CATEGORY_PERSONALIZE: [
        ActionType.ACTION_TYPE_PERSONALIZE_LANDING_PAGE,
        ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_MARKETING,
        ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_SDR,
        ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_GENERAL,
        ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_LINKEDIN,
        ActionType.ACTION_TYPE_PERSONALIZE_MESSAGE_LINKEDIN,
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GENERAL,
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN,
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_CAROUSEL,
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_DOCUMENT,
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_META,
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GOOGLE,
        ActionType.ACTION_TYPE_PERSONALIZE_WHITEPAPER,
        ActionType.ACTION_TYPE_PERSONALIZE_EBOOK,
        ActionType.ACTION_TYPE_PERSONALIZE_BLOG_POST,
        ActionType.ACTION_TYPE_PERSONALIZE_SALES_DECK,
        ActionType.ACTION_TYPE_PERSONALIZE_OTHER,
    ],
    ActionCategory.ACTION_CATEGORY_EXPORT: [
        ActionType.ACTION_TYPE_EXPORT_TOFU_LANDING_PAGE,
        ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL,
        ActionType.ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE,
        ActionType.ACTION_TYPE_EXPORT_SALESFORCE_EMAIL,
        ActionType.ACTION_TYPE_EXPORT_MARKETO_EMAIL,
        ActionType.ACTION_TYPE_EXPORT_MARKETO_LANDING_PAGE,
    ],
    ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE: [
        ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_SDR,
        ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_MARKETING,
        ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_LINKEDIN_MESSAGE,
    ],
}

CATEGORY_INPUTS_NEEDED_FOR_DEFINITION = {
    ActionCategory.ACTION_CATEGORY_USER_INPUT: [],
    ActionCategory.ACTION_CATEGORY_REPURPOSE: ["content_type"],
    ActionCategory.ACTION_CATEGORY_PERSONALIZE: ["content_type"],
    ActionCategory.ACTION_CATEGORY_EXPORT: ["content_type", "platform_type"],
    ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE: ["content_type"],
}

PlatformTypeDetails = {
    PlatformType.PLATFORM_TYPE_HUBSPOT: {
        "label": "HubSpot",
    },
    PlatformType.PLATFORM_TYPE_SALESFORCE: {
        "label": "Salesforce",
    },
    PlatformType.PLATFORM_TYPE_MARKETO: {
        "label": "Marketo",
    },
    PlatformType.PLATFORM_TYPE_LINKEDIN: {
        "label": "LinkedIn",
    },
    PlatformType.PLATFORM_TYPE_TOFU: {
        "label": "Tofu",
    },
}

UPLOAD_OR_SELECT_ANCHOR_CONTENT_DEFINITION = ActionDefinition(
    action_category=ActionCategory.ACTION_CATEGORY_USER_INPUT,
    action_type=ActionType.ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT,
    description="Upload or select anchor content",
    required_inputs={
        "assets": ActionInputOutputDefinition(
            position=1,
            description="Assets to repurpose",
            data_type=TofuDataType.TOFU_DATA_TYPE_ASSET,
            is_array=True,
            is_visible_to_user=True,
            is_saved_to_action=True,
            need_special_handling=True,
        ),
    },
    outputs={
        "assets": ActionInputOutputDefinition(
            description="Anchor content",
            data_type=TofuDataType.TOFU_DATA_TYPE_ASSET,
            is_array=True,
        )
    },
    predefined_inputs={},
    eligible_next_actions={
        ActionType.Name(action_type): ActionEdgeDefinition(
            output_fetch_config_by_name=ActionEdgeOutputFetchConfigByName(
                output_to_input_mapping={
                    "assets": ActionEdgeOutputFetchMethod(
                        input_name="anchor_content",
                        fetch_method=ActionEdgeOutputListFetchMethod.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_FETCH_ALL,
                    )
                }
            ),
            is_shown=True,
            is_for_validation=True,
        )
        for action_type in ACTION_CATEGORY_TO_ACTION_TYPES[
            ActionCategory.ACTION_CATEGORY_REPURPOSE
        ]
    },
)

UPLOAD_OR_SELECT_TEMPLATE_ELIGIBLE_NEXT_ACTIONS = {
    ActionType.Name(action_type): ActionEdgeDefinition(
        output_fetch_config_by_name=ActionEdgeOutputFetchConfigByName(
            output_to_input_mapping={
                "template": ActionEdgeOutputFetchMethod(
                    input_name="template",
                    fetch_method=ActionEdgeOutputListFetchMethod.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_FETCH_ALL,
                )
            }
        ),
        is_shown=True,
        is_for_validation=True,
    )
    for action_type in ACTION_CATEGORY_TO_ACTION_TYPES[
        ActionCategory.ACTION_CATEGORY_PERSONALIZE
    ]
}


def _convert_content_type_to_tofudata(content_type):
    return TofuDataList(
        data=[TofuData(content_type=TofuContentType(content_type=content_type))]
    )


def _convert_platform_type_to_tofudata(platform_type):
    return TofuDataList(
        data=[TofuData(platform_type=TofuPlatformType(platform_type=platform_type))]
    )


def _create_predefined_value(value_type: str, value: TofuDataList) -> PredefinedValue:
    return PredefinedValue(value_type=value_type, value=value)


def _create_repurpose_definition(
    action_type: ActionType,
    content_type: ContentType,
    output_action_type_list: List[ActionType] = None,
) -> ActionDefinition:
    if output_action_type_list is None:
        output_action_type_list = []

    def has_word_count(content_type: ContentType) -> bool:
        return content_type in [
            ContentType.BlogPost,
            ContentType.Whitepaper,
            ContentType.EBook,
            ContentType.CaseStudy,
            ContentType.Other,
        ]

    # Create a map of action type names to ActionEdgeDefinition
    eligible_next_actions = {
        ActionType.Name(output_action_type): ActionEdgeDefinition(
            output_fetch_config_by_name=ActionEdgeOutputFetchConfigByName(
                output_to_input_mapping={
                    "content_group": ActionEdgeOutputFetchMethod(
                        input_name="template",
                        fetch_method=ActionEdgeOutputListFetchMethod.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_EXPAND_ONE,
                    )
                }
            ),
            is_shown=True,
            is_for_validation=True,
        )
        for output_action_type in output_action_type_list
    }

    eligible_next_actions[
        ActionType.Name(ActionType.ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT)
    ] = ActionEdgeDefinition(
        output_fetch_config_by_name=ActionEdgeOutputFetchConfigByName(
            output_to_input_mapping={
                "content_group": ActionEdgeOutputFetchMethod(
                    input_name="assets",
                    fetch_method=ActionEdgeOutputListFetchMethod.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_FETCH_ALL,
                )
            }
        ),
        is_shown=False,
        is_for_validation=True,
    )

    for next_rep_action in ACTION_CATEGORY_TO_ACTION_TYPES[
        ActionCategory.ACTION_CATEGORY_REPURPOSE
    ]:
        eligible_next_actions[ActionType.Name(next_rep_action)] = ActionEdgeDefinition(
            output_fetch_config_by_name=ActionEdgeOutputFetchConfigByName(
                output_to_input_mapping={
                    "content_group": ActionEdgeOutputFetchMethod(
                        input_name="anchor_content",
                        fetch_method=ActionEdgeOutputListFetchMethod.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_FETCH_ALL,
                    )
                }
            ),
            is_shown=True,
            is_for_validation=False,
        )

    content_type_label = ContentTypeDetails[content_type]["label"]
    optional_inputs = {
        "template": ActionInputOutputDefinition(
            position=1,
            description="Repurpose template",
            data_type=TofuDataType.TOFU_DATA_TYPE_TEMPLATE,
            is_visible_to_user=True,
            is_saved_to_action=True,
            need_special_handling=True,
        ),
        "custom_instructions": ActionInputOutputDefinition(
            position=2,
            description="Custom instructions",
            data_type=TofuDataType.TOFU_DATA_TYPE_CUSTOM_INSTRUCTION,
            is_visible_to_user=True,
            is_saved_to_action=True,
            need_special_handling=True,
            is_array=True,
        ),
    }
    if has_word_count(content_type):
        optional_inputs["content_word_count"] = (
            ActionInputOutputDefinition(
                position=3,
                description="Content word count",
                data_type=TofuDataType.TOFU_DATA_TYPE_INT,
                is_visible_to_user=False,
                is_saved_to_action=True,
            ),
        )
    definition = ActionDefinition(
        action_category=ActionCategory.ACTION_CATEGORY_REPURPOSE,
        action_type=action_type,
        description=f"Repurpose content for {content_type_label}",
        required_inputs={
            "content_type": ActionInputOutputDefinition(
                position=1,
                description="Content type to repurpose",
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_TYPE,
                # TODO: we need to distinguish the first setup and modification
                is_visible_to_user=False,
                is_saved_to_action=True,
                need_special_handling=False,
            ),
            "anchor_content": ActionInputOutputDefinition(
                position=2,
                description="Anchor content",
                data_type=TofuDataType.TOFU_DATA_TYPE_ASSET,
                is_visible_to_user=True,
                is_saved_to_action=True,
                is_array=True,
            ),
            "num_outputs": ActionInputOutputDefinition(
                position=3,
                description="Number of outputs to generate",
                data_type=TofuDataType.TOFU_DATA_TYPE_INT,
                is_visible_to_user=True,
                is_saved_to_action=True,
                need_special_handling=True,
                default_value=TofuData(int_value=TofuInt(value=1)),
            ),
        },
        optional_inputs=optional_inputs,
        outputs={
            "content_group": ActionInputOutputDefinition(
                description="Repurposed content group",
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_GROUP,
                is_visible_to_user=True,
                is_array=True,
            )
        },
        eligible_next_actions=eligible_next_actions,
    )

    # Update predefined inputs to use the new structure
    definition.predefined_inputs.append(
        _create_predefined_value(
            "content_type", _convert_content_type_to_tofudata(content_type=content_type)
        )
    )

    return definition


def _create_personalize_definition(
    action_type: ActionType,
    content_type: ContentType,
) -> ActionDefinition:
    # Create a dictionary to store eligible next actions
    eligible_next_actions = {}

    output_action_type_list = []
    if content_type in [ContentType.EmailSDR, ContentType.EmailMarketing]:
        output_action_type_list = [
            ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL,
            ActionType.ACTION_TYPE_EXPORT_SALESFORCE_EMAIL,
            ActionType.ACTION_TYPE_EXPORT_MARKETO_EMAIL,
        ]
    elif content_type in [ContentType.LandingPage]:
        output_action_type_list = [
            ActionType.ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE,
            ActionType.ACTION_TYPE_EXPORT_MARKETO_LANDING_PAGE,
            ActionType.ACTION_TYPE_EXPORT_TOFU_LANDING_PAGE,
        ]

    # Create eligible next actions for each output action type
    for output_action_type in output_action_type_list:
        action_type_name = ActionType.Name(output_action_type)
        eligible_next_actions[action_type_name] = ActionEdgeDefinition(
            output_fetch_config_by_name=ActionEdgeOutputFetchConfigByName(
                output_to_input_mapping={
                    "content_group": ActionEdgeOutputFetchMethod(
                        input_name="content_group",
                        fetch_method=ActionEdgeOutputListFetchMethod.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_FETCH_ALL,
                    )
                }
            ),
            is_shown=True,
            is_for_validation=True,
        )

    content_type_label = ContentTypeDetails[content_type]["label"]
    definition = ActionDefinition(
        action_category=ActionCategory.ACTION_CATEGORY_PERSONALIZE,
        action_type=action_type,
        description=f"Personalize content for {content_type_label}",
        required_inputs={
            "content_type": ActionInputOutputDefinition(
                position=1,
                description="Content type to personalize",
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_TYPE,
                is_visible_to_user=False,
                is_saved_to_action=True,
                need_special_handling=False,
            ),
            "targets": ActionInputOutputDefinition(
                position=2,
                description="Targets to personalize for",
                data_type=TofuDataType.TOFU_DATA_TYPE_TARGET,
                is_visible_to_user=True,
                is_saved_to_action=False,
                need_special_handling=False,
                is_array=True,
            ),
            "template": ActionInputOutputDefinition(
                position=3,
                description="Template to personalize",
                data_type=TofuDataType.TOFU_DATA_TYPE_TEMPLATE,
                is_visible_to_user=True,
                is_saved_to_action=True,
                need_special_handling=True,
            ),
            "components": ActionInputOutputDefinition(
                position=4,
                description="Components to personalize",
                data_type=TofuDataType.TOFU_DATA_TYPE_COMPONENTS,
                is_visible_to_user=True,
                is_saved_to_action=True,
                need_special_handling=True,
            ),
        },
        optional_inputs={
            "custom_instructions": ActionInputOutputDefinition(
                description="Custom instructions",
                data_type=TofuDataType.TOFU_DATA_TYPE_CUSTOM_INSTRUCTION,
                is_visible_to_user=True,
                is_saved_to_action=True,
                need_special_handling=True,
                is_array=True,
            )
        },
        outputs={
            "content_group": ActionInputOutputDefinition(
                description="Personalized content group",
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_GROUP,
            )
        },
        eligible_next_actions=eligible_next_actions,
    )

    # Update predefined inputs to use the new structure
    definition.predefined_inputs.append(
        _create_predefined_value(
            "content_type", _convert_content_type_to_tofudata(content_type=content_type)
        )
    )

    return definition


def _create_export_definition(
    action_type: ActionType, platform_type: PlatformType
) -> ActionDefinition:
    required_inputs = {
        # TODO: think if we need this
        "content_type": ActionInputOutputDefinition(
            position=1,
            description="Content type to export",
            data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_TYPE,
            is_visible_to_user=False,
            is_saved_to_action=True,
            need_special_handling=False,
        ),
        "platform_type": ActionInputOutputDefinition(
            position=2,
            description="Platform type to export to",
            data_type=TofuDataType.TOFU_DATA_TYPE_PLATFORM_TYPE,
            is_visible_to_user=True,
            is_saved_to_action=True,
            need_special_handling=False,
        ),
        # TODO: what do we do here?
        "export_settings": ActionInputOutputDefinition(
            position=3,
            description="Export settings",
            data_type=TofuDataType.TOFU_DATA_TYPE_EXPORT_SETTING,
            is_visible_to_user=True,
            is_saved_to_action=True,
            need_special_handling=True,
        ),
        "content_group": ActionInputOutputDefinition(
            position=4,
            description="Content group to export",
            data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_GROUP,
            # is_visible_to_user=True,
            is_saved_to_action=True,
            need_special_handling=False,
        ),
    }
    if platform_type == PlatformType.PLATFORM_TYPE_TOFU:
        description = "Export to any landing page"
    else:
        description = f"Export content to {PlatformTypeDetails[platform_type]['label']}"
    definition = ActionDefinition(
        action_category=ActionCategory.ACTION_CATEGORY_EXPORT,
        action_type=action_type,
        description=description,
        required_inputs=required_inputs,
        outputs={
            # "export_response": ActionInputOutputDefinition(
            #     description="Export response",
            #     data_type=TofuDataType.TOFU_DATA_TYPE_EXPORT_RESPONSE,
            # ),
        },
    )

    # Update predefined inputs to use the new structure
    definition.predefined_inputs.append(
        _create_predefined_value(
            "platform_type", _convert_platform_type_to_tofudata(platform_type)
        )
    )

    return definition


def _create_seq_personalize_definition(
    action_type: ActionType,
    content_type: ContentType,
) -> ActionDefinition:
    # Create eligible next actions for personalization
    eligible_next_actions = {}
    for personalize_action_type in ACTION_CATEGORY_TO_ACTION_TYPES[
        ActionCategory.ACTION_CATEGORY_PERSONALIZE
    ]:
        action_type_name = ActionType.Name(personalize_action_type)
        eligible_next_actions[action_type_name] = ActionEdgeDefinition(
            output_fetch_config_by_name=ActionEdgeOutputFetchConfigByName(
                output_to_input_mapping={
                    "content_group": ActionEdgeOutputFetchMethod(
                        input_name="template",
                        fetch_method=ActionEdgeOutputListFetchMethod.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_EXPAND_ONE,
                    )
                }
            ),
            is_shown=True,
            is_for_validation=True,
        )

    content_type_label = ContentTypeDetails[content_type]["label"]
    definition = ActionDefinition(
        action_category=ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE,
        action_type=action_type,
        description=f"Sequence Personalize content for {content_type_label}",
        required_inputs={
            "content_type": ActionInputOutputDefinition(
                position=1,
                description="Content type to sequence personalize",
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_TYPE,
                is_visible_to_user=False,
                is_saved_to_action=True,
                need_special_handling=False,
            ),
            "num_outputs": ActionInputOutputDefinition(
                position=2,
                description=f"Choose number of {content_type_label} in sequence",
                data_type=TofuDataType.TOFU_DATA_TYPE_INT,
                is_visible_to_user=False,
                is_saved_to_action=True,
                need_special_handling=True,
                default_value=TofuData(int_value=TofuInt(value=1)),
            ),
            "targets": ActionInputOutputDefinition(
                position=3,
                description="Targets to personalize for",
                data_type=TofuDataType.TOFU_DATA_TYPE_TARGET,
                is_visible_to_user=True,
                is_saved_to_action=False,
                need_special_handling=False,
                is_array=True,
            ),
        },
        optional_inputs={
            "custom_instructions": ActionInputOutputDefinition(
                position=1,
                description="Custom instructions",
                data_type=TofuDataType.TOFU_DATA_TYPE_CUSTOM_INSTRUCTION,
                is_visible_to_user=True,
                is_saved_to_action=True,
                need_special_handling=True,
                is_array=True,
            )
        },
        outputs={
            "content_group": ActionInputOutputDefinition(
                description="Sequence personalized content group",
                data_type=TofuDataType.TOFU_DATA_TYPE_CONTENT_GROUP,
                is_array=True,
            )
        },
        eligible_next_actions=eligible_next_actions,
    )

    # Update predefined inputs to use the new structure
    definition.predefined_inputs.append(
        _create_predefined_value(
            "content_type", _convert_content_type_to_tofudata(content_type=content_type)
        )
    )

    return definition


# Create action definitions for each action type
ACTION_DEFINITIONS = {
    ActionType.ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT: UPLOAD_OR_SELECT_ANCHOR_CONTENT_DEFINITION,
    ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_EMAIL_SDR,
        ContentType.EmailSDR,
        [
            ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_SDR,
        ],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_EMAIL_MARKETING: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_EMAIL_MARKETING,
        ContentType.EmailMarketing,
        [ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_MARKETING],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN,
        ContentType.AdCampaignLinkedin,
        [ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_CAROUSEL: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_CAROUSEL,
        ContentType.AdCampaignLinkedinCarousel,
        [ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_CAROUSEL],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_DOCUMENT: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_DOCUMENT,
        ContentType.AdCampaignLinkedinDocument,
        [ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_DOCUMENT],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_META: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_META,
        ContentType.AdCampaignMeta,
        [ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_META],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GOOGLE: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GOOGLE,
        ContentType.AdCampaignGoogle,
        [ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GOOGLE],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_LANDING_PAGE: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_LANDING_PAGE,
        ContentType.LandingPage,
        [ActionType.ACTION_TYPE_PERSONALIZE_LANDING_PAGE],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_WHITEPAPER: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_WHITEPAPER,
        ContentType.Whitepaper,
        [ActionType.ACTION_TYPE_PERSONALIZE_WHITEPAPER],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_CASE_STUDY: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_CASE_STUDY,
        ContentType.CaseStudy,
        [],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_EBOOK: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_EBOOK,
        ContentType.EBook,
        [ActionType.ACTION_TYPE_PERSONALIZE_EBOOK],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GENERAL: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GENERAL,
        ContentType.AdCampaignGeneral,
        [ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GENERAL],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_GENERAL: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_GENERAL,
        ContentType.SocialGeneral,
        [ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_GENERAL],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_LINKEDIN: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_SOCIAL_LINKEDIN,
        ContentType.SocialLinkedin,
        [ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_LINKEDIN],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_MESSAGE_LINKEDIN: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_MESSAGE_LINKEDIN,
        ContentType.MessageLinkedin,
        [ActionType.ACTION_TYPE_PERSONALIZE_MESSAGE_LINKEDIN],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_SALES_DECK: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_SALES_DECK,
        ContentType.SalesDeck,
        [ActionType.ACTION_TYPE_PERSONALIZE_SALES_DECK],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_SLIDE_DECK: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_SLIDE_DECK,
        ContentType.SlideDeck,
    ),
    ActionType.ACTION_TYPE_REPURPOSE_WEBINAR: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_WEBINAR,
        ContentType.Webinar,
    ),
    ActionType.ACTION_TYPE_REPURPOSE_BLOG_POST: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_BLOG_POST,
        ContentType.BlogPost,
        [ActionType.ACTION_TYPE_PERSONALIZE_BLOG_POST],
    ),
    ActionType.ACTION_TYPE_REPURPOSE_QUOTES_HIGHLIGHTS: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_QUOTES_HIGHLIGHTS,
        ContentType.QuotesHighlights,
    ),
    ActionType.ACTION_TYPE_REPURPOSE_STATISTICS: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_STATISTICS,
        ContentType.Statistics,
    ),
    ActionType.ACTION_TYPE_REPURPOSE_OTHER: _create_repurpose_definition(
        ActionType.ACTION_TYPE_REPURPOSE_OTHER,
        ContentType.Other,
        [ActionType.ACTION_TYPE_PERSONALIZE_OTHER],
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_SDR: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_SDR,
        ContentType.EmailSDR,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_MARKETING: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_EMAIL_MARKETING, ContentType.EmailMarketing
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN,
        ContentType.AdCampaignLinkedin,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_META: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_META,
        ContentType.AdCampaignMeta,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GOOGLE: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GOOGLE,
        ContentType.AdCampaignGoogle,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_CAROUSEL: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_CAROUSEL,
        ContentType.AdCampaignLinkedinCarousel,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_DOCUMENT: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_DOCUMENT,
        ContentType.AdCampaignLinkedinDocument,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_LANDING_PAGE: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_LANDING_PAGE, ContentType.LandingPage
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_WHITEPAPER: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_WHITEPAPER, ContentType.Whitepaper
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_EBOOK: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_EBOOK, ContentType.EBook
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_SALES_DECK: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_SALES_DECK, ContentType.SalesDeck
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GENERAL: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GENERAL,
        ContentType.AdCampaignGeneral,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_MESSAGE_LINKEDIN: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_MESSAGE_LINKEDIN,
        ContentType.MessageLinkedin,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_GENERAL: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_GENERAL,
        ContentType.SocialGeneral,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_LINKEDIN: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_SOCIAL_LINKEDIN,
        ContentType.SocialLinkedin,
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_BLOG_POST: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_BLOG_POST, ContentType.BlogPost
    ),
    ActionType.ACTION_TYPE_PERSONALIZE_OTHER: _create_personalize_definition(
        ActionType.ACTION_TYPE_PERSONALIZE_OTHER, ContentType.Other
    ),
    ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL: _create_export_definition(
        ActionType.ACTION_TYPE_EXPORT_HUBSPOT_EMAIL, PlatformType.PLATFORM_TYPE_HUBSPOT
    ),
    ActionType.ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE: _create_export_definition(
        ActionType.ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE,
        PlatformType.PLATFORM_TYPE_HUBSPOT,
    ),
    ActionType.ACTION_TYPE_EXPORT_SALESFORCE_EMAIL: _create_export_definition(
        ActionType.ACTION_TYPE_EXPORT_SALESFORCE_EMAIL,
        PlatformType.PLATFORM_TYPE_SALESFORCE,
    ),
    ActionType.ACTION_TYPE_EXPORT_MARKETO_EMAIL: _create_export_definition(
        ActionType.ACTION_TYPE_EXPORT_MARKETO_EMAIL, PlatformType.PLATFORM_TYPE_MARKETO
    ),
    ActionType.ACTION_TYPE_EXPORT_MARKETO_LANDING_PAGE: _create_export_definition(
        ActionType.ACTION_TYPE_EXPORT_MARKETO_LANDING_PAGE,
        PlatformType.PLATFORM_TYPE_MARKETO,
    ),
    ActionType.ACTION_TYPE_EXPORT_TOFU_LANDING_PAGE: _create_export_definition(
        ActionType.ACTION_TYPE_EXPORT_TOFU_LANDING_PAGE,
        PlatformType.PLATFORM_TYPE_TOFU,
    ),
    ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_SDR: _create_seq_personalize_definition(
        ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_SDR,
        ContentType.EmailSDR,
    ),
    ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_MARKETING: _create_seq_personalize_definition(
        ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_MARKETING,
        ContentType.EmailMarketing,
    ),
    ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_LINKEDIN_MESSAGE: _create_seq_personalize_definition(
        ActionType.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_LINKEDIN_MESSAGE,
        ContentType.MessageLinkedin,
    ),
}

for action_type, definition in ACTION_DEFINITIONS.items():
    for (
        str_eligible_next_action_type,
        eligible_next_action,
    ) in definition.eligible_next_actions.items():
        eligible_next_action_type = ActionType.Value(str_eligible_next_action_type)
        # Set action category
        ACTION_DEFINITIONS[action_type].eligible_next_actions[
            str_eligible_next_action_type
        ].action_category = ACTION_DEFINITIONS[
            eligible_next_action_type
        ].action_category

        # Clear and copy predefined inputs using the repeated field for edge definitions
        del eligible_next_action.predefined_inputs[:]  # Use del to clear repeated field
        for predefined_input in ACTION_DEFINITIONS[
            eligible_next_action_type
        ].predefined_inputs:
            new_predefined_input = eligible_next_action.predefined_inputs.add()
            new_predefined_input.CopyFrom(predefined_input)


def is_eligible_for_default_action(action_type, definition):
    if (
        definition.action_category == ActionCategory.ACTION_CATEGORY_USER_INPUT
        or definition.action_category == ActionCategory.ACTION_CATEGORY_EXPORT
    ):
        return False
    return True


def generate_action_system_config():
    # Create the ActionSystemConfig instance
    action_system_config = ActionSystemConfig()
    # Add action definitions to the map field
    for action_type, definition in ACTION_DEFINITIONS.items():
        action_system_config.action_definitions[ActionType.Name(action_type)].CopyFrom(
            definition
        )

    # Add action category to action types mapping
    for category, action_types in ACTION_CATEGORY_TO_ACTION_TYPES.items():
        string_list = StringList()
        for action_type in action_types:
            string_list.values.append(ActionType.Name(action_type))
        action_system_config.action_category_to_action_types[
            ActionCategory.Name(category)
        ].CopyFrom(string_list)

    for action_type, definition in ACTION_DEFINITIONS.items():
        if not is_eligible_for_default_action(
            action_type=action_type, definition=definition
        ):
            continue

        # Create edge definition with proper initialization
        edge_definition = ActionEdgeDefinition(
            action_category=definition.action_category,
            is_shown=True,
            is_for_validation=True,
        )

        # Copy predefined inputs
        del edge_definition.predefined_inputs[:]  # Use del to clear repeated field
        for predefined_input in definition.predefined_inputs:
            new_predefined_input = edge_definition.predefined_inputs.add()
            new_predefined_input.CopyFrom(predefined_input)

        action_system_config.all_eligible_actions[
            ActionType.Name(action_type)
        ].CopyFrom(edge_definition)

    return action_system_config


ACTION_SYSTEM_CONFIG = generate_action_system_config()
