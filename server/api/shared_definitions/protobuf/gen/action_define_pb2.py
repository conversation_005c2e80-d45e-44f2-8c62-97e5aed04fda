# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: action_define.proto
# Protobuf Python Version: 5.28.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    28,
    1,
    '',
    'action_define.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13\x61\x63tion_define.proto\x12\x16tofu.definition.action\"\x18\n\x07TofuInt\x12\r\n\x05value\x18\x01 \x01(\x03\"\x1b\n\nTofuString\x12\r\n\x05value\x18\x01 \x01(\t\"\x19\n\x08TofuBool\x12\r\n\x05value\x18\x01 \x01(\x08\"B\n\tTofuAsset\x12\x12\n\x08\x61sset_id\x18\x01 \x01(\x03H\x00\x12\x18\n\x0e\x61sset_group_id\x18\x02 \x01(\x03H\x00\x42\x07\n\x05\x61sset\"\xd5\x03\n\x11TofuTemplateField\x12J\n\x17template_component_type\x18\x01 \x01(\x0e\x32).tofu.definition.action.TofuComponentType\x12\x1b\n\x0e\x63ontent_source\x18\x02 \x01(\tH\x00\x88\x01\x01\x12 \n\x13\x63ontent_source_copy\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\"\n\x15\x63ontent_source_format\x18\x04 \x01(\tH\x02\x88\x01\x01\x12)\n\x1c\x63ontent_source_upload_method\x18\x05 \x01(\tH\x03\x88\x01\x01\x12!\n\x14slate_content_source\x18\x06 \x01(\tH\x04\x88\x01\x01\x12&\n\x19slate_content_source_copy\x18\x07 \x01(\tH\x05\x88\x01\x01\x42\x11\n\x0f_content_sourceB\x16\n\x14_content_source_copyB\x18\n\x16_content_source_formatB\x1f\n\x1d_content_source_upload_methodB\x17\n\x15_slate_content_sourceB\x1c\n\x1a_slate_content_source_copy\"\xe3\x01\n\x11TofuToneReference\x12\x39\n\x0btone_assets\x18\x01 \x01(\x0b\x32$.tofu.definition.action.TofuDataList\x12M\n\rtone_analysis\x18\x02 \x01(\x0b\x32\x36.tofu.definition.action.TofuToneReference.ToneAnalysis\x1a\x44\n\x0cToneAnalysis\x12\x15\n\rtone_analysis\x18\x01 \x01(\t\x12\x1d\n\x15tone_analysis_summary\x18\x02 \x01(\t\"\x8e\x04\n\x0cTofuTemplate\x12Q\n\x0ftemplate_fields\x18\x01 \x03(\x0b\x32\x38.tofu.definition.action.TofuTemplate.TemplateFieldsEntry\x12<\n\x0etone_reference\x18\x02 \x01(\x0b\x32$.tofu.definition.action.TofuDataList\x12\x44\n\x11tone_reference_v2\x18\x03 \x01(\x0b\x32).tofu.definition.action.TofuToneReference\x12S\n\x1ctemplate_custom_instructions\x18\x04 \x03(\x0b\x32-.tofu.definition.action.TofuCustomInstruction\x12\x1c\n\x14\x66ollow_template_tone\x18\x05 \x01(\x08\x12\x1e\n\x16\x66ollow_template_length\x18\x06 \x01(\x08\x12\x32\n*follow_template_core_message_and_key_point\x18\x07 \x01(\x08\x1a`\n\x13TemplateFieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x38\n\x05value\x18\x02 \x01(\x0b\x32).tofu.definition.action.TofuTemplateField:\x02\x38\x01\",\n\x10TofuContentGroup\x12\x18\n\x10\x63ontent_group_id\x18\x01 \x01(\x03\"m\n)TofuComponentContextDataWithFormatForHtml\x12\x10\n\x08html_tag\x18\x01 \x01(\t\x12\x1b\n\x0ehtml_tag_index\x18\x02 \x01(\x05H\x00\x88\x01\x01\x42\x11\n\x0f_html_tag_index\"\xfb\x02\n(TofuComponentContextDataWithFormatForPdf\x12\x10\n\x08page_num\x18\x01 \x01(\x05\x12\x11\n\tnum_lines\x18\x02 \x01(\x05\x12\x62\n\x0c\x62ounding_box\x18\x03 \x01(\x0b\x32L.tofu.definition.action.TofuComponentContextDataWithFormatForPdf.BoundingBox\x12\x16\n\x0e\x61vg_char_width\x18\x04 \x01(\x02\x12\x16\n\x0e\x61vg_line_space\x18\x05 \x01(\x02\x12\x15\n\rchar_capacity\x18\x06 \x01(\x05\x12\x17\n\x0f\x61vg_char_height\x18\x07 \x01(\x02\x1a\x66\n\x0b\x42oundingBox\x12\x0b\n\x03top\x18\x01 \x01(\x02\x12\x0c\n\x04left\x18\x02 \x01(\x02\x12\r\n\x05right\x18\x03 \x01(\x02\x12\x0e\n\x06\x62ottom\x18\x04 \x01(\x02\x12\r\n\x05width\x18\x05 \x01(\x02\x12\x0e\n\x06height\x18\x06 \x01(\x02\"\xab\x02\n\x18TofuComponentContextData\x12\x19\n\x11preceding_element\x18\x01 \x01(\t\x12\x1a\n\x12succeeding_element\x18\x02 \x01(\t\x12\x18\n\x10selected_element\x18\x03 \x01(\t\x12Q\n\x04html\x18\x04 \x01(\x0b\x32\x41.tofu.definition.action.TofuComponentContextDataWithFormatForHtmlH\x00\x12O\n\x03pdf\x18\x05 \x01(\x0b\<EMAIL>\x00\x42\x1a\n\x18\x63ontext_data_with_format\"%\n\x15TofuComponentDataText\x12\x0c\n\x04text\x18\x01 \x01(\t\"\x9f\x01\n\x15TofuComponentDataHtml\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x0c\n\x04href\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\r\n\x05label\x18\x05 \x01(\t\x12\x1d\n\x10\x63ontent_group_id\x18\x06 \x01(\x03H\x00\x88\x01\x01\x12\x0b\n\x03url\x18\x07 \x01(\tB\x13\n\x11_content_group_id\"\xed\x01\n\x16TofuComponentDataImage\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\x12\x0b\n\x03url\x18\x03 \x01(\t\x12\x0b\n\x03\x61lt\x18\x04 \x01(\t\x12\x0e\n\x06srcset\x18\x05 \x01(\t\x12\x0f\n\x07loading\x18\x06 \x01(\t\x12J\n\x06styles\x18\x07 \x03(\x0b\x32:.tofu.definition.action.TofuComponentDataImage.StylesEntry\x1a-\n\x0bStylesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x85\x01\n\x15TofuComponentDataLink\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x0c\n\x04href\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\r\n\x05label\x18\x05 \x01(\t\x12\x18\n\x10\x63ontent_group_id\x18\x06 \x01(\x03\x12\x0b\n\x03url\x18\x07 \x01(\t\"4\n\x16TofuComponentDataVideo\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\"5\n\x17TofuComponentDataAnchor\x12\x0c\n\x04href\x18\x01 \x01(\t\x12\x0c\n\x04text\x18\x02 \x01(\t\"\xfb\x05\n\rTofuComponent\x12\x14\n\x0c\x63omponent_id\x18\x01 \x01(\t\x12\x12\n\ntime_added\x18\x02 \x01(\x03\x12\x41\n\x0e\x63omponent_type\x18\x03 \x01(\x0e\x32).tofu.definition.action.TofuComponentType\x12J\n\x13\x63omponent_meta_type\x18\x04 \x01(\x0e\x32-.tofu.definition.action.TofuComponentMetaType\x12K\n\x1d\x63omponent_custom_instructions\x18\x05 \x01(\x0b\x32$.tofu.definition.action.TofuDataList\x12P\n\x16\x63omponent_context_data\x18\x06 \x01(\x0b\x32\x30.tofu.definition.action.TofuComponentContextData\x12=\n\x04text\x18\x07 \x01(\x0b\x32-.tofu.definition.action.TofuComponentDataTextH\x00\x12=\n\x04html\x18\x08 \x01(\x0b\x32-.tofu.definition.action.TofuComponentDataHtmlH\x00\x12?\n\x05image\x18\t \x01(\x0b\x32..tofu.definition.action.TofuComponentDataImageH\x00\x12=\n\x04link\x18\n \x01(\x0b\x32-.tofu.definition.action.TofuComponentDataLinkH\x00\x12?\n\x05video\x18\x0b \x01(\x0b\x32..tofu.definition.action.TofuComponentDataVideoH\x00\x12\x41\n\x06\x61nchor\x18\x0c \x01(\x0b\x32/.tofu.definition.action.TofuComponentDataAnchorH\x00\x42\x10\n\x0e\x63omponent_data\"\xb6\x01\n\x0eTofuComponents\x12J\n\ncomponents\x18\x01 \x03(\x0b\x32\x36.tofu.definition.action.TofuComponents.ComponentsEntry\x1aX\n\x0f\x43omponentsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x34\n\x05value\x18\x02 \x01(\x0b\x32%.tofu.definition.action.TofuComponent:\x02\x38\x01\"b\n\x15TofuCustomInstruction\x12\x34\n\x06\x61ssets\x18\x01 \x01(\x0b\x32$.tofu.definition.action.TofuDataList\x12\x13\n\x0binstruction\x18\x02 \x01(\t\"\'\n\x0fTofuContentType\x12\x14\n\x0c\x63ontent_type\x18\x01 \x01(\t\"O\n\x10TofuPlatformType\x12;\n\rplatform_type\x18\x01 \x01(\x0e\x32$.tofu.definition.action.PlatformType\"\x1f\n\nTofuTarget\x12\x11\n\ttarget_id\x18\x01 \x01(\x03\"\xf9\x05\n\x08TofuData\x12\x34\n\tint_value\x18\x01 \x01(\x0b\x32\x1f.tofu.definition.action.TofuIntH\x00\x12:\n\x0cstring_value\x18\x02 \x01(\x0b\x32\".tofu.definition.action.TofuStringH\x00\x12\x36\n\nbool_value\x18\x03 \x01(\x0b\x32 .tofu.definition.action.TofuBoolH\x00\x12?\n\x0c\x63ontent_type\x18\x04 \x01(\x0b\x32\'.tofu.definition.action.TofuContentTypeH\x00\x12\x41\n\rplatform_type\x18\x05 \x01(\x0b\x32(.tofu.definition.action.TofuPlatformTypeH\x00\x12\x41\n\rcontent_group\x18\x06 \x01(\x0b\x32(.tofu.definition.action.TofuContentGroupH\x00\x12\x32\n\x05\x61sset\x18\x07 \x01(\x0b\x32!.tofu.definition.action.TofuAssetH\x00\x12\x34\n\x06target\x18\x08 \x01(\x0b\x32\".tofu.definition.action.TofuTargetH\x00\x12<\n\ncomponents\x18\t \x01(\x0b\x32&.tofu.definition.action.TofuComponentsH\x00\x12\x38\n\x08template\x18\n \x01(\x0b\x32$.tofu.definition.action.TofuTemplateH\x00\x12K\n\x12\x63ustom_instruction\x18\x0b \x01(\x0b\x32-.tofu.definition.action.TofuCustomInstructionH\x00\x12\x45\n\x0f\x65xport_settings\x18\x0c \x01(\x0b\x32*.tofu.definition.action.TofuExportSettingsH\x00\x42\x06\n\x04\x64\x61ta\">\n\x0cTofuDataList\x12.\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32 .tofu.definition.action.TofuData\"\xd7\x02\n\x1b\x41\x63tionInputOutputDefinition\x12\x15\n\x08position\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\r\n\x05label\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x37\n\tdata_type\x18\x04 \x01(\x0e\x32$.tofu.definition.action.TofuDataType\x12\x1a\n\x12is_visible_to_user\x18\x05 \x01(\x08\x12\x1a\n\x12is_saved_to_action\x18\x06 \x01(\x08\x12\x1d\n\x15need_special_handling\x18\x07 \x01(\x08\x12<\n\rdefault_value\x18\x08 \x01(\x0b\x32 .tofu.definition.action.TofuDataH\x01\x88\x01\x01\x12\x10\n\x08is_array\x18\t \x01(\x08\x42\x0b\n\t_positionB\x10\n\x0e_default_value\"\x80\x01\n\x1b\x41\x63tionEdgeOutputFetchMethod\x12\x12\n\ninput_name\x18\x01 \x01(\t\x12M\n\x0c\x66\x65tch_method\x18\x02 \x01(\x0e\x32\x37.tofu.definition.action.ActionEdgeOutputListFetchMethod\"\x8b\x02\n!ActionEdgeOutputFetchConfigByName\x12t\n\x17output_to_input_mapping\x18\x01 \x03(\x0b\x32S.tofu.definition.action.ActionEdgeOutputFetchConfigByName.OutputToInputMappingEntry\x1ap\n\x19OutputToInputMappingEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x42\n\x05value\x18\x02 \x01(\x0b\x32\x33.tofu.definition.action.ActionEdgeOutputFetchMethod:\x02\x38\x01\"\xc1\x02\n\x14\x41\x63tionEdgeDefinition\x12?\n\x0f\x61\x63tion_category\x18\x01 \x01(\x0e\x32&.tofu.definition.action.ActionCategory\x12\x42\n\x11predefined_inputs\x18\x02 \x03(\x0b\x32\'.tofu.definition.action.PredefinedValue\x12`\n\x1boutput_fetch_config_by_name\x18\x03 \x01(\x0b\x32\x39.tofu.definition.action.ActionEdgeOutputFetchConfigByNameH\x00\x12\x10\n\x08is_shown\x18\x04 \x01(\x08\x12\x19\n\x11is_for_validation\x18\x05 \x01(\x08\x42\x15\n\x13output_fetch_config\"Z\n\x0fPredefinedValue\x12\x12\n\nvalue_type\x18\x01 \x01(\t\x12\x33\n\x05value\x18\x02 \x01(\x0b\x32$.tofu.definition.action.TofuDataList\"\xf3\x07\n\x10\x41\x63tionDefinition\x12?\n\x0f\x61\x63tion_category\x18\x01 \x01(\x0e\x32&.tofu.definition.action.ActionCategory\x12\x37\n\x0b\x61\x63tion_type\x18\x02 \x01(\x0e\x32\".tofu.definition.action.ActionType\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\r\n\x05label\x18\x04 \x01(\t\x12U\n\x0frequired_inputs\x18\x05 \x03(\x0b\x32<.tofu.definition.action.ActionDefinition.RequiredInputsEntry\x12U\n\x0foptional_inputs\x18\x06 \x03(\x0b\x32<.tofu.definition.action.ActionDefinition.OptionalInputsEntry\x12\x46\n\x07outputs\x18\x07 \x03(\x0b\x32\x35.tofu.definition.action.ActionDefinition.OutputsEntry\x12\x42\n\x11predefined_inputs\x18\x08 \x03(\x0b\x32\'.tofu.definition.action.PredefinedValue\x12`\n\x15\x65ligible_next_actions\x18\t \x03(\x0b\x32\x41.tofu.definition.action.ActionDefinition.EligibleNextActionsEntry\x1aj\n\x13RequiredInputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x42\n\x05value\x18\x02 \x01(\x0b\x32\x33.tofu.definition.action.ActionInputOutputDefinition:\x02\x38\x01\x1aj\n\x13OptionalInputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x42\n\x05value\x18\x02 \x01(\x0b\x32\x33.tofu.definition.action.ActionInputOutputDefinition:\x02\x38\x01\x1a\x63\n\x0cOutputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x42\n\x05value\x18\x02 \x01(\x0b\x32\x33.tofu.definition.action.ActionInputOutputDefinition:\x02\x38\x01\x1ah\n\x18\x45ligibleNextActionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12;\n\x05value\x18\x02 \x01(\x0b\x32,.tofu.definition.action.ActionEdgeDefinition:\x02\x38\x01\"\x1c\n\nStringList\x12\x0e\n\x06values\x18\x01 \x03(\t\"\x80\x05\n\x12\x41\x63tionSystemConfig\x12]\n\x12\x61\x63tion_definitions\x18\x01 \x03(\x0b\x32\x41.tofu.definition.action.ActionSystemConfig.ActionDefinitionsEntry\x12t\n\x1f\x61\x63tion_category_to_action_types\x18\x02 \x03(\x0b\x32K.tofu.definition.action.ActionSystemConfig.ActionCategoryToActionTypesEntry\x12`\n\x14\x61ll_eligible_actions\x18\t \x03(\x0b\x32\x42.tofu.definition.action.ActionSystemConfig.AllEligibleActionsEntry\x1a\x62\n\x16\x41\x63tionDefinitionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x37\n\x05value\x18\x02 \x01(\x0b\x32(.tofu.definition.action.ActionDefinition:\x02\x38\x01\x1a\x66\n ActionCategoryToActionTypesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".tofu.definition.action.StringList:\x02\x38\x01\x1ag\n\x17\x41llEligibleActionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12;\n\x05value\x18\x02 \x01(\x0b\x32,.tofu.definition.action.ActionEdgeDefinition:\x02\x38\x01\"\x98\x01\n\x10GenerationStatus\x12\x41\n\x0bstatus_type\x18\x01 \x01(\x0e\x32,.tofu.definition.action.GenerationStatusType\x12\x13\n\x0btarget_name\x18\x02 \x01(\t\x12\x1a\n\rerror_message\x18\x03 \x01(\tH\x00\x88\x01\x01\x42\x10\n\x0e_error_message\"\xdd\x01\n!ActionStatusDetailsGenPersonalize\x12\\\n\ngen_status\x18\x01 \x03(\x0b\x32H.tofu.definition.action.ActionStatusDetailsGenPersonalize.GenStatusEntry\x1aZ\n\x0eGenStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x37\n\x05value\x18\x02 \x01(\x0b\x32(.tofu.definition.action.GenerationStatus:\x02\x38\x01\"\xf9\x01\n\x1c\x43ontentGroupGenerationStatus\x12q\n\x18\x63ontent_group_gen_status\x18\x01 \x03(\x0b\x32O.tofu.definition.action.ContentGroupGenerationStatus.ContentGroupGenStatusEntry\x1a\x66\n\x1a\x43ontentGroupGenStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x37\n\x05value\x18\x02 \x01(\x0b\x32(.tofu.definition.action.GenerationStatus:\x02\x38\x01\"\xe5\x01\n\x1f\x41\x63tionStatusDetailsGenRepurpose\x12Z\n\ngen_status\x18\x01 \x03(\x0b\x32\x46.tofu.definition.action.ActionStatusDetailsGenRepurpose.GenStatusEntry\x1a\x66\n\x0eGenStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x43\n\x05value\x18\x02 \x01(\x0b\x32\x34.tofu.definition.action.ContentGroupGenerationStatus:\x02\x38\x01\"\xff\x01\n,ActionStatusDetailsGenSeqPersonalizeTemplate\x12g\n\ngen_status\x18\x01 \x03(\x0b\x32S.tofu.definition.action.ActionStatusDetailsGenSeqPersonalizeTemplate.GenStatusEntry\x1a\x66\n\x0eGenStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x43\n\x05value\x18\x02 \x01(\x0b\x32\x34.tofu.definition.action.ContentGroupGenerationStatus:\x02\x38\x01\"\xa5\x01\n\x1d\x41\x63tionStatusDetailsInputCheck\x12\x1f\n\x17missing_required_inputs\x18\x01 \x03(\t\x12\x1f\n\x17missing_optional_inputs\x18\x02 \x03(\t\x12 \n\x18provided_required_inputs\x18\x03 \x03(\t\x12 \n\x18provided_optional_inputs\x18\x04 \x03(\t\"\xd6\x01\n\x19MarketoObjectExportStatus\x12\x14\n\x0c\x65xported_ids\x18\x01 \x03(\x05\x12h\n\x15\x66\x61iled_ids_with_error\x18\x02 \x03(\x0b\x32I.tofu.definition.action.MarketoObjectExportStatus.FailedIdsWithErrorEntry\x1a\x39\n\x17\x46\x61iledIdsWithErrorEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xa4\x02\n\x1a\x45xportResponseTargetStatus\x12?\n\rexport_status\x18\x01 \x01(\x0e\x32(.tofu.definition.action.ExportStatusType\x12\x16\n\x0elast_export_at\x18\x02 \x01(\x03\x12\x43\n\x11url_export_status\x18\x03 \x01(\x0e\x32(.tofu.definition.action.ExportStatusType\x12\x0f\n\x07message\x18\x04 \x01(\t\x12W\n\x1cmarketo_object_export_status\x18\x05 \x01(\x0b\x32\x31.tofu.definition.action.MarketoObjectExportStatus\"\xf5\x01\n\x19\x41\x63tionStatusDetailsExport\x12\x13\n\x0bresponse_id\x18\x01 \x01(\x03\x12Z\n\rtarget_status\x18\x02 \x03(\x0b\x32\x43.tofu.definition.action.ActionStatusDetailsExport.TargetStatusEntry\x1ag\n\x11TargetStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x41\n\x05value\x18\x02 \x01(\x0b\x32\x32.tofu.definition.action.ExportResponseTargetStatus:\x02\x38\x01\"\x83\x02\n!ActionStatusDetailsAnchorPrecheck\x12s\n\x16\x61sset_precheck_results\x18\x01 \x03(\x0b\x32S.tofu.definition.action.ActionStatusDetailsAnchorPrecheck.AssetPrecheckResultsEntry\x1ai\n\x19\x41ssetPrecheckResultsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12;\n\x05value\x18\x02 \x01(\x0b\x32,.tofu.definition.action.AnchorPrecheckResult:\x02\x38\x01\"\xcc\x02\n\x14\x41nchorPrecheckResult\x12@\n\x05label\x18\x01 \x01(\x0e\x32\x31.tofu.definition.action.AnchorPrecheckResultLabel\x12\x0f\n\x07message\x18\x02 \x01(\t\x12o\n\x1b\x61nchor_precheck_doc_results\x18\x03 \x03(\x0b\x32J.tofu.definition.action.AnchorPrecheckResult.AnchorPrecheckDocResultsEntry\x1ap\n\x1d\x41nchorPrecheckDocResultsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12>\n\x05value\x18\x02 \x01(\x0b\x32/.tofu.definition.action.AnchorPrecheckDocResult:\x02\x38\x01\"l\n\x17\x41nchorPrecheckDocResult\x12@\n\x05label\x18\x01 \x01(\x0e\x32\x31.tofu.definition.action.AnchorPrecheckResultLabel\x12\x0f\n\x07message\x18\x02 \x01(\t\"i\n\x11\x41\x63tionStatusStats\x12\x18\n\x10\x63nts_not_started\x18\x01 \x01(\x05\x12\x11\n\tcnts_succ\x18\x02 \x01(\x05\x12\x11\n\tcnts_fail\x18\x03 \x01(\x05\x12\x14\n\x0c\x63nts_running\x18\x04 \x01(\x05\"\x80\x05\n\x13\x41\x63tionStatusDetails\x12\x38\n\x05stats\x18\x01 \x01(\x0b\x32).tofu.definition.action.ActionStatusStats\x12J\n\x0binput_check\x18\x02 \x01(\x0b\x32\x35.tofu.definition.action.ActionStatusDetailsInputCheck\x12\\\n\x17gen_personalize_details\x18\x03 \x01(\x0b\x32\x39.tofu.definition.action.ActionStatusDetailsGenPersonalizeH\x00\x12X\n\x15gen_repurpose_details\x18\x04 \x01(\x0b\x32\x37.tofu.definition.action.ActionStatusDetailsGenRepurposeH\x00\x12t\n$gen_seq_personalize_template_details\x18\x05 \x01(\x0b\x32\x44.tofu.definition.action.ActionStatusDetailsGenSeqPersonalizeTemplateH\x00\x12K\n\x0e\x65xport_details\x18\x06 \x01(\x0b\x32\x31.tofu.definition.action.ActionStatusDetailsExportH\x00\x12\\\n\x17\x61nchor_precheck_details\x18\x07 \x01(\x0b\x32\x39.tofu.definition.action.ActionStatusDetailsAnchorPrecheckH\x00\x42\n\n\x08\x64\x65tailed\"\x9c\x01\n\x0c\x41\x63tionStatus\x12=\n\x0bstatus_type\x18\x01 \x01(\x0e\x32(.tofu.definition.action.ActionStatusType\x12\x0f\n\x07message\x18\x02 \x01(\t\x12<\n\x07\x64\x65tails\x18\x03 \x01(\x0b\x32+.tofu.definition.action.ActionStatusDetails\"\xa5\x01\n\x0c\x41\x63tionInputs\x12@\n\x06inputs\x18\x01 \x03(\x0b\x32\x30.tofu.definition.action.ActionInputs.InputsEntry\x1aS\n\x0bInputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x33\n\x05value\x18\x02 \x01(\x0b\x32$.tofu.definition.action.TofuDataList:\x02\x38\x01\"\xaa\x01\n\rActionOutputs\x12\x43\n\x07outputs\x18\x01 \x03(\x0b\x32\x32.tofu.definition.action.ActionOutputs.OutputsEntry\x1aT\n\x0cOutputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x33\n\x05value\x18\x02 \x01(\x0b\x32$.tofu.definition.action.TofuDataList:\x02\x38\x01\"5\n\x0e\x41\x63tionMetaData\x12#\n\x1b\x61pplied_content_template_id\x18\x01 \x01(\x03\"r\n\x10\x41\x63tionEdgeConfig\x12(\n output_name_from_incoming_action\x18\x01 \x01(\t\x12%\n\x1dinput_name_to_outgoing_action\x18\x02 \x01(\t\x12\r\n\x05index\x18\x03 \x03(\x03\"\x91\x01\n\x0e\x43\x61mpaignConfig\x12\x33\n\x07targets\x18\x01 \x03(\x0b\x32\".tofu.definition.action.TofuTarget\x12J\n\x13\x63ustom_instructions\x18\x02 \x03(\x0b\x32-.tofu.definition.action.TofuCustomInstruction\"\\\n\x15PersonalizationParams\x12\x14\n\x0c\x63ontinue_gen\x18\x01 \x01(\x08\x12\x18\n\x10joint_generation\x18\x02 \x01(\x08\x12\x13\n\x0b\x63ontent_ids\x18\x03 \x03(\x03\"\x11\n\x0fRepurposeParams\"\x1e\n\x1cSeqPersonalizeTemplateParams\"Y\n\x11\x45xportEmailParams\x12\x14\n\x0chtml_content\x18\x01 \x01(\t\x12\x17\n\x0f\x65mail_file_name\x18\x02 \x01(\t\x12\x15\n\remail_subject\x18\x03 \x01(\t\"r\n\x0c\x45xportParams\x12\x15\n\ris_new_export\x18\x01 \x01(\x08\x12\x41\n\x0c\x65mail_params\x18\x02 \x01(\x0b\x32).tofu.definition.action.ExportEmailParamsH\x00\x42\x08\n\x06params\"\xd7\x02\n\x15\x41\x63tionExecutionParams\x12O\n\x16personalization_params\x18\x01 \x01(\x0b\x32-.tofu.definition.action.PersonalizationParamsH\x00\x12\x43\n\x10repurpose_params\x18\x02 \x01(\x0b\x32\'.tofu.definition.action.RepurposeParamsH\x00\x12_\n\x1fseq_personalize_template_params\x18\x03 \x01(\x0b\x32\x34.tofu.definition.action.SeqPersonalizeTemplateParamsH\x00\x12=\n\rexport_params\x18\x04 \x01(\x0b\x32$.tofu.definition.action.ExportParamsH\x00\x42\x08\n\x06params\"\xf8\x01\n\x12TofuExportSettings\x12;\n\rplatform_type\x18\x01 \x01(\x0e\x32$.tofu.definition.action.PlatformType\x12;\n\x05\x65mail\x18\x02 \x01(\x0b\x32*.tofu.definition.action.ExportEmailSettingH\x00\x12\x39\n\x04page\x18\x03 \x01(\x0b\x32).tofu.definition.action.ExportPageSettingH\x00\x12\x19\n\x11is_shadow_testing\x18\x04 \x01(\x08\x42\x12\n\x10\x63ontent_category\"\xfb\x03\n\x12\x45xportEmailSetting\x12<\n\x0b\x65xport_type\x18\x01 \x01(\x0e\x32\'.tofu.definition.action.ExportEmailType\x12W\n\x0ftargets_setting\x18\x02 \x03(\x0b\x32>.tofu.definition.action.ExportEmailSetting.TargetsSettingEntry\x12]\n\x12\x63omponents_setting\x18\x03 \x03(\x0b\x32\x41.tofu.definition.action.ExportEmailSetting.ComponentsSettingEntry\x12L\n\x10\x61\x64vanced_setting\x18\x04 \x01(\x0b\x32\x32.tofu.definition.action.ExportEmailAdvancedSetting\x1ag\n\x13TargetsSettingEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12?\n\x05value\x18\x02 \x01(\x0b\x32\x30.tofu.definition.action.ExportEmailTargetSetting:\x02\x38\x01\x1a\x38\n\x16\x43omponentsSettingEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"[\n\x18\x45xportEmailTargetSetting\x12\x11\n\tdraft_URL\x18\x01 \x01(\t\x12\x12\n\nemail_name\x18\x02 \x01(\t\x12\x18\n\x10is_export_target\x18\x03 \x01(\x08\"F\n\x1a\x45xportEmailAdvancedSetting\x12\x12\n\nemail_type\x18\x01 \x01(\t\x12\x14\n\x0c\x65mail_footer\x18\x02 \x01(\x08\"\xeb\x03\n\x11\x45xportPageSetting\x12;\n\x0b\x65xport_type\x18\x01 \x01(\x0e\x32&.tofu.definition.action.ExportPageType\x12V\n\x0ftargets_setting\x18\x02 \x03(\x0b\x32=.tofu.definition.action.ExportPageSetting.TargetsSettingEntry\x12\\\n\x12\x63omponents_setting\x18\x03 \x03(\x0b\<EMAIL>\x12\x41\n\x0burl_setting\x18\x04 \x01(\x0b\x32,.tofu.definition.action.ExportPageUrlSetting\x1a\x66\n\x13TargetsSettingEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12>\n\x05value\x18\x02 \x01(\x0b\x32/.tofu.definition.action.ExportPageTargetSetting:\x02\x38\x01\x1a\x38\n\x16\x43omponentsSettingEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"S\n\x17\x45xportPageTargetSetting\x12\x11\n\tdraft_URL\x18\x01 \x01(\t\x12\x11\n\tpage_slug\x18\x02 \x01(\t\x12\x12\n\npage_title\x18\x03 \x01(\t\"l\n\x14\x45xportPageUrlSetting\x12\x0e\n\x06\x64omain\x18\x01 \x01(\t\x12\x12\n\ngroup_slug\x18\x02 \x01(\t\x12\x11\n\tURL_token\x18\x03 \x01(\t\x12\x1d\n\x15\x61\x64\x64itional_URL_params\x18\x04 \x01(\t*\xdb\x01\n\x0e\x41\x63tionCategory\x12\x1f\n\x1b\x41\x43TION_CATEGORY_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x41\x43TION_CATEGORY_USER_INPUT\x10\x01\x12\x1d\n\x19\x41\x43TION_CATEGORY_REPURPOSE\x10\x02\x12\x1f\n\x1b\x41\x43TION_CATEGORY_PERSONALIZE\x10\x03\x12\x1a\n\x16\x41\x43TION_CATEGORY_EXPORT\x10\x04\x12,\n(ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE\x10\x05*\x90\x01\n\x17\x41\x63tionExecutionLocation\x12)\n%ACTION_EXECUTION_LOCATION_UNSPECIFIED\x10\x00\x12$\n ACTION_EXECUTION_LOCATION_SERVER\x10\x01\x12$\n ACTION_EXECUTION_LOCATION_CLIENT\x10\x02*\xa4\x11\n\nActionType\x12\x1b\n\x17\x41\x43TION_TYPE_UNSPECIFIED\x10\x00\x12/\n+ACTION_TYPE_UPLOAD_OR_SELECT_ANCHOR_CONTENT\x10\x01\x12#\n\x1f\x41\x43TION_TYPE_REPURPOSE_EMAIL_SDR\x10\x02\x12)\n%ACTION_TYPE_REPURPOSE_EMAIL_MARKETING\x10\x03\x12&\n\"ACTION_TYPE_REPURPOSE_LANDING_PAGE\x10\x04\x12$\n ACTION_TYPE_REPURPOSE_WHITEPAPER\x10\x05\x12$\n ACTION_TYPE_REPURPOSE_CASE_STUDY\x10\x06\x12\x1f\n\x1b\x41\x43TION_TYPE_REPURPOSE_EBOOK\x10\x07\x12-\n)ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GENERAL\x10\x08\x12.\n*ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN\x10\t\x12\x37\n3ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_CAROUSEL\x10\n\x12\x37\n3ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_LINKEDIN_DOCUMENT\x10\x0b\x12*\n&ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_META\x10\x0c\x12,\n(ACTION_TYPE_REPURPOSE_AD_CAMPAIGN_GOOGLE\x10\r\x12(\n$ACTION_TYPE_REPURPOSE_SOCIAL_GENERAL\x10\x0e\x12)\n%ACTION_TYPE_REPURPOSE_SOCIAL_LINKEDIN\x10\x0f\x12*\n&ACTION_TYPE_REPURPOSE_MESSAGE_LINKEDIN\x10\x10\x12$\n ACTION_TYPE_REPURPOSE_SALES_DECK\x10\x11\x12$\n ACTION_TYPE_REPURPOSE_SLIDE_DECK\x10\x12\x12!\n\x1d\x41\x43TION_TYPE_REPURPOSE_WEBINAR\x10\x13\x12#\n\x1f\x41\x43TION_TYPE_REPURPOSE_BLOG_POST\x10\x14\x12+\n\'ACTION_TYPE_REPURPOSE_QUOTES_HIGHLIGHTS\x10\x15\x12$\n ACTION_TYPE_REPURPOSE_STATISTICS\x10\x16\x12\x1f\n\x1b\x41\x43TION_TYPE_REPURPOSE_OTHER\x10\x17\x12%\n!ACTION_TYPE_PERSONALIZE_EMAIL_SDR\x10\x18\x12+\n\'ACTION_TYPE_PERSONALIZE_EMAIL_MARKETING\x10\x19\x12(\n$ACTION_TYPE_PERSONALIZE_LANDING_PAGE\x10\x1a\x12&\n\"ACTION_TYPE_PERSONALIZE_WHITEPAPER\x10\x1b\x12!\n\x1d\x41\x43TION_TYPE_PERSONALIZE_EBOOK\x10\x1c\x12/\n+ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GENERAL\x10\x1d\x12\x30\n,ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN\x10\x1e\x12\x39\n5ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_CAROUSEL\x10\x1f\x12\x39\n5ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_LINKEDIN_DOCUMENT\x10 \x12,\n(ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_META\x10!\x12.\n*ACTION_TYPE_PERSONALIZE_AD_CAMPAIGN_GOOGLE\x10\"\x12,\n(ACTION_TYPE_PERSONALIZE_MESSAGE_LINKEDIN\x10#\x12&\n\"ACTION_TYPE_PERSONALIZE_SALES_DECK\x10$\x12*\n&ACTION_TYPE_PERSONALIZE_SOCIAL_GENERAL\x10%\x12+\n\'ACTION_TYPE_PERSONALIZE_SOCIAL_LINKEDIN\x10&\x12%\n!ACTION_TYPE_PERSONALIZE_BLOG_POST\x10\'\x12!\n\x1d\x41\x43TION_TYPE_PERSONALIZE_OTHER\x10(\x12$\n ACTION_TYPE_EXPORT_HUBSPOT_EMAIL\x10)\x12+\n\'ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE\x10*\x12\'\n#ACTION_TYPE_EXPORT_SALESFORCE_EMAIL\x10+\x12$\n ACTION_TYPE_EXPORT_MARKETO_EMAIL\x10,\x12+\n\'ACTION_TYPE_EXPORT_MARKETO_LANDING_PAGE\x10-\x12(\n$ACTION_TYPE_EXPORT_TOFU_LANDING_PAGE\x10.\x12\x32\n.ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_SDR\x10/\x12\x38\n4ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_EMAIL_MARKETING\x10\x30\x12\x39\n5ACTION_TYPE_SEQ_PERSONALIZE_TEMPLATE_LINKEDIN_MESSAGE\x10\x31\x12\x14\n\x10\x41\x43TION_TYPE_LAST\x10\x32*\xd8\x01\n\x10\x41\x63tionStatusType\x12\"\n\x1e\x41\x43TION_STATUS_TYPE_UNSPECIFIED\x10\x00\x12$\n ACTION_STATUS_TYPE_MISSING_INPUT\x10\x01\x12\x1c\n\x18\x41\x43TION_STATUS_TYPE_READY\x10\x02\x12\x1e\n\x1a\x41\x43TION_STATUS_TYPE_RUNNING\x10\x03\x12\x1f\n\x1b\x41\x43TION_STATUS_TYPE_COMPLETE\x10\x04\x12\x1b\n\x17\x41\x43TION_STATUS_TYPE_FAIL\x10\x05*\xb5\x01\n\x0cPlatformType\x12\x1d\n\x19PLATFORM_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n\x16PLATFORM_TYPE_LINKEDIN\x10\x01\x12\x19\n\x15PLATFORM_TYPE_HUBSPOT\x10\x02\x12\x1c\n\x18PLATFORM_TYPE_SALESFORCE\x10\x03\x12\x19\n\x15PLATFORM_TYPE_MARKETO\x10\x04\x12\x16\n\x12PLATFORM_TYPE_TOFU\x10\x05*\xb3\x03\n\x0cTofuDataType\x12\x1e\n\x1aTOFU_DATA_TYPE_UNSPECIFIED\x10\x00\x12\x16\n\x12TOFU_DATA_TYPE_INT\x10\x01\x12\x19\n\x15TOFU_DATA_TYPE_STRING\x10\x02\x12\x17\n\x13TOFU_DATA_TYPE_BOOL\x10\x03\x12\x1f\n\x1bTOFU_DATA_TYPE_CONTENT_TYPE\x10\x04\x12 \n\x1cTOFU_DATA_TYPE_PLATFORM_TYPE\x10\x05\x12 \n\x1cTOFU_DATA_TYPE_CONTENT_GROUP\x10\x06\x12\x18\n\x14TOFU_DATA_TYPE_ASSET\x10\x07\x12\x19\n\x15TOFU_DATA_TYPE_TARGET\x10\x08\x12\x1d\n\x19TOFU_DATA_TYPE_COMPONENTS\x10\t\x12\x1b\n\x17TOFU_DATA_TYPE_TEMPLATE\x10\n\x12%\n!TOFU_DATA_TYPE_CUSTOM_INSTRUCTION\x10\x0b\x12!\n\x1dTOFU_DATA_TYPE_EXPORT_SETTING\x10\x0c\x12\x17\n\x13TOFU_DATA_TYPE_LAST\x10\r*\xe9\x02\n\x11TofuComponentType\x12#\n\x1fTOFU_COMPONENT_TYPE_UNSPECIFIED\x10\x00\x12%\n!TOFU_COMPONENT_TYPE_EMAIL_SUBJECT\x10\x01\x12\"\n\x1eTOFU_COMPONENT_TYPE_EMAIL_BODY\x10\x02\x12,\n(TOFU_COMPONENT_TYPE_LINKEDIN_AD_HEADLINE\x10\x03\x12/\n+TOFU_COMPONENT_TYPE_LINKEDIN_AD_DESCRIPTION\x10\x04\x12\x35\n1TOFU_COMPONENT_TYPE_LINKEDIN_AD_INTRODUCTORY_TEXT\x10\x05\x12+\n\'TOFU_COMPONENT_TYPE_LINKEDIN_AD_AD_COPY\x10\x06\x12!\n\x1dTOFU_COMPONENT_TYPE_HTML_EDIT\x10\x07*\xf4\x01\n\x15TofuComponentMetaType\x12(\n$TOFU_COMPONENT_META_TYPE_UNSPECIFIED\x10\x00\x12!\n\x1dTOFU_COMPONENT_META_TYPE_TEXT\x10\x01\x12\"\n\x1eTOFU_COMPONENT_META_TYPE_IMAGE\x10\x02\x12!\n\x1dTOFU_COMPONENT_META_TYPE_LINK\x10\x03\x12#\n\x1fTOFU_COMPONENT_META_TYPE_ANCHOR\x10\x04\x12\"\n\x1eTOFU_COMPONENT_META_TYPE_VIDEO\x10\x05*\xf9\x01\n\x1f\x41\x63tionEdgeOutputListFetchMethod\x12\x34\n0ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_UNSPECIFIED\x10\x00\x12\x32\n.ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_FETCH_ALL\x10\x01\x12\x33\n/ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_EXPAND_ONE\x10\x02\x12\x37\n3ACTION_EDGE_OUTPUT_LIST_FETCH_METHOD_FETCH_MULTIPLE\x10\x03*\xae\x01\n\x14GenerationStatusType\x12&\n\"GENERATION_STATUS_TYPE_UNSPECIFIED\x10\x00\x12&\n\"GENERATION_STATUS_TYPE_IN_PROGRESS\x10\x01\x12#\n\x1fGENERATION_STATUS_TYPE_COMPLETE\x10\x02\x12!\n\x1dGENERATION_STATUS_TYPE_FAILED\x10\x03*\x9a\x01\n\x10\x45xportStatusType\x12\"\n\x1e\x45XPORT_STATUS_TYPE_NOT_STARTED\x10\x00\x12\x1f\n\x1b\x45XPORT_STATUS_TYPE_COMPLETE\x10\x01\x12\x1d\n\x19\x45XPORT_STATUS_TYPE_FAILED\x10\x02\x12\"\n\x1e\x45XPORT_STATUS_TYPE_IN_PROGRESS\x10\x03*\x97\x01\n\x19\x41nchorPrecheckResultLabel\x12,\n(ANCHOR_PRECHECK_RESULT_LABEL_UNSPECIFIED\x10\x00\x12%\n!ANCHOR_PRECHECK_RESULT_LABEL_PASS\x10\x01\x12%\n!ANCHOR_PRECHECK_RESULT_LABEL_FAIL\x10\x02*N\n\x0f\x45xportEmailType\x12\x1d\n\x19\x45XPORT_EMAIL_TYPE_DYNAMIC\x10\x00\x12\x1c\n\x18\x45XPORT_EMAIL_TYPE_STATIC\x10\x01*g\n\x0e\x45xportPageType\x12\x1a\n\x16\x45XPORT_PAGE_TYPE_EMBED\x10\x00\x12\x1c\n\x18\x45XPORT_PAGE_TYPE_DYNAMIC\x10\x01\x12\x1b\n\x17\x45XPORT_PAGE_TYPE_STATIC\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'action_define_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_TOFUTEMPLATE_TEMPLATEFIELDSENTRY']._loaded_options = None
  _globals['_TOFUTEMPLATE_TEMPLATEFIELDSENTRY']._serialized_options = b'8\001'
  _globals['_TOFUCOMPONENTDATAIMAGE_STYLESENTRY']._loaded_options = None
  _globals['_TOFUCOMPONENTDATAIMAGE_STYLESENTRY']._serialized_options = b'8\001'
  _globals['_TOFUCOMPONENTS_COMPONENTSENTRY']._loaded_options = None
  _globals['_TOFUCOMPONENTS_COMPONENTSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONEDGEOUTPUTFETCHCONFIGBYNAME_OUTPUTTOINPUTMAPPINGENTRY']._loaded_options = None
  _globals['_ACTIONEDGEOUTPUTFETCHCONFIGBYNAME_OUTPUTTOINPUTMAPPINGENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONDEFINITION_REQUIREDINPUTSENTRY']._loaded_options = None
  _globals['_ACTIONDEFINITION_REQUIREDINPUTSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONDEFINITION_OPTIONALINPUTSENTRY']._loaded_options = None
  _globals['_ACTIONDEFINITION_OPTIONALINPUTSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONDEFINITION_OUTPUTSENTRY']._loaded_options = None
  _globals['_ACTIONDEFINITION_OUTPUTSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONDEFINITION_ELIGIBLENEXTACTIONSENTRY']._loaded_options = None
  _globals['_ACTIONDEFINITION_ELIGIBLENEXTACTIONSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSYSTEMCONFIG_ACTIONDEFINITIONSENTRY']._loaded_options = None
  _globals['_ACTIONSYSTEMCONFIG_ACTIONDEFINITIONSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSYSTEMCONFIG_ACTIONCATEGORYTOACTIONTYPESENTRY']._loaded_options = None
  _globals['_ACTIONSYSTEMCONFIG_ACTIONCATEGORYTOACTIONTYPESENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSYSTEMCONFIG_ALLELIGIBLEACTIONSENTRY']._loaded_options = None
  _globals['_ACTIONSYSTEMCONFIG_ALLELIGIBLEACTIONSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSTATUSDETAILSGENPERSONALIZE_GENSTATUSENTRY']._loaded_options = None
  _globals['_ACTIONSTATUSDETAILSGENPERSONALIZE_GENSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_CONTENTGROUPGENERATIONSTATUS_CONTENTGROUPGENSTATUSENTRY']._loaded_options = None
  _globals['_CONTENTGROUPGENERATIONSTATUS_CONTENTGROUPGENSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSTATUSDETAILSGENREPURPOSE_GENSTATUSENTRY']._loaded_options = None
  _globals['_ACTIONSTATUSDETAILSGENREPURPOSE_GENSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSTATUSDETAILSGENSEQPERSONALIZETEMPLATE_GENSTATUSENTRY']._loaded_options = None
  _globals['_ACTIONSTATUSDETAILSGENSEQPERSONALIZETEMPLATE_GENSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_MARKETOOBJECTEXPORTSTATUS_FAILEDIDSWITHERRORENTRY']._loaded_options = None
  _globals['_MARKETOOBJECTEXPORTSTATUS_FAILEDIDSWITHERRORENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSTATUSDETAILSEXPORT_TARGETSTATUSENTRY']._loaded_options = None
  _globals['_ACTIONSTATUSDETAILSEXPORT_TARGETSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONSTATUSDETAILSANCHORPRECHECK_ASSETPRECHECKRESULTSENTRY']._loaded_options = None
  _globals['_ACTIONSTATUSDETAILSANCHORPRECHECK_ASSETPRECHECKRESULTSENTRY']._serialized_options = b'8\001'
  _globals['_ANCHORPRECHECKRESULT_ANCHORPRECHECKDOCRESULTSENTRY']._loaded_options = None
  _globals['_ANCHORPRECHECKRESULT_ANCHORPRECHECKDOCRESULTSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONINPUTS_INPUTSENTRY']._loaded_options = None
  _globals['_ACTIONINPUTS_INPUTSENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONOUTPUTS_OUTPUTSENTRY']._loaded_options = None
  _globals['_ACTIONOUTPUTS_OUTPUTSENTRY']._serialized_options = b'8\001'
  _globals['_EXPORTEMAILSETTING_TARGETSSETTINGENTRY']._loaded_options = None
  _globals['_EXPORTEMAILSETTING_TARGETSSETTINGENTRY']._serialized_options = b'8\001'
  _globals['_EXPORTEMAILSETTING_COMPONENTSSETTINGENTRY']._loaded_options = None
  _globals['_EXPORTEMAILSETTING_COMPONENTSSETTINGENTRY']._serialized_options = b'8\001'
  _globals['_EXPORTPAGESETTING_TARGETSSETTINGENTRY']._loaded_options = None
  _globals['_EXPORTPAGESETTING_TARGETSSETTINGENTRY']._serialized_options = b'8\001'
  _globals['_EXPORTPAGESETTING_COMPONENTSSETTINGENTRY']._loaded_options = None
  _globals['_EXPORTPAGESETTING_COMPONENTSSETTINGENTRY']._serialized_options = b'8\001'
  _globals['_ACTIONCATEGORY']._serialized_start=14478
  _globals['_ACTIONCATEGORY']._serialized_end=14697
  _globals['_ACTIONEXECUTIONLOCATION']._serialized_start=14700
  _globals['_ACTIONEXECUTIONLOCATION']._serialized_end=14844
  _globals['_ACTIONTYPE']._serialized_start=14847
  _globals['_ACTIONTYPE']._serialized_end=17059
  _globals['_ACTIONSTATUSTYPE']._serialized_start=17062
  _globals['_ACTIONSTATUSTYPE']._serialized_end=17278
  _globals['_PLATFORMTYPE']._serialized_start=17281
  _globals['_PLATFORMTYPE']._serialized_end=17462
  _globals['_TOFUDATATYPE']._serialized_start=17465
  _globals['_TOFUDATATYPE']._serialized_end=17900
  _globals['_TOFUCOMPONENTTYPE']._serialized_start=17903
  _globals['_TOFUCOMPONENTTYPE']._serialized_end=18264
  _globals['_TOFUCOMPONENTMETATYPE']._serialized_start=18267
  _globals['_TOFUCOMPONENTMETATYPE']._serialized_end=18511
  _globals['_ACTIONEDGEOUTPUTLISTFETCHMETHOD']._serialized_start=18514
  _globals['_ACTIONEDGEOUTPUTLISTFETCHMETHOD']._serialized_end=18763
  _globals['_GENERATIONSTATUSTYPE']._serialized_start=18766
  _globals['_GENERATIONSTATUSTYPE']._serialized_end=18940
  _globals['_EXPORTSTATUSTYPE']._serialized_start=18943
  _globals['_EXPORTSTATUSTYPE']._serialized_end=19097
  _globals['_ANCHORPRECHECKRESULTLABEL']._serialized_start=19100
  _globals['_ANCHORPRECHECKRESULTLABEL']._serialized_end=19251
  _globals['_EXPORTEMAILTYPE']._serialized_start=19253
  _globals['_EXPORTEMAILTYPE']._serialized_end=19331
  _globals['_EXPORTPAGETYPE']._serialized_start=19333
  _globals['_EXPORTPAGETYPE']._serialized_end=19436
  _globals['_TOFUINT']._serialized_start=47
  _globals['_TOFUINT']._serialized_end=71
  _globals['_TOFUSTRING']._serialized_start=73
  _globals['_TOFUSTRING']._serialized_end=100
  _globals['_TOFUBOOL']._serialized_start=102
  _globals['_TOFUBOOL']._serialized_end=127
  _globals['_TOFUASSET']._serialized_start=129
  _globals['_TOFUASSET']._serialized_end=195
  _globals['_TOFUTEMPLATEFIELD']._serialized_start=198
  _globals['_TOFUTEMPLATEFIELD']._serialized_end=667
  _globals['_TOFUTONEREFERENCE']._serialized_start=670
  _globals['_TOFUTONEREFERENCE']._serialized_end=897
  _globals['_TOFUTONEREFERENCE_TONEANALYSIS']._serialized_start=829
  _globals['_TOFUTONEREFERENCE_TONEANALYSIS']._serialized_end=897
  _globals['_TOFUTEMPLATE']._serialized_start=900
  _globals['_TOFUTEMPLATE']._serialized_end=1426
  _globals['_TOFUTEMPLATE_TEMPLATEFIELDSENTRY']._serialized_start=1330
  _globals['_TOFUTEMPLATE_TEMPLATEFIELDSENTRY']._serialized_end=1426
  _globals['_TOFUCONTENTGROUP']._serialized_start=1428
  _globals['_TOFUCONTENTGROUP']._serialized_end=1472
  _globals['_TOFUCOMPONENTCONTEXTDATAWITHFORMATFORHTML']._serialized_start=1474
  _globals['_TOFUCOMPONENTCONTEXTDATAWITHFORMATFORHTML']._serialized_end=1583
  _globals['_TOFUCOMPONENTCONTEXTDATAWITHFORMATFORPDF']._serialized_start=1586
  _globals['_TOFUCOMPONENTCONTEXTDATAWITHFORMATFORPDF']._serialized_end=1965
  _globals['_TOFUCOMPONENTCONTEXTDATAWITHFORMATFORPDF_BOUNDINGBOX']._serialized_start=1863
  _globals['_TOFUCOMPONENTCONTEXTDATAWITHFORMATFORPDF_BOUNDINGBOX']._serialized_end=1965
  _globals['_TOFUCOMPONENTCONTEXTDATA']._serialized_start=1968
  _globals['_TOFUCOMPONENTCONTEXTDATA']._serialized_end=2267
  _globals['_TOFUCOMPONENTDATATEXT']._serialized_start=2269
  _globals['_TOFUCOMPONENTDATATEXT']._serialized_end=2306
  _globals['_TOFUCOMPONENTDATAHTML']._serialized_start=2309
  _globals['_TOFUCOMPONENTDATAHTML']._serialized_end=2468
  _globals['_TOFUCOMPONENTDATAIMAGE']._serialized_start=2471
  _globals['_TOFUCOMPONENTDATAIMAGE']._serialized_end=2708
  _globals['_TOFUCOMPONENTDATAIMAGE_STYLESENTRY']._serialized_start=2663
  _globals['_TOFUCOMPONENTDATAIMAGE_STYLESENTRY']._serialized_end=2708
  _globals['_TOFUCOMPONENTDATALINK']._serialized_start=2711
  _globals['_TOFUCOMPONENTDATALINK']._serialized_end=2844
  _globals['_TOFUCOMPONENTDATAVIDEO']._serialized_start=2846
  _globals['_TOFUCOMPONENTDATAVIDEO']._serialized_end=2898
  _globals['_TOFUCOMPONENTDATAANCHOR']._serialized_start=2900
  _globals['_TOFUCOMPONENTDATAANCHOR']._serialized_end=2953
  _globals['_TOFUCOMPONENT']._serialized_start=2956
  _globals['_TOFUCOMPONENT']._serialized_end=3719
  _globals['_TOFUCOMPONENTS']._serialized_start=3722
  _globals['_TOFUCOMPONENTS']._serialized_end=3904
  _globals['_TOFUCOMPONENTS_COMPONENTSENTRY']._serialized_start=3816
  _globals['_TOFUCOMPONENTS_COMPONENTSENTRY']._serialized_end=3904
  _globals['_TOFUCUSTOMINSTRUCTION']._serialized_start=3906
  _globals['_TOFUCUSTOMINSTRUCTION']._serialized_end=4004
  _globals['_TOFUCONTENTTYPE']._serialized_start=4006
  _globals['_TOFUCONTENTTYPE']._serialized_end=4045
  _globals['_TOFUPLATFORMTYPE']._serialized_start=4047
  _globals['_TOFUPLATFORMTYPE']._serialized_end=4126
  _globals['_TOFUTARGET']._serialized_start=4128
  _globals['_TOFUTARGET']._serialized_end=4159
  _globals['_TOFUDATA']._serialized_start=4162
  _globals['_TOFUDATA']._serialized_end=4923
  _globals['_TOFUDATALIST']._serialized_start=4925
  _globals['_TOFUDATALIST']._serialized_end=4987
  _globals['_ACTIONINPUTOUTPUTDEFINITION']._serialized_start=4990
  _globals['_ACTIONINPUTOUTPUTDEFINITION']._serialized_end=5333
  _globals['_ACTIONEDGEOUTPUTFETCHMETHOD']._serialized_start=5336
  _globals['_ACTIONEDGEOUTPUTFETCHMETHOD']._serialized_end=5464
  _globals['_ACTIONEDGEOUTPUTFETCHCONFIGBYNAME']._serialized_start=5467
  _globals['_ACTIONEDGEOUTPUTFETCHCONFIGBYNAME']._serialized_end=5734
  _globals['_ACTIONEDGEOUTPUTFETCHCONFIGBYNAME_OUTPUTTOINPUTMAPPINGENTRY']._serialized_start=5622
  _globals['_ACTIONEDGEOUTPUTFETCHCONFIGBYNAME_OUTPUTTOINPUTMAPPINGENTRY']._serialized_end=5734
  _globals['_ACTIONEDGEDEFINITION']._serialized_start=5737
  _globals['_ACTIONEDGEDEFINITION']._serialized_end=6058
  _globals['_PREDEFINEDVALUE']._serialized_start=6060
  _globals['_PREDEFINEDVALUE']._serialized_end=6150
  _globals['_ACTIONDEFINITION']._serialized_start=6153
  _globals['_ACTIONDEFINITION']._serialized_end=7164
  _globals['_ACTIONDEFINITION_REQUIREDINPUTSENTRY']._serialized_start=6743
  _globals['_ACTIONDEFINITION_REQUIREDINPUTSENTRY']._serialized_end=6849
  _globals['_ACTIONDEFINITION_OPTIONALINPUTSENTRY']._serialized_start=6851
  _globals['_ACTIONDEFINITION_OPTIONALINPUTSENTRY']._serialized_end=6957
  _globals['_ACTIONDEFINITION_OUTPUTSENTRY']._serialized_start=6959
  _globals['_ACTIONDEFINITION_OUTPUTSENTRY']._serialized_end=7058
  _globals['_ACTIONDEFINITION_ELIGIBLENEXTACTIONSENTRY']._serialized_start=7060
  _globals['_ACTIONDEFINITION_ELIGIBLENEXTACTIONSENTRY']._serialized_end=7164
  _globals['_STRINGLIST']._serialized_start=7166
  _globals['_STRINGLIST']._serialized_end=7194
  _globals['_ACTIONSYSTEMCONFIG']._serialized_start=7197
  _globals['_ACTIONSYSTEMCONFIG']._serialized_end=7837
  _globals['_ACTIONSYSTEMCONFIG_ACTIONDEFINITIONSENTRY']._serialized_start=7530
  _globals['_ACTIONSYSTEMCONFIG_ACTIONDEFINITIONSENTRY']._serialized_end=7628
  _globals['_ACTIONSYSTEMCONFIG_ACTIONCATEGORYTOACTIONTYPESENTRY']._serialized_start=7630
  _globals['_ACTIONSYSTEMCONFIG_ACTIONCATEGORYTOACTIONTYPESENTRY']._serialized_end=7732
  _globals['_ACTIONSYSTEMCONFIG_ALLELIGIBLEACTIONSENTRY']._serialized_start=7734
  _globals['_ACTIONSYSTEMCONFIG_ALLELIGIBLEACTIONSENTRY']._serialized_end=7837
  _globals['_GENERATIONSTATUS']._serialized_start=7840
  _globals['_GENERATIONSTATUS']._serialized_end=7992
  _globals['_ACTIONSTATUSDETAILSGENPERSONALIZE']._serialized_start=7995
  _globals['_ACTIONSTATUSDETAILSGENPERSONALIZE']._serialized_end=8216
  _globals['_ACTIONSTATUSDETAILSGENPERSONALIZE_GENSTATUSENTRY']._serialized_start=8126
  _globals['_ACTIONSTATUSDETAILSGENPERSONALIZE_GENSTATUSENTRY']._serialized_end=8216
  _globals['_CONTENTGROUPGENERATIONSTATUS']._serialized_start=8219
  _globals['_CONTENTGROUPGENERATIONSTATUS']._serialized_end=8468
  _globals['_CONTENTGROUPGENERATIONSTATUS_CONTENTGROUPGENSTATUSENTRY']._serialized_start=8366
  _globals['_CONTENTGROUPGENERATIONSTATUS_CONTENTGROUPGENSTATUSENTRY']._serialized_end=8468
  _globals['_ACTIONSTATUSDETAILSGENREPURPOSE']._serialized_start=8471
  _globals['_ACTIONSTATUSDETAILSGENREPURPOSE']._serialized_end=8700
  _globals['_ACTIONSTATUSDETAILSGENREPURPOSE_GENSTATUSENTRY']._serialized_start=8598
  _globals['_ACTIONSTATUSDETAILSGENREPURPOSE_GENSTATUSENTRY']._serialized_end=8700
  _globals['_ACTIONSTATUSDETAILSGENSEQPERSONALIZETEMPLATE']._serialized_start=8703
  _globals['_ACTIONSTATUSDETAILSGENSEQPERSONALIZETEMPLATE']._serialized_end=8958
  _globals['_ACTIONSTATUSDETAILSGENSEQPERSONALIZETEMPLATE_GENSTATUSENTRY']._serialized_start=8598
  _globals['_ACTIONSTATUSDETAILSGENSEQPERSONALIZETEMPLATE_GENSTATUSENTRY']._serialized_end=8700
  _globals['_ACTIONSTATUSDETAILSINPUTCHECK']._serialized_start=8961
  _globals['_ACTIONSTATUSDETAILSINPUTCHECK']._serialized_end=9126
  _globals['_MARKETOOBJECTEXPORTSTATUS']._serialized_start=9129
  _globals['_MARKETOOBJECTEXPORTSTATUS']._serialized_end=9343
  _globals['_MARKETOOBJECTEXPORTSTATUS_FAILEDIDSWITHERRORENTRY']._serialized_start=9286
  _globals['_MARKETOOBJECTEXPORTSTATUS_FAILEDIDSWITHERRORENTRY']._serialized_end=9343
  _globals['_EXPORTRESPONSETARGETSTATUS']._serialized_start=9346
  _globals['_EXPORTRESPONSETARGETSTATUS']._serialized_end=9638
  _globals['_ACTIONSTATUSDETAILSEXPORT']._serialized_start=9641
  _globals['_ACTIONSTATUSDETAILSEXPORT']._serialized_end=9886
  _globals['_ACTIONSTATUSDETAILSEXPORT_TARGETSTATUSENTRY']._serialized_start=9783
  _globals['_ACTIONSTATUSDETAILSEXPORT_TARGETSTATUSENTRY']._serialized_end=9886
  _globals['_ACTIONSTATUSDETAILSANCHORPRECHECK']._serialized_start=9889
  _globals['_ACTIONSTATUSDETAILSANCHORPRECHECK']._serialized_end=10148
  _globals['_ACTIONSTATUSDETAILSANCHORPRECHECK_ASSETPRECHECKRESULTSENTRY']._serialized_start=10043
  _globals['_ACTIONSTATUSDETAILSANCHORPRECHECK_ASSETPRECHECKRESULTSENTRY']._serialized_end=10148
  _globals['_ANCHORPRECHECKRESULT']._serialized_start=10151
  _globals['_ANCHORPRECHECKRESULT']._serialized_end=10483
  _globals['_ANCHORPRECHECKRESULT_ANCHORPRECHECKDOCRESULTSENTRY']._serialized_start=10371
  _globals['_ANCHORPRECHECKRESULT_ANCHORPRECHECKDOCRESULTSENTRY']._serialized_end=10483
  _globals['_ANCHORPRECHECKDOCRESULT']._serialized_start=10485
  _globals['_ANCHORPRECHECKDOCRESULT']._serialized_end=10593
  _globals['_ACTIONSTATUSSTATS']._serialized_start=10595
  _globals['_ACTIONSTATUSSTATS']._serialized_end=10700
  _globals['_ACTIONSTATUSDETAILS']._serialized_start=10703
  _globals['_ACTIONSTATUSDETAILS']._serialized_end=11343
  _globals['_ACTIONSTATUS']._serialized_start=11346
  _globals['_ACTIONSTATUS']._serialized_end=11502
  _globals['_ACTIONINPUTS']._serialized_start=11505
  _globals['_ACTIONINPUTS']._serialized_end=11670
  _globals['_ACTIONINPUTS_INPUTSENTRY']._serialized_start=11587
  _globals['_ACTIONINPUTS_INPUTSENTRY']._serialized_end=11670
  _globals['_ACTIONOUTPUTS']._serialized_start=11673
  _globals['_ACTIONOUTPUTS']._serialized_end=11843
  _globals['_ACTIONOUTPUTS_OUTPUTSENTRY']._serialized_start=11759
  _globals['_ACTIONOUTPUTS_OUTPUTSENTRY']._serialized_end=11843
  _globals['_ACTIONMETADATA']._serialized_start=11845
  _globals['_ACTIONMETADATA']._serialized_end=11898
  _globals['_ACTIONEDGECONFIG']._serialized_start=11900
  _globals['_ACTIONEDGECONFIG']._serialized_end=12014
  _globals['_CAMPAIGNCONFIG']._serialized_start=12017
  _globals['_CAMPAIGNCONFIG']._serialized_end=12162
  _globals['_PERSONALIZATIONPARAMS']._serialized_start=12164
  _globals['_PERSONALIZATIONPARAMS']._serialized_end=12256
  _globals['_REPURPOSEPARAMS']._serialized_start=12258
  _globals['_REPURPOSEPARAMS']._serialized_end=12275
  _globals['_SEQPERSONALIZETEMPLATEPARAMS']._serialized_start=12277
  _globals['_SEQPERSONALIZETEMPLATEPARAMS']._serialized_end=12307
  _globals['_EXPORTEMAILPARAMS']._serialized_start=12309
  _globals['_EXPORTEMAILPARAMS']._serialized_end=12398
  _globals['_EXPORTPARAMS']._serialized_start=12400
  _globals['_EXPORTPARAMS']._serialized_end=12514
  _globals['_ACTIONEXECUTIONPARAMS']._serialized_start=12517
  _globals['_ACTIONEXECUTIONPARAMS']._serialized_end=12860
  _globals['_TOFUEXPORTSETTINGS']._serialized_start=12863
  _globals['_TOFUEXPORTSETTINGS']._serialized_end=13111
  _globals['_EXPORTEMAILSETTING']._serialized_start=13114
  _globals['_EXPORTEMAILSETTING']._serialized_end=13621
  _globals['_EXPORTEMAILSETTING_TARGETSSETTINGENTRY']._serialized_start=13460
  _globals['_EXPORTEMAILSETTING_TARGETSSETTINGENTRY']._serialized_end=13563
  _globals['_EXPORTEMAILSETTING_COMPONENTSSETTINGENTRY']._serialized_start=13565
  _globals['_EXPORTEMAILSETTING_COMPONENTSSETTINGENTRY']._serialized_end=13621
  _globals['_EXPORTEMAILTARGETSETTING']._serialized_start=13623
  _globals['_EXPORTEMAILTARGETSETTING']._serialized_end=13714
  _globals['_EXPORTEMAILADVANCEDSETTING']._serialized_start=13716
  _globals['_EXPORTEMAILADVANCEDSETTING']._serialized_end=13786
  _globals['_EXPORTPAGESETTING']._serialized_start=13789
  _globals['_EXPORTPAGESETTING']._serialized_end=14280
  _globals['_EXPORTPAGESETTING_TARGETSSETTINGENTRY']._serialized_start=14120
  _globals['_EXPORTPAGESETTING_TARGETSSETTINGENTRY']._serialized_end=14222
  _globals['_EXPORTPAGESETTING_COMPONENTSSETTINGENTRY']._serialized_start=13565
  _globals['_EXPORTPAGESETTING_COMPONENTSSETTINGENTRY']._serialized_end=13621
  _globals['_EXPORTPAGETARGETSETTING']._serialized_start=14282
  _globals['_EXPORTPAGETARGETSETTING']._serialized_end=14365
  _globals['_EXPORTPAGEURLSETTING']._serialized_start=14367
  _globals['_EXPORTPAGEURLSETTING']._serialized_end=14475
# @@protoc_insertion_point(module_scope)
