"""
This file is auto-generated from crustdata_fields.json.
Do not edit manually. Run update_crustdata_definition.py to update.
"""

from typing import List, Literal, TypedDict, Union


class EnrichField(TypedDict):
    id: str
    code: str
    label: str
    description: Union[str, None]


class EnrichFieldWithCategory(TypedDict):
    id: Literal[
        "people_profile"
        "firmographics"
        "taxonomy"
        "founder_background"
        "revenue"
        "employee_headcount"
        "job_listing_growth"
        "total_job_listings"
        "seo"
        "funding_and_investment"
        "g2"
        "glassdoor"
        "web_traffic"
        "competitors"
    ]
    category: str
    fields: List[EnrichField]


ENRICH_FIELD_WITH_CATEGORIES: List[EnrichFieldWithCategory] = [
    {
        "id": "people_profile",
        "category": "LinkedIn Profile and Background",
        "fields": [
            {
                "id": "linkedin_profile_url",
                "code": "linkedin_profile_url",
                "label": "Linkedin Profile URL",
                "description": "LinkedIn profile URL in system format",
                "disabled": False,
            },
            {
                "id": "linkedin_flagship_url",
                "code": "linkedin_flagship_url",
                "label": "Linkedin Flagship URL",
                "description": "LinkedIn profile URL in human-readable format",
            },
            {
                "id": "name",
                "code": "name",
                "label": "Name",
                "description": "Full name of the person",
            },
            {
                "id": "email",
                "code": "email",
                "label": "Email",
                "description": "Email address of the person (if available)",
            },
            {
                "id": "title",
                "code": "title",
                "label": "Title",
                "description": "Current professional title",
            },
            {
                "id": "headline",
                "code": "headline",
                "label": "Headline",
                "description": "LinkedIn headline displayed on the profile",
            },
            {
                "id": "summary",
                "code": "summary",
                "label": "Summary",
                "description": "Professional summary or bio from LinkedIn profile",
            },
            {
                "id": "num_of_connections",
                "code": "num_of_connections",
                "label": "Number of Connections",
                "description": "Total number of LinkedIn connections",
            },
            {
                "id": "skills",
                "code": "skills",
                "label": "Skills",
                "description": "List of professional skills listed on LinkedIn",
            },
            {
                "id": "twitter_handle",
                "code": "twitter_handle",
                "label": "Twitter Handle",
                "description": "Twitter username if available on LinkedIn",
            },
            {
                "id": "languages",
                "code": "languages",
                "label": "Languages",
                "description": "Languages spoken by the person as listed on LinkedIn",
            },
            {
                "id": "all_employers",
                "code": "all_employers",
                "label": "All Employers",
                "description": "List of all companies the person has worked for",
            },
            {
                "id": "all_employers_company_id",
                "code": "all_employers_company_id",
                "label": "All Employers Company IDs",
                "description": "List of company IDs corresponding to all employers",
            },
            {
                "id": "all_titles",
                "code": "all_titles",
                "label": "All Job Titles",
                "description": "List of all job titles held by the person",
            },
            {
                "id": "all_schools",
                "code": "all_schools",
                "label": "All Schools",
                "description": "List of all educational institutions attended",
            },
            {
                "id": "all_degrees",
                "code": "all_degrees",
                "label": "All Degrees",
                "description": "List of all degrees earned by the person",
            },
            {
                "id": "location",
                "code": "location",
                "label": "Location",
                "description": "Geographic location of the person as listed on LinkedIn",
            },
            {
                "id": "last_updated",
                "code": "last_updated",
                "label": "Last Updated",
                "description": "Date and time when the profile data was last updated",
            },
            {
                "id": "profile_picture_url",
                "code": "profile_picture_url",
                "label": "Profile Picture URL",
                "description": "URL to the person's LinkedIn profile picture",
            },
            {
                "id": "linkedin_open_to_cards",
                "code": "linkedin_open_to_cards",
                "label": "LinkedIn Open To Cards",
                "description": "LinkedIn 'open to' status information",
            },
            {
                "id": "past_employers",
                "code": "past_employers",
                "label": "Past Employers",
                "description": "List of previous employers with detailed information",
            },
            {
                "id": "current_employers",
                "code": "current_employers",
                "label": "Current Employers",
                "description": "List of current employers with detailed information",
            },
            {
                "id": "education_background.degree_name",
                "code": "education_background.degree_name",
                "label": "Education Degree Name",
                "description": "Name of degree earned in educational background",
            },
            {
                "id": "education_background.institute_name",
                "code": "education_background.institute_name",
                "label": "Education Institute Name",
                "description": "Name of educational institution attended",
            },
            {
                "id": "education_background.institute_linkedin_id",
                "code": "education_background.institute_linkedin_id",
                "label": "Education Institute LinkedIn ID",
                "description": "LinkedIn ID of the educational institution",
            },
            {
                "id": "education_background.institute_linkedin_url",
                "code": "education_background.institute_linkedin_url",
                "label": "Education Institute LinkedIn URL",
                "description": "LinkedIn URL of the educational institution",
            },
            {
                "id": "education_background.institute_logo_url",
                "code": "education_background.institute_logo_url",
                "label": "Education Institute Logo URL",
                "description": "URL to the educational institution's logo",
            },
            {
                "id": "education_background.field_of_study",
                "code": "education_background.field_of_study",
                "label": "Education Field of Study",
                "description": "Major or specialization in educational background",
            },
            {
                "id": "education_background.activities_and_societies",
                "code": "education_background.activities_and_societies",
                "label": "Education Activities and Societies",
                "description": "Extracurricular activities during education",
            },
            {
                "id": "education_background.start_date",
                "code": "education_background.start_date",
                "label": "Education Start Date",
                "description": "Start date of education at the institution",
            },
            {
                "id": "education_background.end_date",
                "code": "education_background.end_date",
                "label": "Education End Date",
                "description": "End date of education at the institution",
            },
            {
                "id": "certifications",
                "code": "certifications",
                "label": "Certifications",
                "description": "Professional certifications earned by the person",
            },
            {
                "id": "honors",
                "code": "honors",
                "label": "Honors",
                "description": "Awards and honors received by the person",
            },
            {
                "id": "all_connections",
                "code": "all_connections",
                "label": "All Connections",
                "description": "Information about the person's LinkedIn connections",
            },
        ],
    },
    {
        "id": "firmographics",
        "category": "Firmographics",
        "fields": [
            {
                "id": "hq_country",
                "code": "hq_country",
                "label": "HQ Country",
                "description": "Country where the company's headquarters is located",
            },
            {
                "id": "largest_headcount_country",
                "code": "largest_headcount_country",
                "label": "Largest Headcount Country",
                "description": "Country with the highest employee count for the company",
            },
            {
                "id": "company_website",
                "code": "company_website",
                "label": "Company Website",
                "description": "Full URL of the company's website",
            },
            {
                "id": "company_website_domain",
                "code": "company_website_domain",
                "label": "Company Website Domain",
                "description": "Domain of the company's website without protocol or path",
            },
            {
                "id": "company_linkedin_profile_url",
                "code": "company_linkedin_profile_url",
                "label": "Company LinkedIn Profile URL",
                "description": "URL to the company's LinkedIn profile",
            },
            {
                "id": "crunchbase_profile_url",
                "code": "crunchbase_profile_url",
                "label": "Crunchbase Profile URL",
                "description": "URL to the company's Crunchbase profile",
            },
            {
                "id": "hq_street_address",
                "code": "hq_street_address",
                "label": "HQ Street Address",
                "description": "Street address of company headquarters",
            },
            {
                "id": "year_founded",
                "code": "year_founded",
                "label": "Year Founded",
                "description": "Year the company was founded",
            },
            {
                "id": "company_type",
                "code": "company_type",
                "label": "Company Type",
                "description": "Type of company (e.g. 'Privately Held', 'Public Company')",
            },
            {
                "id": "linkedin_company_description",
                "code": "linkedin_company_description",
                "label": "LinkedIn Company Description",
                "description": "Description from company's LinkedIn profile",
            },
            {
                "id": "company_id",
                "code": "company_id",
                "label": "Company ID",
                "description": "Unique identifier for the company",
            },
            {
                "id": "linkedin_id",
                "code": "linkedin_id",
                "label": "LinkedIn ID",
                "description": "Unique LinkedIn identifier for the company",
            },
            {
                "id": "linkedin_logo_url",
                "code": "linkedin_logo_url",
                "label": "LinkedIn Logo URL",
                "description": "URL of the company's logo from LinkedIn",
            },
            {
                "id": "company_twitter_url",
                "code": "company_twitter_url",
                "label": "Company Twitter URL",
                "description": "URL to the company's Twitter profile",
            },
            {
                "id": "headquarters",
                "code": "headquarters",
                "label": "Headquarters",
                "description": "Full address of the company headquarters",
            },
            {
                "id": "fiscal_year_end",
                "code": "fiscal_year_end",
                "label": "Fiscal Year End",
                "description": "End of the company's fiscal year",
            },
            {
                "id": "employee_count_range",
                "code": "employee_count_range",
                "label": "Employee Count Range",
                "description": "Range indicating approximate number of employees (e.g. '201-500')",
            },
            {
                "id": "acquisition_status",
                "code": "acquisition_status",
                "label": "Acquisition Status",
                "description": "Status indicating if the company has been acquired",
            },
            {
                "id": "markets",
                "code": "markets",
                "label": "Markets",
                "description": "Markets in which the company operates (e.g., PRIVATE, NASDAQ)",
            },
            {
                "id": "ceo_location",
                "code": "ceo_location",
                "label": "CEO Location",
                "description": "Location of the company's CEO",
            },
            {
                "id": "all_office_addresses",
                "code": "all_office_addresses",
                "label": "All Office Addresses",
                "description": "List of all office locations/addresses for the company",
            },
            {
                "id": "news_articles",
                "code": "news_articles",
                "label": "News Articles",
                "description": "Array of recent news articles about the company",
                "disabled": True,
            },
            {
                "id": "company_name",
                "code": "company_name",
                "label": "Company Name",
                "description": "Name of the company",
            },
            {
                "id": "stock_symbols",
                "code": "stock_symbols",
                "label": "Stock Symbols",
                "description": "Stock ticker symbols for publicly traded companies",
            },
        ],
    },
    {
        "id": "taxonomy",
        "category": "Taxonomy",
        "fields": [
            {
                "id": "linkedin_specialities",
                "code": "taxonomy.linkedin_specialities",
                "label": "LinkedIn Specialities",
                "description": "List of specialties listed on the company's LinkedIn profile",
            },
            {
                "id": "linkedin_industries",
                "code": "taxonomy.linkedin_industries",
                "label": "LinkedIn Industries",
                "description": "Industries in which the company operates according to LinkedIn",
            },
            {
                "id": "crunchbase_categories",
                "code": "taxonomy.crunchbase_categories",
                "label": "Crunchbase Categories",
                "description": "Categories assigned to the company on Crunchbase",
            },
        ],
    },
    {
        "id": "founder_background",
        "category": "Founder Background",
        "fields": [
            {
                "id": "founders_locations",
                "code": "founders.founders_locations",
                "label": "Founders Locations",
                "description": "Locations of company founders",
            },
            {
                "id": "founders_education_institute",
                "code": "founders.founders_education_institute",
                "label": "Founders Education Institute",
                "description": "Educational institutions attended by founders",
            },
            {
                "id": "founders_degree_name",
                "code": "founders.founders_degree_name",
                "label": "Founders Degree Name",
                "description": "Degrees held by company founders",
            },
            {
                "id": "founders_previous_companies",
                "code": "founders.founders_previous_companies",
                "label": "Founders Previous Companies",
                "description": "Previous companies where founders worked",
            },
            {
                "id": "founders_previous_titles",
                "code": "founders.founders_previous_titles",
                "label": "Founders Previous Titles",
                "description": "Previous job titles held by founders",
            },
            {
                "id": "founders.profiles",
                "code": "founders.profiles",
                "label": "Founders Profiles",
                "description": "Detailed profiles of company founders",
            },
            {
                "id": "decision_makers",
                "code": "decision_makers",
                "label": "Decision Makers Profiles",
                "description": "Information about key decision makers and leadership team members",
            },
        ],
    },
    {
        "id": "revenue",
        "category": "Revenue",
        "fields": [
            {
                "id": "estimated_revenue_lower_bound_usd",
                "code": "estimated_revenue_lower_bound_usd",
                "label": "Estimated Revenue Lower Bound USD",
                "description": "Lower estimate of company's annual revenue in USD",
            },
            {
                "id": "estimated_revenue_higher_bound_usd",
                "code": "estimated_revenue_higher_bound_usd",
                "label": "Estimated Revenue Higher Bound USD",
                "description": "Higher estimate of company's annual revenue in USD",
            },
        ],
    },
    {
        "id": "employee_headcount",
        "category": "Employee Headcount",
        "fields": [
            {
                "id": "linkedin_headcount",
                "code": "headcount.linkedin_headcount",
                "label": "LinkedIn Headcount",
                "description": "Total number of employees from LinkedIn",
            },
            {
                "id": "linkedin_followers",
                "code": "linkedin_followers.linkedin_followers",
                "label": "LinkedIn Followers",
                "description": "Total number of LinkedIn followers for the company",
            },
            {
                "id": "linkedin_follower_count_timeseries",
                "code": "linkedin_followers.linkedin_follower_count_timeseries",
                "label": "LinkedIn Followers Timeseries",
                "description": "Historical follower count data over time",
                "disabled": True,
            },
            {
                "id": "linkedin_followers_six_months_growth_percent",
                "code": "linkedin_followers.linkedin_followers_six_months_growth_percent",
                "label": "LinkedIn Followers Six Months Growth Percent",
                "description": "Six-month growth percentage in LinkedIn followers",
            },
            {
                "id": "engineering",
                "code": "headcount.linkedin_headcount_by_role_six_months_growth_percent.engineering",
                "label": "Engineering Headcount",
                "description": "Engineering staff headcount growth percentage over six months",
            },
            {
                "id": "human_resource",
                "code": "headcount.linkedin_headcount_by_role_six_months_growth_percent.human_resource",
                "label": "Human Resource Headcount",
                "description": "Human Resource staff headcount growth percentage over six months",
            },
            {
                "id": "headcount.linkedin_headcount_total_growth_absolute.mom",
                "code": "headcount.linkedin_headcount_total_growth_absolute.mom",
                "label": "LinkedIn Headcount Month-over-Month Absolute Change",
                "description": "Absolute month-over-month change in total LinkedIn headcount",
            },
            {
                "id": "headcount.linkedin_headcount_total_growth_absolute.qoq",
                "code": "headcount.linkedin_headcount_total_growth_absolute.qoq",
                "label": "LinkedIn Headcount Quarter-over-Quarter Absolute Change",
                "description": "Absolute quarter-over-quarter change in total LinkedIn headcount",
            },
            {
                "id": "headcount.linkedin_headcount_total_growth_absolute.yoy",
                "code": "headcount.linkedin_headcount_total_growth_absolute.yoy",
                "label": "LinkedIn Headcount Year-over-Year Absolute Change",
                "description": "Absolute year-over-year change in total LinkedIn headcount",
            },
            {
                "id": "linkedin_followers.linkedin_followers_mom_percent",
                "code": "linkedin_followers.linkedin_followers_mom_percent",
                "label": "LinkedIn Followers Month-over-Month Percentage Change",
                "description": "Month-over-month percentage change in LinkedIn followers",
            },
            {
                "id": "linkedin_followers.linkedin_followers_qoq_percent",
                "code": "linkedin_followers.linkedin_followers_qoq_percent",
                "label": "LinkedIn Followers Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in LinkedIn followers",
            },
            {
                "id": "linkedin_followers.linkedin_followers_yoy_percent",
                "code": "linkedin_followers.linkedin_followers_yoy_percent",
                "label": "LinkedIn Followers Year-over-Year Percentage Change",
                "description": "Year-over-year percentage change in LinkedIn followers",
            },
            {
                "id": "headcount.linkedin_headcount_by_role_six_months_growth_percent.sales",
                "code": "headcount.linkedin_headcount_by_role_six_months_growth_percent.sales",
                "label": "Sales Headcount Six Months Growth Percentage",
                "description": "Six-month growth percentage of sales employees",
            },
            {
                "id": "headcount.linkedin_headcount_by_role_yoy_growth_percent.sales",
                "code": "headcount.linkedin_headcount_by_role_yoy_growth_percent.sales",
                "label": "Sales Headcount Year-over-Year Growth Percentage",
                "description": "Year-over-year growth percentage of sales employees",
            },
            {
                "id": "headcount.linkedin_headcount_by_role_six_months_growth_percent.operations",
                "code": "headcount.linkedin_headcount_by_role_six_months_growth_percent.operations",
                "label": "Operations Headcount Six Months Growth Percentage",
                "description": "Six-month growth percentage of operations employees",
            },
            {
                "id": "headcount.linkedin_headcount_by_role_yoy_growth_percent.operations",
                "code": "headcount.linkedin_headcount_by_role_yoy_growth_percent.operations",
                "label": "Operations Headcount Year-over-Year Growth Percentage",
                "description": "Year-over-year growth percentage of operations employees",
            },
            {
                "id": "headcount.linkedin_headcount_by_role_six_months_growth_percent.quality_assurance",
                "code": "headcount.linkedin_headcount_by_role_six_months_growth_percent.quality_assurance",
                "label": "Quality Assurance Headcount Six Months Growth Percentage",
                "description": "Six-month growth percentage of quality assurance employees",
            },
            {
                "id": "headcount.linkedin_headcount_by_role_yoy_growth_percent.quality_assurance",
                "code": "headcount.linkedin_headcount_by_role_yoy_growth_percent.quality_assurance",
                "label": "Quality Assurance Headcount Year-over-Year Growth Percentage",
                "description": "Year-over-year growth percentage of quality assurance employees",
            },
            {
                "id": "linkedin_headcount_by_region_absolute",
                "code": "headcount.linkedin_headcount_by_region_absolute",
                "label": "LinkedIn Headcount by Region Absolute",
                "description": "Employee counts broken down by geographic region",
            },
            {
                "id": "linkedin_headcount_by_region_percent",
                "code": "headcount.linkedin_headcount_by_region_percent",
                "label": "LinkedIn Headcount by Region Percentage",
                "description": "Percentage breakdown of employees by geographic region",
            },
            {
                "id": "linkedin_headcount_by_role_absolute",
                "code": "headcount.linkedin_headcount_by_role_absolute",
                "label": "LinkedIn Headcount by Role Absolute",
                "description": "Employee counts broken down by role category",
            },
            {
                "id": "linkedin_headcount_by_role_percent",
                "code": "headcount.linkedin_headcount_by_role_percent",
                "label": "LinkedIn Headcount by Role Percentage",
                "description": "Percentage breakdown of employees by role category",
            },
            {
                "id": "linkedin_headcount_by_skill_absolute",
                "code": "headcount.linkedin_headcount_by_skill_absolute",
                "label": "LinkedIn Headcount by Skill Absolute",
                "description": "Employee counts broken down by skill set",
            },
            {
                "id": "linkedin_headcount_by_skill_percent",
                "code": "headcount.linkedin_headcount_by_skill_percent",
                "label": "LinkedIn Headcount by Skill Percentage",
                "description": "Percentage breakdown of employees by skill set",
            },
            {
                "id": "linkedin_headcount_by_function_timeseries",
                "code": "headcount.linkedin_headcount_by_function_timeseries",
                "label": "LinkedIn Headcount by Function Timeseries",
                "description": "Historical data of headcount broken down by function over time",
                "disabled": True,
            },
            {
                "id": "linkedin_headcount_timeseries",
                "code": "headcount.linkedin_headcount_timeseries",
                "label": "LinkedIn Headcount Timeseries",
                "description": "Historical total headcount data over time",
                "disabled": True,
            },
            {
                "id": "linkedin_headcount_total_growth_percent_mom",
                "code": "headcount.linkedin_headcount_total_growth_percent.mom",
                "label": "LinkedIn Headcount MoM Growth Percentage",
                "description": "Month-over-month percentage growth in total LinkedIn headcount",
            },
            {
                "id": "linkedin_headcount_total_growth_percent_qoq",
                "code": "headcount.linkedin_headcount_total_growth_percent.qoq",
                "label": "LinkedIn Headcount QoQ Growth Percentage",
                "description": "Quarter-over-quarter percentage growth in total LinkedIn headcount",
            },
            {
                "id": "linkedin_headcount_total_growth_percent_six_months",
                "code": "headcount.linkedin_headcount_total_growth_percent.six_months",
                "label": "LinkedIn Headcount Six Months Growth Percentage",
                "description": "Six-month percentage growth in total LinkedIn headcount",
            },
            {
                "id": "linkedin_headcount_total_growth_percent_two_years",
                "code": "headcount.linkedin_headcount_total_growth_percent.two_years",
                "label": "LinkedIn Headcount Two Years Growth Percentage",
                "description": "Two-year percentage growth in total LinkedIn headcount",
            },
            {
                "id": "linkedin_headcount_total_growth_percent_yoy",
                "code": "headcount.linkedin_headcount_total_growth_percent.yoy",
                "label": "LinkedIn Headcount YoY Growth Percentage",
                "description": "Year-over-year percentage growth in total LinkedIn headcount",
            },
            {
                "id": "linkedin_region_metrics_0_to_10",
                "code": "headcount.linkedin_region_metrics.0_to_10_percent",
                "label": "LinkedIn Region Metrics 0-10%",
                "description": "Regions representing 0-10% of total company headcount",
            },
            {
                "id": "linkedin_region_metrics_11_to_30",
                "code": "headcount.linkedin_region_metrics.11_to_30_percent",
                "label": "LinkedIn Region Metrics 11-30%",
                "description": "Regions representing 11-30% of total company headcount",
            },
            {
                "id": "linkedin_region_metrics_31_to_50",
                "code": "headcount.linkedin_region_metrics.31_to_50_percent",
                "label": "LinkedIn Region Metrics 31-50%",
                "description": "Regions representing 31-50% of total company headcount",
            },
            {
                "id": "linkedin_region_metrics_51_to_70",
                "code": "headcount.linkedin_region_metrics.51_to_70_percent",
                "label": "LinkedIn Region Metrics 51-70%",
                "description": "Regions representing 51-70% of total company headcount",
            },
            {
                "id": "linkedin_region_metrics_71_to_100",
                "code": "headcount.linkedin_region_metrics.71_to_100_percent",
                "label": "LinkedIn Region Metrics 71-100%",
                "description": "Regions representing 71-100% of total company headcount",
            },
            {
                "id": "linkedin_region_metrics_all",
                "code": "headcount.linkedin_region_metrics.all_regions",
                "label": "LinkedIn All Region Metrics",
                "description": "Comprehensive data on employee distribution across all regions",
            },
            {
                "id": "linkedin_headcount_total_growth_absolute_six_months",
                "code": "headcount.linkedin_headcount_total_growth_absolute.six_months",
                "label": "LinkedIn Headcount Six Months Absolute Change",
                "description": "Absolute change in total LinkedIn headcount over six months",
            },
            {
                "id": "linkedin_headcount_total_growth_absolute_two_years",
                "code": "headcount.linkedin_headcount_total_growth_absolute.two_years",
                "label": "LinkedIn Headcount Two Years Absolute Change",
                "description": "Absolute change in total LinkedIn headcount over two years",
            },
            {
                "id": "linkedin_headcount_by_role_yoy_growth_percent_engineering",
                "code": "headcount.linkedin_headcount_by_role_yoy_growth_percent.engineering",
                "label": "Engineering Headcount YoY Growth Percentage",
                "description": "Year-over-year percentage growth of engineering employees",
            },
            {
                "id": "linkedin_headcount_by_role_yoy_growth_percent_human_resource",
                "code": "headcount.linkedin_headcount_by_role_yoy_growth_percent.human_resource",
                "label": "Human Resource Headcount YoY Growth Percentage",
                "description": "Year-over-year percentage growth of human resource employees",
            },
            {
                "id": "linkedin_role_metrics_0_to_10",
                "code": "headcount.linkedin_role_metrics.0_to_10_percent",
                "label": "Role Metrics 0-10%",
                "description": "Roles representing 0-10% of total company headcount",
            },
            {
                "id": "linkedin_role_metrics_11_to_30",
                "code": "headcount.linkedin_role_metrics.11_to_30_percent",
                "label": "Role Metrics 11-30%",
                "description": "Roles representing 11-30% of total company headcount",
            },
            {
                "id": "linkedin_role_metrics_31_to_50",
                "code": "headcount.linkedin_role_metrics.31_to_50_percent",
                "label": "Role Metrics 31-50%",
                "description": "Roles representing 31-50% of total company headcount",
            },
            {
                "id": "linkedin_role_metrics_51_to_70",
                "code": "headcount.linkedin_role_metrics.51_to_70_percent",
                "label": "Role Metrics 51-70%",
                "description": "Roles representing 51-70% of total company headcount",
            },
            {
                "id": "linkedin_role_metrics_71_to_100",
                "code": "headcount.linkedin_role_metrics.71_to_100_percent",
                "label": "Role Metrics 71-100%",
                "description": "Roles representing 71-100% of total company headcount",
            },
            {
                "id": "linkedin_role_metrics_all",
                "code": "headcount.linkedin_role_metrics.all_roles",
                "label": "All Role Metrics",
                "description": "Comprehensive data on employee distribution across all roles",
            },
            {
                "id": "linkedin_skill_metrics_0_to_10",
                "code": "headcount.linkedin_skill_metrics.0_to_10_percent",
                "label": "Skill Metrics 0-10%",
                "description": "Skills representing 0-10% of total company headcount",
            },
            {
                "id": "linkedin_skill_metrics_11_to_30",
                "code": "headcount.linkedin_skill_metrics.11_to_30_percent",
                "label": "Skill Metrics 11-30%",
                "description": "Skills representing 11-30% of total company headcount",
            },
            {
                "id": "linkedin_skill_metrics_31_to_50",
                "code": "headcount.linkedin_skill_metrics.31_to_50_percent",
                "label": "Skill Metrics 31-50%",
                "description": "Skills representing 31-50% of total company headcount",
            },
            {
                "id": "linkedin_skill_metrics_51_to_70",
                "code": "headcount.linkedin_skill_metrics.51_to_70_percent",
                "label": "Skill Metrics 51-70%",
                "description": "Skills representing 51-70% of total company headcount",
            },
            {
                "id": "linkedin_skill_metrics_71_to_100",
                "code": "headcount.linkedin_skill_metrics.71_to_100_percent",
                "label": "Skill Metrics 71-100%",
                "description": "Skills representing 71-100% of total company headcount",
            },
            {
                "id": "linkedin_skill_metrics_all",
                "code": "headcount.linkedin_skill_metrics.all_skills",
                "label": "All Skill Metrics",
                "description": "Comprehensive data on employee distribution across all skills",
            },
        ],
    },
    {
        "id": "job_listing_growth",
        "category": "Job Listing Growth By Function",
        "fields": [
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.accounting",
                "code": "job_openings.job_openings_by_function_qoq_pct.accounting",
                "label": "Accounting Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in accounting job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.accounting",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.accounting",
                "label": "Accounting Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in accounting job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.art_and_design",
                "code": "job_openings.job_openings_by_function_qoq_pct.art_and_design",
                "label": "Art and Design Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in art and design job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.art_and_design",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.art_and_design",
                "label": "Art and Design Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in art and design job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.business_development",
                "code": "job_openings.job_openings_by_function_qoq_pct.business_development",
                "label": "Business Development Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in business development job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.business_development",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.business_development",
                "label": "Business Development Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in business development job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.engineering",
                "code": "job_openings.job_openings_by_function_qoq_pct.engineering",
                "label": "Engineering Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in engineering job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.engineering",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.engineering",
                "label": "Engineering Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in engineering job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.finance",
                "code": "job_openings.job_openings_by_function_qoq_pct.finance",
                "label": "Finance Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in finance job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.finance",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.finance",
                "label": "Finance Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in finance job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.human_resource",
                "code": "job_openings.job_openings_by_function_qoq_pct.human_resource",
                "label": "Human Resource Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in human resource job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.human_resource",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.human_resource",
                "label": "Human Resource Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in human resource job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.information_technology",
                "code": "job_openings.job_openings_by_function_qoq_pct.information_technology",
                "label": "Information Technology Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in IT job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.information_technology",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.information_technology",
                "label": "Information Technology Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in IT job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.marketing",
                "code": "job_openings.job_openings_by_function_qoq_pct.marketing",
                "label": "Marketing Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in marketing job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.marketing",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.marketing",
                "label": "Marketing Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in marketing job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.media_and_communication",
                "code": "job_openings.job_openings_by_function_qoq_pct.media_and_communication",
                "label": "Media and Communication Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in media and communication job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.media_and_communication",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.media_and_communication",
                "label": "Media and Communication Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in media and communication job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.operations",
                "code": "job_openings.job_openings_by_function_qoq_pct.operations",
                "label": "Operations Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in operations job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.operations",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.operations",
                "label": "Operations Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in operations job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.research",
                "code": "job_openings.job_openings_by_function_qoq_pct.research",
                "label": "Research Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in research job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.research",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.research",
                "label": "Research Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in research job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.sales",
                "code": "job_openings.job_openings_by_function_qoq_pct.sales",
                "label": "Sales Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in sales job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.sales",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.sales",
                "label": "Sales Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in sales job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_qoq_pct.product_management",
                "code": "job_openings.job_openings_by_function_qoq_pct.product_management",
                "label": "Product Management Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in product management job openings",
            },
            {
                "id": "job_openings.job_openings_by_function_six_months_growth_pct.product_management",
                "code": "job_openings.job_openings_by_function_six_months_growth_pct.product_management",
                "label": "Product Management Job Openings Six Months Growth Percentage",
                "description": "Six-month growth percentage in product management job openings",
            },
        ],
    },
    {
        "id": "total_job_listings",
        "category": "Total Job Listings",
        "fields": [
            {
                "id": "job_openings.recent_job_openings_title",
                "code": "job_openings.recent_job_openings_title",
                "label": "Current Job Opening Titles",
                "description": "Titles of current job openings at the company",
            },
            {
                "id": "job_openings.job_openings_count",
                "code": "job_openings.job_openings_count",
                "label": "Total Job Openings",
                "description": "Total count of current job openings",
            },
            {
                "id": "job_openings.job_openings_count_growth_percent.mom",
                "code": "job_openings.job_openings_count_growth_percent.mom",
                "label": "Total Job Openings Month-over-Month Percentage Change",
                "description": "Month-over-month percentage change in total job openings",
            },
            {
                "id": "job_openings.job_openings_count_growth_percent.qoq",
                "code": "job_openings.job_openings_count_growth_percent.qoq",
                "label": "Total Job Openings Quarter-over-Quarter Percentage Change",
                "description": "Quarter-over-quarter percentage change in total job openings",
            },
            {
                "id": "job_openings.job_openings_count_growth_percent.yoy",
                "code": "job_openings.job_openings_count_growth_percent.yoy",
                "label": "Total Job Openings Year-over-Year Percentage Change",
                "description": "Year-over-year percentage change in total job openings",
            },
            {
                "id": "job_openings.open_jobs_timeseries",
                "code": "job_openings.open_jobs_timeseries",
                "label": "Total Job Openings Timeseries",
                "description": "Historical data of total job openings",
                "disabled": True,
            },
            {
                "id": "job_openings.recent_job_openings",
                "code": "job_openings.recent_job_openings",
                "label": "Recent Job Openings",
                "description": "List of recent job openings with details",
                "disabled": True,
            },
        ],
    },
    {
        "id": "seo",
        "category": "SEO Metrics",
        "fields": [
            {
                "id": "average_seo_organic_rank",
                "code": "seo.average_seo_organic_rank",
                "label": "Average SEO Organic Rank",
                "description": "Average rank in organic search results",
            },
            {
                "id": "monthly_paid_clicks",
                "code": "seo.monthly_paid_clicks",
                "label": "Monthly Paid Clicks",
                "description": "Monthly clicks from paid search",
            },
            {
                "id": "monthly_organic_clicks",
                "code": "seo.monthly_organic_clicks",
                "label": "Monthly Organic Clicks",
                "description": "Monthly clicks from organic search",
            },
            {
                "id": "average_ad_rank",
                "code": "seo.average_ad_rank",
                "label": "Average Ad Rank",
                "description": "Average position of ads in search results",
            },
            {
                "id": "total_organic_results",
                "code": "seo.total_organic_results",
                "label": "Total Organic Results",
                "description": "Total number of keywords appearing in organic results",
            },
            {
                "id": "monthly_google_ads_budget",
                "code": "seo.monthly_google_ads_budget",
                "label": "Monthly Google Ads Budget",
                "description": "Estimated monthly Google Ads budget in USD",
            },
            {
                "id": "monthly_organic_value",
                "code": "seo.monthly_organic_value",
                "label": "Monthly Organic Value",
                "description": "Estimated value of organic traffic in USD",
            },
            {
                "id": "total_ads_purchased",
                "code": "seo.total_ads_purchased",
                "label": "Total Ads Purchased",
                "description": "Total number of keywords advertised on",
            },
            {
                "id": "lost_ranked_seo_keywords",
                "code": "seo.lost_ranked_seo_keywords",
                "label": "Lost Ranked SEO Keywords",
                "description": "Number of keywords where ranking decreased",
            },
            {
                "id": "gained_ranked_seo_keywords",
                "code": "seo.gained_ranked_seo_keywords",
                "label": "Gained Ranked SEO Keywords",
                "description": "Number of keywords where ranking improved",
            },
            {
                "id": "newly_ranked_seo_keywords",
                "code": "seo.newly_ranked_seo_keywords",
                "label": "Newly Ranked SEO Keywords",
                "description": "Number of newly ranked keywords",
            },
        ],
    },
    {
        "id": "funding_and_investment",
        "category": "Funding and Investment",
        "fields": [
            {
                "id": "crunchbase_total_investment_usd",
                "code": "funding_and_investment.crunchbase_total_investment_usd",
                "label": "Total Investment (USD)",
                "description": "Total investment amount raised in USD",
            },
            {
                "id": "days_since_last_fundraise",
                "code": "funding_and_investment.days_since_last_fundraise",
                "label": "Days Since Last Fundraise",
                "description": "Days elapsed since most recent fundraising round",
            },
            {
                "id": "last_funding_round_type",
                "code": "funding_and_investment.last_funding_round_type",
                "label": "Last Funding Round Type",
                "description": "Type of the most recent funding round",
            },
            {
                "id": "crunchbase_investors_info_list",
                "code": "funding_and_investment.crunchbase_investors_info_list",
                "label": "Investors Info List",
                "description": "Detailed information about investors",
            },
            {
                "id": "crunchbase_investors",
                "code": "funding_and_investment.crunchbase_investors",
                "label": "Investors",
                "description": "List of investor names",
            },
            {
                "id": "last_funding_round_investment_usd",
                "code": "funding_and_investment.last_funding_round_investment_usd",
                "label": "Last Funding Round Amount (USD)",
                "description": "Amount raised in the most recent round in USD",
            },
            {
                "id": "funding_milestones_timeseries",
                "code": "funding_and_investment.funding_milestones_timeseries",
                "label": "Funding Milestones Timeseries",
                "description": "Historical funding rounds and details",
                "disabled": True,
            },
        ],
    },
    {
        "id": "g2",
        "category": "G2 Reviews",
        "fields": [
            {
                "id": "g2_review_count",
                "code": "g2.g2_review_count",
                "label": "G2 Review Count",
                "description": "Number of reviews on G2",
            },
            {
                "id": "g2_average_rating",
                "code": "g2.g2_average_rating",
                "label": "G2 Average Rating",
                "description": "Average rating on G2",
            },
            {
                "id": "g2_review_count_mom_pct",
                "code": "g2.g2_review_count_mom_pct",
                "label": "G2 Review Count MoM %",
                "description": "Month-over-month percentage change in G2 reviews",
            },
            {
                "id": "g2_review_count_qoq_pct",
                "code": "g2.g2_review_count_qoq_pct",
                "label": "G2 Review Count QoQ %",
                "description": "Quarter-over-quarter percentage change in G2 reviews",
            },
            {
                "id": "g2_review_count_yoy_pct",
                "code": "g2.g2_review_count_yoy_pct",
                "label": "G2 Review Count YoY %",
                "description": "Year-over-year percentage change in G2 reviews",
            },
        ],
    },
    {
        "id": "glassdoor",
        "category": "Glassdoor Reviews",
        "fields": [
            {
                "id": "glassdoor_overall_rating",
                "code": "glassdoor.glassdoor_overall_rating",
                "label": "Overall Rating",
                "description": "Overall company rating on Glassdoor",
            },
            {
                "id": "glassdoor_ceo_approval_pct",
                "code": "glassdoor.glassdoor_ceo_approval_pct",
                "label": "CEO Approval %",
                "description": "Percentage of employees who approve of the CEO",
            },
            {
                "id": "glassdoor_business_outlook_pct",
                "code": "glassdoor.glassdoor_business_outlook_pct",
                "label": "Business Outlook %",
                "description": "Percentage with positive business outlook",
            },
            {
                "id": "glassdoor_review_count",
                "code": "glassdoor.glassdoor_review_count",
                "label": "Review Count",
                "description": "Number of reviews on Glassdoor",
            },
            {
                "id": "glassdoor_senior_management_rating",
                "code": "glassdoor.glassdoor_senior_management_rating",
                "label": "Senior Management Rating",
                "description": "Rating for senior management",
            },
            {
                "id": "glassdoor_compensation_rating",
                "code": "glassdoor.glassdoor_compensation_rating",
                "label": "Compensation Rating",
                "description": "Rating for compensation and benefits",
            },
            {
                "id": "glassdoor_career_opportunities_rating",
                "code": "glassdoor.glassdoor_career_opportunities_rating",
                "label": "Career Opportunities Rating",
                "description": "Rating for career opportunities",
            },
            {
                "id": "glassdoor_culture_rating",
                "code": "glassdoor.glassdoor_culture_rating",
                "label": "Culture Rating",
                "description": "Rating for company culture",
            },
            {
                "id": "glassdoor_diversity_rating",
                "code": "glassdoor.glassdoor_diversity_rating",
                "label": "Diversity Rating",
                "description": "Rating for diversity",
            },
            {
                "id": "glassdoor_work_life_balance_rating",
                "code": "glassdoor.glassdoor_work_life_balance_rating",
                "label": "Work/Life Balance Rating",
                "description": "Rating for work-life balance",
            },
            {
                "id": "glassdoor_recommend_to_friend_pct",
                "code": "glassdoor.glassdoor_recommend_to_friend_pct",
                "label": "Recommend to Friend %",
                "description": "Percentage who would recommend to a friend",
            },
            {
                "id": "glassdoor_ceo_approval_growth_percent_mom",
                "code": "glassdoor.glassdoor_ceo_approval_growth_percent.mom",
                "label": "CEO Approval MoM Growth Percentage",
                "description": "Month-over-month growth percentage in CEO approval",
            },
            {
                "id": "glassdoor_ceo_approval_growth_percent_qoq",
                "code": "glassdoor.glassdoor_ceo_approval_growth_percent.qoq",
                "label": "CEO Approval QoQ Growth Percentage",
                "description": "Quarter-over-quarter growth percentage in CEO approval",
            },
            {
                "id": "glassdoor_ceo_approval_growth_percent_yoy",
                "code": "glassdoor.glassdoor_ceo_approval_growth_percent.yoy",
                "label": "CEO Approval YoY Growth Percentage",
                "description": "Year-over-year growth percentage in CEO approval",
            },
            {
                "id": "glassdoor_review_count_growth_percent_mom",
                "code": "glassdoor.glassdoor_review_count_growth_percent.mom",
                "label": "Review Count MoM Growth Percentage",
                "description": "Month-over-month growth percentage in review count",
            },
            {
                "id": "glassdoor_review_count_growth_percent_qoq",
                "code": "glassdoor.glassdoor_review_count_growth_percent.qoq",
                "label": "Review Count QoQ Growth Percentage",
                "description": "Quarter-over-quarter growth percentage in review count",
            },
            {
                "id": "glassdoor_review_count_growth_percent_yoy",
                "code": "glassdoor.glassdoor_review_count_growth_percent.yoy",
                "label": "Review Count YoY Growth Percentage",
                "description": "Year-over-year growth percentage in review count",
            },
        ],
    },
    {
        "id": "web_traffic",
        "category": "Web Traffic",
        "fields": [
            {
                "id": "monthly_visitors",
                "code": "web_traffic.monthly_visitors",
                "label": "Monthly Visitors",
                "description": "Monthly visitors to the company website",
            },
            {
                "id": "monthly_visitor_mom_pct",
                "code": "web_traffic.monthly_visitor_mom_pct",
                "label": "Monthly Visitors MoM %",
                "description": "Month-over-month percentage change in visitors",
            },
            {
                "id": "monthly_visitor_qoq_pct",
                "code": "web_traffic.monthly_visitor_qoq_pct",
                "label": "Monthly Visitors QoQ %",
                "description": "Quarter-over-quarter percentage change in visitors",
            },
            {
                "id": "traffic_source_social_pct",
                "code": "web_traffic.traffic_source_social_pct",
                "label": "Social Traffic %",
                "description": "Percentage of traffic from social media",
            },
            {
                "id": "traffic_source_search_pct",
                "code": "web_traffic.traffic_source_search_pct",
                "label": "Search Traffic %",
                "description": "Percentage of traffic from search engines",
            },
            {
                "id": "traffic_source_direct_pct",
                "code": "web_traffic.traffic_source_direct_pct",
                "label": "Direct Traffic %",
                "description": "Percentage of traffic from direct visits",
            },
            {
                "id": "traffic_source_paid_referral_pct",
                "code": "web_traffic.traffic_source_paid_referral_pct",
                "label": "Paid Referral Traffic %",
                "description": "Percentage of traffic from paid referrals",
            },
            {
                "id": "traffic_source_referral_pct",
                "code": "web_traffic.traffic_source_referral_pct",
                "label": "Referral Traffic %",
                "description": "Percentage of traffic from organic referrals",
            },
            {
                "id": "monthly_visitors_timeseries",
                "code": "web_traffic.monthly_visitors_timeseries",
                "label": "Monthly Visitors Timeseries",
                "description": "Historical data of monthly visitors",
                "disabled": True,
            },
            {
                "id": "traffic_source_direct_pct_timeseries",
                "code": "web_traffic.traffic_source_direct_pct_timeseries",
                "label": "Direct Traffic Percentage Timeseries",
                "description": "Historical data of direct traffic percentages",
                "disabled": True,
            },
            {
                "id": "traffic_source_paid_referral_pct_timeseries",
                "code": "web_traffic.traffic_source_paid_referral_pct_timeseries",
                "label": "Paid Referral Traffic Percentage Timeseries",
                "description": "Historical data of paid referral traffic percentages",
                "disabled": True,
            },
            {
                "id": "traffic_source_referral_pct_timeseries",
                "code": "web_traffic.traffic_source_referral_pct_timeseries",
                "label": "Referral Traffic Percentage Timeseries",
                "description": "Historical data of referral traffic percentages",
                "disabled": True,
            },
            {
                "id": "traffic_source_search_pct_timeseries",
                "code": "web_traffic.traffic_source_search_pct_timeseries",
                "label": "Search Traffic Percentage Timeseries",
                "description": "Historical data of search traffic percentages",
                "disabled": True,
            },
            {
                "id": "traffic_source_social_pct_timeseries",
                "code": "web_traffic.traffic_source_social_pct_timeseries",
                "label": "Social Traffic Percentage Timeseries",
                "description": "Historical data of social traffic percentages",
                "disabled": True,
            },
        ],
    },
    {
        "id": "competitors",
        "category": "Competitors",
        "fields": [
            {
                "id": "competitor_website_domains",
                "code": "competitors.competitor_website_domains",
                "label": "Competitor Website Domains",
                "description": "List of website domains of competing companies",
            },
            {
                "id": "paid_seo_competitors_website_domains",
                "code": "competitors.paid_seo_competitors_website_domains",
                "label": "Paid SEO Competitor Domains",
                "description": "Domains competing for the same paid keywords",
            },
            {
                "id": "organic_seo_competitors_website_domains",
                "code": "competitors.organic_seo_competitors_website_domains",
                "label": "Organic SEO Competitor Domains",
                "description": "Domains competing for the same organic keywords",
            },
        ],
    },
]

CATEGORIES_FOR_COMPANY: List[str] = [
    "firmographics",
    "founder_background",
    "revenue",
    "employee_headcount",
    "job_listing_growth",
    "total_job_listings",
    "taxonomy",
    "web_traffic",
    "glassdoor",
    "g2",
    "funding_and_investment",
    "seo",
]

CATEGORIES_FOR_CONTACT: List[str] = ["people_profile"]
