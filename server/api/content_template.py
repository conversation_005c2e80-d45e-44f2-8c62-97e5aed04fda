import json
import logging

from .models import Content, ContentTemplate
from .playbook_build.doc_loader import get_template
from .shared_definitions.protobuf.gen.action_define_pb2 import ActionCategory
from .utils import (
    copy_s3_file,
    extract_slate_editor_text,
    get_s3_file,
    replace_with_content_variations,
)


class ContentTemplateHandler:
    def __init__(self, user, destination_playbook):
        self.user = user
        self.destination_playbook = destination_playbook

    def save_content_as_template(
        self, content_group, content_template_name, save_repurpose_variations=True
    ):
        campaign_goal = content_group.campaign.campaign_params.get("campaign_goal", "")
        action_category = None
        if campaign_goal == "Workflow":
            # campaign v3 case
            # get the action associated with the content_group
            action = content_group.action
            if action:
                action_category = action.action_category
        if campaign_goal == "Personalization" or (
            action_category
            and action_category
            == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_PERSONALIZE)
        ):
            content_template = ContentTemplate.objects.create(
                creator=self.user,
                playbook=self.destination_playbook,
                name=content_template_name,
                template_data={
                    "content_group_params": self.filter_content_group_params(
                        content_group.content_group_params
                    ),
                    "components": content_group.components,
                    "orig_content_group_id": content_group.id,
                    "orig_campaign_goal": campaign_goal,
                    "template_preview": self.get_template_preview(content_group),
                    "is_template_v2": True,
                },
            )
            if action_category:
                content_template.template_data["action_category"] = action_category
            return content_template
        elif campaign_goal == "Repurpose Content" or (
            action_category
            and action_category
            == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_REPURPOSE)
        ):
            # Assume one content in this content_group.
            content = Content.objects.filter(content_group=content_group).first()
            content_template = ContentTemplate.objects.create(
                creator=self.user,
                playbook=self.destination_playbook,
                name=content_template_name,
                template_data={
                    "content_group_params": self.filter_content_group_params(
                        content_group.content_group_params
                    ),
                    "components": (
                        replace_with_content_variations(
                            content_group.components, content.id
                        )
                        if save_repurpose_variations
                        else {}
                    ),
                    "orig_content_group_id": content_group.id,
                    "template_preview": self.get_template_preview(
                        content_group, save_repurpose_variations
                    ),
                    "orig_campaign_goal": campaign_goal,
                    "is_template_v2": True,
                },
            )
            if action_category:
                content_template.template_data["action_category"] = action_category
            return content_template
        else:
            logging.error(
                f"Invalid campaign goal: {campaign_goal} or action category: {action_category}"
            )
            return None

    def fetch_content_templates(self, content_type=None, is_template_v2=False):
        if is_template_v2:
            if not content_type:
                raise ValueError("content_type is required for v2 templates")
            # Match content templates with same content_group_params.content_type
            content_templates = ContentTemplate.objects.filter(
                playbook=self.destination_playbook,
                template_data__content_group_params__content_type=content_type,
            )
        else:
            if content_type:
                content_templates = ContentTemplate.objects.filter(
                    playbook=self.destination_playbook,
                    template_data__content_types__contains_element=content_type,
                )
            else:
                content_templates = ContentTemplate.objects.filter(
                    playbook=self.destination_playbook,
                )
        return content_templates

    def filter_content_group_params(self, content_group_params):
        filtered_content_group_params = {
            key: value
            for key, value in content_group_params.items()
            if key
            in [
                "content_type",
                "content_source_format",
                "content_source_copy",
                "content_source",
                "content_source_upload_method",
                "repurpose_template_content_source_copy",
                "slate_repurpose_template_content_source_copy",
                "slate_repurpose_template_content_source",
                "subject_line_only_content_source_copy",
                "hubspotEmailId",
            ]
        }
        for key in [
            "content_source_copy",
            "repurpose_template_content_source_copy",
            "slate_repurpose_template_content_source_copy",
            "subject_line_only_content_source_copy",
        ]:
            if (
                key in filtered_content_group_params
                and filtered_content_group_params[key]
            ):
                filtered_content_group_params[key] = copy_s3_file(
                    filtered_content_group_params[key]
                )
        return filtered_content_group_params

    def get_template_preview(
        self, content_group, save_repurpose_variations=True, char_budget=200
    ):
        campaign_goal = content_group.campaign.campaign_params.get("campaign_goal", "")
        action_category = None
        if campaign_goal == "Workflow":
            # campaign v3 case
            # get the action associated with the content_group
            action = content_group.action
            if action:
                action_category = action.action_category
        if campaign_goal == "Personalization" or (
            action_category
            and action_category
            == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_PERSONALIZE)
        ):
            template_preview = self.get_personalization_template_preview(content_group)
        elif campaign_goal == "Repurpose Content" or (
            action_category
            and action_category
            == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_REPURPOSE)
        ):
            template_preview = self.get_repurpose_content_template_preview(
                content_group, save_repurpose_variations
            )
        else:
            logging.error(f"Invalid campaign goal: {campaign_goal}")
            return ""

        if len(template_preview) > char_budget:
            template_preview = template_preview[:char_budget]

        return template_preview

    def get_personalization_template_preview(self, content_group):
        content_source_upload_method = content_group.content_group_params.get(
            "content_source_upload_method", ""
        )
        if content_source_upload_method in ["Link", "File"]:
            return content_group.content_group_params.get("content_source", "")
        return self.get_p13n_text_preview(content_group)

    def get_repurpose_content_template_preview(
        self, content_group, save_repurpose_variations
    ):
        if save_repurpose_variations:
            return self.get_components_text_preview(content_group)
        else:
            return self.get_repurpose_template_text_preview(content_group)

    def get_repurpose_template_text_preview(self, content_group):
        repurpose_template_content_source_copy = content_group.content_group_params.get(
            "repurpose_template_content_source_copy", ""
        )
        if not repurpose_template_content_source_copy:
            return ""
        repurpose_template_text_dict = get_template(
            repurpose_template_content_source_copy, enable_multimodal=False
        )

        repurpose_template_text = self.extract_repurpose_template_text(
            repurpose_template_text_dict
        )

        return repurpose_template_text

    def extract_repurpose_template_text(self, repurpose_template_text_dict):
        text_component_keys = ["text"]
        email_component_keys = ["subjectLine", "body"]
        ad_component_keys = ["introductory-text", "headline", "description", "ad-copy"]

        component_key_sets = [
            text_component_keys,
            email_component_keys,
            ad_component_keys,
        ]

        for keys in component_key_sets:
            if any(key in repurpose_template_text_dict for key in keys):
                return " ".join(
                    [repurpose_template_text_dict.get(key, "") for key in keys]
                )

        logging.error(
            f"No repurpose template text found for: {repurpose_template_text_dict}"
        )
        return ""

    def get_components_text_preview(self, content_group):
        content = Content.objects.filter(content_group=content_group).first()
        if not content_group.components:
            return ""
        components = replace_with_content_variations(
            content_group.components, content.id
        )
        return "\n".join(
            [component.get("text", "") for _, component in components.items()]
        )

    def get_p13n_text_preview(self, content_group):
        content_source_copy = content_group.content_group_params.get(
            "content_source_copy", ""
        )
        if not content_source_copy:
            logging.error(
                f"No content source copy found for content group: {content_group.id}"
            )
            return ""
        else:
            # try to get s3 file content.
            content_source_data = get_s3_file(content_source_copy)
            if not content_source_data:
                logging.error(
                    f"No content source data found for content source copy: {content_source_copy}"
                )
                return ""
            else:
                try:
                    json_data = json.loads(content_source_data)
                    content_source_text = extract_slate_editor_text(json_data)
                    return content_source_text
                except Exception as e:
                    logging.error(f"Failed to parse content source copy: {e}")
                    return ""
