import logging
import os
import re
import tempfile
from typing import List, Optional
from urllib.parse import urlsplit

import googleapiclient.discovery
import requests
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload

from .retry_utils import retry_operation


def extract_google_drive_file_id(google_drive_url: str) -> str:
    """Extract Google Drive file ID from various URL formats using regex.

    Supports URLs like:
    - https://docs.google.com/document/d/FILE_ID/edit
    - https://docs.google.com/spreadsheets/d/FILE_ID/view
    - https://docs.google.com/presentation/d/FILE_ID/copy
    - https://drive.google.com/file/d/FILE_ID/view
    - https://drive.google.com/open?id=FILE_ID

    Args:
        google_drive_url: The Google Drive URL

    Returns:
        The extracted file ID

    Raises:
        ValueError: If no valid file ID is found in the URL
    """
    if not google_drive_url:
        raise ValueError("Empty Google Drive URL provided")

    # Pattern to match file ID in various Google Drive URL formats
    # Matches /d/FILE_ID or /file/d/FILE_ID or ?id=FILE_ID
    patterns = [
        r"/(?:file/)?d/([a-zA-Z0-9_-]{25,})",  # /d/ID or /file/d/ID
        r"[?&]id=([a-zA-Z0-9_-]{25,})",  # ?id=ID or &id=ID
    ]

    for pattern in patterns:
        match = re.search(pattern, google_drive_url)
        if match:
            return match.group(1)

    raise ValueError(
        f"Could not extract file ID from Google Drive URL: {google_drive_url}"
    )


def _should_skip_permission(permission: dict) -> bool:
    """Check if a permission should be skipped during copying.

    Skips:
    - Owner permissions
    - Service account permissions
    - FlashDocs account permissions
    """
    # Skip owner permissions as we can't transfer ownership
    if permission.get("role", "").lower() == "owner":
        return True

    # Skip if it's the service account permission (already has access)
    email_address = permission.get("emailAddress", "")
    if email_address == "<EMAIL>":
        return True

    # Skip if it's a flashdocs.ai account
    if email_address.endswith("@flashdocs.ai"):
        return True

    return False


def _delete_google_drive_file(file_id: str, drive_service) -> None:
    """Delete a Google Drive file by ID."""
    try:
        drive_service.files().delete(fileId=file_id, supportsAllDrives=True).execute()
        logging.info(f"Deleted Google Drive file: {file_id}")
    except Exception as e:
        logging.warning(f"Failed to delete Google Drive file {file_id}: {e}")


def _copy_file_only(source_file_id: str, name: str, drive_service) -> str:
    """Copy only the file without applying permissions."""
    copied_file = (
        drive_service.files()
        .copy(
            fileId=source_file_id,
            body={"name": name},
            supportsAllDrives=True,
        )
        .execute()
    )
    return copied_file["id"]


def _apply_permissions_to_file(
    source_file_id: str, target_file_id: str, drive_service
) -> None:
    """Apply filtered permissions from source file to target file."""
    # Get original file's permissions
    permissions = (
        drive_service.permissions()
        .list(
            fileId=source_file_id,
            supportsAllDrives=True,
            fields="permissions(emailAddress,role,type,allowFileDiscovery,domain)",
        )
        .execute()
        .get("permissions", [])
    )

    # Apply each permission to the new file, excluding filtered ones
    for permission in permissions:
        # Handle owner permissions specially - convert to writer instead of skipping
        if permission.get("role", "").lower() == "owner":
            # Skip if it's the service account permission (already has access)
            email_address = permission.get("emailAddress", "")
            if email_address == "<EMAIL>":
                continue

            # Skip if it's a flashdocs.ai account
            if email_address.endswith("@flashdocs.ai"):
                continue

            # Convert owner to writer permission
            permission_copy = permission.copy()
            permission_copy["role"] = "writer"
        else:
            # For non-owner permissions, use existing skip logic
            if _should_skip_permission(permission):
                continue
            permission_copy = permission.copy()

        # Handle domain permissions - ensure domain field is present for domain type
        if permission_copy.get("type") == "domain" and not permission_copy.get(
            "domain"
        ):
            logging.warning(
                f"Skipping domain permission without domain field: {permission_copy}"
            )
            continue

        # Remove fields that can't be used in create request
        if "id" in permission_copy:
            del permission_copy["id"]
        if "kind" in permission_copy:
            del permission_copy["kind"]

        drive_service.permissions().create(
            fileId=target_file_id,
            body=permission_copy,
            sendNotificationEmail=False,
            supportsAllDrives=True,
        ).execute()


def copy_google_drive_file(google_drive_url: str, name: str = None):
    """Copy a Google Drive file and maintain its permissions."""
    creds = service_account.Credentials.from_service_account_file(
        ".credentials/google_service_account_key.json",
        scopes=["https://www.googleapis.com/auth/drive"],
    )

    drive = googleapiclient.discovery.build("drive", "v3", credentials=creds)

    # Extract file ID from URL
    file_id = extract_google_drive_file_id(google_drive_url)

    # If no name provided, get original file name and prepend "(COPY)"
    if not name:
        file_metadata = drive.files().get(fileId=file_id, fields="name").execute()
        name = f"(COPY) {file_metadata['name']}"

    # Create the copy
    new_file_id = _copy_file_only(file_id, name, drive)

    try:
        # Apply permissions
        _apply_permissions_to_file(file_id, new_file_id, drive)
    except Exception as e:
        # If permission application fails, delete the orphaned copy
        logging.error(f"Failed to apply permissions to copied file {new_file_id}: {e}")
        _delete_google_drive_file(new_file_id, drive)
        raise

    # Determine the file type from the original URL to return the correct URL format
    if "document" in google_drive_url:
        return f"https://docs.google.com/document/d/{new_file_id}/edit"
    elif "spreadsheets" in google_drive_url:
        return f"https://docs.google.com/spreadsheets/d/{new_file_id}/edit"
    elif "presentation" in google_drive_url:
        return f"https://docs.google.com/presentation/d/{new_file_id}/edit"
    else:
        return f"https://drive.google.com/file/d/{new_file_id}/view"


def copy_google_drive_file_with_retry(google_drive_url: str, name: str = None) -> str:
    """Copy a Google Drive file with retry logic and orphan cleanup."""
    created_file_ids: List[str] = []
    creds = service_account.Credentials.from_service_account_file(
        ".credentials/google_service_account_key.json",
        scopes=["https://www.googleapis.com/auth/drive"],
    )
    drive = googleapiclient.discovery.build("drive", "v3", credentials=creds)

    def copy_operation():
        try:
            # Extract file ID from URL
            file_id = extract_google_drive_file_id(google_drive_url)

            # If no name provided, get original file name and prepend "(COPY)"
            if not name:
                file_metadata = (
                    drive.files().get(fileId=file_id, fields="name").execute()
                )
                copy_name = f"(COPY) {file_metadata['name']}"
            else:
                copy_name = name

            # Create the copy
            new_file_id = _copy_file_only(file_id, copy_name, drive)
            created_file_ids.append(new_file_id)

            # Apply permissions
            _apply_permissions_to_file(file_id, new_file_id, drive)

            # If we get here, the operation succeeded, remove from cleanup list
            if new_file_id in created_file_ids:
                created_file_ids.remove(new_file_id)

            # Determine the file type from the original URL to return the correct URL format
            if "document" in google_drive_url:
                return f"https://docs.google.com/document/d/{new_file_id}/edit"
            elif "spreadsheets" in google_drive_url:
                return f"https://docs.google.com/spreadsheets/d/{new_file_id}/edit"
            elif "presentation" in google_drive_url:
                return f"https://docs.google.com/presentation/d/{new_file_id}/edit"
            else:
                return f"https://drive.google.com/file/d/{new_file_id}/view"
        except Exception as e:
            # Clean up any created files on failure
            for orphaned_file_id in created_file_ids:
                _delete_google_drive_file(orphaned_file_id, drive)
            created_file_ids.clear()
            raise

    try:
        return retry_operation(
            copy_operation,
            max_retries=3,
            base_delay=2.0,
            operation_name=f"copy Google Drive file",
            log_success=True,
            success_message=f"Successfully copied Google Drive file",
        )
    except Exception as e:
        # Final cleanup attempt
        for orphaned_file_id in created_file_ids:
            _delete_google_drive_file(orphaned_file_id, drive)
        raise


def set_google_drive_permissions_public(google_drive_url: str) -> bool:
    """Set Google Drive file permissions to public and share with specific email addresses."""
    if not google_drive_url:
        logging.error("Empty Google Drive URL provided")
        return False

    # Parse the file ID from various Google Drive URL formats
    try:
        file_id = extract_google_drive_file_id(google_drive_url)
    except ValueError as e:
        logging.error(str(e))
        return False

    # Get credentials and build drive service
    creds = service_account.Credentials.from_service_account_file(
        ".credentials/google_service_account_key.json",
        scopes=["https://www.googleapis.com/auth/drive"],
    )

    drive = googleapiclient.discovery.build("drive", "v3", credentials=creds)

    # Set up the public permission
    public_permission = {
        "type": "anyone",
        "role": "writer",
        "allowFileDiscovery": False,
    }

    # Create the public permission
    drive.permissions().create(
        fileId=file_id,
        body=public_permission,
        fields="id",
        supportsAllDrives=True,
    ).execute()

    # Share with specific email addresses
    flashdocs_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]

    for email in flashdocs_emails:
        user_permission = {"type": "user", "role": "writer", "emailAddress": email}
        drive.permissions().create(
            fileId=file_id,
            body=user_permission,
            fields="id",
            supportsAllDrives=True,
            sendNotificationEmail=False,
        ).execute()

    logging.info(
        f"Successfully set public permissions and shared with FlashDocs team for file ID: {file_id}"
    )
    return True


def set_google_drive_permissions_flashdocs(google_drive_url: str) -> bool:
    """Set Google Drive file permissions to share with FlashDocs team."""
    if not google_drive_url:
        logging.error("Empty Google Drive URL provided")
        return False

    # Parse the file ID from various Google Drive URL formats
    try:
        file_id = extract_google_drive_file_id(google_drive_url)
    except ValueError as e:
        logging.error(str(e))
        return False

    # Get credentials and build drive service
    creds = service_account.Credentials.from_service_account_file(
        ".credentials/google_service_account_key.json",
        scopes=["https://www.googleapis.com/auth/drive"],
    )

    drive = googleapiclient.discovery.build("drive", "v3", credentials=creds)

    # Share with specific FlashDocs email addresses
    flashdocs_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]

    def share_with_flashdocs():
        for email in flashdocs_emails:
            user_permission = {"type": "user", "role": "writer", "emailAddress": email}
            drive.permissions().create(
                fileId=file_id,
                body=user_permission,
                fields="id",
                supportsAllDrives=True,
                sendNotificationEmail=False,
            ).execute()
        return True

    try:
        retry_operation(
            share_with_flashdocs,
            max_retries=3,
            base_delay=2.0,
            operation_name=f"share Google Drive file {file_id} with FlashDocs team",
            log_success=True,
            success_message=f"Successfully shared with FlashDocs team for file ID: {file_id}",
        )
        return True

    except Exception as e:
        logging.error(f"Error sharing file with FlashDocs team: {str(e)}")
        return False


def copy_permissions_from_template_to_slides(
    template_url: str, slides_url: str
) -> None:
    """Copy permissions from the original template to the generated slides with retry logic."""
    # Get credentials and build drive service
    creds = service_account.Credentials.from_service_account_file(
        ".credentials/google_service_account_key.json",
        scopes=["https://www.googleapis.com/auth/drive"],
    )
    drive = googleapiclient.discovery.build("drive", "v3", credentials=creds)

    # Extract file IDs from URLs
    template_file_id = extract_google_drive_file_id(template_url)
    slides_file_id = extract_google_drive_file_id(slides_url)

    # Get permissions from the original template with retry logic
    def get_permissions():
        return (
            drive.permissions()
            .list(
                fileId=template_file_id,
                supportsAllDrives=True,
                fields="permissions(emailAddress,role,type,allowFileDiscovery,domain)",
            )
            .execute()
            .get("permissions", [])
        )

    permissions = retry_operation(
        get_permissions,
        max_retries=3,
        base_delay=2.0,
        operation_name=f"get permissions from template {template_file_id}",
        log_success=False,
    )

    # Apply each permission to the slides file, excluding owner permissions
    for permission in permissions:
        # Handle owner permissions specially - convert to writer instead of skipping
        if permission.get("role", "").lower() == "owner":
            # Skip if it's the service account permission (already has access)
            email_address = permission.get("emailAddress", "")
            if email_address == "<EMAIL>":
                continue

            # Skip if it's a flashdocs.ai account
            if email_address.endswith("@flashdocs.ai"):
                continue

            # Convert owner to writer permission
            permission_copy = permission.copy()
            permission_copy["role"] = "writer"
        else:
            # Skip if it's the service account permission (already has access)
            if (
                permission.get("emailAddress")
                == "<EMAIL>"
            ):
                continue

            # Skip if it's a flashdocs.ai account
            email_address = permission.get("emailAddress", "")
            if email_address.endswith("@flashdocs.ai"):
                continue

            permission_copy = permission.copy()

        # Handle domain permissions - ensure domain field is present for domain type
        if permission_copy.get("type") == "domain" and not permission_copy.get(
            "domain"
        ):
            logging.warning(
                f"Skipping domain permission without domain field: {permission_copy}"
            )
            continue

        # Remove fields that can't be used in create request
        if "id" in permission_copy:
            del permission_copy["id"]
        if "kind" in permission_copy:
            del permission_copy["kind"]

        # Apply permission with retry logic
        def apply_permission(permission_copy=permission_copy):
            return (
                drive.permissions()
                .create(
                    fileId=slides_file_id,
                    body=permission_copy,
                    sendNotificationEmail=False,
                    supportsAllDrives=True,
                )
                .execute()
            )

        try:
            retry_operation(
                apply_permission,
                max_retries=3,
                base_delay=1.0,
                operation_name=f"apply permission {permission_copy.get('emailAddress', 'unknown')}",
                log_success=True,
                success_message=f"Applied permission {permission_copy} to slides {slides_file_id}",
            )
        except Exception as e:
            logging.warning(
                f"Failed to apply permission {permission_copy} to slides {slides_file_id} after all retries: {e}"
            )


def upload_cloudfront_pptx_file_to_google_drive(cloudfront_url: str) -> str:
    """Upload a PPTX file from CloudFront to Google Drive and convert to Google Slides."""
    temp_file_path = None

    try:
        # Extract filename from URL
        file_name = cloudfront_url.split("?")[0].split("/")[-1]

        # Validate file type
        if not file_name.lower().endswith(".pptx"):
            raise ValueError("File must be a PPTX file")

        # Clean URL by removing fragments
        clean_url = urlsplit(cloudfront_url).geturl().split("#", 1)[0]

        # Download the PPTX file
        logging.info(f"Downloading PPTX file from {clean_url}")
        with requests.get(clean_url, stream=True, timeout=60) as response:
            response.raise_for_status()
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pptx") as temp_file:
                for chunk in response.iter_content(chunk_size=1 << 16):
                    temp_file.write(chunk)
                temp_file_path = temp_file.name

        # Initialize Google Drive client
        logging.info("Initializing Google Drive client")
        creds = service_account.Credentials.from_service_account_file(
            ".credentials/google_service_account_key.json",
            scopes=["https://www.googleapis.com/auth/drive"],
        )
        drive = build("drive", "v3", credentials=creds, cache_discovery=False)

        # Prepare file metadata and media
        metadata = {
            "name": file_name,
            "mimeType": "application/vnd.google-apps.presentation",
        }
        media = MediaFileUpload(
            temp_file_path,
            mimetype="application/vnd.openxmlformats-officedocument.presentationml.presentation",
            resumable=True,
        )

        # Upload and convert to Google Slides
        logging.info("Uploading file to Google Drive")
        file = (
            drive.files()
            .create(body=metadata, media_body=media, fields="id,webViewLink")
            .execute()
        )

        # Set public permissions
        logging.info("Setting public permissions")
        drive.permissions().create(
            fileId=file["id"],
            body={
                "role": "writer",
                "type": "anyone",
            },
            fields="id",
        ).execute()

        logging.info(
            f"Successfully uploaded and converted file to Google Slides: {file['webViewLink']}"
        )
        return file["webViewLink"]

    except requests.exceptions.RequestException as e:
        logging.error(f"Error downloading file from CloudFront: {str(e)}")
        raise
    except googleapiclient.errors.HttpError as e:
        logging.error(f"Google Drive API error: {str(e)}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logging.info("Cleaned up temporary file")
            except Exception as e:
                logging.warning(f"Failed to remove temporary file: {str(e)}")
