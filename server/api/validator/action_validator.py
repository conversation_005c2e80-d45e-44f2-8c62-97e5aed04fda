import logging
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from django.db.models import Model
from django.forms.models import model_to_dict
from google.protobuf.json_format import ParseDict
from pydantic import BaseModel, ConfigDict, Field, ValidationError, validator

from ..models import Action
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionStatusType,
    ActionType,
    TofuDataList,
    TofuDataType,
)


class ActionValidation(BaseModel):
    id: int
    creator: int
    playbook: int
    campaign: int
    action_name: str
    action_category: str
    inputs: Dict[str, Any]
    outputs: Dict[str, Any]
    status: Dict[str, Any] = Field(default_factory=dict)
    incoming_actions: List[Any] = Field(default_factory=list)

    @validator("inputs", "outputs")
    def validate_tofu_data_list(cls, v):
        for key, value in v.items():
            try:
                # Parse the dictionary directly into a TofuDataList message
                ParseDict(value, TofuDataList())
            except Exception as e:
                raise ValueError(f"Invalid TofuDataList format for key {key}: {str(e)}")
        return v

    @validator("action_category")
    def validate_action_category(cls, v):
        try:
            ActionCategory.Value(v)
            return v
        except ValueError as e:
            raise ValueError(f"Invalid ActionCategory: {str(e)}")

    @validator("incoming_actions")
    def validate_incoming_actions(cls, v):
        if not isinstance(v, list):
            raise ValueError("incoming_actions must be a list")

        if not all(isinstance(action, Action) for action in v):
            raise ValueError("All items in incoming_actions must be Action instances")

        return v

    class Config:
        extra = "forbid"


def validate_action(action_instance):
    """
    Validates an Action instance against the schema defined in protobuf and pydantic models.

    Args:
        action_instance: The Action model instance to validate

    Returns:
        str: Error message if validation fails, None otherwise
    """
    try:
        ActionValidation(**model_to_dict(action_instance))
    except ValidationError as e:
        error = f"Action {action_instance} validation error: {e}"
        return error
    except Exception as e:
        error = f"Action {action_instance} unexpected error: {e}"
        return error
