import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from django.forms.models import model_to_dict
from pydantic import (
    BaseModel,
    Field,
    ValidationError,
    constr,
    root_validator,
    validator,
)


class CustomInstructionValidation(BaseModel):
    saved_instructions: Optional[Dict[str, List[Union[str, Dict[str, Any]]]]] = None


class PlaybookValidation(BaseModel):
    id: int
    users: List[Any]
    name: str
    company_domain: Optional[str] = None
    company_info: dict
    company_info_expanded: Optional[dict] = None
    target_info: dict
    target_info_expanded: Optional[dict] = None
    assets: dict
    assets_expanded: Optional[dict] = None
    custom_instructions: Optional[CustomInstructionValidation] = None
    company_object: Any
    settings: dict

    class Config:
        extra = "forbid"


# external interfaces for validations
def validate_playbook(playbook_instance):
    try:
        PlaybookValidation(**model_to_dict(playbook_instance))
    except ValidationError as e:
        error = f"Playbook {playbook_instance} validation error: {e}"
        return error
