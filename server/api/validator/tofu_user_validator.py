import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from django.forms.models import model_to_dict
from pydantic import (
    BaseModel,
    EmailStr,
    Field,
    ValidationError,
    constr,
    model_validator,
    root_validator,
    validator,
)

from ..admin import TofuUserForm


class TofuUserValidation(BaseModel):
    username: str = Field(..., max_length=150)
    full_name: Optional[str] = Field(None, max_length=150)
    first_name: Optional[str] = Field(None, max_length=150)
    last_name: Optional[str] = Field(None, max_length=150)
    email: Optional[EmailStr] = None
    context: Optional[Dict] = Field(default_factory=dict)
    is_staff: bool = Field(default=False)
    is_active: bool = Field(default=True)
    date_joined: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        extra = "forbid"

    @model_validator(mode="after")
    def check_model(cls, values):
        # check context
        user_context = values.context
        if not user_context:
            return True
        model_context = user_context.get("model", "")
        if model_context and model_context not in TofuUserForm.MODEL_CHOICES:
            raise ValueError(f"Invalid model context: {model_context}")
        model_context_for_repurpose = user_context.get("model_for_repurpose", "")
        if (
            model_context_for_repurpose
            and model_context_for_repurpose
            not in TofuUserForm.MODEL_CHOICES_FOR_REPURPOSE
        ):
            raise ValueError(
                f"Invalid model context for repurpose: {model_context_for_repurpose}"
            )
        scraper_context = user_context.get("webOverrides", {}).get("scraper", "")
        if scraper_context and scraper_context not in TofuUserForm.SCRAPER_CHOICES:
            raise ValueError(f"Invalid scraper context: {scraper_context}")


# external interfaces for validations
def validate_tofu_user(tofu_user_instance):
    try:
        TofuUserValidation(**model_to_dict(tofu_user_instance))
    except ValidationError as e:
        error = f"User {tofu_user_instance} validation error: {e}"
        return error
