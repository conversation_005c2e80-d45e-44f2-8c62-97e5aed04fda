import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from django.forms.models import model_to_dict
from pydantic import (
    BaseModel,
    Field,
    RootModel,
    ValidationError,
    constr,
    root_validator,
)

from ..shared_types import ContentSourceFormat, ContentSourceUploadMethod, ContentType


class Assets(RootModel):
    root: Dict[str, Union[List[str], None]]


class ContentCollectionInstructionAssets(RootModel):
    root: Union[List[Dict[str, str]], Dict[str, str]]


class ContentCollectionInstructionValidation(BaseModel):
    assets: Optional[ContentCollectionInstructionAssets] = None
    instruction: str = Field(..., title="Instruction")

    class Config:
        title = "ContentCollectionInstructionValidation"
        extra = "forbid"


class ContentCollectionValidation(BaseModel):
    id: Optional[str] = Field(default=None, title="Id")
    name: Optional[str] = Field(
        default=None, title="Name"
    )  # not set for several content_groups
    next: Optional[List[int]] = None
    prev: Optional[List[int]] = None
    content_collection_instructions: Optional[
        List[ContentCollectionInstructionValidation]
    ] = None
    custom_instructions: Optional[
        Union[List[Union[str, Dict[str, Any]]], Dict[str, Any]]
    ] = None
    customInstructions: Optional[
        Union[List[Union[str, Dict[str, Any]]], Dict[str, Any]]
    ] = None
    numOfContent: Optional[Union[int, str]] = None
    isDummyContentCollectionContentGroup: Optional[bool] = None

    class Config:
        title = "ContentCollectionValidation"
        extra = "forbid"


class GenStatusValidation(BaseModel):
    status: Optional[str] = Field(default=None, title="Status")
    contents: Optional[Dict[str, Any]] = Field(default=None, title="Contents")
    error: Optional[str] = Field(default=None, title="Error")
    task_id: Optional[str] = Field(default=None, title="Task Id")
    update_time: Optional[str] = Field(default=None, title="Update Time")
    comment: Optional[str] = Field(default=None, title="Comment")

    class Config:
        title = "GenStatusValidation"
        extra = "forbid"


class ContentParamsValidation(BaseModel):
    assets: Optional[Assets] = Field(default=None, title="Assets")
    content_collection: Optional[ContentCollectionValidation] = Field(default=None)
    content_source: Optional[str] = Field(default=None, title="Content Source")
    content_source_copy: Optional[str] = Field(
        default=None, title="Content Source Copy"
    )
    content_source_format: Optional[ContentSourceFormat] = Field(
        default=None, title="Content Source Format"
    )
    content_source_upload_method: Optional[ContentSourceUploadMethod] = Field(
        default=None, title="Content Source Upload Method"
    )
    content_type: Optional[ContentType] = Field(default=None, title="Content Type")
    custom_instructions: Optional[List[Dict[str, Any]]] = Field(
        default=None, title="Custom Instructions"
    )
    export_content_source_copy: Optional[str] = Field(
        default=None, title="Export Content Source Copy"
    )
    foundation_model: Optional[str] = Field(default=None, title="Foundation Model")
    gen_status: Optional[Union[GenStatusValidation, str]] = Field(
        default=None, title="Gen Status"
    )
    generate_status: Optional[str] = Field(default=None, title="Generate Status")
    initialSetup: Optional[bool] = Field(default=None, title="Initialsetup")
    no_rename_alert: Optional[bool] = Field(default=None, title="No Rename Alert")
    orig_content_id: Optional[int] = Field(default=None, title="Orig Content Id")
    prompts: Optional[List[str]] = Field(default=None, title="Prompts")
    repurpose_template_content_source_copy: Optional[str] = Field(
        default=None, title="Repurpose Template Content Source Copy"
    )
    slate_repurpose_template_content_source: Optional[str] = Field(
        default=None, title="Slate Repurpose Template Content Source"
    )
    slate_repurpose_template_content_source_copy: Optional[str] = Field(
        default=None, title="Slate Repurpose Template Content Source Copy"
    )
    status: Optional[str] = Field(default=None, title="Status")
    subject_line_only_content_source: Optional[str] = Field(default=None)
    subject_line_only_content_source_copy: Optional[str] = Field(default=None)
    target_params: Optional[Dict[str, Any]] = Field(default=None, title="Target Params")
    targets: Optional[Dict[str, Any]] = Field(default=None, title="Targets")
    template: Optional[Dict[str, Any]] = Field(default=None, title="Template")
    template_instructions: Optional[List[Dict[str, Any]]] = Field(
        default=None, title="Template Instructions"
    )
    template_settings: Optional[Any] = Field(default=None, title="Template Settings")
    template_content_source: Optional[str] = Field(
        default=None, title="Template Content Source"
    )
    template_content_source_copy: Optional[str] = Field(
        default=None, title="Template Content Source Copy"
    )
    email_template_content_source: Optional[str] = None
    email_template_content_source_copy: Optional[str] = None

    class Config:
        extra = "forbid"


class ContentStatusValidation(BaseModel):
    gen_status: Optional[dict] = None  # TODO: extend this further

    class Config:
        extra = "forbid"


class ComponentMetaValidation(BaseModel):
    time_added: int
    type: Optional[str] = None


class ComponentValidation(BaseModel):
    meta: dict
    text: Optional[str] = None
    image: Optional[dict] = None

    class Config:
        extra = "forbid"


class ContentValidation(BaseModel):
    id: Optional[int] = None
    creator: Any
    playbook: Any
    content_group: Optional[Any] = None
    content_name: Optional[str] = None
    content_params: Optional[ContentParamsValidation] = (
        None  # It's None for several contents
    )
    content_status: Optional[ContentStatusValidation] = None
    components: Optional[dict] = None

    class Config:
        extra = "forbid"


def validate_content(content_instance):
    try:
        ContentValidation(**model_to_dict(content_instance))
    except ValidationError as e:
        error = f"Content {content_instance} validation error: {e}"
        return error
