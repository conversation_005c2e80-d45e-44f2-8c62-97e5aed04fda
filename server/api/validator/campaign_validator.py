import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from django.forms.models import model_to_dict
from pydantic import BaseModel, Field, ValidationError, constr, root_validator

from .content_validator import Assets


# campaign check
class CampaignGoal(str, Enum):
    Personalization = "Personalization"
    Repurposing = "Repurpose Content"
    Workflow = "Workflow"
    General = "General Campaign"
    NotSet = ""
    SeqPersonalizeTemplate = "Sequence Personalize Template"


class CampaignStage(str, Enum):
    Default = ""
    Start = "Start"
    SetContent = "SetContent"
    SetComponent = "SetComponent"
    SetTarget = "SetTarget"
    Generate = "Generate"
    Export = "Export"


class ContentGenStatus(BaseModel):
    status: str


class ContentGroupGenStatus(BaseModel):
    status: str
    contents: Dict[str, ContentGenStatus]


class GenStatus(BaseModel):
    status: str
    content_groups: Dict[str, ContentGroupGenStatus]


class InboundLandingPagesSettings(BaseModel):
    enabled: bool
    target_field: str


class CampaignParamsValidation(BaseModel):
    targets: List[Dict[str, List[str]]]
    assets: Optional[Assets] = None
    foundation_model: Optional[str] = None
    custom_instructions: Optional[Union[List[str], List[Dict[str, Any]]]] = None
    campaign_goal: Optional[CampaignGoal] = None
    campaign_stage: Optional[CampaignStage] = None
    num_of_variations: Optional[int] = None
    gen_status: Optional[GenStatus] = None  # TODO: extend this further
    allSelectedTargets: Optional[List[str]] = None
    enable_auto_sync: Optional[bool] = None
    orig_campaign_id: Optional[int] = None
    suggested_campaign: Optional[bool] = None
    suggested_campaign_desc: Optional[str] = None

    inbound_landing_pages: Optional[InboundLandingPagesSettings] = None
    inbound_landing_page: Optional[InboundLandingPagesSettings] = None
    campaign_template: Optional[Dict] = None

    targets_concat: Optional[bool] = None
    is_campaign_v3: Optional[bool] = None
    campaign_name: Optional[str] = None  # this shouldn't be here

    class Config:
        extra = "forbid"


class CampaignStatusValidation(BaseModel):
    gen_status: Optional[dict] = None  # TODO: extend this further
    clone_status: Optional[dict] = None  # TODO: extend this further

    class Config:
        extra = "forbid"


class CampaignValidation(BaseModel):
    id: Optional[int] = None
    creator: Any
    playbook: Any
    campaign_name: Optional[str] = None
    campaign_params: CampaignParamsValidation
    campaign_status: Optional[CampaignStatusValidation] = None

    class Config:
        extra = "forbid"


def validate_campaign(campaign_instance):
    try:
        CampaignValidation(**model_to_dict(campaign_instance))
    except ValidationError as e:
        error = f"Campaign {campaign_instance} validation error: {e}"
        return error
