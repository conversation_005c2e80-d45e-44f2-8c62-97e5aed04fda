import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from django.forms.models import model_to_dict
from pydantic import BaseModel, Field, ValidationError, constr, root_validator


class ContentVariationValidation(BaseModel):
    id: int
    content: Any
    params: Optional[dict] = None
    variations: Optional[dict] = None  # TODO: make sure this aligns with components

    class Config:
        extra = "forbid"


def validate_content_variation(content_variation_instance):
    try:
        ContentVariationValidation(**model_to_dict(content_variation_instance))
    except ValidationError as e:
        error = f"ContentVariation {content_variation_instance} validation error: {e}"
        return error
