from datetime import datetime
from typing import Dict, Optional, Union

from django.forms.models import model_to_dict
from pydantic import BaseModel, Field, ValidationError, validator


class TargetInfoGroupValidation(BaseModel):
    id: int
    playbook: int
    target_info_group_key: str = Field(..., max_length=255, min_length=1)
    meta: Optional[Dict] = None
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        extra = "forbid"


def validate_target_info_group(target_info_group_instance):
    try:
        TargetInfoGroupValidation(**model_to_dict(target_info_group_instance))
    except ValidationError as e:
        error = f"TargetInfoGroup {target_info_group_instance} validation error: {e}"
        return error


class TargetInfoValidation(BaseModel):
    id: int
    target_info_group: int
    target_key: str = Field(..., max_length=255, min_length=1)
    docs: Optional[Dict] = None
    meta: Optional[Dict] = None
    summary: Optional[str] = None
    index: Optional[Union[Dict, str]] = None
    value_prop: Optional[str] = None
    additional_info: Optional[Dict] = None
    docs_last_build: Optional[Dict] = None
    docs_build_status: Optional[Dict] = None
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        extra = "forbid"


def validate_target_info(target_info_instance):
    try:
        TargetInfoValidation(**model_to_dict(target_info_instance))
    except ValidationError as e:
        error = f"TargetInfo {target_info_instance} validation error: {e}"
        return error
