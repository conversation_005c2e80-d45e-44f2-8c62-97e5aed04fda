import logging
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from django.forms.models import model_to_dict
from pydantic import (
    BaseModel,
    Field,
    HttpUrl,
    ValidationError,
    constr,
    model_validator,
    validator,
)

from ..shared_types import ContentSourceFormat, ContentSourceUploadMethod, ContentType
from .content_validator import (
    Assets,
    ComponentValidation,
    ContentCollectionInstructionValidation,
    ContentParamsValidation,
    ContentSourceFormat,
    ContentSourceUploadMethod,
    ContentType,
)


class CustomInstruction(BaseModel):
    assets: Optional[Assets] = None
    instruction: Optional[str] = None

    @model_validator(mode="after")
    def check_assets_or_instruction(cls, values):
        if not values.assets and not values.instruction:
            raise ValueError("Either assets or instruction must be provided")
        return values

    class Config:
        extra = "forbid"


class ComponentType(str, Enum):
    UNSPECIFIED = "unspecified"
    EMAIL_SUBJECT = "email subject"
    EMAIL_BODY = "email body"
    HTML_EDIT = "edited"
    SMART = "smart"
    HEADLINE = "headline"
    DESCRIPTION = "description"
    INTRODUCTORY_TEXT = "introductory-text"
    AD_COPY = "ad-copy"
    BODY = "body"  # TODO: check the logic about this
    INTRODUCTORY = "introductory"  # TODO: check the logic about this


class ComponentMetaType(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    LINK = "link"
    ANCHOR = "anchor"
    VIDEO = "video"  # Add video type


class ComponentParams(BaseModel):
    assets: Optional[Assets] = None
    custom_instructions: Optional[List[CustomInstruction]] = None

    class Config:
        extra = "forbid"


class ContentGroupComponentMeta(BaseModel):
    # Updated to include component_params
    time_added: Optional[int] = None
    component_type: Optional[ComponentType] = None
    isEmailSubject: Optional[bool] = None
    precedingContent: Optional[str] = None
    succeedingContent: Optional[str] = None
    html_tag: Optional[str] = None
    preceding_element: Optional[str] = None
    succeeding_element: Optional[str] = None
    # Fields for more specific meta types
    pageNum: Optional[int] = None
    numLines: Optional[int] = None
    boundingBox: Optional[Dict[str, Union[int, float]]] = None
    avgCharWidth: Optional[float] = None
    avgLineSpace: Optional[float] = None
    charCapacity: Optional[int] = None
    avgCharHeight: Optional[float] = None
    html_tag_index: Optional[int] = None
    component_params: Optional[ComponentParams] = None
    type: Optional[ComponentMetaType] = None
    selected_element: Optional[str] = None
    template_field_name: Optional[str] = None
    order: Optional[int] = None
    smart_replace_source: Optional[int] = None

    class Config:
        extra = "forbid"


class ContentCollectionCustomInstruction(BaseModel):
    allContents: Optional[List[CustomInstruction]] = None

    class Config:
        extra = "forbid"


class ContentGroupContentCollectionValidation(BaseModel):
    id: Optional[Union[str, int]] = None
    name: Optional[str] = Field(
        default=None, title="Name"
    )  # not set for several content_groups
    next: Optional[Union[List[int], int]] = None
    prev: Optional[Union[List[int], int]] = None
    content_collection_instructions: Optional[
        List[ContentCollectionInstructionValidation]
    ] = None
    custom_instructions: Optional[
        Union[ContentCollectionCustomInstruction, List[CustomInstruction]]
    ] = None
    numOfContent: Optional[int] = None
    isDummyContentCollectionContentGroup: Optional[bool] = None

    customInstructions: Optional[ContentCollectionCustomInstruction] = (
        None  # what is this?
    )

    class Config:
        title = "ContentGroupContentCollectionValidation"
        extra = "forbid"

    @model_validator(mode="after")
    def check_exists(cls, values):
        return values

        # either there are data or all are empty
        if values.isDummyContentCollectionContentGroup:
            if any(
                [
                    values.id,
                    values.next,
                    values.prev,
                    values.content_collection_instructions,
                    values.custom_instructions,
                ]
            ):
                raise ValueError(
                    "ContentGroupContentCollectionValidation must have all fields empty if isDummyContentCollectionContentGroup is True"
                )
            if values.numOfContent != 1:
                raise ValueError(
                    "ContentGroupContentCollectionValidation must have numOfContent equal to 1 if isDummyContentCollectionContentGroup is True"
                )
        else:
            if not all(
                [
                    values.id,
                    values.name,
                    values.next or values.prev,
                    values.content_collection_instructions,
                    # values.numOfContent,
                ]
            ):
                raise ValueError(
                    "ContentGroupContentCollectionValidation must have all fields filled if isDummyContentCollectionContentGroup is False"
                )
        return values


class ContentGroupImageComponent(BaseModel):
    width: Optional[int] = None
    height: Optional[int] = None
    url: Optional[str] = None
    alt: Optional[str] = None
    srcset: Optional[str] = None
    loading: Optional[str] = None
    styles: Optional[Dict[str, Any]] = None

    class Config:
        extra = "forbid"


class ContentGroupLinkType(str, Enum):
    TOFU_LINK = "tofu_link"
    EXTERNAL_LINK = "external_link"


class ContentGroupLinkComponent(BaseModel):
    text: Optional[str] = None
    href: Optional[str] = None
    path: Optional[str] = None  # TODO: check this
    type: Optional[ContentGroupLinkType] = None
    label: Optional[str] = None
    contentGroupId: Optional[int] = None
    url: Optional[str] = None

    class Config:
        extra = "forbid"


class ContentGroupVideoComponent(BaseModel):
    url: str
    title: Optional[str] = None

    class Config:
        extra = "forbid"


class ContentGroupComponent(BaseModel):
    meta: ContentGroupComponentMeta
    text: Optional[str] = None
    image: Optional[ContentGroupImageComponent] = None
    link: Optional[ContentGroupLinkComponent] = None
    anchor: Optional[Dict[str, Any]] = None
    video: Optional[ContentGroupVideoComponent] = None  # Add video field

    class Config:
        extra = "forbid"


class ExportDestination(str, Enum):
    HubSpot = "hubspot"
    Marketo = "marketo"
    Salesforce = "salesforce"
    LinkedIn = "linkedin"
    Other = "other"
    Download = "download"


class ExportType(str, Enum):
    Static = "static"
    Dynamic = "dynamic"
    Embed = "embed"

    # shall be invalid values
    Text = "text"
    Empty = ""


class UrlSetting(BaseModel):
    domain: Optional[str] = None
    domainList: Optional[List[str]] = None
    groupSlug: Optional[str] = None
    downloadURLs: Optional[bool] = None
    hubspotURLToken: Optional[str] = None
    marketoURLToken: Optional[str] = None
    exportURLs2Hubspot: Optional[bool] = None
    exportURLs2Marketo: Optional[bool] = None
    salesforceURLToken: Optional[str] = None
    additionalURLParams: Optional[str] = None

    class Config:
        extra = "forbid"


class EmbedSetting(BaseModel):
    tofu_content_id: Optional[int] = None

    class Config:
        extra = "forbid"


class ImportObjectType(str, Enum):
    CONTACT = "contact"
    COMPANY = "company"


class MarketoIds(BaseModel):
    lead_id: Optional[Union[str, int]] = None
    lead_ids: Optional[List[Union[str, int]]] = None
    object_type: Optional[ImportObjectType] = None
    object_identifier: Optional[str] = None

    # from https://api.tofuhq.com/admin/api/contentgroup/144155/change/
    email: Optional[str] = None
    cookies: Optional[str] = None
    department: Optional[str] = None

    class Config:
        extra = "forbid"


class TargetSetting(BaseModel):
    pageSlug: Optional[str] = None
    contentId: Optional[int] = None
    pageTitle: Optional[str] = None
    isCloneForm: Optional[bool] = None
    exportStatus: Optional[str] = None
    lastExportAt: Optional[int] = None
    targetLabels: Optional[List[str]] = None
    isExportTarget: Optional[bool] = None
    # Add new fields
    hubIds: Optional[Dict[str, str]] = None  # TODO: update this to real data
    salesforceIds: Optional[Dict[str, str]] = None  # TODO: update this to real data
    marketoIds: Optional[Any] = None  # Optional[MarketoIds] = None
    draftURL: Optional[str] = None
    targetNames: Optional[List[str]] = None
    urlExportStatus: Optional[str] = None
    hasJustBeenExported: Optional[bool] = None
    emailName: Optional[str] = None
    targetListNames: Optional[List[str]] = None
    errorMessage: Optional[str] = None

    class Config:
        extra = "forbid"


class EmailType(str, Enum):
    AUTOMATED = "automated"
    REGULAR = "regular"


class ExportAdvancedSetting(BaseModel):
    emailType: Optional[EmailType] = None
    emailFooter: Optional[bool] = None

    class Config:
        extra = "forbid"


class ExportSetting(BaseModel):
    exportType: Optional[ExportType] = None
    urlSetting: Optional[UrlSetting] = None
    destination: Optional[ExportDestination] = None
    embedSetting: Optional[EmbedSetting] = None
    targetsSetting: Optional[List[TargetSetting]] = None
    componentsSetting: Optional[Dict[str, str]] = None
    advancedSetting: Optional[ExportAdvancedSetting] = None  # what is this?

    class Config:
        extra = "forbid"


class HubspotExportResponse(BaseModel):
    id: Optional[int] = None

    class Config:
        extra = "allow"  # there's a lot of extra fields in the response


class MarketoExportResponse(BaseModel):
    id: Optional[int] = None

    class Config:
        extra = "allow"  # there's a lot of extra fields in the response


class ExportResponse(BaseModel):
    hubspot: Optional[HubspotExportResponse] = None
    marketo: Optional[MarketoExportResponse] = None
    id: Optional[int] = None

    class Config:
        extra = "forbid"


class ExportSettings(BaseModel):
    hubspot: Optional[Dict[str, Dict[str, ExportSetting]]] = None
    other: Optional[Dict[str, Dict[str, ExportSetting]]] = None
    download: Optional[Dict[str, Any]] = None  # TODO: check this
    salesforce: Optional[Dict[str, Dict[str, ExportSetting]]] = None
    marketo: Optional[Dict[str, Dict[str, ExportSetting]]] = None

    exportDestination: Optional[ExportDestination] = None  # why is this here?
    exportType: Optional[ExportType] = None  # why is this here?
    alternateExportType: Optional[ExportType] = None  # what is this?
    alternateExportDestination: Optional[ExportDestination] = None  # what is this?
    download_text: Optional[str] = None  # what is this?
    destination: Optional[ExportDestination] = None  # why is this here?

    class Config:
        extra = "forbid"


class ToneReference(BaseModel):
    assets: Optional[Assets] = None


class TemplateSettings(BaseModel):
    follow_tone: Optional[bool] = None
    follow_length: Optional[bool] = None
    tone_reference: Optional[List[ToneReference]] = None
    follow_core_message_and_key_point: Optional[bool] = None

    class Config:
        extra = "forbid"


class ReviewedContent(BaseModel):
    content_id: Optional[int] = None
    reviewed_time: Optional[str] = None

    class Config:
        extra = "forbid"


class Template(BaseModel):
    type: Optional[str] = None
    numOfVariationPerGen: Optional[int] = None  # TODO: this is not used anywhere


class ContentGroupParamsValidation(BaseModel):
    assets: Optional[Dict[str, List[str]]] = None
    targets: Optional[Union[List[Dict[str, List[str]]], Dict[str, str]]] = None
    prompts: Optional[Union[List[str], List[Dict[str, str]]]] = None
    custom_instructions: Optional[Union[List[CustomInstruction], Dict[str, Any]]] = None
    template_instructions: Optional[
        Union[CustomInstruction, List[CustomInstruction], Dict[str, Any]]
    ] = None

    content_type: Optional[ContentType] = None
    content_source: Optional[str] = None
    content_source_copy: Optional[str] = None
    content_source_format: Optional[ContentSourceFormat] = None
    content_source_upload_method: Optional[ContentSourceUploadMethod] = None
    foundation_model: Optional[str] = None
    reviewed_content_list: Optional[List[ReviewedContent]] = None

    # legacy
    last_task_id: Optional[Any] = None
    last_task_status: Optional[Any] = None
    status: Optional[Any] = None

    gen_status: Optional[Dict[str, Any]] = None  # TODO: delete this
    initialSetup: Optional[bool] = None
    # export_response: Optional[ExportResponse] = None
    export_response: Optional[Any] = None
    export_settings: Optional[ExportSettings] = None

    stale: Optional[bool] = None
    export_content_source_copy: Optional[str] = None
    subject_line_only_content_source: Optional[str] = None
    subject_line_only_content_source_copy: Optional[str] = None
    slate_repurpose_template_content_source: Optional[str] = None
    slate_repurpose_template_content_source_copy: Optional[str] = None
    repurpose_template_content_source: Optional[str] = None
    repurpose_template_content_source_copy: Optional[str] = None
    template_content_source: Optional[str] = None
    template_content_source_copy: Optional[str] = None
    email_template_content_source: Optional[str] = None
    email_template_content_source_copy: Optional[str] = None

    template: Optional[Template] = None
    template_settings: Optional[TemplateSettings] = None

    orig_content_group_id: Optional[int] = None
    content_collection: Optional[ContentGroupContentCollectionValidation] = Field(
        default=None
    )
    hideFromCampaign: Optional[bool] = None
    no_rename_alert: Optional[bool] = None
    linkedin_ads_v2: Optional[bool] = None
    hubspotEmailId: Optional[str] = None

    content_group_name: Optional[str] = None  # TODO: delete this
    id: Optional[Any] = None  # TODO: delete this
    creator: Optional[Any] = None  # TODO: delete this
    campaign: Optional[Any] = None  # TODO: delete this
    content_group_params: Optional[Any] = None  # TODO: delete this
    components: Optional[Any] = None  # TODO: delete this

    contents: Optional[List[Dict[str, Any]]] = None  # what is this?

    orig_content_id: Optional[int] = None  # TODO: delete this

    positive_example_contents: Optional[List[int]] = None

    # need to check
    hasAnalysisRun: Optional[bool] = None

    class Config:
        extra = "forbid"


class ContentGroupStatusValidation(BaseModel):
    gen_status: Optional[dict] = None  # TODO: extend this further
    status: Optional[str] = None

    class Config:
        extra = "forbid"


class ContentGroupValidation(BaseModel):
    id: Optional[int] = None
    creator: Any
    campaign: Any
    content_group_name: Optional[str] = None
    content_group_params: Optional[ContentGroupParamsValidation] = None
    content_group_status: Optional[ContentGroupStatusValidation] = None
    components: Optional[Dict[str, ContentGroupComponent]] = None

    class Config:
        extra = "forbid"

    @model_validator(mode="after")
    def check_next_not_equal_id(cls, values):
        return values

        content_group_id = values.id
        content_group_params = values.content_group_params

        if not content_group_params or not (
            content_collection_data := content_group_params.content_collection
        ):
            return values

        next_ids = content_collection_data.next
        if next_ids:
            if content_group_id in next_ids:
                raise ValueError(
                    "ContentGroupContentCollectionValidation.next cannot contain ContentGroupValidation.id"
                )

        prev_ids = content_collection_data.prev
        if prev_ids:
            if content_group_id in prev_ids:
                raise ValueError(
                    "ContentGroupContentCollectionValidation.prev cannot contain ContentGroupValidation.id"
                )

        # check that next and prev are not the same
        if next_ids and prev_ids:
            if set(next_ids) & set(prev_ids):
                raise ValueError(
                    "ContentGroupContentCollectionValidation.next and ContentGroupContentCollectionValidation.prev cannot contain the same ids"
                )

        # check instruction include current content group name
        content_group_name = values.content_group_name
        content_collection_instructions = (
            content_collection_data.content_collection_instructions
        )
        if content_collection_instructions:
            for instruction in content_collection_instructions:
                if content_group_name not in instruction.instruction:
                    raise ValueError(
                        "ContentGroupContentCollectionValidation.content_collection_instructions must contain content group name"
                    )

        return values


def validate_content_group(content_group):
    try:
        ContentGroupValidation(**model_to_dict(content_group))
    except ValidationError as e:
        error = f"ContentGroup {content_group} validation error: {e}"
        return error
