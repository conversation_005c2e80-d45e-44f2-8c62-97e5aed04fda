from datetime import datetime
from typing import Dict, Optional, Union

from django.forms.models import model_to_dict
from pydantic import BaseModel, Field, ValidationError, validator


class AssetInfoGroupValidation(BaseModel):
    id: int
    playbook: int
    asset_info_group_key: str = Field(..., max_length=255, min_length=1)
    meta: Optional[Dict] = None
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        extra = "forbid"


def validate_asset_info_group(asset_info_group_instance):
    try:
        AssetInfoGroupValidation(**model_to_dict(asset_info_group_instance))
    except ValidationError as e:
        error = f"AssetInfoGroup {asset_info_group_instance} validation error: {e}"
        return error


class AssetInfoValidation(BaseModel):
    id: int
    asset_info_group: int
    asset_key: str = Field(..., max_length=255, min_length=1)
    docs: Optional[Dict] = None
    meta: Optional[Dict] = None
    summary: Optional[str] = None
    index: Optional[Union[str, Dict]] = None
    additional_info: Optional[Dict] = None
    docs_last_build: Optional[Dict] = None
    docs_build_status: Optional[Dict] = None
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        extra = "forbid"


def validate_asset_info(asset_info_instance):
    try:
        AssetInfoValidation(**model_to_dict(asset_info_instance))
    except ValidationError as e:
        error = f"AssetInfo {asset_info_instance} validation error: {e}"
        return error
