import json
import logging
import os
import time
import traceback

import boto3
from axiom import Client
from django.db import connection
from django.utils import timezone
from langsmith import Client as LangsmithClient

from .integrations.slack import send_slack_message
from .models import Campaign, Content, PublicContent
from .thread_locals import get_current_campaign, get_current_playbook, get_current_user


class TofuAxiomLogger:
    def __init__(self) -> None:
        self.axiom_client = Client()
        self.axiom_dataset = "tofu_backend"

    def remove_none_values_and_serialize_deep(self, d, depth=0):
        """
        Recursively remove None values from nested dictionaries and
        serialize dictionaries that are more than 2 levels deep
        """
        if not isinstance(d, dict):
            return d

        result = {}
        for k, v in d.items():
            if v is None:
                continue

            if isinstance(v, dict):
                if depth >= 1:  # At depth 2 or deeper, serialize to JSON string
                    result[k] = json.dumps(v)
                else:
                    result[k] = self.remove_none_values_and_serialize_deep(v, depth + 1)
            else:
                result[k] = v
        return result

    def log_axiom(self, event_type, **kwargs) -> None:
        environment = os.getenv("TOFU_ENV", default="unknown")

        # only log to axiom in production
        if environment != "production":
            return

        kwargs["environment"] = environment
        kwargs["event_type"] = event_type

        if "user_id" not in kwargs:
            # TODO: use context manager
            user = get_current_user()
            if user:
                kwargs["user_id"] = user.id
                kwargs["username"] = user.username

        if "playbook_id" not in kwargs:
            playbook = get_current_playbook()
            if playbook:
                kwargs["playbook_id"] = playbook.id

        if "campaign_id" not in kwargs:
            campaign = get_current_campaign()
            if campaign:
                kwargs["campaign_id"] = campaign.id

        try:
            # clean up None values from kwargs and serialize deep nested dicts
            # this is to save # of fields created in axiom because axiom has a limit of 1024 fields in total
            kwargs = self.remove_none_values_and_serialize_deep(kwargs)

            log_res = self.axiom_client.ingest_events(self.axiom_dataset, [kwargs])
            if not log_res.ingested:
                logging.error(f"Error: failed to log to axiom due to {log_res}")
        except Exception as e:
            logging.error(f"Axiom logging error: {e}")


tofu_axiom_logger = TofuAxiomLogger()


class TofuEventLogger:
    def __init__(self) -> None:
        pass

    def _log_event_to_db(self, event_type, **kwargs):
        user_id = kwargs.pop("user_id", None)
        playbook_id = kwargs.pop("playbook_id", None)
        campaign_id = kwargs.pop("campaign_id", None)

        try:
            with connection.cursor() as cursor:
                insert_query = """
                INSERT INTO api_eventlogs (event_type, user_id, playbook_id, campaign_id, payload, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(
                    insert_query,
                    [
                        event_type,
                        user_id,
                        playbook_id,
                        campaign_id,
                        json.dumps(kwargs),
                        timezone.now(),
                    ],
                )
            # we need user_id to do some attribution since we may use admin accounts to run on other playbooks
            if not user_id:
                logging.error(
                    f"User id is not set for event: {event_type} with payload {kwargs}\n{traceback.format_stack()}"
                )
        except Exception as e:
            logging.error(f"Database logging error for events: {e}")

    def log_event(self, event_type, content=None, campaign=None, **kwargs) -> None:
        if not content and not campaign:
            logging.error("Campaign or content must be provided for event logger")
        else:
            if content:
                kwargs["content_id"] = content.id
                campaign = content.content_group.campaign
            kwargs["campaign_id"] = campaign.id
            kwargs["playbook_id"] = campaign.playbook.id

            # it may not be consistent with playbook user, but we need to log the user who actually did the action
            # TODO: use context manager
            cur_user = get_current_user()
            if cur_user:
                kwargs["user_id"] = cur_user.id
            else:
                kwargs["user_id"] = campaign.creator.id

        self._log_event_to_db(event_type, **kwargs)
        tofu_axiom_logger.log_axiom(event_type, **kwargs)


tofu_event_logger = TofuEventLogger()


def log_token_usage(content, llm_outputs, **kwargs):
    try:
        if "token_usage" in llm_outputs:
            # Initialize an empty dictionary for token usage kwargs
            token_usage_kwargs = {}

            if isinstance(llm_outputs.get("token_usage"), dict):
                token_usage_kwargs.update(llm_outputs["token_usage"])
            else:
                raise Exception(
                    "token_usage is not a dictionary: {llm_outputs['token_usage']}"
                )
            token_usage_kwargs["model_name"] = llm_outputs["model_name"]
            token_usage_kwargs.update(kwargs)

            # Logging the event
            tofu_event_logger.log_event(
                event_type="token_usage",
                content=content,
                **token_usage_kwargs,
            )
        else:
            # Claude has empty llm_outputs
            pass
    except Exception as e:
        # Handle any exceptions that might occur
        print(f"An error occurred when log_token_usage: {e}")


def log_content_gen(content, **kwargs):
    tofu_event_logger.log_event(
        event_type="content_gen",
        content=content,
        **kwargs,
    )


def log_campaign_creation(campaign, **kwargs):
    tofu_event_logger.log_event(
        event_type="campaign_creation",
        campaign=campaign,
        **kwargs,
    )


def log_content_creation(content, **kwargs):
    tofu_event_logger.log_event(
        event_type="content_creation",
        content=content,
        **kwargs,
    )


def ping_bad_rating(content, user, **kwargs):
    overallRating = kwargs.get("ratings", {}).get("overallRating", None)
    if overallRating is not None and overallRating < 3:
        results = kwargs.get("results", [])
        if isinstance(results, dict):
            results = [results]
        generations = {x.get("key"): x.get("result") for x in results}
        request_ids = [
            x.get("request_id") for x in results if x.get("request_id") is not None
        ]
        request_ids = list(set(request_ids))

        client = LangsmithClient()
        trace_links = []
        for request_id in request_ids:
            runs = client.list_runs(
                project_name="tofu",
                filter=f"and(eq(metadata_key, 'request_id'), eq(metadata_value, '{request_id}'))",
            )
            if not runs:
                logging.error(f"Could not find run for request_id {request_id}")
                continue
            runs = [run for run in runs if run.name == "content_gen"]
            run_ids = [run.id for run in runs]
            run_links = [
                f"- https://smith.langchain.com/o/44ba7596-5fd3-5a13-9c7f-68a1dd91b341/projects/p/d6ccf463-ce35-4590-904d-9683895ba5bf?peek={run_id}"
                for run_id in run_ids
            ]
            trace_links.extend(run_links)

        # user_to_tag = "U06EVAVQ10X" # Kern's user id
        campaign_goal = content.content_group.campaign.campaign_params.get(
            "campaign_goal", "Personalization"
        )
        campaign_goal_in_link = (
            "personalization" if campaign_goal == "Personalization" else "repurpose"
        )
        playbook = content.playbook
        owner = playbook.get_owner_or_first_user()
        owner_name = owner.username if owner else "Unknown"

        content_link = f"https://app.tofuhq.com/factory/{campaign_goal_in_link}/{content.id}?stage=content"
        message = [
            "-------------",
            f":triangular_flag_on_post:",  # Attention: <@{user_to_tag}>",
            f"A bad user rating has been received for content <{content_link}|{content.id}>",
            f"Account: *{owner_name}*",
            f"Reporter: *{user.username}*",
            f"Overall Rating: {overallRating}",
            "",
            "Detailed Ratings:",
        ]

        detailed_ratings = kwargs.get("ratings", {})
        for rating_key, rating_value in detailed_ratings.items():
            if rating_key.endswith("rating"):
                rating_name = " ".join(
                    word.capitalize() for word in rating_key[:-6].split("_")
                )
                message.append(f"- {rating_name} Rating: {rating_value}")
            else:
                message.append(f"- {rating_key.capitalize()}: {rating_value}")

        message.extend(
            [
                "",
                "Generations:",
            ]
        )

        for gen_key, gen_value in generations.items():
            message.append(f"- {gen_key}: {gen_value}")

        if trace_links:
            message.extend(
                [
                    "",
                    "Trace Links:",
                    *trace_links,
                ]
            )

        message.append("-------------")

        message = "\n".join(message)
        send_slack_message(channel="#customer-feedback", message=message)


def log_component_rating(content, user, **kwargs):
    logging.info(f"Logging component rating for content {content.id} - {kwargs}")
    tofu_event_logger.log_event(
        event_type="component_rating",
        content=content,
        **kwargs,
    )
    ping_bad_rating(content, user, **kwargs)


def log_content_rating(content, user, **kwargs):
    logging.info(f"Logging content rating for content {content.id} - {kwargs}")
    tofu_event_logger.log_event(
        event_type="content_rating",
        content=content,
        **kwargs,
    )
    ping_bad_rating(content, user, **kwargs)


def check_unique_entries(list1, list2):
    """
    Check if two lists of dictionaries contain the same unique entries, efficiently.
    This version uses frozensets to handle unhashable dictionary types.

    Args:
    - list1: First list of dictionaries.
    - list2: Second list of dictionaries.

    Returns:
    - True if both lists contain the same unique entries, False otherwise.
    """

    if not all(isinstance(item, dict) for item in list1 + list2):
        return False

    def not_all_none(d):
        return any(v is not None and v != "" for v in d.values())

    # Convert each dictionary in the lists to a frozenset of items
    set1 = {frozenset(d.items()) for d in list1 if not_all_none(d)}
    set2 = {frozenset(d.items()) for d in list2 if not_all_none(d)}

    # Compare the sets for equality
    return set1 == set2


def log_data_wrapper_mismatch(key, cur_value, wrapper_value, log_error=True):
    if cur_value == wrapper_value:
        return

    try:
        if (
            isinstance(cur_value, list)
            and isinstance(wrapper_value, list)
            and check_unique_entries(cur_value, wrapper_value)
        ):
            return
    except Exception as e:
        pass

    mismatch_source = "\n".join(traceback.format_stack()[-5:-1])

    message = f"Data wrapper mismatch for key {key}: old value {cur_value}, new value {wrapper_value}"
    tofu_axiom_logger.log_axiom(
        event_type="wrapper_equivalence_check",
        message=message,
    )
    if log_error:
        logging.error(f"{message} from:\n{mismatch_source}")


def log_page_view(
    session_id,
    tofu_content_id,
    tofu_slug,
    content_variation_index,
    ip_address,
    user_agent,
    payload,
):
    event_type = "page_view"

    if not payload:
        payload = {}
    payload["tofu_content_id"] = tofu_content_id
    payload["time_page_view"] = 0

    content_id = None
    content_group_id = None
    campaign_id = None
    playbook_id = None

    try:
        # if tofu_slug is None, we'd fetch the first content in the content group
        public_content = (
            PublicContent.objects.filter(
                tofu_content_id=tofu_content_id,
                tofu_slug=tofu_slug,
                source_content_variation__isnull=False,
            )
            if tofu_slug
            else PublicContent.objects.filter(
                tofu_content_id=tofu_content_id,
                source_content_variation__isnull=False,
            )
        )
        query = PublicContent.objects.filter(
            tofu_content_id=tofu_content_id,
            source_content_variation__isnull=False,
        )
        if not public_content.exists():
            logging.warning(
                f"Error when fetching content not found for tofu_content_id {tofu_content_id} and tofu_slug {tofu_slug}"
            )
        else:
            public_content = public_content.first()
            if (
                not public_content.source_content_variation
            ):  # should not happen but just in case
                logging.error(
                    f"Error when fetching variation not found for tofu_content_id {tofu_content_id} and tofu_slug {tofu_slug}"
                )
            else:
                content = public_content.source_content_variation.content
                campaign = content.content_group.campaign

                content_id = content.id if tofu_slug else None
                content_group_id = content.content_group.id
                campaign_id = campaign.id
                playbook_id = campaign.playbook.id
    except Exception as e:
        logging.error(
            f"Error when fetching info for content {tofu_content_id} {tofu_slug}: {e}"
        )

    try:
        with connection.cursor() as cursor:
            # Check if the session_id already exists
            cursor.execute(
                "SELECT created_at, payload FROM api_offsiteeventlogs WHERE session_id = %s",
                [session_id],
            )
            result = cursor.fetchone()
            if result:
                # Update only the payload field
                created_at = result[0]
                existing_payload = json.loads(result[1])
                time_spent = timezone.now() - created_at
                existing_payload["time_spent"] = time_spent.total_seconds()
                cursor.execute(
                    """
                    UPDATE api_offsiteeventlogs
                    SET payload=%s
                    WHERE session_id=%s
                """,
                    [json.dumps(existing_payload), session_id],
                )
            else:
                insert_query = """
                INSERT INTO api_offsiteeventlogs (session_id, event_type, playbook_id, campaign_id, content_group_id, content_id, content_variation_index, tofu_slug, ip_address, user_agent, payload, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(
                    insert_query,
                    [
                        session_id,
                        event_type,
                        playbook_id,
                        campaign_id,
                        content_group_id,
                        content_id,
                        content_variation_index,
                        tofu_slug,
                        ip_address,
                        user_agent,
                        json.dumps(payload),
                        timezone.now(),
                    ],
                )
    except Exception as e:
        logging.error(f"Database logging error for events: {e}")


def log_autosync_update(message):
    send_slack_message(channel="#bot_tests", message=message)


def log_cloudwatch_metric(metric_name, value, **kwargs):

    # Load AWS Secrets into env vars if not already set
    tofu_env = os.environ.get("TOFU_ENV")
    if tofu_env not in ["production", "development"]:
        return

    # Initialize CloudWatch logs client
    logs = boto3.client("logs")

    # TODO: use context manager
    user = get_current_user()
    user_id = user.id if user else None
    username = user.username if user else None
    playbook = get_current_playbook()
    playbook_id = playbook.id if playbook else None

    # Send metrics to CloudWatch
    try:
        logs.put_log_events(
            logGroupName="tofu-be-metrics",
            logStreamName="tofu-be-events",
            logEvents=[
                {
                    "timestamp": int(time.time() * 1000),
                    "message": json.dumps(
                        {
                            "metric": metric_name,
                            "value": value,
                            "user_id": user_id,
                            "username": username,
                            "playbook_id": playbook_id,
                            **kwargs,
                        }
                    ),
                }
            ],
        )
    except Exception as e:
        logging.exception(f"Failed to log cloudwatch metric due to {e}")
