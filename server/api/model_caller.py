import asyncio
import json
import logging
import time

from django.core.cache import cache
from langchain.chains.summarize import load_summarize_chain
from langchain_openai.chat_models import ChatOpenAI
from ratelimit import limits, sleep_and_retry

from .image_generator import ImageGenerator
from .llm.chat_mock import ChatMock
from .llms import get_llm_for_model_name
from .utils import (
    alter_llm_messages,
    convert_messages_to_prompt_for_claude,
    fix_claude_outputs_for_generations,
    get_token_count,
    strip_for_json,
)


class ModelCaller:
    def __init__(self, model_config):
        self.model_config = model_config
        self.model_name = None
        self.llm = None
        self.model_kwargs = {}
        self.image_generator = ImageGenerator(model_config)

    def _execute_with_fallback(self, operation, *args, **kwargs):
        for fallback_level, model_params in enumerate(
            self.model_config.model_params_list
        ):
            try:
                self.model_params = model_params
                self.model_name = model_params.model_name
                self.model_kwargs = model_params.model_params
                return operation(*args, **kwargs)
            except Exception as e:
                logging.exception(
                    f"Error with model {self.model_name} at fallback level {fallback_level}: {e}"
                )
                if fallback_level + 1 >= len(self.model_config.model_params_list):
                    raise Exception(
                        f"Failed to get results for {self.model_name} after {fallback_level + 1} fallback attempts"
                    ) from e

    def get_summary_with_fallback(self, docs, **kwargs):
        return self._execute_with_fallback(self._get_summary, docs, **kwargs)

    def get_results_with_fallback(self, llm_inputs, json_output=False):
        return self._execute_with_fallback(
            self._get_results, llm_inputs, json_output=json_output
        )

    def get_image_generation_with_fallback(
        self, prompt, playbook, prev_images=None, **kwargs
    ):
        return self.image_generator.generate_image_with_fallback(
            prompt,
            playbook,
            prev_images=prev_images,
            **kwargs,
        )

    def get_llm_dict_response(self, llm_inputs):
        try:
            result = self.get_results_with_fallback(llm_inputs, json_output=True)

            if result and len(result) > 0:
                json_text = result[0].text
                json_text = strip_for_json(json_text)
                return json.loads(json_text)
            else:
                logging.error(f"No result from LLM call: {result}")
                raise Exception("No result from LLM call")

        except Exception as e:
            logging.exception(f"Error in LLM call: {str(e)}")
            raise

    def _get_summary(self, docs, **kwargs):
        self.llm = get_llm_for_model_name(**self.model_kwargs)
        if not kwargs.get("summary_length_words"):
            raise ValueError("summary_length_words is required for summary")
        summary_length_words = kwargs.get("summary_length_words")
        chain_run_args = {
            "company_name": kwargs.get("company_name", ""),
            "summary_length_words": summary_length_words,
            "input_documents": docs,
        }

        chain = (
            load_summarize_chain(
                llm=self.llm,
                map_prompt=kwargs.get("map_prompt"),
                combine_prompt=kwargs.get("combine_prompt"),
                collapse_prompt=kwargs.get("map_prompt"),
                chain_type="map_reduce",
                verbose=False,
            )
            if kwargs.get("summarize_with_map_reduce")
            else load_summarize_chain(
                llm=self.llm,
                prompt=kwargs.get("combine_prompt"),
                chain_type="stuff",
                verbose=False,
            )
        )
        summary = chain.invoke(input=chain_run_args, return_only_outputs=True).get(
            "output_text"
        )

        return summary

    @staticmethod
    def get_messages_token_count(llm_inputs):
        model_name = "gpt-4o-2024-11-20"
        model = ChatOpenAI(model_name=model_name)
        return model.get_num_tokens_from_messages(llm_inputs)

    def _get_results(self, llm_inputs, json_output=False):
        self.generations = []
        components = self.model_kwargs.get("components", None)
        num_of_variations = self.model_kwargs.get("n", 1)
        if "n" in self.model_kwargs and "claude" in self.model_name:
            self.model_kwargs.pop("n")
        self.llm = get_llm_for_model_name(**self.model_kwargs, json_output=json_output)
        input_size = ModelCaller.get_messages_token_count(llm_inputs)
        self.check_and_set_lock_token_usage(
            input_size * num_of_variations, model_name=self.model_name
        )
        generations = self._call_model(llm_inputs, num_of_variations, components)
        if not generations or not generations[0]:
            raise Exception("No generations returned")
        output_size = sum(
            get_token_count(generation.text) for generation in generations[0]
        )
        self.check_and_set_lock_token_usage(output_size, model_name=self.model_name)

        if "claude" in self.model_name:
            generations[0] = fix_claude_outputs_for_generations(generations[0])
        return generations[0]

    def _call_model(self, llm_inputs, num_of_variations, components):

        if "claude" in self.model_name:
            generations = self._call_claude_model(llm_inputs, num_of_variations)
        elif "deepseek-reasoner" in self.model_name:
            results = self._call_reasoner_model(llm_inputs)
            generations = results.generations
        elif "gemini" in self.model_name:
            results = self._call_gemini_model(llm_inputs)
            generations = results.generations
        else:
            if isinstance(self.llm, ChatMock):
                results = self.llm.generate(
                    [llm_inputs],
                    components=components,
                    num_of_variations=num_of_variations,
                )
            else:
                results = self.llm.generate([llm_inputs])
            generations = results.generations
            logging.info(f"Generation complete: {results.llm_output}")
        return generations

    def _call_reasoner_model(self, llm_inputs):
        reasoner_llm_inputs = alter_llm_messages(llm_inputs)
        results = self.llm.generate([reasoner_llm_inputs])
        return results

    def _call_claude_model(self, llm_inputs, num_of_variations):
        claude_llm_inputs = convert_messages_to_prompt_for_claude(llm_inputs)
        claude_llm_inputs = [claude_llm_inputs] * num_of_variations

        def _get_or_create_event_loop():
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
                return loop
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return loop

        try:
            loop = _get_or_create_event_loop()

            if not loop.is_running():
                results = loop.run_until_complete(
                    self._async_agenerate_wrapper_claude(claude_llm_inputs)
                )
            else:
                future = asyncio.run_coroutine_threadsafe(
                    self._async_agenerate_wrapper_claude(claude_llm_inputs),
                    loop,
                )
                results = future.result()
        except asyncio.CancelledError:
            logging.warning("Async operation was cancelled")
            raise
        except Exception as e:
            logging.exception(
                f"Error for claude model async generation and would fallback to sync mode: {e}"
            )
            results = self._sync_generate_wrapper_claude(claude_llm_inputs)
        logging.info(f"Generation complete: {results.llm_output}")
        # claude has the format as:
        # [[result_1], [result_2], ...]
        # but gpt has the format as:
        # [[result_1, result_2, ...]]
        generations = [gen[0] for gen in results.generations]
        generations = [generations]
        return generations

    async def _async_agenerate_wrapper_claude(self, llm_inputs):
        results = await self.llm.agenerate(llm_inputs)
        return results

    def _sync_generate_wrapper_claude(self, llm_inputs):
        results = self.llm.generate(llm_inputs)
        return results

    # rate limit at 10 requests per minute. Otherwise sleep and retry.
    @sleep_and_retry
    @limits(calls=10, period=60)
    def _call_gemini_model(self, llm_inputs):
        gemini_llm_inputs = alter_llm_messages(llm_inputs)
        results = self.llm.generate([gemini_llm_inputs])
        return results

    @staticmethod
    def _get_model_tpm_limit(model_name):
        if "gpt-4o-mini" in model_name:
            return 150000000
        elif "gpt-4.1-mini" in model_name:
            return 150000000
        elif "gpt-4o" in model_name:
            return 30000000
        elif "anthropic.claude-3-5" in model_name:  # bedrock claude 3.5
            return 8000000
        elif "anthropic.claude-3-7" in model_name:  # bedrock claude 3.7
            return 3000000
        elif "anthropic.claude-sonnet-4" in model_name:  # bedrock claude sonnet 4
            return 3000000
        elif "anthropic.claude-opus-4" in model_name:  # bedrock claude opus 4
            return 200000
        elif "claude-3" in model_name:
            return 2000000
        elif "claude-sonnet-4" in model_name:
            return 400000
        elif "claude-opus-4" in model_name:
            return 400000
        elif "deepseek" in model_name:
            return 2000000
        elif "gemini" in model_name:
            return 1000000
        elif "o1" in model_name:
            return 30000000
        elif "o3-mini" in model_name:
            return 150000000
        elif "gpt-4.5-preview" in model_name:
            return 2000000
        elif "o4-mini" in model_name:
            return 150000000
        elif "gpt-4.1" in model_name:
            return 30000000
        elif "o3" in model_name:
            return 30000000
        elif "mock" in model_name:
            return 1000000000
        else:
            return None

    def check_and_set_lock_token_usage(self, num_tokens, model_name):
        ttl = 60
        max_token_usage = ModelCaller._get_model_tpm_limit(model_name)
        if not max_token_usage:
            logging.error("max_token_usage is not set for model " + model_name)
            return
        lock_cache = cache.get(f"{model_name}_gen_lock", {})
        token_budget = min(
            lock_cache.get("token_budget", max_token_usage), max_token_usage
        )
        # logging.info("Token budget for " + model_name + ": " + str(token_budget))
        model_hitting_limit = False
        while token_budget < num_tokens and num_tokens < max_token_usage:
            model_hitting_limit = True
            time.sleep(1)
            now = time.time()
            lock_cache = cache.get(f"{model_name}_gen_lock", {})
            timestamp = lock_cache.get("timestamp", now)
            token_budget = (
                lock_cache.get("token_budget", max_token_usage)
                + (now - timestamp) * max_token_usage / ttl
            )

        if model_hitting_limit:
            logging.error(
                f"Model {model_name} is hitting the token limit. Current TPM budget left: {token_budget}"
            )

        now = time.time()
        timestamp = lock_cache.get("timestamp", now)
        token_budget = (
            lock_cache.get("token_budget", max_token_usage)
            + (now - timestamp) * max_token_usage / ttl
            - num_tokens
        )
        cache.set(
            f"{model_name}_gen_lock",
            {"token_budget": token_budget, "timestamp": now},
            timeout=60,
        )
