"""
Company Info related views for the Tofu API.
This module contains endpoints for managing company information within a playbook.
"""

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import mixins, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from ..models import CompanyInfo, Playbook, PlaybookUser
from ..playbook_build.object_builder import ObjectBuilder
from ..serializers import CompanyInfoSerializer


class CompanyInfoViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    queryset = CompanyInfo.objects.all()
    serializer_class = CompanyInfoSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.lookup_url_kwarg in self.kwargs:
            context["company_info_id"] = self.kwargs[self.lookup_url_kwarg]
        return context

    def is_request_from_user_or_superuser(self, request, playbook):
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def is_request_from_creator_or_user_or_superuser(self, request):
        company_info = self.get_object()
        if not company_info:
            return False
        playbook = company_info.playbook
        return self.is_request_from_user_or_superuser(request, playbook)

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Retrieve company info by playbook_id",
        operation_description="",
        manual_parameters=[
            openapi.Parameter(
                name="playbook_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
        ],
    )
    @action(detail=False, methods=["get"])
    def retrieve_by_playbook_id(self, request, *args, **kwargs):
        playbook_id = request.query_params.get("playbook_id")
        if not playbook_id:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        playbook = Playbook.objects.filter(id=playbook_id).first()
        if not playbook:
            return Response(
                status=status.HTTP_404_NOT_FOUND, data={"error": "Playbook not found"}
            )
        if not self.is_request_from_user_or_superuser(request, playbook):
            return Response(status=status.HTTP_403_FORBIDDEN)
        company_info = CompanyInfo.objects.filter(playbook=playbook).first()
        if not company_info:
            return Response(status=status.HTTP_404_NOT_FOUND)
        serializer = self.serializer_class
        return Response(serializer(company_info).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="rebuild company info",
        operation_description="",
        manual_parameters=[],
    )
    @action(detail=True, methods=["post"])
    def rebuild(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        company_info = self.get_object()
        company_builder = ObjectBuilder.get_builder(company_info)
        company_builder.build_docs(rebuild=True)
        company_info.refresh_from_db()
        return Response(
            data=self.serializer_class(company_info).data, status=status.HTTP_200_OK
        )
