"""
Content Variation related views for the Tofu API.
This module contains endpoints for managing variations of generated content.
"""

from rest_framework import mixins, status, viewsets
from rest_framework.response import Response

from ..models import ContentVariation
from ..serializers import ContentVariationSerializer
from ..throttling import tofu_lite_ratelimit


class ContentVariationViewSet(
    mixins.RetrieveModelMixin, mixins.UpdateModelMixin, viewsets.GenericViewSet
):
    queryset = ContentVariation.objects.all()
    serializer_class = ContentVariationSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "variation_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def is_request_from_creator_or_superuser(self, request):
        content = self.get_object().content
        if content.creator == request.user or request.user.is_superuser:
            return True
        return False

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    @tofu_lite_ratelimit(rate="20/m")
    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)
