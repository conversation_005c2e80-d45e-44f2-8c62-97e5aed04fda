"""
User-related views for the Tofu API.
For user related operations.
"""

import base64
import logging
import os

import jwt
import stripe
from django.contrib.auth import authenticate
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAdminUser, IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from ..models import TofuUser
from ..serializers import TofuUserSerializer
from ..stripe import cancel_subscription, change_subscription, has_subscription
from ..throttling import tofu_lite_ratelimit


class UserViewSet(viewsets.ModelViewSet):
    queryset = TofuUser.objects.all()
    serializer_class = TofuUserSerializer
    lookup_field = "username"
    lookup_url_kwarg = "base64_username"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {
            self.lookup_field: base64.b64decode(
                self.kwargs[self.lookup_url_kwarg]
            ).decode("utf-8")
        }
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def is_request_from_owner_or_superuser(self, request):
        user = self.get_object()
        if user == request.user or request.user.is_superuser:
            return True
        return False

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action == "authentication":
            # No authentication required for login
            permission_classes = [AllowAny]
        elif self.action in ("list", "create"):
            permission_classes = [IsAdminUser]
        else:
            # permission handled inside the methods
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @tofu_lite_ratelimit(rate="20/m")
    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    @tofu_lite_ratelimit(rate="20/m")
    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    @tofu_lite_ratelimit(rate="20/m")
    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Verify user",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "username": openapi.Schema(type=openapi.TYPE_STRING),
                "password": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(methods=["post"], detail=False, permission_classes=[AllowAny])
    @tofu_lite_ratelimit(rate="20/m")
    def authentication(self, request: Request, *args, **kwargs):
        try:
            if "username" not in request.data or "password" not in request.data:
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data={"message": "Invalid credentials"},
                )

            username = request.data["username"]
            password = request.data["password"]
            user = authenticate(username=username, password=password)

            if user is not None:
                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        "user_id": user.id,
                        "username": user.username,
                        "fullname": user.full_name,
                    },
                )
            else:
                return Response(
                    status=status.HTTP_401_UNAUTHORIZED,
                    data={"message": "Invalid credentials"},
                )
        except:
            return Response(status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Set user password",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "new_password": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    @tofu_lite_ratelimit(rate="20/m")
    def set_password(self, request: Request, *args, **kwargs):
        try:
            if not self.get_object():
                return Response(status=status.HTTP_404_NOT_FOUND)
            user = self.get_object()
            if user != request.user and not request.user.is_superuser:
                return Response(status=status.HTTP_403_FORBIDDEN)

            user.set_password(request.data["new_password"])
            user.save()
            return Response(status=status.HTTP_200_OK)
        except:
            return Response(status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Get access token for user",
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Access token generated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "access_token": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                ),
            ),
            status.HTTP_403_FORBIDDEN: "Not authorized",
            status.HTTP_404_NOT_FOUND: "User not found",
        },
    )
    @action(detail=True, methods=["get"])
    def get_access_token(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            user = self.get_object()
            payload = {
                "username": user.username,
            }
            jwt_key = os.environ.get("JWT_KEY")
            token = jwt.encode(payload, jwt_key, algorithm="HS256")
            return Response({"access_token": token}, status=status.HTTP_200_OK)
        except Exception as e:
            logging.error(f"Error generating access token: {e}")
            return Response(
                {"error": "Failed to generate access token"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="Cancel a Stripe subscription",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_404_NOT_FOUND: "Not found",
            status.HTTP_403_FORBIDDEN: "Forbidden",
        },
    )
    @action(detail=True, methods=["post"], url_path="cancel_stripe_subscription")
    def stripe_cancel_subscription(self, request, *args, **kwargs):
        stripe.api_key = os.environ.get("STRIPE_API_KEY", "")
        if not stripe.api_key:
            message = "Stripe API key not configured"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        user = self.get_object()
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        if not has_subscription(user.id):
            message = "User does not have an active subscription"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            cancel_subscription(user.id)
            return Response(
                {"message": "Subscription cancelled successfully"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            message = f"Error cancelling subscription: {str(e)}"
            logging.error(message)
            return Response(
                {"error": {"message": str(e)}}, status=status.HTTP_400_BAD_REQUEST
            )

    @swagger_auto_schema(
        operation_summary="Change a Stripe subscription",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["tofuUserId", "priceId"],
            properties={
                "priceId": {
                    "type": openapi.TYPE_STRING,
                    "description": "Stripe Price ID",
                    "example": "price_H5ggYwtDq4fbrJ",
                },
            },
        ),
    )
    @action(detail=True, methods=["post"], url_path="change_stripe_subscription")
    def stripe_change_subscription(self, request, *args, **kwargs):
        stripe.api_key = os.environ.get("STRIPE_API_KEY", "")
        if not stripe.api_key:
            message = "Stripe API key not configured"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        user = self.get_object()
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        tofu_user_id = user.id
        if not tofu_user_id:
            message = "Tofu user ID is required"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # First we check if the user has an active subscription, otherwise throw an error.
        if not has_subscription(tofu_user_id):
            message = "User does not have an active subscription"
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_400_BAD_REQUEST,
            )
        price = request.data.get("priceId")
        if not price:
            message = "Price ID is required"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            change_subscription(tofu_user_id, price)
            return Response(
                {"message": "Subscription changed successfully"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            message = f"Error changing subscription: {str(e)}"
            logging.error(message)
            return Response(
                {"error": {"message": message}}, status=status.HTTP_400_BAD_REQUEST
            )
