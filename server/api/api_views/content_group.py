"""
For ContentGroup related operations
"""

import logging
import traceback

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from pydantic import ValidationError
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.request import Request
from rest_framework.response import Response

from ..actions.action_handler import ActionHandler
from ..component_selection.content_group_component_selector import (
    ContentGroupComponentSelector,
)
from ..content_group import ContentGroupHandler
from ..gen_status import GenStatusUpdater
from ..models import (
    ContentGroup,
)
from ..serializers import (
    ContentGroupSerializer,
)
from ..thread_locals import (
    try_set_current_content_group,
)
from ..throttling import tofu_lite_ratelimit
from ..utils import (
    measure_latency,
)


class ContentGroupViewSet(viewsets.ModelViewSet):
    queryset = ContentGroup.objects.all()
    serializer_class = ContentGroupSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "content_group_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        try_set_current_content_group(obj)
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.lookup_url_kwarg in self.kwargs:
            context["content_group_id"] = self.kwargs[self.lookup_url_kwarg]
        return context

    def is_request_from_creator_or_superuser(self, request):
        content_group = self.get_object()
        if (
            content_group
            and content_group.creator == request.user
            or request.user.is_superuser
        ):
            return True
        return False

    def is_internal_features_enabled(self, request: Request):
        if request.user.is_superuser:
            return True
        if request.user.context and request.user.context.get("internalFeatures", False):
            return True
        return False

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        self.queryset = queryset.filter(creator=request.user).order_by("-updated_at")
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Create a new content group",
        operation_description="Create a new  content group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "campaign": {"type": openapi.TYPE_INTEGER, "example": 5},
                "content_group_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "content group name",
                },
                "content_group_params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "content_type": "Landing Page",
                        "content_source": "Drowning in Product Data Complexities_ Swim with PIM _ Pimcore.com.pdf",
                        "content_source_copy": "/api/web/storage/s3-presigned-url?file=56f34364-e9c4-b998-4ac7-3dbdda4bca02-Drowning in Product Data Complexities_ Swim with PIM _ Pimcore.com.pdf&fileType=application/pdf&directory=tofu-uploaded-files",
                        "content_source_format": "PDF",
                        "content_source_upload_method": "File",
                        "custom_instructions": [
                            "Do not over sell",
                        ],
                        "assets": {},
                    },
                },
                "components": {
                    "type": openapi.TYPE_ARRAY,
                    "example": {
                        "12345678-90ab-cdef-ghij-klmnopqrstuv": {
                            "meta": {
                                "html_tag": "<h1>",
                                "preceding_element": "<div>This is a preceding element.</div>",
                                "succeeding_element": "<p>This is a succeeding element.</p>",
                                "component_params": {
                                    "custom_instructions": [
                                        "Don't use percentage",
                                        "Use pirate language style",
                                    ],
                                    "assets": {"Whitepapers": "whitepaper 1"},
                                },
                            },
                            "text": "this is the original header text",
                        },
                        "87654321-90ab-cdef-ghij-klmnopqrstuv": {
                            "meta": {
                                "html_tag": "<h2>",
                                "preceding_element": "<div>This is a preceding element for body.</div>",
                                "succeeding_element": "<p>This is a succeeding element for body.</p>",
                                "component_params": {
                                    "custom_instructions": [
                                        "Don't use percentage",
                                        "Use pirate language style",
                                    ],
                                    "assets": {"Whitepapers": "whitepaper 2"},
                                },
                            },
                            "text": "this is the original body text",
                        },
                    },
                },
            },
        ),
        responses={
            status.HTTP_201_CREATED: "Created",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @tofu_lite_ratelimit(rate="20/m")
    def create(self, request, *args, **kwargs):
        if not request.user:
            return Response(status=status.HTTP_403_FORBIDDEN)
        request.data["creator"] = request.user.id
        return super().create(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Update a content group",
        operation_description="Update a content group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content_group_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "content group name",
                },
                "content_group_params": {"type": openapi.TYPE_OBJECT, "example": {}},
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @tofu_lite_ratelimit(rate="20/m")
    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if "creator" in request.data:
            content_group_creator = self.get_object().creator
            if content_group_creator != request.user:
                logging.error("creator cannot be updated")
            del request.data["creator"]
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Update a content group",
        operation_description="Update a content group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content_group_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "content group name",
                },
                "content_group_params": {"type": openapi.TYPE_OBJECT, "example": {}},
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @tofu_lite_ratelimit(rate="20/m")
    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if "creator" in request.data:
            content_group_creator = self.get_object().creator
            if content_group_creator != request.user:
                logging.error("creator cannot be updated")
            del request.data["creator"]
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Create conents for a content group",
        operation_description="Create contents for a content group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "targets": {
                    "type": openapi.TYPE_ARRAY,
                    "items": openapi.Schema(type=openapi.TYPE_OBJECT),
                }
            },
        ),
        responses={
            status.HTTP_201_CREATED: "Created",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    @measure_latency
    @tofu_lite_ratelimit(rate="20/m")
    def bulk_create_content(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        targets = request.data.get("targets")

        content_group = self.get_object()
        campaign = content_group.campaign
        if campaign.campaign_params.get("campaign_goal") == "Workflow":
            logging.error(
                f"bulk_create_content is not supposed to be called for campaign v3: {campaign.id}"
            )

        try:
            content_group_handler = ContentGroupHandler(content_group)
            content_group_handler.bulk_create_content(targets)
        except ValidationError as e:
            logging.error(
                f"error in content group {content_group.id} bulk_create_content: {e}"
            )
            return Response(
                {"error": str(e)}, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )
        except Exception as e:
            logging.error(
                f"error in content group {content_group.id} bulk_create_content: {e}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            ContentGroupSerializer(
                content_group, context=self.get_serializer_context()
            ).data,
            status=status.HTTP_201_CREATED,
        )

    # define an API called set_edits, taking parameters as:
    # a list of content_id; a dict of component definition; a dict of variation for the components
    @swagger_auto_schema(
        operation_summary="Use this to set edits.",
        operation_description="API for setting edits.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                # content_ids: a list of int
                "content_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_INTEGER, "example": 1},
                },
                "components": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "EcLlM8MWJCgk9Gt_": {
                            "meta": {
                                "type": "anchor",
                                "component_type": "edited",
                            },
                            "anchor": {
                                "href": "https://example.com",
                                "text": "Click here",
                                "target": "_blank",
                            },
                        }
                    },
                },
                "variations": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "EcLlM8MWJCgk9Gt_": {
                            "meta": {
                                "type": "anchor",
                                "component_type": "edited",
                                "current_version": {
                                    "anchor": {
                                        "href": "https://tofuhq.com",
                                        "text": "Click here",
                                        "target": "_blank",
                                    },
                                },
                            },
                            "anchor": {
                                "href": "https://example.com",
                                "text": "Click here",
                                "target": "_blank",
                            },
                        }
                    },
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def set_edits(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_ids = request.data.get("content_ids", [])
        components = request.data.get("components")
        variations = request.data.get("variations")
        if not variations:
            return Response(
                data={"error": "Missing parameters"}, status=status.HTTP_400_BAD_REQUEST
            )
        # make sure variation keys are the same as component keys
        if components and set(components.keys()) != set(variations.keys()):
            return Response(
                data={"error": "Component keys and variation keys do not match"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            content_group = self.get_object()
            content_group_handler = ContentGroupHandler(content_group)
            updated_content_group = content_group_handler.set_edits(
                content_ids, components, variations
            )
            # serialize the updated content group and updated variation in response
            return Response(
                ContentGroupSerializer(
                    updated_content_group, context=self.get_serializer_context()
                ).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging.error(
                f"error in content group {content_group.id} set_edits: {e}\n{traceback.format_exc()}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    # define an API to delete components
    # input: a list of content_ids which is a list of int; a list of component_ids which is a list of str
    @swagger_auto_schema(
        operation_summary="Use this to delete components.",
        operation_description="API for deleting components.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                # content_ids: a list of int
                "content_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_INTEGER, "example": 1},
                },
                "component_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_STRING, "example": "v7oqL"},
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def delete_components(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_ids = request.data.get("content_ids", [])
        component_ids = request.data.get("component_ids", [])
        if not component_ids:
            return Response(
                data={"error": "Missing component_ids"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            content_group = self.get_object()
            content_group_handler = ContentGroupHandler(content_group)
            updated_content_group = content_group_handler.delete_components(
                content_ids, component_ids
            )
            # serialize the updated content group in response
            return Response(
                ContentGroupSerializer(
                    updated_content_group, context=self.get_serializer_context()
                ).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging.error(
                f"error in content group {content_group.id} delete_components: {e}\n{traceback.format_exc()}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Export content group to linkedin ads",
        operation_description="Export content group to linkedin ads",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                # possible properties
                # campaign_id
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def export_to_linkedin_ads(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_group = self.get_object()
        content_group_handler = ContentGroupHandler(content_group)
        export_code, export_data = content_group_handler.export_to_linkedin_ads()
        return Response(
            export_data,
            status=export_code,
        )

    @swagger_auto_schema(
        operation_summary="Check pregen scores for a content group",
        operation_description="Check pregen scores for a content group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "unsaved_data": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "template": openapi.Schema(type=openapi.TYPE_STRING),
                        "components": openapi.Schema(type=openapi.TYPE_OBJECT),
                        "instructions": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT),
                        ),
                    },
                ),
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def check_pregen_scores(self, request, *args, **kwargs):
        # put the import here to skip the setting in unittest
        from .evaluator.evaluators.content_group_scoring import (
            ContentGroupScoringEvaluator,
        )

        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        unsaved_data = request.data.get("unsaved_data", {})

        content_group = self.get_object()
        content_group_evaluator = ContentGroupScoringEvaluator(
            content_group, unsaved_data
        )
        try:
            check_result = content_group_evaluator.check_pregen_scores()
        except Exception as e:
            logging.error(
                f"error in content group {content_group.id} check_pregen_scores: {e}\n{traceback.format_exc()}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(check_result, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    @measure_latency
    @tofu_lite_ratelimit(rate="40/m")
    def gen_status(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_group = self.get_object()
        try:
            status_data = GenStatusUpdater().get_content_group_gen_status(content_group)
        except Exception as e:
            logging.error(
                f"error in content group {content_group.id} gen_status: {e}\n{traceback.format_exc()}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(status_data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Auto select components for content group",
        operation_description="Automatically select components for a content group. Pass any parameters needed for selection as key-value pairs.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            additional_properties=openapi.Schema(type=openapi.TYPE_STRING),
            example={
                "email_select_body": True,
                "skip_greetings": True,
            },
            description="Parameters for auto-selecting components. Accepts arbitrary key-value pairs.",
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def auto_select_components(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            content_group = self.get_object()
            # Get parameters from request data, with defaults
            email_select_body = request.data.get("email_select_body", True)
            skip_greetings = request.data.get("skip_greetings", True)

            parameters = {
                "email_select_body": email_select_body,
                "skip_greetings": skip_greetings,
            }

            action_handler = ActionHandler(content_group.action)

            content_group_component_selector = ContentGroupComponentSelector(
                action_handler, request.user
            )
            if not content_group_component_selector.is_eligible_for_auto_selection():
                return Response(
                    data={"error": "Content group is not eligible for auto selection"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            updated_content_group = (
                content_group_component_selector.auto_select_components(parameters)
            )
            return Response(
                ContentGroupSerializer(
                    updated_content_group, context=self.get_serializer_context()
                ).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging.error(
                f"error in content group {content_group.id} component_auto_selection: {e}\n{traceback.format_exc()}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
