from django.core.paginator import EmptyPage, Paginator
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.response import Response

from .pagination_models import PaginatedResponseSerializer


class PaginatedListMixin:
    pagination_serializer_class = PaginatedResponseSerializer

    def get_pagination_params(
        self, request, default_page=1, default_page_size=20, max_page_size=100
    ):
        try:
            page = int(request.query_params.get("page", default_page))
            page_size = int(request.query_params.get("page_size", default_page_size))
            if page_size > max_page_size:
                page_size = max_page_size
            if page < 1 or page_size < 1:
                raise ValueError
        except (ValueError, TypeError):
            raise ValidationError("page and page_size must be positive integers")
        return page, page_size

    def paginate_queryset(self, queryset, page=None, page_size=None):
        """
        Paginate the queryset using the provided page and page_size.
        If page or page_size is None, return the full queryset (unpaginated).
        Returns (page_obj, paginator) or (queryset, None) if unpaginated.
        """
        if page is None or page_size is None:
            # No pagination, return all results
            # This is to make it compatible with the current listing API
            return None
        paginator = Paginator(queryset, page_size)
        try:
            page_obj = paginator.page(page)
        except EmptyPage:
            raise NotFound(
                f"Page {page} is out of range. Total pages: {paginator.num_pages}."
            )
        return page_obj

    def get_paginated_response(self, serializer, page_obj):
        paginator = page_obj.paginator
        response_data = {
            "results": serializer.data,
            "page": page_obj.number,
            "total_pages": paginator.num_pages,
            "page_size": paginator.per_page,
            "total_items": paginator.count,
            "has_next": page_obj.has_next(),
            "has_previous": page_obj.has_previous(),
        }
        paginated_serializer = self.pagination_serializer_class(data=response_data)
        paginated_serializer.is_valid(raise_exception=True)
        return Response(paginated_serializer.data)

    def get_unpaginated_response(self, serializer, queryset):
        return Response(
            {
                "results": serializer.data,
                "page": 1,
                "total_pages": 1,
                "page_size": len(queryset),
                "total_items": len(queryset),
                "has_next": False,
                "has_previous": False,
            }
        )

    def apply_ordering(self, queryset, sort_by, order, allowed_sort_fields):
        if sort_by and sort_by not in allowed_sort_fields:
            raise ValidationError(f"Invalid sort_by field: {sort_by}")

        if not sort_by:
            return queryset.order_by("-created_at")

        if order == "desc":
            sort_by = f"-{sort_by}"
        return queryset.order_by(sort_by)
