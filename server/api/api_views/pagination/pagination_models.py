from drf_yasg import openapi
from rest_framework import serializers


class PaginationQuerySerializer(serializers.Serializer):
    page = serializers.IntegerField(required=False, min_value=1, default=1)
    page_size = serializers.IntegerField(
        required=False,
        min_value=1,
        max_value=100,
        default=20,
        error_messages={
            "min_value": "Page size must be greater than 0",
            "max_value": "Page size must be less than or equal to 100",
        },
    )
    sort_by = serializers.CharField(required=False, default="created_at")
    order = serializers.ChoiceField(
        choices=["asc", "desc"], required=False, default="desc"
    )


class PaginatedResponseSerializer(serializers.Serializer):
    results = serializers.ListField()
    page = serializers.IntegerField()
    total_pages = serializers.IntegerField()
    page_size = serializers.IntegerField()
    total_items = serializers.IntegerField()
    has_next = serializers.BooleanField()
    has_previous = serializers.BooleanField()
