"""
For Campaign related operations
"""

import logging
import traceback

from django.db.models import Prefetch
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from pydantic import ValidationError
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.request import Request
from rest_framework.response import Response

from ..actions.action_handler import ActionHandler
from ..actions.legacy_converter.legacy_campaign_converter import LegacyCampaignConverter
from ..autopilot_run import AutopilotObjectBuilder
from ..campaign import CampaignHandler
from ..campaign_gen_wrapper import CampaignGenWrapper
from ..content_collection import ContentCollection
from ..evaluator.evaluate_data_duplicate import (
    copy_content_collection_for_eval,
)
from ..gen_status import GenStatusUpdater
from ..mixins import TofuPagesKillSwitchMixin
from ..models import (
    Action,
    Campaign,
    Content,
    ContentGroup,
    Tag,
)
from ..serializers import (
    ActionSerializer,
    CampaignSerializer,
)
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionStatusType,
)
from ..thread_locals import (
    try_set_current_campaign,
)
from ..throttling import tofu_lite_ratelimit
from ..utils import (
    measure_latency,
)


class CampaignViewSet(TofuPagesKillSwitchMixin, viewsets.ModelViewSet):
    queryset = Campaign.objects.all()
    serializer_class = CampaignSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "campaign_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        try_set_current_campaign(obj)
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.lookup_url_kwarg in self.kwargs:
            context["campaign_id"] = self.kwargs[self.lookup_url_kwarg]
        return context

    def is_request_from_creator_or_superuser(self, request):
        campaign = self.get_object()
        if campaign.creator == request.user or request.user.is_superuser:
            return True
        return False

    def is_internal_features_enabled(self, request: Request):
        if request.user.is_superuser:
            return True
        if request.user.context and request.user.context.get("internalFeatures", False):
            return True
        return False

    def is_enable_target_object(self, request: Request):
        if request.user.context and request.user.context.get(
            "enableTargetObject", False
        ):
            return True
        return False

    @swagger_auto_schema(
        operation_summary="Get all campaigns this user has access to",
        operation_description="Optionally filter by tag IDs",
        manual_parameters=[
            openapi.Parameter(
                "tag_ids",
                openapi.IN_QUERY,
                description="Comma-separated list of tag IDs to filter by",
                type=openapi.TYPE_STRING,
            ),
        ],
    )
    @measure_latency
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        # Filter by tag IDs if provided
        tag_ids = request.query_params.get("tag_ids")
        if tag_ids:
            try:
                tag_id_list = [int(tag_id.strip()) for tag_id in tag_ids.split(",")]
                queryset = queryset.filter(campaignTags__id__in=tag_id_list).distinct()
            except ValueError:
                raise ValidationError(
                    "Invalid tag ID format. Please provide comma-separated integers."
                )

        self.queryset = (
            queryset.filter(creator=self.request.user)
            .prefetch_related(
                Prefetch("contentgroup_set"),
                Prefetch("campaignTags", queryset=Tag.objects.order_by("name")),
            )
            .order_by("-updated_at")
        )

        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Create a new campaign",
        operation_description="Create a new campaign",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "playbook": {"type": openapi.TYPE_INTEGER, "example": 5},
                "campaign_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "campaign name",
                },
                "campaign_params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "targets": [
                            {
                                "Indutries": ["Technology"],
                                "Persona": ["CEO", "CTO"],
                            },
                            {
                                "Indutries": ["Finance"],
                                "Persona": ["CTO"],
                            },
                            {
                                "Indutries": ["Finance"],
                                "Keywords": ["AI for GTM"],
                            },
                        ],
                        "assets": {},
                        "custom_instructions": ["Do not over sell"],
                        "foundation_model": "gpt-4o-2024-11-20",
                        "num_of_variations": 3,
                    },
                },
            },
        ),
        responses={
            status.HTTP_201_CREATED: "Created",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @tofu_lite_ratelimit(rate="20/m")
    def create(self, request, *args, **kwargs):
        if not request.user:
            return Response(status=status.HTTP_403_FORBIDDEN)
        request.data["gen_status"] = request.data.get(
            "gen_status", {"content_groups": {}, "status": "NOT_STARTED"}
        )
        request.data["creator"] = request.user.id
        return super().create(request, *args, **kwargs)

    @measure_latency
    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update_inbound_domain_field(self, request: Request):
        if (
            "campaign_params" not in request.data
            or "inbound_landing_pages" not in request.data["campaign_params"]
        ):
            return
        campaign = self.get_object()
        campaign_handler = CampaignHandler(campaign)

        prev_inbound_enabled = campaign.campaign_params.get(
            "inbound_landing_pages", {}
        ).get("enabled", False)
        prev_inbound_domain_field = campaign.campaign_params.get(
            "inbound_landing_pages", {}
        ).get("domain_field", None)
        current_inbound_enabled = request.data["campaign_params"][
            "inbound_landing_pages"
        ].get("enabled", False)
        domain_field = request.data["campaign_params"]["inbound_landing_pages"].get(
            "target_field", None
        )

        campaign_handler.update_exported_inbound_data(
            current_inbound_enabled=current_inbound_enabled,
            prev_inbound_enabled=prev_inbound_enabled,
        )
        if (
            current_inbound_enabled
            and domain_field
            and domain_field != prev_inbound_domain_field
        ):
            campaign_handler.set_inbound_domain_field(domain_field)

    @swagger_auto_schema(
        operation_summary="Update a campaign",
        operation_description="Update a campaign",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "campaign_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "campaign name",
                },
                "campaign_params": {"type": openapi.TYPE_OBJECT, "example": {}},
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @measure_latency
    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if "creator" in request.data:
            campaign_creator = self.get_object().creator
            if campaign_creator != request.user:
                logging.error("creator cannot be updated")
            del request.data["creator"]
        self.update_inbound_domain_field(request)
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Update a campaign",
        operation_description="Update a campaign",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "campaign_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "campaign name",
                },
                "campaign_params": {"type": openapi.TYPE_OBJECT, "example": {}},
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @measure_latency
    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if "creator" in request.data:
            campaign_creator = self.get_object().creator
            if campaign_creator != request.user:
                logging.error("creator cannot be updated")
            del request.data["creator"]
        self.update_inbound_domain_field(request)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Use this API to clone a campaign.",
        operation_description="API for campaign clone.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def clone(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        campaign = self.get_object()
        campaign_cloned = CampaignHandler(campaign).clone()

        return Response(
            CampaignSerializer(
                campaign_cloned, context=self.get_serializer_context()
            ).data,
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Use this to generate for campaign. Call once for the whole campaign.",
        operation_description="API for generating campaign contents.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content_group_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_INTEGER, "example": 1},
                },
                "content_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_INTEGER, "example": 1},
                },
                "params": {"type": openapi.TYPE_OBJECT, "example": {}},
                "continue_gen": {"type": openapi.TYPE_BOOLEAN, "example": False},
                "joint_generation": {"type": openapi.TYPE_BOOLEAN, "example": False},
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    @tofu_lite_ratelimit(rate="20/m")
    def gen(self, request: Request, *args, **kwargs):
        """
        Return a task id to fetch results, for example:
        """
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        campaign = self.get_object()
        content_group_ids = request.data.get("content_group_ids")
        content_ids = request.data.get("content_ids")
        collection_ids = request.data.get("collection_ids")
        continue_gen = request.data.get("continue_gen", False)
        joint_generation = request.data.get("joint_generation", False)
        if collection_ids:
            if content_group_ids or content_ids:
                return Response(
                    {
                        "error": "cannot specify both collection_ids and content_group_ids or content_ids"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if content_group_ids and content_ids:
            return Response(
                {"error": "cannot specify both content_group_ids and content_ids"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if any job is not terminated
        gen_status = GenStatusUpdater().get_campaign_gen_status(campaign)
        if collection_ids:
            content_group_ids_to_check = []
            for collection_id in collection_ids:
                content_group_ids = list(
                    ContentCollection(collection_id).content_collection_map.keys()
                )
                content_group_ids_to_check.extend(content_group_ids)
            for content_group_id in content_group_ids_to_check:
                if gen_status["content_groups"].get(content_group_id, {}).get(
                    "status"
                ) in ("IN_PROGRESS", "QUEUED"):
                    return Response(
                        {
                            "error": f"content group {content_group_id} is still in progress"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
        elif content_group_ids:
            for content_group_id in content_group_ids:
                if gen_status["content_groups"].get(content_group_id, {}).get(
                    "status"
                ) in ("IN_PROGRESS", "QUEUED"):
                    return Response(
                        {
                            "error": f"content group {content_group_id} is still in progress"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
        elif content_ids:
            contents = Content.objects.filter(id__in=content_ids)
            for content in contents:
                if content.content_status.get("gen_status", {}).get("status") in (
                    "IN_PROGRESS",
                    "QUEUED",
                ):
                    return Response(
                        {"error": f"content {content.id} is still in progress"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
        else:
            if gen_status.get("status") in ("IN_PROGRESS", "QUEUED"):
                return Response(
                    {"error": f"campaign {campaign.id} is still in progress"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        campaign_gen_wrapper = CampaignGenWrapper(campaign)
        task_id = campaign_gen_wrapper.submit_job(
            user=request.user,
            content_group_ids=content_group_ids,
            content_ids=content_ids,
            collection_ids=collection_ids,
            continue_gen=continue_gen,
            joint_generation=joint_generation,
            use_all_contents=False,
        )

        return Response({"task_id": task_id}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Use this to terminate generation for campaign or content_groups.",
        operation_description="API for terminating campaign contents.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content_group_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_INTEGER, "example": 1},
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def terminate_gen(self, request: Request, *args, **kwargs):
        """
        Return a task id to fetch results, for example:
        """
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        campaign = self.get_object()
        content_group_ids = request.data.get("content_group_ids")
        if not content_group_ids:
            content_groups = ContentGroup.objects.filter(campaign=campaign)
            content_group_ids = [content_group.id for content_group in content_groups]

        campaign_gen_wrapper = CampaignGenWrapper(campaign)
        campaign_gen_wrapper.terminate_gen(content_group_ids)

        updated_status = GenStatusUpdater().get_campaign_gen_status(campaign)
        return Response(updated_status, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    @measure_latency
    def gen_status(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        campaign = self.get_object()
        try:
            status_data = GenStatusUpdater().get_campaign_gen_status(campaign)
        except Exception as e:
            logging.error(
                f"error in campaign {campaign.id} gen_status: {e}\n{traceback.format_exc()}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(status_data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Use this to delete results of a campaign or content_groups and reset gen status",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content_group_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": openapi.Schema(type=openapi.TYPE_INTEGER),
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def delete_results(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        campaign = self.get_object()
        content_group_ids = request.data.get("content_group_ids", [])

        campaign_handler = CampaignHandler(campaign)
        campaign_handler.delete_results(content_group_ids)

        updated_status = GenStatusUpdater().get_campaign_gen_status(campaign)
        return Response(updated_status, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def metric_tiles(self, request: Request, *args, **kwargs):
        obj = self.get_object()
        if not obj:
            return Response(status=status.HTTP_404_NOT_FOUND)
        metric_tiles = CampaignHandler(obj).get_metric_tiles()
        return Response(metric_tiles, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Copy content collection for evaluation",
        operation_description="Copy content collection for evaluation",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "collection_id": 1,
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def copy_content_collection_for_eval(self, request: Request, *args, **kwargs):
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        campaign = self.get_object()
        collection_id = request.data.get("params", {}).get("collection_id", None)
        if not collection_id:
            return Response(
                data={"error": "collection_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            new_campaign = copy_content_collection_for_eval(campaign, collection_id)
            return Response(
                data=CampaignSerializer(new_campaign).data, status=status.HTTP_200_OK
            )
        except Exception as e:
            logging.error(f"error in copy_content_collection_for_eval: {e}")
            return Response(
                data={"error": f"Error in copy_content_collection_for_eval: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Check pregen scores for a campaign",
        operation_description="Check pregen scores for a campaign",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def check_pregen_scores(self, request, *args, **kwargs):
        # put the import here to skip the setting in unittest
        from ..evaluator.evaluators.campaign_scoring import CampaignScoringEvaluator

        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        campaign = self.get_object()
        campaign_evaluator = CampaignScoringEvaluator(campaign)
        try:
            check_result = campaign_evaluator.check_pregen_scores()
        except Exception as e:
            logging.error(
                f"error in campaign {campaign.id} check_pregen_scores: {e}\n{traceback.format_exc()}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(check_result, status=status.HTTP_200_OK)

    # write a function to get all actions for a campaign
    @swagger_auto_schema(
        method="get",
        operation_summary="Get all actions for a campaign",
        operation_description="Retrieves all actions associated with the specified campaign",
        responses={
            200: openapi.Response(
                description="List of actions successfully retrieved",
                schema=ActionSerializer(many=True),
            ),
            403: "Forbidden - User does not have permission",
            404: "Campaign not found",
        },
    )
    @action(detail=True, methods=["get"])
    def get_actions(self, request, *args, **kwargs):
        """Get all actions associated with a campaign"""
        campaign = self.get_object()
        if not campaign:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        actions = Action.objects.filter(campaign=campaign).order_by("-created_at")
        serializer = ActionSerializer(actions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def autopilot_runs(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        campaign = self.get_object()
        autopilot_runs = AutopilotObjectBuilder(campaign.id).build()
        return Response(
            {"runs": autopilot_runs},
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Convert legacy campaign to v3",
        operation_description="Convert legacy campaign to v3",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def convert_to_v3(self, request, *args, **kwargs):
        campaign = self.get_object()
        if not campaign:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        try:
            new_campaign = LegacyCampaignConverter(campaign).convert_to_v3()
            return Response(
                data=CampaignSerializer(new_campaign).data, status=status.HTTP_200_OK
            )
        except Exception as e:
            logging.exception(f"error in campaign {campaign.id} convert_to_v3: {e}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Get the next action that needs attention",
        operation_description="Returns the first action (ordered by id) that has a status of missing_input, ready, or fail",
        responses={
            status.HTTP_200_OK: ActionSerializer,
            status.HTTP_204_NO_CONTENT: "No action needs attention",
            status.HTTP_404_NOT_FOUND: "Campaign not found",
            status.HTTP_403_FORBIDDEN: "Forbidden",
        },
    )
    @action(detail=True, methods=["get"])
    def next_action_need_attention(self, request, *args, **kwargs):
        campaign = self.get_object()
        if not campaign:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        # Get all actions for this campaign
        actions = Action.objects.filter(campaign=campaign).order_by("id")

        # Get status for each action and find first one needing attention
        for action in actions:
            try:
                action_handler = ActionHandler(action)
                action_status = action_handler.update_status(save=True)
                status_type = action_status.status_type
                if status_type in [
                    ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT,
                    ActionStatusType.ACTION_STATUS_TYPE_READY,
                    ActionStatusType.ACTION_STATUS_TYPE_FAIL,
                ]:
                    serializer = ActionSerializer(action)
                    return Response(serializer.data, status=status.HTTP_200_OK)
                elif status_type in [
                    ActionStatusType.ACTION_STATUS_TYPE_COMPLETE,
                    ActionStatusType.ACTION_STATUS_TYPE_RUNNING,
                ]:
                    pass
                else:
                    logging.error(f"Unknown action status: {status_type}")
                    continue
            except Exception as e:
                logging.exception(f"Error checking action status: {str(e)}")
                continue

        return Response(status=status.HTTP_204_NO_CONTENT)
