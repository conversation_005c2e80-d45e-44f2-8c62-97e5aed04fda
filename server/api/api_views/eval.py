"""
Evaluation-related views for the Tofu API.
This module contains endpoints for running evaluation and benchmark tests.
"""

import logging
import uuid
from datetime import datetime

from django.core.cache import cache
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from ..async_eval import (
    async_eval_gen_review,
    async_eval_gen_review_repurpose,
    async_eval_gen_review_template,
)
from ..eval import (
    benchmark_review_personalization_campaigns,
    benchmark_review_repurpose_campaigns,
    benchmark_review_template_campaigns,
)
from ..release_tests.benchmark_batch_test import BenchmarkBatchTest
from ..release_tests.llm_inout_check.llm_check_runner import LLMInOutCheckRunner
from ..serializers import EmptySerializer
from ..utils import measure_latency


class EvalViewSet(viewsets.GenericViewSet):
    serializer_class = EmptySerializer

    def is_request_from_superuser(self, request):
        if request.user.is_superuser:
            return True
        return False

    @swagger_auto_schema(
        operation_summary="Generate all components given a list of content id",
        # operation_description='',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "campaign_ids": benchmark_review_personalization_campaigns,
                        "prefix": f'eval_{datetime.now().date().strftime("%Y-%m-%d")}',
                        "model": "gpt-4o-2024-11-20",
                        "num_of_variations": 1,
                        "enable_custom": True,
                        "generate_all_targets": [4480, 6599],
                        "joint_generation": True,
                        "test_only": False,
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=False, methods=["post"])
    @measure_latency
    def gen_review(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        prefix = request.data["params"].get("prefix")
        if not prefix:
            return Response(
                data={"error": "Missing prefix"}, status=status.HTTP_400_BAD_REQUEST
            )

        content_ids = []  # keep the placeholder in case we want to bring it back later
        campaign_ids = request.data["params"]["campaign_ids"]
        model_name = request.data["params"].get("model", "gpt-4o-2024-11-20")
        num_of_variations = request.data["params"].get("num_of_variations", 1)
        enable_custom = request.data["params"].get("enable_custom", False)
        generate_all_targets = request.data["params"].get("generate_all_targets", [])
        joint_generation = request.data["params"].get("joint_generation", False)
        test_only = request.data["params"].get("test_only", True)

        logging.info(
            f"Generating eval doc for campaigns {campaign_ids} and contents {content_ids} with {model_name}"
        )

        task_id = f"gen_review_{uuid.uuid4()}"
        cache.set(task_id, {}, timeout=60 * 60 * 24)
        ret = async_eval_gen_review.apply_async(
            args=[
                request.user.id if request.user else None,
                prefix,
                campaign_ids,
                content_ids,
                model_name,
                num_of_variations,
                enable_custom,
                generate_all_targets,
                joint_generation,
                False,
                test_only,
            ],
            task_id=task_id,
            priority=1,
        )

        logging.info(
            f"debug: ret is {ret} and ret.id is {ret.id} and ret.status is {ret.status}"
        )

        return Response({"task_id": task_id}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Generate templates for the given ids",
        # operation_description='',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "campaign_ids": benchmark_review_template_campaigns,
                        "prefix": f'eval_{datetime.now().date().strftime("%Y-%m-%d")}',
                        "model": "gpt-4o-2024-11-20",
                        "num_of_variations": 1,
                        "enable_custom": False,
                        "generate_all_targets": [],
                        "template_generation": True,
                        "test_only": False,
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=False, methods=["post"])
    @measure_latency
    def gen_review_template(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        prefix = request.data["params"].get("prefix")
        if not prefix:
            return Response(
                data={"error": "Missing prefix"}, status=status.HTTP_400_BAD_REQUEST
            )

        content_ids = []  # keep the placeholder in case we want to bring it back later
        campaign_ids = request.data["params"]["campaign_ids"]
        model_name = request.data["params"].get("model", "gpt-4o-2024-11-20")
        num_of_variations = request.data["params"].get("num_of_variations", 1)
        enable_custom = request.data["params"].get("enable_custom", False)
        generate_all_targets = request.data["params"].get("generate_all_targets", [])
        template_generation = request.data["params"].get("template_generation", True)
        test_only = request.data["params"].get("test_only", True)

        logging.info(
            f"Generating eval doc for campaigns {campaign_ids} and contents {content_ids} with {model_name}"
        )

        task_id = f"gen_review_template_{uuid.uuid4()}"
        cache.set(task_id, {}, timeout=60 * 60 * 24)
        ret = async_eval_gen_review_template.apply_async(
            args=[
                request.user.id if request.user else None,
                prefix,
                campaign_ids,
                content_ids,
                model_name,
                num_of_variations,
                enable_custom,
                generate_all_targets,
                test_only,
            ],
            task_id=task_id,
            priority=1,
        )

        logging.info(
            f"debug: ret is {ret} and ret.id is {ret.id} and ret.status is {ret.status}"
        )

        return Response({"task_id": task_id}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Generate repurpose review contents",
        # operation_description='',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "campaign_ids": benchmark_review_repurpose_campaigns,
                        "prefix": f'eval_{datetime.now().date().strftime("%Y-%m-%d")}',
                        "model": "gpt-4o-2024-11-20",
                        "num_of_variations": 1,
                        "enable_custom": True,
                        "generate_all_targets": [],
                        "test_only": False,
                        "joint_generation": True,
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=False, methods=["post"])
    @measure_latency
    def gen_review_repurpose(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        prefix = request.data["params"].get("prefix")
        if not prefix:
            return Response(
                data={"error": "Missing prefix"}, status=status.HTTP_400_BAD_REQUEST
            )

        content_ids = []  # keep the placeholder in case we want to bring it back later
        campaign_ids = request.data["params"]["campaign_ids"]
        model_name = request.data["params"].get("model", "gpt-4o-2024-11-20")
        num_of_variations = request.data["params"].get("num_of_variations", 1)
        enable_custom = request.data["params"].get("enable_custom", False)
        generate_all_targets = request.data["params"].get("generate_all_targets", [])
        test_only = request.data["params"].get("test_only", True)
        joint_generation = request.data["params"].get("joint_generation", True)

        logging.info(
            f"Generating eval doc for campaigns {campaign_ids} and contents {content_ids} with {model_name}"
        )

        task_id = f"gen_review_repurpose_{uuid.uuid4()}"
        cache.set(task_id, {}, timeout=60 * 60 * 24)
        ret = async_eval_gen_review_repurpose.apply_async(
            args=[
                request.user.id if request.user else None,
                prefix,
                campaign_ids,
                content_ids,
                model_name,
                num_of_variations,
                enable_custom,
                generate_all_targets,
                joint_generation,
                test_only,
            ],
            task_id=task_id,
            priority=1,
        )

        logging.info(
            f"debug: ret is {ret} and ret.id is {ret.id} and ret.status is {ret.status}"
        )

        return Response({"task_id": task_id}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Integration test with benchmark data",
        # operation_description='',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "quickRun": True,
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=False, methods=["post"])
    def test_benchmark(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        quickRun = request.data["params"].get("quickRun", True)
        BenchmarkBatchTest(quickRun=quickRun).run()

        return Response(status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def release_test(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        test_intf = ReleaseTestIntf()
        res = test_intf.run_release_tests()
        if res:
            return Response(status=status.HTTP_200_OK)
        else:
            errors = test_intf.get_errors()
            return Response(status=status.HTTP_400_BAD_REQUEST, data={"errors": errors})

    @action(detail=False, methods=["post"])
    def update_golden_testset(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            runner = LLMInOutCheckRunner()
            session_id = runner.update_golden_set()
        except Exception as e:
            return Response(
                data={"error": f"Error in update_golden_testset: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if session_id:
            return Response(status=status.HTTP_200_OK, data={"session_id": session_id})
        else:
            return Response(status=status.HTTP_400_BAD_REQUEST)
