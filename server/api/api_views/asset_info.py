"""
Asset Info related views for the Tofu API.
This module contains endpoints for managing asset information within a playbook.
"""

import logging
from datetime import datetime

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import mixins, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response

from ..connected_assets.connected_assets_async_tasks import (
    async_refresh_connected_asset_group,
)
from ..connected_assets.connected_assets_refresher import (
    ConnectedAssetsGroupRefreshStatus,
    get_connected_asset_group_refresh_status,
    update_connected_asset_group_refresh_status,
)
from ..connected_assets.connected_assets_utils import is_tofu_connected_assets_group
from ..models import (
    AssetInfo,
    AssetInfoGroup,
    Playbook,
    PlaybookUser,
)
from ..playbook_build.object_builder import ObjectBuilder
from ..serializers import (
    AssetInfoGroupSerializer,
    AssetInfoSerializer,
)


class AssetInfoGroupViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    queryset = AssetInfoGroup.objects.all()
    serializer_class = AssetInfoGroupSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "asset_info_group_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.lookup_url_kwarg in self.kwargs:
            context["asset_info_group_id"] = self.kwargs[self.lookup_url_kwarg]

        if "legacy" in self.request.query_params:
            legacy_literal = self.request.query_params.get("legacy")
            context["legacy"] = legacy_literal.lower() in ["true", "1", "yes"]
        return context

    def is_request_from_creator_or_user_or_superuser(self, request):
        asset_info_group = self.get_object()
        playbook = asset_info_group.playbook
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def is_request_from_creator_or_user_or_superuser_without_object(
        self, request, playbook
    ):
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="legacy",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
            )
        ],
    )
    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Retrieve asset info group by key",
        operation_description="",
        manual_parameters=[
            openapi.Parameter(
                name="playbook_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
            openapi.Parameter(
                name="l1_key",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
            ),
        ],
    )
    @action(detail=False, methods=["get"])
    def retrieve_by_keys(self, request, *args, **kwargs):
        playbook_id = request.query_params.get("playbook_id")

        playbook = Playbook.objects.filter(id=playbook_id).first()
        if not playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser_without_object(
            request, playbook
        ):
            return Response(status=status.HTTP_403_FORBIDDEN)
        l1_key = request.query_params.get("l1_key")
        if not playbook_id or not l1_key:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        asset_group_info = AssetInfoGroup.objects.filter(
            playbook_id=playbook_id, asset_info_group_key=l1_key
        )
        if not asset_group_info:
            return Response(status=status.HTTP_404_NOT_FOUND)
        asset_group_info = asset_group_info.first()
        serializer = self.serializer_class
        return Response(serializer(asset_group_info).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Refresh connected assets in an asset group",
        operation_description="Triggers an asynchronous refresh of connected assets in the asset group",
        responses={
            status.HTTP_200_OK: "Refresh task started successfully",
            status.HTTP_400_BAD_REQUEST: "Invalid request",
            status.HTTP_404_NOT_FOUND: "Asset group not found",
        },
    )
    @action(detail=True, methods=["post"])
    def refresh(self, request, *args, **kwargs):
        """Trigger an asynchronous refresh of connected assets in the asset group."""
        asset_info_group = self.get_object()

        # Check if this is a connected asset group
        if not is_tofu_connected_assets_group(asset_info_group):
            logging.warning(
                f"Asset group {asset_info_group.id} is not tofu managed, skipping"
            )
            return Response(
                {"error": "This asset group is not a connected asset group"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        current_status = get_connected_asset_group_refresh_status(asset_info_group)
        if current_status == ConnectedAssetsGroupRefreshStatus.IN_PROGRESS.value:
            logging.error(f"Asset group {asset_info_group.id} is already in progress")
            return Response(
                {"message": "Refresh task already in progress"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Trigger the async task
        task_id = f"refresh_connected_asset_group_{asset_info_group.id}:{datetime.now().isoformat()}"
        update_connected_asset_group_refresh_status(
            asset_info_group, ConnectedAssetsGroupRefreshStatus.NOT_STARTED
        )
        try:
            # Use 6 hours as soft time limit for the refresh task
            # We use soft time so we could catch the timeout exception and update the status
            async_refresh_connected_asset_group.apply_async(
                args=[asset_info_group.id, task_id],
                task_id=task_id,
                soft_time_limit=60 * 60 * 6,
                priority=8,
            )
        except Exception as e:
            logging.exception(
                f"Failed to submit refresh task for asset group {asset_info_group.id}, error: {e}"
            )
            update_connected_asset_group_refresh_status(
                asset_info_group, ConnectedAssetsGroupRefreshStatus.ERROR
            )
            return Response(
                {"message": "Failed to submit refresh task"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(
            {
                "message": "Refresh task started",
                "task_id": task_id,
            },
            status=status.HTTP_200_OK,
        )


class AssetInfoViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    queryset = AssetInfo.objects.all()
    serializer_class = AssetInfoSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "asset_info_id"

    def get_queryset(self):
        if self.action == "list":
            # Raise an exception to indicate that the list method is not allowed
            raise MethodNotAllowed("GET", detail="List method is not allowed.")
        return super().get_queryset()

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def is_request_from_creator_or_user_or_superuser(self, request):
        asset_info = self.get_object()
        playbook = asset_info.asset_info_group.playbook
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def is_request_from_creator_or_user_or_superuser_without_object(
        self, request, playbook
    ):
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Retrieve asset info by key",
        operation_description="",
        manual_parameters=[
            openapi.Parameter(
                name="playbook_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
            openapi.Parameter(
                name="l1_key",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
            ),
            openapi.Parameter(
                name="l2_key",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
            ),
        ],
    )
    @action(detail=False, methods=["get"])
    def retrieve_by_keys(self, request, *args, **kwargs):
        playbook_id = request.query_params.get("playbook_id")
        playbook = Playbook.objects.filter(id=playbook_id).first()
        if not playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser_without_object(
            request, playbook
        ):
            return Response(status=status.HTTP_403_FORBIDDEN)

        l1_key = request.query_params.get("l1_key")
        l2_key = request.query_params.get("l2_key")
        if not playbook_id or not l1_key or not l2_key:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        asset_info = AssetInfo.objects.filter(
            asset_info_group__playbook_id=playbook_id,
            asset_info_group__asset_info_group_key=l1_key,
            asset_key=l2_key,
        )
        if not asset_info:
            return Response(status=status.HTTP_404_NOT_FOUND)
        asset_info = asset_info.first()
        serializer = self.serializer_class
        return Response(serializer(asset_info).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Rebuild asset info",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
    )
    @action(detail=True, methods=["post"])
    def rebuild(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        asset_info = self.get_object()
        asset_builder = ObjectBuilder.get_builder(asset_info)
        asset_builder.build_docs(rebuild=True)
        asset_info.refresh_from_db()
        return Response(
            data=self.serializer_class(asset_info).data, status=status.HTTP_200_OK
        )
