"""
Chatbot-related views for the Tofu API.
This module contains endpoints for managing chat interactions with AI models.
"""

import logging
import traceback

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.request import Request
from rest_framework.response import Response

from ..chatbot import (
    playground_chat,
    poll_playground_chat_response,
    reset_playground_chat,
    terminate_gen,
)
from ..models import Cha<PERSON><PERSON><PERSON><PERSON>, TofuUser
from ..serializers import ChatHistorySerializer, LiteChatHistorySerializer


class ChatbotViewSet(viewsets.ModelViewSet):
    serializer_class = ChatHistorySerializer
    queryset = ChatHistory.objects.all()
    lookup_field = "pk"
    lookup_url_kwarg = "thread_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        if not obj:
            logging.error(
                f"Chat thread not found: {self.kwargs[self.lookup_url_kwarg]}"
            )
        return obj

    def is_request_from_owner_or_superuser(self, request):
        chat_thread = self.get_object()
        if chat_thread:
            creator = chat_thread.creator
            if creator == request.user or request.user.is_superuser:
                return True
        return False

    def is_tofu_lite_user(self, request):
        if request.user.customer_type == TofuUser.CustomerType.LITE:
            return True
        return False

    @swagger_auto_schema(
        operation_summary="Get all the chat histories",
        operation_description="",
    )
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        self.queryset = queryset.filter(creator=request.user).order_by("-created_at")

        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Get all the lite chat histories for the current user",
        operation_description="Get all the lite chat histories for the current user",
    )
    @action(detail=False, methods=["get"])
    def list_lite(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        self.queryset = queryset.filter(creator=request.user).order_by("-created_at")
        self.serializer_class = LiteChatHistorySerializer
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Chat with models",
        operation_description="Chat with models",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "model": "gpt-4o-2024-11-20",
                        "message": "Hi how are you?",
                        "use_company_info": True,
                        "use_brand_guidelines": True,
                        "regenerate": False,
                        "targets": {},
                        "assets": {},
                    },
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def chat(self, request, *args, **kwargs):
        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this chat"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        model = request.data["params"].get("model") or "gpt-4o-2024-11-20"
        if model not in [
            "gpt-4o-2024-11-20",
            "claude-3-5-sonnet-20240620",
            "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
            "us.anthropic.claude-3-7-sonnet-20250219-thinking-v1:0",
            "claude-3-7-sonnet-20250219",
            "deepseek-chat",
            "deepseek-reasoner",
            "gemini-2.0-flash-exp",
            "o1-2024-12-17",
            "o3-mini-2025-01-31",
            "gpt-4.5-preview",
            "gpt-4.1-2025-04-14",
            "o3-2025-04-16",
            "o4-mini-2025-04-16",
            "claude-sonnet-4-20250514",
            "claude-opus-4-20250514",
            "us.anthropic.claude-sonnet-4-20250514-v1:0",
            "us.anthropic.claude-opus-4-20250514-v1:0",
        ]:
            return Response(
                data={"error": f"Model {model} is not supported."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        message = request.data["params"].get("message", "")
        use_company_info = request.data["params"].get("use_company_info", True)
        use_brand_guidelines = request.data["params"].get("use_brand_guidelines", True)
        regenerate = request.data["params"].get("regenerate", False)
        targets = request.data["params"].get("targets", {})
        assets = request.data["params"].get("assets", {})
        is_deepresearch = request.data["params"].get("is_deepresearch", False)
        s3_files = request.data["params"].get("s3_files", None)
        image_gen = request.data["params"].get("image_gen", False)
        chat_thread = self.get_object()
        if not chat_thread:
            return Response(
                data={"error": "Chat thread not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        key = chat_thread.key
        try:
            # set thread local user and playbook
            async_task_id = playground_chat(
                request.user,
                model,
                message,
                key,
                use_company_info=use_company_info,
                use_brand_guidelines=use_brand_guidelines,
                regenerate=regenerate,
                targets=targets,
                assets=assets,
                is_deepresearch=is_deepresearch,
                s3_files=s3_files,
                image_gen=image_gen,
            )
        except Exception as e:
            logging.error(f"Error in chat: {e}\n{traceback.format_exc()}")
            return Response(
                data={"error": f"Error in chat: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        response_data = {"model": model, "task_id": async_task_id}

        return Response(response_data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Use this to terminate generation for chat.",
        operation_description="API for terminating chat.",
        manual_parameters=[
            openapi.Parameter(
                name="task_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="task_id",
                required=True,
            ),
        ],
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def terminate_gen(self, request: Request, *args, **kwargs):
        """
        Terminate task given task_id
        """
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        task_id = request.query_params.get("task_id", None)
        if not task_id:
            return Response(
                data={"error": "task_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        response_data = terminate_gen(task_id)

        return Response(response_data, status=status.HTTP_200_OK)

    # define an API to query the response from task_id which is created above
    @swagger_auto_schema(
        operation_summary="Query the response from task_id",
        operation_description="Query the response from task_id",
        manual_parameters=[
            openapi.Parameter(
                name="task_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="task_id",
                required=True,
            ),
        ],
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["get"])
    def poll(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_owner_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        task_id = request.query_params.get("task_id", None)
        if not task_id:
            return Response(
                data={"error": "task_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            response = poll_playground_chat_response(task_id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            logging.error(f"Error in poll: {e}")
            return Response(
                data={"error": f"Error in poll: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Reset chat",
        operation_description="Reset chat",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "model": {
                    "type": openapi.TYPE_STRING,
                    "example": "gpt-4o-2024-11-20",
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=False, methods=["post"])
    def reset(self, request, *args, **kwargs):
        model = request.data.get("model", "gpt-4o-2024-11-20")
        try:
            chat_history = reset_playground_chat(request.user, model)
            new_key = chat_history.key
        except Exception as e:
            logging.error(f"Error in reset: {e}")
            return Response(
                data={"error": f"Error in reset: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        response_data = {"key": new_key}
        return Response(response_data, status=status.HTTP_200_OK)
