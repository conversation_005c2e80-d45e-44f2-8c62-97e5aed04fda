"""
ActionEdge-related views for the Tofu API.
This module contains endpoints for managing action edge relationships in the system.
"""

import logging

from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db import IntegrityError
from rest_framework import mixins, status, viewsets
from rest_framework.response import Response

from ..actions.action_edge_creator import ActionEdgeCreator
from ..models import Action, ActionEdge
from ..serializers import ActionEdgeSerializer


class ActionEdgeViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    queryset = ActionEdge.objects.all()
    serializer_class = ActionEdgeSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "action_edge_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def is_request_from_creator_or_superuser(self, request, action_edge):
        if not action_edge:
            return False
        return (
            action_edge.from_action.creator == request.user
            or action_edge.to_action.creator == request.user
            or request.user.is_superuser
        )

    def create(self, request, *args, **kwargs):
        """Create a new action edge."""
        # Validate required fields
        if not request.data.get("to_action") or not request.data.get("from_action"):
            return Response(
                {"error": "Both to_action and from_action are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            edge_creator = ActionEdgeCreator()
            outgoing_action_id = request.data.get("to_action")
            incoming_edges = [
                {
                    "from_action": request.data.get("from_action"),
                    "config": request.data.get("config", {}),
                }
            ]

            # Verify permissions
            outgoing_action = Action.objects.get(id=outgoing_action_id)
            if not outgoing_action:
                return Response(
                    {"error": "Outgoing action not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            if not (
                outgoing_action.creator == request.user or request.user.is_superuser
            ):
                return Response(
                    {"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN
                )

            edge_creator.create(outgoing_action, incoming_edges)

            return Response(status=status.HTTP_201_CREATED)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as e:
            return Response(
                {"error": f"Action edge already exists: {str(e)}"},
                status=status.HTTP_409_CONFLICT,
            )
        except (ValueError, ObjectDoesNotExist) as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logging.exception(f"Error creating action edge: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def update(self, request, *args, **kwargs):
        """Update an existing action edge."""
        action_edge = self.get_object()
        if not action_edge:
            return Response(status=status.HTTP_404_NOT_FOUND)

        if not self.is_request_from_creator_or_superuser(request, action_edge):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            edge_creator = ActionEdgeCreator()
            config = request.data.get("config", {})
            edge_creator.update(action_edge, config)

            return Response(status=status.HTTP_200_OK)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logging.error(f"Error updating action edge: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
