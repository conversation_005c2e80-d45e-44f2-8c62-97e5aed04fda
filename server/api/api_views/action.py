"""
Action-related views for the Tofu API.
This module contains endpoints for managing workflow actions and their execution.
"""

import logging
import time
import traceback

from drf_yasg import openapi
from drf_yasg.utils import no_body, swagger_auto_schema
from google.protobuf.json_format import MessageToDict, ParseDict
from rest_framework import serializers, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from ..actions.action_data_wrapper import ActionDataWrapper
from ..actions.action_handler import ActionHandler
from ..actions.action_system_config_loader import ActionSystemConfigLoader
from ..actions.legacy_converter.legacy_components_converter import (
    convert_components_v2_to_v3,
)
from ..actions.legacy_converter.legacy_custom_instruction_converter import (
    convert_custom_instructions_v2_to_v3,
)
from ..actions.legacy_converter.legacy_export_settings_converter import (
    convert_export_settings_v2_to_v3,
)
from ..actions.legacy_converter.legacy_repurpose_template_converter import (
    convert_repurpose_template_v2_to_v3,
)
from ..actions.legacy_converter.legacy_template_converter import (
    convert_template_v2_to_v3,
)
from ..actions.tofu_data_wrapper import TofuDataList<PERSON>andler
from ..evaluator.evaluate_data_duplicate import ActionDuplicator
from ..mixins import TofuPagesKillSwitchMixin
from ..models import Action, ContentTemplate
from ..serializers import ActionSerializer, CampaignSerializer
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionExecutionParams,
    TofuDataList,
)
from ..utils import sort_action_types


class ActionViewSet(TofuPagesKillSwitchMixin, viewsets.ModelViewSet):
    queryset = Action.objects.all()
    serializer_class = ActionSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "action_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def is_request_from_creator_or_superuser(self, request):
        action = self.get_object()
        if action and (action.creator == request.user or request.user.is_superuser):
            return True
        return False

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        self.queryset = queryset.filter(creator=request.user).order_by("-created_at")
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        if not request.user:
            return Response(status=status.HTTP_403_FORBIDDEN)

        if "creator" not in request.data:
            request.data["creator"] = request.user.id

        try:
            # Use the serializer directly to create and get the instance
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save()

            # Now you can access the campaign
            campaign = instance.campaign

            # Get the full action data if needed
            full_action_data = ActionSerializer(instance).get_campaign_actions(campaign)

            # Return whatever data you want based on the campaign
            return Response(full_action_data, status=status.HTTP_201_CREATED)

        except serializers.ValidationError as e:
            error_msg = e.detail[0] if isinstance(e.detail, list) else e.detail
            return Response(
                {"error": str(error_msg)}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logging.exception(f"Unexpected error creating action: {e}")
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        try:
            ActionHandler(self.get_object()).delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            logging.exception(f"Error deleting action: {str(e)}")
            return Response(
                {"error": f"Failed to delete action: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Update action name",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["action_name"],
            properties={
                "action_name": openapi.Schema(
                    type=openapi.TYPE_STRING, description="New name for the action"
                )
            },
        ),
        responses={
            status.HTTP_200_OK: "Action name updated successfully",
            status.HTTP_400_BAD_REQUEST: "Invalid request or update failed",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Action not found",
        },
    )
    @action(detail=True, methods=["patch"])
    def update_name(self, request, *args, **kwargs):
        action_instance = self.get_object()
        if not action_instance:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        action_name = request.data.get("action_name")
        if not action_name:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        try:
            ActionHandler(action_instance).update_action_name(action_name)
            return Response(status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error updating action name: {str(e)}")
            return Response(
                {"error": f"Failed to update action name: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Update action inputs",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["inputs"],
            properties={
                "inputs": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="New input configuration for the action",
                )
            },
        ),
        responses={
            status.HTTP_200_OK: "Action inputs updated successfully",
            status.HTTP_400_BAD_REQUEST: "Invalid request or update failed",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Action not found",
        },
    )
    @action(detail=True, methods=["patch"])
    def update_inputs(self, request, *args, **kwargs):
        action_instance = self.get_object()
        if not action_instance:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        inputs = request.data.get("inputs", {})
        if not inputs:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        # inputs shall be the only field in the request
        if len(request.data) > 1:
            return Response(
                {
                    "error": f"Invalid inputs - request should only contain 'inputs' field, but got {request.data.keys()}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            ActionHandler(action_instance).update_inputs(inputs)
            return Response(
                data=ActionSerializer(action_instance).data, status=status.HTTP_200_OK
            )
        except Exception as e:
            logging.exception(f"Error updating action inputs: {str(e)}")
            return Response(
                {"error": f"Failed to update action inputs: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Get all inputs of an action from incoming edges",
        responses={
            status.HTTP_200_OK: "Action inputs",
            status.HTTP_400_BAD_REQUEST: "Invalid request or update failed",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Action not found",
        },
    )
    @action(detail=True, methods=["get"])
    def get_all_inputs_from_incoming_edges(self, request, *args, **kwargs):
        action_instance = self.get_object()
        if not action_instance:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        try:
            inputs = ActionDataWrapper(
                action_instance
            ).get_all_inputs_from_incoming_edges()
            for key, value in inputs.items():
                if isinstance(value, TofuDataList):
                    inputs[key] = TofuDataListHandler.convert_tofu_data_to_json(value)
            return Response(inputs, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error getting action inputs: {str(e)}")
            return Response(
                {"error": f"Failed to get action inputs: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Execute an action",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "execution_params": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Parameters for action execution",
                    example={
                        "personalization_params": {
                            "continue_gen": True,
                            "joint_generation": True,
                        }
                    },
                )
            },
        ),
        responses={
            status.HTTP_200_OK: "Action started",
            status.HTTP_404_NOT_FOUND: "Action not found",
            status.HTTP_400_BAD_REQUEST: "Invalid request or action validation failed",
        },
    )
    @action(detail=True, methods=["post"])
    def execute(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        execution_params = request.data.get("execution_params", {})
        if execution_params:
            try:
                execution_params = ParseDict(execution_params, ActionExecutionParams())
            except Exception as e:
                logging.exception(f"Error parsing execution params: {str(e)}")
                return Response(
                    {"error": f"Invalid execution params: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            execution_params = ActionExecutionParams()

        try:
            action_handler = ActionHandler(action)
            action_handler.execute(execution_params=execution_params)
            action_category = action.action_category
            time_wait = (
                6
                if action_category
                == ActionCategory.Name(ActionCategory.ACTION_CATEGORY_USER_INPUT)
                else 3
            )
            time.sleep(time_wait)
            # update action data.
            action = self.get_object()
            return Response(
                data=ActionSerializer(action).data, status=status.HTTP_200_OK
            )
        except ValueError as e:
            logging.exception(f"Error executing action: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logging.exception(f"Error executing action: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="Terminate a running action",
        request_body=no_body,  # Use no_body instead of None
        manual_parameters=[],  # Empty list to override default parameters
        responses={
            status.HTTP_200_OK: "Action terminated",
            status.HTTP_404_NOT_FOUND: "Action not found",
            status.HTTP_400_BAD_REQUEST: "Action is not running or termination failed",
        },
    )
    @action(detail=True, methods=["post"])
    def terminate(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            action_handler = ActionHandler(action)
            action_handler.terminate()
            # TODO: campaign v3: check what terminate data we want to return
            return Response(
                data=ActionSerializer(action).data, status=status.HTTP_200_OK
            )
        except Exception as e:
            logging.exception(f"Error terminating action: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Get action status",
        responses={
            status.HTTP_200_OK: "Action status",
            status.HTTP_404_NOT_FOUND: "Action not found",
        },
    )
    @action(detail=True, methods=["get"])
    def get_status(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            action_handler = ActionHandler(action)
            updated_status = action_handler.update_status(save=True)
            return Response(
                MessageToDict(updated_status, preserving_proto_field_name=True),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging.exception(f"Error getting action status: {str(e)}")
            return Response(
                {"error": f"Failed to get action status: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Get action definition by action ID",
        responses={
            status.HTTP_200_OK: "Action definition",
            status.HTTP_404_NOT_FOUND: "Action not found",
            status.HTTP_400_BAD_REQUEST: "Invalid request",
        },
    )
    @action(detail=True, methods=["get"])
    def definition(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            action_def = ActionDataWrapper(action).action_definition
            if not action_def:
                return Response(
                    {
                        "error": f"No definition found for action type {action.action_type}"
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )
            action_def_dict = MessageToDict(
                action_def, preserving_proto_field_name=True
            )

            if "eligible_next_actions" in action_def_dict:
                action_def_dict["eligible_next_actions"] = sort_action_types(
                    action_def_dict["eligible_next_actions"]
                )

            return Response(action_def_dict, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error getting action definition: {str(e)}")
            return Response(
                {"error": f"Failed to get action definition: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Get action definition by action type",
        manual_parameters=[
            openapi.Parameter(
                name="action_type",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="Action type (e.g. ACTION_TYPE_REPURPOSE_EMAIL_SDR)",
            ),
        ],
        responses={
            status.HTTP_200_OK: "Action definition",
            status.HTTP_404_NOT_FOUND: "Action type not found",
            status.HTTP_400_BAD_REQUEST: "Invalid request",
        },
    )
    @action(detail=False, methods=["get"])
    def definition_by_type(self, request, *args, **kwargs):
        action_type = request.query_params.get("action_type")
        if not action_type:
            return Response(
                {"error": "action_type parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            action_def = ActionSystemConfigLoader().get_definition(action_type)
            if not action_def:
                return Response(
                    {"error": f"No definition found for action type {action_type}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            action_def_dict = MessageToDict(
                action_def, preserving_proto_field_name=True
            )
            if "eligible_next_actions" in action_def_dict:
                action_def_dict["eligible_next_actions"] = sort_action_types(
                    action_def_dict["eligible_next_actions"]
                )

            return Response(action_def_dict, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error getting action definition: {str(e)}")
            return Response(
                {"error": f"Failed to get action definition: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Get all eligible action definitions",
        operation_description="Returns a list of all eligible action definitions that can be used in the system",
        responses={
            status.HTTP_200_OK: "List of all eligible action definitions",
            status.HTTP_400_BAD_REQUEST: "Invalid request",
        },
    )
    @action(detail=False, methods=["get"])
    def all_eligible_actions(self, request, *args, **kwargs):
        try:
            all_action_def = ActionSystemConfigLoader().get_all_eligible_actions()
            sorted_all_action_def = sort_action_types(all_action_def)
            # Convert the map container to a regular dictionary
            result = {}
            for key, value in sorted_all_action_def.items():
                result[key] = MessageToDict(value, preserving_proto_field_name=True)
            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error getting action definition: {str(e)}")
            return Response(
                {"error": f"Failed to get action definition: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Convert components data schema from v2 to v3",
        operation_description="Converts components data from schema version 2 to version 3 format",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["components"],
            properties={
                "components": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Components data in v2 format",
                    example={
                        "-qOT61701CA30D3c": {
                            "meta": {
                                "time_added": 1722967286,
                                "component_type": "email body",
                                "isEmailSubject": False,
                                "precedingContent": "",
                                "succeedingContent": "",
                            },
                            "text": "Repurpose Content",
                        },
                        "5_J_9xAIcBNxW1BL": {
                            "meta": {
                                "time_added": 1722967286,
                                "component_type": "email subject",
                                "isEmailSubject": True,
                                "component_params": {"custom_instructions": []},
                                "precedingContent": "",
                                "succeedingContent": "",
                            },
                            "text": "Email Subject",
                        },
                    },
                )
            },
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Successfully converted components",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "components": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            description="Components data in v3 format",
                        ),
                    },
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Invalid components data format",
            status.HTTP_500_INTERNAL_SERVER_ERROR: "Conversion error",
        },
    )
    @action(detail=True, methods=["post"])
    def convert_components_v2_to_v3(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        playbook_id = action.playbook.id
        components_v2 = request.data.get("components", {})
        try:
            components_v3 = convert_components_v2_to_v3(
                playbook_id, components_v2, compare=True
            )
            components_v3_tofu_data_list = (
                TofuDataListHandler.convert_components_to_tofu_data(components_v3)
            )
            components_v3_tofu_data_list_dict = (
                TofuDataListHandler.convert_tofu_data_to_json(
                    components_v3_tofu_data_list
                )
            )
            return Response(
                components_v3_tofu_data_list_dict, status=status.HTTP_200_OK
            )
        except Exception as e:
            logging.exception(f"Error converting components from v2 to v3: {str(e)}")
            return Response(
                {"error": f"Failed to convert components from v2 to v3: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Convert template from v2 to v3",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["content_group_params"],
            properties={
                "content_group_params": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Content group parameters in v2 format",
                    example={
                        "content_type": "Email - SDR",
                        "initialSetup": False,
                        "content_source": "3200738.json",
                        "no_rename_alert": True,
                        "template_settings": {
                            "follow_tone": True,
                            "follow_length": True,
                            "tone_reference": None,
                            "follow_core_message_and_key_point": True,
                        },
                        "content_source_copy": "/api/web/storage/s3-presigned-url?file=b5e9eabc-2712-4bfa-80ff-5d21fac2e27d-3bbbaa5a-6926-439c-a941-a4daf647a9aa-1ad289ba-e7b4-45b1-9260-aa4e6576f05d-7289d1f4-3647-4685-aac7-0fc44140be91-eec59fe7-2c41-2419-aae7-f6bfae445d5d.json&fileType=application/json&directory=tofu-uploaded-files",
                        "custom_instructions": [
                            {"assets": None, "instruction": "don't use cliches. "}
                        ],
                        "selected_pdf_method": "pdf_content_edit",
                        "content_source_format": "Text",
                        "orig_content_group_id": 234312,
                        "content_source_upload_method": "Text",
                        "subject_line_only_content_source_copy": None,
                    },
                ),
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def convert_template_v2_to_v3(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        playbook_id = action.playbook.id
        content_group_params = request.data.get("content_group_params", {})
        try:
            template = convert_template_v2_to_v3(playbook_id, content_group_params)
            template_tofu_data_list = TofuDataListHandler.convert_template_to_tofu_data(
                template
            )
            template_tofu_data_list_dict = (
                TofuDataListHandler.convert_tofu_data_to_json(template_tofu_data_list)
            )
            return Response(template_tofu_data_list_dict, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error converting template from v2 to v3: {str(e)}")
            return Response(
                {"error": f"Failed to convert template from v2 to v3: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Convert repurpose template from v3 to v2",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["content_group_params"],
            properties={
                "content_group_params": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Content group parameters in v2 format",
                    example={
                        "gen_status": {"status": "NOT_STARTED"},
                        "content_type": "Email - SDR",
                        "initialSetup": False,
                        "hasAnalysisRun": True,
                        "template_settings": {
                            "follow_tone": True,
                            "follow_length": True,
                            "tone_reference": None,
                            "follow_core_message_and_key_point": False,
                        },
                        "custom_instructions": [],
                        "content_source_format": "Text",
                        "content_source_upload_method": "Text",
                        "content_source": "1562565.json",
                        "repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=7e00eba1-7aab-371e-3c08-ddc7f3f90c37.json&fileType=application/json&directory=tofu-uploaded-files",
                        "slate_repurpose_template_content_source": "1562565.json",
                        "slate_repurpose_template_content_source_copy": "/api/web/storage/s3-presigned-url?file=69842ad6-72a2-93b2-1cd5-c3937077a7b3.json&fileType=application/json&directory=tofu-uploaded-files",
                    },
                ),
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def convert_repurpose_template_v2_to_v3(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        playbook_id = action.playbook.id
        content_group_params = request.data.get("content_group_params", {})
        try:
            template = convert_repurpose_template_v2_to_v3(
                playbook_id, content_group_params
            )
            template_tofu_data_list = TofuDataListHandler.convert_template_to_tofu_data(
                template
            )
            template_tofu_data_list_dict = (
                TofuDataListHandler.convert_tofu_data_to_json(template_tofu_data_list)
            )
            return Response(template_tofu_data_list_dict, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error converting template from v2 to v3: {str(e)}")
            return Response(
                {"error": f"Failed to convert template from v2 to v3: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    # add one API to convert custom instruction from v2 to v3
    @swagger_auto_schema(
        operation_summary="Convert custom instructions from v2 to v3",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["custom_instructions"],
            properties={
                "custom_instructions": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT),
                )
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def convert_custom_instructions_v2_to_v3(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        playbook_id = action.playbook.id
        custom_instructions = request.data.get("custom_instructions", [])
        if not playbook_id:
            logging.error("playbook_id is required")
            return Response(
                data={"error": "playbook_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            custom_instructions_v3 = convert_custom_instructions_v2_to_v3(
                playbook_id, custom_instructions
            )
            return Response(
                data=MessageToDict(
                    custom_instructions_v3, preserving_proto_field_name=True
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging.exception(
                f"Error converting custom instruction from v2 to v3: {str(e)}"
            )
            return Response(
                data={
                    "error": f"Failed to convert custom instruction from v2 to v3: {str(e)}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Convert export settings from v2 to v3",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["content_group_params"],
            properties={
                "content_group_params": openapi.Schema(type=openapi.TYPE_OBJECT),
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def convert_export_settings_v2_to_v3(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_group_params = request.data.get("content_group_params", {})
        try:
            export_settings_v3 = convert_export_settings_v2_to_v3(content_group_params)
            tofu_data_list_dict = TofuDataListHandler.convert_tofu_data_to_json(
                export_settings_v3
            )
            return Response(tofu_data_list_dict, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(
                f"Error converting export settings from v2 to v3: {str(e)}"
            )
            return Response(status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Reset results for subsequent actions",
        operation_description="Resets results for all subsequent actions",
        request_body=no_body,  # Specify that no request body is expected
    )
    @action(detail=True, methods=["post"])
    def reset_subsequent_actions_results(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        try:
            action_handler = ActionHandler(action)
            action_handler.reset_subsequent_actions_results()
            return Response(status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error resetting subsequent actions results: {str(e)}")
            return Response(
                {"error": f"Failed to reset subsequent actions results: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Pull results from incoming actions",
        operation_description="Pulls and updates the current action's results from its incoming connected actions",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "overwrite": openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    default=True,
                    description="Whether to overwrite existing results",
                )
            },
        ),
        responses={
            status.HTTP_200_OK: "Results successfully pulled from incoming actions",
            status.HTTP_400_BAD_REQUEST: "Failed to pull results from incoming actions",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Action not found",
        },
    )
    @action(detail=True, methods=["post"])
    def pull_results_from_incoming_actions(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            action_handler = ActionHandler(action)
            overwrite_val = request.data.get("overwrite", True)
            if isinstance(overwrite_val, str):
                overwrite = overwrite_val.lower() in ("true", "1", "t", "y", "yes")
            else:
                overwrite = bool(overwrite_val)
            action_handler.pull_results_from_incoming_actions(overwrite=overwrite)
            return Response(status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error pulling results from incoming actions: {str(e)}")
            return Response(
                {"error": f"Failed to pull results from incoming actions: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Push results to outgoing actions",
        operation_description="Pushes the current action's results to its outgoing connected actions",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "overwrite": openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    default=True,
                    description="Whether to overwrite existing results. Accepts boolean or string values like 'true', '1', etc.",
                )
            },
        ),
        responses={
            status.HTTP_200_OK: "Results successfully pushed to outgoing actions",
            status.HTTP_400_BAD_REQUEST: "Failed to push results to outgoing actions",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Action not found",
        },
    )
    @action(detail=True, methods=["post"])
    def push_results_to_outgoing_actions(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        try:
            action_handler = ActionHandler(action)
            overwrite_val = request.data.get("overwrite", True)
            # Handle various truthy values
            if isinstance(overwrite_val, str):
                overwrite = overwrite_val.lower() in ("true", "1", "t", "y", "yes")
            else:
                overwrite = bool(overwrite_val)

            action_handler.push_results_to_outgoing_actions(overwrite=overwrite)
            return Response(status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error pushing results to outgoing actions: {str(e)}")
            return Response(
                {"error": f"Failed to push results to outgoing actions: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Apply a content template to an action",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["content_template_id"],
            properties={
                "content_template_id": openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="ID of the content template to apply",
                )
            },
        ),
        responses={
            status.HTTP_200_OK: "Template successfully applied to action",
            status.HTTP_400_BAD_REQUEST: "Invalid request",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Action or template not found",
        },
    )
    @action(detail=True, methods=["post"])
    def apply_content_template(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_template_id = request.data.get("content_template_id")
        if not content_template_id:
            return Response(
                {"error": "content_template_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Get the template
            # TODO: if we'd check playbook visibility
            template = ContentTemplate.objects.filter(
                id=content_template_id,
            ).first()

            if not template:
                return Response(
                    {"error": "Template not found or not accessible"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Apply the template to the action's inputs
            action_handler = ActionHandler(action)
            action_handler.apply_content_template(template)

            return Response(
                {"message": "Template applied successfully"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            logging.exception(f"Error applying template to action: {str(e)}")
            return Response(
                {"error": f"Failed to apply template: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Copy an action for evaluation",
        operation_description="Duplicates an action and its campaign, then returns the new campaign",
        responses={
            status.HTTP_200_OK: "Campaign successfully duplicated",
            status.HTTP_400_BAD_REQUEST: "Failed to duplicate campaign",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Action or campaign not found",
        },
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
    )
    @action(detail=True, methods=["post"])
    def copy_action_for_eval(self, request, *args, **kwargs):
        action = self.get_object()
        if not action:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        try:
            new_campaign = ActionDuplicator.duplicate_action(action)
            return Response(
                data=CampaignSerializer(new_campaign).data, status=status.HTTP_200_OK
            )
        except Exception as e:
            logging.error(f"error in copy_for_eval: {e}\n{traceback.format_exc()}")
            return Response(
                data={"error": f"Error in copy_for_eval: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
