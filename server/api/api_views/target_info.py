"""
TargetInfo related views for the Tofu application.
"""

import hashlib
import json
import logging
import uuid

from django.core.paginator import Paginator
from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import mixins, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import MethodNotAllowed
from rest_framework.response import Response

from ..async_tasks import async_enrich_target_data
from ..models import (
    Playbook,
    PlaybookUser,
    TargetInfo,
    TargetInfoGroup,
    TofuUser,
)
from ..playbook_build.object_builder import ObjectBuilder
from ..playbook_build.target_info_group_enricher import (
    TargetInfoGroupEnricher,
    async_process_data_enrichment,
)
from ..playbook_build.target_info_group_wrapper import (
    TargetInfoGroupWrapper,
)
from ..serializers import (
    TargetInfoGroupSerializer,
    TargetInfoSerializer,
)
from ..sync.crm_list_sync.crm_list_sync_tasks import (
    sync_list_to_target_info_group_task,
)
from ..tasks import async_export_tofu_insights
from .pagination.pagination_mixin import PaginatedListMixin
from .pagination.pagination_models import (
    PaginatedResponseSerializer,
    PaginationQuerySerializer,
)


class TargetInfoGroupViewSet(
    PaginatedListMixin,
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    queryset = TargetInfoGroup.objects.all()
    serializer_class = TargetInfoGroupSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "target_info_group_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.lookup_url_kwarg in self.kwargs:
            context["target_info_group_id"] = self.kwargs[self.lookup_url_kwarg]

        if "legacy" in self.request.query_params:
            legacy_literal = self.request.query_params.get("legacy")
            context["legacy"] = legacy_literal.lower() in ["true", "1", "yes"]
        return context

    def is_request_from_creator_or_user_or_superuser(self, request):
        target_info_group = self.get_object()
        if not target_info_group:
            return False
        playbook = target_info_group.playbook
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def is_request_from_creator_or_user_or_superuser_without_object(
        self, request, playbook
    ):
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def is_tofu_lite_user(self, request):
        if request.user.customer_type == TofuUser.CustomerType.LITE:
            return True
        return False

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="legacy",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
            )
        ],
    )
    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Retrieve target info group by key",
        operation_description="",
        manual_parameters=[
            openapi.Parameter(
                name="playbook_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
            openapi.Parameter(
                name="l1_key",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
            ),
            openapi.Parameter(
                name="legacy",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
            ),
        ],
    )
    @action(detail=False, methods=["get"])
    def retrieve_by_keys(self, request, *args, **kwargs):
        playbook_id = request.query_params.get("playbook_id")

        playbook = Playbook.objects.filter(id=playbook_id).first()
        if not playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser_without_object(
            request, playbook
        ):
            return Response(status=status.HTTP_403_FORBIDDEN)
        l1_key = request.query_params.get("l1_key")
        if not playbook_id or not l1_key:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        target_group_info = TargetInfoGroup.objects.filter(
            playbook_id=playbook_id, target_info_group_key=l1_key
        )
        if not target_group_info:
            return Response(status=status.HTTP_404_NOT_FOUND)
        target_group_info = target_group_info.first()
        serializer = self.serializer_class
        return Response(serializer(target_group_info).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Bulk create target_info",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "target_infos_data": {
                    "type": openapi.TYPE_ARRAY,
                    "items": openapi.Schema(type=openapi.TYPE_OBJECT),
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def bulk_create(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        target_info_group = self.get_object()
        target_infos_data = request.data.get("target_infos_data", [])

        target_info_group_wrapper = TargetInfoGroupWrapper(target_info_group)
        target_info_group_wrapper.bulk_create(target_infos_data)
        return Response(status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Bulk update target_info_group",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "target_infos_data": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {},
                },
            },
        ),
    )
    @action(detail=True, methods=["patch"])
    def bulk_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        target_info_group = self.get_object()
        target_infos_data = request.data.get("target_infos_data", {})

        target_info_group_wrapper = TargetInfoGroupWrapper(target_info_group)
        target_info_group_wrapper.bulk_update(target_infos_data)
        return Response(status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="bulk delete target_info",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "target_info_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": openapi.Schema(type=openapi.TYPE_INTEGER),
                },
            },
        ),
    )
    @action(detail=True, methods=["delete"])
    def bulk_delete(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        target_info_group = self.get_object()
        target_info_ids = request.data.get("target_info_ids", [])

        target_info_group_wrapper = TargetInfoGroupWrapper(target_info_group)
        target_info_group_wrapper.bulk_delete(target_info_ids)
        return Response(status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Rebuild target info group",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
    )
    @action(detail=True, methods=["post"])
    def rebuild(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return Response(status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="set the field for domain extraction",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "domain_field": {
                    "type": openapi.TYPE_STRING,
                    "example": "field_name",
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def set_domain_field(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        domain_field = request.data.get("domain_field", "")
        if not domain_field:
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"error": "domain_field is required"},
            )

        target_info_group = self.get_object()
        try:
            TargetInfoGroupWrapper(target_info_group).set_domain_field(domain_field)
            serializer = self.serializer_class
            return Response(
                status=status.HTTP_200_OK, data=serializer(target_info_group).data
            )
        except Exception as e:
            logging.error(f"Error setting domain field: {e}")
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"error": f"Failed to set domain field: {e}"},
            )

    @swagger_auto_schema(
        operation_summary="Enrich data for targets in a target list",
        operation_description="Enrich data for targets in a target list asynchronously",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "enrich_field": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Field to enrich"
                ),
            },
            required=["enrich_field"],
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Task submitted successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={"task_id": openapi.Schema(type=openapi.TYPE_STRING)},
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_404_NOT_FOUND: "Not found",
        },
    )
    @action(detail=True, methods=["post"])
    def data_enrichment(self, request, *args, **kwargs):
        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this data enrichment"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        target_info_group = self.get_object()
        if not target_info_group:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        enrich_field = request.data.get("enrich_field")

        if not enrich_field:
            return Response(
                {"error": "enrich_field parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            task_id = f"enrich_target_data:target_info_group:{target_info_group.id}:{enrich_field}:{uuid.uuid4()}"
            # Submit new task
            task = async_enrich_target_data.apply_async(
                args=(target_info_group.id, enrich_field, task_id), task_id=task_id
            )
            return Response({"task_id": task.id}, status=status.HTTP_200_OK)

        except Exception as e:
            logging.exception(
                f"Error submitting data enrichment task for target list {target_info_group.id}: {e}"
            )
            return Response(
                {
                    "error": "An unexpected error occurred while submitting the enrichment task"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="Enrich data for targets in a target list",
        operation_description="Enrich data for targets in a target list asynchronously",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "enrich_fields": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="Fields to enrich",
                ),
            },
            required=["enrich_fields"],
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Task submitted successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={"task_id": openapi.Schema(type=openapi.TYPE_STRING)},
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_404_NOT_FOUND: "Not found",
        },
    )
    @action(detail=True, methods=["post"])
    def process_data_enrichment(self, request, *args, **kwargs):
        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this data enrichment"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        target_info_group = self.get_object()
        if not target_info_group:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        enrich_fields = request.data.get("enrich_fields")

        if (
            not enrich_fields
            or not isinstance(enrich_fields, list)
            or not all(
                isinstance(field, str) and field.strip() for field in enrich_fields
            )
        ):
            return Response(
                {
                    "error": "enrich_fields parameter is required and must be a list of strings"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Create a deterministic hash of the enrich_fields
        enrich_fields_hash = hashlib.sha1(
            json.dumps(sorted(set(enrich_fields))).encode()
        ).hexdigest()

        try:
            task_id = f"enrich_target_data:target_info_group:{target_info_group.id}:{enrich_fields_hash}:{uuid.uuid4()}"
            # Submit new task
            task = async_process_data_enrichment.apply_async(
                args=(target_info_group.id, enrich_fields, task_id), task_id=task_id
            )
            return Response({"task_id": task.id}, status=status.HTTP_200_OK)

        except Exception as e:
            logging.exception(
                f"Error submitting data enrichment task for target list {target_info_group.id}: {e}"
            )
            return Response(
                {
                    "error": "An unexpected error occurred while submitting the enrichment task"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="Get data enrichment preview",
        operation_description="Get data enrichment preview for a target list",
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Preview result",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT),
            ),
        },
    )
    @action(detail=True, methods=["get"])
    def get_data_enrichment_preview(self, request, *args, **kwargs):
        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this data enrichment"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        target_info_group = self.get_object()
        if not target_info_group:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        target_info_group_enricher = TargetInfoGroupEnricher(target_info_group)

        try:
            result = target_info_group_enricher.get_data_enrichment_preview()
            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error getting data enrichment preview: {e}")
            return Response(
                {
                    "error": "An unexpected error occurred while getting the data enrichment preview"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="Perform TOFU research on target list",
        operation_description="Perform TOFU research on target list",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "query_id": {"type": openapi.TYPE_STRING, "example": "1234_4567_890"},
                "query": {"type": openapi.TYPE_STRING, "example": "Research query"},
                "targets": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_INTEGER},
                    "example": [1, 2, 3],
                },
                "preview": {"type": openapi.TYPE_BOOLEAN, "example": True},
            },
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Preview result or task ID",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "result": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            description="Result object returned for preview mode",
                        ),
                        "task_id": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description="Task ID returned for non-preview mode",
                        ),
                    },
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def tofu_research(self, request, *args, **kwargs):
        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this research"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        target_info_group = self.get_object()
        if not target_info_group:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        target_info_group = self.get_object()
        query_id = request.data.get("query_id")
        query = request.data.get("query")
        targets = request.data.get("targets", [])
        preview = request.data.get("preview", True)

        if not query_id and not preview:
            return Response(
                {"error": "Query ID is required for non-preview research"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not query:
            return Response(
                {"error": "Query is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            target_info_group_wrapper = TargetInfoGroupWrapper(target_info_group)

            if preview:
                if not targets or len(targets) > 2:
                    logging.error(
                        f"No targets or too many targets for preview and would pick first 2. The targets passed are {targets}."
                    )
                    targets = targets[:2] if targets else None
            else:
                if targets:
                    logging.error(f"Targets are not allowed for non-preview research.")
                    targets = None

            result = target_info_group_wrapper.tofu_research(
                query_id=query_id,
                query=query,
                target_ids=targets,
                preview=preview,
            )
            return Response(
                {"result": result} if preview else {"task_id": result},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging.exception(f"Error in TOFU research: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="Delete a specific TOFU research from target info group",
        operation_description="Delete a specific TOFU research from target info group by query_id",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "query_id": {
                    "type": openapi.TYPE_STRING,
                    "example": "1234_5678_90ab",
                },
            },
            required=["query_id"],
        ),
        responses={
            status.HTTP_204_NO_CONTENT: "No Content",
            status.HTTP_400_BAD_REQUEST: "Bad Request",
            status.HTTP_404_NOT_FOUND: "Not Found",
        },
    )
    @action(detail=True, methods=["delete"])
    def delete_tofu_research(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        target_info_group = self.get_object()
        query_id = request.data.get("query_id")

        if not query_id:
            return Response(
                {"error": "query_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not isinstance(query_id, str):
            return Response(
                {"error": f"query_id must be a string: {query_id}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            target_info_group_wrapper = TargetInfoGroupWrapper(target_info_group)
            deleted = target_info_group_wrapper.delete_tofu_research(query_id)

            if deleted:
                return Response(status=status.HTTP_204_NO_CONTENT)
            else:
                return Response(
                    {"error": f"No research found with query_id: {query_id}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            logging.error(f"Error deleting TOFU research: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred while deleting the research."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="Export Tofu Insights to CRM platforms",
        operation_description="Export Tofu insights (summaries and value propositions) to CRM platforms like HubSpot or Salesforce for each target in the target group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "insights_to_crm_field_mapping": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "summary_html": openapi.Schema(type=openapi.TYPE_STRING),
                        "value_prop_html": openapi.Schema(type=openapi.TYPE_STRING),
                        "tofu_research_html": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                ),
                "platform": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="hubspot",
                    description="CRM platform to export insights to",
                ),
            },
            required=["insights_to_crm_field_mapping", "platform"],
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Task initiated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "task_id": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_404_NOT_FOUND: "Not found",
        },
    )
    @action(detail=True, methods=["post"])
    def export_insights(self, request, *args, **kwargs):
        target_info_group = self.get_object()

        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(
                {
                    "error": "You don't have permission to export insights for this target group"
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Get request data
        insights_to_crm_field_mapping = request.data.get(
            "insights_to_crm_field_mapping"
        )
        platform = request.data.get("platform")

        if not insights_to_crm_field_mapping or not platform:
            return Response(
                {"error": "insights_to_crm_field_mapping and platform are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate platform
        supported_platforms = ["hubspot", "salesforce"]
        if platform not in supported_platforms:
            return Response(
                {
                    "error": f"This target group was not synced from a supported platform. Supported platforms: {', '.join(supported_platforms)}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Start an asynchronous task to export insights
            task = async_export_tofu_insights.delay(
                target_info_group_id=target_info_group.id,
                platform=platform,
                insights_to_crm_field_mapping=insights_to_crm_field_mapping,
            )

            return Response({"task_id": task.id}, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error initiating export insights to {platform}: {e}")
            return Response(
                {"error": f"Failed to initiate export process to {platform}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"])
    def sync_list(self, request, *args, **kwargs):
        target_info_group = self.get_object()
        if not target_info_group:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        target_info_group_id = target_info_group.id
        task_id = f"sync_list_{target_info_group_id}:{uuid.uuid4()}"
        sync_list_to_target_info_group_task.apply_async(
            kwargs={"target_info_group_id": target_info_group_id},
            task_id=task_id,
            priority=9,
        )

        return Response(
            {
                "task_id": task_id,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Convert titles to a contact list",
        operation_description="Convert a list of titles to a contact list",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "titles": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of titles to convert to contacts",
                ),
            },
            required=["titles"],
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Conversion successful",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "csv_url": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description="URL to the generated CSV file",
                        ),
                    },
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_404_NOT_FOUND: "Not found",
        },
    )
    @action(detail=True, methods=["post"])
    def convert_to_contact_list(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        target_info_group = self.get_object()
        titles = request.data.get("titles", [])

        if (
            not titles
            or not isinstance(titles, list)
            or not all(isinstance(title, str) for title in titles)
        ):
            return Response(
                {"error": f"A non-empty string list of titles is required: {titles}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            target_info_group_wrapper = TargetInfoGroupWrapper(target_info_group)
            csv_url = target_info_group_wrapper.convert_titles_to_contact_list(titles)
            return Response({"csv_url": csv_url}, status=status.HTTP_200_OK)
        except Exception as e:
            logging.exception(f"Error converting titles to contact list: {e}")
            return Response(
                {"error": "An unexpected error occurred during conversion"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        operation_summary="List all targets from a target_info_group with pagination",
        operation_description="List all targets from a target_info_group with pagination support",
        query_serializer=PaginationQuerySerializer,
        responses={
            status.HTTP_200_OK: PaginatedResponseSerializer,
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_403_FORBIDDEN: "Forbidden",
            status.HTTP_404_NOT_FOUND: "Not found",
        },
    )
    @action(detail=True, methods=["get"])
    def targets(self, request, *args, **kwargs):
        target_info_group = self.get_object()
        if not target_info_group:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        serializer = PaginationQuerySerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        params = serializer.validated_data
        sort_by = params["sort_by"]
        order = params["order"]
        page = params["page"]
        page_size = params["page_size"]

        targets = TargetInfo.objects.filter(target_info_group=target_info_group)

        # temp: if caller don't provide page and page_size, return all targets
        if (
            request.query_params.get("page") is None
            or request.query_params.get("page_size") is None
        ):
            serializer = TargetInfoSerializer(targets, many=True)
            return self.get_unpaginated_response(serializer, targets)

        allowed_sort_fields = ["id", "target_key", "created_at"]
        targets = self.apply_ordering(targets, sort_by, order, allowed_sort_fields)

        page_obj = self.paginate_queryset(targets, page, page_size)

        serializer = TargetInfoSerializer(page_obj, many=True)
        return self.get_paginated_response(serializer, page_obj)


class TargetInfoViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    queryset = TargetInfo.objects.all()
    serializer_class = TargetInfoSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "target_info_id"

    def get_queryset(self):
        if self.action == "list":
            # Raise an exception to indicate that the list method is not allowed
            raise MethodNotAllowed("GET", detail="List method is not allowed.")
        return super().get_queryset()

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def is_request_from_creator_or_user_or_superuser(self, request):
        target_info = self.get_object()
        playbook = target_info.target_info_group.playbook
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def is_request_from_creator_or_user_or_superuser_without_object(
        self, request, playbook
    ):
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Retrieve target info by key",
        operation_description="",
        manual_parameters=[
            openapi.Parameter(
                name="playbook_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
            openapi.Parameter(
                name="l1_key",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
            ),
            openapi.Parameter(
                name="l2_key",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
            ),
        ],
    )
    @action(detail=False, methods=["get"])
    def retrieve_by_keys(self, request, *args, **kwargs):
        playbook_id = request.query_params.get("playbook_id")

        playbook = Playbook.objects.filter(id=playbook_id).first()
        if not playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser_without_object(
            request, playbook
        ):
            return Response(status=status.HTTP_403_FORBIDDEN)
        l1_key = request.query_params.get("l1_key")
        l2_key = request.query_params.get("l2_key")
        if not playbook_id or not l1_key or not l2_key:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        target_info = TargetInfo.objects.filter(
            target_info_group__playbook_id=playbook_id,
            target_info_group__target_info_group_key=l1_key,
            target_key=l2_key,
        )
        if not target_info:
            return Response(status=status.HTTP_404_NOT_FOUND)
        target_info = target_info.first()
        serializer = self.serializer_class
        return Response(serializer(target_info).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Rebuild target info",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
    )
    @action(detail=True, methods=["post"])
    def rebuild(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        target_info = self.get_object()
        target_builder = ObjectBuilder.get_builder(target_info)
        target_builder.build_docs(rebuild=True)
        target_info.refresh_from_db()
        return Response(
            data=self.serializer_class(target_info).data, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Extract target info from external websites",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "keyword": {
                    "type": openapi.TYPE_STRING,
                    "example": "company introductions and products",
                },
                "num_links": {
                    "type": openapi.TYPE_INTEGER,
                    "example": 5,
                },
                "include_sitelinks": {
                    "type": openapi.TYPE_BOOLEAN,
                    "example": False,
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def extract_external(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        target_info = self.get_object()

        keyword = request.data.get("keyword", "")
        num_links = request.data.get("num_links", 5)
        include_sitelinks = request.data.get("include_sitelinks", False)

        target_builder = ObjectBuilder.get_builder(target_info)
        links = target_builder.build_external_info(
            keyword=keyword, num_links=num_links, include_sitelinks=include_sitelinks
        )

        return Response(
            data={"links": links},
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Perform TOFU research on target",
        operation_description="Perform TOFU research on target",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "query_id": {"type": openapi.TYPE_STRING, "example": "1234_5678"},
                "query": {"type": openapi.TYPE_STRING, "example": "Research query"},
                "disable_cache": {"type": openapi.TYPE_BOOLEAN, "example": False},
                "preview": {"type": openapi.TYPE_BOOLEAN, "example": True},
            },
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Result",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "result": openapi.Schema(type=openapi.TYPE_OBJECT),
                    },
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def tofu_research(self, request, *args, **kwargs):
        target_info = self.get_object()
        if not target_info:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        query_id = request.data.get("query_id")
        query = request.data.get("query")
        disable_cache = request.data.get("disable_cache", False)
        preview = request.data.get("preview", True)

        if not query:
            return Response(
                {"error": "Query is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        if not preview:
            if not query_id:
                logging.error("Query ID is required for non-preview tofu research")
                return Response(
                    {"error": "Query ID is required for non-preview tofu research"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            if not query_id:
                query_id = str(uuid.uuid4())  # generate a random query id

        try:
            target_builder = ObjectBuilder.get_builder(target_info)
            result = target_builder.tofu_research(
                query_id=query_id,
                query=query,
                preview=preview,
                disable_cache=disable_cache,
            )
            return Response({"result": result}, status=status.HTTP_200_OK)

        except Exception as e:
            logging.exception("Error in TOFU research")
            return Response(
                {"error": "An unexpected error occurred. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
