"""
Content-related views for the Tofu API.
This module contains endpoints for managing content instances and their generation.
"""

import logging
import traceback

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.request import Request
from rest_framework.response import Response

from ..content import ContentGenerator
from ..content_group import ContentGroupHandler
from ..evaluator.evaluate_data_duplicate import copy_content_for_eval
from ..inbound_handler import get_matching_criteria
from ..logger import log_component_rating, log_content_rating
from ..models import (
    Campaign,
    Content,
    ContentVariation,
    InboundTypeChoices,
    OffsiteEventLogs,
    PublicContent,
    TofuUser,
)
from ..playbook import PlaybookHandler
from ..serializers import ContentSerializer, ContentVariationSerializer
from ..thread_locals import try_set_current_content
from ..throttling import tofu_lite_ratelimit
from ..utils import measure_latency, publish_image


class ContentViewSet(viewsets.ModelViewSet):
    queryset = Content.objects.all()
    serializer_class = ContentSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "content_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        try_set_current_content(obj)
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.lookup_url_kwarg in self.kwargs:
            context["content_id"] = self.kwargs[self.lookup_url_kwarg]
        return context

    def is_request_from_creator_or_superuser(self, request):
        content = self.get_object()
        if content and (content.creator == request.user or request.user.is_superuser):
            return True
        return False

    def is_internal_features_enabled(self, request: Request):
        if request.user.is_superuser:
            return True
        if request.user.context and request.user.context.get("internalFeatures", False):
            return True
        return False

    def is_tofu_lite_user(self, request: Request):
        if request.user.customer_type == TofuUser.CustomerType.LITE:
            return True
        return False

    def is_enable_target_object(self, request: Request):
        if request.user.context and request.user.context.get(
            "enableTargetObject", False
        ):
            return True
        return False

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        self.queryset = queryset.filter(
            creator=request.user, content_group=None
        ).order_by("-updated_at")
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Use this to create a content instance",
        operation_description="Start a content generation instance to store params, etc",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "playbook": {"type": openapi.TYPE_INTEGER, "example": 5},
                "content_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "example_content",
                },
                "content_params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "content_type": "Landing Page",
                        "content_source": "url",
                        "targets": {
                            "Customer Segments": "Labs",
                            "Persona": "CFO",
                        },
                    },
                },
                "components": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "12345678-90ab-cdef-ghij-klmnopqrstuv": {
                            "meta": {
                                "html_tag": "<h1>",
                                "preceding_element": "<div>This is a preceding element.</div>",
                                "succeeding_element": "<p>This is a succeeding element.</p>",
                                "component_params": {
                                    "custom_instructions": [
                                        "Don't use percentage",
                                        "Use pirate language style",
                                    ],
                                    "assets": {"Whitepapers": "whitepaper 1"},
                                },
                            },
                            "text": "this is the original header text",
                        },
                        "87654321-90ab-cdef-ghij-klmnopqrstuv": {
                            "meta": {
                                "html_tag": "<h2>",
                                "preceding_element": "<div>This is a preceding element for body.</div>",
                                "succeeding_element": "<p>This is a succeeding element for body.</p>",
                                "component_params": {
                                    "custom_instructions": [
                                        "Don't use percentage",
                                        "Use pirate language style",
                                    ],
                                    "assets": {"Whitepapers": "whitepaper 2"},
                                },
                            },
                            "text": "this is the original body text",
                        },
                    },
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    def create(self, request, *args, **kwargs):
        if not request.user:
            return Response(status=status.HTTP_403_FORBIDDEN)
        if "campaign" in request.data:
            campaign_id = request.data["campaign"]
            try:
                campaign_instance = Campaign.objects.get(id=campaign_id)
                if not campaign_instance:
                    raise ValueError(
                        f"Failed to fetch campaign for content creation: {campaign_id}"
                    )
                if campaign_instance.campaign_params.get("campaign_goal") == "Workflow":
                    logging.error(
                        f"content creation is not supposed to be called for campaign v3: {campaign_instance.id}"
                    )
            except Exception as e:
                logging.exception(
                    f"Failed to fetch campaign for content creation: {campaign_id}: {str(e)}"
                )
                return Response(
                    data={"error": f"Campaign not found: {campaign_id}"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        request.data["creator"] = request.user.id
        return super().create(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def check_update_target(self, request):
        if (
            "content_params" in request.data
            and "targets" in request.data["content_params"]
        ):
            passed_targets = request.data["content_params"]["targets"]
            existing_targets = self.get_object().content_params.get("targets", {})

            if existing_targets and existing_targets != passed_targets:
                logging.error(
                    f"content {self.get_object().id} already has targets {existing_targets}, passed targets: {passed_targets}"
                )
                del request.data["content_params"]["targets"]
            else:
                new_content_name = ContentGroupHandler(
                    self.get_object().content_group
                ).get_content_name(request.data["content_params"]["targets"])
                request.data["content_name"] = new_content_name

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if "creator" in request.data:
            content_creator = self.get_object().creator
            if content_creator != request.user:
                logging.error("creator cannot be updated")
            del request.data["creator"]

        self.check_update_target(request)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if "creator" in request.data:
            content_creator = self.get_object().creator
            if content_creator != request.user:
                logging.error("creator cannot be updated")
            del request.data["creator"]
        self.check_update_target(request)
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Use this to generate content variations. Call once for each variation generation.",
        operation_description="API for generating content variations.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "components": ["component_id_1", "component_id_2"],
                        "joint_generation": False,
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    @measure_latency
    def gen(self, request: Request, *args, **kwargs):
        """
        Return variations of components, for example:
        variations = {
            'component_id_1': {
                'text': 'this is the original header text',
                'meta': {
                    variations: [
                        {'text': 'this is variation 1'},
                        {'text': 'this is variation 2'},
                    ],
                    current_variation_index: 0,
                }
            },
            'component_id_2': {
                'text': 'this is the original body text',
                'meta': {
                    variations: [
                        {'text': 'this is variation 1'},
                        {'text': 'this is variation 2'},
                    ],
                    current_variation_index: 0,
                }
            },
        }
        """
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this content generation"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        content = self.get_object()
        playbook = content.playbook
        playbook_handler = PlaybookHandler.load_from_db_instance(playbook)
        content_generator = ContentGenerator(playbook_handler, content)

        # an array of component ids
        component_ids = request.data["params"].get("components", [])
        joint_generation = request.data["params"].get("joint_generation", False)
        template_generation = request.data["params"].get("template_generation", False)
        template_personalization = request.data["params"].get(
            "template_personalization", False
        )

        template_instructions = request.data["params"].get("template_instructions", {})
        if not isinstance(template_instructions, dict):
            return Response(
                data={"error": "template_instructions must be a dictionary"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        template_length = template_instructions.get("length", None)
        template_purpose = template_instructions.get("purpose", None)
        components = content.content_group.components
        # check if all the keys in component_ids is contained by components
        if (
            not template_generation
            and not template_personalization
            and not set(component_ids).issubset(set(components.keys()))
        ):
            return Response(
                data={
                    "error": "Only the selected components can be used for generation."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # if component_ids is not empty, only generate variations for the passed in components
        if len(component_ids) > 0:
            components = {k: components[k] for k in component_ids}

        # TODO: all the settings are default for now
        content_generator.set_settings(
            joint_generation=joint_generation,
            template_generation=template_generation,
            components=components,
            is_template_personalization=template_personalization,
            template_length=template_length,
            template_purpose=template_purpose,
            save_variations=True,
        )

        try:
            ret_variation = content_generator.gen()
        except Exception as e:
            logging.error(f"Error in generation: {e}\n{traceback.format_exc()}")
            return Response(
                data={"error": f"Error in generation: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            ContentVariationSerializer(ret_variation).data,
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Use this to generate content variations. Call once for each variation generation.",
        operation_description="API for generating content variations.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "components": {
                            "v7oqL": {
                                "meta": {
                                    "parent_component_id": "djwi23",
                                    "custom_instructions": [
                                        {"instruction": "Use pirate style"}
                                    ],
                                },
                                "text": "I hope you're eager to streamline your marketing efforts and generate hyper-personalized campaigns to connect with your audience effectively.",
                            }
                        },
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    @measure_latency
    def free_gen(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this content generation"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        content = self.get_object()
        playbook = content.playbook
        playbook_handler = PlaybookHandler.load_from_db_instance(playbook)
        content_generator = ContentGenerator(playbook_handler, content)

        # an array of component ids
        components = request.data["params"].get("components")
        if not components:
            return Response(
                data={"error": "component is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if len(components) != 1:
            return Response(
                data={"error": "only one component is allowed"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        num_of_variations = 1
        free_gen = True

        # TODO: all the settings are default for now
        content_generator.set_settings(
            components=components,
            num_of_variations=num_of_variations,
            free_gen=free_gen,
            save_variations=False,
        )

        try:
            variation = content_generator.gen()
            return Response(data=variation, status=status.HTTP_200_OK)
        except Exception as e:
            logging.error(f"Error in partial generation: {e}\n{traceback.format_exc()}")
            return Response(
                data={"error": f"Error in partial generation: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Use this to generate template variations.",
        operation_description="API for generating template variations.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "params": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {
                        "template_generation": True,
                        "template_personalization": False,
                        "template_instructions": {
                            "length": "150 words",
                            "purpose": "webinar followup",
                        },
                    },
                }
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def template_gen(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        if self.is_tofu_lite_user(request):
            return Response(
                data={"error": "Tofu Lite is not enabled for this content generation"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        content = self.get_object()
        playbook = content.playbook
        playbook_handler = PlaybookHandler.load_from_db_instance(playbook)
        content_generator = ContentGenerator(playbook_handler, content)
        template_generation = request.data["params"].get("template_generation", False)
        template_personalization = request.data["params"].get(
            "template_personalization", False
        )
        if not template_generation and not template_personalization:
            return Response(
                data={
                    "error": "template_generation or template_personalization is required"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        if template_generation and template_personalization:
            return Response(
                data={
                    "error": "template_generation and template_personalization cannot be True at the same time"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        template_instructions = request.data["params"].get("template_instructions", {})
        if template_instructions and template_personalization:
            return Response(
                data={
                    "error": "template_instructions is not allowed when template_personalization is True"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not isinstance(template_instructions, dict):
            return Response(
                data={"error": "template_instructions must be a dictionary"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        template_length = template_instructions.get("length", None)
        template_purpose = template_instructions.get("purpose", None)

        content_generator.set_settings(
            template_generation=template_generation,
            is_template_personalization=template_personalization,
            template_length=template_length,
            template_purpose=template_purpose,
            save_variations=False,
            num_of_variations=1,
        )

        try:
            variation = content_generator.gen()
            return Response(data=variation, status=status.HTTP_200_OK)
        except Exception as e:
            logging.error(f"Error in template generation: {e}")
            return Response(
                data={"error": f"Error in template generation: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Make the result of this content public",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "tofu_content_id": {"type": openapi.TYPE_STRING, "example": "uuid"},
                "tofu_slug": {"type": openapi.TYPE_STRING, "example": "url slug"},
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    @tofu_lite_ratelimit(rate="20/m")
    def make_result_public(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        tofu_content_id = request.data.get("tofu_content_id", None)
        tofu_slug = request.data.get("tofu_slug", None)
        if not tofu_content_id or not tofu_slug:
            return Response(
                data={"error": "Missing parameters"}, status=status.HTTP_400_BAD_REQUEST
            )

        content = self.get_object()
        content_variation = ContentVariation.objects.filter(content=content)
        if not content_variation:
            return Response(
                data={"error": "Content variation not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        content_variation = content_variation.first()

        try:

            is_backend_publish_image = False
            if is_backend_publish_image:
                # scan the variations and publish images
                for component_id, variation in content_variation.variations.items():
                    meta_data = variation.get("meta", {})
                    var_type = meta_data.get("type", "text")

                    if var_type == "image":
                        variation_cands = meta_data.get("variations", [])
                        for variation_cand in variation_cands:
                            image_url = variation_cand.get("image", {}).get("url", None)
                            if not image_url:
                                logging.error(
                                    f"image url is not found for {component_id} in {variation}"
                                )
                                continue
                            _published_image_url = publish_image(image_url)
                        # current version
                        current_version = meta_data.get("current_version").get(
                            "image", {}
                        )
                        image_url = current_version.get("url", None)
                        if not image_url:
                            logging.error(
                                f"image url is not found for {component_id} in {variation}"
                            )
                            continue
                        _published_image_url = publish_image(image_url)

            enable_partial_inbound = content.content_group.campaign.campaign_params.get(
                "inbound_landing_pages", {}
            ).get("enabled", False)
            inbound_type_choice = (
                InboundTypeChoices.PARTIAL_INBOUND
                if enable_partial_inbound
                else InboundTypeChoices.OUTBOUND
            )

            matching_criteria = []
            if inbound_type_choice == InboundTypeChoices.PARTIAL_INBOUND:
                try:
                    matching_criteria = get_matching_criteria(content)
                except Exception as e:
                    logging.error(
                        f"Failed to get matching criteria for content {content.id}: {e}\n{traceback.format_exc()}"
                    )

            public_content, created = PublicContent.objects.update_or_create(
                tofu_content_id=tofu_content_id,
                tofu_slug=tofu_slug,
                defaults={
                    "content_id": content.id,
                    "inbound_type": inbound_type_choice,
                    "matching_criteria": matching_criteria,
                    "creator": request.user,
                    "source_content_variation": content_variation,
                    "variations": content_variation.variations,
                },
            )
            if created:
                return Response(
                    data={
                        "message": f"PublicContent ({tofu_content_id} : {tofu_slug}) is created."
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    data={
                        "message": f"PublicContent ({tofu_content_id} : {tofu_slug}) is updated."
                    },
                    status=status.HTTP_200_OK,
                )
        except Exception as e:
            logging.error(f"error in make_result_public: {e}\n{traceback.format_exc()}")
            return Response(
                data={"error": f"Error in make_result_public: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def save_ratings(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        content = self.get_object()
        user = request.user
        log_content_rating(content, user, **request.data)
        return Response(status=status.HTTP_200_OK)

    @action(detail=True, methods=["post"])
    def save_component_ratings(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        content = self.get_object()
        user = request.user
        log_component_rating(content, user, **request.data)
        return Response(status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Verify whether there is a page view happened for the given tofu_content_id and slug",
        manual_parameters=[
            openapi.Parameter(
                name="tofu_slug",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="tofu_slug set during export",
                required=True,
            ),
        ],
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="OK",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "result": {
                            "type": openapi.TYPE_BOOLEAN,
                            "description": "true or false based on the whether there is a page view",
                        },
                    },
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["get"])
    def verify_page_view(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        tofu_slug = request.query_params.get("tofu_slug", "")
        content_id = self.get_object().id

        offsite_event_log = OffsiteEventLogs.objects.filter(
            content_id=content_id, tofu_slug=tofu_slug, event_type="page_view"
        ).first()
        if offsite_event_log:
            return Response(data={"result": True}, status=status.HTTP_200_OK)
        return Response(data={"result": False}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Copy content for evaluation",
        operation_description="Copy content for evaluation",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=True, methods=["post"])
    def copy_for_eval(self, request: Request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        content = self.get_object()
        try:
            new_content = copy_content_for_eval(content)
            return Response(
                data=ContentSerializer(new_content).data, status=status.HTTP_200_OK
            )
        except Exception as e:
            logging.error(f"error in copy_for_eval: {e}")
            return Response(
                data={"error": f"Error in copy_for_eval: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
