"""
API Views package - contains all API views for the Tofu application
"""

# Action views
from .action import ActionViewSet
from .action_edge import ActionEdgeViewSet

# Asset views
from .asset_info import AssetInfoGroupViewSet, AssetInfoViewSet

# Content views
from .campaign import CampaignViewSet

# Chatbot views
from .chatbot import ChatbotViewSet

# Company views
from .company_info import CompanyInfoViewSet
from .content import ContentViewSet
from .content_group import ContentGroupViewSet
from .content_variation import ContentVariationViewSet

# Evaluation views
from .eval import EvalViewSet

# Playbook views
from .playbook import PlaybookViewSet

# Public views
from .public import PublicViewSet

# Target views
from .target_info import TargetInfoGroupViewSet, TargetInfoViewSet

# User views
from .user import UserViewSet

__all__ = [
    # User views
    "UserViewSet",
    # Action views
    "ActionViewSet",
    "ActionEdgeViewSet",
    # Asset views
    "AssetInfoViewSet",
    "AssetInfoGroupViewSet",
    # Content views
    "CampaignViewSet",
    "ContentViewSet",
    "ContentGroupViewSet",
    "ContentVariationViewSet",
    # Company views
    "CompanyInfoViewSet",
    # Playbook views
    "PlaybookViewSet",
    # Evaluation views
    "EvalViewSet",
    # Public views
    "PublicViewSet",
    # Target views
    "TargetInfoViewSet",
    "TargetInfoGroupViewSet",
    # Chatbot views
    "ChatbotViewSet",
]
