"""
Public-facing API views for the Tofu application.
These views are accessible without authentication.
"""

import json
import logging
import os
import traceback
import uuid

import stripe
from django.views.decorators.csrf import csrf_exempt
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from ..inbound_handler import InboundHandler
from ..logger import (
    log_page_view,
)
from ..models import (
    PublicContent,
)
from ..serializers import (
    EmptySerializer,
)
from ..stripe import (
    handle_stripe_event,
    link_tofu_user_to_checkout_session,
)
from ..throttling import public_api_ratelimit


class PublicViewSet(viewsets.GenericViewSet):
    serializer_class = EmptySerializer
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Return tofu generated components for tofu.js",
        manual_parameters=[
            openapi.Parameter(
                name="tofu_content_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
            ),
            openapi.Parameter(
                name="tofu_slug",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name="tofu_variation",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name="cookie_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name="url",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name="debug_ip_address",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=False, methods=["get"])
    @public_api_ratelimit(rate="100/m")
    def components(self, request, *args, **kwargs):
        tofu_content_id = request.query_params.get("tofu_content_id", None)
        tofu_slug = request.query_params.get("tofu_slug", None)
        tofu_variation = request.query_params.get("tofu_variation", None)

        def extract_variation(variations, tofu_variation):
            ret = {}
            for key, value in variations.items():
                try:
                    if tofu_variation is not None:
                        var_value = value.get("meta", {}).get("variations", [])[
                            tofu_variation
                        ]
                    else:
                        var_value = value.get("meta", {}).get("current_version", None)
                    if not var_value:
                        raise Exception(
                            f"Cannot find tofu data for component {key} with id and slug: {tofu_content_id}, {tofu_slug} for variation {tofu_variation}"
                        )
                except Exception as e:
                    logging.error(f"Error in extract_variation: {e}")
                    raise Exception(f"Error in extract_variation: {e}")
                var_type = value.get("meta", {}).get("type", "text")
                if var_type == "text":
                    ret[key] = var_value
                    ret[key]["original_text"] = value.get("text", None)
                elif var_type == "image":
                    ret[key] = var_value
                    ret[key]["original_image"] = value.get("image", None)
                elif var_type == "link":
                    ret[key] = var_value
                    ret[key]["original_link"] = value.get("link", None)
                else:
                    raise Exception(f"Unknown type {var_type} for component {key}")
                ret[key]["meta"] = {
                    "html_tag_index": value.get("meta", {}).get("html_tag_index", None),
                }
            return ret

        if not tofu_content_id:
            return Response(
                data={"error": "tofu_content_id is missing"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        query_type = "outbound" if tofu_slug else "partial-inbound"

        try:
            if query_type == "outbound":
                if tofu_variation:
                    tofu_variation = int(tofu_variation) - 1

                public_content = PublicContent.objects.get(
                    tofu_content_id=tofu_content_id, tofu_slug=tofu_slug
                )
                variations = public_content.variations
                ret = extract_variation(variations, tofu_variation)
                if not ret:
                    return Response(
                        data={
                            "error": f"Cannot find tofu data for tofu_content_id: {tofu_content_id}"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                else:
                    return Response(ret, status=status.HTTP_200_OK)
            else:  # partial-inbound
                ip_address = request.headers.get(
                    "X-Forwarded-For", request.META.get("REMOTE_ADDR", None)
                )
                cookie_id = request.query_params.get("cookie_id", None)
                debug_ip_address = request.query_params.get("debug_ip_address", None)
                if ip_address:
                    ip_address = ip_address.split(",")[0].strip()
                if debug_ip_address:  # debug purpose
                    ip_address = debug_ip_address

                if not ip_address:
                    logging.error(
                        f"No valid IP address found for inbound tofu components."
                    )
                    return Response(
                        data={
                            "error": "No valid IP address found for inbound tofu components."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                variation_finder = InboundHandler(
                    passed_tofu_content_id=tofu_content_id,
                    ip_address=ip_address,
                    cookie_id=cookie_id,
                )
                if not variation_finder.is_eligible_for_partial_inbound():
                    return Response(
                        data={
                            "error": f"Inbound: tofu_content_id {tofu_content_id} is not eligible for inbound"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                inbound_result = variation_finder.get_inbound_components()
                if not inbound_result:
                    return Response(
                        data={
                            "error": f"Inbound: cannot find tofu data for tofu_content_id: {tofu_content_id}"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                (
                    tofu_content_id_returned,
                    tofu_slug_returned,
                    tofu_variation_returned,
                ) = inbound_result
                if tofu_content_id_returned != tofu_content_id:
                    logging.error(
                        f"debug: tofu_content_id_returned: {tofu_content_id_returned} is not equal to tofu_content_id: {tofu_content_id}"
                    )
                tofu_variation_returned = extract_variation(
                    tofu_variation_returned, tofu_variation
                )
                tofu_variation_returned["meta"] = {
                    "tofu_content_id": tofu_content_id_returned,
                    "tofu_slug": tofu_slug_returned,
                }
                return Response(tofu_variation_returned, status=status.HTTP_200_OK)

        except Exception as e:
            logging.error(
                f"Error when querying tofu data: {e}\n{traceback.format_exc()}"
            )
            return Response(
                data={"error": f"Error when querying tofu data: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(ret, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Log Page View Event",
        operation_description="API endpoint to log a user's page view event. It captures details about the content being viewed, the user's device information, and additional metadata.",
        manual_parameters=[
            openapi.Parameter(
                name="tofu_content_id",
                in_=openapi.IN_QUERY,
                description="Unique identifier for the content being viewed.",
                type=openapi.TYPE_STRING,
                required=True,
            ),
        ],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["tofu_slug", "ip_address", "user_agent"],
            properties={
                "session_id": {
                    "type": openapi.TYPE_STRING,
                    "description": "Unique identifier for the user's session.",
                    "example": "1234-5678-9012-3456",
                },
                "tofu_slug": {
                    "type": openapi.TYPE_STRING,
                    "description": "URL slug of the content.",
                    "example": "example-slug",
                },
                "tofu_variation": {
                    "type": openapi.TYPE_STRING,
                    "description": "Variation identifier of the content, if applicable.",
                    "example": "1",
                },
                "payload": {
                    "type": openapi.TYPE_OBJECT,
                    "description": "Additional JSON payload with event-specific data.",
                    "example": {"key": "value"},
                },
            },
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Event logged successfully."
            ),
            status.HTTP_400_BAD_REQUEST: openapi.Response(
                description="Invalid request parameters or format."
            ),
        },
    )
    @action(
        detail=False,
        methods=["post"],
        url_path="analytics/page_view",
        url_name="analytics_page_view",
    )
    def page_view(self, request, *args, **kwargs):
        tofu_content_id = request.query_params.get("tofu_content_id", None)
        if not tofu_content_id:
            return Response(
                data={"error": "tofu_content_id is missing"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        tofu_slug = request.data.get("tofu_slug", None)

        # fetch data
        session_id = request.data.get("session_id", None)
        if not session_id:
            session_id = str(uuid.uuid4())

        tofu_variation = request.data.get("tofu_variation", None)
        payload = request.data.get("payload", {}) or {}

        # just for debugging purposes
        cookies = request.COOKIES or {}
        payload["cookies"] = cookies

        ip_address = request.headers.get("X-Forwarded-For", None)
        if ip_address:
            ip_address = ip_address.split(",")[0].strip()
        else:
            ip_address = request.META.get("REMOTE_ADDR", None)
        user_agent = request.headers.get(
            "User-Agent", request.META.get("HTTP_USER_AGENT", None)
        )
        # end fetch data

        if not ip_address:
            logging.error(f"No valid IP address found for page view event.")

        log_page_view(
            session_id=session_id,
            tofu_content_id=tofu_content_id,
            tofu_slug=tofu_slug,
            content_variation_index=tofu_variation,
            ip_address=ip_address,
            user_agent=user_agent,
            payload=payload,
        )

        return Response({}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Handle Stripe webhook events",
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_500_INTERNAL_SERVER_ERROR: "Server error",
        },
    )
    @csrf_exempt
    @action(detail=False, methods=["post"], url_path="stripe/webhook")
    def stripe_webhook(self, request, *args, **kwargs):
        payload = request.body
        sig_header = request.META.get("HTTP_STRIPE_SIGNATURE")
        endpoint_secret = os.environ.get("STRIPE_WEBHOOK_SECRET", "")

        if not endpoint_secret:
            logging.error("Stripe webhook endpoint secret is not set.")
            return Response({"success": False}, status=status.HTTP_400_BAD_REQUEST)

        try:
            try:
                event = stripe.Webhook.construct_event(
                    payload, sig_header, endpoint_secret
                )
            except (ValueError, json.decoder.JSONDecodeError) as e:
                logging.error(f"⚠️  Webhook error while parsing request: {str(e)}")
                return Response({"success": False}, status=status.HTTP_400_BAD_REQUEST)
            except stripe.error.SignatureVerificationError as e:
                logging.error(f"⚠️  Webhook signature verification failed: {str(e)}")
                return Response({"success": False}, status=status.HTTP_400_BAD_REQUEST)

            # Handle the event
            if event:
                handle_stripe_event(event)

            return Response({"success": True}, status=status.HTTP_200_OK)

        except Exception as e:
            logging.error(f"⚠️  Webhook error: {str(e)}")
            return Response(
                {"success": False}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @swagger_auto_schema(
        operation_summary="Create a Stripe checkout session",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["priceId", "tofuUserId"],
            properties={
                "tofuUserId": {
                    "type": openapi.TYPE_INTEGER,
                    "description": "Tofu user ID",
                    "example": 1,
                },
                "priceId": {
                    "type": openapi.TYPE_STRING,
                    "description": "Stripe Price ID",
                    "example": "price_H5ggYwtDq4fbrJ",
                },
                "mode": {
                    "type": openapi.TYPE_STRING,
                    "description": "Stripe checkout mode",
                    "example": "subscription",
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Invalid request",
        },
    )
    @csrf_exempt
    @action(detail=False, methods=["post"], url_path="stripe/checkout")
    def stripe_checkout(self, request, *args, **kwargs):
        stripe.api_key = os.environ.get("STRIPE_API_KEY", "")
        if not stripe.api_key:
            message = "Stripe API key not configured"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        price = request.data.get("priceId")
        mode = request.data.get("mode", "subscription")
        # check that mode is valid
        if mode not in ["subscription", "payment"]:
            message = "Invalid mode"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        domain_url = os.environ.get("STRIPE_DOMAIN_URL")
        if not domain_url:
            message = "Stripe domain URL not configured"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        if not price:
            message = "Price ID is required"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        tofu_user_id = request.data.get("tofuUserId", None)
        if not tofu_user_id:
            message = "Tofu user ID is required"
            logging.error(message)
            return Response(
                {"error": {"message": message}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Create new Checkout Session for the order
            checkout_session = stripe.checkout.Session.create(
                success_url=f"{domain_url}/welcome?session_id={{CHECKOUT_SESSION_ID}}",
                cancel_url=f"{domain_url}/pricing",
                mode=mode,
                ui_mode="hosted",
                line_items=[{"price": price, "quantity": 1}],
                metadata={
                    "tofu_user_id": tofu_user_id,
                    "price_id": price,
                    "tofu_product": "tofu_pages",
                },
            )
            if not link_tofu_user_to_checkout_session(
                tofu_user_id, checkout_session.id
            ):
                message = "Failed to create the checkout session"
                logging.error(message)
                return Response(
                    {"error": {"message": message}},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                {"checkout_session_url": checkout_session.url},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            message = f"Error creating checkout session: {str(e)}"
            logging.error(message)
            return Response(
                {"error": {"message": message}}, status=status.HTTP_400_BAD_REQUEST
            )
