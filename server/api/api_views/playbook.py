"""
For Playbook related operations
"""

import logging
import time
import traceback
import uuid

from django.core.cache import cache
from django.db import transaction
from django.db.models import Prefetch
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.response import Response

from ..connected_assets.connected_assets_url_validator import (
    ConnectedAssetsUrlValidator,
)
from ..content_template import ContentTemplateHandler
from ..logger import (
    tofu_axiom_logger,
)
from ..mixins import TofuPagesKillSwitchMixin
from ..models import (
    Campaign,
    ContentGroup,
    Playbook,
    PlaybookUser,
    TargetInfo,
    TargetInfoGroup,
)
from ..playbook import PlaybookHandler
from ..playbook_build.doc_loader import DocLoader
from ..playbook_campaign import PlaybookCampaignHandler
from ..search.tofu_searcher import <PERSON>coneTofuSearcher, SearchObjectType
from ..serializers import (
    CampaignSerializer,
    CompanyInfoSerializer,
    ContentTemplateSerializer,
    PlaybookSerializer,
    TargetInfoGroupSerializer,
)
from ..shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ..status import StatusHandler
from ..sync.crm_list_sync.crm_list_sync_tasks import (
    import_list_as_target_info_group_task,
)
from ..sync.crm_list_sync.crm_list_sync_workflow import (
    ImportListAsTargetInfoGroupTaskInput,
    RecordType,
)
from ..tasks import async_refresh_playbook_context
from ..thread_locals import (
    try_set_current_playbook,
)
from ..throttling import tofu_lite_ratelimit
from ..utils import is_valid_url
from .pagination.pagination_mixin import PaginatedListMixin
from .pagination.pagination_models import (
    PaginatedResponseSerializer,
    PaginationQuerySerializer,
)


class PlaybookViewSet(
    PaginatedListMixin, TofuPagesKillSwitchMixin, viewsets.ModelViewSet
):
    queryset = Playbook.objects
    serializer_class = PlaybookSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "playbook_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        try_set_current_playbook(obj)
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if "lite" in self.request.query_params:
            lite_literal = self.request.query_params.get("lite")
            context["lite"] = lite_literal.lower() in ["true", "1", "yes"]
        return context

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ("create", "destroy"):
            permission_classes = [IsAdminUser]
        else:
            # permission handled inside the methods
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def is_request_from_creator_or_user_or_superuser(self, request):
        playbook = self.get_object()
        playbook_user = PlaybookUser.objects.filter(
            playbook=playbook, user=request.user
        ).first()
        if (
            playbook_user and playbook_user.type in ("creator", "user")
        ) or request.user.is_superuser:
            return True
        return False

    @swagger_auto_schema(
        operation_summary="Get all playbooks this user has access to",
        operation_description="",
        manual_parameters=[
            openapi.Parameter(
                name="lite",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
            )
        ],
    )
    @tofu_lite_ratelimit(rate="20/m")
    def list(self, request, *args, **kwargs):
        # Query the PlaybookUser model to get all instances where the user is either a "creator" or "user"
        playbook_users = PlaybookUser.objects.select_related("playbook").filter(
            user=request.user, type__in=["creator", "user"]
        )

        # Using Prefetch to optimize the fetching of related playbooks
        playbooks_prefetch = Prefetch(
            "playbook",
            queryset=Playbook.objects.select_related("company_object").prefetch_related(
                "target_info_groups__targets", "asset_info_groups__assets"
            ),
            to_attr="user_playbooks",
        )

        # Get playbooks directly from the prefetched data
        self.queryset = [
            pu.playbook for pu in playbook_users.prefetch_related(playbooks_prefetch)
        ]

        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Get playbook given id",
        operation_description="",
        manual_parameters=[
            openapi.Parameter(
                name="lite",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
            )
        ],
    )
    @tofu_lite_ratelimit(rate="20/m")
    def retrieve(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        with transaction.atomic():
            # Fetch and lock the instance
            instance = self.get_object()
            Playbook.objects.select_for_update().get(id=instance.id)

            return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="use this to update playbook data",
        operation_description="Update playbook info. Always use this method for updates unless you want to change all the fields.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "company_info": {
                    "type": openapi.TYPE_OBJECT,
                    "description": "a json object of company info",
                    "example": {
                        "meta": {"position": 1},
                        "Company Name": {
                            "data": [
                                {
                                    "id": "uuid1",
                                    "type": "text",
                                    "value": "name of the company",
                                }
                            ],
                            "meta": {"position": 1},
                        },
                        "Company Website": {
                            "data": [
                                {
                                    "id": "uuid1",
                                    "type": "url",
                                    "value": "url of the company website",
                                }
                            ],
                            "meta": {"position": 2},
                        },
                        "Company Description": {
                            "data": [
                                {
                                    "id": "uuid1",
                                    "type": "text",
                                    "value": "description of the company",
                                }
                            ],
                            "meta": {"position": 3},
                        },
                    },
                },
                "target_info": {
                    "type": openapi.TYPE_OBJECT,
                    "description": "a json object of target info",
                    "example": {
                        "meta": {"position": 2},
                        "Customer Segments": {
                            "Digital Health": {
                                "data": [
                                    {
                                        "id": "uuid1",
                                        "type": "text",
                                        "value": "text description",
                                    },
                                    {"id": "uuid2", "type": "url", "value": "url"},
                                ],
                                "meta": {"position": 1},
                            },
                            "Labs": {
                                "data": [
                                    {
                                        "id": "uuid1",
                                        "type": "text",
                                        "value": "text description",
                                    },
                                    {"id": "uuid2", "type": "url", "value": "url"},
                                ],
                                "meta": {"position": 2},
                            },
                            "meta": {"position": 1},
                        },
                        "Companies": {
                            "Nuvo": {
                                "data": [
                                    {
                                        "id": "uuid1",
                                        "type": "text",
                                        "value": "text description",
                                    },
                                    {"id": "uuid2", "type": "url", "value": "url"},
                                ],
                                "meta": {"position": 1},
                            },
                            "Equip": {
                                "data": [
                                    {
                                        "id": "uuid1",
                                        "type": "text",
                                        "value": "text description",
                                    },
                                    {"id": "uuid2", "type": "url", "value": "url"},
                                ],
                                "meta": {"position": 2},
                            },
                        },
                    },
                },
                "assets": {
                    "type": openapi.TYPE_OBJECT,
                    "description": "a json object of assets",
                    "example": {
                        "meta": {"position": 3},
                        "Blogs": {
                            "blog 1": {
                                "data": [
                                    {
                                        "id": "uuid1",
                                        "type": "text",
                                        "value": "text description",
                                    },
                                    {"id": "uuid2", "type": "url", "value": "url"},
                                ],
                                "meta": {"position": 1},
                            },
                            "meta": {"position": 1},
                        },
                    },
                },
            },
        ),
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @tofu_lite_ratelimit(rate="20/m")
    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        with transaction.atomic():
            # Fetch and lock the instance
            instance = self.get_object()
            Playbook.objects.select_for_update().get(id=instance.id)

            return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Copy playbook to another one",
        operation_description="Copy playbook to another one",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "source_playbook_id": {
                    "type": openapi.TYPE_INTEGER,
                }
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def copy_from_playbook(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        source_playbook_id = request.data.get("source_playbook_id")
        if not source_playbook_id:
            return Response(
                {"error": "source_playbook_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        source_playbook = Playbook.objects.filter(id=source_playbook_id).first()
        if not source_playbook:
            return Response(
                {"error": f"source_playbook_id {source_playbook_id} does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        destination_playbook = self.get_object()
        PlaybookHandler(destination_playbook).copy_from_playbook(source_playbook)

        return Response({}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Copy campaign from another playbook",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "source_campaign_id": {
                    "type": openapi.TYPE_INTEGER,
                },
                "copy_generations": {
                    "type": openapi.TYPE_BOOLEAN,
                    "default": False,
                },
            },
            required=["source_campaign_id"],
        ),
    )
    @action(detail=True, methods=["post"])
    def copy_campaign(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        source_campaign_id = request.data.get("source_campaign_id")
        if not source_campaign_id:
            return Response(
                {"error": "source_campaign_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        source_campaign = Campaign.objects.filter(id=source_campaign_id).first()
        if not source_campaign:
            return Response(
                {"error": f"source_campaign_id {source_campaign_id} does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        copy_generations = request.data.get("copy_generations", False)

        destination_playbook = self.get_object()
        copied_campaign = PlaybookCampaignHandler(destination_playbook).copy_campaign(
            source_campaign, copy_generations
        )

        return Response(
            CampaignSerializer(copied_campaign).data, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Get the current status of the playbook",
        operation_description="It will return a dictionary with {type: status}",
    )
    @action(detail=True, methods=["get"])
    def status(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        obj = self.get_object()
        playbook_status_handlers = StatusHandler.get_playbook_status_queryset(obj)
        playbook_status = {
            status_handler.type: status_handler.status
            for status_handler in playbook_status_handlers
        }
        try:
            target_status = cache.get(f"playbook_{obj.id}_target_status")
        except Exception as e:
            logging.exception(f"debug: Failed to get target status for {obj.id}: {e}")
            target_status = None
        try:
            asset_status = cache.get(f"playbook_{obj.id}_asset_status")
        except Exception as e:
            logging.exception(f"debug: Failed to get asset status for {obj.id}: {e}")
            asset_status = None
        try:
            doc_status = cache.get(f"playbook_{obj.id}_doc_status")
        except Exception as e:
            logging.exception(f"debug: Failed to get doc status for {obj.id}: {e}")
            doc_status = None
        try:
            tone_status = cache.get(f"playbook_{obj.id}_tone_status")
        except Exception as e:
            logging.exception(f"debug: Failed to get tone status for {obj.id}: {e}")
            tone_status = None
        try:
            doc_status_expanded = cache.get(f"playbook_{obj.id}_doc_status_expanded")
        except Exception as e:
            logging.exception(
                f"debug: Failed to get doc status expanded for {obj.id}: {e}"
            )
            doc_status_expanded = None
        return Response(
            {
                "playbook_status": playbook_status,
                "target_status": target_status,
                "asset_status": asset_status,
                "doc_status": doc_status,
                "tone_status": tone_status,
                "doc_status_expanded": doc_status_expanded,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Force to refresh the derived context of the playbook",
        operation_description="Only refresh context for the given columns",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "column_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": {"type": openapi.TYPE_STRING},
                    "default": ["company_info", "target_info", "assets"],
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def force_context_refresh(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        column_ids = request.data.get("column_ids", [])
        obj = self.get_object()

        task_id = f"refresh_playbook_context_{uuid.uuid4()}"
        async_refresh_playbook_context.apply_async(
            args=(
                obj.id,
                column_ids,
            ),
            task_id=task_id,
            priority=1,
        )

        return Response({"task_id": task_id}, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"])
    def metric_tiles(self, request, *args, **kwargs):
        obj = self.get_object()
        if not obj:
            return Response(status=status.HTTP_404_NOT_FOUND)
        metric_tiles = PlaybookHandler(obj).get_metric_tiles()
        return Response(metric_tiles, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Websites search",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "keyword": {
                    "type": openapi.TYPE_STRING,
                    "example": "Costco company introductions and products",
                },
                "num_links": {
                    "type": openapi.TYPE_INTEGER,
                    "example": 5,
                },
                "include_sitelinks": {
                    "type": openapi.TYPE_BOOLEAN,
                    "example": False,
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    # the reason to put this endpoint under playbook is because it helps to know which customers use the resources
    # which means the web_search is not free but under scrapingbee's plan and we need to know who uses it
    def web_search(self, request, *args, **kwargs):
        # Deprecated 02/03/2025
        return Response(status=status.HTTP_404_NOT_FOUND)
        # if not self.get_object():
        #     return Response(status=status.HTTP_404_NOT_FOUND)
        # if not self.is_request_from_creator_or_user_or_superuser(request):
        #     return Response(status=status.HTTP_403_FORBIDDEN)
        # playbook_instance = self.get_object()

        # keyword = request.data.get("keyword", "")
        # num_links = request.data.get("num_links", 5)
        # include_sitelinks = request.data.get("include_sitelinks", False)

        # links = PlaybookHandler(playbook_instance).web_search(
        #     keyword=keyword, num_links=num_links, include_sitelinks=include_sitelinks
        # )

        # return Response(
        #     data={"links": links},
        #     status=status.HTTP_200_OK,
        # )

    @action(detail=True, methods=["get"])
    def suggested_campaigns(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        playbook_instance = self.get_object()
        suggested_campaign_queryset = Campaign.objects.filter(
            playbook=playbook_instance, campaign_params__suggested_campaign=True
        ).order_by("-created_at")

        suggested_campaigns_data = CampaignSerializer(
            suggested_campaign_queryset, many=True
        ).data
        return Response(
            data={"suggested_campaigns": suggested_campaigns_data},
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="List campaign templates",
        operation_description="Retrieve a list of campaign templates, optionally filtering by whether they are v3 campaigns.",
        manual_parameters=[
            openapi.Parameter(
                name="is_campaign_v3",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Filter campaign templates by v3 status",
                required=False,
            )
        ],
    )
    @action(detail=True, methods=["get"])
    def list_campaign_templates(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        is_campaign_v3 = request.query_params.get("is_campaign_v3", None)
        if is_campaign_v3 is not None:
            is_campaign_v3 = is_campaign_v3.lower() in ["true", "1", "yes"]
        is_campaign_v3 = bool(is_campaign_v3)

        campaign_templates = PlaybookCampaignHandler(
            self.get_object()
        ).list_campaign_templates(is_campaign_v3=is_campaign_v3)

        return Response(
            data={"campaign_templates": campaign_templates},
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Copy campaign template",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "campaign_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "Campaign Name",
                },
                "campaign_template_id": {
                    "type": openapi.TYPE_INTEGER,
                    "example": 1,
                },
                "assets": {
                    "type": openapi.TYPE_OBJECT,
                    "example": {"Campaign Assets": ["whitepaper 1"]},
                },
                "content_group_ids": {
                    "type": openapi.TYPE_ARRAY,
                    "items": openapi.Schema(type=openapi.TYPE_INTEGER),
                    "example": [1, 2],
                },
                "dest_campaign_id": {
                    "type": openapi.TYPE_INTEGER,
                    "example": 1,
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def copy_campaign_template(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        campaign_template_id = request.data.get("campaign_template_id")
        assets = request.data.get("assets", {})
        content_group_ids = request.data.get("content_group_ids", [])
        campaign_name = request.data.get("campaign_name", "")
        if not campaign_name:
            return Response(
                {"error": "campaign_name is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not campaign_template_id:
            return Response(
                {"error": "campaign_template_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        source_campaign = Campaign.objects.filter(id=campaign_template_id).first()
        if not source_campaign:
            return Response(
                {"error": f"campaign id {campaign_template_id} does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        is_source_campaign_v3 = source_campaign.campaign_params.get(
            "is_campaign_v3", False
        )
        if not is_source_campaign_v3 and not assets:
            return Response(
                {"error": "assets is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if is_source_campaign_v3:  # v3 copy
            if content_group_ids:
                return Response(
                    {
                        "error": "content_group_ids is not available for campaign_template_v3"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            if not content_group_ids:
                return Response(
                    {"error": "content_group_ids is required for campaign_template_v2"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        destination_playbook = self.get_object()
        dest_campaign_id = request.data.get("dest_campaign_id", None)
        if dest_campaign_id:
            dest_campaign = Campaign.objects.filter(id=dest_campaign_id).first()
            if not dest_campaign:
                return Response(
                    {"error": f"dest_campaign_id {dest_campaign_id} does not exist"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            is_dest_campaign_v3 = dest_campaign.campaign_params.get(
                "is_campaign_v3", False
            )
            is_campaign_match = (is_source_campaign_v3 and is_dest_campaign_v3) or (
                not is_source_campaign_v3 and not is_dest_campaign_v3
            )
            if not is_campaign_match:
                logging.error(f"mismatch for campaign template copy")

                return Response(
                    {
                        "error": f"campaign doesn't match on v3 type between campaigns {source_campaign.id} and {dest_campaign.id}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        try:
            new_campaign = PlaybookCampaignHandler(
                destination_playbook
            ).copy_campaign_template(
                new_campaign_user=request.user,
                src_campaign=source_campaign,
                new_campaign_name=campaign_name,
                assets=assets,
                content_group_ids=content_group_ids,
                dest_campaign=dest_campaign if dest_campaign_id else None,
            )
            new_campaign_data = CampaignSerializer(new_campaign).data
            return Response(
                data=new_campaign_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging.error(
                f"Error copying campaign template: {e}\n{traceback.format_exc()}"
            )
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Extract docs",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "doc_type": {
                    "type": openapi.TYPE_STRING,
                    "example": "file",
                },
                "doc_value": {
                    "oneOf": [
                        {
                            "type": openapi.TYPE_OBJECT,
                            "example": {
                                "s3_bucket": "tofu-uploaded-files",
                                "s3_filename": "009933db-0a81-6747-5564-fcf5479f3b8c.json",
                                "mime_file_type": "application/json",
                            },
                        },
                        {
                            "type": openapi.TYPE_STRING,
                            "example": "https://www.google.com",
                        },
                    ]
                },
                "disable_cache": {
                    "type": openapi.TYPE_BOOLEAN,
                    "example": False,
                },
                "update_cache": {
                    "type": openapi.TYPE_BOOLEAN,
                    "example": False,
                },
                "fast_return": {
                    "type": openapi.TYPE_BOOLEAN,
                    "example": False,
                },
            },
        ),
    )
    @action(detail=False, methods=["post"])
    def extract_doc(self, request, *args, **kwargs):
        doc_type = request.data.get("doc_type", "")
        doc_value = request.data.get("doc_value", "")
        disable_cache = request.data.get("disable_cache", False)
        update_cache = request.data.get("update_cache", False)
        fast_return = request.data.get("fast_return", False)
        if not doc_type:
            doc_type = "url"

        if not doc_type or not doc_value:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        doc_loader = DocLoader()
        try:
            result = doc_loader.extract_single_doc_from_loader(
                type=doc_type,
                value=doc_value,
                disable_cache=disable_cache,
                update_cache=update_cache,
                fast_return=fast_return,
            )

            tofu_axiom_logger.log_axiom(
                event_type="extract_doc",
                user_id=request.user.id,
                data={"doc_type": doc_type, "doc_value": doc_value},
                result=result,
            )
        except Exception as e:
            logging.exception(f"Error extracting doc: {e}")
            return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": str(e)})
        if not result:
            return Response(
                status=status.HTTP_400_BAD_REQUEST, data={"error": "No data extracted"}
            )
        result = {"content": result[0].page_content, "metadata": result[0].metadata}
        return Response(result, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Get Tofu Insights",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "target_group_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "Target Group Name",
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def insights_csv(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        playbook_instance = self.get_object()
        target_group_name = request.data.get("target_group_name", "")
        insight_url = PlaybookHandler(playbook_instance).get_insight_csv_url(
            target_group_name
        )
        return Response(
            data=insight_url,
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Save as content template",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content_group_id": {
                    "type": openapi.TYPE_INTEGER,
                    "example": 1,
                },
                "content_template_name": {
                    "type": openapi.TYPE_STRING,
                    "example": "Content Template Name",
                },
                "save_repurpose_variations": {
                    "type": openapi.TYPE_BOOLEAN,
                    "example": True,
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def save_content_template(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_group_id = request.data.get("content_group_id")
        content_template_name = request.data.get("content_template_name")
        if not content_group_id:
            return Response(
                {"error": "content_group_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not content_template_name:
            return Response(
                {"error": "content_template_name is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        content_group = ContentGroup.objects.filter(id=content_group_id).first()
        if not content_group:
            return Response(
                {"error": f"content group id {content_group_id} does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        save_repurpose_variations = request.data.get("save_repurpose_variations", True)
        try:
            new_content_template = ContentTemplateHandler(
                request.user, self.get_object()
            ).save_content_as_template(
                content_group,
                content_template_name,
                save_repurpose_variations=save_repurpose_variations,
            )
            new_content_template_data = ContentTemplateSerializer(
                new_content_template
            ).data
            return Response(
                data=new_content_template_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging.error(
                f"Error saving content template: {e}\n{traceback.format_exc()}"
            )
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Fetch content templates for the given content type",
        operation_description="",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content_type": {
                    "type": openapi.TYPE_STRING,
                    "example": "Email - SDR",
                },
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def fetch_content_templates(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        content_type = request.data.get("content_type")
        if not content_type:
            return Response(
                {"error": "content_type is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        content_templates = ContentTemplateHandler(
            request.user, self.get_object()
        ).fetch_content_templates(
            content_type,
            is_template_v2=True,
        )
        content_templates_data = ContentTemplateSerializer(
            content_templates, many=True
        ).data
        return Response(
            data={"content_templates": content_templates_data},
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Check if URL is supported for connected assets",
        operation_description="Validates if the given URL can be used as a connected asset",
        manual_parameters=[
            openapi.Parameter(
                "url",
                openapi.IN_QUERY,
                description="URL to validate",
                type=openapi.TYPE_STRING,
                required=True,
            ),
        ],
    )
    @action(detail=False, methods=["get"])
    def check_url_support_for_connected_assets(self, request, *args, **kwargs):
        url = request.query_params.get("url")
        if not url:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        url_validator = ConnectedAssetsUrlValidator(url)
        return Response(
            url_validator.validate().model_dump(), status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Search Tofu",
        operation_description="Search Tofu",
        manual_parameters=[
            openapi.Parameter(
                "query",
                openapi.IN_QUERY,
                description="Query to search",
                type=openapi.TYPE_STRING,
                required=True,
            ),
            openapi.Parameter(
                "top_k",
                openapi.IN_QUERY,
                description="Number of results to return",
                type=openapi.TYPE_INTEGER,
                required=False,
                default=5,
            ),
            openapi.Parameter(
                "object_types",
                openapi.IN_QUERY,
                description="Object types to search",
                type=openapi.TYPE_ARRAY,
                items=openapi.Items(type=openapi.TYPE_STRING),
                required=False,
                default=[],
            ),
        ],
    )
    @action(detail=True, methods=["get"])
    def search(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        playbook_instance = self.get_object()

        query = request.query_params.get("query")
        top_k = int(request.query_params.get("top_k", 5))

        if top_k < 1:
            logging.error(f"top_k must be greater than 0")
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"error": "top_k must be greater than 0"},
            )

        object_types_str = request.query_params.get("object_types", "")
        object_types = object_types_str.split(",") if object_types_str else []

        for object_type in object_types:
            if object_type not in SearchObjectType.__members__:
                logging.error(f"Invalid object type: {object_type}")
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data={"error": f"Invalid object type: {object_type}"},
                )

        tofu_searcher = PineconeTofuSearcher(playbook_instance.id)
        results = tofu_searcher.search(query, top_k, object_types)
        if len(results) == 0:
            logging.warning(f"No results found for query: {query}")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
                data={"results": []},
            )
        return Response(results, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Add company from Clay to target list",
        operation_description="Add a company from Clay into a target list in the playbook",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["target_list_name", "company_name", "company_website"],
            properties={
                "target_list_name": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Name of the target list to add the company to",
                ),
                "company_name": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Name of the company"
                ),
                "company_website": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Website of the company"
                ),
                "optional_fields": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Additional fields for the company",
                    example={"industry": "Technology", "size": "1000-5000"},
                ),
            },
        ),
    )
    @action(detail=False, methods=["post"])
    def add_company_from_clay(self, request, *args, **kwargs):
        # get the playbook the user is attached to
        playbook = Playbook.objects.filter(users=request.user).first()
        if not playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)

        target_list_name = request.data.get("target_list_name")
        company_name = request.data.get("company_name")
        company_website = request.data.get("company_website")
        optional_fields = request.data.get("optional_fields", {})

        if not all([target_list_name, company_name, company_website]):
            return Response(
                {
                    "error": "target_list_name, company_name, and company_website are required"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Find or create target info group with proper locking
            with transaction.atomic():
                target_info_group = (
                    TargetInfoGroup.objects.select_for_update()
                    .filter(playbook=playbook, target_info_group_key=target_list_name)
                    .first()
                )

                if not target_info_group:
                    # Create new target info group
                    target_info_group = TargetInfoGroup.objects.create(
                        playbook=playbook,
                        target_info_group_key=target_list_name,
                        meta={
                            "type": "Company",
                            "tofu_lite": True,
                            "importListSettings": {
                                "syncFrom": "clay",
                                "lastSync": int(time.time() * 1000),
                            },
                        },
                    )

            # Prepare company data
            company_website_id = str(uuid.uuid4())
            company_data = {
                "docs": {
                    company_website_id: {
                        "id": company_website_id,
                        "type": "url",
                        "value": company_website,
                        "meta": {
                            "field_name": "company_website",
                            "label": "Company Website",
                        },
                        "position": 0,
                    }
                },
                "meta": {"synced_from": "clay"},
            }

            # Add optional fields
            for field_name, field_value in optional_fields.items():
                field_id = str(uuid.uuid4())
                field_value_str = str(field_value)
                field_type = "url" if is_valid_url(field_value_str) else "text"
                company_data["docs"][field_id] = {
                    "id": field_id,
                    "type": field_type,
                    "value": field_value_str,
                    "meta": {"label": field_name, "field_name": field_name},
                    "position": len(company_data["docs"]),
                }

            # create target info directly
            TargetInfo.objects.create(
                target_key=company_name,
                target_info_group=target_info_group,
                **company_data,
            )
            return Response(
                {
                    "message": f"Successfully added {company_name} to target list {target_list_name}"
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging.error(
                f"Error adding company to target list: {str(e)}\n{traceback.format_exc()}"
            )
            return Response(
                {"error": f"Failed to add company to target list: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Add contact from Clay to target list",
        operation_description="Add a contact from Clay into a target list in the playbook",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "target_list_name",
                "contact_full_name",
                "company_website",
                "contact_job_title",
            ],
            properties={
                "target_list_name": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Name of the target list to add the contact to",
                ),
                "contact_full_name": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Full name of the contact"
                ),
                "company_website": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Website of the company the contact belongs to",
                ),
                "contact_job_title": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Job title of the contact"
                ),
                "optional_fields": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Additional fields for the contact",
                    example={
                        "email": "<EMAIL>",
                        "linkedin_url": "https://linkedin.com/in/john",
                    },
                ),
            },
        ),
    )
    @action(detail=False, methods=["post"])
    def add_contact_from_clay(self, request, *args, **kwargs):
        # get the playbook the user is attached to
        playbook = Playbook.objects.filter(users=request.user).first()
        if not playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)

        target_list_name = request.data.get("target_list_name")
        full_name = request.data.get("contact_full_name")
        company_website = request.data.get("company_website")
        job_title = request.data.get("contact_job_title")
        optional_fields = request.data.get("optional_fields", {})

        if not all([target_list_name, full_name, company_website, job_title]):
            return Response(
                {
                    "error": "target_list_name, full_name, company_website, and job_title are required"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Find or create target info group with proper locking
            with transaction.atomic():
                target_info_group = (
                    TargetInfoGroup.objects.select_for_update()
                    .filter(playbook=playbook, target_info_group_key=target_list_name)
                    .first()
                )

                if not target_info_group:
                    # Create new target info group
                    target_info_group = TargetInfoGroup.objects.create(
                        playbook=playbook,
                        target_info_group_key=target_list_name,
                        meta={
                            "type": "Contact",
                            "tofu_lite": True,
                            "importListSettings": {
                                "syncFrom": "clay",
                                "lastSync": int(time.time() * 1000),
                            },
                        },
                    )

            # Prepare contact data
            company_website_id = str(uuid.uuid4())
            job_title_id = str(uuid.uuid4())
            contact_data = {
                "docs": {
                    company_website_id: {
                        "id": company_website_id,
                        "type": "url",
                        "value": company_website,
                        "meta": {
                            "field_name": "company_website",
                            "label": "Company Website",
                        },
                        "position": 0,
                    },
                    job_title_id: {
                        "id": job_title_id,
                        "type": "text",
                        "value": job_title,
                        "meta": {"field_name": "job_title", "label": "Job Title"},
                        "position": 1,
                    },
                },
                "meta": {"synced_from": "clay"},
            }

            # Add optional fields
            for field_name, field_value in optional_fields.items():
                field_id = str(uuid.uuid4())
                field_value_str = str(field_value)
                field_type = "url" if is_valid_url(field_value_str) else "text"
                contact_data["docs"][field_id] = {
                    "id": field_id,
                    "type": field_type,
                    "value": field_value_str,
                    "meta": {
                        "label": field_name.replace("_", " ").title(),
                        "field_name": field_name,
                    },
                    "position": len(contact_data["docs"]),
                }

            # create target info directly
            TargetInfo.objects.create(
                target_key=full_name,
                target_info_group=target_info_group,
                **contact_data,
            )
            return Response(
                {
                    "message": f"Successfully added contact {full_name} to target list {target_list_name}"
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging.error(
                f"Error adding contact to target list: {str(e)}\n{traceback.format_exc()}"
            )
            return Response(
                {"error": f"Failed to add contact to target list: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Import list from 3rd-party platform (e.g., HubSpot, Salesforce, Marketo) and create targets in the playbook",
        operation_description="Import a list from a 3rd-party platform into the playbook. Optionally, add imported targets to a campaign.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["list_id", "source", "record_type"],
            properties={
                "list_id": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="The ID of the list to import",
                ),
                "tableData": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    description="An array specifying which columns to fetch from the 3rd-party platform. Each item should have 'dataType', 'columnName', and optionally 'label'. If omitted, defaults will be used.",
                    items=openapi.Items(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            "dataType": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description="Type of the data, e.g., 'subtarget_name', 'hubspot_identifier', 'url', 'text'",
                            ),
                            "columnName": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description="Column name in the 3rd-party platform",
                            ),
                            "label": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description="Optional label for the column",
                            ),
                        },
                        required=["dataType", "columnName"],
                    ),
                    example=[
                        {
                            "dataType": "subtarget_name",
                            "columnName": "name",
                            "label": "Company Name",
                        },
                        {
                            "dataType": "hubspot_identifier",
                            "columnName": "hs_object_id",
                            "label": "HubSpot ID",
                        },
                        {
                            "dataType": "url",
                            "columnName": "website",
                            "label": "Website",
                        },
                        {
                            "dataType": "text",
                            "columnName": "about_us",
                            "label": "About Us",
                        },
                        {
                            "dataType": "text",
                            "columnName": "hs_keywords",
                            "label": "Keywords",
                        },
                    ],
                ),
                "source": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="The source platform (e.g., 'hubspot', 'salesforce', 'marketo')",
                    example="hubspot",
                ),
                "record_type": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Type of record to import: 'company', 'contact', or 'lead'",
                    example="company",
                ),
                "create_targets": openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description="Whether to create targets for the imported records",
                    example=True,
                ),
            },
        ),
    )
    @action(detail=True, methods=["post"])
    def import_list(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        # Extract and validate input
        list_id = request.data.get("list_id")
        table_data = request.data.get("tableData")
        source = request.data.get("source")
        record_type = request.data.get("record_type")
        create_targets = request.data.get("create_targets", True)

        # Validate required fields
        if not list_id or not source or not record_type:
            return Response(
                {"error": "list_id, source, and record_type are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if record_type not in [RecordType.COMPANY, RecordType.CONTACT, RecordType.LEAD]:
            return Response(
                {"error": "record_type must be one of: 'Company', 'Contact', 'Lead'"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            source_platform = PlatformType.Value(source)
        except ValueError:
            return Response(
                {
                    "error": f"source must be one of: {', '.join([platform.value for platform in PlatformType])}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        task_id = f"import_list_{self.get_object().id}_{list_id}_{source}_{record_type}:{uuid.uuid4()}"
        task_submit_time = time.time()
        task_input = ImportListAsTargetInfoGroupTaskInput(
            playbook_id=self.get_object().id,
            list_id=list_id,
            source=source_platform,
            record_type=record_type,
            table_data=table_data,
            task_submit_time=task_submit_time,
            create_targets=create_targets,
        )
        import_list_as_target_info_group_task.apply_async(
            kwargs={"task_input": task_input, "task_id": task_id},
            task_id=task_id,
            priority=9,
        )

        return Response(
            {
                "task_id": task_id,
            },
            status=status.HTTP_200_OK,
        )

    # The below APIs are added for playbook refactoring
    @action(detail=True, methods=["get"])
    def company(self, request, *args, **kwargs):
        playbook_instance = self.get_object()
        if not playbook_instance:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        if not playbook_instance.company_object:
            return Response(status=status.HTTP_404_NOT_FOUND)
        return Response(
            CompanyInfoSerializer(playbook_instance.company_object).data,
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Get all target info groups for a playbook",
        operation_description="Get all target info groups for a playbook",
        query_serializer=PaginationQuerySerializer,
        responses={
            200: PaginatedResponseSerializer,
            400: "Bad request",
            403: "Forbidden",
            404: "Not found",
        },
    )
    @action(detail=True, methods=["get"])
    def target_info_groups(self, request, *args, **kwargs):
        playbook = self.get_object()
        if not playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_or_user_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        serializer = PaginationQuerySerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        params = serializer.validated_data
        sort_by = params["sort_by"]
        order = params["order"]
        page = params["page"]
        page_size = params["page_size"]

        target_info_groups = playbook.target_info_groups.all()

        # temp: if caller don't provide page and page_size, return all target info groups
        if (
            request.query_params.get("page") is None
            or request.query_params.get("page_size") is None
        ):
            target_info_groups_serialized = TargetInfoGroupSerializer(
                target_info_groups, many=True
            )
            return self.get_unpaginated_response(
                target_info_groups_serialized, target_info_groups
            )

        allowed_sort_fields = [
            "id",
            "target_info_group_key",
            "created_at",
            "updated_at",
        ]
        target_info_groups = self.apply_ordering(
            target_info_groups, sort_by, order, allowed_sort_fields
        )
        page_obj = self.paginate_queryset(target_info_groups, page, page_size)
        target_info_groups_serialized = TargetInfoGroupSerializer(page_obj, many=True)
        return self.get_paginated_response(target_info_groups_serialized, page_obj)
