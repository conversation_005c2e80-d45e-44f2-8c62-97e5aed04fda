import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple

from langchain.schema import AIMessage, BaseMessage, HumanMessage, SystemMessage

from .content_selection_llm import (
    ComponentIDValidator,
    ComponentSelectionOutput,
    SmallLLMTaskResolver,
)
from .template_segmenter import LandingPageTemplateSegmenter

# Note: base_component_selection_prompt template removed - now using messages instead of PromptTemplate


class LandingPageComponentSelector:
    def __init__(self, html_template_data: str) -> None:
        self._html_template_data = html_template_data
        self._segmenter = LandingPageTemplateSegmenter(html_template_data)

    def select_components(self):
        segments = self._segmenter.segments_to_select

        if not segments:
            return {}
        selected_ids = self._select_component_ids()

        if not selected_ids:
            logging.warning("No components were selected by the LLM")
            return {}

        components = self._segmenter.get_components(selected_ids)
        return components

    def _select_component_ids(self) -> List[str]:
        """
        Select components using LLM with validation

        Returns:
            List of selected component IDs
        """
        # Convert candidates to the format expected by the messages
        components_dict = self._segmenter.segments_data
        messages = self._build_component_selection_messages(components_dict)

        task_resolver = SmallLLMTaskResolver()

        try:
            # Call the LLM task resolver with messages
            result = task_resolver.call_with_messages(
                messages=messages,
                output_pydantic_model=ComponentSelectionOutput,
                validation_context={"input_components": components_dict},
            )
            if "selected_component_ids" not in result:
                logging.error(f"No selected component ids found in result: {result}")
                return []

            return result["selected_component_ids"]

        except Exception as e:
            logging.error(f"Error in LLM component selection: {str(e)}")
            raise e

    def _build_component_selection_messages(
        self, components_dict: Dict[str, Any]
    ) -> List[BaseMessage]:
        """
        Build messages for landing page component selection instead of using PromptTemplate
        """
        selection_instructions = """
Instructions:
1. BE VERY SELECTIVE - Only choose components that will significantly benefit from personalization for sales/marketing effectiveness.

2. HIGHEST PRIORITY: Select ONLY components that contain:
   - Company-specific value propositions that can be tailored to target industries
   - Problem statements that can be customized for specific business challenges
   - Product descriptions that can be adapted to different use cases or industries
   - Benefit statements that can be contextualized for specific roles or company sizes
   - Statistics or metrics that can be made relevant to specific audiences

3. DO NOT select:
   - Customer quotes, testimonials, or reviews (these are authentic customer words that must remain unchanged)
   - Generic call-to-action buttons (e.g., "Learn More", "Get Started", "Book A Demo", "Request a Call", "Try Now", "Sign Up", "Contact Us")
   - Standard navigation elements (menus, headers, footers)
   - Generic promotional language ("Best in class", "Industry leading", "Trusted by", "Award winning")
   - Simple promotional questions or engagement prompts that lack substantive content
   - Generic product positioning statements and feature headers that lack detailed content
   - Basic product features that don't vary by audience
   - Copyright notices, legal text, or footer content
   - Generic greeting text or standard marketing copy
   - Button text or link text that doesn't contain substantive content
   - Generic value propositions that apply to all audiences equally
   - Standard business language that doesn't change based on target audience

4. MEDIUM PRIORITY: Select components that contain:
   - Industry-specific pain points that can be emphasized differently
   - Role-specific benefits that can be highlighted for different personas
   - Company size-specific advantages (enterprise vs SMB messaging)
   - Use case descriptions that can be tailored to different scenarios

5. Selection Strategy:
   - Default to NOT selecting unless there's clear personalization value
   - Focus on substantive content that changes meaning when personalized
   - Prioritize content that directly impacts conversion when customized
   - Avoid selecting content that remains the same regardless of audience
"""

        # Create the messages based on the original prompt structure
        messages = [
            SystemMessage(
                content="You are a helpful assistant that selects components from a landing page template for personalization to optimize marketing/sales goals. You must respond ONLY in JSON and follow the schema strictly."
            ),
            AIMessage(
                content="I'd like to understand the context first. Could you show me the original landing page template that we'll be working with?"
            ),
            HumanMessage(
                content=f"Here is the original landing page template:\n\n{self._html_template_data}"
            ),
            AIMessage(
                content="Thank you for showing me the landing page template. Now, what specific landing page components are available for me to analyze for personalization?"
            ),
            HumanMessage(
                content=f"Here are the available components to select from:\n\nComponents: {components_dict}"
            ),
            AIMessage(
                content="Thank you for providing the landing page components. What specific selection criteria should I follow? I want to make sure I'm being appropriately selective for conversion optimization."
            ),
            HumanMessage(
                content=f"""Please follow these selection instructions:

{selection_instructions}"""
            ),
            AIMessage(
                content="Excellent! I understand the selective approach for landing page optimization. What output format should I use, and what additional factors should I consider for maximum conversion impact?"
            ),
            HumanMessage(
                content="""Consider the marketing/sales goal when selecting components.
Ensure the selected components flow naturally together.
Determine the optimal number of components to select based on:
- Amount of target-specific information
- Content complexity
- Natural flow of the message
- Impact of personalization
- Balance between personalization and template structure

Respond with a JSON object that matches this schema:
{
  "selected_component_ids": ["component_id1", "component_id2", ...],
  "explanation": "Brief explanation of why these components were selected",
  "selection_strategy": "Brief explanation of how you determined the number of components to select"
}"""
            ),
        ]

        return messages
