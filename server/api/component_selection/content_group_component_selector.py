import logging
import re
from urllib.parse import parse_qs, urlparse

from bs4 import BeautifulSoup

from ..actions.legacy_converter.legacy_components_converter import (
    convert_components_v2_to_v3,
)
from ..actions.tofu_data_wrapper import TofuDataListHandler
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..playbook_build.doc_loader import get_template
from ..s3_utils import save_json_to_s3
from ..shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    TofuData,
    TofuDataList,
)
from ..shared_types import ContentType
from ..utils import get_s3_file
from .content_selection_llm import (
    EmailBodyComponentSelector,
    TextComponentSelector,
    WholeTextComponentSelector,
)
from .content_selection_lp import LandingPageComponentSelector


class TextEmailContentGroupComponentSelector:
    def __init__(self, action_handler) -> None:
        self._action_handler = action_handler
        self._content_group = (
            action_handler._action_handler_impl._action_data_wrapper.content_group
        )

        # set default
        self._email_select_body = True
        self._skip_greetings = True

    def _get_email_template_format(self):
        action_instance = (
            self._action_handler._action_handler_impl._action_data_wrapper.action_instance
        )
        template_data = action_instance.inputs.get("template", None)
        if not template_data:
            return None
        try:
            template_data = TofuDataListHandler.parse_json_to_tofu_data(template_data)
        except Exception:
            return None
        if not template_data or not template_data.data or len(template_data.data) == 0:
            return None

        template = template_data.data[0].template
        if not template or not template.template_fields:
            return None

        # Check the content_source_format of template fields
        # Priority: EMAIL_BODY > EMAIL_SUBJECT > first available field
        template_fields = template.template_fields

        # First check EMAIL_BODY if it exists
        email_body_field = template_fields.get("TOFU_COMPONENT_TYPE_EMAIL_BODY")
        if email_body_field and email_body_field.content_source_format:
            return email_body_field.content_source_format

        return None

    def is_eligible_for_auto_selection(self):
        template_format = self._get_email_template_format()
        if not template_format or template_format.lower() != "text":
            return False
        return True

    def _get_text_email_content_source_copy(self):
        template_tofu_data = self._content_group.action.inputs.get("template", None)
        if not template_tofu_data:
            return None
        try:
            template_tofu_data = TofuDataListHandler.parse_json_to_tofu_data(
                template_tofu_data
            )
        except Exception as e:
            return None
        if not template_tofu_data:
            return None

        template_data = template_tofu_data.data[0]

        # Get the template fields from the template data
        template_fields = template_data.template.template_fields
        if (
            not template_fields
            or "TOFU_COMPONENT_TYPE_EMAIL_BODY" not in template_fields
        ):
            logging.error("No email body template field found")
            return None

        content_source_copy = template_fields[
            "TOFU_COMPONENT_TYPE_EMAIL_BODY"
        ].content_source_copy
        return content_source_copy

    def _save_components_to_template_field_data(
        self, original_text, selected_components, section_type
    ):
        """
        Constructs template data with selected components in a structured format.

        Args:
            original_text: The original template text
            selected_components: Dictionary of selected components with their IDs and text

        Returns:
            dict: Template data in the format:
            {
                "type": "body",
                "children": [
                    {
                        "type": "paragraph",
                        "children": [
                            {
                                "type": "selected-component",
                                "id": component_id,
                                "children": [{"text": component_text}]
                            },
                            {"text": separator_text},
                            ...
                        ]
                    }
                ]
            }
        """
        # Initialize the template structure
        template_data = {
            "type": section_type,
            "children": [{"type": "paragraph", "children": []}],
        }

        # Get the paragraph children list
        children = template_data["children"][0]["children"]

        # Process the text and components
        current_pos = 0
        text = original_text

        # Find all components in the text
        matches = []
        matched_component_ids = set()

        for component_id, component in selected_components.items():
            component_text = component["text"]
            if component_text in text:
                matches.append(
                    (text.find(component_text), component_id, component_text)
                )
                matched_component_ids.add(component_id)

        # Check for unmatched components and log errors
        all_component_ids = set(selected_components.keys())
        unmatched_component_ids = all_component_ids - matched_component_ids

        if unmatched_component_ids:
            logging.error(
                f"Failed to match {len(unmatched_component_ids)} out of {len(all_component_ids)} components in original text"
            )
            for unmatched_id in unmatched_component_ids:
                component_text = selected_components[unmatched_id]["text"]
                # Truncate long text for logging
                truncated_text = (
                    component_text[:100] + "..."
                    if len(component_text) > 100
                    else component_text
                )
                logging.error(
                    f"Component '{unmatched_id}' not found in original text. Component text: '{truncated_text}'"
                )
        else:
            logging.info(
                f"Successfully matched all {len(all_component_ids)} components in original text"
            )

        if matches:
            # Sort matches by their position in the text
            matches.sort(key=lambda x: x[0])

            # Split the text around all components
            for pos, component_id, component_text in matches:
                # Add text before the component
                if pos > current_pos:
                    before_text = text[current_pos:pos]
                    # Preserve whitespace for email formatting
                    children.append({"text": before_text})

                # Add the component
                children.append(
                    {
                        "type": "selected-component",
                        "id": component_id,
                        "children": [{"text": component_text}],
                    }
                )

                current_pos = pos + len(component_text)

            # Add any remaining text
            if current_pos < len(text):
                after_text = text[current_pos:]
                # Preserve whitespace for email formatting
                children.append({"text": after_text})
        else:
            # If no components match, keep the original text
            logging.error(f"No components matched in original text: {text}")
            children.append({"text": text})

        return template_data

    def _save_text_email_components_to_template(
        self,
        text_email_subject_line_template,
        text_email_body_template,
        selected_subject_line_components,
        selected_body_components,
    ):

        # Create a new template content with updated components
        updated_template_content = []
        updated_template_content.append(
            self._save_components_to_template_field_data(
                text_email_subject_line_template,
                selected_subject_line_components,
                "subject-line",
            )
        )
        updated_template_content.append(
            self._save_components_to_template_field_data(
                text_email_body_template, selected_body_components, "body"
            )
        )

        content_source_copy = self._get_text_email_content_source_copy()
        if not content_source_copy:
            logging.error("No content source copy found")
            return self._content_group

        # Extract file name from the content_source_copy URL
        parsed_url = urlparse(content_source_copy)
        query_params = parse_qs(parsed_url.query)

        # Validate query parameters
        if "file" not in query_params or "directory" not in query_params:
            logging.error(
                f"Invalid URL format - missing required parameters: {content_source_copy}"
            )
            return self._content_group

        file_name = query_params["file"][0]
        bucket_name = query_params["directory"][0]

        # Save the updated template content to S3
        if save_json_to_s3(file_name, updated_template_content, bucket_name):
            logging.info(f"Successfully saved updated template to S3: {file_name}")
        else:
            logging.error(f"Failed to save updated template to S3: {file_name}")
            return self._content_group

        components_to_save = selected_subject_line_components | selected_body_components
        tofu_component_data = convert_components_v2_to_v3(
            self._content_group.action.playbook.id, components_to_save
        )
        tofu_data_list = TofuDataList(data=[TofuData(components=tofu_component_data)])
        self._action_handler.update_inputs({"components": tofu_data_list})

    def _get_text_email_template(self):
        content_source_copy = self._get_text_email_content_source_copy()
        if not content_source_copy:
            logging.error("No content source copy found")
            return None, None

        template_slate_data = get_template(content_source_copy)
        if not template_slate_data:
            logging.error("No template slate data found")
            return None, None

        def merge_slate_data(slate_data):
            """
            Merges slate data into a single text string while preserving selected components.

            Args:
                slate_data: List of sections with their children and components

            Returns:
                str: Merged text content
            """
            merged_text_subject_line = ""
            merged_text_body = ""

            # Process each section
            for section in slate_data:
                if section["type"] not in ["subject-line", "body"]:
                    continue

                merged_text = ""
                # Process each paragraph in the section
                for paragraph in section["children"]:
                    if paragraph["type"] != "paragraph":
                        continue

                    # Process each child in the paragraph
                    for child in paragraph["children"]:
                        if "text" in child:
                            # Regular text
                            merged_text += child["text"]
                        elif child["type"] == "selected-component":
                            # Selected component text
                            component_text = child["children"][0]["text"]
                            merged_text += component_text

                if section["type"] == "subject-line":
                    merged_text_subject_line += merged_text
                else:
                    merged_text_body += merged_text

            return merged_text_subject_line, merged_text_body

        subject_line_template, body_template = merge_slate_data(template_slate_data)

        return subject_line_template, body_template

    def _auto_select_text_email(self):
        text_email_subject_line_template, text_email_body_template = (
            self._get_text_email_template()
        )
        if not text_email_subject_line_template or not text_email_body_template:
            logging.error("No email template field found")
            return self._content_group
        if not isinstance(text_email_subject_line_template, str):
            logging.error(
                f"Subject line template is not a string: {text_email_subject_line_template}"
            )
            return self._content_group
        if not isinstance(text_email_body_template, str):
            logging.error(f"Body template is not a string: {text_email_body_template}")
            return self._content_group

        # select for whole subject line
        whole_text_selector = WholeTextComponentSelector(
            text_email_subject_line_template
        )
        selected_subject_line_components = whole_text_selector.auto_select_components()

        # select for body
        selected_body_components = None
        if self._email_select_body:
            email_body_selector = EmailBodyComponentSelector(
                text_email_body_template, select_greetings=not self._skip_greetings
            )
            selected_body_components = email_body_selector.select_components()
        else:
            text_selector = TextComponentSelector(
                text_email_body_template, select_greetings=not self._skip_greetings
            )
            selected_body_components = text_selector.select_components()

        self._save_text_email_components_to_template(
            text_email_subject_line_template,
            text_email_body_template,
            selected_subject_line_components,
            selected_body_components,
        )

        return self._content_group

    def auto_select_components(self, select_parameters):
        self._email_select_body = select_parameters.get(
            "email_select_body", self._email_select_body
        )
        self._skip_greetings = select_parameters.get(
            "skip_greetings", self._skip_greetings
        )
        try:
            return self._auto_select_text_email()
        except Exception as e:
            logging.exception(f"Error auto selecting components: {e}")
            return self._content_group


class LandingPageContentGroupComponentSelector:
    def __init__(self, action_handler) -> None:
        self._action_handler = action_handler

    def is_eligible_for_auto_selection(self):
        return True

    def _validate_html_format(self, content):
        """
        Validate if the content is in HTML format using BeautifulSoup.

        Args:
            content: The content to validate

        Returns:
            bool: True if content appears to be valid HTML, False otherwise
        """
        if not content or not isinstance(content, str):
            return False

        try:
            soup = BeautifulSoup(content, "html.parser")

            # Check if BeautifulSoup found any HTML tags
            # A valid HTML document should have at least some tags
            return len(soup.find_all()) > 0

        except Exception as e:
            logging.debug(f"HTML validation failed with exception: {e}")
            return False

    def _get_landing_page_template(self):
        content_group = (
            self._action_handler._action_handler_impl._action_data_wrapper.content_group
        )
        if not content_group:
            logging.error("No content group found")
            return None
        content_source_copy = content_group.content_group_params.get(
            "content_source_copy", None
        )
        if not content_source_copy:
            logging.error("No content source copy found")
            return None
        html_template = get_s3_file(content_source_copy)

        # Validate that the retrieved content is in HTML format
        if not self._validate_html_format(html_template):
            logging.error(
                f"Retrieved content is not valid HTML format: {content_source_copy}"
            )
            return None

        return html_template

    def auto_select_components(self, select_parameter=None):
        content_group = (
            self._action_handler._action_handler_impl._action_data_wrapper.content_group
        )
        if not content_group:
            logging.error("No content group found")
            return content_group

        html_template = self._get_landing_page_template()
        if not html_template:
            logging.error("No html template found")
            return content_group
        landing_page_component_selector = LandingPageComponentSelector(html_template)
        selected_components = landing_page_component_selector.select_components()
        tofu_component_data = convert_components_v2_to_v3(
            content_group.action.playbook.id, selected_components
        )
        tofu_data_list = TofuDataList(data=[TofuData(components=tofu_component_data)])
        self._action_handler.update_inputs({"components": tofu_data_list})
        content_group.refresh_from_db()
        return content_group


class ContentGroupComponentSelector(BaseTracableClass):
    def __init__(self, action_handler, user) -> None:
        self._action_handler = action_handler
        self._user = user
        self._selector = self._get_selector()

    def get_metadata(self):
        # TODO: add template data
        return {
            "action_id": self._action_handler._action_handler_impl._action_data_wrapper.action_instance.id,
        }

    def is_eligible_for_auto_selection(self):
        if not self._user or not self._user.is_eligible_for_auto_select_components():
            logging.info(
                f"User {self._user.username if self._user else 'None'} is not eligible for auto select components"
            )
            return False
        return (
            self._selector is not None
            and self._selector.is_eligible_for_auto_selection()
        )

    def _get_selector(self):
        action_instance = (
            self._action_handler._action_handler_impl._action_data_wrapper.action_instance
        )
        if action_instance.action_category != ActionCategory.Name(
            ActionCategory.ACTION_CATEGORY_PERSONALIZE
        ):
            # TODO: check if we need to support SEQ case
            return None
        if not action_instance.inputs.get("template", None):
            return None

        content_type = self._action_handler._action_handler_impl._action_data_wrapper.content_group.content_group_params.get(
            "content_type", ""
        )

        if content_type in [
            ContentType.EmailMarketing,
            ContentType.EmailSDR,
        ]:
            return TextEmailContentGroupComponentSelector(self._action_handler)
        elif content_type == ContentType.LandingPage:
            return LandingPageContentGroupComponentSelector(self._action_handler)
        return None

    @dynamic_traceable(
        name="auto_select_components",
    )
    def auto_select_components(self, select_parameters):
        if not self._selector or not self.is_eligible_for_auto_selection():
            return self._content_group
        return self._selector.auto_select_components(select_parameters)
