import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple

from langchain.chains import <PERSON><PERSON>hain
from langchain.chat_models import ChatOpenAI
from langchain.output_parsers import OutputF<PERSON>ingParser, PydanticOutputParser
from langchain.prompts import PromptTemplate
from langchain.schema import AIMessage, BaseMessage, HumanMessage, SystemMessage
from pydantic import BaseModel, Field, validator

from .template_segmenter import TextTemplateSegmenter


class SmallLLMTaskResolver:
    """Handles small NLP tasks using LLMs"""

    def __init__(self):
        self._model = "gpt-4o-mini-2024-07-18"
        # fallback models: gpt-4o-2024-11-20, claude-3-5-haiku-20241022

    def call(
        self,
        arguments: Dict[str, Any],
        prompt_template: PromptTemplate,
        output_pydantic_model: BaseModel,
        validation_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a task using LLM with validation

        Args:
            arguments: Input arguments for the prompt
            prompt_template: Template for the LLM prompt
            output_pydantic_model: Pydantic model for output validation
            validation_context: Optional context for validation (e.g., input components for component selection)

        Returns:
            Validated output as dictionary
        """
        # Initialize model
        llm = ChatOpenAI(model=self._model, temperature=0)

        # Set up parser with error correction
        parser = PydanticOutputParser(pydantic_object=output_pydantic_model)
        retrying_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)

        # Create and run the chain
        chain = LLMChain(
            llm=llm,
            prompt=prompt_template,
            output_parser=retrying_parser,
        )

        # Merge arguments and context into a single dictionary
        chain_input = {**arguments}
        if validation_context:
            chain_input["context"] = validation_context

        # Run the chain with merged input
        response = chain.run(**chain_input)

        return response.dict()

    def call_with_messages(
        self,
        messages: List[BaseMessage],
        output_pydantic_model: BaseModel,
        validation_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a task using LLM with a list of messages instead of a prompt template

        Args:
            messages: List of messages to send to the LLM
            arguments: Input arguments (not used directly but kept for consistency)
            output_pydantic_model: Pydantic model for output validation
            validation_context: Optional context for validation

        Returns:
            Validated output as dictionary
        """
        # Initialize model
        llm = ChatOpenAI(model=self._model, temperature=0)

        # Set up parser with error correction
        parser = PydanticOutputParser(pydantic_object=output_pydantic_model)
        retrying_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)

        # Add format instructions as additional messages instead of modifying the last message
        if messages:
            format_instructions = parser.get_format_instructions()

            # Add AI message explaining the format requirements
            ai_format_message = AIMessage(
                content="I need to respond in a specific JSON format. Let me ensure I follow the correct schema."
            )

            # Add User message with the format instructions
            user_format_message = HumanMessage(
                content=f"Please make sure your response follows this format:\n\n{format_instructions}"
            )

            # Append both messages to the conversation
            messages.append(ai_format_message)
            messages.append(user_format_message)

        # Call the LLM directly with messages
        response = llm(messages)

        # Parse the response with validation
        def parse_and_validate(content: str, use_retrying_parser: bool = False):
            """Helper function to parse and validate response content"""
            try:
                if use_retrying_parser:
                    parsed_response = retrying_parser.parse(content)
                else:
                    parsed_response = parser.parse(content)

                # Manually validate component IDs if this is ComponentSelectionOutput and we have validation context
                if (
                    hasattr(parsed_response, "selected_component_ids")
                    and validation_context
                    and "input_components" in validation_context
                ):

                    input_components = validation_context["input_components"]
                    validator = ComponentIDValidator(input_components)
                    is_valid, invalid_ids, corrected_ids = (
                        validator.validate_component_ids(
                            parsed_response.selected_component_ids
                        )
                    )

                    if not is_valid:
                        raise ValueError(f"Invalid component IDs found: {invalid_ids}")

                    # Apply corrected component IDs if any fuzzy matching occurred
                    if corrected_ids != parsed_response.selected_component_ids:
                        logging.error(
                            f"debug: Applied fuzzy matching corrections: {parsed_response.selected_component_ids} -> {corrected_ids}"
                        )
                        parsed_response.selected_component_ids = corrected_ids

                return parsed_response.dict()
            except Exception as e:
                raise e

        # Try parsing with standard parser first
        try:
            return parse_and_validate(response.content, use_retrying_parser=False)
        except Exception as parse_e:
            # If standard parsing fails, try the retrying parser
            logging.warning(f"Standard parsing failed: {parse_e}, attempting to fix...")
            try:
                return parse_and_validate(response.content, use_retrying_parser=True)
            except Exception as retry_e:
                logging.error(f"All parsing attempts failed. Last error: {retry_e}")
                raise retry_e

    # Usage example for call_with_messages with component validation:
    #
    # from langchain.schema import HumanMessage, SystemMessage
    #
    # resolver = SmallLLMTaskResolver()
    # components_dict = {"greeting_1": {"text": "Hello"}, "body_2": {"text": "World"}}
    # messages = [
    #     SystemMessage(content="You are a component selector."),
    #     HumanMessage(content=f"Select components from: {components_dict}")
    # ]
    #
    # result = resolver.call_with_messages(
    #     messages=messages,
    #     output_pydantic_model=ComponentSelectionOutput,
    #     validation_context={"input_components": components_dict}
    # )
    #
    # # If LLM returns "greeting_01" instead of "greeting_1", it will be auto-corrected
    # # The final result will contain the correct component IDs: ["greeting_1", "body_2"]


class ComponentIDValidator:
    """Validator for component IDs with extensible matching strategies"""

    def __init__(
        self, input_components: Dict[str, Any], matching_strategy: str = "exact"
    ):
        """
        Initialize the validator with input components and matching strategy

        Args:
            input_components: Dictionary of components where keys are component IDs
            matching_strategy: Strategy for matching component IDs ("exact" or "fuzzy")
        """
        self.input_components = input_components
        self.matching_strategy = matching_strategy
        self._validate_strategy()

    def _validate_strategy(self):
        """Validate that the matching strategy is supported"""
        if self.matching_strategy not in ["exact", "fuzzy"]:
            raise ValueError(f"Unsupported matching strategy: {self.matching_strategy}")

    def _exact_match(self, component_id: str) -> bool:
        """Check if component_id exists exactly in input_components"""
        return component_id in self.input_components

    def _fuzzy_match(self, component_id: str) -> Tuple[bool, Optional[str]]:
        """Check if component_id matches any input component ID using fuzzy matching

        Returns:
            Tuple of (is_valid, corrected_component_id)
        """
        from difflib import SequenceMatcher

        # Get the best match from input components
        best_match = max(
            self.input_components.keys(),
            key=lambda x: SequenceMatcher(None, component_id, x).ratio(),
            default=None,
        )

        if best_match is None:
            return False, None

        # If similarity ratio is above threshold, consider it a match
        similarity = SequenceMatcher(None, component_id, best_match).ratio()
        if similarity > 0.8:  # 80% similarity threshold
            return True, best_match
        else:
            return False, None

    def validate_component_ids(
        self, component_ids: List[str]
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate that all component IDs exist in input_components

        Args:
            component_ids: List of component IDs to validate

        Returns:
            Tuple of (is_valid, list_of_invalid_ids, list_of_corrected_ids)
        """
        invalid_ids = []
        corrected_ids = []

        for component_id in component_ids:
            if self.matching_strategy == "exact":
                is_valid = self._exact_match(component_id)
                if is_valid:
                    corrected_ids.append(component_id)  # No correction needed
                else:
                    invalid_ids.append(component_id)
            else:  # fuzzy matching
                is_valid, corrected_id = self._fuzzy_match(component_id)
                if is_valid and corrected_id:
                    corrected_ids.append(corrected_id)  # Use the corrected ID
                else:
                    invalid_ids.append(component_id)

        return len(invalid_ids) == 0, invalid_ids, corrected_ids


class ComponentSelectionOutput(BaseModel):
    selected_component_ids: List[str] = Field(
        ..., description="List of selected component IDs for personalization"
    )
    explanation: str = Field(
        ..., description="Explanation of why these components were selected"
    )
    selection_strategy: str = Field(
        ..., description="Strategy used to determine number of components to select"
    )

    @validator("selected_component_ids")
    def validate_component_ids(cls, v, values, **kwargs):
        """Validate that all component IDs exist in the input components"""
        # Get the input components from the context
        input_components = kwargs.get("context", {}).get("input_components", {})
        if not input_components:
            return v

        validator = ComponentIDValidator(input_components)
        is_valid, invalid_ids, corrected_ids = validator.validate_component_ids(v)

        if not is_valid:
            raise ValueError(f"Invalid component IDs found: {invalid_ids}")

        return v


# Note: base_component_selection_prompt template removed - now using messages instead of PromptTemplate


class TextSetenceComponentSelector(ABC):
    def __init__(self, text_template_data: str) -> None:
        self._text_template_data = text_template_data
        self._segmenter = TextTemplateSegmenter(text_template_data)

    def select_components(self):
        segments = self._segmenter.segments_to_select

        if not segments:
            return {}
        selected_ids = self._select_component_ids()

        if not selected_ids:
            logging.warning("No components were selected by the LLM")
            return {}

        components = self._segmenter.get_components(selected_ids)
        return components

    @abstractmethod
    def _build_component_selection_messages(
        self, components_dict: Dict[str, Any]
    ) -> List[BaseMessage]:
        """Build messages for component selection instead of using PromptTemplate"""
        pass

    def _select_component_ids(self) -> List[str]:
        """
        Select components using LLM with validation

        Returns:
            List of selected component IDs
        """
        # Convert candidates to the format expected by the messages
        components_dict = self._segmenter.segments_data
        messages = self._build_component_selection_messages(components_dict)

        task_resolver = SmallLLMTaskResolver()

        try:
            # Call the LLM task resolver with messages
            result = task_resolver.call_with_messages(
                messages=messages,
                output_pydantic_model=ComponentSelectionOutput,
                validation_context={"input_components": components_dict},
            )
            if "selected_component_ids" not in result:
                logging.error(f"No selected component ids found in result: {result}")
                return []

            return result["selected_component_ids"]

        except Exception as e:
            logging.error(f"Error in LLM component selection: {str(e)}")
            raise e


class TextComponentSelector(TextSetenceComponentSelector):
    # supported types
    # - text
    #   - email body
    #   - other
    def __init__(self, text_template_data: str, select_greetings: bool = False) -> None:
        super().__init__(text_template_data)
        self._select_greetings = select_greetings

    def _build_component_selection_messages(
        self, components_dict: Dict[str, Any]
    ) -> List[BaseMessage]:
        """Build messages for component selection instead of using PromptTemplate"""
        greeting_instruction = (
            ""  # allow selection
            if self._select_greetings
            else "- ANY greeting sections including personal greetings like 'Hi [Name],' 'Hello [Name],' 'Dear [Name],' or any variation that starts an email with a salutation"
        )
        selection_instructions = f"""
Instructions:
1. Review each component and select the most impactful ones for personalization.

2. HIGHEST PRIORITY: Select ALL components that contain:
   - Specific mentions of the recipient's name (WITHIN THE CONTENT, not in greetings)
   - Specific details about the recipient's work/achievements
   - Specific events or projects the recipient is involved in
   - Specific organizations or companies the recipient is associated with
   - Specific industry or field details relevant to the recipient

3. DO NOT select:
   {greeting_instruction}
   - Generic statements that don't benefit from personalization
   - Generic call-to-actions (e.g., "Would you be open to a chat?", "Let's connect", "I'd love to hear your thoughts")
   - Generic value propositions that don't reference specific recipient details

4. SECONDARY PRIORITY: Select components that contain:
   - Value propositions that can be meaningfully personalized
   - Pain points that can be tied to specific recipient details
   - Benefits that can be connected to recipient's specific situation
   - Specific features that can be related to recipient's work
   - Statistics or metrics that can be contextualized for the recipient
   - Industry-specific content that can be tailored to recipient's role
"""

        # Create the messages based on the original prompt structure
        messages = [
            SystemMessage(
                content="You are a helpful assistant that selects components from an email template for personalization to optimize marketing/sales goals. You must respond ONLY in JSON and follow the schema strictly."
            ),
            AIMessage(
                content="I'd like to understand the context first. Could you show me the original email template that we'll be working with?"
            ),
            HumanMessage(
                content=f"Here is the original email template:\n\n{self._text_template_data}"
            ),
            AIMessage(
                content="Thank you for showing me the email template. Now, what specific components are available for me to choose from for personalization?"
            ),
            HumanMessage(
                content=f"Here are the available components to select from:\n\nComponents: {components_dict}"
            ),
            AIMessage(
                content="Thank you for providing the components. What specific rules and criteria should I follow when selecting these components for personalization?"
            ),
            HumanMessage(
                content=f"""Please follow these selection instructions:

{selection_instructions}"""
            ),
            AIMessage(
                content="Got it! I understand the selection criteria. What output format should I use for my response, and are there any additional considerations I should keep in mind?"
            ),
            HumanMessage(
                content="""Consider the marketing/sales goal when selecting components.
Ensure the selected components flow naturally together.
Determine the optimal number of components to select based on:
- Amount of target-specific information
- Content complexity
- Natural flow of the message
- Impact of personalization
- Balance between personalization and template structure

Respond with a JSON object that matches this schema:
{
  "selected_component_ids": ["component_id1", "component_id2", ...],
  "explanation": "Brief explanation of why these components were selected",
  "selection_strategy": "Brief explanation of how you determined the number of components to select"
}"""
            ),
        ]

        return messages


class EmailBodyComponentSelector(TextSetenceComponentSelector):
    # supported types
    # - text
    #   - email body
    #   - other
    def __init__(self, text_template_data: str, select_greetings: bool = False) -> None:
        super().__init__(text_template_data)
        self._select_greetings = select_greetings

    def _build_component_selection_messages(
        self, components_dict: Dict[str, Any]
    ) -> List[BaseMessage]:
        """Build messages for component selection instead of using PromptTemplate"""
        greeting_instruction = (
            ""  # allow selection
            if self._select_greetings
            else "- ANY greeting sections including personal greetings like 'Hi [Name],' 'Hello [Name],' 'Dear [Name],' or any variation that starts an email with a salutation"
        )
        selection_instructions = f"""
Instructions:
1. Select ALL sentences in the email body EXCEPT:
   {greeting_instruction}
   - Any closing sections (e.g., "Best regards", "Sincerely", "Thank you", "Yours truly", "Warm regards")
   - Any signature blocks (typically containing name, title, contact info, or company details)
   - Any email signatures (usually marked with "--" or containing contact information)

2. DO include:
   - All main body content
   - Any PS (postscript) sections that appear after the closing - SELECT ALL SEQUENTIAL COMPONENTS that belong to the same P.S. section
   - Any PPS (post-postscript) sections - SELECT ALL SEQUENTIAL COMPONENTS that belong to the same P.P.S. section
   - Any additional notes or follow-ups

3. IMPORTANT - Sequential Component Selection:
   - If you see "P.S." as a component, also select the immediately following components that contain the P.S. content
   - If you see "P.P.S." as a component, also select the immediately following components that contain the P.P.S. content
   - Components are indexed sequentially, so consecutive components often belong together (e.g., "P.S." followed by the actual postscript text)
   - Always consider the logical grouping of sequential components that form complete thoughts or sections

4. Selection Strategy:
   - Select everything by default
   - Only exclude the specific sections mentioned above
   - Maintain the natural flow of the email
   - Keep all content that contributes to the main message
   - When selecting postscripts, select the complete postscript including all its sequential components
"""

        # Create the messages based on the original prompt structure
        messages = [
            SystemMessage(
                content="You are a helpful assistant that selects components from an email template for personalization to optimize marketing/sales goals. You must respond ONLY in JSON and follow the schema strictly."
            ),
            AIMessage(
                content="I'd like to understand the context first. Could you show me the original email template that we'll be working with?"
            ),
            HumanMessage(
                content=f"Here is the original email template:\n\n{self._text_template_data}"
            ),
            AIMessage(
                content="Thank you for showing me the email template. Now, what specific email body components are available for me to choose from for personalization?"
            ),
            HumanMessage(
                content=f"Here are the available components to select from:\n\nComponents: {components_dict}"
            ),
            AIMessage(
                content="Thank you for providing the components. What specific rules and criteria should I follow when selecting these email body components?"
            ),
            HumanMessage(
                content=f"""Please follow these selection instructions:

{selection_instructions}"""
            ),
            AIMessage(
                content="Perfect! I understand the email body selection rules. What output format should I use for my response, and are there any additional considerations for email flow?"
            ),
            HumanMessage(
                content="""Consider the marketing/sales goal when selecting components.
Ensure the selected components flow naturally together.
Determine the optimal number of components to select based on:
- Amount of target-specific information
- Content complexity
- Natural flow of the message
- Impact of personalization
- Balance between personalization and template structure

Respond with a JSON object that matches this schema:
{
  "selected_component_ids": ["component_id1", "component_id2", ...],
  "explanation": "Brief explanation of why these components were selected",
  "selection_strategy": "Brief explanation of how you determined the number of components to select"
}"""
            ),
        ]

        return messages


class WholeTextComponentSelector:
    def __init__(self, text_template_data: str) -> None:
        self.text_template_data = text_template_data

    def auto_select_components(self):
        component_id = str(uuid.uuid4())[:16]

        components = {}
        components[component_id] = {
            "meta": {
                "type": "text",
                "time_added": int(time.time()),
                "component_type": "email body",
                "html_tag_index": None,
                "isEmailSubject": False,
                "component_params": {"custom_instructions": []},
                "selected_element": "",
                "preceding_element": "",
                "succeeding_element": "",
            },
            "text": self.text_template_data,
        }

        return components
