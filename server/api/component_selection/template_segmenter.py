import logging
import re
import time
import uuid
from typing import Any, Dict, List, Optional, Tuple

from bs4 import BeautifulSoup


class TextTemplateSegmenter:
    def __init__(self, text_template_data: str) -> None:
        if not isinstance(text_template_data, str):
            raise ValueError("text_template_data must be a string")

        self.text_template_data = text_template_data

        self._segments = []
        self._segments_data = {}

        if text_template_data:
            self._process_text_template_data()

    @property
    def segments_to_select(self) -> List[str]:
        return [seg for seg in self._segments if seg.strip()]

    @property
    def segments_data(self) -> Dict[str, Dict[str, Any]]:
        return self._segments_data

    def _process_text_template_data(self):

        # First split by newlines to preserve them
        parts = re.split(r"(\n+)", self.text_template_data)

        # Then split each non-newline part into sentences
        segments = []
        for part in parts:
            if not part:  # Skip empty strings
                continue

            if part.isspace():  # If it's just whitespace (newlines)
                segments.append(part)
                continue

            # Split by period, question mark, or exclamation mark but keep the punctuation and following space with the sentence
            sentences = re.split(r"(?<=[.!?]\s)(?=[A-Z])|(?<=[.!?])$", part)
            segments.extend(s for s in sentences if s)  # Filter out empty strings

        segments_data = {}
        for i, segment in enumerate(segments):
            segment_id = str(uuid.uuid4())[:16]
            segments_data[segment_id] = {
                "id": segment_id,
                "text": segment,
                "idx": i,
            }

        self._segments = segments
        self._segments_data = segments_data

    def get_components(self, component_ids):
        # Create components with the selected segments
        components = {}
        current_time = int(time.time())

        # Sort component_ids by their index to find consecutive segments
        sorted_component_ids = sorted(
            component_ids, key=lambda x: self._segments_data[x]["idx"]
        )

        # Group consecutive segments
        current_group = []
        current_group_indices = []

        for i, component_id in enumerate(sorted_component_ids):
            current_idx = self._segments_data[component_id]["idx"]

            # Check if we should merge with previous group
            should_merge = False
            if current_group:
                last_idx = current_group_indices[-1]
                # Check if there are only newlines between last_idx and current_idx
                between_segments = self._segments[last_idx + 1 : current_idx]
                if all(seg.isspace() for seg in between_segments):
                    should_merge = True

            if not current_group or should_merge:
                # Add to current group if it's the first segment or separated by only newlines
                current_group.append(component_id)
                current_group_indices.append(current_idx)
            else:
                # Create component for the previous group
                if current_group:
                    first_idx = current_group_indices[0]
                    last_idx = current_group_indices[-1]

                    # Get all segments in the group, including newlines
                    group_segments = []
                    for idx in range(first_idx, last_idx + 1):
                        group_segments.append(self._segments[idx])

                    # Merge the text of all segments in the group
                    merged_text = "".join(group_segments)

                    # Get preceding and succeeding elements
                    preceding = "".join(self._segments[:first_idx])
                    # Only include segments up to the start of the next group
                    next_group_start = current_idx
                    succeeding = "".join(
                        self._segments[last_idx + 1 : next_group_start]
                    )

                    # Use the first component's ID for the merged component
                    components[current_group[0]] = {
                        "meta": {
                            "type": "text",
                            "time_added": current_time + first_idx,
                            "component_type": "email body",
                            "html_tag_index": None,
                            "isEmailSubject": False,
                            "component_params": {"custom_instructions": []},
                            "selected_element": "",
                            "preceding_element": preceding,
                            "succeeding_element": succeeding,
                        },
                        "text": merged_text,
                    }

                # Start a new group
                current_group = [component_id]
                current_group_indices = [current_idx]

        # Handle the last group
        if current_group:
            first_idx = current_group_indices[0]
            last_idx = current_group_indices[-1]

            # Get all segments in the group, including newlines
            group_segments = []
            for idx in range(first_idx, last_idx + 1):
                group_segments.append(self._segments[idx])

            # Merge the text of all segments in the group
            merged_text = "".join(group_segments)

            # Get preceding and succeeding elements
            preceding = "".join(self._segments[:first_idx])
            # For the last group, include all remaining segments
            succeeding = "".join(self._segments[last_idx + 1 :])

            # Use the first component's ID for the merged component
            components[current_group[0]] = {
                "meta": {
                    "type": "text",
                    "time_added": current_time + first_idx,
                    "component_type": "email body",
                    "html_tag_index": None,
                    "isEmailSubject": False,
                    "component_params": {"custom_instructions": []},
                    "selected_element": "",
                    "preceding_element": preceding,
                    "succeeding_element": succeeding,
                },
                "text": merged_text,
            }

        return components


class LandingPageTemplateSegmenter:
    def __init__(self, html_template_data: str) -> None:
        if not isinstance(html_template_data, str):
            raise ValueError("html_template_data must be a string")

        self.html_template_data = html_template_data

        self._segments = []
        self._segments_data = {}

        if html_template_data:
            self._process_html_template_data()

    @property
    def segments_to_select(self) -> List[str]:
        """Return list of segment IDs that can be selected (only segments with more than 4 words)"""
        return [
            seg_id
            for seg_id, seg_data in self._segments_data.items()
            if len(seg_data.get("text", "").split()) > 4
        ]

    @property
    def segments_data(self) -> Dict[str, Dict[str, Any]]:
        """Return dictionary of all segments data"""
        return self._segments_data

    def _process_html_template_data(self):
        """Parse HTML and extract tofu-element segments"""
        try:
            soup = BeautifulSoup(self.html_template_data, "html.parser")

            # Find all elements with tofu-element class
            tofu_elements = soup.find_all(class_="tofu-element")

            segments_data = {}

            for i, element in enumerate(tofu_elements):
                # Get or generate component ID
                component_id = element.get("data-tofu-id")
                if not component_id:
                    logging.error(
                        f"No tofu-element data-tofu-id found for element: {element}"
                    )
                    component_id = str(uuid.uuid4())[:16]

                # Extract text content
                text_content = element.get_text(strip=True)

                # Get HTML content of the element
                html_content = str(element)

                # Get tag name
                tag_name = element.name

                # Get preceding and succeeding elements for context
                preceding_element = self._get_preceding_context(element)
                succeeding_element = self._get_succeeding_context(element)

                # Store segment data
                segments_data[component_id] = {
                    "id": component_id,
                    "text": text_content,
                    "html": html_content,
                    "tag_name": tag_name,
                    "idx": i,
                    "preceding_element": preceding_element,
                    "succeeding_element": succeeding_element,
                    "attributes": dict(element.attrs) if element.attrs else {},
                }

            self._segments_data = segments_data

        except Exception as e:
            logging.exception(f"Error processing HTML template data: {e}")
            self._segments_data = {}

    def _get_preceding_context(self, element) -> str:
        """Get text content of elements that come before this element"""
        try:
            preceding_elements = []
            current = element.previous_sibling

            # Get up to 3 preceding text elements for context
            count = 0
            while current and count < 3:
                if hasattr(current, "get_text"):
                    text = current.get_text(strip=True)
                    if text:
                        preceding_elements.insert(0, text)
                        count += 1
                elif isinstance(current, str) and current.strip():
                    preceding_elements.insert(0, current.strip())
                    count += 1
                current = current.previous_sibling

            return " ".join(preceding_elements)
        except Exception:
            return ""

    def _get_succeeding_context(self, element) -> str:
        """Get text content of elements that come after this element"""
        try:
            succeeding_elements = []
            current = element.next_sibling

            # Get up to 3 succeeding text elements for context
            count = 0
            while current and count < 3:
                if hasattr(current, "get_text"):
                    text = current.get_text(strip=True)
                    if text:
                        succeeding_elements.append(text)
                        count += 1
                elif isinstance(current, str) and current.strip():
                    succeeding_elements.append(current.strip())
                    count += 1
                current = current.next_sibling

            return " ".join(succeeding_elements)
        except Exception:
            return ""

    def get_components(self, component_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Create components dictionary from selected component IDs"""
        components = {}
        current_time = int(time.time() * 1000)  # Convert to milliseconds

        for component_id in component_ids:
            if component_id not in self._segments_data:
                continue

            segment_data = self._segments_data[component_id]

            # Create HTML tag string (opening tag only)
            tag_name = segment_data["tag_name"]
            html_tag = f"<{tag_name}>"

            components[component_id] = {
                "meta": {
                    "type": "text",
                    "pageNum": None,
                    "html_tag": html_tag,
                    "numLines": None,
                    "time_added": current_time + segment_data["idx"],
                    "boundingBox": None,
                    "avgCharWidth": None,
                    "avgLineSpace": None,
                    "charCapacity": None,
                    "avgCharHeight": None,
                    "component_type": "unspecified",
                    "html_tag_index": segment_data["idx"],
                    "isEmailSubject": False,
                    "component_params": {"custom_instructions": []},
                    "selected_element": segment_data["html"],
                    "preceding_element": segment_data["preceding_element"],
                    "succeeding_element": segment_data["succeeding_element"],
                },
                "text": segment_data["text"],
            }

        return components

    def _determine_component_type(self, segment_data: Dict[str, Any]) -> str:
        """Determine the component type based on HTML tag and content"""
        tag_name = segment_data.get("tag_name", "").lower()
        text = segment_data.get("text", "").lower()

        # Map HTML tags to component types
        if tag_name in ["h1", "h2", "h3", "h4", "h5", "h6"]:
            return "heading"
        elif tag_name == "p":
            return "paragraph"
        elif tag_name == "button":
            return "button"
        elif tag_name == "a":
            return "link"
        elif tag_name in ["span", "div"]:
            if "button" in text or "click" in text:
                return "button"
            elif len(text) > 100:
                return "paragraph"
            else:
                return "text"
        elif tag_name in ["li", "ul", "ol"]:
            return "list"
        else:
            return "text"

    def get_all_segments_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all segments for display/selection purposes"""
        return {
            segment_id: {
                "id": segment_data["id"],
                "text": (
                    segment_data["text"][:100] + "..."
                    if len(segment_data["text"]) > 100
                    else segment_data["text"]
                ),
                "tag_name": segment_data["tag_name"],
                "component_type": self._determine_component_type(segment_data),
                "idx": segment_data["idx"],
            }
            for segment_id, segment_data in self._segments_data.items()
        }
