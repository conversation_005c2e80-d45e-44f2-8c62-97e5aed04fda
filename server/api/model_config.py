import copy

from .task_registry import GenerationGoal, TaskRegistry


class ModelConfig:
    def __init__(
        self,
        model_budget,
        model_params_list,
        generation_goal=None,
    ):
        self.model_budget = model_budget
        self.model_params_list = model_params_list
        self.generation_goal = generation_goal


class ModelParams:
    def __init__(self, model_name, model_params):
        self.model_name = model_name
        self.model_params = model_params

    def __repr__(self):
        return f"ModelParams(model_name={self.model_name}, model_params={self.model_params})"


class ModelConfigResolver:
    @staticmethod
    def resolve(generation_goal, **kwargs):
        try:
            task_registry = TaskRegistry.get_task_registry(generation_goal)
        except ValueError as e:
            raise ValueError(f"Invalid generation goal: {generation_goal}") from e
        foundation_model = kwargs.get("foundation_model", None)

        if foundation_model is None:
            # TODO: Check for internal user to use internal_default_model
            foundation_model = task_registry["default_model"]

        model_budget = task_registry["budget_limit"]
        # override budget_limit if model_config has it
        if "budget_limit" in task_registry.get("model_config", {}).get(
            foundation_model, {}
        ):
            model_budget = task_registry["model_config"][foundation_model][
                "budget_limit"
            ]

        model_params_list = [
            ModelConfigResolver.construct_model_params(
                task_registry, foundation_model, generation_goal, **kwargs
            ),
        ]
        for model_name in task_registry["model_fallback"][foundation_model]:
            model_params_list.append(
                ModelConfigResolver.construct_model_params(
                    task_registry, model_name, generation_goal, **kwargs
                )
            )
        return ModelConfig(model_budget, model_params_list, generation_goal)

    @staticmethod
    def construct_model_params(task_registry, model_name, generation_goal, **kwargs):
        model_params = copy.deepcopy(task_registry["model_config"][model_name])
        model_params.pop("budget_limit", None)
        if generation_goal in [
            GenerationGoal.GENERATION,
            GenerationGoal.GENERATION_REPURPOSING,
        ]:
            if "deepseek" not in model_name:
                model_params["n"] = kwargs.get("n", 1)
            if model_name == "mock":
                model_params["components"] = kwargs.get("components", {})
        return ModelParams(model_name, model_params)
