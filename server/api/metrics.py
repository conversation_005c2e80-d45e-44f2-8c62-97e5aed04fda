import logging
import os
from datetime import datetime, timedelta

import boto3
from botocore.config import Config
from django.utils import timezone


class APIMetricsMiddleware:
    """
    Middleware to track API metrics in CloudWatch.

    The middleware hooks into Django's request/response cycle:
    1. __call__ is called with the request
    2. get_response(request) processes the request through other middleware and views
    3. process_response handles the response and tracks metrics
    4. The response is returned back through the middleware chain
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.namespace = "Tofu/API"
        self._cloudwatch = None
        self._env = os.environ.get("TOFU_ENV", "development")
        self._allowed_envs = {"production", "development"}
        logging.info(f"APIMetricsMiddleware initialized in {self._env} environment")

    def _get_cloudwatch_client(self):
        if self._cloudwatch is None:
            try:
                if self._env in self._allowed_envs:
                    self._cloudwatch = boto3.client(
                        "cloudwatch",
                        region_name=os.environ.get("AWS_DEFAULT_REGION", "us-west-2"),
                        config=Config(retries={"max_attempts": 3, "mode": "standard"}),
                    )
                    return self._cloudwatch
                return None  # Not in allowed environments
            except Exception as e:
                logging.error(f"Failed to initialize CloudWatch client: {str(e)}")
                return None  # Failed to initialize
        return self._cloudwatch  # Return existing client

    def _get_request_size(self, request):
        """Safely get the request size in bytes."""
        try:
            # Try to get content length from headers first
            content_length = request.META.get("CONTENT_LENGTH")
            if content_length:
                return int(content_length)

            # For POST/PUT requests, try to get size from body
            if request.method in ("POST", "PUT", "PATCH"):
                if hasattr(request, "body"):
                    return len(request.body)
                elif hasattr(request, "_body"):
                    return len(request._body)

            # For GET requests, estimate size from query params
            if request.method == "GET" and request.GET:
                return len(request.GET.urlencode())

            return 0
        except (ValueError, TypeError, AttributeError) as e:
            logging.warning(f"Failed to get request size: {str(e)}")
            return 0

    def __call__(self, request):
        try:
            # Only allow metrics in production, development, and load_test
            if self._env not in self._allowed_envs:
                return self.get_response(request)

            # Skip metrics for healthcheck endpoint
            if request.path == "/":
                return self.get_response(request)

            # Get endpoint name early for tracking
            resolver_match = getattr(request, "resolver_match", None)
            if resolver_match and hasattr(resolver_match, "func"):
                endpoint = resolver_match.url_name or resolver_match.func.__name__
                if endpoint.startswith("api_"):
                    endpoint = endpoint[4:]

                # Track request size
                request_size = self._get_request_size(request)
                request.metrics_request_size = request_size  # Store for later use

        except Exception as e:
            logging.warning(f"Failed to check environment: {str(e)}")
            return self.get_response(request)

        # Start timer using Django's timezone
        request.start_time = timezone.now()

        # Process request through middleware chain and get response
        response = self.get_response(request)

        # Process response and track metrics
        self.process_response(request, response)

        return response

    def process_response(self, request, response):
        try:
            # Get view function name - fail early if we can't get it
            resolver_match = getattr(request, "resolver_match", None)
            if not resolver_match or not hasattr(resolver_match, "func"):
                return response

            # Get the endpoint name from the view function
            endpoint = resolver_match.url_name or resolver_match.func.__name__
            if endpoint.startswith("api_"):
                endpoint = endpoint[4:]  # Remove api_ prefix if present

            # Calculate duration using Django's timezone
            duration = (timezone.now() - request.start_time).total_seconds() * 1000

            # Base dimensions for all metrics
            base_dimensions = [
                {
                    "Name": "Endpoint",
                    "Value": endpoint,
                },
                {"Name": "Method", "Value": request.method},
                {"Name": "Environment", "Value": self._env},
                {"Name": "StatusCode", "Value": str(response.status_code)},
            ]

            # Track all metrics
            try:
                # Prepare all metrics data
                metric_data = []

                # Track request count
                metric_data.append(
                    {
                        "MetricName": "RequestCount",
                        "Value": 1,
                        "Unit": "Count",
                        "Dimensions": base_dimensions,
                    }
                )

                # Track response time
                metric_data.append(
                    {
                        "MetricName": "ResponseTime",
                        "Value": duration,
                        "Unit": "Milliseconds",
                        "Dimensions": base_dimensions,
                    }
                )

                # Track request size if available
                request_size = getattr(request, "metrics_request_size", 0)
                metric_data.append(
                    {
                        "MetricName": "RequestSize",
                        "Value": request_size,
                        "Unit": "Bytes",
                        "Dimensions": base_dimensions,
                    }
                )

                # Track error rates
                if 400 <= response.status_code < 500:
                    metric_data.append(
                        {
                            "MetricName": "ClientErrorCount",
                            "Value": 1,
                            "Unit": "Count",
                            "Dimensions": base_dimensions,
                        }
                    )
                elif response.status_code >= 500:
                    metric_data.append(
                        {
                            "MetricName": "ServerErrorCount",
                            "Value": 1,
                            "Unit": "Count",
                            "Dimensions": base_dimensions,
                        }
                    )

                # Track success rate
                if 200 <= response.status_code < 300:
                    metric_data.append(
                        {
                            "MetricName": "SuccessCount",
                            "Value": 1,
                            "Unit": "Count",
                            "Dimensions": base_dimensions,
                        }
                    )

                # Send all metrics in a single call
                cloudwatch = self._get_cloudwatch_client()
                if cloudwatch is not None:
                    cloudwatch.put_metric_data(
                        Namespace=self.namespace, MetricData=metric_data
                    )

            except Exception as e:
                logging.exception(f"Failed to send metrics to CloudWatch: {str(e)}")

        except Exception as e:
            logging.exception(f"Failed to collect metrics: {str(e)}")

        return response
