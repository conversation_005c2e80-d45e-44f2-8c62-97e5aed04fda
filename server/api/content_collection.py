import logging
import tempfile
import traceback
import uuid

from .content_gen.content_collection_plan_gen import ContentCollectionPlanGen
from .models import AssetInfo, AssetInfoGroup, ContentGroup
from .playbook import PlaybookHandler
from .s3_utils import upload_file


class ContentCollection:
    def __init__(self, content_collection_id, content_collection_map={}):
        self.content_collection_id = content_collection_id
        self.content_collection_map = content_collection_map
        if not self.content_collection_map:
            self.content_collection_map = {}
            collection_content_groups = ContentGroup.objects.filter(
                content_group_params__content_collection__id=content_collection_id
            )
            for content_group in collection_content_groups:
                self.content_collection_map[content_group.id] = (
                    content_group.content_group_params.get("content_collection", {})
                )


class ContentCollectionHandler:
    def __init__(self, gen_env, content_collection=None):
        self.playbook = gen_env._data_wrapper.playbook
        self.playbook_handler = PlaybookHandler.load_from_db_instance(self.playbook)
        self.foundation_model = gen_env._gen_settings._foundation_model
        self._gen_env = gen_env
        self.content_collection = content_collection

    def update_content_group_collections_param(
        self,
        collection_id,
    ):
        self.content_collection = ContentCollection(collection_id)

        content_collection_asset = self.build_content_collection_asset()

        # save content_collection_instructions to content_group_params of every content_group in this content_collection
        for content_group_id in self.content_collection.content_collection_map:
            try:
                collection_content_group = ContentGroup.objects.get(id=content_group_id)
            except ContentGroup.DoesNotExist:
                logging.error(
                    f"ContentGroup with id {content_group_id} does not exist."
                )
                continue
            collection_content_group_name = collection_content_group.content_group_name

            content_collection_instructions = [
                {
                    "assets": content_collection_asset,
                    "instruction": f"Follow the directions from this plan for the content: {collection_content_group_name}",
                }
            ]
            collection_content_group.content_group_params["content_collection"][
                "content_collection_instructions"
            ] = content_collection_instructions
            collection_content_group.save(update_fields=["content_group_params"])
            self.content_collection.content_collection_map[content_group_id][
                "content_collection_instructions"
            ] = content_collection_instructions

    def get_content_collection_instructions(self, content_group_id):
        content_group = ContentGroup.objects.get(
            id=content_group_id
        )  # refetch to prevent errors
        if content_group.content_group_params.get("content_collection", None):
            content_collection_instructions = content_group.content_group_params.get(
                "content_collection", {}
            ).get("content_collection_instructions", [])
            if content_collection_instructions:
                return content_collection_instructions
            else:
                collection_id = content_group.content_group_params.get(
                    "content_collection", {}
                ).get("id", None)
                if collection_id:
                    self.update_content_group_collections_param(collection_id)
                    return self.content_collection.content_collection_map.get(
                        content_group.id, {}
                    ).get("content_collection_instructions", [])
        return []

    def build_content_collection_asset(self):
        plan_gen_env = self._gen_env.copy()
        plan_gen_env._gen_settings._content_collection_plan_gen = True
        generator = ContentCollectionPlanGen(
            playbook_handler=self.playbook_handler,
            content_collection=self.content_collection,
            gen_env=plan_gen_env,
        )

        output = generator.gen()

        # save output as an asset

        # Use temp dir and dump text to file.
        file_name = f"content_collection_plan-{self.content_collection.content_collection_id}.txt"
        with tempfile.TemporaryDirectory() as temp_dir:
            file_location = f"{temp_dir}/{file_name}"
            with open(file_location, "w") as f:
                f.write(output)

            # upload file to s3
            file_type = "text/plain"
            s3_presigned_path = upload_file(file_location, file_type)
            s3_file_value = {
                "s3_bucket": "tofu-uploaded-files",
                "s3_filename": file_name,
                "mime_file_type": file_type,
                "original_filename": file_name,
                "s3_presigned_path": s3_presigned_path,
            }

        id = str(uuid.uuid4())
        asset_group_key = "[TOFU Internal] Content Collection Plans"
        asset_info_group = AssetInfoGroup.objects.filter(
            asset_info_group_key=asset_group_key, playbook=self.playbook
        ).first()
        if not asset_info_group:
            asset_info_group = AssetInfoGroup.objects.create(
                asset_info_group_key=asset_group_key,
                playbook=self.playbook,
                meta={"position": 0},
            )

        asset_key = f"Plan {self.content_collection.content_collection_id}"
        try:
            asset_info = AssetInfo.objects.filter(
                asset_key=asset_key,
                asset_info_group=asset_info_group,
            ).first()

            docs = {
                id: {
                    "id": id,
                    "type": "file",
                    "value": s3_file_value,
                    "position": 0,
                }
            }
            if asset_info:
                # only for backwards compatibility
                asset_info.docs = docs
                asset_info.save(update_fields=["docs"])
            else:
                asset_info = AssetInfo.objects.create(
                    asset_info_group=asset_info_group,
                    asset_key=asset_key,
                    docs=docs,
                    meta={"position": 0, "type": "content_collection_plan"},
                )
        except Exception as e:
            logging.error(
                f"Error creating content collection asset: {e}\n{traceback.format_exc()}"
            )

        content_collection_asset = {asset_group_key: asset_key}
        return content_collection_asset
