import copy
import functools
import json
import logging
import os
import re
import signal
import tempfile
import time
import traceback
import uuid
from difflib import SequenceMatcher
from functools import lru_cache, wraps
from itertools import product
from typing import Any, Dict, List
from urllib.parse import parse_qs, urlparse

import boto3
import brotli
import nltk
import psutil
import tiktoken
from botocore.exceptions import ClientError
from bs4 import BeautifulSoup
from django.core.cache import cache
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from json_repair import repair_json
from langchain.globals import set_llm_cache
from langchain_community.cache import SQLAlchemyCache
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from nltk.tokenize import sent_tokenize
from sqlalchemy import create_engine

from .logger import tofu_axiom_logger
from .models import (
    Content,
    ContentGroup,
    ContentVariation,
    FulltextLLMCacheAlchemy,
    TofuUser,
)
from .thread_locals import get_current_user

encoding = tiktoken.get_encoding("cl100k_base")


def get_token_count(text: str) -> int:
    return len(encoding.encode(text))


"""
    Given a text to replace with prev gen variations,
    replace the substring of text with the text variation if 
    the substring is similar to the orig_text of each prev gen variation
"""


def replace_variation(text, prev_gen_variations):
    text = clean_html(text)
    for _, variation in prev_gen_variations.items():
        orig_text = variation["orig_text"]
        text_variation = variation["text_variation"]
        s = SequenceMatcher(None, text, orig_text)
        (i, j, k) = s.find_longest_match(0, len(text), 0, len(orig_text))
        if k > 0.8 * len(orig_text):
            text = text[:i] + text_variation + text[i + k :]
    return text


def clean_html(text):
    try:
        soup = BeautifulSoup(text, "html.parser")
        text = soup.get_text(separator=" ").strip()
        return text
    except Exception as e:
        logging.exception(f"Error cleaning html {text}: {e}")
        return text


def rewrite_length_limit(
    model_caller,
    original_llm_inputs,
    generated_content,
    expected_length,
):
    llm_inputs = copy.deepcopy(original_llm_inputs)
    llm_inputs.extend(
        [
            AIMessage(content=generated_content),
            HumanMessage(
                content=f"Rewrite the above generated text to be around {expected_length} words long. Only output the rewritten text. Do not include the previously generated text."
            ),
        ]
    )
    result = model_caller.get_results_with_fallback(llm_inputs)
    if not isinstance(result, list):
        raise ValueError("Result is not a list")
    if not result[0].text:
        raise ValueError("Result is empty")
    return result[0].text


def measure_latency(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        latency = end_time - start_time

        # Convert args and kwargs to a readable format
        args_str = ", ".join(map(str, args))
        kwargs_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())

        # Log the latency using Axiom logging
        tofu_axiom_logger.log_axiom(
            event_type="latency",
            func=f"{func.__qualname__}",
            args=args_str,
            kwargs=kwargs_str,
            latency=latency,
        )

        return result

    return wrapper


def get_first_n_tokens(text: str, n: int) -> str:
    tokens = encoding.encode(text)[:n]
    return encoding.decode(tokens)


def count_sentences(text: str) -> int:
    return len(sent_tokenize(text))


def is_google_slides_url(url: str) -> bool:
    if not url:
        return False
    return "docs.google.com/presentation/d/" in url.lower()


def is_pptx_s3_file(s3_filename: str) -> bool:
    if not s3_filename:
        return False
    return s3_filename.lower().endswith((".pptx", ".ppt"))


def is_s3_url(url):
    return url.startswith("/api/web/storage/s3-presigned-url")


def parse_s3_presigned_url(url):
    # Parse the URL
    parsed_url = urlparse(url)

    # Extract query parameters
    query_params = parse_qs(parsed_url.query)
    if (
        "file" not in query_params
        or "fileType" not in query_params
        or "directory" not in query_params
    ):
        raise Exception(f"Invalid S3 presigned URL: {url}")

    # Get the file name and S3 bucket name from the query parameters
    source_file_name = query_params["file"][0]
    file_type = query_params["fileType"][0]
    s3_bucket = query_params["directory"][0]
    return source_file_name, file_type, s3_bucket


def copy_s3_file(source):
    source_file_name, file_type, s3_bucket = parse_s3_presigned_url(source)

    destination = f"{uuid.uuid4()}-{source_file_name}"

    # Initialize the S3 client
    s3 = boto3.client("s3")
    try:
        # Copy the object
        s3.copy_object(
            Bucket=s3_bucket,
            CopySource={"Bucket": s3_bucket, "Key": source_file_name},
            Key=destination,
        )
        destination_presigned_url = f"/api/web/storage/s3-presigned-url?file={destination}&fileType={file_type}&directory={s3_bucket}"
    except Exception as e:
        logging.error(f"An error occurred for s3 copy: {e}")
        raise e
    return destination_presigned_url


def get_s3_file(source):
    source_file_name, file_type, s3_bucket = parse_s3_presigned_url(source)
    if file_type != "text/html" and file_type != "application/json":
        logging.error(
            f"File type {file_type} is not tested in get_s3_file and it may fail"
        )

    s3 = boto3.resource("s3")

    obj = s3.Object(s3_bucket, source_file_name)
    file_content = obj.get()["Body"].read()

    try:
        file_content = file_content.decode("utf-8")
    except Exception as e:
        try:
            with TempS3File(s3_bucket, source_file_name) as temp_file_name:
                with open(temp_file_name, "rb") as compressed_file:
                    file_content = compressed_file.read()
                    decompressed_data = brotli.decompress(file_content)
                    file_content = decompressed_data.decode("utf-8")
        except Exception as e:
            logging.error(
                f"An error occurred while decompressing the file with brotli: {e}"
            )
    return file_content


def download_s3_file_and_upload_to_gdrive(s3_bucket, s3_filename):
    # Step 0: Check if file exists in GDrive
    key = f"{s3_bucket}/{s3_filename}"
    try:
        cache_data = cache.get(key)
    except Exception as e:
        logging.exception(f"debug: Failed to get gdrive link for {key}: {e}")
        cache_data = None
    if cache_data:
        return cache_data
    # Step 1: Download file from S3

    with tempfile.TemporaryDirectory() as temp_dir:
        file_path = f"{temp_dir}/{s3_filename}"
        s3 = boto3.client("s3")
        s3.download_file(s3_bucket, s3_filename, file_path)

        # Step 2: Upload file to Google Drive
        SCOPES = [
            "https://www.googleapis.com/auth/drive",
        ]
        creds = service_account.Credentials.from_service_account_file(
            ".credentials/google_service_account_key.json", scopes=SCOPES
        )
        drive_service = build("drive", "v3", credentials=creds)
        file_metadata = {"name": file_path}
        media = MediaFileUpload(file_path, resumable=True)
        file = (
            drive_service.files()
            .create(body=file_metadata, media_body=media, fields="id")
            .execute()
        )

        # Step 3: Get the sharable link
        permission = {"type": "anyone", "role": "reader"}
        drive_service.permissions().create(
            fileId=file["id"], body=permission, fields="id"
        ).execute()

        gdrive_link = f"https://drive.google.com/file/d/{file['id']}/view"

    # Step 4: Cache the link
    cache.set(key, gdrive_link, 60 * 60 * 24 * 7)
    return gdrive_link


def process_html(input_html: str):
    unique_id_counter = 0
    mapping = {}
    unique_id_key = "<id-{unique_id_counter}>"
    output_html = ""
    text_end = 0

    body_start = input_html.find("<body")
    if body_start == -1:
        logging.error("Cannot find <body> tag in the html")
    else:
        text_end = input_html.find(">", body_start) + 1
        mapping[unique_id_key.format(unique_id_counter=unique_id_counter)] = input_html[
            :text_end
        ]
        output_html += unique_id_key.format(unique_id_counter=unique_id_counter)
        unique_id_counter += 1
        input_html = input_html[text_end:]

    while "data-tofu-id" in input_html:
        pointer = input_html.find("data-tofu-id")
        if pointer == -1:
            break
        text_start = input_html.find(">", pointer) + 1
        mapping[unique_id_key.format(unique_id_counter=unique_id_counter)] = input_html[
            :text_start
        ]
        output_html += unique_id_key.format(unique_id_counter=unique_id_counter)
        unique_id_counter += 1
        text_end = input_html.find("</", text_start)
        text_chunk = input_html[text_start:text_end]
        while "<" in text_chunk:
            span_begin = text_chunk.find("<")
            output_html += text_chunk[:span_begin]
            span_end = text_chunk.find(">", span_begin) + 1
            text_to_replace = text_chunk[span_begin:span_end]
            mapping[unique_id_key.format(unique_id_counter=unique_id_counter)] = (
                text_to_replace
            )
            unique_id_counter += 1
            text_chunk = text_chunk[span_end:]
        output_html += text_chunk
        input_html = input_html[text_end:]

    end = input_html
    mapping[unique_id_key.format(unique_id_counter=unique_id_counter)] = end
    output_html += unique_id_key.format(unique_id_counter=unique_id_counter)

    return output_html, mapping


def expand_modified_html(modified_html, mapping):
    expanded_html = modified_html
    for key, value in mapping.items():
        expanded_html = expanded_html.replace(key, value)
    return expanded_html


def get_tofu_components(html):
    soup = BeautifulSoup(html, "html.parser")

    elements_with_data_tofu_id = soup.find_all(
        attrs={"data-tofu-id": re.compile(r".*")}
    )

    id_to_content = [
        (element["data-tofu-id"], element.get_text())
        for element in elements_with_data_tofu_id
    ]

    return id_to_content


def retrieve_variations_from_html(expanded_modified_html, runtime, request_id):
    variations = {}

    id_to_content = get_tofu_components(expanded_modified_html)
    counter = 0
    for component_id, text in id_to_content:
        generated_variations = [
            {
                "text": text,
                "meta": {
                    "runtime_seconds": runtime,
                    "gen_runtime": runtime,
                    "postprocess_runtime": 0,
                    "request_id": request_id,
                },
            }
        ]
        variations[component_id] = {}
        variations[component_id]["meta"] = {}
        variations[component_id]["meta"]["time_added"] = int(time.time()) + counter
        variations[component_id]["meta"]["current_variation_index"] = 0
        variations[component_id]["meta"]["variations"] = generated_variations
        variations[component_id]["meta"]["current_version"] = {
            "text": generated_variations[0]["text"],
            "request_id": request_id,
        }
        counter += 1

    return variations


def postprocess_repurpose_html(outputs, mapping, orig_components, request_id):
    variations = copy.deepcopy(orig_components)
    for output in outputs:
        output_html = output["text"]
        runtime = output["meta"]["runtime_seconds"]
        expanded_modified_html = expand_modified_html(output_html, mapping)
        generated_variation = retrieve_variations_from_html(
            expanded_modified_html, runtime, request_id
        )
        for component_id, variation in generated_variation.items():
            if component_id in variations:
                if not "variations" in variations[component_id]["meta"]:
                    variations[component_id]["meta"]["variations"] = variation["meta"][
                        "variations"
                    ]
                else:
                    variations[component_id]["meta"]["variations"].extend(
                        variation["meta"]["variations"]
                    )
                if not "current_variation_index" in variations[component_id]["meta"]:
                    variations[component_id]["meta"]["current_variation_index"] = 0
                if not "current_version" in variations[component_id]["meta"]:
                    variations[component_id]["meta"]["current_version"] = variations[
                        component_id
                    ]["meta"]["variations"][0]
                if not "time_added" in variations[component_id]["meta"]:
                    variations[component_id]["meta"]["time_added"] = int(time.time())

    return variations


def dict_to_tuple(d):
    t = [(k, v) for k, v in d.items()]
    t.sort()
    return tuple(t)


def single_dict_to_tuple(d):
    # single key and single value
    if not isinstance(d, dict):
        raise ValueError(f"Input must be a dictionary: {d}")
    if len(d) != 1:
        raise ValueError(f"Dictionary must have exactly one key-value pair: {d}")
    key, value = list(d.items())[0]
    return (key, value)


def iter_campaign_target_dict(target_params, is_targets_concat):
    if not target_params:
        return []

    target_params_with_value_list = {
        key: value if isinstance(value, list) else [value]
        for key, value in target_params.items()
    }

    if is_targets_concat:
        # Concatenation mode: yield one dict per key-value pair
        for key, values in target_params_with_value_list.items():
            for value in values:
                yield {key: value}
    else:
        # Combination mode: yield all possible combinations
        combinations = list(product(*target_params_with_value_list.values()))
        for combination in combinations:
            yield {
                key: value
                for key, value in zip(target_params_with_value_list.keys(), combination)
            }


def iter_campaign_target_params(target_params, is_targets_concat):
    if not target_params:
        return []
    if isinstance(target_params, list) and all(
        isinstance(item, tuple) for item in target_params
    ):
        for target_pair in target_params:
            l1_key, l2_key = target_pair
            yield {l1_key: l2_key}
    else:
        for target_pair in target_params:
            for iter in iter_campaign_target_dict(target_pair, is_targets_concat):
                yield iter


def merge_dict_with_value_list(dict1, dict2, create_new=False, add_repeated=False):
    if create_new:
        dict1 = copy.deepcopy(dict1)
    for key, value in dict2.items():
        if key not in dict1:
            dict1[key] = []
        if not isinstance(value, list):
            dict1[key].append(value)
        elif add_repeated:
            dict1[key].extend(value)
        else:
            for v in value:
                if v not in dict1[key]:
                    dict1[key].append(v)
    return dict1


def create_presigned_url(bucket_name, object_name, expiration=60 * 60 * 24 * 7):
    """Generate a presigned URL to share an S3 object

    :param bucket_name: string
    :param object_name: string
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Presigned URL as string. If error, returns None.
    """

    # Generate a presigned URL for the S3 object
    s3_client = boto3.client("s3")
    try:
        response = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket_name, "Key": object_name},
            ExpiresIn=expiration,
        )
    except ClientError as e:
        logging.error(e)
        return None

    # The response contains the presigned URL
    return response


def clean_personalization_token(input_str):
    if not input_str:
        return input_str
    try:
        pattern = r"\{\{(.*?)\}\}"

        def replacement(match):
            # Extract the content within the curly braces
            inner_content = match.group(1)
            # Check the length of the content
            if len(inner_content) < 50:
                return ""
            else:
                # Return the original matched text if the condition is not met
                return match.group(0)

        cleaned_text = re.sub(pattern, replacement, input_str)
        return cleaned_text
    except Exception as e:
        logging.exception(f"Error cleaning personalization token {input_str}: {e}")
        return input_str


def edit_distance(s1, s2):
    # https://www.nltk.org/api/nltk.metrics.distance.html

    return nltk.edit_distance(s1, s2)


def fuzzy_match_json_key(key, json_object):
    # Do fuzzy matching to find the closest key
    key_len = len(key)
    closest_key = None
    closest_edit_distance = key_len // 2

    for json_key in json_object:
        num_diff = edit_distance(key, json_key)

        # Check if this key is closer based on edit distance
        if num_diff <= closest_edit_distance:
            closest_edit_distance = num_diff
            closest_key = json_key
        # For long strings, also consider first third matching, but only if it's closer
        elif (
            key_len >= 10
            and key[: key_len // 3] == json_key[: key_len // 3]
            and (closest_key is None or num_diff < closest_edit_distance)
        ):
            closest_key = json_key
            closest_edit_distance = num_diff

    return closest_key


def check_generations_are_valid_json(generations, model_name):
    try:
        outputs = [strip_for_json(generation.text) for generation in generations]
        for output in outputs:
            try:
                json.loads(output)
            except json.JSONDecodeError as e:
                # do a replace for \n
                output = output.replace("\n", "\\n")
                json.loads(output)
    except Exception as e:
        logging.error(
            f"An error occurred while checking if generations are valid json: {e} for {model_name} with {outputs}"
        )
        return False
    return True


# In order to handle non-unique target names when duplicate records are being imported from HubSpot, etc,
# we might have to add a suffix `(string)` to the target name to make it unique,
# but we want to keep the target name clean during generation so the model doesn't hallucinate.
# Hence here we remove the `(string)` suffix whenever possible
def get_display_l2_key(l2_key, remove_suffix=False):
    display_key = l2_key
    if remove_suffix:
        pattern = r"\([^\)]*\)$"
        display_key = re.sub(pattern, "", l2_key)
    return display_key.strip()


# usage:
# with TempS3File(bucket_name, object_key) as temp_file_name:
#     # do something with the file
class TempS3File:
    def __init__(self, bucket_name, object_key):
        self.bucket_name = bucket_name
        self.object_key = object_key
        self.temp_file = None
        self.temp_file_name = None

    def __enter__(self):
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file_name = self.temp_file.name
        s3 = boto3.resource("s3")
        s3.Bucket(self.bucket_name).download_file(self.object_key, self.temp_file_name)
        self.temp_file.close()  # It's important to close the file before using it elsewhere
        return self.temp_file_name

    def __exit__(self, exc_type, exc_val, exc_tb):
        os.remove(self.temp_file_name)


# usage: when the generated content has leading message before json output
#   we need to strip the leading message before parsing the json
def strip_for_json(json_str):
    if not json_str.startswith("{") and not json_str.startswith("["):
        json_str = json_str[json_str.find("{") :]
    if not json_str.endswith("}") and not json_str.endswith("]"):
        json_str = json_str[: json_str.rfind("}") + 1]

    # for every string inside the quotes, replace \n with \\n.
    # Use a more precise pattern that won't affect Unicode characters
    json_str = re.sub(
        r'("(?:[^"\\]|\\.)*")',
        lambda m: m.group(0).replace("\n", "\\n"),
        json_str,
        flags=re.S,
    )
    # replace {{ with { and }} with }
    json_str = re.sub(r"\{\{(.*?)\}\}", r"{\1}", json_str)
    try:
        json.loads(json_str)
        return json_str
    except Exception as e:
        # check if the json_str is a list of json objects, if so combine them and return the combined JSON string.
        combined_dict = {}

        lines = json_str.strip().split("\n")

        for line in lines:
            try:
                # Attempt to parse each line as a JSON object
                json_obj = json.loads(line)
                if isinstance(json_obj, dict):
                    # Merge the parsed JSON object into the combined dictionary
                    combined_dict.update(json_obj)
            except json.JSONDecodeError:
                pass

        if combined_dict:
            return json.dumps(combined_dict)

    try:
        # apply skip_json_loads since we know it fails to load
        fixed_json_str = repair_json(json_str, skip_json_loads=True)
        return fixed_json_str
    except Exception as e:
        pass

    # if we reach here, which means all efforts above don't work
    logging.error(f"Fail to find a way to fix json: {json_str}")
    return json_str


def _get_text_content(content):
    """Helper function to extract text content from either a string or a list of dicts."""
    if isinstance(content, list):
        if not content:
            return ""
        if not isinstance(content[0], dict):
            return str(content[0]) if content[0] else ""
        return content[0].get("text", "")
    return content


# usage: given gemini and claude models' restriction,
# merge adjacent HumanMessage or AIMessage
# when no_system_message is True, convert SystemMessage to HumanMessage
def alter_llm_messages(llm_inputs, no_system_message=True):
    if not llm_inputs:
        return []

    results = []
    prev_message_buff = llm_inputs[0].content

    prev_msg_type = type(llm_inputs[0])

    for i in range(1, len(llm_inputs)):
        cur_message = llm_inputs[i]

        message_content = cur_message.content

        if isinstance(message_content, list):  # image case.
            if not message_content:
                message_content = [{"text": "continue"}]
                cur_message.content = message_content
            elif not isinstance(message_content[0], dict):
                message_content[0] = {
                    "text": (
                        str(message_content[0]) if message_content[0] else "continue"
                    )
                }
                cur_message.content = message_content
            else:
                message_content_text = message_content[0].get("text", "")
                if not message_content_text.strip():
                    cur_message.content[0]["text"] = "continue"

        else:
            if not cur_message.content.strip():
                cur_message.content = "continue"
        cur_msg_type = type(cur_message)
        if no_system_message and isinstance(cur_message, SystemMessage):
            cur_msg_type = HumanMessage

        if cur_msg_type == prev_msg_type:  # append
            if isinstance(prev_message_buff, list):
                # ensure there's at least one dict to read/write
                if not prev_message_buff:
                    prev_message_buff = [{"text": ""}]

                prev_message_buff_text = prev_message_buff[0].get("text", "")
                cur_text = _get_text_content(cur_message.content)

                # coerce to str to avoid TypeError on concatenation
                if not isinstance(prev_message_buff_text, str):
                    prev_message_buff_text = (
                        str(prev_message_buff_text)
                        if prev_message_buff_text is not None
                        else ""
                    )
                if not isinstance(cur_text, str):
                    cur_text = str(cur_text) if cur_text is not None else ""

                prev_message_buff[0]["text"] = (
                    prev_message_buff_text + "\n\n\n" + cur_text
                )
                # append other message content types like image_url
                if isinstance(cur_message.content, list):
                    for content in cur_message.content:
                        if content.get("type") == "image_url":
                            prev_message_buff.append(content)

            else:
                cur_text = _get_text_content(cur_message.content)

                # coerce to str to avoid TypeError on concatenation
                if not isinstance(prev_message_buff, str):
                    prev_message_buff = (
                        str(prev_message_buff) if prev_message_buff is not None else ""
                    )
                if not isinstance(cur_text, str):
                    cur_text = str(cur_text) if cur_text is not None else ""

                prev_message_buff += "\n\n\n" + cur_text

                # check if cur_message.content is a list
                if isinstance(cur_message.content, list):
                    # make prev_message_buff a list
                    prev_message_buff = [{"type": "text", "text": prev_message_buff}]
                    # append other message content types like image_url
                    for content in cur_message.content:
                        if content.get("type") == "image_url":
                            prev_message_buff.append(content)
        else:
            results.append(prev_msg_type(prev_message_buff))

            prev_message_buff = cur_message.content
            prev_msg_type = cur_msg_type

    if prev_message_buff:
        results.append(prev_msg_type(prev_message_buff))

    if no_system_message:
        # claude deepseek-reasoner and gemini constraint: only first can be system message
        for i in range(1, len(results)):
            if isinstance(results[i], SystemMessage):
                results[i] = HumanMessage(results[i].content)

        # claude constraint: first non system can only be human message
        first_index = 1 if isinstance(results[0], SystemMessage) else 0
        if isinstance(results[first_index], AIMessage):
            results.insert(first_index, HumanMessage(content="Let's start"))
    return results


# usage: given claude's message api's requirement, convert langchain schema messages to json format
def convert_messages_to_prompt_for_claude(
    llm_inputs, alter_message=True, convert_message=False
):
    if alter_message:
        llm_inputs = alter_llm_messages(llm_inputs, no_system_message=True)

    if not convert_message:
        return llm_inputs

    # convert_message is for rewrite case. We shall unify them later

    json_messages = []
    for message in llm_inputs:  # List[BaseMessage]
        role = "system"
        if isinstance(message, SystemMessage):
            role = "system"
        elif isinstance(message, HumanMessage):
            role = "user"
        elif isinstance(message, AIMessage):
            role = "assistant"
        else:
            logging.error(f"Unknown message type {type(message)}")
            continue

        json_messages.append({"role": role, "content": message.content})

    return json_messages


def fix_claude_outputs(outputs):
    # For a list of strings, remove leading line like "Here is ...:" or "Here's ...:"

    for i in range(len(outputs)):
        outputs[i] = re.sub(r"^\s*(Here is|Here's) .*?:", "", outputs[i])
    return outputs


def fix_claude_outputs_for_generations(generations):
    for i in range(len(generations)):
        generations[i].text = re.sub(
            r"^\s*(Here is|Here's) .*?:", "", generations[i].text
        )
    return generations


def get_published_image_url(image_url):
    publish_bucket = os.environ["S3_PUBLISH_BUCKET"]
    if not publish_bucket:
        raise Exception("S3_PUBLISH_BUCKET is not set")

    s3_filename, file_type, s3_bucket = parse_s3_presigned_url(image_url)

    uploaded_s3_filename = f"image-published/{s3_filename}"

    published_image_url = (
        f"https://{publish_bucket}.s3.amazonaws.com/{uploaded_s3_filename}"
    )
    return published_image_url, publish_bucket, uploaded_s3_filename


def get_published_cdn_image_url(image_url):
    s3_filename, file_type, s3_bucket = parse_s3_presigned_url(image_url)

    uploaded_s3_filename = f"image-published/{s3_filename}"

    published_image_url = f"https://dbkhip2mkef50.cloudfront.net/{uploaded_s3_filename}"
    return published_image_url


def publish_image(image_url):
    published_image_url, publish_bucket, uploaded_s3_filename = get_published_image_url(
        image_url
    )

    # image_url is a s3 presigned url
    s3 = boto3.client("s3")
    s3_filename, file_type, s3_bucket = parse_s3_presigned_url(image_url)

    # check if exists
    try:
        if s3.head_object(Bucket=publish_bucket, Key=uploaded_s3_filename):
            return published_image_url
    except Exception as e:
        logging.error(f"An error occurred while checking if image exists: {e}")

    s3.copy_object(
        Bucket=publish_bucket,
        CopySource={"Bucket": s3_bucket, "Key": s3_filename},
        Key=uploaded_s3_filename,
    )

    return published_image_url


def find_common_values(dictionary):
    if not dictionary:
        return set()
    common_values = set(dictionary[next(iter(dictionary))])
    for key in dictionary:
        common_values = common_values.intersection(dictionary[key])

    return common_values


def get_tofu_sqlalchemy_cache():
    connection_string = (
        f"postgresql://{os.environ.get('DB_USER')}:{os.environ.get('DB_PASSWORD')}@"
        f"{os.environ.get('DB_HOST')}:{os.environ.get('DB_PORT')}/{os.environ.get('DB_NAME')}"
    )
    engine = create_engine(connection_string)
    return SQLAlchemyCache(engine, FulltextLLMCacheAlchemy)


@lru_cache(maxsize=None)
def get_tofu_llm_cache():
    try:
        return get_tofu_sqlalchemy_cache()
    except Exception as e:
        logging.error(
            f"An error occurred while getting tofu llm cache: {e}\n{traceback.format_exc()}"
        )
        return None


# Function to handle the timeout
def handle_timeout(signum, frame):
    raise TimeoutError("Function timed out")


# Decorator to set the timeout for the main thread, it doesn't work for the child threads
# The function will raise a TimeoutError if it takes longer than the specified number of seconds
def main_thread_timeout(seconds):
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Set the signal handler and a timer
            signal.signal(signal.SIGALRM, handle_timeout)
            signal.alarm(seconds)
            try:
                result = func(*args, **kwargs)
            finally:
                # Disable the alarm
                signal.alarm(0)
            return result

        return wrapper

    return decorator


def get_curr_selected_variation_json(content_id, only_reviewed=True):
    # Get content
    try:
        content = Content.objects.get(id=content_id)
    except Content.DoesNotExist:
        logging.error(f"Content with id {content_id} does not exist")
        return None
    # Get variations for this content
    content_variation = ContentVariation.objects.filter(
        content=content
    ).first()  # Assume only one?
    if content_variation:
        # check if content_variation.params has "is_reviewed"
        if only_reviewed and not content_variation.params.get("is_reviewed", False):
            return None
        variations = content_variation.variations
        json_dict = {}
        for key in variations:
            value = variations.get(key, {})
            current_version = value.get("meta").get("current_version", {})
            text = current_version.get("text", "")
            if text:
                json_dict[key] = {"text": text, "word count": len(text.split())}
        return json_dict
    return None


def get_curr_selected_variation_single_component(
    content_id, component_id, only_reviewed=True, cache_enabled=True
):
    all_components_variations = None
    if cache_enabled:
        cache_key = f"get_curr_selected_variation_single_component_{content_id}"
        try:
            all_components_variations = cache.get(cache_key)
        except Exception as e:
            logging.exception(
                f"debug: Failed to get curr selected variation single component for {content_id}: {e}"
            )
    if not all_components_variations:
        all_components_variations = get_curr_selected_variation_json(
            content_id, only_reviewed
        )
        if cache_enabled:
            cache.set(cache_key, all_components_variations, 60 * 60 * 24)
    if all_components_variations:
        return all_components_variations.get(component_id, {}).get("text", "")
    return None


def replace_with_content_variations(components, content_id, cache_enabled=True):
    # components can be null
    if not components:
        return {}
    new_components = copy.deepcopy(components)
    for component_key, component_value in new_components.items():
        new_components[component_key]["text"] = (
            get_curr_selected_variation_single_component(
                content_id, component_key, False, cache_enabled
            )
        )
    return new_components


def user_has_internal_features(tofu_user):
    return tofu_user.is_superuser or (
        tofu_user.context and tofu_user.context.get("internalFeatures", False)
    )


def ceildiv(a, b):
    return -(a // -b)


def contains_words_substring(s, min_length, max_length):
    """Given a string s, check if it contains a substring of the form "xx words" where xx is in the range [min_length, max_length] inclusive.
    And return the matched substring if found, otherwise return None."""

    # Match a substring of the form "xx word" or "xx words" where xx is in the range [a, b] inclusive
    match = re.search(r"\b(\d+) words?\b", s)
    if match:
        num_words = int(match.group(1))
        if min_length <= num_words <= max_length:
            return match.group(0)
    return None


# def escape_xml_special_chars(string):
#     string = str(string)
#     return string.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")


def to_camel_case(s):
    # Split the string by spaces or underscores
    parts = re.split(r"[_\s]", s)

    # Capitalize the first letter of each part except the first one
    camel_case = parts[0].lower() + "".join(word.capitalize() for word in parts[1:])

    return camel_case


def fix_xml_context_tags(xml_string, supress_error=False):
    # if string doesn't start with <context>, add it.
    if not xml_string.startswith("<context>"):
        xml_string = "<context>" + xml_string

    # if the count of <content> greater than the count of </content>, add it.
    if xml_string.count("<content>") > xml_string.count("</content>"):
        xml_string = xml_string + "\n</content>"
    # if string doesn't end with </context>, add it.
    if not xml_string.endswith("</context>"):
        xml_string = xml_string + "\n</context>"

    # if not supress_error:
    #     # Check if xml_string is a valid xml, otherwise raise a logging error.
    #     # Wrap the string first in <data> and </data> tags to make it a valid xml.
    #     xml_string_test = f"<data>{xml_string}</data>"

    #     if not is_valid_xml(xml_string_test):
    #         logging.error(f"Invalid XML: {xml_string}")

    return xml_string


# def is_valid_xml(xml_string):
#     try:
#         ET.fromstring(xml_string)
#         return True
#     except Exception as e:
#         logging.error(f"Failed to parse XML string: {xml_string}\n{e}")
#         return False


def fix_content_html_tags(content):
    # for the following list of tags, replace <tag/> with <tag>
    tags = [
        "b",  # bold
        "i",  # italic
        "u",  # underline
        "strong",  # strong
        "em",  # emphasis
        "mark",  # marked
        "small",  # small
        "sub",  # subscript
        "sup",  # superscript
        "del",  # deleted
        "ins",  # inserted
        "s",  # strikethrough
    ]
    for tag in tags:
        content = re.sub(r"<" + tag + r"/?>", "<" + tag + ">", content)
    return content


def extract_slate_editor_text(node):
    if isinstance(node, dict):
        if "text" in node:
            return node["text"]
        if "children" in node:
            return " ".join(
                extract_slate_editor_text(child) for child in node["children"]
            )
    elif isinstance(node, list):
        return " ".join(extract_slate_editor_text(item) for item in node)
    return ""


def log_memory_usage(job, stage):
    process = psutil.Process(os.getpid())
    mem = process.memory_info().rss / 1024 / 1024  # Convert to MB
    logging.warning(f"{job} - {stage} - Memory usage: {mem:.2f} MB")


# llm_cache
def set_llm_cache_based_on_user() -> bool:
    try:
        # TODO: use context manager
        current_user = get_current_user()
        is_user_use_cache = current_user and current_user.is_eligible_for_llm_cache()

        if is_user_use_cache:
            tofu_llm_cache = get_tofu_llm_cache()
            logging.warning(
                f"User {current_user} is using cache for summarization as {tofu_llm_cache}"
            )
            set_llm_cache(tofu_llm_cache)
            return True
        else:
            set_llm_cache(None)
            return False
    except Exception as e:
        logging.error(f"Error setting llm cache: {e}\n{traceback.format_exc()}")
        set_llm_cache(None)
        return False


class TofuLLMCache:
    def __init__(self):
        pass

    def __enter__(self):
        try:
            set_llm_cache_based_on_user()
        except Exception as e:
            logging.error(f"Error setting llm cache: {e}\n{traceback.format_exc()}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            set_llm_cache(None)
        except Exception as e:
            logging.error(f"Error resetting llm cache: {e}\n{traceback.format_exc()}")


# rate limit decorator
# it may throw exception if the rate limit is exceeded
def tofu_rate_limit(
    calls, period, sleep_time=5, max_retries=3, exponential_backoff=True
):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get class name if method belongs to a class
            if args and hasattr(args[0].__class__, "__name__"):
                class_name = args[0].__class__.__name__
                key = f"rate_limit:{class_name}.{func.__name__}"
            else:
                key = f"rate_limit:{func.__name__}"

            retry_count = 0
            while retry_count <= max_retries:
                current = cache.get(key)
                if current is None:
                    cache.set(key, 1, timeout=period)
                    return func(*args, **kwargs)

                if int(current) < calls:
                    cache.incr(key)
                    return func(*args, **kwargs)

                retry_count += 1
                if retry_count > max_retries:
                    raise Exception(f"Rate limit exceeded for {key}")

                # Calculate sleep duration based on backoff strategy
                current_sleep = (
                    sleep_time * (2 ** (retry_count - 1))
                    if exponential_backoff
                    else sleep_time
                )
                time.sleep(current_sleep)

        return wrapper

    return decorator


def is_integer(value):
    if isinstance(value, int) and not isinstance(value, bool):
        return True
    if isinstance(value, str):
        # Check if it's a valid integer string (no decimal point)
        return value.isdigit() or (value.startswith("-") and value[1:].isdigit())
    return False


def try_parse_int(value):
    if is_integer(value):
        try:
            return int(value), True
        except (ValueError, TypeError):
            return None, False
    return None, False


def get_user_from_id_or_object(user_id, db_object):
    user = None
    try:
        if user_id:
            user = TofuUser.objects.filter(id=user_id).first()
            if not user:
                logging.error(f"User with id {user_id} does not exist")
        if not user:
            if db_object and hasattr(db_object, "creator"):
                user = db_object.creator
    except Exception as e:
        logging.exception(
            f"Error getting user from id {user_id} or object {db_object}: {e}"
        )
    return user


def fix_content_collection_pointers(content_group_ids):
    # Fix content_collection pointers
    # first content_group should have prev = None, next = content_group_ids[1], etc.
    content_groups = ContentGroup.objects.filter(id__in=content_group_ids)
    content_groups_dict = {cg.id: cg for cg in content_groups}

    # sort the content_groups by content_collection id.
    content_groups_by_content_collection_id = {}
    for content_group_id in content_group_ids:
        content_group = content_groups_dict.get(content_group_id)
        if not content_group:
            raise ValueError(f"Content group not found: {content_group_id}")
        content_collection_id = content_group.content_group_params.get(
            "content_collection", {}
        ).get("id")
        if content_collection_id:
            if content_collection_id not in content_groups_by_content_collection_id:
                content_groups_by_content_collection_id[content_collection_id] = []
            content_groups_by_content_collection_id[content_collection_id].append(
                content_group_id
            )

    # Sort content_group_ids within each content_collection using the already fetched content groups
    for (
        content_collection_id,
        content_group_ids,
    ) in content_groups_by_content_collection_id.items():
        content_group_ids.sort(
            key=lambda x: content_groups_dict[x].content_group_params.get(
                "action_index", 0
            )
        )

    updates = []
    # update the content_group_params for each content_group in each content_collection
    for (
        content_collection_id,
        content_group_ids,
    ) in content_groups_by_content_collection_id.items():
        for i, content_group_id in enumerate(content_group_ids):
            content_group = content_groups_dict.get(content_group_id)
            if not content_group:
                raise ValueError(f"Content group not found: {content_group_id}")
            content_collection_params = content_group.content_group_params.get(
                "content_collection", {}
            )

            # Set prev pointer
            if i == 0:
                content_collection_params["prev"] = None
            else:
                content_collection_params["prev"] = [content_group_ids[i - 1]]

            # Set next pointer
            if i == len(content_group_ids) - 1:
                content_collection_params["next"] = None
            else:
                content_collection_params["next"] = [content_group_ids[i + 1]]

            content_group.content_group_params["content_collection"] = (
                content_collection_params
            )
            updates.append(content_group)

    ContentGroup.objects.bulk_update(updates, ["content_group_params"])


def sort_action_types(eligible_actions):
    """
    Sort the eligible actions dictionary based on a predefined order.

    Args:
        eligible_actions (dict): Dictionary of eligible next actions to sort

    Returns:
        dict: Sorted eligible actions dictionary
    """
    from .actions.action_system_config_loader import ActionSystemConfigLoader

    # Get ordered list of action types from config loader
    action_types_order = ActionSystemConfigLoader().get_all_action_types_str_in_order()

    # Create index mapping for sorting
    action_type_indices = {
        action_type: idx for idx, action_type in enumerate(action_types_order)
    }

    # Sort eligible actions by action type order
    sorted_eligible_actions = {
        action_type: eligible_actions[action_type]
        for action_type in sorted(
            eligible_actions.keys(),
            key=lambda x: action_type_indices.get(x, float("inf")),
        )
    }

    return sorted_eligible_actions


# CloudWatch Metrics utilities
class CloudWatchDimensionError(Exception):
    """Exception raised for errors in CloudWatch dimensions processing."""

    pass


class CloudWatchMetrics:
    # Class-level variables
    _env = os.environ.get("TOFU_ENV", "dev")
    _base_namespace = "Tofu"

    # Only create cloudwatch client if not in unit test
    _cloudwatch = (
        None
        if _env
        in {
            "unit_test",
            "local",
            "integration_test",
        }
        else boto3.client(
            "cloudwatch", region_name=os.environ.get("AWS_DEFAULT_REGION", "us-west-2")
        )
    )

    @staticmethod
    def _process_dimensions(dimensions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        try:
            for dimension in dimensions:
                if "Value" not in dimension:
                    raise CloudWatchDimensionError(
                        f"Missing 'Value' key in dimension: {dimension}"
                    )

                if "Name" not in dimension:
                    raise CloudWatchDimensionError(
                        f"Missing 'Name' key in dimension: {dimension}"
                    )

                original_value = str(dimension["Value"])

                # Check if the value exceeds the length limit
                if len(original_value) > 1024:
                    logging.warning(
                        f"Dimension value for {dimension['Name']} exceeded 1024 characters and was truncated."
                    )
                    # Truncate the value to 1024 characters
                    truncated_value = original_value[:1024]
                else:
                    truncated_value = original_value

                # Check if the value is ASCII
                if not truncated_value.isascii():
                    logging.warning(
                        f"Dimension value for {dimension['Name']} contains non-ASCII characters and will be cleaned."
                    )
                    # Ensure the value is ASCII
                    ascii_value = truncated_value.encode("ascii", "ignore").decode(
                        "ascii"
                    )
                else:
                    ascii_value = truncated_value

                # Assign the processed value back to the dimension
                dimension["Value"] = ascii_value

            return dimensions
        except CloudWatchDimensionError as e:
            logging.error(f"Error processing dimensions: {str(e)}")
            raise
        except Exception as e:
            logging.exception(f"Unexpected error processing dimensions: {str(e)}")
            raise CloudWatchDimensionError(
                f"Unexpected error processing dimensions: {str(e)}"
            )

    @staticmethod
    def put_metric(
        metric_name: str,
        value: float,
        dimensions: List[Dict[str, str]],
        unit: str = "Count",
    ) -> None:
        # Skip sending metrics in unit test environment
        if CloudWatchMetrics._env in {
            "unit_test",
            "local",
            "integration_test",
        }:
            return

        try:
            dimensions = CloudWatchMetrics._process_dimensions(dimensions)
            dimensions.append({"Name": "Environment", "Value": CloudWatchMetrics._env})

            CloudWatchMetrics._cloudwatch.put_metric_data(
                Namespace=CloudWatchMetrics._base_namespace,
                MetricData=[
                    {
                        "MetricName": metric_name,
                        "Value": value,
                        "Unit": unit,
                        "Dimensions": dimensions,
                    }
                ],
            )
        except Exception as e:
            logging.error(f"Failed to put CloudWatch metric {metric_name}: {str(e)}")


def run_with_retry(max_retries=3, retry_exceptions=(Exception,), operation_name=None):
    """
    Decorator to automatically retry a function on specific exceptions with exponential backoff.

    Args:
        max_retries: Maximum number of retry attempts (default: 3)
        retry_exceptions: Tuple of exception classes that should trigger a retry (default: Exception)
        operation_name: Custom name for the operation in logs (default: function name)

    Returns:
        A decorator function

    Example:
        @run_with_retry(max_retries=3, retry_exceptions=(DatabaseError, ConnectionError))
        def fetch_data_from_db(id):
            # Function that might fail temporarily
            return db.query(id)
    """
    import time

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            func_name = operation_name or func.__name__
            retry_count = 0
            last_exception = None

            while retry_count <= max_retries:
                try:
                    return func(*args, **kwargs)
                except retry_exceptions as e:
                    retry_count += 1
                    last_exception = e

                    if retry_count > max_retries:
                        logging.error(
                            f"{func_name} failed after {max_retries} retries: {e}"
                        )
                        break

                    # Calculate exponential backoff delay
                    delay = 0.5 * (2 ** (retry_count - 1))
                    logging.warning(
                        f"{func_name} failed, retry {retry_count} in {delay:.1f}s: {e}"
                    )
                    time.sleep(delay)

            # If we've exhausted retries, re-raise the last exception
            if last_exception:
                raise last_exception

            return None

        return wrapper

    return decorator


def is_valid_url(url):
    try:
        url_pattern = re.compile(
            r"(?:https?:\/\/)?"  # Optional http:// or https://
            r"(?:www\.)?"  # Optional www.
            r"[A-Za-z0-9-]+"  # Domain name part (before the first dot)
            r"(?:\.[A-Za-z0-9-]+)*"  # Optional subdomains, e.g. .co, .co.uk, etc.
            r"\.(?:com|org|net|edu|gov|io|co|ai|app|dev)"  # Restrict TLD
            r"(?:\/[^\s]*)*"  # Optional path/query string
        )
        return bool(url_pattern.match(url))
    except Exception:
        return False
