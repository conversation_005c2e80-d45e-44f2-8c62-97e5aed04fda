import base64
import logging
import os
import uuid

from openai import OpenAI
from ratelimit import limits, sleep_and_retry

from .image_utils import upload_image_to_s3
from .langsmith_integration import BaseTracableClass, dynamic_traceable
from .thread_locals import get_current_playbook
from .utils import measure_latency, tofu_rate_limit


class ImageGenerator(BaseTracableClass):
    def __init__(self, model_config):
        BaseTracableClass.__init__(self)
        self.model_config = model_config
        self.model_name = None
        self.model_kwargs = {}

    def _execute_with_fallback(self, operation, *args, **kwargs):
        for fallback_level, model_params in enumerate(
            self.model_config.model_params_list
        ):
            try:
                self.model_params = model_params
                self.model_name = model_params.model_name
                self.model_kwargs = model_params.model_params
                return operation(*args, **kwargs)
            except Exception as e:
                logging.exception(
                    f"Error with model {self.model_name} at fallback level {fallback_level}: {e}"
                )
                if fallback_level + 1 >= len(self.model_config.model_params_list):
                    raise Exception(
                        f"Failed to get results for {self.model_name} after {fallback_level + 1} fallback attempts"
                    ) from e

    # hardcoded rpm for gpt-image-1
    @tofu_rate_limit(calls=250, period=60, sleep_time=10, max_retries=10)
    def generate_image_with_fallback(
        self, prompt, playbook, prev_images=None, **kwargs
    ):
        return self._execute_with_fallback(
            self._generate_image,
            prompt,
            playbook,
            prev_images=prev_images,
            **kwargs,
        )

    @measure_latency
    @dynamic_traceable(name="image_generation")
    def _generate_image(self, image_gen_prompt, playbook, prev_images=None, **kwargs):
        file_name = f"{uuid.uuid4()}"
        if not playbook:
            playbook = get_current_playbook()
        if not playbook:
            logging.error("No playbook found")
        playbook_id = playbook.id if playbook else None

        if self.model_name == "gpt-image-1":
            client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

            # edit image case
            if prev_images:
                if isinstance(prev_images, list) and len(prev_images) == 1:
                    prev_images = prev_images[0]
                img = client.images.edit(
                    model="gpt-image-1",
                    image=prev_images,
                    prompt=image_gen_prompt,
                    quality=self.model_kwargs.get("quality", "auto"),
                )
                image_bytes = base64.b64decode(img.data[0].b64_json)
                s3_url = upload_image_to_s3(
                    image_bytes, file_name, playbook_id=playbook_id
                )
                return s3_url
            else:
                img = client.images.generate(
                    model="gpt-image-1",
                    prompt=image_gen_prompt,
                    n=1,
                    quality=self.model_kwargs.get("quality", "auto"),
                )
                base64_image_data = base64.b64decode(img.data[0].b64_json)
                s3_url = upload_image_to_s3(
                    base64_image_data, file_name, playbook_id=playbook_id
                )
                return s3_url
        else:
            raise ValueError(f"Model {self.model_name} is not supported")
