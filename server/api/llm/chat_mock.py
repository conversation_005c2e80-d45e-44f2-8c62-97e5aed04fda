import logging
import traceback
from typing import Any, Dict, List, Optional, Tuple

from langchain_core.callbacks import CallbackManagerForLLMRun
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.outputs import ChatGeneration, ChatResult


class ChatMock(BaseChatModel):
    """A mock chat model for testing purposes."""

    responses: Dict[str, str] = {}
    default_response: str = "I don't know how to respond to that."

    class Config:
        """Configuration for this pydantic object."""

        arbitrary_types_allowed = True

    @property
    def _llm_type(self) -> str:
        """Return type of chat model."""
        return "chat-mock"

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generate a response based on the input messages."""
        components = kwargs.get("components", {}) or kwargs.get("kwargs", {}).get(
            "components", {}
        )
        if not components:
            current_stack = traceback.extract_stack()
            logging.error(
                f"Current call stack:\n{''.join(traceback.format_list(current_stack))}"
            )
        num_of_variations = kwargs.get("num_of_variations", 1)
        generations = []
        for _ in range(num_of_variations):
            # Process components and create the desired output format
            output = {}
            for key, component in components.items():
                text = component.get("text", "")
                word_count = len(text.split())
                output[key] = {"text": text, "word count": word_count}

            # Create an AIMessage with the output as content
            message = AIMessage(content=str(output))
            generation = ChatGeneration(message=message)
            generations.append(generation)

        return ChatResult(generations=generations)

    def _get_message_key(self, messages: List[BaseMessage]) -> str:
        """Generate a key based on the input messages."""
        return " ".join([msg.content for msg in messages])

    def add_response(self, messages: List[Tuple[str, str]], response: str) -> None:
        """Add a predefined response for a specific input pattern."""
        key = " ".join([f"{role}: {content}" for role, content in messages])
        self.responses[key] = response

    def set_default_response(self, response: str) -> None:
        """Set the default response for when no matching pattern is found."""
        self.default_response = response

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        # Here, use the messages, stop, run_manager, and any additional kwargs
        # to generate your response
        response = self._generate_response(messages, stop, **kwargs)
        return response
