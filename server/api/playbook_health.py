import concurrent.futures
import gc
import logging
import traceback
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime
from typing import List
from unittest.mock import patch

from django.db.models import Case, IntegerField, Prefetch, Value, When

from .admin import TofuUserForm
from .data_loaders.url_loader import TofuURLLoader
from .data_loaders.url_loader_utils import try_parse_google_url
from .integrations.slack import send_slack_file_with_tmp_file, send_slack_message
from .models import (
    Action,
    AssetInfo,
    AssetInfoGroup,
    Campaign,
    CompanyInfo,
    Content,
    ContentGroup,
    ContentVariation,
    Playbook,
    TargetInfo,
    TargetInfoGroup,
    TofuUser,
)
from .playbook_build.doc_loader import <PERSON><PERSON>oader
from .playbook_build.playbook_builder import PlaybookBuilder, PlaybookStatus
from .utils import get_s3_file, log_memory_usage, parse_s3_presigned_url
from .validator.action_validator import validate_action
from .validator.asset_info_validator import (
    validate_asset_info,
    validate_asset_info_group,
)
from .validator.campaign_validator import validate_campaign
from .validator.content_group_validator import validate_content_group
from .validator.content_validator import validate_content
from .validator.content_variation_validator import validate_content_variation
from .validator.playbook_validator import validate_playbook
from .validator.target_info_validator import (
    validate_target_info,
    validate_target_info_group,
)
from .validator.tofu_user_validator import validate_tofu_user


@contextmanager
def suppress_logging_error():
    logger = logging.getLogger()  # Get the root logger
    old_level = logger.getEffectiveLevel()  # Save the current logging level
    logger.setLevel(
        logging.CRITICAL
    )  # Temporarily set logging level to CRITICAL to suppress ERROR logs
    try:
        yield
    finally:
        logger.setLevel(old_level)  # Restore the original logging level


@dataclass
class CheckOption:
    playbook_list: List[int] = None
    check_playbook: bool = True
    check_campaign: bool = True
    check_content: bool = True
    check_content_group: bool = True
    check_content_variation: bool = True
    check_action: bool = True
    check_asset: bool = True
    check_target: bool = True

    def __post_init__(self):
        if self.playbook_list is None:
            self.playbook_list = []

    @classmethod
    def all_enabled(cls):
        return cls()

    def is_content_check_enabled(self):
        return (
            self.check_content
            or self.check_content_group
            or self.check_content_variation
        )

    def is_campaign_check_enabled(self):
        return (
            self.check_campaign or self.is_content_check_enabled() or self.check_action
        )

    def is_playbook_check_enabled(self):
        return (
            self.check_playbook
            or self.check_asset
            or self.check_target
            or self.is_campaign_check_enabled()
        )


class BaseDataValidationChecker:
    def __init__(self) -> None:
        self._errors = {}
        self._health = True

    @property
    def health(self):
        return self._health

    @property
    def errors(self):
        return self._errors

    def _append_error(self, error_type, error):
        if not error:
            return

        if error_type not in self._errors:
            self._errors[error_type] = []
        if isinstance(error, list):
            self._errors[error_type].extend(error)
        elif isinstance(error, str):
            self._errors[error_type].append(error)
        else:
            logging.error(f"unknown error type: {error_type} with error: {error}")
            self._errors[error_type].append(str(error))
        self._health = False

    def _update_sub_checker(self, sub_checker):
        self._health = self._health and sub_checker.health
        self._errors.update(sub_checker.errors)


class CampaignDataValidationChecker(BaseDataValidationChecker):
    def __init__(self, campaign_instance) -> None:
        super().__init__()
        self.campaign_instance = campaign_instance

    def check(self, options: CheckOption = None):
        if options is None:
            options = CheckOption.all_enabled()
        self._check_option = options
        if not self._check_option.is_campaign_check_enabled():
            return

        self._check_campaign()
        self._check_content_group()
        self._check_action()

    def _check_campaign(self):
        if not self._check_option.check_campaign:
            return
        try:
            error = validate_campaign(self.campaign_instance)
            if error:
                self._append_error("data_validation", error)
        except Exception as e:
            error = f"Failed to validate campaign {self.campaign_instance.id} with error: {e}"
            self._append_error("data_validation", error)

    def _check_content_group(self):
        if not self._check_option.is_content_check_enabled():
            return
        content_groups = ContentGroup.objects.filter(campaign=self.campaign_instance)
        for content_group in content_groups:
            try:
                error = validate_content_group(content_group)
                if error:
                    self._append_error("data_validation", error)
            except Exception as e:
                error = f"Failed to validate content_group {content_group.id} with error: {e}"
                self._append_error("data_validation", error)
            self._check_content(content_group)

    def _check_content(self, content_group):
        if not self._check_option.check_content:
            return
        contents = Content.objects.filter(content_group=content_group)
        for content in contents:
            try:
                error = validate_content(content)
                if error:
                    self._append_error("data_validation", error)
            except Exception as e:
                error = f"Failed to validate content {content.id} with error: {e}"
                self._append_error("data_validation", error)

            if self._check_option.check_content_variation:
                self._check_content_variation(content)

    def _check_content_variation(self, content):
        if not self._check_option.check_content_variation:
            return
        content_variations = ContentVariation.objects.filter(content=content)
        for content_variation in content_variations:
            try:
                error = validate_content_variation(content_variation)
                if error:
                    self._append_error("data_validation", error)
            except Exception as e:
                error = f"Failed to validate content_variation {content_variation.id} with error: {e}"
                self._append_error("data_validation", error)

    def _check_action(self):
        if not self._check_option.check_action:
            return
        actions = Action.objects.filter(campaign=self.campaign_instance)
        for action in actions:
            try:
                error = validate_action(action)
                if error:
                    self._append_error("data_validation", error)
            except Exception as e:
                error = f"Failed to validate action {action.id} with error: {e}"
                self._append_error("data_validation", error)


class PlaybookDataValidationChecker(BaseDataValidationChecker):
    def __init__(self, playbook_instance) -> None:
        super().__init__()
        self.playbook_instance = playbook_instance

    def check(self, options: CheckOption = None):
        if options is None:
            options = CheckOption.all_enabled()
        self._check_option = options
        if not self._check_option.is_playbook_check_enabled():
            return

        self._check_playbook()
        self._check_asset()
        self._check_target()
        self._check_campaigns()

    def _check_playbook(self):
        if not self._check_option.check_playbook:
            return
        try:
            error = validate_playbook(self.playbook_instance)
            if error:
                self._append_error("data_validation", error)
        except Exception as e:
            error = f"Failed to validate playbook {self.playbook_instance.id} with error: {e}"
            self._append_error("data_validation", error)

    def _check_asset(self):
        if not self._check_option.check_asset:
            return
        asset_info_groups = AssetInfoGroup.objects.filter(
            playbook=self.playbook_instance
        )
        for asset_info_group in asset_info_groups:
            try:
                error = validate_asset_info_group(asset_info_group)
                if error:
                    self._append_error("data_validation", error)
            except Exception as e:
                error = f"Failed to validate asset_info_group {asset_info_group.id} with error: {e}"
                self._append_error("data_validation", error)

            asset_infos = AssetInfo.objects.filter(asset_info_group=asset_info_group)
            for asset_info in asset_infos:
                try:
                    error = validate_asset_info(asset_info)
                    if error:
                        self._append_error("data_validation", error)
                except Exception as e:
                    error = (
                        f"Failed to validate asset_info {asset_info.id} with error: {e}"
                    )
                    self._append_error("data_validation", error)

    def _check_target(self):
        if not self._check_option.check_target:
            return
        target_info_groups = TargetInfoGroup.objects.filter(
            playbook=self.playbook_instance
        )
        for target_info_group in target_info_groups:
            try:
                error = validate_target_info_group(target_info_group)
                if error:
                    self._append_error("data_validation", error)
            except Exception as e:
                error = f"Failed to validate target_info_group {target_info_group.id} with error: {e}"
                self._append_error("data_validation", error)

            target_infos = TargetInfo.objects.filter(
                target_info_group=target_info_group
            )
            for target_info in target_infos:
                try:
                    error = validate_target_info(target_info)
                    if error:
                        self._append_error("data_validation", error)
                except Exception as e:
                    error = f"Failed to validate target_info {target_info.id} with error: {e}"
                    self._append_error("data_validation", error)

    def _check_campaigns(self):
        if not self._check_option.is_campaign_check_enabled():
            return
        campaigns = Campaign.objects.filter(playbook=self.playbook_instance)
        for campaign in campaigns:
            campaign_data_validation_checker = CampaignDataValidationChecker(campaign)
            campaign_data_validation_checker.check(self._check_option)
            self._update_sub_checker(campaign_data_validation_checker)


class PlaybookHealthChecker(BaseDataValidationChecker):
    def __init__(self, playbook_instance) -> None:
        super().__init__()
        self.playbook_instance = playbook_instance

    def _check_users(self):
        users = self.playbook_instance.users.all()
        if not users:
            self._append_error(
                "users", f"playbook {self.playbook_instance.id} has no users"
            )

    def _check_object_build(self):
        playbook_builder = PlaybookBuilder(self.playbook_instance)
        try:
            healthiness, errors, objects_to_build = playbook_builder.check_healthiness(
                rebuild=True
            )
            if healthiness != PlaybookStatus.SUCCESS:
                self._append_error(
                    "object_build",
                    {
                        "id": self.playbook_instance.id,
                        "status": healthiness,
                        "errors": errors,
                        "objects_to_build": objects_to_build,
                    },
                )
        except Exception as e:
            errors = f"Error in checking playbook healthiness for playbook {self.playbook_instance.id}: {e}"
            logging.error(errors)
            self._append_error(
                "object_build",
                {
                    "id": self.playbook_instance.id,
                    "status": "error",
                    "errors": [errors],
                    "objects_to_build": [],
                },
            )

    def _check_link(self, link, link_type, source):
        try:
            if link_type == "url" or link_type == "file":
                with suppress_logging_error():
                    doc_loader = DocLoader(metadata={})
                    documents = doc_loader.extract_single_doc_from_loader(
                        link_type, link
                    )
            elif link_type == "content_source_copy":
                source_file_name, file_type, s3_bucket = parse_s3_presigned_url(link)
                if file_type == "text/html":
                    _content = get_s3_file(link)
                else:
                    # with suppress_logging_error():
                    doc_loader = DocLoader(metadata={})
                    try:
                        documents = doc_loader.extract_single_doc_from_loader(
                            "file",
                            {
                                "s3_filename": source_file_name,
                                "s3_bucket": s3_bucket,
                                "mime_file_type": file_type,
                            },
                        )
                    except Exception as e:
                        message = f"for s3 in content_source_copy: {source} - failed to load with error: {e}"
                        self._append_error("link", message)

            else:
                logging.error(f"unknown link_type: {link_type}")
        except Exception as e:
            message = f"for {link_type } in context: {source} - failed to load: {link} with error: {e}"
            self._append_error("link", message)

    def _check_object_files(self):
        playbook_builder = PlaybookBuilder(self.playbook_instance)
        all_objects = playbook_builder.get_all_objects()

        for obj in all_objects:
            for v in obj.docs.values():
                try:
                    if v.get("type") == "url":
                        continue  # skip google drive check
                        google_link = try_parse_google_url(v.get("value", ""))
                        if google_link:
                            if "id" not in google_link:
                                logging.error(
                                    f"failed to get id from parsed google drive link: {v.get('value')} with result {google_link}"
                                )
                                continue
                            self._check_link(v.get("value"), "url", str(obj))
                    elif v.get("type") == "file":
                        file_value = v.get("value", {})
                        if not file_value or "s3_filename" not in file_value:
                            # TODO: tackle the failure that value is empty for file
                            continue

                        key = v.get("value", {}).get("s3_filename", "")
                        if key:
                            self._check_link(v.get("value"), "file", str(obj))
                        else:
                            logging.error(f"failed to get s3_filename: {v}")
                except Exception as e:
                    logging.error(
                        f"failed to get file link for object {str(obj)}: {v} with error: {e}\n{traceback.format_exc()}"
                    )

    def _check_content_source_copy(self):
        added_content_source_copy = set()

        for content_group in ContentGroup.objects.filter(
            campaign__playbook=self.playbook_instance
        ):
            if not content_group.content_group_params:
                continue
            content_source_copy = content_group.content_group_params.get(
                "content_source_copy", ""
            )
            if content_source_copy:
                if content_source_copy in added_content_source_copy:
                    continue
                added_content_source_copy.add(content_source_copy)
                self._check_link(
                    content_source_copy, "content_source_copy", str(content_group)
                )

    def _check_links(self):
        with (
            patch("django.core.cache.cache.get", return_value=None),
            patch("django.core.cache.cache.set", lambda *args, **kwargs: None),
        ):
            self._check_object_files()
            self._check_content_source_copy()

    def _check_data_validation(self, options: CheckOption):
        playbook_data_validation_checker = PlaybookDataValidationChecker(
            self.playbook_instance
        )
        playbook_data_validation_checker.check(options)
        self._update_sub_checker(playbook_data_validation_checker)

    def check(self, options: CheckOption = None):
        if options is None:
            options = CheckOption.all_enabled()

        logging.warning(f"checking playbook: {self.playbook_instance.id}")
        self._check_users()

        try:
            self._check_object_build()
        except Exception as e:
            errors = f"Error in _check_check_object_build_links for playbook health check {self.playbook_instance.id}: {e}\n{traceback.format_exc()}"
            logging.error(errors)

        try:
            self._check_data_validation(options)
        except Exception as e:
            errors = f"Error in _check_data_validation for playbook health check {self.playbook_instance.id}: {e}\n{traceback.format_exc()}"
            logging.error(errors)

        log_memory_usage(
            "playbook_health_check",
            f"end of PlaybookHealthChecker.check for playbook {self.playbook_instance.id}",
        )

    def gen_report(self):
        if self.health:
            logging.error(
                f"report is not needed for healthy playbook {self.playbook_instance.id}"
            )
            return f"Playbook {self.playbook_instance.id} is healthy"

        user = self.playbook_instance.users.first()
        username = user.username if user else "unknown"
        headline = f"Playbook {self.playbook_instance.id} for {username} is unhealthy"
        details = ""
        for error_type, errors in self.errors.items():
            details += f"{error_type}:\n"
            for error in errors:
                details += f"  - {error}\n"
        return f"{headline}\n{details}"


class PlaybookCheckManager:
    def __init__(self) -> None:
        pass

    def run_health_check(
        self,
        check_options: CheckOption,
    ):
        log_memory_usage("playbook_health_check", "start of run_health_check")

        def check_playbook_health(playbook, options: CheckOption):
            checker = PlaybookHealthChecker(playbook)
            checker.check(options)
            report = None
            if not checker.health:
                report = checker.gen_report()
            del checker
            gc.collect()
            return report

        cnt_unhealthy = 0
        reports = []

        excluded_playbook_ids = [
            1,  # tofuadmin
            2,  # <EMAIL>
            3,  # <EMAIL>
            4,  # <EMAIL>
            88,  # <EMAIL>
            287,  # tofuadmin-kimi
            369,  # tofuadmin-isaque
            868,  # tofuadmin-roberto
        ]

        if check_options.playbook_list:
            playbooks = Playbook.objects.filter(id__in=check_options.playbook_list)
        else:
            playbooks = (
                Playbook.objects.exclude(users__username__startswith="tofuadmin")
                .exclude(id__in=excluded_playbook_ids)
                .order_by("-id")
                .distinct()
            )
        cnt_total = playbooks.count()

        log_memory_usage("playbook_health_check", "after fetching playbooks")

        parallel_check = False
        if parallel_check:
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                future_to_playbook = {
                    executor.submit(
                        check_playbook_health, playbook, check_options
                    ): playbook
                    for playbook in playbooks.iterator()
                }

                for future in concurrent.futures.as_completed(future_to_playbook):
                    playbook = future_to_playbook[future]
                    try:
                        report = future.result()
                        if report is not None:
                            cnt_unhealthy += 1
                            reports.append(report)
                    except Exception as exc:
                        print(f"{playbook} generated an exception: {exc}")
                    gc.collect()
                    log_memory_usage(
                        "playbook_health_check",
                        f"after parallel processing playbook {playbook.id}",
                    )
        else:
            for playbook in playbooks.iterator():
                report = check_playbook_health(playbook, check_options)
                if report is not None:
                    cnt_unhealthy += 1
                    reports.append(report)
                gc.collect()
                log_memory_usage(
                    "playbook_health_check",
                    f"after serial processing playbook {playbook.id}",
                )

        log_memory_usage("playbook_health_check", "end of run_health_check")

        pct_unhealthy = (
            int(cnt_unhealthy * 100 / cnt_total)
            if cnt_total > 0
            else "[Error: count for playbook is 0]"
        )

        details = "\n".join(reports)

        headline = f"Of {cnt_total} playbooks, {cnt_unhealthy} ({pct_unhealthy}%) are unhealthy."
        report = f"""{headline}
{details}"""

        channel = "#alerts"
        if len(report) > 500:  # dump to a file and upload to slack
            send_slack_file_with_tmp_file(
                channel,
                report,
                f"Unhealthy Playbooks Report-{datetime.now().date().strftime('%Y-%m-%d')}",
                headline,
            )
        else:
            send_slack_message(channel, report)


class TofuUserCheckManager:
    def __init__(self) -> None:
        pass

    def check_user(self, user):
        try:
            error = validate_tofu_user(user)
        except Exception as e:
            error = str(e)
        return bool(error), error

    def run_health_check(self):
        users = TofuUser.objects.all()
        cnt_unhealthy = 0
        reports = []

        for user in users:
            succ, error = self.check_user(user)
            if not succ:
                cnt_unhealthy += 1
                reports.append(f"User {user.id} is unhealthy: {error}")

        cnt_total = TofuUser.objects.count()

        pct_unhealthy = (
            int(cnt_unhealthy * 100 / cnt_total)
            if cnt_total > 0
            else "[Error: count for user is 0]"
        )

        details = "\n".join(reports)

        headline = (
            f"Of {cnt_total} users, {cnt_unhealthy} ({pct_unhealthy}%) are unhealthy."
        )
        report = f"""{headline}
{details}"""

        channel = "#alerts"
        if len(report) > 500:  # dump to a file and upload to slack
            send_slack_file_with_tmp_file(
                channel,
                report,
                f"Unhealthy TofuUser Report-{datetime.now().date().strftime('%Y-%m-%d')}",
                headline,
            )
        else:
            send_slack_message(channel, report)
