import logging
from dataclasses import dataclass
from typing import Callable, List, Optional, cast

from langchain_text_splitters import TextSplitter

from .utils import get_token_count

logger = logging.getLogger(__name__)


class SentenceSplitter(TextSplitter):
    """Split text with a preference for complete sentences.

    In general, this class tries to keep sentences and paragraphs together so there
    are less likely to be hanging sentences or parts of sentences at the end of the node chunk.

    """

    def __init__(
        self,
        chunk_size: int,
        chunk_overlap: int,
        separator: str = " ",
        paragraph_separator: Optional[str] = "\n",
        chunking_tokenizer_fn: Optional[Callable[[str], List[str]]] = None,
        secondary_chunking_regex: str = "[^.;。]+[.;。]?",
    ):
        """Initialize with parameters."""
        if chunk_overlap > chunk_size:
            raise ValueError(
                f"Got a larger chunk overlap ({chunk_overlap}) than chunk size "
                f"({chunk_size}), should be smaller."
            )
        self._separator = separator
        self._chunk_size = chunk_size
        self._chunk_overlap = chunk_overlap
        if chunking_tokenizer_fn is None:
            import nltk.tokenize.punkt as pkt

            class CustomLanguageVars(pkt.PunktLanguageVars):
                _period_context_fmt = r"""
                    %(SentEndChars)s             # a potential sentence ending
                    (\)\"\s)\s*                  # other end chars and
                                                 # any amount of white space
                    (?=(?P<after_tok>
                        %(NonWord)s              # either other punctuation
                        |
                        (?P<next_tok>\S+)     # or whitespace and some other token
                    ))"""

            custom_tknzr = pkt.PunktSentenceTokenizer(lang_vars=CustomLanguageVars())

            chunking_tokenizer_fn = custom_tknzr.tokenize
        self.paragraph_separator = paragraph_separator
        self.chunking_tokenizer_fn = chunking_tokenizer_fn
        self.second_chunking_regex = secondary_chunking_regex
        """
        By default we use the second chunking regex "[^.;]+[.;]?".
        This regular expression will split the sentences into phrases,
        where each phrase is a sequence of one or more non-comma,
        non-period, and non-semicolon characters, followed by an optional comma,
        period, or semicolon. The regular expression will also capture the
        delimiters themselves as separate items in the list of phrases.
        """
        super().__init__(chunk_size=chunk_size, chunk_overlap=chunk_overlap)

    def _postprocess_splits(self, docs: List[str]) -> List[str]:
        """Post-process splits."""
        new_docs = []
        for doc in docs:
            if doc.replace(" ", "") == "":
                continue
            new_docs.append(doc)
        return new_docs

    def split_text(self, text: str) -> List[str]:
        """
        Split incoming text and return chunks with overlap size.

        Has a preference for complete sentences, phrases, and minimal overlap.
        """
        if text == "":
            return []

        # If chunk size is larger than the text, return the text as a single chunk.
        if get_token_count(text) <= self._chunk_size:
            return [text]

        effective_chunk_size = self._chunk_size - self._chunk_overlap

        # First we split paragraphs using separator
        splits = text.split(self.paragraph_separator)

        # Merge paragraphs that are too small.

        idx = 0
        while idx < len(splits):
            if idx < len(splits) - 1 and len(splits[idx]) < effective_chunk_size:
                splits[idx] = "\n\n".join([splits[idx], splits[idx + 1]])
                splits.pop(idx + 1)
            else:
                idx += 1

        # Next we split the text using the chunk tokenizer fn,
        # which defaults to the sentence tokenizer from nltk.
        chunked_splits = [self.chunking_tokenizer_fn(text) for text in splits]
        splits = [chunk for split in chunked_splits for chunk in split]

        # Check if any sentences exceed the chunk size. If they do, split again
        # using the second chunk separator. If it any still exceed,
        # use the default separator (" ").
        @dataclass
        class Split:
            text: str  # the split text
            is_sentence: bool  # save whether this is a full sentence

        new_splits: List[Split] = []
        for split in splits:
            split_len = get_token_count(split)
            if split_len <= effective_chunk_size:
                new_splits.append(Split(split, True))
            else:
                import re

                # Default regex is "[^\.;]+[\.;]?"
                splits2 = re.findall(self.second_chunking_regex, split)

                for split2 in splits2:
                    if get_token_count(split) <= effective_chunk_size:
                        new_splits.append(Split(split2, False))
                    else:
                        splits3 = split2.split(self._separator)
                        new_splits.extend([Split(split3, False) for split3 in splits3])

        # Create the list of text splits by combining smaller chunks.
        docs: List[str] = []
        cur_doc_list: List[str] = []
        cur_tokens = 0
        while len(new_splits) > 0:
            cur_token = new_splits[0]
            cur_len = get_token_count(cur_token.text)
            if cur_len > self._chunk_size:
                logger.warn("Got chunk larger than allowed, skipping")
                new_splits.pop(0)
            elif cur_tokens + cur_len > self._chunk_size:
                docs.append(" ".join(cur_doc_list).strip())
                cur_doc_list = [cur_token.text]
                cur_tokens = cur_len
                new_splits.pop(0)
            else:
                if cur_token.is_sentence or cur_tokens + cur_len < effective_chunk_size:
                    cur_tokens += cur_len
                    cur_doc_list.append(cur_token.text)
                    new_splits.pop(0)
                else:
                    docs.append(" ".join(cur_doc_list).strip())
                    cur_doc_list = [cur_token.text]
                    cur_tokens = cur_len
                    new_splits.pop(0)

        docs.append(" ".join(cur_doc_list).strip())

        # run postprocessing to remove blank spaces
        docs = self._postprocess_splits(docs)

        return docs
