from .models import TofuUser

PROD_TOFU_PAGES_100_PRICE_ID = "price_1Qen1fBByvT24VU2xr0paHTv"
PROD_TOFU_PAGES_300_PRICE_ID = "price_1Qen2MBByvT24VU24HGDXag0"
DEV_TOFU_PAGES_100_PRICE_ID = "price_1Qf9teBA7Wa70pbPUMLsbRt5"
DEV_TOFU_PAGES_300_PRICE_ID = "price_1Qf9u1BA7Wa70pbPs76BAoGr"

price_to_credits = {
    PROD_TOFU_PAGES_100_PRICE_ID: 100,
    PROD_TOFU_PAGES_300_PRICE_ID: 300,
    DEV_TOFU_PAGES_100_PRICE_ID: 100,
    DEV_TOFU_PAGES_300_PRICE_ID: 300,
}


def get_tier_from_price_amount(amount):
    if amount == 4900:
        return TofuUser.TofuLiteSubscriptionTier.TIER_1
    elif amount == 9900:
        return TofuUser.TofuLiteSubscriptionTier.TIER_2
    else:
        raise ValueError(f"Unexpected price amount: {amount}")


def get_credits_from_subscription_tier(subscription_tier):
    if subscription_tier == TofuUser.TofuLiteSubscriptionTier.TIER_1:
        return 100
    elif subscription_tier == TofuUser.TofuLiteSubscriptionTier.TIER_2:
        return 300
    else:
        raise ValueError(f"Unexpected subscription tier: {subscription_tier}")


def get_tier_from_price_id(price_id):
    credits = price_to_credits.get(price_id, None)
    if not credits:
        raise ValueError(f"Unexpected price id: {price_id}")
    if credits == 100:
        return TofuUser.TofuLiteSubscriptionTier.TIER_1
    elif credits == 300:
        return TofuUser.TofuLiteSubscriptionTier.TIER_2
    else:
        raise ValueError(
            f"Unexpected price credits: {credits} for price id: {price_id}"
        )
