import logging
import os

from rest_framework import status
from rest_framework.exceptions import APIException

from .models import TofuUser


class TofuPagesKillSwitchMixin:
    """
    Mixin that checks if Tofu pages are enabled before processing the request.
    This runs after authentication but before view execution.
    Must be first in the inheritance list, e.g.:
    class MyViewSet(TofuPagesKillSwitchMixin, ViewSet):
    """

    def initial(self, request, *args, **kwargs):
        """
        Runs before view execution but after authentication.
        """
        check_tofu_pages_enabled(request)
        super().initial(request, *args, **kwargs)


class TofuPagesDisabledException(APIException):
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    default_detail = "Tofu pages are currently disabled"
    default_code = "service_unavailable"


def check_tofu_pages_enabled(request):
    """Check if Tofu pages are enabled for the current user"""
    user = request.user

    # Skip check for anonymous or non-normal users
    if (
        not user
        or user.is_anonymous
        or user.customer_type != TofuUser.CustomerType.LITE
    ):
        return

    # Check if pages are disabled
    if os.environ.get("DISABLE_TOFU_PAGES", "false").lower() == "true":
        raise TofuPagesDisabledException()
