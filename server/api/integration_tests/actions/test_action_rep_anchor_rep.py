import logging
import os
from unittest.mock import patch

import boto3
from django.core.cache import cache
from langchain_core.messages import AIMessage
from langchain_core.outputs import ChatGeneration
from moto import mock_aws
from rest_framework import status

from ...actions.tofu_data_wrapper import TofuData<PERSON>ist<PERSON>and<PERSON>
from ...models import Action, AssetInfo, ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionStatusType,
    PlatformType,
)
from .action_test_base import ActionTestBase, create_mock_check_asset_info


class TestActionRepAnchorRep(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest
        # Clear the cache before each test
        cache.clear()

    def tearDown(self):
        # Clear the cache after each test
        cache.clear()
        super().tearDown()

    @mock_aws
    def test_action_rep_anchor_rep(self):
        """Test Create, Read, Update, Delete operations for Actions"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):

            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            mock_repupose_response = [
                ChatGeneration(
                    text="abcde",
                    message=AIMessage(content="abcde"),
                    generation_info={},  # Optional metadata
                )
            ]
            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_repupose_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # TODO: add anchor content action
                anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Test Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                self.asset_ids, []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                anchor_content_action_id = response.data[-1]["id"]

                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # fetch anchor content's status

                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                # check outputs
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 3)
                self.assertEqual(len(asset_group_ids), 0)

                num_outputs = 3
                repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Test Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                # Create action
                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                if response.status_code != status.HTTP_201_CREATED:
                    logging.error("Error response: %s", response.content)
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                repurpose_action_id = response.data[-1]["id"]

                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                response = self.client.post(
                    f"/api/action/{repurpose_action_id}/execute/", {}, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

                # Verify content group IDs in the repurpose action output
                repurpose_action = Action.objects.get(id=repurpose_action_id)
                self.assertIn("content_group", repurpose_action.outputs)
                content_group_data = repurpose_action.outputs["content_group"]
                rep_content_group_ids = [
                    group["content_group"]["content_group_id"]
                    for group in content_group_data["data"]
                ]

                # second anchor
                second_anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Test Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {},
                    "incoming_edges": [
                        {
                            "from_action": repurpose_action_id,
                            "config": {
                                "output_name_from_incoming_action": "content_group",
                                "input_name_to_outgoing_action": "assets",
                            },
                        }
                    ],
                }
                response = self.client.post(
                    "/api/action/", second_anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                second_anchor_content_action_id = response.data[-1]["id"]

                # Verify the assets in the second anchor content action
                second_anchor_action = Action.objects.get(
                    id=second_anchor_content_action_id
                )
                self.assertIn("assets", second_anchor_action.inputs)
                asset_data = second_anchor_action.inputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)

                self.assertEqual(len(asset_ids), 3)
                self.assertEqual(len(asset_group_ids), 0)

                asset_infos = AssetInfo.objects.filter(id__in=asset_ids)
                self.assertEqual(len(asset_infos), 3)

                asset_names = {asset.asset_key for asset in asset_infos}
                content_groups = ContentGroup.objects.filter(
                    id__in=rep_content_group_ids
                )
                expected_names = {
                    content_group.content_group_name for content_group in content_groups
                }
                self.assertEqual(asset_names, expected_names)

                # now add user anchor to anchor content
                updated_asset_ids = asset_ids + [self.asset_ids[0]]
                # update anchor content action inputs
                response = self.client.patch(
                    f"/api/action/{second_anchor_content_action_id}/update_inputs/",
                    {
                        "inputs": {
                            "assets": TofuDataListHandler.convert_tofu_data_to_json(
                                TofuDataListHandler.convert_asset_to_tofu_data(
                                    updated_asset_ids, []
                                )
                            ),
                        }
                    },
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the updated inputs
                updated_action = Action.objects.get(id=second_anchor_content_action_id)
                self.assertIn("assets", updated_action.inputs)
                updated_asset_data = updated_action.inputs["assets"]
                (
                    updated_asset_ids_from_db,
                    updated_asset_group_ids,
                ) = TofuDataListHandler.get_assets(updated_asset_data)

                # Should have 4 assets now (3 from previous + 1 new user anchor)
                self.assertEqual(len(updated_asset_ids_from_db), 4)
                self.assertEqual(len(updated_asset_group_ids), 0)
                self.assertEqual(set(updated_asset_ids_from_db), set(updated_asset_ids))

                num_outputs = 3
                second_repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Test Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": second_anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                # Create action
                response = self.client.post(
                    "/api/action/", second_repurpose_action_data, format="json"
                )
                if response.status_code != status.HTTP_201_CREATED:
                    logging.error("Error response: %s", response.content)
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                second_repurpose_action_id = response.data[-1]["id"]

                # now execute the second anchor
                response = self.client.post(
                    f"/api/action/{second_anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # check the second rep's status
                repurpose_action_status = self.client.get(
                    f"/api/action/{second_repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                # check the second rep's inputs
                updated_rep_action = Action.objects.get(id=second_repurpose_action_id)
                self.assertIn("anchor_content", updated_rep_action.inputs)
                updated_anchor_content_data = updated_rep_action.inputs[
                    "anchor_content"
                ]

                # Get asset IDs using the getter method
                (
                    anchor_content_asset_ids,
                    anchor_content_asset_group_ids,
                ) = TofuDataListHandler.get_assets(updated_anchor_content_data)

                # Verify that the asset IDs match
                self.assertEqual(
                    set(anchor_content_asset_ids), set(updated_asset_ids_from_db)
                )
                self.assertEqual(len(anchor_content_asset_group_ids), 0)

                # execute the second rep
                response = self.client.post(
                    f"/api/action/{second_repurpose_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)
