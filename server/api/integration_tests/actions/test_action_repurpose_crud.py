# test_actions.py
import logging

from rest_framework import status

from ...actions.legacy_converter.legacy_custom_instruction_converter import (
    convert_custom_instructions_v2_to_v3,
)
from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...models import ContentGroup
from .action_test_base import ActionTestBase


class ActionTestRepurposeCRUD(ActionTestBase):
    def setUp(self):
        super().setUp()

    def test_crud_repurpose_action(self):
        """Test Create, Read, Update, Delete operations for Actions"""

        num_outputs = 3
        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
        }

        # Create action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]
        outputs = response.data[-1]["outputs"]

        # Get action status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # check that content_collection is created correctly.
        # assert content_group is in outputs
        self.assertIn("content_group", outputs)
        content_group_data = outputs["content_group"]
        content_groups = TofuDataListHandler.get_content_group_ids(content_group_data)
        self.assertEqual(len(content_groups), num_outputs)
        for content_group_id in content_groups:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_group_params = content_group.content_group_params
            self.assertNotEqual(content_group_params.get("content_collection"), None)

        # Update action name
        response = self.client.patch(
            f"/api/action/{action_id}/update_name/",
            {"action_name": "Updated Action Name"},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Update action inputs
        num_outputs_updated = 4
        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {
                "inputs": {
                    "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_int_to_tofu_data(
                            num_outputs_updated
                        )
                    ),
                }
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # check outputs
        response = self.client.get(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # assert content_group is in outputs
        self.assertIn("content_group", response.data["outputs"])
        content_group_data = response.data["outputs"]["content_group"]
        content_groups = TofuDataListHandler.get_content_group_ids(content_group_data)
        self.assertEqual(len(content_groups), num_outputs_updated)

        num_outputs_updated = 2
        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {
                "inputs": {
                    "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_int_to_tofu_data(
                            num_outputs_updated
                        )
                    ),
                }
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # check outputs
        response = self.client.get(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("content_group", response.data["outputs"])
        content_group_data = response.data["outputs"]["content_group"]
        content_groups = TofuDataListHandler.get_content_group_ids(content_group_data)
        self.assertEqual(len(content_groups), num_outputs_updated)

        # Get action definition
        response = self.client.get(f"/api/action/{action_id}/definition/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Delete action
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        num_outputs = 1
        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
        }

        # Create action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]
        outputs = response.data[-1]["outputs"]

        # Get action status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # check that content_collection is not being created.
        # assert content_group is in outputs
        self.assertIn("content_group", outputs)
        content_group_data = outputs["content_group"]
        content_groups = TofuDataListHandler.get_content_group_ids(content_group_data)
        self.assertEqual(len(content_groups), num_outputs)
        for content_group_id in content_groups:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_group_params = content_group.content_group_params
            # assert that 'content_collection' is not in content_group_params
            self.assertNotIn("content_collection", content_group_params)

    def test_crud_repurpose_action_missing_num_outputs(self):
        """Test Create, Read, Update, Delete operations for Actions"""

        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_repurpose_action_with_default_template(self):
        """Test that repurpose action with default template input returns appropriate status"""

        num_outputs = 3
        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Action With Default Template",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
        }

        # Create action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # Get action status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify template is in missing_optional_inputs
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "missing_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "template",
            response.data["details"]["input_check"]["missing_optional_inputs"],
        )

        # Update action inputs with a default template missing content_source_copy
        default_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_UNSPECIFIED": {
                                "content_source": "140454503.json",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                                # Note: content_source_copy is intentionally missing to test validation
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": default_template_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action status again - should indicate template is missing/invalid
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that template is still in missing_optional_inputs even after we tried to provide it
        # This indicates the template was invalid and not accepted
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "missing_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "template",
            response.data["details"]["input_check"]["missing_optional_inputs"],
        )

        # Now update with a valid template
        valid_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_UNSPECIFIED": {
                                "content_source": "140454503.json",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?file=b13eb84a-75c6-b8b2-dfd5-980a36e473f8.json&fileType=application/json&directory=tofu-uploaded-files",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                                "slate_content_source": "140454503.json",
                                "slate_content_source_copy": "/api/web/storage/s3-presigned-url?file=d913f71d-139a-2bf3-7ff9-a16305e5d09b.json&fileType=application/json&directory=tofu-uploaded-files",
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": valid_template_data, "custom_instructions": {}}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action status again - template should no longer be in missing inputs
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify that template is no longer in missing_optional_inputs
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "missing_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertNotIn(
            "template",
            response.data["details"]["input_check"]["missing_optional_inputs"],
        )

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_repurpose_action_with_custom_instructions(self):
        """Test custom instructions handling in repurpose action with collection"""

        # Create repurpose action with 3 outputs (collection case)
        num_outputs = 3
        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Action With Custom Instructions",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
        }

        # Create action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]
        outputs = response.data[-1]["outputs"]

        # Verify content groups are created with collection
        self.assertIn("content_group", outputs)
        content_group_data = outputs["content_group"]
        content_groups = TofuDataListHandler.get_content_group_ids(content_group_data)
        self.assertEqual(len(content_groups), num_outputs)
        for content_group_id in content_groups:
            content_group = ContentGroup.objects.get(id=content_group_id)
            self.assertIn("content_collection", content_group.content_group_params)

        # Get action status - should show custom_instructions as missing optional input
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])

        # Verify input check fields
        input_check = response.data["details"]["input_check"]
        self.assertIn("missing_required_inputs", input_check)
        self.assertIn("missing_optional_inputs", input_check)
        self.assertIn("provided_required_inputs", input_check)

        # Verify custom_instructions is in missing_optional_inputs
        self.assertIn("custom_instructions", input_check["missing_optional_inputs"])
        self.assertIn("anchor_content", input_check["missing_required_inputs"])
        self.assertIn("content_type", input_check["provided_required_inputs"])
        self.assertIn("num_outputs", input_check["provided_required_inputs"])

        # Update action with custom instructions
        custom_instructions_v2 = [
            {"assets": None, "instruction": "Make it longer"},
            {"assets": None, "instruction": "add emoji"},
            {"assets": None, "instruction": "make it long enough"},
        ]

        custom_instructions_data = {
            "inputs": {
                "custom_instructions": TofuDataListHandler.convert_tofu_data_to_json(
                    convert_custom_instructions_v2_to_v3(
                        self.playbook_id, custom_instructions_v2
                    )
                )
            }
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            custom_instructions_data,
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action status again - custom_instructions should no longer be in missing_optional_inputs
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])

        # Verify input check fields after update
        input_check = response.data["details"]["input_check"]
        self.assertIn("missing_required_inputs", input_check)
        self.assertIn("missing_optional_inputs", input_check)
        self.assertIn("provided_required_inputs", input_check)

        # Verify custom_instructions is no longer in missing_optional_inputs
        self.assertNotIn("custom_instructions", input_check["missing_optional_inputs"])

        # Verify custom instructions are stored in content collection
        for content_group_id in content_groups:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_collection = content_group.content_group_params.get(
                "content_collection", {}
            )
            self.assertIn("custom_instructions", content_collection)
            stored_instructions = content_collection["custom_instructions"]
            self.assertEqual(len(stored_instructions), 3)
            self.assertEqual(stored_instructions[0]["instruction"], "Make it longer")
            self.assertEqual(stored_instructions[1]["instruction"], "add emoji")
            self.assertEqual(
                stored_instructions[2]["instruction"], "make it long enough"
            )
            self.assertIsNone(stored_instructions[0]["assets"])

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_repurpose_action_with_content_group_instructions_only(self):
        """Test repurpose action with content group instructions but no collection instructions"""

        # Create repurpose action with 3 outputs (no custom instructions initially)
        num_outputs = 3
        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Action With Content Group Instructions",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
        }

        # Create action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]
        outputs = response.data[-1]["outputs"]

        # Verify content groups are created
        self.assertIn("content_group", outputs)
        content_group_data = outputs["content_group"]
        content_groups = TofuDataListHandler.get_content_group_ids(content_group_data)
        self.assertEqual(len(content_groups), num_outputs)

        # Verify no custom instructions initially in any content group
        for content_group_id in content_groups:
            content_group = ContentGroup.objects.get(id=content_group_id)
            content_group_params = content_group.content_group_params
            self.assertIn("content_collection", content_group_params)
            content_collection = content_group_params["content_collection"]
            self.assertIn("custom_instructions", content_collection)
            self.assertEqual(content_collection["custom_instructions"], [])

        # Add custom instructions using the update_input API
        custom_instructions_data = {
            "data": [
                {
                    "custom_instruction": {
                        "assets": None,
                        "instruction": "Make it more formal",
                    }
                },
                {
                    "custom_instruction": {
                        "assets": None,
                        "instruction": "Add professional tone",
                    }
                },
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"custom_instructions": custom_instructions_data}},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action status - custom_instructions should not be in missing_optional_inputs
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        input_check = response.data["details"]["input_check"]
        self.assertIn("missing_optional_inputs", input_check)
        self.assertNotIn("custom_instructions", input_check["missing_optional_inputs"])

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
