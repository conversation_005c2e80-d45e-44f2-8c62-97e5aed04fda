# test_actions.py
import logging
import time
from unittest.mock import patch

from rest_framework import status

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...models import Action
from ...shared_definitions.protobuf.action_system_config import ActionStatusType
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    AnchorPrecheckResultLabel,
)
from .action_test_base import ActionTestBase, create_mock_check_asset_info


class ActionTestAnchorContentCRUD(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest

    def test_crud_full_action(self):
        """Test Create, Read, Update, Delete operations for Actions"""
        # Mock Celery task execution and AnchorContentPrecheckEvaluator.check_asset_info
        with (
            self.settings(
                CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                new=create_mock_check_asset_info(),
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
            ) as mock_llm_check,
        ):
            # Mock the evaluator's LLM check method to prevent real API calls
            mock_llm_check.return_value = {"label": "PASS", "comment": None}

            # Create action
            anchor_content_action_data = {
                "action_category": "ACTION_CATEGORY_USER_INPUT",
                "action_name": "Test Action",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "assets": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_asset_to_tofu_data(
                            self.asset_ids, []
                        )
                    ),
                },
            }
            response = self.client.post(
                "/api/action/", anchor_content_action_data, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            anchor_content_action_id = response.data[-1]["id"]

            # Execute action
            response = self.client.post(
                f"/api/action/{anchor_content_action_id}/execute/", {}, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            anchor_content_action = Action.objects.get(id=anchor_content_action_id)

            # check outputs
            self.assertIn("assets", anchor_content_action.outputs)
            asset_data = anchor_content_action.outputs["assets"]
            asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
            self.assertEqual(len(asset_ids), 3)
            self.assertEqual(len(asset_group_ids), 0)

            asset_precheck_results = {
                str(id): {
                    "label": AnchorPrecheckResultLabel.Name(
                        AnchorPrecheckResultLabel.ANCHOR_PRECHECK_RESULT_LABEL_PASS
                    )
                }
                for id in self.asset_ids
            }

            # check status
            expected_status = {
                "details": {
                    "stats": {"cnts_succ": 3},
                    "input_check": {"provided_required_inputs": ["assets"]},
                    "anchor_precheck_details": {
                        "asset_precheck_results": asset_precheck_results,
                    },
                },
                "message": "Action is complete",
                "status_type": ActionStatusType.Name(
                    ActionStatusType.ACTION_STATUS_TYPE_COMPLETE
                ),
            }
            self.assertEqual(anchor_content_action.status, expected_status)
