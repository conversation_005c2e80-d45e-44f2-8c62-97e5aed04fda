# test_actions.py
import logging
import time

from rest_framework import status

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...models import Action, Campaign, Content, ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionStatusType,
    PlatformType,
)
from .action_test_base import ActionTestBase


class ActionTestGen(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest

    def test_gen_action(self):
        # Create p13n action
        p13n_action_data = {
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create p13n action
        response = self.client.post("/api/action/", p13n_action_data, format="json")
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        p13n_action_id = response.data[-1]["id"]

        # First, test with invalid template (empty content_source_copy)
        invalid_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                "content_source": "Test Subject Line for Email",
                                "content_source_copy": "",  # Empty content_source_copy
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                                "slate_content_source": "Test Subject Line for Email",
                                "slate_content_source_copy": "",
                            },
                            "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                                "content_source": "Dear {{first_name}},\n\nThis is the email body content.",
                                "content_source_copy": "",  # Empty content_source_copy
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                                "slate_content_source": "Dear {{first_name}},\n\nThis is the email body content.",
                                "slate_content_source_copy": "",
                            },
                        },
                        "tone_reference": None,
                        "template_custom_instructions": [],
                        "follow_template_tone": True,
                        "follow_template_length": True,
                        "follow_template_core_message_and_key_point": True,
                    }
                }
            ]
        }

        invalid_template_tofu_data = TofuDataListHandler.parse_json_to_tofu_data(
            invalid_template_data
        )
        converted_invalid_template_data = TofuDataListHandler.convert_tofu_data_to_json(
            invalid_template_tofu_data
        )

        components_data = {
            "data": [
                {
                    "components": {
                        "components": {
                            "subject": {
                                "component_id": "subject",
                                "time_added": int(time.time()),
                                "component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                "component_meta_type": "TOFU_COMPONENT_META_TYPE_TEXT",
                                "component_custom_instructions": [],
                                "component_context_data": {
                                    "preceding_element": "",
                                    "succeeding_element": "",
                                },
                                "text": {"text": "Test Subject Line for Email"},
                            },
                            "body": {
                                "component_id": "body",
                                "time_added": int(time.time()),
                                "component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                                "component_meta_type": "TOFU_COMPONENT_META_TYPE_TEXT",
                                "component_custom_instructions": [],
                                "component_context_data": {
                                    "preceding_element": "",
                                    "succeeding_element": "",
                                },
                                "text": {
                                    "text": "Dear {{first_name}},\n\nThis is the email body content."
                                },
                            },
                        }
                    }
                }
            ]
        }

        components_tofu_data = TofuDataListHandler.parse_json_to_tofu_data(
            components_data
        )
        converted_components_data = TofuDataListHandler.convert_tofu_data_to_json(
            components_tofu_data
        )

        # Set invalid template and components
        invalid_action_data = {
            "inputs": {
                "template": converted_invalid_template_data,
                "components": converted_components_data,
            }
        }

        # Update p13n action with invalid template and components
        response = self.client.patch(
            f"/api/action/{p13n_action_id}/update_inputs/",
            invalid_action_data,
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify that the action status is not READY due to invalid template
        self.assertNotEqual(
            response.data["status"]["status_type"],
            ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
        )

        # Now test with valid template
        template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                "content_source": "Test Subject Line for Email",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?key=test.txt",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                                "slate_content_source": "Test Subject Line for Email",
                                "slate_content_source_copy": "/api/web/storage/s3-presigned-url?key=test.txt",
                            },
                            "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                                "content_source": "Dear {{first_name}},\n\nThis is the email body content.",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?key=test.txt",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                                "slate_content_source": "Dear {{first_name}},\n\nThis is the email body content.",
                                "slate_content_source_copy": "/api/web/storage/s3-presigned-url?key=test.txt",
                            },
                        },
                        "tone_reference": None,
                        "template_custom_instructions": [],
                        "follow_template_tone": True,
                        "follow_template_length": True,
                        "follow_template_core_message_and_key_point": True,
                    }
                }
            ]
        }

        template_tofu_data = TofuDataListHandler.parse_json_to_tofu_data(template_data)
        converted_template_data = TofuDataListHandler.convert_tofu_data_to_json(
            template_tofu_data
        )

        # Set valid template and components
        valid_action_data = {
            "inputs": {
                "template": converted_template_data,
                "components": converted_components_data,
            }
        }

        # Update p13n action with valid template and components
        response = self.client.patch(
            f"/api/action/{p13n_action_id}/update_inputs/",
            valid_action_data,
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify that the action status is now READY with valid template
        self.assertEqual(
            response.data["status"]["status_type"],
            ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
        )

        content = Content.objects.select_related("content_group").get(
            content_group__action=p13n_action_id
        )
        # Additional assertions for the full status
        self.assertEqual(
            response.data["status"],
            {
                "status_type": ActionStatusType.Name(
                    ActionStatusType.ACTION_STATUS_TYPE_READY
                ),
                "details": {
                    "stats": {"cnts_not_started": 1},
                    "input_check": {
                        "missing_optional_inputs": ["custom_instructions"],
                        "provided_required_inputs": [
                            "components",
                            "template",
                            "content_type",
                            "targets",
                        ],
                    },
                    "gen_personalize_details": {
                        "gen_status": {
                            f"{content.id}": {"target_name": "Veterinary Clinics:Heska"}
                        }
                    },
                },
            },
        )

        # response = self.client.post(
        #     f"/api/action/{p13n_action_id}/execute/", format="json"
        # )
        # self.assertEqual(response.status_code, status.HTTP_200_OK)

        # self.assertEqual(response.data["status"]["status_type"], ActionStatusType.ACTION_STATUS_TYPE_RUNNING)

        # # Create export action
        # export_action_data = {
        #     "action_category": "ACTION_CATEGORY_EXPORT",
        #     "action_name": "Test Action",
        #     "campaign": self.campaign_id,
        #     "playbook": self.playbook_id,
        #     "inputs": {
        #         "content_type": TofuDataListHandler.convert_tofu_data_to_json(
        #             TofuDataListHandler.convert_content_type_to_tofu_data(
        #                 "Email - SDR"
        #             )
        #         ),
        #         "platform_type": TofuDataListHandler.convert_tofu_data_to_json(
        #             TofuDataListHandler.convert_platform_type_to_tofu_data(
        #                 PlatformType.PLATFORM_TYPE_HUBSPOT
        #             )
        #         ),
        #     },
        #     "incoming_edges": [
        #         {
        #             "from_action": p13n_action_id,
        #             "config": {
        #                 "output_name_from_incoming_action": "content_group",
        #                 "input_name_to_outgoing_action": "template",
        #             },
        #         }
        #     ],
        # }

        # # Create export action
        # response = self.client.post(
        #     "/api/action/", export_action_data, format="json"
        # )
        # if response.status_code != status.HTTP_201_CREATED:
        #     logging.error("Error response: %s", response.content)
        # self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # export_action_id = response.data[-1]["id"]

    def test_p13n_action_target_handling(self):
        # 1. Create P13N action without targets
        p13n_action_data = {
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test Target Handling",
            "campaign": self.campaign_id_no_target,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                # Adding template with valid content_source_copy
                "template": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.parse_json_to_tofu_data(
                        {
                            "data": [
                                {
                                    "template": {
                                        "template_fields": {
                                            "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                                "content_source": "Test Subject",
                                                "content_source_copy": "/api/web/storage/s3-presigned-url?key=test.txt",
                                                "content_source_format": "Text",
                                                "content_source_upload_method": "Text",
                                                "slate_content_source": "Test Subject",
                                                "slate_content_source_copy": "/api/web/storage/s3-presigned-url?key=test.txt",
                                            }
                                        },
                                        "tone_reference": None,
                                        "template_custom_instructions": [],
                                        "follow_template_tone": True,
                                        "follow_template_length": True,
                                        "follow_template_core_message_and_key_point": True,
                                    }
                                }
                            ]
                        }
                    )
                ),
                # Adding components with valid structure
                "components": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.parse_json_to_tofu_data(
                        {
                            "data": [
                                {
                                    "components": {
                                        "components": {
                                            "subject": {
                                                "component_id": "subject",
                                                "component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                                "component_meta_type": "TOFU_COMPONENT_META_TYPE_TEXT",
                                                "component_custom_instructions": [],
                                                "text": {"text": "Test Subject"},
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    )
                ),
            },
        }

        # Create P13N action
        response = self.client.post("/api/action/", p13n_action_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        p13n_action_id = response.data[-1]["id"]
        campaign_id = response.data[-1]["campaign"]

        # 2. Check status - should be missing target
        response = self.client.get(f"/api/action/{p13n_action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify status shows missing target
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "missing_required_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "targets",
            response.data["details"]["input_check"]["missing_required_inputs"],
        )

        # 3. Update the campaign with target
        Campaign.objects.filter(id=self.campaign_id_no_target).update(
            campaign_params={
                "is_campaign_v3": True,
                "targets": [{"Veterinary Clinics": ["Heska"]}],
                "campaign_status": {},
            },
        )

        # 4. Check status again - should now have target and be ready
        response = self.client.get(f"/api/action/{p13n_action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify target no longer in missing inputs
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertNotIn(
            "missing_required_inputs", response.data["details"]["input_check"]
        )

        # Verify target is in provided inputs
        self.assertIn(
            "provided_required_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "targets",
            response.data["details"]["input_check"]["provided_required_inputs"],
        )
