# test_actions.py
import logging

from rest_framework import status

from ...actions.tofu_data_wrapper import TofuDataListHandler
from .action_test_base import ActionTestBase


class ActionTestP13nCRUD(ActionTestBase):
    def setUp(self):
        super().setUp()

    def test_crud_p13n_action(self):
        """Test Create, Read, Update, Delete operations for Actions"""

        self.action_base_data = {
            "action_type": "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create action
        response = self.client.post(
            "/api/action/", self.action_base_data, format="json"
        )
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # Get action status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Update action name
        response = self.client.patch(
            f"/api/action/{action_id}/update_name/",
            {"action_name": "Updated Action Name"},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Update action inputs
        # response = self.client.patch(f'/api/action/{action_id}/update_inputs/', {
        #     'inputs': {
        #         'template': 'updated template',
        #         'components': {'updated_key': 'updated_value'}
        #     }
        # }, format='json')
        # self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action definition
        response = self.client.get(f"/api/action/{action_id}/definition/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Delete action
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_p13n_action_with_default_template(self):
        """Test that personalization action with default template input returns appropriate status"""

        p13n_action_data = {
            "action_type": "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test P13n Action With Default Template",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create action
        response = self.client.post("/api/action/", p13n_action_data, format="json")
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # Get action status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify template is in missing_required_inputs (unlike repurpose where it's optional)
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "missing_required_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "template",
            response.data["details"]["input_check"]["missing_required_inputs"],
        )

        # Update action inputs with a default template missing content_source_copy
        default_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_UNSPECIFIED": {
                                "content_source_upload_method": "Link",
                                "content_source_format": "Html",
                                # Note: content_source_copy is intentionally missing to test validation
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": default_template_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action status again - should indicate template is still missing/invalid
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that template is still in missing_required_inputs even after we tried to provide it
        # This indicates the template was invalid and not accepted
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "missing_required_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "template",
            response.data["details"]["input_check"]["missing_required_inputs"],
        )

        # Now update with a valid template
        valid_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_UNSPECIFIED": {
                                "content_source": "140454503.json",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?file=b13eb84a-75c6-b8b2-dfd5-980a36e473f8.json&fileType=application/json&directory=tofu-uploaded-files",
                                "content_source_format": "Html",
                                "content_source_upload_method": "Link",
                                "slate_content_source": "140454503.json",
                                "slate_content_source_copy": "/api/web/storage/s3-presigned-url?file=d913f71d-139a-2bf3-7ff9-a16305e5d09b.json&fileType=application/json&directory=tofu-uploaded-files",
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": valid_template_data, "custom_instructions": {}}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action status again - template should no longer be in missing inputs
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify that template is no longer in missing_required_inputs
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        if "missing_required_inputs" in response.data["details"]["input_check"]:
            self.assertNotIn(
                "template",
                response.data["details"]["input_check"]["missing_required_inputs"],
            )

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_p13n_action_component_custom_instructions(self):
        """Test that component custom instructions are properly set and retrieved"""

        # 1. Create a personalization action
        p13n_action_data = {
            "action_type": "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test P13n Action Component Instructions",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create action
        response = self.client.post("/api/action/", p13n_action_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # 2. Update with a valid template
        valid_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                                "content_source": "119273921.json",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?file=39809f2a-15de-d542-904b-6f2752af9ea5.json&fileType=application/json&directory=tofu-uploaded-files",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                            },
                            "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                "content_source": "",
                                "content_source_copy": "",
                                "content_source_format": "",
                                "content_source_upload_method": "",
                            },
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": valid_template_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 3. Add components without custom instructions
        components_data = {
            "data": [
                {
                    "components": {
                        "components": {
                            "Dg849mNR_AznCic4": {
                                "time_added": "1739921089658",
                                "component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                                "component_meta_type": "TOFU_COMPONENT_META_TYPE_TEXT",
                                "component_custom_instructions": {},  # Empty custom instructions
                                "component_context_data": {
                                    "preceding_element": "Hi [Name], Your work on AI and big data at [Company] is impressive. With your experience in Apache Spark and Hadoop, you know the hurdles of managing large-scale data systems. ",
                                    "succeeding_element": " We also offer ways to turn AI models into practical products faster. This could help you create more customer-facing solutions that solve real problems. Would you like to chat about how we could help you build more scalable AI systems? I'm happy to share some quick ideas if you have 15 minutes this week. Best, [Your Name]",
                                    "html": {"html_tag_index": 0},
                                },
                                "text": {
                                    "text": "We've helped teams like yours solve scalability issues while keeping costs down. Our tools make it easier to handle growing data volumes and real-time processing needs."
                                },
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"components": components_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 4. Check that custom_instructions is not set in get_status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify custom_instructions is in missing_optional_inputs initially
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "missing_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "custom_instructions",
            response.data["details"]["input_check"]["missing_optional_inputs"],
            "Custom instructions should be in missing_optional_inputs initially",
        )

        # 5. Update components with custom instructions and add custom_instructions to inputs
        components_with_instructions_data = {
            "data": [
                {
                    "components": {
                        "components": {
                            "Dg849mNR_AznCic4": {
                                "time_added": "1739921089658",
                                "component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                                "component_meta_type": "TOFU_COMPONENT_META_TYPE_TEXT",
                                "component_custom_instructions": {
                                    "data": [
                                        {
                                            "custom_instruction": {
                                                "assets": {},
                                                "instruction": "make it longer",
                                            }
                                        }
                                    ]
                                },
                                "component_context_data": {
                                    "preceding_element": "Hi [Name], Your work on AI and big data at [Company] is impressive. With your experience in Apache Spark and Hadoop, you know the hurdles of managing large-scale data systems. ",
                                    "succeeding_element": " We also offer ways to turn AI models into practical products faster. This could help you create more customer-facing solutions that solve real problems. Would you like to chat about how we could help you build more scalable AI systems? I'm happy to share some quick ideas if you have 15 minutes this week. Best, [Your Name]",
                                    "html": {"html_tag_index": 0},
                                },
                                "text": {
                                    "text": "We've helped teams like yours solve scalability issues while keeping costs down. Our tools make it easier to handle growing data volumes and real-time processing needs."
                                },
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {
                "inputs": {
                    "components": components_with_instructions_data,
                }
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 6. Check that custom_instructions is now set in get_status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify custom_instructions is now set and contains the instruction
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "provided_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "custom_instructions",
            response.data["details"]["input_check"]["provided_optional_inputs"],
            "Custom instructions should be in provided_optional_inputs after being provided",
        )

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_p13n_action_combined_custom_instructions(self):
        """Test that both component custom instructions and standalone custom instructions are properly combined"""

        # 1. Create a personalization action
        p13n_action_data = {
            "action_type": "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test P13n Action Combined Instructions",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create action
        response = self.client.post("/api/action/", p13n_action_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # 2. Add a valid template
        valid_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                                "content_source": "119273921.json",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?file=39809f2a-15de-d542-904b-6f2752af9ea5.json&fileType=application/json&directory=tofu-uploaded-files",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                            },
                            "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                "content_source": "",
                                "content_source_copy": "",
                                "content_source_format": "",
                                "content_source_upload_method": "",
                            },
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": valid_template_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 3. Add components with custom instructions
        components_with_instructions_data = {
            "data": [
                {
                    "components": {
                        "components": {
                            "Dg849mNR_AznCic4": {
                                "time_added": "1739921089658",
                                "component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                                "component_meta_type": "TOFU_COMPONENT_META_TYPE_TEXT",
                                "component_custom_instructions": {
                                    "data": [
                                        {
                                            "custom_instruction": {
                                                "assets": {},
                                                "instruction": "make it longer",
                                            }
                                        }
                                    ]
                                },
                                "component_context_data": {
                                    "preceding_element": "Hi [Name], Your work on AI and big data at [Company] is impressive. With your experience in Apache Spark and Hadoop, you know the hurdles of managing large-scale data systems. ",
                                    "succeeding_element": " We also offer ways to turn AI models into practical products faster. This could help you create more customer-facing solutions that solve real problems. Would you like to chat about how we could help you build more scalable AI systems? I'm happy to share some quick ideas if you have 15 minutes this week. Best, [Your Name]",
                                    "html": {"html_tag_index": 0},
                                },
                                "text": {
                                    "text": "We've helped teams like yours solve scalability issues while keeping costs down. Our tools make it easier to handle growing data volumes and real-time processing needs."
                                },
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {
                "inputs": {
                    "components": components_with_instructions_data,
                }
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 4. Add standalone custom instructions
        standalone_custom_instructions_data = {
            "data": [
                {
                    "custom_instruction": {
                        "assets": {},
                        "instruction": "make it more formal",
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"custom_instructions": standalone_custom_instructions_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 5. Check that custom_instructions is now set in get_status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify custom_instructions is now set and contains the instruction
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "provided_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "custom_instructions",
            response.data["details"]["input_check"]["provided_optional_inputs"],
            "Custom instructions should be in provided_optional_inputs after being provided",
        )

        # 6. Get the action data to verify both instructions are combined
        response = self.client.get(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_p13n_action_no_components(self):
        """Test personalization action with a valid template but no components"""

        # 1. Create a personalization action
        p13n_action_data = {
            "action_type": "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test P13n Action No Components",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create action
        response = self.client.post("/api/action/", p13n_action_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # 2. Add a valid template
        valid_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                                "content_source": "119273921.json",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?file=39809f2a-15de-d542-904b-6f2752af9ea5.json&fileType=application/json&directory=tofu-uploaded-files",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                            },
                            "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                "content_source": "",
                                "content_source_copy": "",
                                "content_source_format": "",
                                "content_source_upload_method": "",
                            },
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": valid_template_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 3. Add standalone custom instructions without any components
        standalone_custom_instructions_data = {
            "data": [
                {
                    "custom_instruction": {
                        "assets": {},
                        "instruction": "make the email more formal and professional",
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"custom_instructions": standalone_custom_instructions_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 4. Check status to verify custom instructions are set without components
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify custom_instructions is set in provided_optional_inputs
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "provided_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "custom_instructions",
            response.data["details"]["input_check"]["provided_optional_inputs"],
            "Custom instructions should be in provided_optional_inputs even without components",
        )

        # Verify components is in missing_required_inputs (not optional as originally expected)
        self.assertIn(
            "missing_required_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "components",
            response.data["details"]["input_check"]["missing_required_inputs"],
            "Components should be in missing_required_inputs",
        )

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_p13n_action_standalone_custom_instructions(self):
        """Test personalization action with only standalone custom instructions (no component-specific instructions)"""

        # 1. Create a personalization action
        p13n_action_data = {
            "action_type": "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_PERSONALIZE",
            "action_name": "Test P13n Action Standalone Instructions",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        # Create action
        response = self.client.post("/api/action/", p13n_action_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # 2. Add a valid template
        valid_template_data = {
            "data": [
                {
                    "template": {
                        "template_fields": {
                            "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                                "content_source": "119273921.json",
                                "content_source_copy": "/api/web/storage/s3-presigned-url?file=39809f2a-15de-d542-904b-6f2752af9ea5.json&fileType=application/json&directory=tofu-uploaded-files",
                                "content_source_format": "Text",
                                "content_source_upload_method": "Text",
                            },
                            "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                                "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                                "content_source": "",
                                "content_source_copy": "",
                                "content_source_format": "",
                                "content_source_upload_method": "",
                            },
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"template": valid_template_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 3. Add components without custom instructions
        components_without_instructions_data = {
            "data": [
                {
                    "components": {
                        "components": {
                            "Dg849mNR_AznCic4": {
                                "time_added": "1739921089658",
                                "component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                                "component_meta_type": "TOFU_COMPONENT_META_TYPE_TEXT",
                                "component_custom_instructions": {},  # Empty custom instructions
                                "component_context_data": {
                                    "preceding_element": "Hi [Name], Your work on AI and big data at [Company] is impressive. With your experience in Apache Spark and Hadoop, you know the hurdles of managing large-scale data systems. ",
                                    "succeeding_element": " We also offer ways to turn AI models into practical products faster. This could help you create more customer-facing solutions that solve real problems. Would you like to chat about how we could help you build more scalable AI systems? I'm happy to share some quick ideas if you have 15 minutes this week. Best, [Your Name]",
                                    "html": {"html_tag_index": 0},
                                },
                                "text": {
                                    "text": "We've helped teams like yours solve scalability issues while keeping costs down. Our tools make it easier to handle growing data volumes and real-time processing needs."
                                },
                            }
                        }
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {
                "inputs": {
                    "components": components_without_instructions_data,
                }
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 4. Add only standalone custom instructions
        standalone_custom_instructions_data = {
            "data": [
                {
                    "custom_instruction": {
                        "assets": {},
                        "instruction": "make the entire email more formal and professional",
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"custom_instructions": standalone_custom_instructions_data}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 5. Check status to verify only standalone custom instructions are set
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify custom_instructions is set in provided_optional_inputs
        self.assertIn("details", response.data)
        self.assertIn("input_check", response.data["details"])
        self.assertIn(
            "provided_optional_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "custom_instructions",
            response.data["details"]["input_check"]["provided_optional_inputs"],
            "Custom instructions should be in provided_optional_inputs",
        )

        # Check if components is in provided_required_inputs instead of provided_optional_inputs
        self.assertIn(
            "provided_required_inputs", response.data["details"]["input_check"]
        )
        self.assertIn(
            "components",
            response.data["details"]["input_check"]["provided_required_inputs"],
            "Components should be in provided_required_inputs",
        )

        # 6. Get the action data to verify only standalone instructions are present
        response = self.client.get(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Clean up
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
