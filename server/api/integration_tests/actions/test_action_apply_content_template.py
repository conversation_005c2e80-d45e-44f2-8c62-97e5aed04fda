import logging
from unittest.mock import MagicMock, patch
from urllib.parse import parse_qs, urlparse

from google.protobuf.json_format import MessageToDict
from rest_framework import status

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...models import Action, ContentTemplate
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionStatusType
from .action_test_base import ActionTestBase


class ActionTestApplyContentTemplate(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest and ActionTestBase

    def test_apply_content_template(self):
        """Test applying a content template to a repurpose action"""
        # Mock the precheck evaluator to avoid dependency on external service

        # Mock S3 copy operations
        def mock_copy_s3_file(source_path):
            # Extract the file name from the source path
            parsed_url = urlparse(source_path)
            query_params = parse_qs(parsed_url.query)
            source_file = query_params.get("file", [""])[0]
            directory = query_params.get("directory", ["tofu-uploaded-files"])[0]

            # Generate a new file name with a UUID
            new_file = f"copied_{source_file}"

            # Return a new presigned URL with the copied file name
            return f"/api/web/storage/s3-presigned-url?file={new_file}&fileType=application/json&directory={directory}"

        # Mock boto3 client
        mock_s3_client = MagicMock()
        mock_s3_client.copy_object.return_value = {}

        with (
            self.settings(
                CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
            ) as mock_llm_check,
            patch(
                "api.utils.copy_s3_file",
                side_effect=mock_copy_s3_file,
            ),
            patch(
                "boto3.client",
                return_value=mock_s3_client,
            ),
            patch.dict(
                "os.environ",
                {
                    "AWS_ACCESS_KEY_ID": "testing",
                    "AWS_SECRET_ACCESS_KEY": "testing",
                    "AWS_SECURITY_TOKEN": "testing",
                    "AWS_SESSION_TOKEN": "testing",
                    "AWS_DEFAULT_REGION": "us-east-1",
                },
            ),
        ):
            # Mock the evaluator's LLM check method to prevent real API calls
            mock_llm_check.return_value = {"label": "PASS", "comment": None}

            # 1. Create anchor content action
            anchor_content_action_data = {
                "action_category": "ACTION_CATEGORY_USER_INPUT",
                "action_name": "Test Anchor Content",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "assets": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_asset_to_tofu_data(
                            self.asset_ids, []
                        )
                    ),
                },
            }
            response = self.client.post(
                "/api/action/", anchor_content_action_data, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            anchor_content_action_id = response.data[-1]["id"]

            # Execute the anchor content action
            response = self.client.post(
                f"/api/action/{anchor_content_action_id}/execute/", {}, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # 2. Create repurpose action
            num_outputs = 1
            repurpose_action_data = {
                "action_category": "ACTION_CATEGORY_REPURPOSE",
                "action_name": "Email - SDR - text",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_content_type_to_tofu_data(
                            "Email - SDR"
                        )
                    ),
                    "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                    ),
                },
                "incoming_edges": [
                    {
                        "from_action": anchor_content_action_id,
                        "config": {
                            "output_name_from_incoming_action": "assets",
                            "input_name_to_outgoing_action": "anchor_content",
                        },
                    }
                ],
            }

            # Create repurpose action
            response = self.client.post(
                "/api/action/", repurpose_action_data, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            repurpose_action_id = response.data[-1]["id"]

            # 3. Create content template
            template_data = {
                "template_fields": {
                    "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                        "content_source": "3537.json",
                        "content_source_copy": "/api/web/storage/s3-presigned-url?file=a10586c3-7d75-17b8-9317-2f2ef98da0df.json&fileType=application/json&directory=tofu-uploaded-files",
                        "slate_content_source": "3537.json",
                        "content_source_format": "Text",
                        "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                        "slate_content_source_copy": "/api/web/storage/s3-presigned-url?file=8f0ce4f2-d54d-9a16-9b39-a482950d7ca5.json&fileType=application/json&directory=tofu-uploaded-files",
                        "content_source_upload_method": "Text",
                    },
                    "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                        "content_source": "",
                        "content_source_copy": "",
                        "content_source_format": "Text",
                        "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                        "content_source_upload_method": "Text",
                    },
                },
                "tone_reference_v2": {
                    "tone_assets": {
                        "data": [{"asset": {"asset_id": self.asset_ids[0]}}]
                    },
                    "tone_analysis": {
                        "tone_analysis": "- **Type of Writing Style**\n  * Promotional and informative\n  * The author's style is primarily promotional, aiming to market the Tofu platform while providing detailed information about its features, benefits, and use cases. The text is designed to persuade the audience to adopt the product.",
                        "tone_analysis_summary": "Promotional, Informative, Conversational, Persuasive, Technical",
                    },
                },
            }

            # Create the content template
            content_template = ContentTemplate.objects.create(
                playbook=self.playbook,
                creator=self.user,
                name="Test Email SDR Template",
                template_data=template_data,
                content_types=["Email - SDR"],
                default_content_types=["Email - SDR"],
            )

            # 4. Apply the content template to the repurpose action
            response = self.client.post(
                f"/api/action/{repurpose_action_id}/apply_content_template/",
                {"content_template_id": content_template.id},
                format="json",
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # 5. Check if the template was applied correctly
            repurpose_action = Action.objects.get(id=repurpose_action_id)
            self.assertIn("template", repurpose_action.inputs)

            # Check that template fields match what we expect
            template_data_from_action = repurpose_action.inputs["template"]
            self.assertIsNotNone(template_data_from_action)

            # Extract the template data using the proper methods
            tofu_data_list = TofuDataListHandler.parse_json_to_tofu_data(
                template_data_from_action
            )
            template_proto = TofuDataListHandler.get_template(tofu_data_list)

            # Convert the protobuf message to a Python dictionary for easier assertions
            template_dict = MessageToDict(
                template_proto, preserving_proto_field_name=True
            )

            # Verify the template was properly applied
            self.assertIn("template_fields", template_dict)
            self.assertIn(
                "TOFU_COMPONENT_TYPE_EMAIL_BODY", template_dict["template_fields"]
            )

            # Verify specific template field values
            email_body_field = template_dict["template_fields"][
                "TOFU_COMPONENT_TYPE_EMAIL_BODY"
            ]
            self.assertIsNotNone(email_body_field["content_source"])
            self.assertTrue(email_body_field["content_source"].endswith(".json"))
            self.assertIsNotNone(email_body_field["content_source_copy"])
            self.assertTrue(
                email_body_field["content_source_copy"].startswith(
                    "/api/web/storage/s3-presigned-url"
                )
            )
            self.assertIsNotNone(email_body_field["slate_content_source"])
            self.assertTrue(email_body_field["slate_content_source"].endswith(".json"))
            self.assertIsNotNone(email_body_field["slate_content_source_copy"])
            self.assertTrue(
                email_body_field["slate_content_source_copy"].startswith(
                    "/api/web/storage/s3-presigned-url"
                )
            )
            self.assertEqual(email_body_field["content_source_format"], "Text")
            self.assertEqual(
                email_body_field["template_component_type"],
                "TOFU_COMPONENT_TYPE_EMAIL_BODY",
            )
            self.assertEqual(email_body_field["content_source_upload_method"], "Text")

            # Verify email subject field
            email_subject_field = template_dict["template_fields"][
                "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT"
            ]
            self.assertEqual(email_subject_field["content_source"], "")
            self.assertEqual(email_subject_field["content_source_copy"], "")
            self.assertEqual(email_subject_field["content_source_format"], "Text")
            self.assertEqual(
                email_subject_field["template_component_type"],
                "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
            )
            self.assertEqual(
                email_subject_field["content_source_upload_method"], "Text"
            )

            # Verify tone reference data
            self.assertIn("tone_reference_v2", template_dict)
            tone_reference = template_dict["tone_reference_v2"]

            # Verify tone assets
            self.assertIn("tone_assets", tone_reference)
            self.assertIn("data", tone_reference["tone_assets"])
            self.assertEqual(len(tone_reference["tone_assets"]["data"]), 1)
            self.assertIn("asset", tone_reference["tone_assets"]["data"][0])
            self.assertIn("asset_id", tone_reference["tone_assets"]["data"][0]["asset"])
            self.assertIsInstance(
                tone_reference["tone_assets"]["data"][0]["asset"]["asset_id"],
                (int, str),
            )

            # Verify tone analysis
            self.assertIn("tone_analysis", tone_reference)
            self.assertIn("tone_analysis", tone_reference["tone_analysis"])
            self.assertIn("tone_analysis_summary", tone_reference["tone_analysis"])
            self.assertIsInstance(tone_reference["tone_analysis"]["tone_analysis"], str)
            self.assertIsInstance(
                tone_reference["tone_analysis"]["tone_analysis_summary"], str
            )

            # Check the repurpose action's status
            response = self.client.get(f"/api/action/{repurpose_action_id}/get_status/")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Verify the status_type is READY
            self.assertEqual(
                response.data["status_type"],
                ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
            )

            # Verify details structure
            details = response.data["details"]

            # Verify stats
            self.assertIn("stats", details)
            self.assertEqual(details["stats"].get("cnts_not_started"), 1)

            # Verify input_check
            self.assertIn("input_check", details)
            input_check = details["input_check"]

            # The exact values may vary, but we want to ensure we have at least these
            self.assertIn("missing_optional_inputs", input_check)
            self.assertIn("custom_instructions", input_check["missing_optional_inputs"])

            self.assertIn("provided_optional_inputs", input_check)
            self.assertIn("template", input_check["provided_optional_inputs"])

            self.assertIn("provided_required_inputs", input_check)
            required_inputs = ["anchor_content", "content_type", "num_outputs"]
            for input_name in required_inputs:
                self.assertIn(input_name, input_check["provided_required_inputs"])

            # Verify gen_repurpose_details
            self.assertIn("gen_repurpose_details", details)
            self.assertIn("gen_status", details["gen_repurpose_details"])

            # Note: The exact content IDs will differ in each test run, so we only verify structure
            gen_status = details["gen_repurpose_details"]["gen_status"]
            self.assertEqual(len(gen_status), 1)  # Should have one content group ID

            content_group_id = list(gen_status.keys())[0]
            self.assertIn("content_group_gen_status", gen_status[content_group_id])

            content_group_gen_status = gen_status[content_group_id][
                "content_group_gen_status"
            ]
            self.assertEqual(
                len(content_group_gen_status), 1
            )  # Should have one content ID

            content_id = list(content_group_gen_status.keys())[0]
            self.assertIn("target_name", content_group_gen_status[content_id])
            self.assertTrue(
                content_group_gen_status[content_id]["target_name"].startswith(
                    "Email - SDR"
                )
            )

    def test_apply_content_template_to_p13n_action(self):
        """Test applying a content template to a personalization action"""
        # Mock the precheck evaluator to avoid dependency on external service

        # Mock S3 copy operations
        def mock_copy_s3_file(source_path):
            # Extract the file name from the source path
            parsed_url = urlparse(source_path)
            query_params = parse_qs(parsed_url.query)
            source_file = query_params.get("file", [""])[0]
            directory = query_params.get("directory", ["tofu-uploaded-files"])[0]

            # Generate a new file name with a UUID
            new_file = f"copied_{source_file}"

            # Return a new presigned URL with the copied file name
            return f"/api/web/storage/s3-presigned-url?file={new_file}&fileType=application/json&directory={directory}"

        # Mock boto3 client
        mock_s3_client = MagicMock()
        mock_s3_client.copy_object.return_value = {}

        with (
            self.settings(
                CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
            ) as mock_llm_check,
            patch(
                "api.utils.copy_s3_file",
                side_effect=mock_copy_s3_file,
            ),
            patch(
                "boto3.client",
                return_value=mock_s3_client,
            ),
            patch.dict(
                "os.environ",
                {
                    "AWS_ACCESS_KEY_ID": "testing",
                    "AWS_SECRET_ACCESS_KEY": "testing",
                    "AWS_SECURITY_TOKEN": "testing",
                    "AWS_SESSION_TOKEN": "testing",
                    "AWS_DEFAULT_REGION": "us-east-1",
                },
            ),
        ):
            # Mock the evaluator's LLM check method to prevent real API calls
            mock_llm_check.return_value = {"label": "PASS", "comment": None}

            # 1. Create personalization action
            p13n_action_data = {
                "action_type": "ACTION_TYPE_PERSONALIZE_EMAIL_SDR",
                "action_category": "ACTION_CATEGORY_PERSONALIZE",
                "action_name": "Test P13n Action With Template",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_content_type_to_tofu_data(
                            "Email - SDR"
                        )
                    ),
                },
            }

            # Create action
            response = self.client.post("/api/action/", p13n_action_data, format="json")
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            p13n_action_id = response.data[-1]["id"]
            logging.error(f"Created P13n action with ID: {p13n_action_id}")

            # 2. Create content template
            template_data = {
                "template_fields": {
                    "TOFU_COMPONENT_TYPE_EMAIL_BODY": {
                        "content_source": "3537.json",
                        "content_source_copy": "/api/web/storage/s3-presigned-url?file=a10586c3-7d75-17b8-9317-2f2ef98da0df.json&fileType=application/json&directory=tofu-uploaded-files",
                        "slate_content_source": "3537.json",
                        "content_source_format": "Text",
                        "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_BODY",
                        "slate_content_source_copy": "/api/web/storage/s3-presigned-url?file=8f0ce4f2-d54d-9a16-9b39-a482950d7ca5.json&fileType=application/json&directory=tofu-uploaded-files",
                        "content_source_upload_method": "Text",
                    },
                    "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT": {
                        "content_source": "",
                        "content_source_copy": "",
                        "content_source_format": "Text",
                        "template_component_type": "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
                        "content_source_upload_method": "Text",
                    },
                },
                "tone_reference_v2": {
                    "tone_assets": {
                        "data": [{"asset": {"asset_id": self.asset_ids[0]}}]
                    },
                    "tone_analysis": {
                        "tone_analysis": "- **Type of Writing Style**\n  * Promotional and informative\n  * The author's style is primarily promotional, aiming to market the Tofu platform while providing detailed information about its features, benefits, and use cases. The text is designed to persuade the audience to adopt the product.",
                        "tone_analysis_summary": "Promotional, Informative, Conversational, Persuasive, Technical",
                    },
                },
            }

            # Create the content template
            content_template = ContentTemplate.objects.create(
                playbook=self.playbook,
                creator=self.user,
                name="Test Email SDR Template",
                template_data=template_data,
                content_types=["Email - SDR"],
                default_content_types=["Email - SDR"],
            )

            # 3. Apply the content template to the personalization action
            response = self.client.post(
                f"/api/action/{p13n_action_id}/apply_content_template/",
                {"content_template_id": content_template.id},
                format="json",
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # 4. Check if the template was applied correctly
            p13n_action = Action.objects.get(id=p13n_action_id)
            self.assertIn("template", p13n_action.inputs)

            # Check that template fields match what we expect
            template_data_from_action = p13n_action.inputs["template"]
            self.assertIsNotNone(template_data_from_action)

            # Extract the template data using the proper methods
            tofu_data_list = TofuDataListHandler.parse_json_to_tofu_data(
                template_data_from_action
            )
            template_proto = TofuDataListHandler.get_template(tofu_data_list)

            # Convert the protobuf message to a Python dictionary for easier assertions
            template_dict = MessageToDict(
                template_proto, preserving_proto_field_name=True
            )

            # Verify the template was properly applied
            self.assertIn("template_fields", template_dict)
            self.assertIn(
                "TOFU_COMPONENT_TYPE_EMAIL_BODY", template_dict["template_fields"]
            )

            # Verify specific template field values
            email_body_field = template_dict["template_fields"][
                "TOFU_COMPONENT_TYPE_EMAIL_BODY"
            ]
            self.assertIsNotNone(email_body_field["content_source"])
            self.assertTrue(email_body_field["content_source"].endswith(".json"))
            self.assertIsNotNone(email_body_field["content_source_copy"])
            self.assertTrue(
                email_body_field["content_source_copy"].startswith(
                    "/api/web/storage/s3-presigned-url"
                )
            )
            self.assertIsNotNone(email_body_field["slate_content_source"])
            self.assertTrue(email_body_field["slate_content_source"].endswith(".json"))
            self.assertIsNotNone(email_body_field["slate_content_source_copy"])
            self.assertTrue(
                email_body_field["slate_content_source_copy"].startswith(
                    "/api/web/storage/s3-presigned-url"
                )
            )
            self.assertEqual(email_body_field["content_source_format"], "Text")
            self.assertEqual(
                email_body_field["template_component_type"],
                "TOFU_COMPONENT_TYPE_EMAIL_BODY",
            )
            self.assertEqual(email_body_field["content_source_upload_method"], "Text")

            # Verify email subject field
            email_subject_field = template_dict["template_fields"][
                "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT"
            ]
            self.assertEqual(email_subject_field["content_source"], "")
            self.assertEqual(email_subject_field["content_source_copy"], "")
            self.assertEqual(email_subject_field["content_source_format"], "Text")
            self.assertEqual(
                email_subject_field["template_component_type"],
                "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
            )
            self.assertEqual(
                email_subject_field["content_source_upload_method"], "Text"
            )

            # Verify tone reference data
            self.assertIn("tone_reference_v2", template_dict)
            tone_reference = template_dict["tone_reference_v2"]

            # Verify tone assets
            self.assertIn("tone_assets", tone_reference)
            self.assertIn("data", tone_reference["tone_assets"])
            self.assertEqual(len(tone_reference["tone_assets"]["data"]), 1)
            self.assertIn("asset", tone_reference["tone_assets"]["data"][0])
            self.assertIn("asset_id", tone_reference["tone_assets"]["data"][0]["asset"])
            self.assertIsInstance(
                tone_reference["tone_assets"]["data"][0]["asset"]["asset_id"],
                (int, str),
            )

            # Verify tone analysis
            self.assertIn("tone_analysis", tone_reference)
            self.assertIn("tone_analysis", tone_reference["tone_analysis"])
            self.assertIn("tone_analysis_summary", tone_reference["tone_analysis"])
            self.assertIsInstance(tone_reference["tone_analysis"]["tone_analysis"], str)
            self.assertIsInstance(
                tone_reference["tone_analysis"]["tone_analysis_summary"], str
            )

            # Check the personalization action's status
            response = self.client.get(f"/api/action/{p13n_action_id}/get_status/")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Verify the status_type is MISSING_INPUT (since custom_instructions is optional but expected)
            self.assertEqual(
                response.data["status_type"],
                ActionStatusType.Name(
                    ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                ),
            )

            # Verify details structure
            details = response.data["details"]

            # Verify input_check
            self.assertIn("input_check", details)
            input_check = details["input_check"]

            self.assertIn("provided_required_inputs", input_check)
            self.assertIn("template", input_check["provided_required_inputs"])

            # Get the personalization action to verify template data
            logging.error(f"Attempting to get action with ID: {p13n_action_id}")
            response = self.client.get(f"/api/action/{p13n_action_id}/")
            logging.error(f"Response status code: {response.status_code}")
            if response.status_code != status.HTTP_200_OK:
                logging.error(f"Response content: {response.content}")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Verify template data in the response
            self.assertIn("inputs", response.data)
            self.assertIn("template", response.data["inputs"])
            template_data_from_api = response.data["inputs"]["template"]

            # Extract the template data using the proper methods
            tofu_data_list = TofuDataListHandler.parse_json_to_tofu_data(
                template_data_from_api
            )
            template_proto = TofuDataListHandler.get_template(tofu_data_list)

            # Convert the protobuf message to a Python dictionary for easier assertions
            template_dict = MessageToDict(
                template_proto, preserving_proto_field_name=True
            )

            # Verify the template was properly applied
            self.assertIn("template_fields", template_dict)
            self.assertIn(
                "TOFU_COMPONENT_TYPE_EMAIL_BODY", template_dict["template_fields"]
            )

            # Verify specific template field values
            email_body_field = template_dict["template_fields"][
                "TOFU_COMPONENT_TYPE_EMAIL_BODY"
            ]
            self.assertIsNotNone(email_body_field["content_source"])
            self.assertTrue(email_body_field["content_source"].endswith(".json"))
            self.assertIsNotNone(email_body_field["content_source_copy"])
            self.assertTrue(
                email_body_field["content_source_copy"].startswith(
                    "/api/web/storage/s3-presigned-url"
                )
            )
            self.assertIsNotNone(email_body_field["slate_content_source"])
            self.assertTrue(email_body_field["slate_content_source"].endswith(".json"))
            self.assertIsNotNone(email_body_field["slate_content_source_copy"])
            self.assertTrue(
                email_body_field["slate_content_source_copy"].startswith(
                    "/api/web/storage/s3-presigned-url"
                )
            )
            self.assertEqual(email_body_field["content_source_format"], "Text")
            self.assertEqual(
                email_body_field["template_component_type"],
                "TOFU_COMPONENT_TYPE_EMAIL_BODY",
            )
            self.assertEqual(email_body_field["content_source_upload_method"], "Text")

            # Verify email subject field
            email_subject_field = template_dict["template_fields"][
                "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT"
            ]
            self.assertEqual(email_subject_field["content_source"], "")
            self.assertEqual(email_subject_field["content_source_copy"], "")
            self.assertEqual(email_subject_field["content_source_format"], "Text")
            self.assertEqual(
                email_subject_field["template_component_type"],
                "TOFU_COMPONENT_TYPE_EMAIL_SUBJECT",
            )
            self.assertEqual(
                email_subject_field["content_source_upload_method"], "Text"
            )

            # Verify tone reference data from API response
            self.assertIn("tone_reference_v2", template_dict)
            tone_reference = template_dict["tone_reference_v2"]

            # Verify tone assets
            self.assertIn("tone_assets", tone_reference)
            self.assertIn("data", tone_reference["tone_assets"])
            self.assertEqual(len(tone_reference["tone_assets"]["data"]), 1)
            self.assertIn("asset", tone_reference["tone_assets"]["data"][0])
            self.assertIn("asset_id", tone_reference["tone_assets"]["data"][0]["asset"])
            self.assertIsInstance(
                tone_reference["tone_assets"]["data"][0]["asset"]["asset_id"],
                (int, str),
            )

            # Verify tone analysis
            self.assertIn("tone_analysis", tone_reference)
            self.assertIn("tone_analysis", tone_reference["tone_analysis"])
            self.assertIn("tone_analysis_summary", tone_reference["tone_analysis"])
            self.assertIsInstance(tone_reference["tone_analysis"]["tone_analysis"], str)
            self.assertIsInstance(
                tone_reference["tone_analysis"]["tone_analysis_summary"], str
            )
