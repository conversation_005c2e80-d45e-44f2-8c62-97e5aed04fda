import logging
import os
from unittest.mock import MagicMock, patch

import boto3
from django.core.cache import cache
from langchain_core.messages import AIMessage
from langchain_core.outputs import ChatGeneration
from moto import mock_aws
from rest_framework import status

from ...actions.tofu_data_wrapper import To<PERSON>DataListHandler
from ...models import Action, Campaign, ContentGroup, TargetInfo, TargetInfoGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionCategory,
    ActionStatusType,
)
from ...thread_locals import set_current_user
from .action_test_base import ActionTestBase


class TestActionSeqPersonalizeTemplateP13n(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest
        # Clear the cache before each test
        cache.clear()

        # Get the TargetInfo ID
        target_group = TargetInfoGroup.objects.get(
            target_info_group_key="Veterinary Clinics"
        )
        self.target = TargetInfo.objects.get(
            target_info_group=target_group, target_key="Heska"
        )
        self.target_id = self.target.id

        # Make sure the campaign has a proper creator relationship
        # This ensures the campaign.creator field is properly set
        self.campaign = Campaign.objects.get(id=self.campaign_id)
        self.campaign.creator = self.user
        self.campaign.save()

        # Set the current user for the thread
        set_current_user(self.user)

    def tearDown(self):
        # Clear the cache after each test
        cache.clear()
        super().tearDown()

    @mock_aws
    @patch("api.logger.log_content_creation", MagicMock())
    @patch("api.logger.log_campaign_creation", MagicMock())
    @patch("api.logger.log_content_gen", MagicMock())
    @patch("api.logger.log_token_usage", MagicMock())
    @patch("api.logger.tofu_event_logger.log_event", MagicMock())
    def test_seq_personalize_template_p13n_connection(self):
        """Test the connection between sequence personalize template action and personalization actions"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            mock_gen_response = [
                ChatGeneration(
                    text="Generated template content",
                    message=AIMessage(content="Generated template content"),
                    generation_info={},
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_gen_response,
                ),
            ):
                # Step 1: Create SEQ_PERSONALIZE_TEMPLATE action
                # The category value is 5 in the protobuf definition
                num_outputs = 3
                seq_personalize_template_action_data = {
                    "action_category": ActionCategory.Name(
                        ActionCategory.ACTION_CATEGORY_SEQ_PERSONALIZE_TEMPLATE
                    ),
                    "action_name": "Sequence Personalize Template Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                        "targets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_targets_to_tofu_data(
                                [self.target_id]
                            )
                        ),
                    },
                }

                response = self.client.post(
                    "/api/action/", seq_personalize_template_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                seq_template_action_id = response.data[-1]["id"]

                # Verify the action is completed
                seq_template_action_status = self.client.get(
                    f"/api/action/{seq_template_action_id}/get_status/"
                )
                self.assertEqual(
                    seq_template_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    seq_template_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                # Step 2: Execute the SEQ_PERSONALIZE action
                # response = self.client.post(
                #     f"/api/action/{seq_template_action_id}/execute/",
                #     {},
                #     format="json",
                # )
                # self.assertEqual(response.status_code, status.HTTP_200_OK)

                # # Verify the action is completed
                # seq_template_action_status = self.client.get(
                #     f"/api/action/{seq_template_action_id}/get_status/"
                # )
                # self.assertEqual(
                #     seq_template_action_status.status_code, status.HTTP_200_OK
                # )
                # self.assertEqual(
                #     seq_template_action_status.data["status_type"],
                #     ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                # )

                # Get the content group IDs from the seq personalize template action
                seq_template_action = Action.objects.get(id=seq_template_action_id)
                self.assertIn("content_group", seq_template_action.outputs)
                content_groups = ContentGroup.objects.filter(action=seq_template_action)
                self.assertEqual(content_groups.count(), num_outputs)

                # Step 3: Create personalization actions connected to each template output
                p13n_action_ids = []
                for i in range(num_outputs):
                    p13n_action_data = {
                        "action_category": "ACTION_CATEGORY_PERSONALIZE",
                        "action_name": f"Personalization Action {i+1}",
                        "campaign": self.campaign_id,
                        "playbook": self.playbook_id,
                        "inputs": {
                            "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                                TofuDataListHandler.convert_content_type_to_tofu_data(
                                    "Email - SDR"
                                )
                            ),
                        },
                        "incoming_edges": [
                            {
                                "from_action": seq_template_action_id,
                                "config": {
                                    "output_name_from_incoming_action": "content_group",
                                    "input_name_to_outgoing_action": "template",
                                    "index": [
                                        i
                                    ],  # Connect to the specific content group
                                },
                            }
                        ],
                    }

                    response = self.client.post(
                        "/api/action/", p13n_action_data, format="json"
                    )
                    self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                    p13n_action_id = response.data[-1]["id"]
                    p13n_action_ids.append(p13n_action_id)

                self.assertEqual(len(p13n_action_ids), num_outputs)

                # Step 4: Verify each personalization action has the template properly set
                for p13n_action_id in p13n_action_ids:
                    p13n_action = Action.objects.get(id=p13n_action_id)
                    # Verify template is properly set in the inputs
                    # TODO: fix this
                    # self.assertIn("template", p13n_action.inputs)

                    # Get the personalization action status
                    p13n_action_status = self.client.get(
                        f"/api/action/{p13n_action_id}/get_status/"
                    )
                    self.assertEqual(p13n_action_status.status_code, status.HTTP_200_OK)
                    # status is missing input
                    self.assertEqual(
                        p13n_action_status.data["status_type"],
                        ActionStatusType.Name(
                            ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                        ),
                    )

                    # Check components are also populated (should be automatically created based on template)
                    self.assertNotIn("components", p13n_action.inputs)

                    # Verify the status
                    # Note: For a complete personalization action, we need both template and components
                    # The action will be in "missing input" state until we provide all required inputs
                    details = p13n_action_status.data.get("details", {})
                    input_check = details.get("input_check", {})
                    provided_inputs = input_check.get("provided_required_inputs", [])

                    # Verify template is in the provided inputs
                    # self.assertIn("template", provided_inputs)
