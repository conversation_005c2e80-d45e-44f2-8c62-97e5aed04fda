# test_actions.py
import logging
from unittest.mock import patch

from rest_framework import status

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...feature.data_wrapper.data_wrapper import ContentGroupWrapper
from ...models import Action, AssetInfo, AssetInfoGroup, ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionStatusType
from .action_test_base import ActionTestBase, create_mock_check_asset_info


class TestActionRepurposeGen(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest

    def test_repurpose_gen_action(self):

        with (
            self.settings(
                CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                new=create_mock_check_asset_info(),
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
            ) as mock_llm_check,
        ):
            # Mock the evaluator's LLM check method to prevent real API calls
            mock_llm_check.return_value = {"label": "PASS", "comment": None}

            anchor_content_action_data = {
                "action_category": "ACTION_CATEGORY_USER_INPUT",
                "action_name": "Test Action",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "assets": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_asset_to_tofu_data(
                            self.asset_ids, []
                        )
                    ),
                },
            }
            response = self.client.post(
                "/api/action/", anchor_content_action_data, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            anchor_content_action_id = response.data[-1]["id"]

            response = self.client.post(
                f"/api/action/{anchor_content_action_id}/execute/", {}, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            num_outputs = 1
            repurpose_action_data = {
                "action_category": "ACTION_CATEGORY_REPURPOSE",
                "action_name": "Test Action",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_content_type_to_tofu_data(
                            "Email - SDR"
                        )
                    ),
                    "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                    ),
                },
                "incoming_edges": [
                    {
                        "from_action": anchor_content_action_id,
                        "config": {
                            "output_name_from_incoming_action": "assets",
                            "input_name_to_outgoing_action": "anchor_content",
                        },
                    }
                ],
            }

            # Create action
            response = self.client.post(
                "/api/action/", repurpose_action_data, format="json"
            )
            if response.status_code != status.HTTP_201_CREATED:
                logging.error("Error response: %s", response.content)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            repurpose_action_id = response.data[-1]["id"]

            repurpose_action_status = self.client.get(
                f"/api/action/{repurpose_action_id}/get_status/"
            )
            self.assertEqual(repurpose_action_status.status_code, status.HTTP_200_OK)

            # get the content_group instance.
            action = Action.objects.get(id=repurpose_action_id)
            content_group = ContentGroup.objects.filter(action=action).first()
            self.assertIsNotNone(content_group)

            content_group_wrapper = ContentGroupWrapper(content_group)
            self.assertEqual(content_group_wrapper.content_type, "Email - SDR")
            self.assertTrue(content_group_wrapper.is_campaign_v3)

            # Compare the content group asset params regardless of order
            expected_params = [
                {
                    "assets": {"test_for_assets": "costco"},
                    "instruction": None,
                    "meta": "repurpose_anchor_content",
                },
                {
                    "assets": {"test_for_assets": "Walmart"},
                    "instruction": None,
                    "meta": "repurpose_anchor_content",
                },
                {
                    "assets": {"test_for_assets": "Amazon"},
                    "instruction": None,
                    "meta": "repurpose_anchor_content",
                },
            ]

            # Sort both lists to make the test order-agnostic
            sorted_actual = sorted(
                content_group_wrapper._content_group_asset_params,
                key=lambda x: x["assets"]["test_for_assets"],
            )
            sorted_expected = sorted(
                expected_params,
                key=lambda x: x["assets"]["test_for_assets"],
            )
            self.assertEqual(sorted_actual, sorted_expected)
