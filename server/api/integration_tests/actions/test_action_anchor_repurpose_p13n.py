import logging
import os
from unittest.mock import patch

import boto3
from django.core.cache import cache
from langchain_core.messages import AIMessage
from langchain_core.outputs import ChatGeneration
from moto import mock_aws
from rest_framework import status

from ...actions.tofu_data_wrapper import To<PERSON>Data<PERSON>ist<PERSON><PERSON><PERSON>
from ...models import Action, ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionStatusType
from .action_test_base import ActionTestBase, create_mock_check_asset_info


class TestActionAnchorRepurposeP13n(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest
        # Clear the cache before each test
        cache.clear()

    def tearDown(self):
        # Clear the cache after each test
        cache.clear()
        super().tearDown()

    @mock_aws
    @patch("api.actions.repurpose_to_p13n.copy_s3_file")
    def test_anchor_repurpose_personalize_connection(self, mock_copy_s3_file):
        """Test the connection between anchor content, repurpose, and personalization actions"""

        # Setup mock for S3 copy operations to prevent NoSuchKey errors
        def mock_copy_implementation(source_path, destination_path, bucket_name=None):
            # Return a valid URL similar to what the real function would return
            if bucket_name is None:
                bucket_name = "tofu-uploaded-files"
            return f"/api/web/storage/s3-presigned-url?file={destination_path}&fileType=application/json&directory={bucket_name}"

        mock_copy_s3_file.side_effect = mock_copy_implementation

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            mock_gen_response = [
                ChatGeneration(
                    text="Generated content",
                    message=AIMessage(content="Generated content"),
                    generation_info={},
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_gen_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # Step 1: Create anchor content action
                anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Anchor Content Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                self.asset_ids, []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                anchor_content_action_id = response.data[-1]["id"]

                # Step 2: Execute the anchor content action
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 3)
                self.assertEqual(len(asset_group_ids), 0)

                # Step 3: Create repurpose action connected to the anchor content action
                num_outputs = 2
                repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                repurpose_action_id = response.data[-1]["id"]

                # Step 4: Execute the repurpose action
                response = self.client.post(
                    f"/api/action/{repurpose_action_id}/execute/", {}, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the repurpose action is completed
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

                # Get the content group IDs from the repurpose action
                repurpose_action = Action.objects.get(id=repurpose_action_id)
                self.assertIn("content_group", repurpose_action.outputs)
                content_groups = ContentGroup.objects.filter(action=repurpose_action)
                self.assertEqual(content_groups.count(), num_outputs)

                # Step 5: Create personalization action connected to the first repurpose output
                p13n_action_data = {
                    "action_category": "ACTION_CATEGORY_PERSONALIZE",
                    "action_name": "Personalization Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": repurpose_action_id,
                            "config": {
                                "output_name_from_incoming_action": "content_group",
                                "input_name_to_outgoing_action": "template",
                                "index": [0],  # Connect to the first content group
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", p13n_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                p13n_action_id = response.data[-1]["id"]

                p13n_action = Action.objects.get(id=p13n_action_id)
                # Verify template is properly set in the inputs
                self.assertIn("template", p13n_action.inputs)

                # Step 6: Verify the personalization action status is ready
                p13n_action_status = self.client.get(
                    f"/api/action/{p13n_action_id}/get_status/"
                )
                self.assertEqual(p13n_action_status.status_code, status.HTTP_200_OK)

                # Verify the status type is MISSING_INPUT
                self.assertEqual(
                    p13n_action_status.data["status_type"],
                    ActionStatusType.Name(
                        ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                    ),
                )

                # Verify the missing inputs details
                self.assertIn("details", p13n_action_status.data)
                self.assertIn("input_check", p13n_action_status.data["details"])
                self.assertIn(
                    "provided_required_inputs",
                    p13n_action_status.data["details"]["input_check"],
                )
                # Verify the specific missing inputs
                provided_required_inputs = p13n_action_status.data["details"][
                    "input_check"
                ]["provided_required_inputs"]
                self.assertIn("template", provided_required_inputs)
