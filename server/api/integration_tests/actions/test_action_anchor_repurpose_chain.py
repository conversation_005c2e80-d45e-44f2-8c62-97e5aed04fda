import logging
import os
from unittest.mock import patch

import boto3
from django.core.cache import cache
from langchain_core.messages import AIMessage
from langchain_core.outputs import ChatGeneration
from moto import mock_aws
from rest_framework import status

from ...actions.tofu_data_wrapper import TofuData<PERSON>ist<PERSON>and<PERSON>
from ...models import Action, AssetInfo, ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionStatusType
from .action_test_base import ActionTestBase, create_mock_check_asset_info


class TestActionAnchorRepurposeChain(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest
        # Clear the cache before each test
        cache.clear()

    def tearDown(self):
        # Clear the cache after each test
        cache.clear()
        super().tearDown()

    @mock_aws
    def test_anchor_repurpose_anchor_repurpose_chain(self):
        """Test a chain of: anchor content -> repurpose -> anchor content -> repurpose"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            mock_repupose_response = [
                ChatGeneration(
                    text="Generated repurpose content",
                    message=AIMessage(content="Generated repurpose content"),
                    generation_info={},
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_repupose_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # Step 1: Create the first anchor content action
                logging.info("Creating first anchor content action")
                first_anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "First Anchor Content",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                self.asset_ids, []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", first_anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                first_anchor_content_action_id = response.data[-1]["id"]

                # Execute the first anchor content action
                response = self.client.post(
                    f"/api/action/{first_anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the first anchor content action outputs
                first_anchor_content_action = Action.objects.get(
                    id=first_anchor_content_action_id
                )
                self.assertIn("assets", first_anchor_content_action.outputs)
                asset_data = first_anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(
                    len(asset_ids), 3
                )  # We expect 3 assets from the test setup
                self.assertEqual(len(asset_group_ids), 0)

                # Step 2: Create the first repurpose action that takes the first anchor content as input
                logging.info("Creating first repurpose action")
                num_outputs = 4
                first_repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "First Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": first_anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", first_repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                first_repurpose_action_id = response.data[-1]["id"]

                # Verify the repurpose action status is ready
                repurpose_action_status = self.client.get(
                    f"/api/action/{first_repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                # Execute the first repurpose action
                response = self.client.post(
                    f"/api/action/{first_repurpose_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the repurpose action is complete
                repurpose_action_status = self.client.get(
                    f"/api/action/{first_repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

                # Get content group IDs from the first repurpose action
                first_repurpose_action = Action.objects.get(
                    id=first_repurpose_action_id
                )
                self.assertIn("content_group", first_repurpose_action.outputs)
                content_group_data = first_repurpose_action.outputs["content_group"]
                first_content_group_ids = [
                    group["content_group"]["content_group_id"]
                    for group in content_group_data["data"]
                ]
                self.assertEqual(len(first_content_group_ids), num_outputs)

                # Step 3: Create a second anchor content action that takes the first repurpose as input
                logging.info("Creating second anchor content action")
                second_anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Second Anchor Content",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {},  # Empty initially, will be populated from incoming edge
                    "incoming_edges": [
                        {
                            "from_action": first_repurpose_action_id,
                            "config": {
                                "output_name_from_incoming_action": "content_group",
                                "input_name_to_outgoing_action": "assets",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", second_anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                second_anchor_content_action_id = response.data[-1]["id"]

                # Verify the second anchor content action received assets from the first repurpose
                second_anchor_action = Action.objects.get(
                    id=second_anchor_content_action_id
                )
                self.assertIn("assets", second_anchor_action.inputs)
                asset_data = second_anchor_action.inputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)

                self.assertEqual(
                    len(asset_ids), num_outputs
                )  # Should match num_outputs from first repurpose
                self.assertEqual(len(asset_group_ids), 0)

                # Verify these assets are the converted content groups from first repurpose
                asset_infos = AssetInfo.objects.filter(id__in=asset_ids)
                self.assertEqual(len(asset_infos), num_outputs)

                asset_keys = {asset.asset_key for asset in asset_infos}
                content_groups = ContentGroup.objects.filter(
                    id__in=first_content_group_ids
                )
                expected_keys = {
                    content_group.content_group_name for content_group in content_groups
                }
                self.assertEqual(asset_keys, expected_keys)

                # Execute the second anchor content action
                response = self.client.post(
                    f"/api/action/{second_anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the second anchor content action outputs
                second_anchor_content_action = Action.objects.get(
                    id=second_anchor_content_action_id
                )
                self.assertIn("assets", second_anchor_content_action.outputs)
                asset_data = second_anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), num_outputs)
                self.assertEqual(len(asset_group_ids), 0)

                # Step 4: Create a second repurpose action that takes the second anchor content as input
                logging.info("Creating second repurpose action")
                second_repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Second Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": second_anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", second_repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                second_repurpose_action_id = response.data[-1]["id"]

                # Verify the second repurpose action status is ready
                second_repurpose_status = self.client.get(
                    f"/api/action/{second_repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    second_repurpose_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    second_repurpose_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                # Verify anchor_content inputs in the second repurpose action
                second_repurpose_action = Action.objects.get(
                    id=second_repurpose_action_id
                )
                self.assertIn("anchor_content", second_repurpose_action.inputs)
                anchor_content_data = second_repurpose_action.inputs["anchor_content"]
                (
                    anchor_asset_ids,
                    anchor_asset_group_ids,
                ) = TofuDataListHandler.get_assets(anchor_content_data)

                # Should match the asset IDs from the second anchor content
                self.assertEqual(set(anchor_asset_ids), set(asset_ids))
                self.assertEqual(len(anchor_asset_group_ids), 0)

                # Execute the second repurpose action
                response = self.client.post(
                    f"/api/action/{second_repurpose_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the second repurpose action is complete
                second_repurpose_status = self.client.get(
                    f"/api/action/{second_repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    second_repurpose_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

                # Verify output content groups in the second repurpose action
                second_repurpose_action = Action.objects.get(
                    id=second_repurpose_action_id
                )
                self.assertIn("content_group", second_repurpose_action.outputs)
                content_group_data = second_repurpose_action.outputs["content_group"]
                second_content_group_ids = [
                    group["content_group"]["content_group_id"]
                    for group in content_group_data["data"]
                ]
                self.assertEqual(len(second_content_group_ids), num_outputs)

                # Verify these are different content groups than the first repurpose
                self.assertEqual(
                    len(set(first_content_group_ids) & set(second_content_group_ids)), 0
                )
