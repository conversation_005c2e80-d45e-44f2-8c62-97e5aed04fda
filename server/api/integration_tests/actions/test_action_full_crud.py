# test_actions.py
import logging
from unittest.mock import patch

from rest_framework import status

from ...actions.action_data_wrapper import ActionDataWrapper
from ...actions.tofu_data_wrapper import TofuDataList<PERSON>andler
from ...feature.data_wrapper.data_wrapper import ContentGroupWrapper
from ...models import Action
from ...shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionStatusType,
    PlatformType,
)
from .action_test_base import ActionTestBase, create_mock_check_asset_info


class ActionTestFullCRUD(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest

    def test_crud_full_action(self):
        """Test Create, Read, Update, Delete operations for Actions"""
        with (
            self.settings(
                CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                new=create_mock_check_asset_info(),
            ),
            patch(
                "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
            ) as mock_llm_check,
        ):
            # Mock the evaluator's LLM check method to prevent real API calls
            mock_llm_check.return_value = {"label": "PASS", "comment": None}

            # TODO: add anchor content action
            anchor_content_action_data = {
                "action_category": "ACTION_CATEGORY_USER_INPUT",
                "action_name": "Test Action",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "assets": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_asset_to_tofu_data(
                            self.asset_ids, []
                        )
                    ),
                },
            }
            response = self.client.post(
                "/api/action/", anchor_content_action_data, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            anchor_content_action_id = response.data[-1]["id"]

            response = self.client.post(
                f"/api/action/{anchor_content_action_id}/execute/", {}, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            anchor_content_action = Action.objects.get(id=anchor_content_action_id)
            # check outputs
            self.assertIn("assets", anchor_content_action.outputs)
            asset_data = anchor_content_action.outputs["assets"]
            asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
            self.assertEqual(len(asset_ids), 3)
            self.assertEqual(len(asset_group_ids), 0)

        num_outputs = 3
        repurpose_action_data = {
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
            "incoming_edges": [
                {
                    "from_action": anchor_content_action_id,
                    "config": {
                        "output_name_from_incoming_action": "assets",
                        "input_name_to_outgoing_action": "anchor_content",
                    },
                }
            ],
        }

        # Create action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        repurpose_action_id = response.data[-1]["id"]

        # check repurpose data
        if True:
            repupose_action = Action.objects.get(id=repurpose_action_id)
            response_action_data_wrapper = ActionDataWrapper(repupose_action)
            first_content_group = response_action_data_wrapper.content_groups[0]
            content_group_data_wrapper = ContentGroupWrapper(first_content_group)

            logging.error(
                f"content_group_data_wrapper._content_group_asset_params: {content_group_data_wrapper._content_group_asset_params}"
            )

            # Sort both lists to make the test order-agnostic
            sorted_actual = sorted(
                content_group_data_wrapper._content_group_asset_params,
                key=lambda x: x["assets"]["test_for_assets"],
            )
            sorted_expected = sorted(
                [
                    {
                        "assets": {"test_for_assets": "costco"},
                        "instruction": None,
                        "meta": "repurpose_anchor_content",
                    },
                    {
                        "assets": {"test_for_assets": "Walmart"},
                        "instruction": None,
                        "meta": "repurpose_anchor_content",
                    },
                    {
                        "assets": {"test_for_assets": "Amazon"},
                        "instruction": None,
                        "meta": "repurpose_anchor_content",
                    },
                ],
                key=lambda x: x["assets"]["test_for_assets"],
            )
            assert sorted_actual == sorted_expected

        repurpose_action_status = self.client.get(
            f"/api/action/{repurpose_action_id}/get_status/"
        )
        self.assertEqual(repurpose_action_status.status_code, status.HTTP_200_OK)
        self.assertEqual(
            repurpose_action_status.data["status_type"],
            ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
        )
        for i in range(num_outputs):
            p13n_action_data = {
                "action_category": "ACTION_CATEGORY_PERSONALIZE",
                "action_name": "Test Action",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_content_type_to_tofu_data(
                            "Email - SDR"
                        )
                    ),
                },
                "incoming_edges": [
                    {
                        "from_action": repurpose_action_id,
                        "config": {
                            "output_name_from_incoming_action": "content_group",
                            "input_name_to_outgoing_action": "template",
                            "index": [i],
                        },
                    }
                ],
            }

            # Create p13n action
            response = self.client.post("/api/action/", p13n_action_data, format="json")
            if response.status_code != status.HTTP_201_CREATED:
                logging.error("Error response: %s", response.content)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            p13n_action_id = response.data[-1]["id"]

            # Create export action
            export_action_data = {
                "action_category": "ACTION_CATEGORY_EXPORT",
                "action_name": "Test Action",
                "campaign": self.campaign_id,
                "playbook": self.playbook_id,
                "inputs": {
                    "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_content_type_to_tofu_data(
                            "Email - SDR"
                        )
                    ),
                    "platform_type": TofuDataListHandler.convert_tofu_data_to_json(
                        TofuDataListHandler.convert_platform_type_to_tofu_data(
                            PlatformType.PLATFORM_TYPE_HUBSPOT
                        )
                    ),
                },
                "incoming_edges": [
                    {
                        "from_action": p13n_action_id,
                        "config": {
                            "output_name_from_incoming_action": "content_group",
                            "input_name_to_outgoing_action": "template",
                        },
                    }
                ],
            }

            # Create export action
            response = self.client.post(
                "/api/action/", export_action_data, format="json"
            )
            if response.status_code != status.HTTP_201_CREATED:
                logging.error("Error response: %s", response.content)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            export_action_id = response.data[-1]["id"]

            response = self.client.get(f"/api/action/{export_action_id}/get_status/")
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(
                response.data["status_type"],
                ActionStatusType.Name(
                    ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                ),
            )

        # Delete action
        response = self.client.delete(f"/api/action/{anchor_content_action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # check if no actions in campaign
        response = self.client.get(f"/api/campaign/{self.campaign_id}/get_actions/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)
