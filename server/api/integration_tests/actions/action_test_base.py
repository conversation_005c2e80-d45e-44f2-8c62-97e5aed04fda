# test_base_setup.py
import logging
import os
from unittest.mock import MagicMock, patch

from rest_framework import status
from rest_framework.test import APITestCase

from ...models import (
    AssetInfo,
    AssetInfoGroup,
    Campaign,
    Playbook,
    TargetInfo,
    TargetInfoGroup,
    TofuUser,
)
from ...thread_locals import set_current_playbook, set_current_user


class BaseWorkflowTest(APITestCase):
    def setUp(self):
        self.maxDiff = None
        self.setup_user()
        self.setup_playbook()
        self.setup_target_list()

    def setup_user(self):
        """Create and authenticate user"""
        # Create the user which is actually a TofuUser
        self.user = TofuUser.objects.create_user(
            username="testuser", password="testpass123", email="<EMAIL>"
        )
        self.client.force_authenticate(user=self.user)
        set_current_user(self.user)

    def setup_playbook(self):
        """Get the user's playbook"""
        self.playbook = Playbook.objects.get(users=self.user)
        self.playbook_id = self.playbook.id
        set_current_playbook(self.playbook)

    def setup_target_list(self):
        """Create target info group"""
        response = self.client.post(
            "/api/target_info_group/",
            {
                "playbook": self.playbook_id,
                "target_info_group_key": "Test Target List",
                "meta": {"name": "Test Target List", "description": "Test targets"},
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.target_info_group_id = response.data["id"]


class ActionTestBase(BaseWorkflowTest):
    def setUp(self):
        # Set environment variable to indicate we're in unit test mode
        os.environ["TOFU_ENV"] = "unit_test"

        # Start all Pinecone-related patches
        self._setup_pinecone_mocks()

        super().setUp()
        self.setup_campaign()
        self.setup_campaign_with_no_target()

    def tearDown(self):
        # Stop all Pinecone patches
        self._teardown_pinecone_mocks()

        # Clean up the environment variable
        if "TOFU_ENV" in os.environ:
            del os.environ["TOFU_ENV"]
        super().tearDown()

    def _setup_pinecone_mocks(self):
        """Set up comprehensive Pinecone mocking to prevent real API calls"""
        # Store patch references for cleanup
        self._pinecone_patches = []

        # Mock IndexBuilder Pinecone usage
        pinecone_patch = patch("api.playbook_build.index_builder.pinecone.Pinecone")
        mock_pinecone_class = pinecone_patch.start()
        self._pinecone_patches.append(pinecone_patch)

        # Mock Langchain Pinecone
        langchain_pinecone_patch = patch("api.playbook_build.index_builder.Pinecone")
        mock_langchain_pinecone = langchain_pinecone_patch.start()
        self._pinecone_patches.append(langchain_pinecone_patch)

        # Set up mock behavior first
        mock_pinecone_instance = MagicMock()
        mock_index = MagicMock()
        mock_pinecone_instance.Index.return_value = mock_index
        mock_pinecone_class.return_value = mock_pinecone_instance

        # Mock Pinecone index operations
        mock_index.query.return_value = {"matches": []}
        mock_index.delete.return_value = None

        # Mock feature builder Pinecone usage
        feature_patches = [
            "api.feature.feature_builder.company_context_feature_builder.pinecone.Pinecone",
            "api.feature.feature_builder.target_context_feature_builder.pinecone.Pinecone",
            "api.feature.feature_builder.asset_context_feature_builder.pinecone.Pinecone",
        ]

        for patch_path in feature_patches:
            p = patch(patch_path)
            mock_feature_pinecone = p.start()
            # Configure the same behavior as the main Pinecone mock
            mock_feature_pinecone.return_value = mock_pinecone_instance
            self._pinecone_patches.append(p)

        # Mock Langchain Pinecone
        mock_langchain_pinecone.from_documents.return_value = MagicMock()

    def _teardown_pinecone_mocks(self):
        """Clean up all Pinecone patches"""
        for patch_obj in getattr(self, "_pinecone_patches", []):
            patch_obj.stop()

    def setup_campaign(self):
        """Create campaign and associated targets"""
        # Create target info group for "Veterinary Clinics"
        target_group = TargetInfoGroup.objects.create(
            playbook=self.playbook,
            target_info_group_key="Veterinary Clinics",
            meta={
                "name": "Veterinary Clinics",
                "description": "Veterinary clinic targets",
            },
        )

        # Create target info for "Heska"
        TargetInfo.objects.create(
            target_info_group=target_group,
            target_key="Heska",
            docs={},  # Add any initial docs if needed
            meta={"name": "Heska"},
        )

        # Create asset info group for "test_for_assets"
        self.asset_group = AssetInfoGroup.objects.create(
            playbook=self.playbook,
            asset_info_group_key="test_for_assets",
            meta={"position": 0},
        )

        # Create asset infos
        asset_1 = AssetInfo.objects.create(
            asset_info_group=self.asset_group,
            asset_key="costco",
            docs={
                "6ec5e479-0cf1-75a7-37d5-a6ee148074e0": {
                    "id": "6ec5e479-0cf1-75a7-37d5-a6ee148074e0",
                    "type": "text",
                    "value": "Costco Wholesale Corporation is one of the world's largest membership-only warehouse clubs, known for offering bulk goods at competitive prices. Founded in 1983 in Seattle, Washington, Costco operates a network of warehouse-style stores that sell a wide range of products, including groceries, electronics, home goods, clothing, and automotive supplies.",
                    "position": 0,
                },
                "9ff05037-4cd1-45fb-a3ad-a6ee148074e0": {
                    "id": "9ff05037-4cd1-45fb-a3ad-a6ee148074e0",
                    "type": "text",
                    "value": "The company is renowned for its Kirkland Signature private label, which offers high-quality alternatives to name-brand products. Costco's membership model grants shoppers access to its discounted pricing, with two main tiers: Gold Star and Executive.",
                    "position": 0,
                },
            },
            meta={
                "brief": "<context>\n<meta>\n<keyIds>['test_for_assets', 'costco']</keyIds>\n</meta>\n<content>\nCostco Wholesale Corporation is one of the world's largest membership-only warehouse clubs, known for offering bulk goods at competitive prices. Founded in 1983 in Seattle, Washington, Costco operates a network of warehouse-style stores that sell a wide range of products, including groceries, electronics, home goods, clothing, and automotive supplies. \n</content>\n</context>",
                "position": 0,
            },
        )

        asset_2 = AssetInfo.objects.create(
            asset_info_group=self.asset_group,
            asset_key="Walmart",
            docs={
                "9ff05037-4cd1-45fb-a3ad-2481369fb2f5": {
                    "id": "9ff05037-4cd1-45fb-a3ad-2481369fb2f5",
                    "type": "text",
                    "value": "Walmart Inc. is a global retail giant headquartered in Bentonville, Arkansas, and is one of the largest companies in the world by revenue. Founded by Sam Walton in 1962, Walmart operates thousands of stores in multiple formats, including Walmart Supercenters, Discount Stores, and Neighborhood Markets. It also owns Sam's Club, a membership-based warehouse retailer similar to Costco.",
                    "position": 0,
                }
            },
            meta={
                "brief": "<context>\n<meta>\n<keyIds>['test_for_assets', 'Walmart']</keyIds>\n</meta>\n<content>\Walmart Inc. is a global retail giant headquartered in Bentonville, Arkansas, and is one of the largest companies in the world by revenue. Founded by Sam Walton in 1962, Walmart operates thousands of stores in multiple formats, including Walmart Supercenters, Discount Stores, and Neighborhood Markets. It also owns Sam's Club, a membership-based warehouse retailer similar to Costco.\n</content>\n</context>",
                "position": 1,
            },
        )

        asset_3 = AssetInfo.objects.create(
            asset_info_group=self.asset_group,
            asset_key="Amazon",
            docs={
                "78f31875-fc64-bddd-5a92-3a7d1f091d16": {
                    "id": "78f31875-fc64-bddd-5a92-3a7d1f091d16",
                    "type": "text",
                    "value": "Amazon.com, Inc. is a multinational technology and e-commerce company headquartered in Seattle, Washington. Founded by Jeff Bezos in 1994, Amazon started as an online bookstore before expanding into a vast marketplace selling everything from electronics and clothing to groceries and cloud computing services.",  # Truncated for brevity
                    "position": 0,
                }
            },
            meta={
                "brief": "<context>\n<meta>\n<keyIds>['test_for_assets', 'Amazon']</keyIds>\n</meta>\n<content>\nAmazon.com, Inc. is a multinational technology and e-commerce company headquartered in Seattle, Washington. Founded by Jeff Bezos in 1994, Amazon started as an online bookstore before expanding into a vast marketplace selling everything from electronics and clothing to groceries and cloud computing services.\n</content>\n</context>",
                "position": 2,
            },
        )

        self.assets = [asset_1, asset_2, asset_3]
        self.asset_ids = [asset.id for asset in self.assets]

        # Create the campaign
        campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            playbook=self.playbook,
            creator=self.user,
            campaign_params={
                "is_campaign_v3": True,
                "targets": [{"Veterinary Clinics": ["Heska"]}],
                "campaign_status": {},
            },
        )
        self.campaign_id = campaign.id

    def setup_campaign_with_no_target(self):
        """Create campaign and associated targets"""
        # Create the campaign
        campaign = Campaign.objects.create(
            campaign_name="Test Campaign",
            playbook=self.playbook,
            creator=self.user,
            campaign_params={
                "is_campaign_v3": True,
                "targets": [],
                "campaign_status": {},
            },
        )
        self.campaign_id_no_target = campaign.id


def create_mock_check_asset_info():
    """Creates a mock function that properly handles AssetEvalBuilder.check_asset_info with side effects"""

    def mock_check_asset_info(self):
        """Mock that applies proper side effects"""
        asset_object = self.asset_object

        # Check if there's an LLM evaluator callback that would return FAIL
        # This allows tests to control the result by mocking _llm_check_content
        if hasattr(self, "llm_evaluator_callback") and self.llm_evaluator_callback:
            try:
                # Try to call the LLM callback to see what it would return
                llm_result = self.llm_evaluator_callback("dummy content")
                if llm_result.get("label") == "FAIL":
                    mock_result = llm_result
                else:
                    mock_result = {"label": "PASS", "comment": None}
            except:
                # If LLM callback fails, default to PASS
                mock_result = {"label": "PASS", "comment": None}
        else:
            mock_result = {"label": "PASS", "comment": None}

        # Apply the side effects we need
        if not asset_object.docs_build_status:
            asset_object.docs_build_status = {}
        asset_object.docs_build_status["anchor_precheck_status"] = "DONE"

        additional_info = asset_object.additional_info or {}
        additional_info["anchor_content_precheck"] = mock_result
        asset_object.additional_info = additional_info
        asset_object.save()

        return mock_result

    return mock_check_asset_info
