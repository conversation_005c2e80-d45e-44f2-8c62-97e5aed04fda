# test_action_export.py
import logging

from rest_framework import status

from ...actions.tofu_data_wrapper import TofuDataListHandler
from ...models import Content, ContentGroup, TargetInfo, TargetInfoGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from .action_test_base import ActionTestBase


class ActionTestExportCRUD(ActionTestBase):
    def setUp(self):
        super().setUp()

    def test_crud_export_action(self):
        """Test Create, Read, Update, Delete operations for Export Actions"""

        # First, create a repurpose action that will serve as the "from_action"
        num_outputs = 1
        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Repurpose Action for Export",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
        }

        # Create repurpose action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        from_action_id = response.data[-1]["id"]

        # Store the content group IDs created by the repurpose action
        repurpose_outputs = response.data[-1]["outputs"]
        self.assertIn("content_group", repurpose_outputs)
        content_group_data = repurpose_outputs["content_group"]
        content_group_ids = TofuDataListHandler.get_content_group_ids(
            content_group_data
        )
        self.assertEqual(len(content_group_ids), 1)
        content_group_id = content_group_ids[0]

        # Now enhance the content group with proper components and content, following the export handler test
        content_group = ContentGroup.objects.get(id=content_group_id)

        # Add components to the content group
        components = {
            "AE1T9-OliNbfiJ9t": {"text": "Email subject component"},
            "K7hpfnHGl-VzvkyZ": {"text": "Email body component"},
        }
        content_group.components = components

        # Add proper export settings structure
        content_group_params = content_group.content_group_params.copy()
        content_group_params.update(
            {
                "content_goal": "Export",
                "content_type": "Email - SDR",
            }
        )
        content_group.content_group_params = content_group_params
        content_group.save()

        # Create TargetInfoGroup and TargetInfo for the test
        target_info_group = TargetInfoGroup.objects.create(
            playbook_id=self.playbook_id,
            target_info_group_key="test_list1",
            meta={},
        )
        TargetInfo.objects.create(
            target_info_group=target_info_group,
            target_key="target1",
            meta={
                "hubspot_record_id": "*********",
                "hubspot_object_type": "company",
            },
        )

        # Add Content objects to the content group
        Content.objects.create(
            creator=self.user,
            playbook_id=self.playbook_id,
            content_group=content_group,
            content_name="Test Export Content",
            content_params={"targets": {"test_list1": "target1"}},
        )

        # Now create the export action with incoming_edges following the example payload
        export_action_data = {
            "action_type": "ACTION_TYPE_EXPORT_HUBSPOT_EMAIL",
            "action_category": "ACTION_CATEGORY_EXPORT",
            "action_name": "Test Export Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
                "platform_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_platform_type_to_tofu_data(
                        PlatformType.PLATFORM_TYPE_HUBSPOT
                    )
                ),
            },
            "incoming_edges": [
                {
                    "from_action": from_action_id,
                    "config": {
                        "input_name_to_outgoing_action": "content_group",
                        "output_name_from_incoming_action": "content_group",
                    },
                }
            ],
        }

        # Create export action
        response = self.client.post("/api/action/", export_action_data, format="json")
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # Get action status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Update action inputs with export settings - new export case (first call)
        export_settings_new = {
            "data": [
                {
                    "export_settings": {
                        "platform_type": "PLATFORM_TYPE_HUBSPOT",
                        "email": {
                            "targets_setting": {
                                "*********": {
                                    "draft_URL": "https://app.hubspot.com/email/********/edit/190457101858/content",
                                    "email_name": "Tofu Test Export Email AAA",
                                },
                                "258693937": {
                                    "draft_URL": "https://app.hubspot.com/email/********/edit/190457101858/content",
                                    "email_name": "Tofu Test Export Email AAA",
                                },
                                "258693941": {
                                    "email_name": "Tofu Test Export Email AAA",
                                    "is_export_target": False,
                                },
                            },
                            "components_setting": {
                                "AE1T9-OliNbfiJ9t": "tofu_email_345873_2a",
                                "K7hpfnHGl-VzvkyZ": "tofu_email_345873_1b",
                            },
                            "advanced_setting": {
                                "email_type": "automated",
                                "email_footer": True,
                            },
                        },
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"export_settings": export_settings_new}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get the content group directly from DB and verify export settings
        content_group.refresh_from_db()
        export_settings = content_group.content_group_params.get("export_settings", {})

        # TODO: Uncomment this once we verified export settings are generated correctly

        # # Verify export settings are stored correctly
        # self.assertIn("exportDestination", export_settings)
        # self.assertEqual(export_settings["exportDestination"], "hubspot")
        # self.assertIn("hubspot", export_settings)
        # hubspot_settings = export_settings["hubspot"]
        # self.assertIn("email", hubspot_settings)
        # self.assertIn("dynamic", hubspot_settings["email"])

        # # The actual structure is hubspot.email.dynamic.targetsSetting (singular)
        # dynamic_settings = hubspot_settings["email"]["dynamic"]
        # self.assertIn("targetsSetting", dynamic_settings)

        # # Spot check: Verify the export settings match what we passed to the action
        # self.assertIn("componentsSetting", dynamic_settings)

        # # Check specific email names from our input
        # targets_settings = dynamic_settings["targetsSetting"]
        # if isinstance(targets_settings, list):
        #     # Convert list to dict for easier checking
        #     targets_dict = {}
        #     for setting in targets_settings:
        #         if "emailName" in setting:
        #             targets_dict[setting.get("hubspot_record_id", "")] = setting
        # else:
        #     targets_dict = targets_settings

        # # Verify some of the email names we set
        # found_correct_email_names = False
        # if isinstance(targets_dict, dict):
        #     for target_data in targets_dict.values():
        #         if (
        #             isinstance(target_data, dict)
        #             and target_data.get("emailName") == "Tofu Test Export Email AAA"
        #         ):
        #             found_correct_email_names = True
        #             break

        # # Check components setting exists (the exact structure may vary after conversion)
        # components_setting = dynamic_settings["componentsSetting"]
        # self.assertIsInstance(components_setting, dict)

        export_settings_updated = {
            "data": [
                {
                    "export_settings": {
                        "platform_type": "PLATFORM_TYPE_HUBSPOT",
                        "email": {
                            "targets_setting": {
                                "*********": {
                                    "draft_URL": "https://app.hubspot.com/email/********/edit/190457101858/content",
                                    "email_name": "Tofu Test Export Email UPDATED BBB",
                                },
                                "258693937": {
                                    "draft_URL": "https://app.hubspot.com/email/********/edit/190457101858/content",
                                    "email_name": "Tofu Test Export Email UPDATED BBB",
                                },
                                "258693941": {
                                    "email_name": "Tofu Test Export Email UPDATED BBB",
                                    "is_export_target": False,
                                },
                            },
                            "components_setting": {
                                "K7hpfnHGl-VzvkyZ": "tofu_email_updated_999_4d"
                            },
                            "advanced_setting": {
                                "email_type": "automated",
                                "email_footer": True,
                            },
                        },
                    }
                }
            ]
        }

        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"export_settings": export_settings_updated}},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get content group again and verify the updates were applied
        content_group.refresh_from_db()
        updated_export_settings = content_group.content_group_params.get(
            "export_settings", {}
        )

        # TODO: Uncomment this once we verified export settings are generated correctly

        # # Verify the structure is still correct
        # self.assertIn("exportDestination", updated_export_settings)
        # self.assertEqual(updated_export_settings["exportDestination"], "hubspot")
        # self.assertIn("hubspot", updated_export_settings)
        # updated_hubspot_settings = updated_export_settings["hubspot"]
        # self.assertIn("email", updated_hubspot_settings)
        # self.assertIn("dynamic", updated_hubspot_settings["email"])

        # updated_dynamic_settings = updated_hubspot_settings["email"]["dynamic"]
        # self.assertIn("targetsSetting", updated_dynamic_settings)
        # self.assertIn("componentsSetting", updated_dynamic_settings)

        # # Verify the email names were updated
        # updated_targets_settings = updated_dynamic_settings["targetsSetting"]
        # if isinstance(updated_targets_settings, list):
        #     # Convert list to dict for easier checking
        #     updated_targets_dict = {}
        #     for setting in updated_targets_settings:
        #         if "emailName" in setting:
        #             updated_targets_dict[setting.get("hubspot_record_id", "")] = setting
        # else:
        #     updated_targets_dict = updated_targets_settings

        # # Verify some of the updated email names
        # found_updated_email_names = False
        # if isinstance(updated_targets_dict, dict):
        #     for target_data in updated_targets_dict.values():
        #         if (
        #             isinstance(target_data, dict)
        #             and target_data.get("emailName")
        #             == "Tofu Test Export Email UPDATED BBB"
        #         ):
        #             found_updated_email_names = True
        #             break

        # # Verify the components were updated
        # updated_components_setting = updated_dynamic_settings["componentsSetting"]
        # self.assertIsInstance(updated_components_setting, dict)

        # Update action name
        response = self.client.patch(
            f"/api/action/{action_id}/update_name/",
            {"action_name": "Updated Export Action Name"},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get action definition
        response = self.client.get(f"/api/action/{action_id}/definition/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Delete export action
        response = self.client.delete(f"/api/action/{action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Delete repurpose action
        response = self.client.delete(f"/api/action/{from_action_id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_export_action_different_platforms(self):
        """Test export actions for different platforms (Marketo, Salesforce)"""

        test_cases = [
            {
                "action_type": "ACTION_TYPE_EXPORT_MARKETO_EMAIL",
                "platform_type": PlatformType.PLATFORM_TYPE_MARKETO,
                "content_type": "Email - Marketing",
                "platform_name": "marketo",
            },
            {
                "action_type": "ACTION_TYPE_EXPORT_SALESFORCE_EMAIL",
                "platform_type": PlatformType.PLATFORM_TYPE_SALESFORCE,
                "content_type": "Email - SDR",
                "platform_name": "salesforce",
            },
        ]

        for test_case in test_cases:
            with self.subTest(platform=test_case["platform_name"]):
                # Create a repurpose action first
                num_outputs = 1
                repurpose_action_data = {
                    "action_type": "ACTION_TYPE_REPURPOSE_EMAIL_SDR",
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": f"Test Repurpose Action for {test_case['platform_name']}",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                test_case["content_type"]
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                }

                # Create repurpose action
                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                from_action_id = response.data[-1]["id"]

                # Store the content group IDs created by the repurpose action
                repurpose_outputs = response.data[-1]["outputs"]
                self.assertIn("content_group", repurpose_outputs)
                content_group_data = repurpose_outputs["content_group"]
                content_group_ids = TofuDataListHandler.get_content_group_ids(
                    content_group_data
                )
                self.assertEqual(len(content_group_ids), 1)
                content_group_id = content_group_ids[0]

                # Create export action
                export_action_data = {
                    "action_type": test_case["action_type"],
                    "action_category": "ACTION_CATEGORY_EXPORT",
                    "action_name": f"Test {test_case['platform_name']} Export Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                test_case["content_type"]
                            )
                        ),
                        "platform_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_platform_type_to_tofu_data(
                                test_case["platform_type"]
                            )
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": from_action_id,
                            "config": {
                                "input_name_to_outgoing_action": "content_group",
                                "output_name_from_incoming_action": "content_group",
                            },
                        }
                    ],
                }

                # Create action
                response = self.client.post(
                    "/api/action/", export_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                action_id = response.data[-1]["id"]

                # Get action status
                response = self.client.get(f"/api/action/{action_id}/get_status/")
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify content group exists in DB and is linked properly
                content_group = ContentGroup.objects.get(id=content_group_id)
                self.assertEqual(content_group.creator, self.user)
                self.assertEqual(content_group.campaign_id, self.campaign_id)

                # Verify platform type is correct
                response = self.client.get(f"/api/action/{action_id}/")
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Clean up
                response = self.client.delete(f"/api/action/{action_id}/")
                self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

                response = self.client.delete(f"/api/action/{from_action_id}/")
                self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_export_action_missing_required_inputs(self):
        """Test export action creation with missing required inputs"""

        # Missing platform_type
        export_action_data = {
            "action_type": "ACTION_TYPE_EXPORT_HUBSPOT_EMAIL",
            "action_category": "ACTION_CATEGORY_EXPORT",
            "action_name": "Test Export Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data("Email - SDR")
                ),
            },
        }

        response = self.client.post("/api/action/", export_action_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Missing content_type
        export_action_data = {
            "action_type": "ACTION_TYPE_EXPORT_HUBSPOT_EMAIL",
            "action_category": "ACTION_CATEGORY_EXPORT",
            "action_name": "Test Export Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "platform_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_platform_type_to_tofu_data(
                        PlatformType.PLATFORM_TYPE_HUBSPOT
                    )
                ),
            },
        }

        response = self.client.post("/api/action/", export_action_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_export_action_landing_page_update_inputs_no_error(self):
        """Test export action update_inputs for landing page settings returns no 400 error"""

        # First, create a repurpose action that will serve as the "from_action"
        num_outputs = 1
        repurpose_action_data = {
            "action_type": "ACTION_TYPE_REPURPOSE_LANDING_PAGE",
            "action_category": "ACTION_CATEGORY_REPURPOSE",
            "action_name": "Test Repurpose Landing Page Action for Export",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data(
                        "Landing Page"
                    )
                ),
                "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                ),
            },
        }

        # Create repurpose action
        response = self.client.post(
            "/api/action/", repurpose_action_data, format="json"
        )
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        from_action_id = response.data[-1]["id"]

        # Store the content group IDs created by the repurpose action
        repurpose_outputs = response.data[-1]["outputs"]
        self.assertIn("content_group", repurpose_outputs)
        content_group_data = repurpose_outputs["content_group"]
        content_group_ids = TofuDataListHandler.get_content_group_ids(
            content_group_data
        )
        self.assertEqual(len(content_group_ids), 1)
        content_group_id = content_group_ids[0]

        # Now enhance the content group with proper components and content
        content_group = ContentGroup.objects.get(id=content_group_id)

        # Add components to the content group
        components = {
            "test_component_1": {"text": "Landing page component 1"},
            "test_component_2": {"text": "Landing page component 2"},
        }
        content_group.components = components

        # Add proper export settings structure
        content_group_params = content_group.content_group_params.copy()
        content_group_params.update(
            {
                "content_goal": "Export",
                "content_type": "Landing Page",
            }
        )
        content_group.content_group_params = content_group_params
        content_group.save()

        # Create TargetInfoGroup and TargetInfo for the test
        target_info_group = TargetInfoGroup.objects.create(
            playbook_id=self.playbook_id,
            target_info_group_key="test_landing_page_list",
            meta={},
        )
        TargetInfo.objects.create(
            target_info_group=target_info_group,
            target_key="target1",
            meta={
                "hubspot_record_id": "*********",
                "hubspot_object_type": "contact",
            },
        )
        TargetInfo.objects.create(
            target_info_group=target_info_group,
            target_key="target2",
            meta={
                "hubspot_record_id": "*********",
                "hubspot_object_type": "contact",
            },
        )

        # Add Content objects to the content group
        Content.objects.create(
            creator=self.user,
            playbook_id=self.playbook_id,
            content_group=content_group,
            content_name="Test Landing Page Export Content",
            content_params={"targets": {"test_landing_page_list": "target1"}},
        )

        # Now create the export action with incoming_edges
        export_action_data = {
            "action_type": "ACTION_TYPE_EXPORT_HUBSPOT_LANDING_PAGE",
            "action_category": "ACTION_CATEGORY_EXPORT",
            "action_name": "Test Landing Page Export Action",
            "campaign": self.campaign_id,
            "playbook": self.playbook_id,
            "inputs": {
                "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_content_type_to_tofu_data(
                        "Landing Page"
                    )
                ),
                "platform_type": TofuDataListHandler.convert_tofu_data_to_json(
                    TofuDataListHandler.convert_platform_type_to_tofu_data(
                        PlatformType.PLATFORM_TYPE_HUBSPOT
                    )
                ),
            },
            "incoming_edges": [
                {
                    "from_action": from_action_id,
                    "config": {
                        "input_name_to_outgoing_action": "content_group",
                        "output_name_from_incoming_action": "content_group",
                    },
                }
            ],
        }

        # Create export action
        response = self.client.post("/api/action/", export_action_data, format="json")
        if response.status_code != status.HTTP_201_CREATED:
            logging.error("Error response: %s", response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        action_id = response.data[-1]["id"]

        # Get action status
        response = self.client.get(f"/api/action/{action_id}/get_status/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Update action inputs with landing page export settings - this should NOT return 400
        export_settings_landing_page = {
            "data": [
                {
                    "export_settings": {
                        "is_shadow_testing": True,
                        "platform_type": "PLATFORM_TYPE_HUBSPOT",
                        "page": {
                            "export_type": "EXPORT_PAGE_TYPE_DYNAMIC",
                            "targets_setting": {
                                "*********": {
                                    "page_slug": "jamie_brown@www_austintexas_gov"
                                },
                                "*********": {
                                    "page_slug": "morgan_johnson@www_illinoistollway_com"
                                },
                            },
                            "url_setting": {
                                "domain": "tofu-demo-account-********.hubspotpagebuilder.com/",
                                "group_slug": "carbyne_prospects_list_csv_3",
                                "URL_token": "tofu_url_361286",
                            },
                            "components_setting": {
                                "test_component_1": "test_token_1",
                                "test_component_2": "test_token_2",
                            },
                        },
                    }
                }
            ]
        }

        # Test the landing page export settings update - should work like email export
        response = self.client.patch(
            f"/api/action/{action_id}/update_inputs/",
            {"inputs": {"export_settings": export_settings_landing_page}},
            format="json",
        )
        # This should work now that we're using the same JSON structure pattern as email export
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # TODO: Uncomment this once we support landing page export settings and verified export settings are generated correctly

        # Verify the landing page settings were processed (similar to email verification)
        # content_group.refresh_from_db()
        # export_settings = content_group.content_group_params.get("export_settings", {})

        # # Basic verification that export settings were stored
        # self.assertIn("exportDestination", export_settings)
        # self.assertEqual(export_settings["exportDestination"], "hubspot")
        # self.assertIn("hubspot", export_settings)
        # hubspot_settings = export_settings["hubspot"]

        # # Verify landing page structure exists
        # if "page" in hubspot_settings:
        #     page_settings = hubspot_settings["page"]
        #     if "dynamic" in page_settings:
        #         dynamic_settings = page_settings["dynamic"]
        #         # Check if basic structure exists - the exact format may vary after conversion
        #         self.assertIsInstance(dynamic_settings, dict)

        # # Clean up
        # response = self.client.delete(f"/api/action/{action_id}/")
        # self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # response = self.client.delete(f"/api/action/{from_action_id}/")
        # self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
