import logging
import os
from unittest.mock import patch

import boto3
from django.core.cache import cache
from langchain_core.messages import AIMessage
from langchain_core.outputs import ChatGeneration
from moto import mock_aws
from rest_framework import status

from ...actions.tofu_data_wrapper import TofuDataList<PERSON>and<PERSON>
from ...models import Action
from ...shared_definitions.protobuf.gen.action_define_pb2 import ActionStatusType
from .action_test_base import ActionTestBase, create_mock_check_asset_info


class TestActionAnchorRepurpose(ActionTestBase):
    def setUp(self):
        super().setUp()  # This runs all the setup from BaseWorkflowTest
        # Clear the cache before each test
        cache.clear()

    def tearDown(self):
        # Clear the cache after each test
        cache.clear()
        super().tearDown()

    @mock_aws
    def test_anchor_repurpose_connection_set_anchor_content_to_none(self):
        """Test the connection between anchor content and repurpose actions"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            mock_repurpose_response = [
                ChatGeneration(
                    text="abcde",
                    message=AIMessage(content="abcde"),
                    generation_info={},  # Optional metadata
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_repurpose_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # Create anchor content action
                anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Anchor Content Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                self.asset_ids, []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                anchor_content_action_id = response.data[-1]["id"]

                # Execute the anchor content action
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 3)
                self.assertEqual(len(asset_group_ids), 0)

                # Create repurpose action connected to the anchor content action
                num_outputs = 2
                repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                repurpose_action_id = response.data[-1]["id"]

                # Check the status of the repurpose action
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                # Execute the repurpose action
                response = self.client.post(
                    f"/api/action/{repurpose_action_id}/execute/", {}, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the repurpose action is completed
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

                # Now clear all inputs from the anchor content action
                response = self.client.patch(
                    f"/api/action/{anchor_content_action_id}/update_inputs/",
                    {
                        "inputs": {
                            "assets": TofuDataListHandler.convert_tofu_data_to_json(
                                TofuDataListHandler.convert_asset_to_tofu_data([], [])
                            ),
                        }
                    },
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action's inputs are cleared
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                asset_data = anchor_content_action.inputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 0)
                self.assertEqual(len(asset_group_ids), 0)

                # Check if the anchor content action status is MISSING_INPUT
                anchor_action_status = self.client.get(
                    f"/api/action/{anchor_content_action_id}/get_status/"
                )
                self.assertEqual(anchor_action_status.status_code, status.HTTP_200_OK)
                self.assertEqual(
                    anchor_action_status.data["status_type"],
                    ActionStatusType.Name(
                        ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                    ),
                )

                # Verify the anchor content action outputs are updated
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 0)
                self.assertEqual(len(asset_group_ids), 0)

                # Now check the status of repurpose action
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )

    @mock_aws
    def test_anchor_set_anchor_to_invalid(self):
        """Test setting anchor content to invalid input"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            # First pass test with good input
            mock_repurpose_response = [
                ChatGeneration(
                    text="abcde",
                    message=AIMessage(content="abcde"),
                    generation_info={},
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_repurpose_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # Create anchor content action
                anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Anchor Content Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                [self.asset_ids[1], self.asset_ids[2]], []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                anchor_content_action_id = response.data[-1]["id"]

                # Execute the anchor content action
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 2)
                self.assertEqual(len(asset_group_ids), 0)

                # Create repurpose action connected to the anchor content action
                num_outputs = 2
                repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                repurpose_action_id = response.data[-1]["id"]

                # Check the status of the repurpose action
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                # Execute the repurpose action
                response = self.client.post(
                    f"/api/action/{repurpose_action_id}/execute/", {}, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the repurpose action is completed
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

            # Now test with input that will fail validation
            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {
                    "label": "FAIL",
                    "comment": "Test failure",
                }

                # Update anchor content action with input that will fail validation
                response = self.client.patch(
                    f"/api/action/{anchor_content_action_id}/update_inputs/",
                    {
                        "inputs": {
                            "assets": TofuDataListHandler.convert_tofu_data_to_json(
                                TofuDataListHandler.convert_asset_to_tofu_data(
                                    [self.asset_ids[0]], []
                                )
                            ),
                        }
                    },
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Execute the anchor content action again
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Check if the anchor content action status is FAILED
                anchor_action_status = self.client.get(
                    f"/api/action/{anchor_content_action_id}/get_status/"
                )
                self.assertEqual(anchor_action_status.status_code, status.HTTP_200_OK)
                self.assertEqual(
                    anchor_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_FAIL),
                )

    @mock_aws
    def test_anchor_repurpose_connection_set_anchor_to_invalid(self):
        """Test repurpose action status when anchor content fails validation"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            # First create and execute actions with valid input
            mock_repurpose_response = [
                ChatGeneration(
                    text="abcde",
                    message=AIMessage(content="abcde"),
                    generation_info={},
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_repurpose_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # Create anchor content action
                anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Anchor Content Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                [self.asset_ids[1], self.asset_ids[2]], []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                anchor_content_action_id = response.data[-1]["id"]

                # Execute the anchor content action
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 2)
                self.assertEqual(len(asset_group_ids), 0)

                # Create repurpose action connected to the anchor content action
                num_outputs = 2
                repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                repurpose_action_id = response.data[-1]["id"]

                # Check the status of the repurpose action
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_READY),
                )

                # Execute the repurpose action
                response = self.client.post(
                    f"/api/action/{repurpose_action_id}/execute/", {}, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the repurpose action is completed
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

            # Now test with input that will fail validation
            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {
                    "label": "FAIL",
                    "comment": "Test failure",
                }

                # Update anchor content action with input that will fail validation
                response = self.client.patch(
                    f"/api/action/{anchor_content_action_id}/update_inputs/",
                    {
                        "inputs": {
                            "assets": TofuDataListHandler.convert_tofu_data_to_json(
                                TofuDataListHandler.convert_asset_to_tofu_data(
                                    [self.asset_ids[0]], []
                                )
                            ),
                        }
                    },
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Execute the anchor content action again
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Check if the anchor content action status is FAILED
                anchor_action_status = self.client.get(
                    f"/api/action/{anchor_content_action_id}/get_status/"
                )
                self.assertEqual(anchor_action_status.status_code, status.HTTP_200_OK)
                self.assertEqual(
                    anchor_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_FAIL),
                )

                # Verify the anchor content action outputs
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 1)
                self.assertEqual(len(asset_group_ids), 0)

                # Check the status of the repurpose action
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )

                # The repurpose action should now be in MISSING_INPUT state
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(
                        ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                    ),
                )

                # Verify that the repurpose action's inputs now show empty anchor content
                repurpose_action = Action.objects.get(id=repurpose_action_id)
                self.assertIn("anchor_content", repurpose_action.inputs)
                anchor_content_data = repurpose_action.inputs["anchor_content"]
                input_asset_ids, input_asset_group_ids = TofuDataListHandler.get_assets(
                    anchor_content_data
                )
                self.assertEqual(len(input_asset_ids), 0)
                self.assertEqual(len(input_asset_group_ids), 0)

    @mock_aws
    def test_anchor_repurpose_connection_remove_one_asset(self):
        """Test repurpose action inputs update when one asset is removed from anchor content"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            mock_repurpose_response = [
                ChatGeneration(
                    text="abcde",
                    message=AIMessage(content="abcde"),
                    generation_info={},
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_repurpose_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # Create anchor content action with all assets
                anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Anchor Content Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                self.asset_ids, []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                anchor_content_action_id = response.data[-1]["id"]

                # Execute the anchor content action
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 3)  # All 3 assets
                self.assertEqual(len(asset_group_ids), 0)

                # Create repurpose action connected to the anchor content action
                num_outputs = 2
                repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                repurpose_action_id = response.data[-1]["id"]

                # Execute the repurpose action
                response = self.client.post(
                    f"/api/action/{repurpose_action_id}/execute/", {}, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the repurpose action has all assets in its input
                repurpose_action = Action.objects.get(id=repurpose_action_id)
                self.assertIn("anchor_content", repurpose_action.inputs)
                anchor_content_data = repurpose_action.inputs["anchor_content"]
                input_asset_ids, input_asset_group_ids = TofuDataListHandler.get_assets(
                    anchor_content_data
                )
                self.assertEqual(len(input_asset_ids), 3)  # All 3 assets
                self.assertEqual(len(input_asset_group_ids), 0)

                # Now remove one asset from the anchor content
                response = self.client.patch(
                    f"/api/action/{anchor_content_action_id}/update_inputs/",
                    {
                        "inputs": {
                            "assets": TofuDataListHandler.convert_tofu_data_to_json(
                                TofuDataListHandler.convert_asset_to_tofu_data(
                                    self.asset_ids[1:], []  # Remove the first asset
                                )
                            ),
                        }
                    },
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Execute the anchor content action again
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs now have 2 assets
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 2)  # Now 2 assets
                self.assertEqual(len(asset_group_ids), 0)

                # Check if the repurpose action's inputs reflect the change
                repurpose_action = Action.objects.get(id=repurpose_action_id)
                self.assertIn("anchor_content", repurpose_action.inputs)
                anchor_content_data = repurpose_action.inputs["anchor_content"]
                input_asset_ids, input_asset_group_ids = TofuDataListHandler.get_assets(
                    anchor_content_data
                )
                self.assertEqual(len(input_asset_ids), 2)  # Now 2 assets
                self.assertEqual(len(input_asset_group_ids), 0)

                # Verify that the repurpose action remains in READY state
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(ActionStatusType.ACTION_STATUS_TYPE_COMPLETE),
                )

    @mock_aws
    def test_anchor_repurpose_connection_remove_all_asset(self):
        """Test repurpose action inputs update when all assets are removed from anchor content"""

        # Set up mock AWS credentials and region
        mock_env = {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
        }

        with patch.dict(os.environ, mock_env):
            # Create the mock S3 bucket
            s3 = boto3.client("s3", region_name="us-east-1")
            s3.create_bucket(Bucket="tofu-uploaded-files")

            mock_repurpose_response = [
                ChatGeneration(
                    text="abcde",
                    message=AIMessage(content="abcde"),
                    generation_info={},
                )
            ]

            with (
                self.settings(
                    CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=True
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.asset_eval_builder.AssetEvalBuilder.check_asset_info",
                    new=create_mock_check_asset_info(),
                ),
                patch(
                    "api.content_gen.base_content_generator.BaseContentGenerator.get_results",
                    return_value=mock_repurpose_response,
                ),
                patch(
                    "api.evaluator.evaluators.pregen_evaluators.anchor_content_precheck.AnchorContentPrecheckEvaluator._llm_check_content"
                ) as mock_llm_check,
            ):
                # Mock the evaluator's LLM check method to prevent real API calls
                mock_llm_check.return_value = {"label": "PASS", "comment": None}

                # Create anchor content action with all assets
                anchor_content_action_data = {
                    "action_category": "ACTION_CATEGORY_USER_INPUT",
                    "action_name": "Anchor Content Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "assets": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_asset_to_tofu_data(
                                self.asset_ids, []
                            )
                        ),
                    },
                }
                response = self.client.post(
                    "/api/action/", anchor_content_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                anchor_content_action_id = response.data[-1]["id"]

                # Execute the anchor content action
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 3)  # All 3 assets
                self.assertEqual(len(asset_group_ids), 0)

                # Create repurpose action connected to the anchor content action
                num_outputs = 2
                repurpose_action_data = {
                    "action_category": "ACTION_CATEGORY_REPURPOSE",
                    "action_name": "Repurpose Action",
                    "campaign": self.campaign_id,
                    "playbook": self.playbook_id,
                    "inputs": {
                        "content_type": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_content_type_to_tofu_data(
                                "Email - SDR"
                            )
                        ),
                        "num_outputs": TofuDataListHandler.convert_tofu_data_to_json(
                            TofuDataListHandler.convert_int_to_tofu_data(num_outputs)
                        ),
                    },
                    "incoming_edges": [
                        {
                            "from_action": anchor_content_action_id,
                            "config": {
                                "output_name_from_incoming_action": "assets",
                                "input_name_to_outgoing_action": "anchor_content",
                            },
                        }
                    ],
                }

                response = self.client.post(
                    "/api/action/", repurpose_action_data, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                repurpose_action_id = response.data[-1]["id"]

                # Execute the repurpose action
                response = self.client.post(
                    f"/api/action/{repurpose_action_id}/execute/", {}, format="json"
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the repurpose action has all assets in its input
                repurpose_action = Action.objects.get(id=repurpose_action_id)
                self.assertIn("anchor_content", repurpose_action.inputs)
                anchor_content_data = repurpose_action.inputs["anchor_content"]
                input_asset_ids, input_asset_group_ids = TofuDataListHandler.get_assets(
                    anchor_content_data
                )
                self.assertEqual(len(input_asset_ids), 3)  # All 3 assets
                self.assertEqual(len(input_asset_group_ids), 0)

                # Now remove all assets from the anchor content
                response = self.client.patch(
                    f"/api/action/{anchor_content_action_id}/update_inputs/",
                    {
                        "inputs": {
                            "assets": TofuDataListHandler.convert_tofu_data_to_json(
                                TofuDataListHandler.convert_asset_to_tofu_data(
                                    [], []  # Remove all assets
                                )
                            ),
                        }
                    },
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Execute the anchor content action again
                response = self.client.post(
                    f"/api/action/{anchor_content_action_id}/execute/",
                    {},
                    format="json",
                )
                self.assertEqual(response.status_code, status.HTTP_200_OK)

                # Verify the anchor content action outputs now have 0 assets
                anchor_content_action = Action.objects.get(id=anchor_content_action_id)
                self.assertIn("assets", anchor_content_action.outputs)
                asset_data = anchor_content_action.outputs["assets"]
                asset_ids, asset_group_ids = TofuDataListHandler.get_assets(asset_data)
                self.assertEqual(len(asset_ids), 0)  # Now 0 assets
                self.assertEqual(len(asset_group_ids), 0)

                # Check if the repurpose action's inputs reflect the change
                repurpose_action = Action.objects.get(id=repurpose_action_id)
                self.assertIn("anchor_content", repurpose_action.inputs)
                anchor_content_data = repurpose_action.inputs["anchor_content"]
                input_asset_ids, input_asset_group_ids = TofuDataListHandler.get_assets(
                    anchor_content_data
                )
                self.assertEqual(len(input_asset_ids), 0)  # Now 0 assets
                self.assertEqual(len(input_asset_group_ids), 0)

                # Verify that the repurpose action remains in READY state
                repurpose_action_status = self.client.get(
                    f"/api/action/{repurpose_action_id}/get_status/"
                )
                self.assertEqual(
                    repurpose_action_status.status_code, status.HTTP_200_OK
                )
                self.assertEqual(
                    repurpose_action_status.data["status_type"],
                    ActionStatusType.Name(
                        ActionStatusType.ACTION_STATUS_TYPE_MISSING_INPUT
                    ),
                )
