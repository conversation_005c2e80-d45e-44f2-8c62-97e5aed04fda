import copy
import logging

from django.core.cache import cache

from .feature.data_wrapper.data_wrapper_campaign_v3 import (
    get_campaign_v3_content_group_asset_params,
)
from .gdrive_utils import create_and_save_gsheet
from .models import Content, ContentGroup
from .utils import create_presigned_url, merge_dict_with_value_list


def get_repurpose_asset_params_from_content(content_instance):
    asset_params = {}
    if content_instance.content_group.campaign:
        asset_params = content_instance.content_group.campaign.campaign_params.get(
            "assets", {}
        )
    if content_instance.content_group:
        asset_params = merge_dict_with_value_list(
            asset_params,
            content_instance.content_group.content_group_params.get("assets", {}),
        )

    asset_params_list = []
    for key, value in asset_params.items():
        value = value if isinstance(value, list) else [value]
        for k2 in value:
            asset_params_list.append(
                {
                    "assets": [{key: k2}],
                    "instruction": None,
                    "meta": "repurpose_anchor_content",
                }
            )
    # campaign v3 case.
    if content_instance.content_group.campaign.campaign_params.get(
        "is_campaign_v3", False
    ):
        # use orig_content_group_id of the content_group to get the asset params
        orig_content_group_id = content_instance.content_group.content_group_params.get(
            "orig_content_group_id", None
        )
        if orig_content_group_id:
            try:
                orig_content_group = ContentGroup.objects.get(id=orig_content_group_id)
            except ContentGroup.DoesNotExist:
                logging.error(
                    f"ContentGroup with id {orig_content_group_id} does not exist"
                )
                return asset_params_list

            campaign_v3_asset_params = get_campaign_v3_content_group_asset_params(
                orig_content_group
            )
            # need to make the assets a list of dicts.
            for asset_param in campaign_v3_asset_params:
                asset_params_list.append(
                    {
                        "assets": [asset_param["assets"]],
                        "instruction": None,
                        "meta": "repurpose_anchor_content",
                    }
                )
    return asset_params_list


def campaign_has_any_custom_instructions(campaign):
    try:
        campaign_params = campaign.campaign_params
        prompt_params = campaign_params.get("custom_instructions", [])
        prompt_params = [
            p for p in prompt_params if isinstance(p, dict) and p.get("instruction")
        ]
        if len(prompt_params) > 0:
            return True
        content_groups = ContentGroup.objects.filter(campaign=campaign)
        for content_group in content_groups:
            prompt_params.extend(
                content_group.content_group_params.get("custom_instructions", [])
            )
            prompt_params = [
                p for p in prompt_params if isinstance(p, dict) and p.get("instruction")
            ]
            if len(prompt_params) > 0:
                return True
            contents = Content.objects.filter(content_group=content_group)
            components = content_group.components
            for content in contents:
                prompt_params.extend(
                    content.content_params.get("custom_instructions", [])
                )
                prompt_params = [
                    p
                    for p in prompt_params
                    if isinstance(p, dict) and p.get("instruction")
                ]
                if len(prompt_params) > 0:
                    return True
                keys = list(components.keys())
                for component_id in keys:
                    component = components[component_id]
                    metadata = component.get("meta", {})
                    component_prompt_params = metadata.get("component_params", {}).get(
                        "custom_instructions", []
                    )
                    prompt_params.extend(component_prompt_params)
                    prompt_params = [
                        p
                        for p in prompt_params
                        if isinstance(p, dict) and p.get("instruction")
                    ]
                    if len(prompt_params) > 0:
                        return True
    except Exception as e:
        logging.error(f"An error occurred while fetching custom instructions: {e}")
        return False
    return False


def get_asset_names_from_content(content):
    content_params = content.content_params
    prompt_params = content_params.get("custom_instructions", [])
    content_group = content.content_group
    campaign = content_group.campaign
    prompt_params.extend(
        content_group.content_group_params.get("custom_instructions", [])
    )
    prompt_params.extend(campaign.campaign_params.get("custom_instructions", []))
    asset_params = get_asset_params_from_prompt_params(prompt_params)

    asset_names = {}
    for asset_param in asset_params:
        for asset_type, asset_list in asset_param["assets"].items():
            if asset_type not in asset_names:
                asset_names[asset_type] = []
            asset_names[asset_type].extend(asset_list)
    return asset_names


def get_asset_params_from_prompt_params(prompt_params):
    asset_params = copy.deepcopy(prompt_params)
    asset_params = [p for p in asset_params if isinstance(p, dict) and p.get("assets")]
    return asset_params


def fetch_asset(playbook_instance, key1, key2):
    if key1 in playbook_instance.assets:
        v1 = playbook_instance.assets[key1]
        if key2 in v1:
            data = v1[key2].get("data", [])
            # assume first data entry
            for entry in data:
                datatype = entry.get("type", "")
                if datatype == "url":
                    return entry.get("value", "")
                elif datatype == "text":
                    data = entry.get("value", "")
                    gdrive_filename = f"{playbook_instance.id}-{key1}-{key2}"
                    # check if the data is already uploaded to gdrive and cached.
                    cache_key = f"fetch_asset_gdrive_{gdrive_filename}"
                    gdrive_link = cache.get(cache_key)
                    if not gdrive_link:
                        fields = ["content"]
                        results = [fields]
                        results.append([data])
                        result_data = create_and_save_gsheet(
                            gdrive_filename, fields, results
                        )
                        gdrive_link = result_data["google_sheet_url"]
                        logging.info(f"Uploaded text asset to gdrive: {gdrive_link}")
                        cache.set(cache_key, gdrive_link, 60 * 60 * 24 * 365)
                    return gdrive_link
                elif datatype == "file":
                    value = entry.get("value", {})
                    s3_bucket = value.get("s3_bucket", "")
                    s3_filename = value.get("s3_filename", "")
                    if s3_bucket and s3_filename:
                        # get presigned s3 url
                        presigned_s3_url = create_presigned_url(s3_bucket, s3_filename)
                        return presigned_s3_url
                else:
                    return ""
