import logging
from typing import Any, Optional, Type

from langchain_core.callbacks import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_core.tools import BaseTool

from ..feature.data_wrapper.data_wrapper import (
    DefaultGenSettings,
    GenerateEnv,
    PlaybookWrapper,
)
from ..feature.feature_builder.target_context_feature_builder import (
    TargetContextBuilder,
)
from ..models import Playbook, TargetInfo, TargetInfoGroup


class PlaybookTargetInfoToolInput(BaseModel):
    target_list_name: str = Field(
        description="The name used to identify the list where the target is in. A combination of (target_list_name, target_name) is unique to identify a target in the Playbook."
    )
    target_name: str = Field(
        description="The name used to identify a target in a list. A combination of (target_list_name, target_name) is unique to identify a target in the Playbook"
    )


class PlaybookTargetInfoTool(BaseTool):
    name = "fetch_target_info_from_playbook"
    description = """This tool is for fetching information from Playbook for your target audience (persona, company, contact, industry, etc). 
    Only use this tool if the user asks you to write content for a target.
    You shouldn't call this function if the target doesn't exist in the Playbook.
    Return: information of the target. In the case when the target doesn't exist in the Playbook, return empty string.
    """
    args_schema: Type[BaseModel] = PlaybookTargetInfoToolInput
    playbook_instance: Optional[Any] = None

    def _run(
        self,
        target_list_name: str,
        target_name: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Use the tool."""
        target_info_group = (
            TargetInfoGroup.objects.filter(playbook=self.playbook_instance)
            .filter(target_info_group_key=target_list_name)
            .first()
        )
        if not target_info_group:
            return ""
        target_info = (
            TargetInfo.objects.filter(target_info_group=target_info_group)
            .filter(target_key=target_name)
            .first()
        )
        if not target_info:
            return ""

        data_wrapper = PlaybookWrapper(self.playbook_instance)
        gen_settings = DefaultGenSettings(data_wrapper)
        gen_env = GenerateEnv(data_wrapper, gen_settings)

        target_context_builder = TargetContextBuilder(gen_env=gen_env)
        target_context_builder.set_budgets({"target_context": 4000})
        single_target_context = target_context_builder.get_single_target_context(
            target_list_name, target_name
        )
        return single_target_context

    async def _arun(
        self,
        target_list_name: str,
        target_name: str,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
    ) -> str:
        """Use the tool asynchronously."""
        raise NotImplementedError("this tool does not support async")
