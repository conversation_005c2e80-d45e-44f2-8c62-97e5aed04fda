import logging

from langchain_core.messages import AIMessage, HumanMessage
from langchain_openai.chat_models.base import _convert_message_to_dict

from ..data_loaders.url_loader import TofuURLLoader


def serialize_message_history(messages):
    message_history = []
    for m in messages:
        message_dict = _convert_message_to_dict(m)
        # check for additional_kwargs in the message, and add it to the dict if present.
        additional_kwargs = getattr(m, "additional_kwargs", None)
        if additional_kwargs:
            for k, v in additional_kwargs.items():
                message_dict[k] = v
        message_history.append(message_dict)

    return message_history


def crawl_url(url):
    MAX_CONTENT_LENGTH = 5000  # Move to configuration
    try:
        url_loader = TofuURLLoader(url)
        docs = url_loader.load()
        if not docs:
            logging.warning(f"No content found for URL: {url}")
            return ""

        contents_string = "\n".join([doc.page_content for doc in docs])
        return contents_string[:MAX_CONTENT_LENGTH]
    except Exception as e:
        logging.error(f"Failed to crawl URL {url}: {e}")
        return ""


def crawl_urls(urls):
    url_contents = {}
    for url in urls:
        url_contents[url] = crawl_url(url)
    return url_contents


def remove_image_messages(messages):
    """
    Remove AI messages that contain image URLs and their associated previous human messages.

    Args:
        messages: List of message objects

    Returns:
        List of messages with image messages and their associated previous human messages removed
    """
    filtered_messages = []

    # First pass: Handle AI messages with image URLs
    for message in messages:
        # Check if this is an AI message with image URLs
        if (
            isinstance(message, AIMessage)
            and hasattr(message, "additional_kwargs")
            and message.additional_kwargs
            and "image_urls" in message.additional_kwargs
        ):
            # Remove the last added message only if it's a human message
            if filtered_messages and isinstance(filtered_messages[-1], HumanMessage):
                filtered_messages.pop()
            continue

        filtered_messages.append(message)

    final_messages = []
    # Second pass: Handle human messages with image URLs
    current_index = 0
    while current_index < len(filtered_messages):
        current_message = filtered_messages[current_index]

        # Skip this message and its AI response if it's a human message with image URLs
        if (
            isinstance(current_message, HumanMessage)
            and hasattr(current_message, "additional_kwargs")
            and current_message.additional_kwargs
            and "image_urls" in current_message.additional_kwargs
        ):
            # Skip the human message with image
            current_index += 1

            # Skip the following AI response if it exists
            has_next_message = current_index < len(filtered_messages)
            next_is_ai = has_next_message and isinstance(
                filtered_messages[current_index], AIMessage
            )
            if has_next_message and next_is_ai:
                current_index += 1
            continue

        final_messages.append(current_message)
        current_index += 1

    return final_messages
