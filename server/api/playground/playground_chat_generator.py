import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from django.core.cache import cache
from langchain.schema import HumanMessage

from ..feature.data_wrapper.data_wrapper import (
    DefaultGenSettings,
    GenerateEnv,
    PlaybookWrapper,
)
from ..feature.feature_builder.asset_context_feature_builder import (
    AssetContextFeatureBuilder,
)
from ..feature.feature_builder.target_context_feature_builder import (
    TargetContextBuilder,
)
from ..langsmith_integration import BaseTracableClass, dynamic_traceable
from ..models import Playbook
from .playground_feature_builder import PlaygroundFeatureBuilder


@dataclass
class GenerateChatRequest:
    """Input parameters for chat generation."""

    task_id: str
    model: str
    previous_messages: List[Any]
    new_message: str
    use_company_info: bool = True
    use_brand_guidelines: bool = True
    targets: Optional[Dict[str, List[str]]] = None
    assets: Optional[Dict[str, List[str]]] = None
    image_gen: bool = False

    def __post_init__(self):
        """Validate the input parameters."""
        if not self.task_id:
            raise ValueError("task_id is required")
        if not self.model:
            raise ValueError("model is required")
        if not self.new_message:
            raise ValueError("new_message is required")
        if not isinstance(self.previous_messages, list):
            raise ValueError("previous_messages must be a list")
        if not isinstance(self.use_company_info, bool):
            raise ValueError("use_company_info must be a boolean")
        if not isinstance(self.use_brand_guidelines, bool):
            raise ValueError("use_brand_guidelines must be a boolean")
        if self.targets is not None and not isinstance(self.targets, dict):
            raise ValueError("targets must be a dictionary")
        if self.assets is not None and not isinstance(self.assets, dict):
            raise ValueError("assets must be a dictionary")
        if not isinstance(self.image_gen, bool):
            raise ValueError("image_gen must be a boolean")


class BasePlaygroundChatGenerator(BaseTracableClass, ABC):
    def __init__(self, user, chat_history, thread_id):
        super().__init__()
        self.user = user
        self.chat_history = chat_history
        self.thread_id = thread_id

        self.playbook = Playbook.objects.filter(users=self.user).first()
        if not self.playbook:
            raise Exception("No Playbook associated with the user.")
        data_wrapper = PlaybookWrapper(self.playbook)
        gen_settings = DefaultGenSettings(data_wrapper)
        self.gen_env = GenerateEnv(data_wrapper, gen_settings)

    def get_metadata(self):
        return {
            "username": self.user.username if self.user else "unknown",
            "chat_thread_key": self.chat_history.key,
        }

    def get_targets_message(self, targets, target_budget=4000):
        if not isinstance(target_budget, int):
            raise Exception("Target budget is not an integer")
        target_context_builder = TargetContextBuilder(gen_env=self.gen_env)
        target_context_builder.set_budgets({"target_context": target_budget})
        target_info = ""
        target_tuple_set = set()
        for key, value in targets.items():
            target_list_name = key
            for target_name in value:
                target_tuple_set.add((target_list_name, target_name))

        for target_list_name, target_name in target_tuple_set:
            target_context = target_context_builder.get_single_target_context(
                target_list_name, target_name, max_token_size=target_budget
            )
            target_info += f'<target name="{target_name}">\n{target_context}\n</target>'
        return HumanMessage(
            content=target_info, additional_kwargs={"keep_hidden": True}
        )

    def get_assets_message(
        self,
        assets,
        additional_dynamic_index_query=None,
        asset_budget=4000,
    ):
        if not isinstance(asset_budget, int):
            raise Exception("Asset budget is not an integer")
        asset_context_builder = AssetContextFeatureBuilder(gen_env=self.gen_env)
        asset_context_builder.set_budgets({"asset_context": asset_budget})
        asset_info = ""
        asset_tuple_set = set()
        for key, value in assets.items():
            asset_list_name = key
            for asset_name in value:
                asset_tuple_set.add((asset_list_name, asset_name))

        for asset_list_name, asset_name in asset_tuple_set:
            asset_context = asset_context_builder.get_asset_context(
                asset_list_name,
                asset_name,
                additional_dynamic_index_query=additional_dynamic_index_query,
                max_token_size=asset_budget,
            )
            if asset_context:
                asset_info += asset_context
        return HumanMessage(content=asset_info, additional_kwargs={"keep_hidden": True})

    def get_system_context(self):
        # Fetch the playbook instance for the user
        playground_feature_builder = PlaygroundFeatureBuilder(self.gen_env)
        company_name = playground_feature_builder.get_company_name()
        company_context = playground_feature_builder.get_company_context()
        valid_target_keys = playground_feature_builder.get_valid_target_keys()
        brand_guidelines = playground_feature_builder.get_brand_guidelines()
        return {
            "company_name": company_name,
            "company_context": company_context,
            "valid_target_keys": valid_target_keys,
            "brand_guidelines": brand_guidelines,
        }

    @dynamic_traceable(name="playground_chat")
    @abstractmethod
    def generate_chat_response(self, chat_input: GenerateChatRequest):
        """Generate a chat response based on the input parameters.

        Args:
            chat_input: The input parameters for chat generation.

        Returns:
            The generated chat response.
        """
        raise NotImplementedError("Subclasses must implement generate_chat_response")
