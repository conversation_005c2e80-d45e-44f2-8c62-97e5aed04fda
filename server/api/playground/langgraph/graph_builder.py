import logging
import threading
from typing import Dict

from langgraph.graph import E<PERSON>, StateGraph

from ...feature.data_wrapper.data_wrapper import GenerateEnv
from ...utils import CloudWatchMetrics
from ..chatbot_status import ChatbotStepStatusUpdator
from ..playground_chat_generator import GenerateC<PERSON>Request
from .models import Cha<PERSON><PERSON><PERSON>, UserIntent
from .nodes.intent_identification_node import IntentIdentificationNode
from .nodes.reflection_node import ReflectionNode
from .nodes.response_generation_node import ResponseGenerationNode
from .nodes.result_synthesis_node import ResultSynthesisNode
from .nodes.tool_selection_node import ToolSelectionNode
from .tools.tool_registry import create_final_response_tools, create_tools


class GraphBuilder:

    def get_or_create_graph(
        self,
        chat_request: GenerateChatRequest,
        gen_env: GenerateEnv,
        status_updator: ChatbotStepStatusUpdator,
    ) -> StateGraph:
        """Returns a graph for the given chat_model_name."""
        return self._build_graph(chat_request, gen_env, status_updator)

    def _build_graph(self, chat_request, gen_env, status_updator) -> StateGraph:
        """Build the graph with specialized nodes."""
        try:

            model_name = chat_request.model
            playbook_id = gen_env._data_wrapper.playbook_instance.id
            task_id = chat_request.task_id

            # Create tools
            available_tools = create_tools(
                gen_env,
                asset_budget=4000,
                target_budget=4000,
                playbook_id=playbook_id,
                task_id=task_id,
                chat_request=chat_request,
                status_updator=status_updator,
            )
            final_response_tools = create_final_response_tools(
                model_name=model_name,
                gen_env=gen_env,
                chat_request=chat_request,
                playbook_id=playbook_id,
                task_id=task_id,
                status_updator=status_updator,
            )
            # Create a graph with the ChatState schema
            graph = StateGraph(ChatState)

            # Create node instances
            intent_node = IntentIdentificationNode(status_updator)

            tool_node = ToolSelectionNode(
                tools=available_tools.values(),
                model_name=model_name,
                status_updator=status_updator,
            )

            synthesis_node = ResultSynthesisNode(status_updator)

            reflection_node = ReflectionNode(
                status_updator=status_updator,
                model_name=model_name,
            )

            response_node = ResponseGenerationNode(
                model_name=model_name,
                tools=final_response_tools.values(),
                status_updator=status_updator,
            )

            all_nodes = [
                intent_node,
                tool_node,
                synthesis_node,
                reflection_node,
                response_node,
            ]

            # Add nodes to graph
            for node in all_nodes:
                graph.add_node(node.node_name, node)

            # Define the edges (connections between nodes)
            def intent_router(state: ChatState):
                if state["intent"] == UserIntent.NON_MARKETING_QUESTION:
                    return response_node.node_name
                return tool_node.node_name

            graph.add_conditional_edges(
                intent_node.node_name,
                intent_router,
                {
                    tool_node.node_name: tool_node.node_name,
                    response_node.node_name: response_node.node_name,
                },
            )

            graph.add_edge(tool_node.node_name, synthesis_node.node_name)
            graph.add_edge(synthesis_node.node_name, reflection_node.node_name)

            # Add conditional edge from reflection
            def reflection_router(state: ChatState):
                if state["needs_more_info"]:
                    return tool_node.node_name
                return response_node.node_name

            graph.add_conditional_edges(
                reflection_node.node_name,
                reflection_router,
                {
                    tool_node.node_name: tool_node.node_name,
                    response_node.node_name: response_node.node_name,
                },
            )

            graph.add_edge(response_node.node_name, END)

            # Set the entry point
            graph.set_entry_point(intent_node.node_name)

            # Compile and return the graph
            return graph.compile()

        except Exception as e:
            logging.exception(f"Error building graph: {e}")
            CloudWatchMetrics.put_metric("langgraph_graph_build_error", 1, [])
            raise


# Global instance
graph_builder = GraphBuilder()


# Simple accessor function
def get_or_create_graph(
    thread_id: str,
    chat_request: GenerateChatRequest,
    gen_env: GenerateEnv,
    status_updator: ChatbotStepStatusUpdator,
) -> StateGraph:
    return graph_builder.get_or_create_graph(chat_request, gen_env, status_updator)
