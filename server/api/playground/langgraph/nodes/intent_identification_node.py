import logging
from typing import Any, Dict

from langchain_core.messages import HumanMessage

from ....prompt.prompt_library.playground_deep_research_prompt import (
    INTENT_IDENTIFICATION_PROMPT,
)
from ....utils import get_token_count
from ...chatbot_status import Chat<PERSON><PERSON>tepStatusUpdator, PlaygroundChatbotStep
from ...langgraph.models import Chat<PERSON>tate, UserIntent
from ...langgraph.nodes.base_node import BaseNode


class IntentIdentificationNode(BaseNode):
    """Node for identifying user intent from messages."""

    model_name = "us.anthropic.claude-3-5-sonnet-20240620-v1:0"

    @property
    def node_name(self) -> str:
        """Get the node name for this instance."""
        return "intent_identification"

    def __init__(self, status_updator: ChatbotStepStatusUpdator):
        super().__init__(IntentIdentificationNode.model_name, status_updator)
        self.llm = self.get_llm()

    def process(self, state: ChatState) -> ChatState:
        """Identify the user's intent from their message."""

        self.add_to_state(state, "intent", UserIntent.MARKETING_QUESTION)
        return state

        # Retrieve the user's message from the state
        message = self.get_user_original_query(state)
        self.status_updator.update_chatbot_step(
            PlaygroundChatbotStep.THINKING,
            f"Let me carefully analyze your question to understand what you're looking for. I'm taking a moment to consider the context and nuances of your request:\n"
            f"{message}",
        )
        token_budget = self.model_caller.model_config.model_budget
        remaining_token_budget = token_budget - self.get_current_token_count(
            state, include_state=False
        )

        if remaining_token_budget < 0:
            logging.warning(
                f"Token limit exceeded by {remaining_token_budget}. Attempting reduction."
            )
            remaining_token_budget = 0

        previous_messages_str = self._get_previous_messages_str(
            state, remaining_token_budget * 0.6
        )
        # Format the prompt with the user's message
        formatted_prompt = [
            HumanMessage(
                content=INTENT_IDENTIFICATION_PROMPT.format(
                    message=message, previous_messages=previous_messages_str
                )
            )
        ]

        # Get the response from the LLM using model_caller
        response = self.model_caller.get_llm_dict_response(formatted_prompt)

        try:
            # Extract the intent from the response
            intent = response.get("intent")

            # Map the string intent to the UserIntent enum
            intent_mapping = {
                "marketing_question": UserIntent.MARKETING_QUESTION,
                "content_generation": UserIntent.CONTENT_GENERATION,
                "non_marketing_question": UserIntent.NON_MARKETING_QUESTION,
            }

            user_intent = intent_mapping.get(intent, UserIntent.NON_MARKETING_QUESTION)
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.THINKING,
                f"I understand what you're looking for. Let me gather the most relevant information and insights to help you with this.\n",
            )

            logging.info(
                f"🧠 [{self.node_name}] Identified intent: {user_intent.value}"
            )
            self.add_to_state(state, "intent", user_intent)

        except KeyError as e:
            logging.error(f"🧠 [{self.node_name}] Error processing intent: {e}")
            self.add_to_state(state, "intent", UserIntent.NON_MARKETING_QUESTION)

        return state
