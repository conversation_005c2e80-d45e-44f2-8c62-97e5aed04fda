import logging
import time
from typing import Any, Dict, List

from langchain_core.messages import AIMessage, BaseMessage, SystemMessage
from langchain_core.tools import BaseTool
from langgraph.prebuilt.chat_agent_executor import create_tool_calling_executor

from ...chatbot_status import Cha<PERSON><PERSON><PERSON>tepStatusUpdator, PlaygroundChatbotStep
from ...langgraph.callbacks import PlaygroundCallbackHandler
from ...langgraph.nodes.base_node import BaseNode
from ..models import ChatState, UserIntent


class ToolSelectionNode(BaseNode):
    """Node for selecting and executing tools."""

    def __init__(
        self,
        status_updator: ChatbotStepStatusUpdator,
        tools: List[BaseTool],
        model_name: str = "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
    ):
        super().__init__(model_name, status_updator)
        self.tools = tools
        # The tool agent node only accept langchain llm, so we need to convert the model caller to a langchain llm
        self.llm = self.get_llm()
        self.tool_executor = self.custom_agent_executor(tools, self.llm, status_updator)

    @property
    def node_name(self) -> str:
        """Get the node name for this instance."""
        return "tool_selection"

    # we don't use it for now, but we can use it to add hardcoded tools to the state
    def _add_hard_coded_tools(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Add hardcoded tools to the state."""
        if state.get("targets"):
            state["messages"].append(
                AIMessage(
                    content="",
                    tool_calls=[
                        {
                            "name": "get_targets_message",
                            "args": {"targets": state.get("targets")},
                        }
                    ],
                )
            )
        if state.get("assets"):
            state["messages"].append(
                AIMessage(
                    content="",
                    tool_calls=[
                        {
                            "name": "get_assets_message",
                            "args": {"assets": state.get("assets")},
                        }
                    ],
                )
            )
        return state

    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Select and execute appropriate tools based on intent."""
        logging.info(f"🛠️ [{self.node_name}] Tool selection")

        # Check if we're approaching timeout before starting tool execution
        if self.is_timeout_approaching(state):
            logging.warning(
                "🚨 Timeout threshold reached (70%), skipping tool execution."
            )
            self.add_to_state(
                state,
                "tool_result",
                "Tool execution skipped due to timeout approaching.",
            )
            self.add_to_state(state, "raw_tool_results", [])
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.THINKING,
                "Time limit approaching. Skipping tool execution and proceeding with available information.",
                step_finished=True,
            )
            return state

        intent = UserIntent(state.get("intent"))
        self.status_updator.update_chatbot_step(
            PlaygroundChatbotStep.THINKING,
            f"I understand what you're looking for. Let's start by using our available tools to collect the necessary data.\n",
        )
        result = self.tool_executor(state)
        logging.info(
            f"🛠️ [{self.node_name}] Raw tool executor result type: {type(result)}"
        )

        tool_result = self.get_latest_ai_response(result)
        self.add_to_state(state, "tool_result", tool_result)

        # Extract raw tool results from ToolMessages within the messages list
        raw_tool_results = []
        if "messages" in result:
            from langchain_core.messages import ToolMessage  # Import ToolMessage

            for message in result["messages"]:
                if isinstance(message, ToolMessage):
                    raw_tool_results.append(
                        {
                            "tool_call_id": message.tool_call_id,
                            "content": message.content,
                            "name": message.name,
                        }
                    )

        if raw_tool_results:
            self.add_to_state(state, "raw_tool_results", raw_tool_results)
            logging.info(
                f"🛠️ [{self.node_name}] Extracted raw tool results from messages: {raw_tool_results}"
            )
        else:
            logging.info(f"🛠️ [{self.node_name}] No ToolMessages found in the result.")

        # Keep the check for __tool_calls__ just in case it appears sometimes
        if isinstance(result, dict) and result.get("__tool_calls__"):
            self.add_to_state(state, "raw_tool_calls", result["__tool_calls__"])

        return state

    @staticmethod
    def custom_agent_executor(tools, llm, status_updator: ChatbotStepStatusUpdator):
        def tool_call_prompt(state: ChatState) -> List[BaseMessage]:
            messages = list(
                state.get("messages", [])
            )  # Copy to avoid mutating original
            remaining_steps = state.get("remaining_steps")

            # Prepare the extra info to inject
            system_additions = [
                "======================",
                "Tool Calling Rules: ",
                f"You have {remaining_steps} steps left to call tools. If the remaining steps is 0, stop and return the best results you can.",
            ]
            reflection_result = state.get("reflection_output")
            if reflection_result:
                system_additions.append(
                    f"""Previous reflection result: {reflection_result}. 
                                        For the next tool call, please only focus on the missing information from the previous reflection result.
                                        If you believe we can't get more information from the previous reflection result, stop calling tools and return the best results you can.
                                        """
                )

            # Merge into the first system message if present
            if messages and isinstance(messages[0], SystemMessage):
                # Combine the additions and the original content
                original_content = messages[0].content
                merged_content = original_content + "\n" + "\n".join(system_additions)
                messages[0] = SystemMessage(content=merged_content)
            else:
                # No system message present, so insert one at the start
                merged_content = "\n".join(system_additions)
                messages.insert(0, SystemMessage(content=merged_content))

            return messages

        agent = create_tool_calling_executor(
            tools=tools, model=llm, prompt=tool_call_prompt, state_schema=ChatState
        )

        def wrapped_agent(state):
            remaining_steps = state.get("remaining_steps", 1)
            if remaining_steps <= 0:
                logging.warning("🚨 Step limit reached, forcing final answer.")
                state["messages"].append(
                    AIMessage(
                        content="Step limit reached. Returning best results so far.",
                    )
                )
                return state

            logging.info("🧠 [agent] LLM thinking...")
            result = agent.invoke(
                state, config={"callbacks": [PlaygroundCallbackHandler(status_updator)]}
            )
            logging.info(f"🛠️ [agent] Tool result: {result}")
            if result.get("__tool_calls__"):
                logging.info("🛠️  [agent] Tool calls planned:")
                for call in result["__tool_calls__"]:
                    logging.info(f"   → {call['name']}({call['args']})")
            else:
                logging.info("💬 [agent] No tool calls. Might be final response.")
            return result

        return wrapped_agent
