import logging
from typing import Any, Dict, List

from langchain_core.messages import HumanMessage
from langchain_core.tools import BaseTool
from langgraph.prebuilt.chat_agent_executor import create_tool_calling_executor

from ...chatbot_status import ChatbotStepStatusUpdator, PlaygroundChatbotStep
from ...langgraph.hooks import create_pre_model_hook_function
from ...langgraph.models import ChatState
from ...langgraph.nodes.base_node import BaseNode


class ResponseGenerationNode(BaseNode):
    """Node for generating the final response."""

    @property
    def node_name(self) -> str:
        """Get the node name for this instance."""
        return "response_generation"

    def __init__(
        self,
        model_name: str,
        status_updator: ChatbotStepStatusUpdator,
        tools: List[BaseTool],
    ):
        super().__init__(model_name, status_updator)
        self.llm = self.get_llm()
        pre_model_hook_func = create_pre_model_hook_function(
            self.model_caller.model_config.model_budget
        )
        self.agent = create_tool_calling_executor(
            tools=tools, model=self.llm, pre_model_hook=pre_model_hook_func
        )

    def preprocess(self, state: ChatState) -> ChatState:
        """Preprocess the state and return updated state."""
        # in this step, we need to mark all previous steps as finished
        self.status_updator.mark_all_running_steps_as_finished()
        return state

    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final response to the user."""
        logging.info(f"💬 [{self.node_name}] Starting response generation")
        self.status_updator.update_chatbot_step(
            PlaygroundChatbotStep.RESPONSE_GENERATION,
            f"Start to generate the response for you.",
        )
        # Let the agent generate the response with just the prompt message
        state = self._update_user_prompt(state)

        result = self.agent.invoke(state)
        # Get the final response
        final_response = self.get_latest_ai_response(result)
        # update the status
        if (
            PlaygroundChatbotStep.RESPONSE_GENERATION
            in self.status_updator.get_current_running_steps()
        ):
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.RESPONSE_GENERATION,
                f"We have generated a response for you. \n\n {final_response} \n\n",
            )
        self.add_to_state(state, "final_response", final_response)
        return state

    def _update_user_prompt(self, state: ChatState) -> ChatState:
        # Extract necessary information from the state
        tool_results = state.get("tool_result", "")
        reflection = state.get("reflection_output", "")
        intent = state.get("intent", "")

        # Retrieve the original user query
        original_query = self.get_user_original_query(state)

        # Construct the prompt dynamically
        prompt_parts = []

        if original_query:
            prompt_parts.append(f"User's Request:\n{original_query}")
        if tool_results:
            prompt_parts.append(f"Tool Results:\n{tool_results}")
        if reflection:
            prompt_parts.append(f"Reflection:\n{reflection}")
        if intent:
            prompt_parts.append(f"Intent:\n{intent}")

        prompt_parts.append(
            "Based on the above information, please proceed accordingly."
        )

        dynamic_prompt = "\n\n".join(prompt_parts)

        # pop the last message from the messages list
        if isinstance(state["messages"][-1], HumanMessage):
            state["messages"].pop()
        # append the dynamic prompt as a HumanMessage
        state["messages"].append(HumanMessage(content=dynamic_prompt))

        return state
