import logging
from typing import Any, Dict

from ...chatbot_status import ChatbotStepStatusUpdator
from ...langgraph.nodes.base_node import BaseNode


class ResultSynthesisNode(BaseNode):
    """Simple node that concatenates raw results from tools."""

    model_name = "us.anthropic.claude-3-5-sonnet-20240620-v1:0"

    def __init__(self, status_updator: ChatbotStepStatusUpdator):
        super().__init__(ResultSynthesisNode.model_name, status_updator)

    @property
    def node_name(self) -> str:
        """Get the node name for this instance."""
        return "result_synthesis"

    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        logging.info(f"📊 [{self.node_name}] Concatenating raw tool results")
        # Get raw tool results if available
        raw_tool_results = state.get("raw_tool_results", [])

        # Concatenate all raw tool results
        synthesis = "\n".join(
            f"Tool '{result['name']}' output:\n{result['content']}"
            for result in raw_tool_results
        )

        # Store in state
        self.add_to_state(state, "synthesis_content", synthesis)

        return state
