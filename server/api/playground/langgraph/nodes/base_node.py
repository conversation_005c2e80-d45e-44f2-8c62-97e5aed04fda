import json
import logging
import time
from abc import ABC, abstractmethod
from typing import Any, Dict

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.messages.utils import count_tokens_approximately

from ....llms import get_llm_for_model_name
from ....model_caller import ModelCaller
from ....model_config import ModelConfigResolver
from ....task_registry import GenerationGoal
from ....utils import CloudWatchMetrics, get_token_count
from ...chatbot_status import ChatbotStepStatusUpdator
from ..models import ChatState


class BaseNode(ABC):
    """Abstract base class for all graph nodes with configurable model caller."""

    def __init__(self, model_name: str, status_updator: ChatbotStepStatusUpdator):
        """
        Initialize node with a specific model caller.

        Args:
            model_caller: The model caller instance to use for this node
        """
        self.model_caller = self.get_model_caller_from_model_name(model_name)
        self.status_updator = status_updator

    @property
    def node_name(self) -> str:
        """Get the node name for this instance."""
        return "base_node"

    def get_llm(self):
        model_config = self.model_caller.model_config
        for model_param in model_config.model_params_list:
            model_kwargs = model_param.model_params
            return get_llm_for_model_name(**model_kwargs)
        raise ValueError(f"Model not found in model config")

    @staticmethod
    def get_model_caller_from_model_name(model_name):
        model_config = ModelConfigResolver.resolve(
            GenerationGoal.CHATBOT, foundation_model=model_name
        )
        return ModelCaller(model_config)

    def get_latest_message(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Get the latest message from the state."""
        if not state.get("messages"):
            raise ValueError("No messages found in state")
        return state["messages"][-1]

    def get_latest_ai_response(self, result: Dict[str, Any]) -> str:
        """Get the latest AI response from the result.

        Args:
            result: The result from the agent

        Returns:
            str: The content of the latest AI response

        Raises:
            ValueError: If no messages or no AI response is found
        """
        if "messages" not in result:
            raise ValueError("No messages found in result")

        # Try to find the last AI message
        final_message = next(
            (msg for msg in reversed(result["messages"]) if isinstance(msg, AIMessage)),
            None,
        )

        if not final_message:
            raise ValueError("No AI response found in result")

        return final_message.content

    def get_user_original_query(self, state: ChatState) -> str:
        """
        Get the user's original query from the state's message history.

        Args:
            state: The current state dictionary containing messages

        Returns:
            str: The content of the most recent user message

        Raises:
            ValueError: If no user message is found in the state
        """
        if "messages" not in state or not state["messages"]:
            raise ValueError("No messages found in state")

        for message in reversed(state["messages"]):
            if isinstance(message, HumanMessage):
                return message.content

        raise ValueError("No user message found in message history")

    def get_system_message(self, state: ChatState) -> str:
        """Get the system message from the state."""
        if "messages" not in state or not state["messages"]:
            raise ValueError("No messages found in state")
        for message in state["messages"]:
            if message.type == "system":
                return message.content
        raise ValueError("No system message found in message history")

    # TODO: implement a previous message summarization node
    def _get_previous_messages_str(self, state: ChatState, token_budget: int) -> str:
        """Get the previous messages from the state, honor the token budget."""
        if token_budget < 0:
            raise ValueError("Token budget must be greater than 0")
        # handle no previous messages case
        if len(state["messages"]) < 3:
            return ""
        all_previous_messages = state["messages"]
        # calculate the token count of the messages
        token_count = get_token_count(
            "\n".join([message.content for message in all_previous_messages])
        )
        while token_count > token_budget:
            if len(all_previous_messages) == 2:
                # we cannot trim anymore
                break
            all_previous_messages = all_previous_messages[2:]
            token_count = get_token_count(
                "\n".join([message.content for message in all_previous_messages])
            )
        if token_count > token_budget:
            logging.error(
                f"Token count is still greater than the token budget: {token_count}, return the first two messages"
            )
            CloudWatchMetrics.put_metric(
                "previous_messages_token_count_exceed_budget", 1, []
            )
            return "\n".join([message.content for message in all_previous_messages])
        return "\n".join([message.content for message in all_previous_messages])

    @staticmethod
    def get_current_token_count(state: ChatState, include_state: bool = False) -> int:
        """Get the current token count of the state.
        For tooling node, we will need to pass all state messages to LLM
        For other node, we only consider the messages
        """
        messages_tokens = count_tokens_approximately(state["messages"])
        if include_state:
            state_dict = ChatState.to_serializable_dict(state).copy()
            state_dict.pop("messages")
            return messages_tokens + get_token_count(json.dumps(state_dict))
        else:
            return messages_tokens

    def add_to_state(self, state: ChatState, key: str, value: Any) -> ChatState:
        """Helper to add data to state with logging."""
        state[key] = value
        logging.info(f"📝 [{self.node_name}] Added {key} to state")
        return state

    def is_timeout_approaching(
        self, state: ChatState, threshold_percentage: float = 0.7
    ) -> bool:
        """
        Check if we're approaching the timeout threshold.

        Args:
            state: Current chat state containing execution start time and timeout
            threshold_percentage: Percentage of timeout to trigger early termination (default 70%)

        Returns:
            bool: True if we've exceeded the threshold percentage of total timeout
        """
        if not state.get("execution_start_time") or not state.get("timeout_seconds"):
            return False

        if threshold_percentage < 0 or threshold_percentage > 1:
            logging.error(
                f"Threshold percentage must be between 0 and 1: {threshold_percentage}"
            )
            return False

        elapsed_time = time.time() - state["execution_start_time"]
        timeout_threshold = state["timeout_seconds"] * threshold_percentage

        if elapsed_time > timeout_threshold:
            logging.warning(
                f"⏰ [{self.node_name}] Timeout approaching: {elapsed_time:.1f}s elapsed "
                f"out of {state['timeout_seconds']}s total (threshold: {timeout_threshold:.1f}s)"
            )
            CloudWatchMetrics.put_metric(
                "langgraph_timeout_threshold_reached",
                1,
                [
                    {"Name": "Node", "Value": self.node_name},
                    {"Name": "ElapsedTime", "Value": str(int(elapsed_time))},
                ],
            )
            return True

        return False

    @abstractmethod
    def process(self, state: ChatState) -> ChatState:
        """Process the state and return updated state."""
        logging.error(f"❌ [{self.node_name}] Process method must be implemented")
        return state

    def preprocess(self, state: ChatState) -> ChatState:
        """Preprocess the state and return updated state."""
        return state

    def postprocess(self, state: ChatState) -> ChatState:
        """Postprocess the state and return updated state."""
        return state

    def __call__(self, state: ChatState) -> ChatState:
        """Make node instances callable for LangGraph."""
        try:
            start_time = time.time()
            state = self.preprocess(state)
            logging.info(f"▶️ [{self.node_name}] Processing state")
            state = self.process(state)
            logging.info(f"✅ [{self.node_name}] Processing complete")
            state = self.postprocess(state)
            end_time = time.time()
            CloudWatchMetrics.put_metric(
                "langgraph_node_processing_time",
                end_time - start_time,
                [
                    {"Name": "Node", "Value": self.node_name},
                ],
            )
            return state
        except Exception as e:
            logging.exception(f"❌ [{self.node_name}] Error: {e}")
            CloudWatchMetrics.put_metric(
                "langgraph_node_error",
                1,
                [
                    {"Name": "Node", "Value": self.node_name},
                ],
            )
            raise
