import logging
import time
from typing import Any, Dict

from langchain_core.messages import HumanMessage

from ....prompt.prompt_library.playground_deep_research_prompt import REFLECTION_PROMPT
from ....utils import CloudWatchMetrics
from ...chatbot_status import ChatbotStepStatusUpdator, PlaygroundChatbotStep
from ...langgraph.models import ChatState
from ...langgraph.nodes.base_node import BaseNode


class ReflectionNode(BaseNode):
    """Node for reflecting on current information and determining if more research is needed."""

    MAX_TOOL_ROUNDS = 3  # New constant

    def __init__(
        self,
        status_updator: ChatbotStepStatusUpdator,
        model_name: str = "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
    ):
        super().__init__(model_name, status_updator)

    @property
    def node_name(self) -> str:
        """Get the node name for this instance."""
        return "reflection"

    def preprocess(self, state: ChatState) -> ChatState:
        # mark all running steps as finished
        self.status_updator.mark_all_running_steps_as_finished()
        return state

    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        logging.info(f"🤔 [{self.node_name}] Starting reflection")

        # Get current tool round or initialize to 0
        current_round = state.get("tool_round", 0)

        # If we've exceeded max rounds, skip further tool execution
        if current_round >= self.MAX_TOOL_ROUNDS:
            self.add_to_state(state, "needs_more_info", False)
            self.add_to_state(
                state, "reflection_output", "Maximum research rounds reached"
            )
            # Don't increment tool_round when max rounds reached
            CloudWatchMetrics.put_metric(
                "langgraph_reflection_max_rounds_reached",
                1,
                [
                    {"Name": "Node", "Value": self.node_name},
                ],
            )
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.REFLECTION,
                "Maximum research rounds reached. Returning best results so far.",
                step_finished=True,
            )
            return state

        # Check if we're approaching timeout (70% of total time elapsed)
        if self.is_timeout_approaching(state):
            logging.warning(
                "🚨 Timeout threshold reached (70%), skipping further research."
            )
            self.add_to_state(state, "needs_more_info", False)
            self.add_to_state(
                state,
                "reflection_output",
                "Timeout threshold reached. Returning best results so far.",
            )
            CloudWatchMetrics.put_metric(
                "langgraph_reflection_skipped_timeout",
                1,
                [
                    {"Name": "Node", "Value": self.node_name},
                ],
            )
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.REFLECTION,
                "Time limit approaching. Returning best results so far.",
                step_finished=True,
            )
            return state

        # If we don't have too much remaining steps, we can skip reflection
        remaining_steps = state.get("remaining_steps", 5)
        # We reserve 3 steps for the final answer and brand guidenline rewrite
        if remaining_steps <= 3:
            logging.warning("🚨 Step limit reached, skipping reflection.")
            self.add_to_state(state, "needs_more_info", False)
            self.add_to_state(
                state,
                "reflection_output",
                "Step limit reached. Returning best results so far.",
            )
            CloudWatchMetrics.put_metric(
                "langgraph_reflection_skipped_global_step_limit",
                1,
                [
                    {"Name": "Node", "Value": self.node_name},
                ],
            )
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.REFLECTION,
                "Step limit reached. Returning best results so far.",
                step_finished=True,
            )
            return state

        try:
            # Get required information from state
            user_query = self.get_user_original_query(state)
            synthesis_content = state.get("synthesis_content", "")
            tool_result = state.get("tool_result", "")

            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.REFLECTION,
                f"I have found some information that might be relevant to your question **{user_query}** \n\n Let me reflect on it and determine if more research is needed.",
            )

            # it is totally valid if synthesis_content is empty in multiple rounds
            if not tool_result:
                raise ValueError("Tool result is missing")

            token_budget = self.model_caller.model_config.model_budget
            remaining_token_budget = token_budget - self.get_current_token_count(
                state, include_state=True
            )
            previous_messages_str = self._get_previous_messages_str(
                state, remaining_token_budget * 0.6
            )

            previous_reflections_str = state.get("reflection_output", "")

            # Create reflection prompt
            prompt = [
                HumanMessage(
                    content=REFLECTION_PROMPT.format(
                        user_query=user_query,
                        synthesis_content=synthesis_content,
                        tool_round=current_round,
                        tool_result=tool_result,
                        previous_messages=previous_messages_str,
                        previous_reflections=previous_reflections_str,
                    )
                )
            ]

            # Get reflection from LLM
            reflection_dict = self.model_caller.get_llm_dict_response(prompt)

            # Update state with only required fields
            needs_more_info = reflection_dict.get("needs_more_info", False)
            self.add_to_state(state, "needs_more_info", needs_more_info)
            self.add_to_state(
                state, "reflection_output", reflection_dict.get("reflection_output", "")
            )
            # Only increment tool_round if we need more info
            if needs_more_info:
                self.add_to_state(state, "tool_round", current_round + 1)
            logging.info(f"[{self.node_name}] Needs more info: {needs_more_info}")
            if not needs_more_info:
                CloudWatchMetrics.put_metric(
                    "langgraph_reflection_pass",
                    1,
                    [
                        {"Name": "task_id", "Value": state.get("task_id", "unknown")},
                    ],
                )
                self.status_updator.update_chatbot_step(
                    PlaygroundChatbotStep.REFLECTION,
                    f"### Reflection result: \n\n"
                    f"{reflection_dict.get('reflection_output', '')}\n\n"
                    f"Cool, I think I got all the information I need. Let me generate the final response for you.",
                    step_finished=True,
                )
            else:
                CloudWatchMetrics.put_metric(
                    "langgraph_reflection_fail",
                    1,
                    [
                        {"Name": "task_id", "Value": state.get("task_id", "unknown")},
                    ],
                )
                self.status_updator.update_chatbot_step(
                    PlaygroundChatbotStep.REFLECTION,
                    f"Reflection result: \n\n"
                    f"{reflection_dict.get('reflection_output', '')}\n\n"
                    f"Seems we still need to gather more information. Let me try to gather more information.",
                    step_finished=True,
                )

            time.sleep(2)
        except Exception as e:
            logging.error(f"🤔 [{self.node_name}] Reflection failed: {e}")
            # On error, don't continue tool execution and don't increment tool_round
            self.add_to_state(state, "needs_more_info", False)
            self.add_to_state(
                state, "reflection_output", f"Error during reflection: {str(e)}"
            )
            CloudWatchMetrics.put_metric(
                "langgraph_node_error",
                1,
                [
                    {"Name": "Node", "Value": self.node_name},
                ],
            )

        return state
