import json
import logging
from typing import Any, Optional
from uuid import UUID

from langchain.callbacks.base import BaseCallbackHandler

from ...utils import CloudWatchMetrics
from ..chatbot_status import ChatbotStepStatusUpdator, PlaygroundChatbotStep


class PlaygroundCallbackHandler(BaseCallbackHandler):

    def __init__(self, status_updator: ChatbotStepStatusUpdator):
        self.status_updator = status_updator
        self.run_id = None
        self.current_tool_names = None

    def on_chain_start(
        self,
        serialized: dict[str, Any],
        inputs: dict[str, Any],
        name: str,
        run_id: UUID,
        **kwargs: Any,
    ) -> Any:
        if name == "tools":
            messages = inputs.get("messages")
            if not messages or not isinstance(messages, list):
                logging.warning("No messages found or invalid messages format")
                CloudWatchMetrics.put_metric(
                    "tool_chain_no_messages",
                    1,
                    {"name": name, "run_id": str(run_id)},
                )
                return

            # Find the latest AI message by iterating backwards
            latest_ai_message = None
            for message in reversed(messages):
                if hasattr(message, "type") and message.type == "ai":
                    latest_ai_message = message
                    break

            if not latest_ai_message:
                logging.warning("No AI message found in the conversation")
                CloudWatchMetrics.put_metric(
                    "tool_chain_no_ai_message",
                    1,
                    {"name": name, "run_id": str(run_id)},
                )
                return

            if not hasattr(latest_ai_message, "tool_calls"):
                logging.warning("Latest AI message does not have tool_calls attribute")
                CloudWatchMetrics.put_metric(
                    "tool_chain_no_tool_calls",
                    1,
                    {"name": name, "run_id": str(run_id)},
                )
                return

            tool_calls = latest_ai_message.tool_calls
            if not tool_calls:
                logging.warning("No tool calls found in the latest AI message")
                CloudWatchMetrics.put_metric(
                    "tool_chain_no_tool_calls",
                    1,
                    {"name": name, "run_id": str(run_id)},
                )
                return

            tool_call_names = []
            for tool_call in tool_calls:
                if not isinstance(tool_call, dict):
                    logging.warning(f"Invalid tool call format: {tool_call}")
                    CloudWatchMetrics.put_metric(
                        "tool_chain_invalid_tool_call",
                        1,
                        {"name": name, "run_id": str(run_id)},
                    )
                    continue

                tool_name = tool_call.get("name")
                if not tool_name:
                    logging.warning("Tool call missing name")
                    CloudWatchMetrics.put_metric(
                        "tool_chain_tool_call_missing_name",
                        1,
                        {"name": name, "run_id": str(run_id)},
                    )
                    continue
                tool_call_names.append(tool_name)

            if not tool_call_names:
                return

            # enter the tool chain
            self.current_tool_names = tool_call_names
            self.run_id = run_id

            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.THINKING,
                f"To better answer your question, I identified the tools {', '.join(set(tool_call_names))} is helpful for me to use.",
                step_finished=True,
            )

    def on_chain_end(
        self,
        outputs: dict[str, Any],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        **kwargs: Any,
    ) -> None:
        if self.run_id == run_id:
            # exit the tool chain
            logging.info("Chain end")
            # Extract messages from outputs
            messages = outputs.get("messages", [])
            if not messages:
                logging.warning("No messages found in chain outputs")
                return

            # Get the last message which should be the tool message
            last_message = messages[-1]
            if not hasattr(last_message, "content"):
                logging.warning("Last message does not have content attribute")
                return

            try:
                # Parse the content which is a JSON string
                content = json.loads(last_message.content)
                tool_name = content.get("tool_name", "")
                tool_status = content.get("tool_status", "")

                if tool_status == "success":
                    self.status_updator.update_chatbot_step(
                        PlaygroundChatbotStep.THINKING,
                        f"I've successfully used the {tool_name} tool to gather information. Let me process this and check if we need to use other tools.\n\n",
                    )
                else:
                    self.status_updator.update_chatbot_step(
                        PlaygroundChatbotStep.THINKING,
                        f"I encountered an issue while using the {tool_name} tool. Let me try a different approach.",
                    )

            except json.JSONDecodeError:
                logging.error("Failed to parse tool message content as JSON")
                self.status_updator.update_chatbot_step(
                    PlaygroundChatbotStep.THINKING,
                    "I encountered an issue processing the tool response. Let me try a different approach.",
                )
