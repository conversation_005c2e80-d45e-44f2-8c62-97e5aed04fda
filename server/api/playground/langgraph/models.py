from enum import Enum
from typing import Any, Dict, List, TypedDict

from langgraph.prebuilt.chat_agent_executor import AgentState


class UserIntent(str, Enum):
    MARKETING_QUESTION = "marketing_question"
    CONTENT_GENERATION = "content_generation"
    NON_MARKETING_QUESTION = "non_marketing_question"


class ToolStatus(str, Enum):
    SUCCESS = "success"
    ERROR = "error"
    SKIPPED = "skipped"


class ToolResult(TypedDict):
    """Represents the standardized dictionary output of a tool."""

    output_text: str
    tool_name: str
    tool_input: Any  # Input could be various types depending on the tool
    metadata: Dict[str, Any]
    tool_status: ToolStatus


class ChatState(AgentState):
    """Type definition for the graph state."""

    task_id: str  # the thread id for conversation, passing from
    playbook_id: str  # the playbook id for conversation, passing from
    intent: UserIntent  # result from intent identification node
    synthesis_content: str  # the synthesis content from all tools, aggregation
    reflection_output: str  # reflection node result
    final_response: str  # the final results
    current_token_count: int  # the current token count of the conversation
    raw_tool_results: List[ToolResult]
    tool_result: str
    needs_more_info: bool  # whether the agent needs more information
    tool_round: int  # the current tool round
    # some hardcoded parameters, make it backward compatible to the current chatbot
    assets: Dict[str, List[str]]
    targets: Dict[str, List[str]]
    # Time tracking for early termination
    execution_start_time: float  # timestamp when execution started
    timeout_seconds: int  # total timeout in seconds

    @staticmethod
    def to_serializable_dict(state: Dict):
        """Create a JSON-serializable version of the state"""
        serialized = dict(state)
        # Convert messages to use their content
        if "messages" in serialized:
            serialized["messages"] = [
                {
                    "content": msg.content,
                    "type": msg.type if hasattr(msg, "type") else "unknown",
                }
                for msg in serialized["messages"]
            ]
        return serialized
