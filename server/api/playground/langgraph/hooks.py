import logging

from langchain_core.messages.utils import count_tokens_approximately, trim_messages

from ...utils import CloudWatchMetrics, get_token_count
from .nodes.base_node import BaseNode


def create_pre_model_hook_function(model_budget: int):
    return lambda state: pre_model_hook_with_model_budget(state, model_budget)


def pre_model_hook_with_model_budget(state, model_budget: int):
    """
    This hook is called before the model is called.
    It manages token budget and adjusts content to fit within limits.
    Following LangGraph's best practices for message history management.
    """

    logging.info(f"Pre model hook called")
    current_token_count = BaseNode.get_current_token_count(state, include_state=True)

    to_update = {}

    if current_token_count < model_budget:
        return to_update

    logging.info(
        f"Current token count: {current_token_count}, token budget: {model_budget}, We need to trim the state"
    )
    try:
        # Reserve 20% token for completion
        prompt_token_budget = int(model_budget * 0.8)

        # Get content components
        tool_results = state.get("tool_results", "")
        reflection_output = state.get("reflection_output", "")
        messages = state.get("messages", [])

        # Calculate token usage for each component
        tool_results_tokens = get_token_count(tool_results)
        reflection_tokens = get_token_count(reflection_output)
        messages_tokens = count_tokens_approximately(messages)

        # Adjust content to fit within token budget
        # 1. Try dropping tool_results if needed
        if tool_results_tokens > 0 and current_token_count > prompt_token_budget:
            to_update["tool_results"] = ""
            current_token_count = current_token_count - tool_results_tokens
            tool_results_tokens = 0
            CloudWatchMetrics.put_metric(
                "tool_results_trimmed",
                1,
                [],
            )

        # 2. Try reducing previous messages if still over budget
        if messages_tokens > 0 and current_token_count > prompt_token_budget:
            # Keep only the most recent messages that fit within the remaining budget
            remaining_budget = (
                prompt_token_budget - tool_results_tokens - reflection_tokens
            )
            if remaining_budget > 0:
                trimmed_messages = trim_messages(
                    messages,
                    strategy="last",
                    token_counter=count_tokens_approximately,
                    max_tokens=remaining_budget,
                    start_on=("human", "system"),
                    end_on=("tool", "ai", "human"),
                    include_system=True,
                )
                if len(trimmed_messages) < 3:
                    # we force keep the last 2 messages if the trimmed_messages is less than 3
                    trimmed_messages = messages[-2:]
            else:
                trimmed_messages = messages[-2:]
            to_update["llm_input_messages"] = trimmed_messages
            current_token_count = (
                current_token_count
                - messages_tokens
                + count_tokens_approximately(trimmed_messages)
            )
            CloudWatchMetrics.put_metric("messages_trimmed", 1, [])

        # 3. Try dropping reflection_output if still over budget
        if reflection_tokens > 0 and current_token_count > prompt_token_budget:
            to_update["reflection_output"] = ""
            current_token_count = current_token_count - reflection_tokens
            CloudWatchMetrics.put_metric(
                "reflection_output_trimmed",
                1,
                [],
            )

        # Final token count check
        if current_token_count > prompt_token_budget:
            CloudWatchMetrics.put_metric(
                "pre_model_hook_error_exceed",
                1,
                [],
            )
            raise ValueError(
                "The chat message is too long and has reached the limit. Please start a new thread."
            )

        return to_update
    except ValueError as e:
        raise e
    except Exception as e:
        logging.exception(f"Error in pre model hook: {e}, by passing")
        CloudWatchMetrics.put_metric("pre_model_hook_error", 1, [])
        return {}
