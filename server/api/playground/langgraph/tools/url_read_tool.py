import json
import logging
from typing import List

from ....playground.chatbot_status import Playground<PERSON>hatbotStep
from ....playground.langgraph.models import ToolResult, ToolStatus
from ....playground.playground_utils import crawl_url
from .base_custom_tool import BaseCustomTool


class UrlReadTool(BaseCustomTool):
    name: str = "url_read"
    description: str = (
        """When there is urls in the message, use this tool to read the content of the urls.
        Please note that only consider the urls in the original user message, not the urls in the tool result.
        """
    )

    def _run(self, list_of_urls: List[str]) -> str:
        logging.info(f"🔍 [{self.name}] Reading URLs: {list_of_urls}")

        self.status_updator.update_chatbot_step(
            PlaygroundChatbotStep.READING_URL,
            f"We are reading the URLs you provided.",
        )

        url_contents = {}
        for url in list_of_urls:
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.READING_URL,
                f"Reading the URL: {url}",
            )
            url_contents[url] = crawl_url(url)
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.READING_URL,
                f"We have read the URL: {url}",
            )
        if not url_contents:
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.READING_URL,
                f"We have read all the URLs you provided and understood the content.",
                step_finished=True,
            )
            return json.dumps(
                ToolResult(
                    output_text="",
                    tool_name=self.name,
                    tool_input=list_of_urls,
                    metadata={},
                    tool_status=ToolStatus.ERROR,
                )
            )

        url_contents_str = ""
        for url, content in url_contents.items():
            url_contents_str += f'<urlContent url="{url}">\n{content}\n</urlContent>'

        self.status_updator.update_chatbot_step(
            PlaygroundChatbotStep.READING_URL,
            f"We have read all the URLs you provided and understood the content.",
            step_finished=True,
        )

        return json.dumps(
            ToolResult(
                output_text=url_contents_str,
                tool_name=self.name,
                tool_input=list_of_urls,
                metadata={},
                tool_status=ToolStatus.SUCCESS,
            )
        )
