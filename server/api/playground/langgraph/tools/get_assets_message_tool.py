import json
import logging
from typing import Dict, List

from ....feature.data_wrapper.data_wrapper import GenerateEnv
from ....feature.feature_builder.asset_context_feature_builder import (
    AssetContextFeatureBuilder,
)
from ....playground.chatbot_status import (
    ChatbotStepStatusUpdator,
    PlaygroundChatbotStep,
    PlaygroundChatbotSubStep,
)
from ....playground.langgraph.models import ToolResult, ToolStatus
from .base_custom_tool import BaseCustomTool


class GetAssetsMessageTool(BaseCustomTool):
    name: str = "get_assets_message"
    description: str = """Get the assets message for the task.
    Please use it when you see exact asset information in the system context.
    The assets are usually in the form of a dictionary with the key as the asset list name and the value as the asset name.
    For example:
    {
        "assets": ["asset1", "asset2"]
    }
    """
    gen_env: GenerateEnv
    asset_budget: int
    status_updator: ChatbotStepStatusUpdator

    def __init__(
        self,
        gen_env: GenerateEnv,
        asset_budget: int,
        playbook_id: int,
        task_id: str,
        status_updator: ChatbotStepStatusUpdator,
    ):
        super().__init__(
            gen_env=gen_env,
            asset_budget=asset_budget,
            playbook_id=playbook_id,
            task_id=task_id,
            status_updator=status_updator,
        )

    def _run(self, assets: Dict[str, List[str]]) -> str:
        """
        Get the assets message for the task.

        Args:
            assets: Dictionary of assets.
        """
        try:
            logging.info(
                f"🔍 [{self.name}] Getting assets message for task {self.task_id}."
            )

            # Validate assets parameter
            if not assets:
                logging.warning(f"[{self.name}] Empty assets dictionary provided")
                return json.dumps(
                    ToolResult(
                        output_text="",
                        tool_name=self.name,
                        tool_input=assets,
                        metadata={},
                        tool_status=ToolStatus.ERROR,
                    )
                )

            for key, value in assets.items():
                if not isinstance(key, str) or not isinstance(value, list):
                    logging.warning(
                        f"[{self.name}] Invalid asset format: {key}:{value}"
                    )
                    return json.dumps(
                        ToolResult(
                            output_text="",
                            tool_name=self.name,
                            tool_input=assets,
                            metadata={},
                            tool_status=ToolStatus.ERROR,
                        )
                    )

            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.ASSET_READ,
                f"We are getting your assets information from the playbook.",
            )
            if not isinstance(self.asset_budget, int):
                raise Exception("Asset budget is not an integer")
            asset_context_builder = AssetContextFeatureBuilder(gen_env=self.gen_env)
            asset_context_builder.set_budgets({"asset_context": self.asset_budget})
            asset_info = ""
            asset_tuple_set = set()
            for key, value in assets.items():
                asset_list_name = key
                for asset_name in value:
                    asset_tuple_set.add((asset_list_name, asset_name))

            for asset_list_name, asset_name in asset_tuple_set:
                try:
                    asset_context = asset_context_builder.get_asset_context(
                        asset_list_name,
                        asset_name,
                        max_token_size=self.asset_budget,
                    )
                except Exception as e:
                    if "does not exist" in str(e):
                        logging.warning(
                            f"[{self.name}] Error occurred while getting asset context: {str(e)}"
                        )
                        asset_context = None
                    else:
                        raise e
                if asset_context:
                    asset_info += asset_context
            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.ASSET_READ,
                f"We have read all the assets you provided and understood the content: \n{asset_info}",
                is_finished=True,
            )
            return json.dumps(
                ToolResult(
                    output_text=asset_info,
                    tool_name=self.name,
                    tool_input=assets,
                    metadata={},
                    tool_status=ToolStatus.SUCCESS,
                )
            )
        except Exception as e:
            logging.error(
                f"[{self.name}] Error occurred while getting assets message: {str(e)}"
            )
            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.ASSET_READ,
                "We have encountered an error while getting your assets information",
                is_finished=True,
            )
            return json.dumps(
                ToolResult(
                    output_text=f"",
                    tool_name=self.name,
                    tool_input=assets,
                    metadata={},
                    tool_status=ToolStatus.ERROR,
                )
            )
