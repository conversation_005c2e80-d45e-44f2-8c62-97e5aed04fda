import json
import logging
import time
from typing import List

from ....playground.chatbot_status import (
    PlaygroundChatbotStep,
    PlaygroundChatbotSubStep,
)
from ....playground.langgraph.models import ToolResult, ToolStatus
from ....search.tofu_searcher import <PERSON><PERSON><PERSON><PERSON>ofuSearcher, SearchObjectType
from ....utils import CloudWatchMetrics
from .base_custom_tool import BaseCustomTool


class PlaybookSearchTool(BaseCustomTool):
    name: str = "playbook_search"
    description: str = """Use playbook search to answer a query based on user's information in the Playbook.
    Please use this tool when you need to know more about the user's target audience, assets, company context
    if it is not provided in the system context.
    You can use the tool multiple times to get more detailed results.
    You can specify the object_types to search in.
    For object_types, you can use the following values:
    - targets: search in targets
    - assets: search in assets
    If you're not sure about the object_types, you can leave it as an empty list so that the tool will search across all object types.
    """

    def _run(self, query: str, object_types: List[SearchObjectType] = None) -> str:
        """Process method to search the playbook."""
        logging.info(
            f"🔍 [{self.name}] Searching for: {query}, object_types: {object_types}"
        )

        if object_types is None:
            object_types = []

        # Validate tool-specific inputs
        query = query.strip()
        if not query:
            logging.error(f"[{self.name}] Empty query provided")
            tool_result = ToolResult(
                output_text="",
                tool_name=self.name,
                tool_input=query,
                metadata={},
                tool_status=ToolStatus.ERROR,
            )
            return json.dumps(tool_result)

        self.status_updator.update_substep(
            PlaygroundChatbotStep.READING_PLAYBOOK,
            PlaygroundChatbotSubStep.PLAYBOOK_SEARCH,
            f"We are searching your playbook for: {query}",
        )

        try:
            # Initialize the searcher with playbook_id from the tool instance
            searcher = PineconeTofuSearcher(playbook_id=str(self.playbook_id))

            # Search across all object types
            results = searcher.search(query=query, top_k=3, object_types=object_types)

            if len(results) > 0:
                self.status_updator.update_chatbot_step(
                    PlaygroundChatbotStep.READING_PLAYBOOK,
                    f"We have found some results in your playbook:",
                )

            # Format the results into a readable string
            output_text = "Playbook Search Results:\n\n"
            for i, result in enumerate(results, 1):
                output_text += f"Result {i}:\n"
                output_text += f"Content: {result['content']}\n"
                output_text += f"Metadata: {result['metadata']}\n\n"

                column_id = result["metadata"].get("column_id", "")
                title = result["metadata"].get("title", "")
                source = result["metadata"].get("source", "")
                description = result["metadata"].get("description", "")
                if column_id and title:
                    self.status_updator.update_substep(
                        PlaygroundChatbotStep.READING_PLAYBOOK,
                        PlaygroundChatbotSubStep.PLAYBOOK_SEARCH,
                        f"Analyzing the playbook search result data\n"
                        f"{column_id} - {title} \n"
                        f"Source: {source} \n"
                        f"Description: {description} \n",
                    )
                time.sleep(2)
            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.PLAYBOOK_SEARCH,
                f"We have found all the playbook search results",
                is_finished=True,
            )
            tool_result = ToolResult(
                output_text=output_text,
                tool_name=self.name,
                tool_input=query,
                metadata={
                    "results_count": len(results),
                    "raw_results": results,
                    "playbook_id": self.playbook_id,
                },
                tool_status=ToolStatus.SUCCESS,
            )
            CloudWatchMetrics.put_metric(
                "playbook_search_tool_success",
                1,
                [
                    {"Name": "tool_name", "Value": self.name},
                ],
            )
            return json.dumps(tool_result)

        except Exception as e:
            logging.exception(f"❌ [{self.name}] Error: {e}")
            tool_result = ToolResult(
                output_text=f"Error: {e}",
                tool_name=self.name,
                tool_input=query,
                metadata={},
                tool_status=ToolStatus.ERROR,
            )
            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.PLAYBOOK_SEARCH,
                "We have encountered an error while searching your playbook",
                is_finished=True,
            )
            CloudWatchMetrics.put_metric(
                "playbook_search_tool_error",
                1,
                [
                    {"Name": "tool_name", "Value": self.name},
                ],
            )
            return json.dumps(tool_result)
