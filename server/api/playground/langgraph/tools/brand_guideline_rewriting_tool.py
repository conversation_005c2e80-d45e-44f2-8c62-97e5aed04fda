import json
import logging

from ....content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from ....feature.data_wrapper.data_wrapper import GenerateEnv
from ....model_caller import ModelCaller
from ....playground.chatbot_status import PlaygroundChatbotStep
from ....playground.langgraph.models import ToolResult, ToolStatus
from ....playground.playground_feature_builder import PlaygroundFeatureBuilder
from ....utils import CloudWatchMetrics
from ...chatbot_status import ChatbotStepStatusUpdator
from .base_custom_tool import BaseCustomTool


class BrandGuidelineRewritingTool(BaseCustomTool):
    name: str = "brand_guideline_rewriting"
    description: str = """Rewrites content according to brand guidelines while maintaining the original message.
    Useful for ensuring content consistency with brand voice and style. 
    Please only use it if you feel the content is inconsistent with the brand guidelines provided in the system context.
    """
    model_caller: ModelCaller = None
    content_processor: DefaultContentGenPostprocessor = None
    feature_builder: PlaygroundFeatureBuilder = None

    def __init__(
        self,
        model_caller: ModelCaller,
        gen_env: GenerateEnv,
        playbook_id: int,
        task_id: str,
        status_updator: ChatbotStepStatusUpdator,
    ):
        # Create content processor first
        content_processor = DefaultContentGenPostprocessor(
            gen_env=gen_env, model_caller=model_caller
        )
        feature_builder = PlaygroundFeatureBuilder(gen_env)

        # Pass all required fields to parent class
        super().__init__(
            model_caller=model_caller,
            content_processor=content_processor,
            feature_builder=feature_builder,
            playbook_id=playbook_id,
            task_id=task_id,
            status_updator=status_updator,
        )

    def _run(self, response: str) -> str:
        """
        Rewrites content according to brand guidelines.

        Args:
            response (str): The text to rewrite
        """

        try:
            logging.info(
                f"🔍 [{self.name}] Rewriting content according to brand guidelines."
            )

            # Validate tool-specific inputs
            # This is separate from the base validation
            if not response:
                logging.error(f"[{self.name}] Empty response")
                return json.dumps(
                    ToolResult(
                        output_text="",
                        tool_name=self.name,
                        tool_input={
                            "original_response": response,
                        },
                        metadata={},
                        tool_status=ToolStatus.ERROR,
                    )
                )

            # checking if there is brand guideline for this playbook, this is to prevent hallucination from LLM
            brand_guidelines = self.feature_builder.get_brand_guidelines()
            if not brand_guidelines:
                logging.warning(
                    f"[{self.name}] No brand guidelines found for playbook {self.playbook_id}"
                )
                CloudWatchMetrics.put_metric(
                    "brand_guideline_rewriting_tool_hallucination",
                    1,
                    [
                        {"Name": "tool_name", "Value": self.name},
                    ],
                )
                return json.dumps(
                    ToolResult(
                        output_text="",
                        tool_name=self.name,
                        tool_input={
                            "original_response": response,
                            "brand_guidelines": brand_guidelines,
                        },
                        metadata={},
                        tool_status=ToolStatus.ERROR,
                    )
                )
            # update the status

            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.RESPONSE_GENERATION,
                "We have generated a response for you. \n\n" f"{response} \n\n",
            )

            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.REWRITING_CONTENT,
                f"We are rewriting the content according to the brand guidelines:\n"
                f"{brand_guidelines} \n",
            )
            rewritten_content = self.content_processor.rewrite_with_brand_guideline(
                [response], brand_guidelines
            )[0]
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.REWRITING_CONTENT,
                f"We have rewritten the content according to the brand guidelines:\n"
                f"Brand guidelines: \n"
                f"{brand_guidelines} \n"
                f"Rewritten content: \n"
                f"{rewritten_content} \n"
                f"Original content: \n"
                f"{response}",
                step_finished=True,
            )

            tool_result = ToolResult(
                output_text=rewritten_content,
                tool_name=self.name,
                tool_input={
                    "original_response": response,
                    "brand_guidelines": brand_guidelines,
                },
                metadata={
                    "original_response": response,
                    "brand_guidelines": brand_guidelines,
                    "playbook_id": self.playbook_id,
                },
                tool_status=ToolStatus.SUCCESS,
            )
            CloudWatchMetrics.put_metric(
                "brand_guideline_rewriting_tool_success",
                1,
                [
                    {"Name": "tool_name", "Value": self.name},
                ],
            )
            return json.dumps(tool_result)

        except Exception as e:
            logging.exception(f"❌ [{self.name}] Error: {e}")
            tool_result = ToolResult(
                output_text=f"Error: {e}",
                tool_name=self.name,
                tool_input={
                    "original_response": response,
                    "brand_guidelines": brand_guidelines,
                },
                metadata={},
                tool_status=ToolStatus.ERROR,
            )
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.REWRITING_CONTENT, ""
            )
            CloudWatchMetrics.put_metric(
                "brand_guideline_rewriting_tool_error",
                1,
                [
                    {"Name": "tool_name", "Value": self.name},
                ],
            )
            return json.dumps(tool_result)
