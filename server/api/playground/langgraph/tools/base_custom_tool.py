import logging
from abc import ABC

from langchain_core.tools import BaseTool

from ...chatbot_status import ChatbotStepStatusUpdator


class BaseCustomTool(BaseTool, ABC):
    """
    Base class for custom tools that require playbook_id and task_id.
    All tools that need these common fields should inherit from this class.
    """

    playbook_id: int
    task_id: str
    status_updator: ChatbotStepStatusUpdator

    def validate_inputs(self, **kwargs) -> bool:
        """
        Validate that required inputs are present.

        Returns:
            bool: True if all required inputs are present, False otherwise
        """
        if not self.playbook_id:
            logging.error(f"[{self.name}] Empty playbook ID provided")
            return False

        if not self.task_id:
            logging.error(f"[{self.name}] Empty task ID provided")
            return False

        return True
