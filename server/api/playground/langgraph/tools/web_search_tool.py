import json
import logging
import time
from typing import Dict, List

from openai import OpenAI
from openai.types.responses.response import Response

from ....playground.chatbot_status import PlaygroundChatbotStep
from ....playground.langgraph.models import ToolResult, ToolStatus
from ....utils import CloudWatchMetrics
from .base_custom_tool import BaseCustomTool


class WebSearchTool(BaseCustomTool):
    name: str = "web_search"
    description: str = """This tool performs a web search to gather information relevant to a given query.
    Use this tool specifically to collect raw search results, snippets, and links based on the user's query.
    Do **not** attempt to synthesize an answer or respond directly to the user's query using this tool.
    Focus on formulating an effective search query to retrieve the most relevant information available on the web.
    The gathered information will be used by a separate component to formulate the final answer.
    Please adjust the query to fit the above requirements.
    You can use the tool multiple times if the initial search does not yield sufficient information.
    """

    def _run(self, query: str) -> str:
        logging.info(f"🔍 [{self.name}] Searching for: {query}")
        # Validate input parameters
        if not query:
            logging.error(f"[{self.name}] Empty query provided")
            return json.dumps(
                ToolResult(
                    output_text="",
                    tool_name=self.name,
                    tool_input=query,
                    metadata={},
                    tool_status=ToolStatus.ERROR,
                )
            )

        self.status_updator.update_chatbot_step(
            PlaygroundChatbotStep.WEB_SEARCH,
            f"We are searching the web for: **{query}**",
        )
        try:
            client = OpenAI()
            response = client.responses.create(
                model="gpt-4o", tools=[{"type": "web_search_preview"}], input=query
            )

            output_text = response.output_text
            citations = self._extract_citations(response)

            for citation in citations:
                self.status_updator.update_chatbot_step(
                    PlaygroundChatbotStep.WEB_SEARCH,
                    f"Reading information from the URL: [{citation['title']}]({citation['url']})",
                )
                time.sleep(2)

            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.WEB_SEARCH,
                f"We have read all the information from the URLs you provided and understood the content:\n\n"
                f"{output_text}",
                step_finished=True,
            )

            tool_result = ToolResult(
                output_text=output_text,
                tool_name=self.name,
                tool_input=query,
                metadata={
                    "citations": citations,
                    "response_id": response.id,
                    "model": response.model,
                },
                tool_status=ToolStatus.SUCCESS,
            )
            CloudWatchMetrics.put_metric(
                "web_search_tool_success",
                1,
                [
                    {"Name": "tool_name", "Value": self.name},
                ],
            )
            return json.dumps(tool_result)

        except Exception as e:
            logging.exception(f"❌ [{self.name}] Error: {e}")
            tool_result = ToolResult(
                output_text=f"Error: {e}",
                tool_name=self.name,
                tool_input=query,
                metadata={},
                tool_status=ToolStatus.ERROR,
            )
            CloudWatchMetrics.put_metric(
                "web_search_tool_error",
                1,
                [
                    {"Name": "tool_name", "Value": self.name},
                ],
            )
            self.status_updator.update_chatbot_step(
                PlaygroundChatbotStep.WEB_SEARCH,
                f"We have encountered an error while searching the web for: **{query}**",
                step_finished=True,
            )
            return json.dumps(tool_result)

    @staticmethod
    def _extract_citations(response: Response) -> List[Dict[str, str | None]]:
        """Extracts URL citations from the OpenAI response object."""
        citations: List[Dict[str, str | None]] = []
        # There are multiple citations in the response, we need to deduplicate them
        seeing_urls = set()
        for output_item in response.output:
            if output_item.type == "message":
                for content_item in output_item.content:
                    if content_item.type == "output_text":
                        if (
                            hasattr(content_item, "annotations")
                            and content_item.annotations
                        ):
                            for annotation in content_item.annotations:
                                if annotation.type == "url_citation":
                                    url = getattr(annotation, "url", None)
                                    if url not in seeing_urls:
                                        seeing_urls.add(url)
                                        citations.append(
                                            {
                                                "url": url,
                                                "title": getattr(
                                                    annotation, "title", None
                                                ),
                                            }
                                        )
                        break
                break
        return citations
