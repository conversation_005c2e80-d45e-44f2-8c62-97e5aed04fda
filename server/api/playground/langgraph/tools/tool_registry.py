import logging
from typing import Dict, List

from langchain_core.tools import BaseTool

from ....feature.data_wrapper.data_wrapper import GenerateEnv
from ....utils import CloudWatchMetrics
from ...chatbot_status import ChatbotStepStatusUpdator
from ...playground_chat_generator import GenerateChatRequest
from ...playground_feature_builder import PlaygroundFeatureBuilder
from ..nodes.base_node import BaseNode
from .brand_guideline_rewriting_tool import BrandGuidelineRewritingTool
from .get_assets_message_tool import GetAssetsMessageTool
from .get_targets_message_tool import GetTargetsMessageTool
from .playbook_search_tool import PlaybookSearchTool
from .url_read_tool import UrlReadTool
from .web_search_tool import WebSearchTool


def create_tools(
    gen_env: GenerateEnv,
    asset_budget: int,
    target_budget: int,
    playbook_id: int,
    task_id: str,
    chat_request: GenerateChatRequest,
    status_updator: ChatbotStepStatusUpdator,
    exclude_tools: List[str] = None,
) -> Dict[str, BaseTool]:
    """
    Create and configure tool instances with the provided parameters.

    Args:
        gen_env: Generation environment
        asset_budget: Budget for asset retrieval
        target_budget: Budget for target retrieval
        exclude_tools: Don't create these tools
        playbook_id: Playbook ID to pass to tools that need it
        task_id: Task ID to pass to tools that need it
        chat_request: Chat request
    Returns:
        Dict[str, BaseTool]: Dictionary of configured tool instances
    """
    available_tools = [
        WebSearchTool(
            playbook_id=playbook_id, task_id=task_id, status_updator=status_updator
        ),
        PlaybookSearchTool(
            playbook_id=playbook_id, task_id=task_id, status_updator=status_updator
        ),
        UrlReadTool(
            playbook_id=playbook_id, task_id=task_id, status_updator=status_updator
        ),
    ]

    if chat_request.assets:
        available_tools.append(
            GetAssetsMessageTool(
                gen_env,
                asset_budget,
                playbook_id=playbook_id,
                task_id=task_id,
                status_updator=status_updator,
            )
        )

    if chat_request.targets:
        available_tools.append(
            GetTargetsMessageTool(
                gen_env,
                target_budget,
                playbook_id=playbook_id,
                task_id=task_id,
                status_updator=status_updator,
            )
        )

    available_tools_defs = {tool.name: tool for tool in available_tools}

    tools = {}
    for name, tool_def in available_tools_defs.items():
        if exclude_tools and name in exclude_tools:
            continue

        # Handle different ways tools can be defined (functions vs class instances)
        if isinstance(tool_def, BaseTool):
            tools[name] = tool_def  # Already an instance
        elif callable(tool_def):
            # Handle @tool decorated functions (like web_search)
            # This assumes they don't need gen_env or target_budget directly
            # If they do, you'll need to adjust their definition or how they're called
            try:
                # Check if it's a @tool function by looking for 'name' attribute
                if hasattr(tool_def, "name"):
                    tools[name] = tool_def
                else:
                    # Fallback for other callables, though likely unnecessary now
                    tools[name] = tool_def()
            except (
                TypeError
            ):  # Handle cases where calling tool() might fail if it's not a simple function
                tools[name] = tool_def  # Use as is if calling fails
    CloudWatchMetrics.put_metric(
        "create_graph_tools_added",
        len(tools),
        [{"Name": "playbook_id", "Value": str(playbook_id)}],
    )

    return tools


def create_final_response_tools(
    model_name: str,
    gen_env: GenerateEnv,
    chat_request: GenerateChatRequest,
    playbook_id: int,
    task_id: str,
    status_updator: ChatbotStepStatusUpdator,
):
    """
    Create tools for final response processing.
    We only add brand guideline rewriting tool if the brand guidelines are provided in the chat request and the brand guidelines are not empty.

    Args:
        model_name: Name of the model to use
        gen_env: Generation environment
        chat_request: Chat request
        playbook_id: Playbook ID to pass to tools that need it
        task_id: Task ID to pass to tools that need it

    Returns:
        Dict[str, BaseTool]: Dictionary of configured tool instances
    """
    model_caller = BaseNode.get_model_caller_from_model_name(model_name)
    tools = {}
    if chat_request.use_brand_guidelines:
        feature_builder = PlaygroundFeatureBuilder(gen_env)
        brand_guidelines = feature_builder.get_brand_guidelines()
        if brand_guidelines:
            logging.info(f"[{task_id}] Using brand guidelines: {brand_guidelines}")
            brand_guideline_rewriting_tool = BrandGuidelineRewritingTool(
                model_caller,
                gen_env,
                playbook_id=playbook_id,
                task_id=task_id,
                status_updator=status_updator,
            )
            tools = {"brand_guideline_rewriting": brand_guideline_rewriting_tool}
            CloudWatchMetrics.put_metric(
                "create_graph_brand_guidelines_tool_added",
                1,
                [{"Name": "playbook_id", "Value": str(playbook_id)}],
            )
        else:
            logging.info(
                f"[{task_id}] No brand guidelines found for playbook {playbook_id}"
            )
            CloudWatchMetrics.put_metric(
                "create_graph_no_brand_guidelines_tool_added",
                1,
                [{"Name": "playbook_id", "Value": str(playbook_id)}],
            )
    return tools
