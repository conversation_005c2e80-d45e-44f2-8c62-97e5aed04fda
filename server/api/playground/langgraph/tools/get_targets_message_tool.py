import json
import logging
from typing import Dict, List

from ....feature.data_wrapper.data_wrapper import GenerateEnv
from ....feature.feature_builder.target_context_feature_builder import (
    TargetContextBuilder,
)
from ....playground.chatbot_status import (
    PlaygroundChatbotStep,
    PlaygroundChatbotSubStep,
)
from ....playground.langgraph.models import ToolResult, ToolStatus
from ...chatbot_status import ChatbotStepStatusUpdator
from .base_custom_tool import BaseCustomTool


class GetTargetsMessageTool(BaseCustomTool):
    name: str = "get_targets_message"
    description: str = """Get the targets message for the task.
    Please use it and only use it when you see exact target information in the system context.
    The targets are usually in the form of a dictionary with the key as the target list name and the value as the target name.
    For example:
    {
        "targets": ["target1", "target2"]
    }
    """
    gen_env: GenerateEnv
    target_budget: int
    status_updator: ChatbotStepStatusUpdator

    def __init__(
        self,
        gen_env: GenerateEnv,
        target_budget: int,
        playbook_id: int,
        task_id: str,
        status_updator: ChatbotStepStatusUpdator,
    ):
        super().__init__(
            gen_env=gen_env,
            target_budget=target_budget,
            playbook_id=playbook_id,
            task_id=task_id,
            status_updator=status_updator,
        )

    def _run(self, targets: Dict[str, List[str]]) -> str:
        """
        Get the targets message for the task.

        Args:
            targets: Dictionary of targets.
        """
        try:
            logging.info(
                f"🔍 [{self.name}] Getting targets message for task {self.task_id}."
            )

            # Validate targets parameter
            if not targets:
                logging.warning(f"[{self.name}] Empty targets dictionary provided")
                return json.dumps(
                    ToolResult(
                        output_text="",
                        tool_name=self.name,
                        tool_input=targets,
                        metadata={},
                        tool_status=ToolStatus.ERROR,
                    )
                )

            for key, value in targets.items():
                if not isinstance(key, str) or not isinstance(value, list):
                    logging.warning(
                        f"[{self.name}] Invalid target format: {key}:{value}"
                    )
                    return json.dumps(
                        ToolResult(
                            output_text="",
                            tool_name=self.name,
                            tool_input=targets,
                            metadata={},
                            tool_status=ToolStatus.ERROR,
                        )
                    )

            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.TARGET_READ,
                f"We are getting your targets information from the playbook.",
            )
            if not isinstance(self.target_budget, int):
                raise Exception("Target budget is not an integer")
            target_context_builder = TargetContextBuilder(gen_env=self.gen_env)
            target_context_builder.set_budgets({"target_context": self.target_budget})
            target_info = ""
            target_tuple_set = set()
            for key, value in targets.items():
                target_list_name = key
                for target_name in value:
                    target_tuple_set.add((target_list_name, target_name))

            for target_list_name, target_name in target_tuple_set:
                target_context = target_context_builder.get_single_target_context(
                    target_list_name, target_name, max_token_size=self.target_budget
                )
                target_info += (
                    f'<target name="{target_name}">\n{target_context}\n</target>'
                )

            truncated_target_info = target_info[: self.target_budget]
            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.TARGET_READ,
                f"We have read all the targets you provided and understood the content: \n{truncated_target_info}",
                is_finished=True,
            )
            return json.dumps(
                ToolResult(
                    output_text=target_info,
                    tool_name=self.name,
                    tool_input=targets,
                    metadata={},
                    tool_status=ToolStatus.SUCCESS,
                )
            )
        except Exception as e:
            logging.error(
                f"[{self.name}] Error occurred while getting targets message: {str(e)}"
            )
            self.status_updator.update_substep(
                PlaygroundChatbotStep.READING_PLAYBOOK,
                PlaygroundChatbotSubStep.TARGET_READ,
                "We have encountered an error while getting your targets information",
                is_finished=True,
            )
            return json.dumps(
                ToolResult(
                    output_text=f"",
                    tool_name=self.name,
                    tool_input=targets,
                    metadata={},
                    tool_status=ToolStatus.ERROR,
                )
            )
