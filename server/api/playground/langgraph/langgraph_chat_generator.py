import asyncio
import logging
import time
import traceback
from concurrent.futures import TimeoutError
from datetime import datetime
from typing import Any, Dict, List

from django.core.cache import cache
from langchain.schema import AIMessage, HumanMessage
from langchain_core.messages import SystemMessage
from langchain_core.runnables.config import RunnableConfig

from ...langsmith_integration import dynamic_traceable
from ...prompt.prompt_library.playground import (
    company_context_message,
)
from ...prompt.prompt_library.playground_deep_research_prompt import (
    BRAND_GUIDELINES_PROMPT,
    SYSTEM_ASSETS_PROMPT,
    SYSTEM_CONTEXT_PROMPT,
    SYSTEM_TARGETS_PROMPT,
)
from ...utils import CloudWatchMetrics
from ..chatbot_status import (
    ChatbotStepStatusUpdator,
    PlaygroundChatbotStatus,
    TaskState,
)
from ..playground_chat_generator import BasePlaygroundChatGenerator, GenerateChatRequest
from ..playground_utils import remove_image_messages, serialize_message_history
from .graph_builder import get_or_create_graph
from .models import ChatState


class LangGraphAgentChatGenerator(BasePlaygroundChatGenerator):
    def __init__(
        self, user, chat_history, thread_id, chat_request: GenerateChatRequest
    ):
        super().__init__(user, chat_history, thread_id)
        self.status_updator = ChatbotStepStatusUpdator(task_id=chat_request.task_id)
        self.graph = get_or_create_graph(
            thread_id, chat_request, self.gen_env, self.status_updator
        )
        self.timeout_in_seconds = 480  # 8 minutes
        self.recursion_limit = 20

    @dynamic_traceable(name="agent_chat")
    def generate_chat_response(self, generate_chat_request: GenerateChatRequest):
        start_time = time.time()
        generate_chat_status = "success"
        try:
            CloudWatchMetrics.put_metric(
                "deep_research_chat_start",
                1,
                [{"Name": "task_id", "Value": generate_chat_request.task_id}],
            )
            cache.set(
                generate_chat_request.task_id,
                PlaygroundChatbotStatus(
                    status=TaskState.RUNNING,
                    message=generate_chat_request.new_message.content,
                    current_step=None,
                    step_output=None,
                    steps=[],
                ),
                timeout=3600 * 24,
            )

            generate_chat_request.previous_messages = remove_image_messages(
                generate_chat_request.previous_messages
            )

            messages_before_chat = generate_chat_request.previous_messages + [
                generate_chat_request.new_message
            ]
            self._store_chat_history(messages_before_chat, generate_chat_request)

            # Use the agent to generate a response
            response_content = self._chat(
                generate_chat_request,
                self.gen_env._data_wrapper.playbook_instance.id,
            )

            # Create an AI message with the response
            response_message = AIMessage(content=response_content)

            self.status_updator.update_chatbot_final_result(
                message=response_message.content,
            )

            messages_after_chat = messages_before_chat + [
                response_message,
            ]
            self._store_chat_history(messages_after_chat, generate_chat_request)

            return response_content
        except Exception as e:
            generate_chat_status = "failed"
            logging.error(
                f"agent chat failed: {e} for task {generate_chat_request.task_id}\n{traceback.format_exc()}"
            )
            raise
        finally:
            end_time = time.time()
            duration = end_time - start_time
            CloudWatchMetrics.put_metric(
                "deep_research_chat_duration",
                duration,
                [
                    {"Name": "task_id", "Value": generate_chat_request.task_id},
                    {"Name": "status", "Value": generate_chat_status},
                ],
            )

    def _chat(
        self,
        chat_input: GenerateChatRequest,
        playbook_id: int,
    ) -> str:
        logging.info(f"Chatting with message: {chat_input.new_message.content}")
        state = self._initialize_state(
            chat_input,
            playbook_id,
        )

        try:
            result = asyncio.run(self._run_graph_with_timeout(state))
            logging.info(f"Chat result: {result}")
            return result["final_response"]
        except TimeoutError:
            logging.error(
                f"Graph execution timed out after {self.timeout_in_seconds} seconds"
            )
            CloudWatchMetrics.put_metric(
                "langgraph_agent_chat_timeout",
                1,
                [],
            )
            self.status_updator.update_chatbot_failed(
                f"Chat timed out after {self.timeout_in_seconds} seconds, please try different question"
            )
            raise TimeoutError(
                f"Graph execution timed out after {self.timeout_in_seconds} seconds"
            )
        except Exception as e:
            CloudWatchMetrics.put_metric(
                "langgraph_agent_chat_error",
                1,
                [],
            )
            logging.error(f"Error during graph execution: {str(e)}")
            self.status_updator.update_chatbot_failed(
                f"Error during answering your question, please contact support"
            )
            raise

    async def _run_graph_with_timeout(self, state: Dict[str, Any]) -> Dict[str, Any]:
        try:
            config = RunnableConfig(recursion_limit=self.recursion_limit)
            # Set the timeout for the entire graph execution
            return await asyncio.wait_for(
                asyncio.to_thread(self.graph.invoke, state, config),
                timeout=self.timeout_in_seconds,
            )
        except asyncio.TimeoutError:
            logging.error(
                f"Graph execution asyncio timed out after {self.timeout_in_seconds} seconds"
            )
            raise TimeoutError(
                f"Graph execution timed out after {self.timeout_in_seconds} seconds"
            )

    def _initialize_state(
        self,
        chat_input: GenerateChatRequest,
        playbook_id: int,
    ) -> ChatState:
        # Create a system message with context specifically for tool selection and calling
        system_context = self.get_system_context()
        context_message = ""
        if chat_input.use_company_info:
            context_message += company_context_message.format(**system_context)

        system_message_string = SYSTEM_CONTEXT_PROMPT.format(
            company_name=system_context["company_name"],
            context_message=context_message,
            current_time=datetime.now().isoformat(),
            tool_calling_rules=self._get_tool_calling_rules(chat_input),
        )
        if chat_input.assets:
            system_message_string += SYSTEM_ASSETS_PROMPT.format(
                assets=chat_input.assets
            )
        if chat_input.targets:
            system_message_string += SYSTEM_TARGETS_PROMPT.format(
                targets=chat_input.targets
            )
        if chat_input.use_brand_guidelines:
            system_message_string += BRAND_GUIDELINES_PROMPT.format(
                brand_guidelines=system_context["brand_guidelines"]
            )

        system_message = SystemMessage(content=system_message_string)
        human_message = HumanMessage(content=chat_input.new_message.content)

        return ChatState(
            task_id=chat_input.task_id,
            playbook_id=playbook_id,
            messages=[system_message, *chat_input.previous_messages, human_message],
            intent=None,
            synthesis_content="",
            reflection_output="",
            final_response="",
            current_token_count=0,
            raw_tool_results=[],
            tool_result="",
            needs_more_info=False,
            tool_round=0,
            assets=chat_input.assets,
            targets=chat_input.targets,
            is_last_step=False,
            remaining_steps=self.recursion_limit,
            execution_start_time=time.time(),
            timeout_seconds=self.timeout_in_seconds,
        )

    def _get_tool_calling_rules(self, chat_input: GenerateChatRequest) -> str:
        tool_calling_rules = ""
        if not chat_input.use_brand_guidelines:
            tool_calling_rules += (
                "You should not use the brand guidelines in the tool calls.\n"
            )
        return tool_calling_rules

    def _store_chat_history(
        self, messages: List[Any], chat_input: GenerateChatRequest
    ) -> None:
        self.chat_history.json["previous_messages"] = serialize_message_history(
            messages
        )
        self.chat_history.model = chat_input.model
        self.chat_history.save()
        self.chat_history.refresh_from_db()
