import logging
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, TypedDict

from django.core.cache import cache


class TaskState(str, Enum):
    SUBMITTED = "submitted"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TERMINATED = "terminated"


class PlaygroundChatbotStatus(TypedDict):
    status: TaskState  # The status of the chatbot
    display_message: Optional[str]  # The message to display to the user
    current_step: Optional[str]  # The current step of the chatbot
    step_output: Optional[str]  # The output of the current step
    steps: List[str]  # All steps that the chatbot will take
    message: Optional[str]  # the final message to display to the user


class PlaygroundChatbotSubStep(str, Enum):
    PLAYBOOK_SEARCH = "playbook_search"
    ASSET_READ = "asset_read"
    TARGET_READ = "target_read"


class PlaygroundChatbotStep(str, Enum):
    THINKING = "Thinking"
    READING_URL = "Reading URL"
    WEB_SEARCH = "Searching the web"
    READING_PLAYBOOK = "Reading Playbook Information"
    REFLECTION = "Reflection"
    REWRITING_CONTENT = "Rewriting Content"
    RESPONSE_GENERATION = "Response Generation"
    FINAL_RESULT = "Final Result"


# Static mapping of steps to their sub-steps
STEP_SUB_STEPS = {
    PlaygroundChatbotStep.READING_PLAYBOOK: [
        PlaygroundChatbotSubStep.PLAYBOOK_SEARCH,
        PlaygroundChatbotSubStep.ASSET_READ,
        PlaygroundChatbotSubStep.TARGET_READ,
    ]
}


class ChatbotStepStatusUpdator:
    def __init__(self, task_id: str):
        self.task_id = task_id

    def update_chatbot_step(
        self, step: PlaygroundChatbotStep, output: str, step_finished: bool = False
    ):
        status = self._generate_chatbot_step_status(step, output, step_finished)
        cache.set(self.task_id, status, timeout=3600 * 24)

    def update_chatbot_final_result(self, message: str):
        # If there is not brand guideline, the Response Generation step will be the last step
        # Otherwise, the Final Result step will be the last step
        all_steps = self.get_all_steps()
        if PlaygroundChatbotStep.REWRITING_CONTENT in all_steps:
            step = PlaygroundChatbotStep.FINAL_RESULT
            status = self._generate_chatbot_step_status(step, message, True)
        else:
            step = PlaygroundChatbotStep.RESPONSE_GENERATION
            status = self._generate_chatbot_step_status(step, "", True)
        status["status"] = TaskState.COMPLETED
        status["message"] = message
        cache.set(self.task_id, status, timeout=3600 * 24)

    def update_chatbot_failed(self, error_message: str):
        status = self._generate_chatbot_step_status(
            PlaygroundChatbotStep.FINAL_RESULT, error_message, True
        )
        status["status"] = TaskState.FAILED
        cache.set(self.task_id, status, timeout=3600 * 24)

    def get_current_running_steps(self) -> List[PlaygroundChatbotStep]:
        status = cache.get(self.task_id)
        if status is None:
            return []
        return [step["name"] for step in status["steps"] if step["is_current"]]

    def mark_all_running_steps_as_finished(self):
        for step in self.get_current_running_steps():
            self.update_chatbot_step(step, "", True)

    def get_all_steps(self) -> List[PlaygroundChatbotStep]:
        status = cache.get(self.task_id)
        if status is None:
            return []
        return [step["name"] for step in status["steps"]]

    def _generate_chatbot_step_status(
        self, step: PlaygroundChatbotStep, output: str, step_finished: bool = False
    ) -> PlaygroundChatbotStatus:
        status = cache.get(self.task_id) or {
            "status": TaskState.RUNNING,
            "display_message": None,
            "steps": [],
        }

        # Look for the step to change the status
        target_step = None
        for s in reversed(status["steps"]):
            if s["name"] == step and s["name"] in self.get_current_running_steps():
                target_step = s
                break

        if target_step is not None:
            # Always append the output
            if target_step["output"]:
                target_step["output"] += "\n\n" + output
            else:
                target_step["output"] = output
            target_step["is_current"] = not step_finished
        else:
            status["steps"].append(
                {
                    "id": str(len(status["steps"]) + 1),
                    "name": step,
                    "is_current": not step_finished,
                    "output": output,
                }
            )

        status["display_message"] = output
        return status

    def update_substep(
        self,
        step: PlaygroundChatbotStep,
        sub_step: PlaygroundChatbotSubStep,
        output: str,
        is_finished: bool = False,
    ):
        # Validate that the step can have sub-steps
        sub_steps = STEP_SUB_STEPS.get(step)
        if sub_steps is None:
            logging.error(f"Step {step} does not support sub-steps")
            return

        # Validate that the sub-step is valid for this step
        if sub_step not in sub_steps:
            logging.error(f"Invalid sub-step {sub_step} for step {step}")
            return

        # Get current status
        status = cache.get(self.task_id) or {
            "status": TaskState.RUNNING,
            "display_message": None,
            "steps": [],
        }

        # Find or create the parent step
        parent_step = self._find_parent_step(status, step)
        if parent_step is None:
            # If parent step doesn't exist, create it first
            self.update_chatbot_step(step, "", False)
            status = cache.get(self.task_id)
            parent_step = self._find_parent_step(status, step)

        # Update the sub-step
        self._create_or_update_substep(parent_step, sub_step, output, is_finished)

        # Save the updated status with sub-steps to cache
        cache.set(self.task_id, status, timeout=3600 * 24)

        # Update parent step status
        self._update_parent_step_status(parent_step, step, output)

    def _find_parent_step(
        self, status: Dict, step: PlaygroundChatbotStep
    ) -> Optional[Dict]:
        """Find the most recent parent step in the status."""
        for step_info in reversed(status["steps"]):
            if step_info["name"] == step:
                return step_info
        return None

    def _create_or_update_substep(
        self,
        main_step: Dict,
        sub_step: PlaygroundChatbotSubStep,
        output: str,
        is_finished: bool,
    ) -> None:
        """Create or update a sub-step within a main step."""
        if "sub_steps" not in main_step:
            main_step["sub_steps"] = []

        # Find existing sub-step
        existing_substep = None
        for substep_info in main_step["sub_steps"]:
            if substep_info["name"] == sub_step:
                existing_substep = substep_info
                break

        if existing_substep is None:
            new_substep = {
                "name": sub_step,
                "is_running": not is_finished,
                "output": output,
            }
            main_step["sub_steps"].append(new_substep)
        else:
            existing_substep["output"] = output
            existing_substep["is_running"] = not is_finished

    def _update_parent_step_status(
        self, main_step: Dict, step: PlaygroundChatbotStep, output: str
    ) -> None:
        """Update the main step status based on its sub-steps."""
        all_sub_steps_completed = all(
            substep["is_running"] == False for substep in main_step["sub_steps"]
        )

        if all_sub_steps_completed:
            self.update_chatbot_step(step, output, True)
        else:
            self.update_chatbot_step(step, output, False)
