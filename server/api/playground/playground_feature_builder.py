from ..feature.feature_builder.company_context_feature_builder import (
    CompanyContextFeatureBuilder,
)
from ..models import AssetInfo, AssetInfoGroup, Playbook
from ..playbook_build.object_builder_asset import extract_asset_raw_text
from ..shared_types import ContentType


class PlaygroundFeatureBuilder:
    def __init__(self, gen_env):
        self.gen_env = gen_env

    def get_company_name(self):
        """Fetch the company name for the given playbook instance."""
        return self.gen_env._data_wrapper.company_name

    def get_company_context(self):
        """Fetch information of the company that you are working for."""

        company_context_feature_builder = CompanyContextFeatureBuilder(
            gen_env=self.gen_env
        )
        company_context_feature_builder.set_budgets({"company_context": 6000})
        company_context = company_context_feature_builder.extract_company_context()
        return company_context

    def get_brand_guidelines(self):
        brand_guidelines_text = self.get_content_type_brand_guidelines()
        return brand_guidelines_text

    def get_content_type_brand_guidelines(self):
        l1_key = "[TOFU Internal] Brand Guideline"  # hardcoded
        brand_guideline_l2_key = "Brand Guideline"  # hardcoded

        # Check if the asset exists
        brand_guideline_asset_group = AssetInfoGroup.objects.filter(
            asset_info_group_key=l1_key,
            playbook=self.gen_env._data_wrapper.playbook_instance,
        ).first()
        if not brand_guideline_asset_group:
            return None
        brand_guideline_asset_info = AssetInfo.objects.filter(
            asset_info_group=brand_guideline_asset_group,
            asset_key=brand_guideline_l2_key,
        ).first()

        if not brand_guideline_asset_info:
            return None

        additional_info = brand_guideline_asset_info.additional_info
        brand_guidelines = None
        if additional_info and additional_info.get("guideline", {}):
            brand_guidelines = additional_info.get("guideline", {}).get(
                ContentType.Other, ""
            )
        if brand_guidelines:
            return brand_guidelines

        brand_guideline_raw_text = extract_asset_raw_text(brand_guideline_asset_info)
        return brand_guideline_raw_text

    def get_valid_target_keys(self):
        """
        Fetch the (target_l1_key, target_l2_key) of all the targets stored in the Playbook.

        Return:
            An array of (target_l1_key, target_l2_key) for all the targets.
        """
        playbook_instance = (
            Playbook.objects.filter(pk=self.gen_env._data_wrapper.playbook_instance.id)
            .prefetch_related("target_info_groups__targets")
            .get()
        )
        valid_target_keys = []
        for target_info_group in playbook_instance.target_info_groups.all():
            for target_info in target_info_group.targets.all():
                valid_target_keys.append(
                    (target_info_group.target_info_group_key, target_info.target_key)
                )
        return valid_target_keys
