# async tasks using celery
# restart the celery worker if there's any code change to the tasks

import os
import time
import traceback
from datetime import datetime

import psutil
from celery.exceptions import SoftTimeLimitExceeded
from celery.result import AsyncResult
from django.core.cache import cache
from django.db.models import Q
from django.db.models.signals import post_save
from django.dispatch import receiver
from server.celery import app
from server.logging_config import get_json_logger

from .campaign_gen import CampaignGenerator, mark_leftover_contents_killed
from .connected_assets.connected_assets_async_tasks import (
    async_generated_suggested_connected_asset_group,
)
from .connected_assets.connected_assets_utils import get_company_website
from .models import (
    CompanyInfo,
    Playbook,
    TargetInfoGroup,
    TofuUser,
)
from .playbook_build.target_info_group_wrapper import TargetInfoGroupWrapper
from .thread_locals import (
    reset_current_campaign,
    reset_current_playbook,
    reset_current_user,
    set_current_campaign,
    set_current_playbook,
    set_current_user,
)
from .utils import CloudWatchMetrics, get_user_from_id_or_object

logger = get_json_logger(__name__)


# cannot be put to tasks.py because of circular import
@app.task(bind=True, acks_late=True)
def async_campaign_gen(
    self,
    user_id,
    campaign_id,
    contents_to_update,
    collection_ids=[],
    cache_key="",
    joint_generation=False,
):
    start_time = time.time()
    pid = os.getpid()
    process = psutil.Process(pid)
    initial_memory = process.memory_info().rss / (1024 * 1024)  # Convert to MB
    gen_results = []

    try:
        logger.info(
            "Starting campaign generation",
            extra={
                "campaign_id": campaign_id,
                "cache_key": cache_key,
                "pid": pid,
                "start_time": time.strftime(
                    "%Y-%m-%d %H:%M:%S", time.localtime(start_time)
                ),
                "initial_memory_mb": round(initial_memory, 2),
                "task_id": self.request.id,
            },
        )
        campaign_handler = CampaignGenerator.load_from_db(campaign_id)

        user = get_user_from_id_or_object(user_id, campaign_handler.campaign_instance)
        if not user:
            raise ValueError(
                f"No user specified and campaign {campaign_id} has no creator"
            )
        logger.info(
            "Using user for campaign generation",
            extra={
                "user_id": user.id,
                "campaign_id": campaign_id,
                "task_id": self.request.id,
            },
        )
        set_current_user(user)
        set_current_campaign(campaign_handler.campaign_instance)
        set_current_playbook(campaign_handler.campaign_instance.playbook)

        generate_job_id = f"{cache_key}:execute"

        gen_results = campaign_handler.gen(
            user_id=user_id,
            contents_to_update=contents_to_update,
            collection_ids=collection_ids,
            cache_key=generate_job_id,
            joint_generation=joint_generation,
        )

        # At the end of successful execution:
        cache.set(cache_key, {"status": "SUCCESS"}, timeout=60 * 60 * 24)
        return gen_results
    except SoftTimeLimitExceeded:
        # update the status of the contents from database
        content_ids = [
            content_id
            for contents in contents_to_update.values()
            for content_id in contents
        ]
        mark_leftover_contents_killed(
            content_ids=content_ids, kill_reason="Time limit exceeded"
        )
        cache_data = cache.get(cache_key, {})
        cache_data.update({"status": "FAILURE", "error": "Time limit exceeded"})
        cache.set(cache_key, cache_data, 3600 * 24 * 30)
        logger.info(
            "Task timed out",
            extra={"campaign_id": campaign_id, "pid": pid, "task_id": self.request.id},
        )
    except Exception as e:
        logger.error(
            "Error in async_campaign_gen",
            extra={
                "campaign_id": campaign_id,
                "cache_key": cache_key,
                "pid": pid,
                "error": str(e),
                "traceback": traceback.format_exc(),
                "task_id": self.request.id,
            },
        )

        # update the status of the contents from database
        content_ids = [
            content_id
            for contents in contents_to_update.values()
            for content_id in contents
        ]
        if "Insufficient credits" in str(e):
            mark_leftover_contents_killed(
                content_ids=content_ids, kill_reason="Insufficient credits"
            )
        else:
            mark_leftover_contents_killed(
                content_ids=content_ids, kill_reason="Time limit exceeded"
            )

        cache_data = cache.get(cache_key, {})
        cache_data.update({"status": "FAILURE", "error": str(e)})
        cache.set(cache_key, cache_data, 3600 * 24 * 30)
    finally:
        reset_current_campaign()
        reset_current_playbook()
        reset_current_user()

        final_memory = process.memory_info().rss / (1024 * 1024)  # Convert to MB
        stop_time = time.time()
        duration = stop_time - start_time
        logger.info(
            "Completed campaign generation",
            extra={
                "campaign_id": campaign_id,
                "cache_key": cache_key,
                "pid": pid,
                "stop_time": time.strftime(
                    "%Y-%m-%d %H:%M:%S", time.localtime(stop_time)
                ),
                "duration_seconds": round(duration, 2),
                "final_memory_mb": round(final_memory, 2),
                "task_id": self.request.id,
            },
        )
    return gen_results


# post-processing operations triggered by signals
@receiver(post_save, sender=Playbook)
def postprocess_playbook(sender, instance, created, update_fields=None, **kwargs):
    if os.environ.get("TOFU_ENV") == "unit_test":
        return
    from .tasks import async_postprocess_playbook

    try:
        if created:
            update_fields = ["company_info", "target_info", "assets"]
        elif not update_fields:
            update_fields = []
        else:
            update_fields = list(update_fields)
        task_key = f"playbook_postprocess:{instance.id}"

        # revoke any previously running task
        existing_task_id = cache.get(task_key)
        # skip revoke for eager mode
        if not app.conf.task_always_eager and existing_task_id is not None:
            running_task = AsyncResult(existing_task_id, app=app)
            if running_task.state in ("PENDING", "STARTED", "RETRY"):
                logger.info(
                    "Revoking running task",
                    extra={
                        "task_id": existing_task_id,
                        "state": running_task.state,
                        "playbook_id": instance.id,
                    },
                )
                app.control.revoke(existing_task_id, terminate=True)

        task_id = f"{task_key}:{datetime.utcnow().isoformat()}"

        async_postprocess_playbook.apply_async(
            args=(instance.id, update_fields),
            task_id=task_id,
            priority=1,
        )

        # Store the task ID in cache for 1 hour
        cache.set(task_key, task_id, timeout=60 * 60)
    except Exception as e:
        logger.error(
            "Error in postprocess_playbook",
            extra={
                "error": str(e),
                "playbook_id": instance.id,
                "update_fields": update_fields,
            },
        )


post_save.connect(postprocess_playbook, sender=Playbook)


@receiver(post_save, sender=CompanyInfo)
def postprocess_company_info(sender, instance, created, update_fields=None, **kwargs):
    if os.environ.get("TOFU_ENV") == "unit_test":
        return

    try:
        company_info = CompanyInfo.objects.get(id=instance.id)
        if not hasattr(company_info, "playbook"):
            logger.info(
                "No playbook found for company info",
                extra={"company_info_id": company_info.id},
            )
            return

        playbook = company_info.playbook

        lite_user = playbook.users.filter(
            customer_type=TofuUser.CustomerType.LITE
        ).first()
        if lite_user:
            logger.info(
                "Skipping since user is tofu lite",
                extra={"user_id": lite_user.id, "playbook_id": playbook.id},
            )
            return

        # filter out on e2e test user and eval user (user name like e2e_test or eval)
        playbook_users = playbook.users.all()
        if playbook_users.filter(
            Q(username__startswith="tofuadmin-e2etest")
            | Q(username__startswith="tofuadmin-eval")
        ).exists():
            logger.info(
                "Skipping since user is e2e test or eval",
                extra={
                    "user_id": playbook_users.first().id,
                    "playbook_id": playbook.id,
                },
            )
            return

        playbook_id = playbook.id

        company_website = get_company_website(playbook)
        if not company_website:
            logger.warning(
                "No company website found for playbook",
                extra={"playbook_id": playbook_id},
            )
            return
        task_key = f"company_info_postprocess_site_map_discovering:{playbook_id}"
        if not app.conf.task_always_eager:
            running_task = AsyncResult(task_key, app=app)
            if running_task.state in ("STARTED", "RETRY"):
                logger.info(
                    "Skipping since there is already a running task",
                    extra={
                        "task_key": task_key,
                        "state": running_task.state,
                        "playbook_id": playbook_id,
                    },
                )
                return
            # we need to forget the task since celery will not rerun the task has already ran
            running_task.forget()
            if running_task.state == "REVOKED":
                logger.info(
                    "Revoked task, re-running",
                    extra={"task_key": task_key, "playbook_id": playbook_id},
                )
        # delete the cache key if it exists for new company info
        cache.delete(task_key)
        async_generated_suggested_connected_asset_group.apply_async(
            args=(playbook_id,),
            task_id=task_key,
            priority=5,
        )
    except Exception as e:
        logger.exception(
            "Error in postprocess_company_info",
            extra={
                "error": str(e),
                "company_info_id": instance.id,
                "traceback": traceback.format_exc(),
            },
        )
        CloudWatchMetrics.put_metric(
            "ConnectedAssetsDiscovery_SubmissionFailed",
            1,
            dimensions=[],
        )
        # we don't raise the error for now because it's not a critical error
        # ToDo: raise the error in the future


post_save.connect(postprocess_company_info, sender=CompanyInfo)


@app.task(bind=True, acks_late=False)
def celery_heartbeat(self):
    try:
        current_time = datetime.utcnow().isoformat()
        logger.info(
            "Setting heartbeat cache",
            extra={"timestamp": current_time, "task_id": self.request.id},
        )
        cache.set("celery_heartbeat", current_time, timeout=60 * 60)
        logger.info(
            "Heartbeat cache set",
            extra={"timestamp": current_time, "task_id": self.request.id},
        )
    except Exception as e:
        logger.error(
            "Error in celery_heartbeat",
            extra={"error": str(e), "task_id": self.request.id},
        )


@app.task(bind=True, acks_late=True)
def async_enrich_target_data(self, target_info_group_id, enrich_field, task_id):
    try:
        target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)
        target_info_list_wrapper = TargetInfoGroupWrapper(target_info_group)

        data_enrichment_result = target_info_list_wrapper.enrich_data(
            enrich_field_name=enrich_field,
            task_id=task_id,
        )
        cache.set(
            task_id, {"task_return": data_enrichment_result}, timeout=60 * 60 * 24 * 30
        )
        return data_enrichment_result
    except Exception as e:
        logger.exception(
            "Error in data enrichment task",
            extra={
                "error": str(e),
                "target_info_group_id": target_info_group_id,
                "enrich_field": enrich_field,
                "task_id": task_id,
                "traceback": traceback.format_exc(),
            },
        )
        raise


def get_task_result(task_id):
    try:
        async_result = AsyncResult(task_id, app=app)
        if not async_result.ready():
            return None
        if async_result.successful():
            return async_result.result
        elif async_result.failed():
            error = async_result.result
            return {
                "error": str(error) if error else "Task failed without specific error"
            }
        else:
            return {"error": f"Unexpected task state: {async_result.state}"}
    except Exception as e:
        logger.error(
            "Error retrieving task result", extra={"error": str(e), "task_id": task_id}
        )
        return {"error": f"Error retrieving result: {str(e)}"}


def wait_for_all_tasks(task_ids, timeout, check_interval=5):
    if not task_ids:
        return {}

    start_time = time.time()
    async_results = {task_id: AsyncResult(task_id, app=app) for task_id in task_ids}
    results = {}

    # Keep checking pending tasks until timeout
    try:
        while True:
            # Check if we've exceeded timeout
            if time.time() - start_time > timeout:
                # Add timeout error for remaining pending tasks
                timeout_tasks = [
                    task_id
                    for task_id, result in async_results.items()
                    if task_id not in results
                ]
                for task_id in timeout_tasks:
                    results[task_id] = {"error": "Task timed out"}
                logger.error(
                    "Tasks timed out",
                    extra={
                        "timeout_tasks": timeout_tasks,
                        "timeout_seconds": timeout,
                        "elapsed_seconds": time.time() - start_time,
                    },
                )
                break

            # Process any newly completed tasks
            still_pending = False
            for task_id, result in async_results.items():
                if task_id in results:
                    continue

                if result.ready():
                    try:
                        results[task_id] = get_task_result(task_id)
                    except Exception as e:
                        logger.exception(
                            "Error getting result for task",
                            extra={
                                "error": str(e),
                                "task_id": task_id,
                                "traceback": traceback.format_exc(),
                            },
                        )
                        results[task_id] = {"error": str(e)}
                else:
                    still_pending = True

            # If no tasks are pending, we're done
            if not still_pending:
                break

            # Wait a bit before checking again
            time.sleep(check_interval)
    except Exception as e:
        logger.exception(
            "Error in wait_for_all_tasks",
            extra={
                "error": str(e),
                "task_ids": task_ids,
                "timeout": timeout,
                "traceback": traceback.format_exc(),
            },
        )
        return {}

    return results
