import logging

from django.core.cache import cache
from django.db import transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, F, Prefetch, Value
from django.db.models.functions import Cast, Coalesce
from django.forms.models import model_to_dict
from django.utils import timezone
from langchain_core.documents import Document

from .actions.action_copier import ActionCopier
from .campaign import CampaignHandler
from .content_group import ContentGroupHandler
from .models import Action, Campaign, Content, ContentGroup, EventLogs, Playbook


class PlaybookCampaignHandler:
    def __init__(self, playbook_instance) -> None:
        self.playbook_instance = playbook_instance

    def copy_campaign(self, src_campaign, copy_generations):

        content_group_mapping = {}
        with transaction.atomic():
            creator = self.playbook_instance.users.first()

            new_campaign_data = model_to_dict(
                src_campaign, exclude=["id", "updated_at", "created_at"]
            )
            new_campaign_data.update(
                {
                    "id": None,
                    "creator": creator,
                    "playbook": self.playbook_instance,
                }
            )
            new_campaign = Campaign.objects.create(**new_campaign_data)

            src_content_groups = ContentGroup.objects.filter(campaign=src_campaign)
            for content_group in src_content_groups:
                new_content_group_data = model_to_dict(
                    content_group, exclude=["id", "updated_at", "created_at", "actions"]
                )
                new_content_group_data.update(
                    {
                        "id": None,
                        "creator": creator,
                        "campaign": new_campaign,
                        "content_group_status": {},
                    }
                )
                new_content_group = ContentGroup.objects.create(
                    **new_content_group_data
                )
                content_group_mapping[content_group.id] = new_content_group

        with transaction.atomic():
            for (
                src_content_group_id,
                dest_content_group,
            ) in content_group_mapping.items():
                CampaignHandler.fix_linked_content_group(dest_content_group)
                content_group_handler = ContentGroupHandler(dest_content_group)
                content_group_handler.bulk_create_content()
                if copy_generations:
                    src_content_group = ContentGroup.objects.get(
                        id=src_content_group_id
                    )
                    content_group_handler.bulk_copy_generations(src_content_group)
            CampaignHandler(new_campaign).update_content_collection_params()

        return new_campaign

    def list_campaign_templates(self, is_campaign_v3=False):
        cache_key = (
            "campaign_templates" if not is_campaign_v3 else "campaign_templates_v3"
        )

        try:
            cached_data = cache.get(cache_key)
        except Exception as e:
            logging.exception(f"debug: Failed to get campaign templates: {e}")
            cached_data = None
        if cached_data:
            return cached_data
        campaign_templates = {}
        # Prefetch related ContentGroup objects
        content_group_prefetch = Prefetch(
            "contentgroup_set", queryset=ContentGroup.objects.order_by("created_at")
        )

        # Fetch Campaign objects and prefetch related ContentGroup objects
        campaign_ids = (
            Campaign.objects.annotate(
                is_campaign_v3_value=Coalesce(
                    Cast(
                        F("campaign_params__is_campaign_v3"),
                        output_field=BooleanField(),
                    ),
                    Value(False),
                    output_field=BooleanField(),
                )
            )
            .filter(
                campaign_params__campaign_template__is_active=True,
                is_campaign_v3_value=is_campaign_v3,
            )
            .prefetch_related(content_group_prefetch)
        )

        # Process the campaigns and their content groups
        for campaign in campaign_ids:
            campaign_templates[campaign.id] = campaign.campaign_params.get(
                "campaign_template", {}
            )
            campaign_templates[campaign.id].pop("is_active", None)
            campaign_templates[campaign.id]["campaign_name"] = campaign.campaign_name

            action_data = list(
                Action.objects.filter(campaign=campaign).values(
                    "id", "action_name", "action_category"
                )
            )
            campaign_templates[campaign.id]["actions"] = action_data

            content_groups = []
            for content_group in campaign.contentgroup_set.all():
                content_group_dict = {
                    "name": content_group.content_group_name,
                    "id": content_group.id,
                    "contentType": content_group.content_group_params.get(
                        "content_type", ""
                    ),
                }
                if content_group.content_group_params.get("content_collection"):
                    content_group_dict["contentCollectionId"] = (
                        content_group.content_group_params.get(
                            "content_collection"
                        ).get("id")
                    )
                    content_group_dict["contentCollectionName"] = (
                        content_group.content_group_params.get(
                            "content_collection"
                        ).get("name")
                    )
                content_groups.append(content_group_dict)
            campaign_templates[campaign.id]["content_groups"] = content_groups

        cache.set(cache_key, campaign_templates, 60 * 60 * 24 * 7)
        return campaign_templates

    def copy_campaign_template(
        self,
        new_campaign_user,
        src_campaign,
        new_campaign_name,
        assets,
        content_group_ids,
        dest_campaign=None,
    ):
        with transaction.atomic():
            is_src_campaign_v3 = src_campaign.campaign_params.get(
                "is_campaign_v3", False
            )
            new_campaign_data = model_to_dict(
                src_campaign,
                exclude=[
                    "id",
                    "updated_at",
                    "created_at",
                    "campaign_status",
                    "campaign_name",
                ],
            )
            new_campaign_data.update(
                {
                    "id": None,
                    "creator": new_campaign_user,
                    "playbook": self.playbook_instance,
                    "campaign_name": new_campaign_name,
                    "campaign_status": {},
                }
            )
            if not dest_campaign:
                new_campaign = Campaign.objects.create(**new_campaign_data)
            else:
                new_campaign = dest_campaign

                # store the custom_instructions from dest_campaign if it exists
                dest_campaign_custom_instructions = dest_campaign.campaign_params.get(
                    "custom_instructions", {}
                )
                # Validate if this campaign has any content_groups, and if so log the error.
                dest_campaign_content_groups = ContentGroup.objects.filter(
                    campaign=dest_campaign
                )
                if dest_campaign_content_groups.exists():
                    logging.error(
                        "Copying a campaign template with existing content groups."
                    )
                for key, value in new_campaign_data.items():
                    if value:
                        setattr(new_campaign, key, value)

                # update new_campaign custom_instructions
                if dest_campaign_custom_instructions:
                    new_campaign.campaign_params["custom_instructions"] = (
                        dest_campaign_custom_instructions
                    )
            # update new_campaign assets
            if assets:
                new_campaign.campaign_params["assets"] = assets
            # update new_campaign orig_campaign_id
            new_campaign.campaign_params["orig_campaign_id"] = src_campaign.id
            # pop campaign_template
            new_campaign.campaign_params.pop("campaign_template", None)
            # pop targets
            new_campaign.campaign_params.pop("targets", None)
            new_campaign.save()

            if is_src_campaign_v3:  # campaign v3
                action_copier = ActionCopier()
                action_copier.copy_actions_for_campaign(
                    src_campaign,
                    new_campaign,
                    pop_assets=True,
                    pop_template=True,
                )

                new_campaign.campaign_status["clone_status"] = {
                    "status": "FINISHED",
                    "update_time": timezone.now().isoformat(),
                }
                new_campaign.save(update_fields=["campaign_status"])
            else:
                new_content_groups = []
                src_content_groups = ContentGroup.objects.filter(
                    campaign=src_campaign
                ).filter(id__in=content_group_ids)
                for content_group in src_content_groups:
                    new_content_group_data = model_to_dict(
                        content_group,
                        exclude=["id", "updated_at", "created_at", "actions"],
                    )
                    new_content_group_data.update(
                        {
                            "id": None,
                            "creator": new_campaign_user,
                            "campaign": new_campaign,
                            "content_group_status": {},
                        }
                    )
                    new_content_group = ContentGroup.objects.create(
                        **new_content_group_data
                    )
                    # update orig_content_group_id
                    new_content_group.content_group_params["orig_content_group_id"] = (
                        content_group.id
                    )
                    new_content_group.save()
                    new_content_groups.append(new_content_group)

                with transaction.atomic():
                    for content_group in new_content_groups:
                        CampaignHandler.fix_linked_content_group(content_group)
                        content_group_handler = ContentGroupHandler(content_group)
                        content_group_handler.bulk_create_content()

        with transaction.atomic():
            CampaignHandler(new_campaign).update_content_collection_params(
                is_campaign_v3=is_src_campaign_v3
            )
            return new_campaign
