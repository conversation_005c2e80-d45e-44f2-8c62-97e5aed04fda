"""
Import all views from the api_views package.
This file is maintained for backwards compatibility.
New code should import directly from api_views.
"""

import logging
from datetime import datetime

from celery.result import AsyncResult
from django.core.cache import cache
from django.db import IntegrityError
from django.db.utils import OperationalError
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import mixins, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from rest_framework.response import Response

# Re-export ViewSets from api_views
from .api_views import (  # User views; Action views; Asset views; Content views; Company views; Playbook views; Evaluation views; Public views; Target views; Chatbot views
    ActionEdgeViewSet,
    ActionViewSet,
    AssetInfoGroupViewSet,
    AssetInfoViewSet,
    CampaignViewSet,
    ChatbotViewSet,
    CompanyInfoViewSet,
    ContentGroupViewSet,
    ContentVariationViewSet,
    ContentViewSet,
    EvalViewSet,
    PlaybookViewSet,
    PublicViewSet,
    TargetInfoGroupViewSet,
    TargetInfoViewSet,
    UserViewSet,
)
from .async_tasks import celery_heartbeat
from .campaign import CampaignHandler
from .campaign_gen_wrapper import CampaignGenWrapper
from .gen_status import GenStatusUpdater
from .logger import tofu_axiom_logger
from .models import (
    Campaign,
    ContentTemplate,
    FeatureAnnouncement,
    Playbook,
    Tag,
    TofuUser,
)
from .playbook_build.template_tone_analysis_builder import async_create_tone_analysis
from .serializers import (
    CampaignSerializer,
    ContentTemplateSerializer,
    EmptySerializer,
    FeatureAnnouncementSerializer,
    TagSerializer,
)

# Maintain backwards compatibility by re-exporting all names


def index(request):
    return HttpResponse("Hello from Tofu makers!")


class TaskViewSet(viewsets.GenericViewSet):
    serializer_class = EmptySerializer

    @swagger_auto_schema(
        operation_summary="Check async task status",
        manual_parameters=[
            openapi.Parameter(
                name="task_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Task ID",
                required=True,
            ),
        ],
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="OK",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": {
                            "type": openapi.TYPE_STRING,
                            "description": "Task status",
                        },
                        "result": {
                            "type": openapi.TYPE_OBJECT,
                            "description": "Task result if task successes",
                        },
                    },
                ),
            ),
            status.HTTP_400_BAD_REQUEST: "Bad request",
        },
    )
    @action(detail=False, methods=["get"])
    def status(self, request, *args, **kwargs):
        task_id = request.query_params.get("task_id")
        if not task_id:
            return Response(
                {"error": f"task_id {task_id} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        result = AsyncResult(task_id)
        response_data = {
            "status": result.status,
        }
        if result.status == "FAILURE":
            response_data["error"] = str(result.result)

        cache_data = cache.get(task_id)
        if cache_data:
            # partial results
            response_data["task_return"] = cache_data.get("task_return", None)

            if result.status == "PENDING":
                if "status" in cache_data:
                    response_data["status"] = cache_data["status"]
                if "error" in cache_data:
                    response_data["error"] = cache_data["error"]
        else:
            # final results
            response_data["task_return"] = []

        return Response(response_data, status=status.HTTP_200_OK)


class DataViewSet(viewsets.GenericViewSet):
    serializer_class = EmptySerializer

    def is_request_from_superuser(self, request):
        if request.user.is_superuser:
            return True
        return False

    @swagger_auto_schema(
        operation_summary="Save value to cache",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "key": {
                    "type": openapi.TYPE_STRING,
                    "example": "1234-5678-9012-3456",
                },
                "value": {
                    "type": openapi.TYPE_STRING,
                    "example": "cache_value",
                },
                "timeout": {
                    "type": openapi.TYPE_INTEGER,
                    "example": 168,
                },
            },
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="OK",
            ),
            status.HTTP_400_BAD_REQUEST: openapi.Response(
                description="Bad request",
            ),
        },
    )
    @action(detail=False, methods=["post"])
    def save_cache(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        key = request.data.get("key")
        value = request.data.get("value")
        timeout = request.data.get("timeout", 24)
        if not key:
            return Response(
                {"error": f"key {key} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not value:
            return Response(
                {"error": f"empty value {value} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if timeout > 24 * 7:
            return Response(
                {"error": f"timeout {timeout} hours is too long"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        cache.set(key, value, timeout=60 * 60 * timeout)
        return Response({}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Get value from cache",
        manual_parameters=[
            openapi.Parameter(
                name="key",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Key",
                required=True,
            ),
        ],
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="OK",
            ),
            status.HTTP_400_BAD_REQUEST: openapi.Response(
                description="Bad request",
            ),
        },
    )
    @action(detail=False, methods=["get"])
    def get_cache(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        key = request.query_params.get("key")
        if not key:
            return Response(
                {"error": f"key {key} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        value = cache.get(key)
        if not value:
            return Response(
                {"error": f"value not found for key {key}"},
                status=status.HTTP_404_NOT_FOUND,
            )
        return Response({"value": value}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Delete value from cache",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "key": {
                    "type": openapi.TYPE_STRING,
                    "example": "1234-5678-9012-3456",
                },
            },
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="OK",
            ),
            status.HTTP_400_BAD_REQUEST: openapi.Response(
                description="Bad request",
            ),
        },
    )
    @action(detail=False, methods=["post"])
    def delete_cache(self, request, *args, **kwargs):
        if not self.is_request_from_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        key = request.data.get("key")

        if not key:
            return Response(
                {"error": f"key {key} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        value = cache.get(key)

        if value == None:
            return Response(
                {"error": f"value not found for key {key}"},
                status=status.HTTP_404_NOT_FOUND,
            )

        if cache.delete(key):
            return Response({}, status=status.HTTP_200_OK)

        return Response(
            {"error": f"value for key {key} not deleted successfully"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    @swagger_auto_schema(
        operation_summary="Log an event depending on the event type",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "event_type": openapi.Schema(
                    type=openapi.TYPE_STRING, description="The type of the event to log"
                ),
                "payload": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="The payload of the event to log",
                ),
            },
        ),
    )
    @action(detail=False, methods=["post"])
    def log_event(self, request, *args, **kwargs):
        user = request.user
        event_type = request.data.get("event_type")
        payload = request.data.get("payload", {})
        kwargs = {**payload, "user_id": user.id, "username": user.username}
        if event_type == "paragon_request_frontend":
            tofu_axiom_logger.log_axiom(event_type=event_type, **kwargs)
        else:
            return Response(
                {"error": f"event_type {event_type} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response({}, status=status.HTTP_200_OK)


class HeartBeatViewSet(viewsets.GenericViewSet):
    serializer_class = EmptySerializer
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Check the heartbeat of the celery workers",
        manual_parameters=[
            openapi.Parameter(
                name="token",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="access token",
                required=True,
            ),
            openapi.Parameter(
                name="interval",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description="expected interval between heartbeats",
                required=True,
            ),
        ],
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_503_SERVICE_UNAVAILABLE: "Service unavailable",
        },
    )
    @action(detail=False, methods=["get"])
    def celery(self, request, *args, **kwargs):
        token = request.query_params.get("token")
        if not token or token != "friedtofu":
            return Response(
                {"error": f"token {token} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        interval = int(request.query_params.get("interval"))
        celery_heartbeat.apply_async(
            args=[],
            task_id="celery_heartbeat",
            priority=9,
        )

        last_request_time = cache.get("celery_heartbeat_request_time", None)
        cache.set("celery_heartbeat_request_time", datetime.utcnow().isoformat())
        if last_request_time:
            last_request_time = datetime.fromisoformat(last_request_time)
            diff = datetime.utcnow() - last_request_time
            if diff.seconds > interval:
                return Response(
                    {
                        "error": f"heartbeat is not received for {diff.seconds} seconds. skipping this check"
                    },
                    status=status.HTTP_200_OK,
                )

        heartbeat = cache.get("celery_heartbeat", None)
        if not heartbeat:
            return Response(
                {"error": f"celery worker is not running"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )
        else:
            heartbeat = datetime.fromisoformat(heartbeat)
            diff = datetime.utcnow() - heartbeat
            if diff.seconds > interval:
                return Response(
                    {"error": f"celery worker is not running or overwhelmed"},
                    status=status.HTTP_503_SERVICE_UNAVAILABLE,
                )
        return Response(
            {"message": "celery worker is running"}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Check the heartbeat of the personalization campaign generation",
        manual_parameters=[
            openapi.Parameter(
                name="token",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="access token",
                required=True,
            ),
        ],
        responses={
            status.HTTP_200_OK: "OK",
            status.HTTP_400_BAD_REQUEST: "Bad request",
            status.HTTP_500_INTERNAL_SERVER_ERROR: "Internal server error",
        },
    )
    @action(detail=False, methods=["get"])
    def p13n_campaign_gen(self, request, *args, **kwargs):
        token = request.query_params.get("token")
        if not token or token != "friedtofu":
            return Response(
                {"error": f"token {token} is not valid"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # personalization campaign for heartbeat check
        user_id = 196
        campaign = Campaign.objects.get(id=3133)
        status_data = GenStatusUpdater().get_campaign_gen_status(campaign)
        campaign_handler = CampaignHandler.load_from_db_instance(campaign)
        campaign_handler.delete_results()

        user = TofuUser.objects.get(id=user_id)

        campaign_gen_wrapper = CampaignGenWrapper(campaign)
        campaign_gen_wrapper.submit_job(
            user=user,
            content_group_ids=[],
            content_ids=[],
            collection_ids=[],
            continue_gen=False,
            joint_generation=True,
            use_all_contents=True,
        )

        if status_data.get("status", "") != "FINISHED":
            return Response(
                {"error": f"p13n campaign generation is failed"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        return Response(
            {"message": "p13n campaign generation is successful"},
            status=status.HTTP_200_OK,
        )


class ContentTemplateViewSet(viewsets.ModelViewSet):
    queryset = ContentTemplate.objects.all()
    serializer_class = ContentTemplateSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "content_template_id"

    def get_object(self):
        queryset = self.get_queryset()
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_url_kwarg]}
        obj = queryset.filter(**filter_kwargs).first()
        return obj

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.lookup_url_kwarg in self.kwargs:
            context["content_template_id"] = self.kwargs[self.lookup_url_kwarg]
        return context

    def is_request_from_creator_playbook_or_superuser(self, request):
        obj = self.get_object()
        if not obj:
            return False
        if not request.user:
            return False
        obj_creator_playbook = obj.playbook
        request_user_playbook = Playbook.objects.filter(users=request.user).first()
        if obj_creator_playbook == request_user_playbook or request.user.is_superuser:
            return True
        return False

    def is_internal_features_enabled(self, request: Request):
        if request.user.is_superuser:
            return True
        if request.user.context and request.user.context.get("internalFeatures", False):
            return True
        return False

    @swagger_auto_schema(
        operation_summary="List content templates",
        manual_parameters=[
            openapi.Parameter(
                name="is_template_v2",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Filter for template_v2 templates",
                required=False,
            ),
            openapi.Parameter(
                name="content_type",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter by content type",
                required=False,
            ),
        ],
        responses={
            status.HTTP_200_OK: ContentTemplateSerializer(many=True),
            status.HTTP_404_NOT_FOUND: "User's playbook not found",
        },
    )
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        request_user_playbook = Playbook.objects.filter(users=request.user).first()
        if not request_user_playbook:
            return Response(status=status.HTTP_404_NOT_FOUND)

        # Filter by playbook
        queryset = queryset.filter(playbook=request_user_playbook)

        # Fix is_template_v2 parameter handling
        is_template_v2_param = request.query_params.get(
            "is_template_v2", "false"
        ).lower()
        is_template_v2 = is_template_v2_param in ("true", "1", "yes")

        if is_template_v2:
            queryset = queryset.filter(template_data__is_template_v2=True)
        else:
            queryset = queryset.filter(
                template_data__is_template_v2__isnull=True
            ) | queryset.filter(template_data__is_template_v2=False)

        # Filter by content_type if provided
        content_type = request.query_params.get("content_type")
        if content_type:
            queryset = queryset.filter(content_types__contains=[content_type])

        # Order the results
        queryset = queryset.order_by("-updated_at")

        self.queryset = queryset
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        if not request.user:
            return Response(status=status.HTTP_403_FORBIDDEN)

        # add request user as creator
        request.data["creator"] = request.user.id
        return super().create(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_playbook_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_playbook_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        if not self.get_object():
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_playbook_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        method="post",
        request_body=openapi.Schema(type=openapi.TYPE_OBJECT, properties={}),
        responses={
            status.HTTP_200_OK: "Tone analysis task submitted successfully",
            status.HTTP_403_FORBIDDEN: "Permission denied",
            status.HTTP_404_NOT_FOUND: "Content template not found",
            status.HTTP_400_BAD_REQUEST: "This template is not supported for tone analysis.",
        },
    )
    @action(detail=True, methods=["post"])
    def tone_analyze(self, request, *args, **kwargs):
        content_template = self.get_object()
        if not content_template:
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not self.is_request_from_creator_playbook_or_superuser(request):
            return Response(status=status.HTTP_403_FORBIDDEN)

        # check if it's v3
        if content_template.template_data.get("is_template_v2", False):
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"error": "This template is not supported for tone analysis."},
            )

        task_id = f"tone_analyze_{content_template.id}_{datetime.now().isoformat()}"
        async_create_tone_analysis.delay(content_template.id, task_id)
        return Response(status=status.HTTP_200_OK, data={"task_id": task_id})


class FeatureAnnouncementViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    queryset = FeatureAnnouncement.objects.filter(is_shown=True).order_by(
        "position", "-updated_at"
    )
    serializer_class = FeatureAnnouncementSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["type"]
    # change this api to public since we want to show the service outage banner on login page as well
    permission_classes = [AllowAny]


class TagViewSet(viewsets.ModelViewSet):
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    lookup_field = "pk"
    lookup_url_kwarg = "tag_id"

    def get_object(self):
        obj = get_object_or_404(
            self.get_queryset(), pk=self.kwargs[self.lookup_url_kwarg]
        )
        if not self.is_request_from_creator_or_superuser(self.request, obj):
            raise PermissionDenied("You don't have permission to access this tag.")
        return obj

    def is_request_from_creator_or_superuser(self, request, obj):
        return obj.creator == request.user or request.user.is_superuser

    def list(self, request, *args, **kwargs):
        self.queryset = self.queryset.filter(creator=request.user)
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Create a new tag",
        request_body=TagSerializer,
        responses={status.HTTP_201_CREATED: TagSerializer},
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                self.perform_create(serializer)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except IntegrityError as e:
                if "unique constraint" in str(e).lower() and "name" in str(e).lower():
                    return Response(
                        {
                            "error": "A tag with this name already exists for your account."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                else:
                    return Response(
                        {"error": "An error occurred while creating the tag."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except OperationalError:
                return Response(
                    {"error": "A database error occurred. Please try again."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Partially update a tag",
        request_body=TagSerializer(partial=True),
        responses={
            status.HTTP_200_OK: TagSerializer,
            status.HTTP_403_FORBIDDEN: "You don't have permission to update this tag.",
            status.HTTP_404_NOT_FOUND: "Tag not found.",
        },
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Manage campaign tags",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "campaign_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_INTEGER),
                ),
                "tag_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_INTEGER),
                ),
                "action": openapi.Schema(
                    type=openapi.TYPE_STRING, enum=["add", "remove"]
                ),
            },
            required=["campaign_ids", "tag_ids", "action"],
        ),
        responses={
            status.HTTP_200_OK: CampaignSerializer(many=True),
            status.HTTP_400_BAD_REQUEST: "Invalid request",
            status.HTTP_404_NOT_FOUND: "Campaigns or tags not found",
        },
    )
    @action(detail=False, methods=["post"])
    def manage_campaign_tags(self, request):
        campaign_ids = request.data.get("campaign_ids", [])
        tag_ids = request.data.get("tag_ids", [])
        action = request.data.get("action", None)

        if not campaign_ids or not tag_ids:
            return Response(
                {"error": "Both campaign_ids and tag_ids are required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        campaigns = Campaign.objects.filter(id__in=campaign_ids, creator=request.user)
        if not campaigns:
            return Response(
                {
                    "error": "No campaigns found with the provided IDs in the user's account."
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        tags = Tag.objects.filter(id__in=tag_ids, creator=request.user)
        if not tags:
            return Response(
                {"error": "No tags found with the provided IDs in the user's account."},
                status=status.HTTP_404_NOT_FOUND,
            )

        if action == "add":
            for campaign in campaigns:
                campaign.campaignTags.add(*tags)
        elif action == "remove":
            for campaign in campaigns:
                campaign.campaignTags.remove(*tags)
        else:
            return Response(
                {"error": "Invalid action. Use 'add' or 'remove'."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        updated_campaigns = CampaignSerializer(campaigns, many=True).data
        return Response(updated_campaigns, status=status.HTTP_200_OK)
