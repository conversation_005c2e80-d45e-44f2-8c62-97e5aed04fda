import logging

from .data_loaders.url_loader import TofuURLLoader
from .models import AssetInfo
from .playbook_build.doc_loader import <PERSON><PERSON>oa<PERSON>
from .thread_locals import get_enable_crawl_api


def check_valid_url_anchor_contents(data_wrapper):
    asset_params = data_wrapper.aggregated_asset_params
    playbook_instance = data_wrapper.playbook_instance
    # Check if the anchor contents in the asset_params have valid url contents.
    for asset_param in asset_params:
        if asset_param.get("meta", "") != "repurpose_anchor_content":
            continue
        asset = asset_param.get("assets")
        if isinstance(asset, list):
            asset = asset[0]
        for l1_key, l2_key in asset.items():
            # raises error if the asset info does not exist
            try:
                asset_info = AssetInfo.objects.get(
                    asset_key=l2_key,
                    asset_info_group__asset_info_group_key=l1_key,
                    asset_info_group__playbook=playbook_instance,
                )
            except AssetInfo.DoesNotExist:
                logging.error(
                    f"Asset info with asset_key {l2_key} and asset_info_group_key {l1_key} does not exist for playbook {playbook_instance.id}."
                )
                raise ValueError(
                    f"Asset info with asset_key {l2_key} and asset_info_group_key {l1_key} does not exist."
                )
            for data_entry in asset_info.docs.values():
                if data_entry.get("type", "") == "url":
                    value = data_entry.get("value", "")

                    if get_enable_crawl_api():
                        try:
                            loaded_docs = DocLoader.crawl_docs_from_api("url", value)
                        except Exception as e:
                            raise ValueError(
                                f"The generation failed because we weren't able to crawl {value}. You can try to re-generate by deleting the url and adding more information manually."
                            )
                    else:
                        loader = TofuURLLoader(value)
                        loaded_docs = loader.load()
                    total_words = sum(
                        len(doc.page_content.split()) for doc in loaded_docs
                    )
                    if total_words < 10:
                        raise ValueError(
                            f"The generation failed because we weren't able to get enough content from {value}. You can try to re-generate by deleting the url and adding more information manually."
                        )
