import logging

import google.auth
import googleapiclient.discovery


def save_to_google_drive(creds, spreadsheet_id):
    """Save a Google Sheet to a specific Google Drive folder."""
    drive_service = googleapiclient.discovery.build("drive", "v3", credentials=creds)

    # hardcoded
    GOOGLE_DRIVE_ID_FOR_BENCHMARK = "190RlobXLLXlCHpAACkSW157UFLcuTNNT"
    res = (
        drive_service.files()
        .update(
            fileId=spreadsheet_id,
            body={},
            addParents=GOOGLE_DRIVE_ID_FOR_BENCHMARK,
            removeParents="root",
            supportsAllDrives=True,
        )
        .execute()
    )
    logging.info(f"saving drive result: {res}")
    return res


def create_gsheet(creds, title, fields, data, wrap_requests=[]):
    """Create a new Google Sheet with the specified data."""
    service = googleapiclient.discovery.build("sheets", "v4", credentials=creds)
    spreadsheet = (
        service.spreadsheets()
        .create(
            body={"properties": {"title": title}},
            fields="spreadsheetId",
        )
        .execute()
    )
    spreadsheet_id = spreadsheet.get("spreadsheetId")

    save_to_google_drive(creds, spreadsheet_id)

    range = "Sheet1"
    value_range_body = {"values": data}
    request = (
        service.spreadsheets()
        .values()
        .update(
            spreadsheetId=spreadsheet_id,
            range=range,
            valueInputOption="RAW",
            body=value_range_body,
        )
    )
    response = request.execute()

    return response, spreadsheet_id


def create_and_save_gsheet(title, fields, data, wrap_requests=[]):
    """Create and save a Google Sheet with the specified data."""
    try:
        SCOPES = [
            "https://www.googleapis.com/auth/spreadsheets",
            "https://www.googleapis.com/auth/drive",
        ]
        creds = google.oauth2.service_account.Credentials.from_service_account_file(
            ".credentials/google_service_account_key.json", scopes=SCOPES
        )

        _response, spreadsheet_id = create_gsheet(creds, title, fields, data)
        return {
            "google_sheet_url": f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}/edit"
        }
    except googleapiclient.errors.HttpError as error:
        print(f"Failed to generate google sheet due to: {error}")
        return error
