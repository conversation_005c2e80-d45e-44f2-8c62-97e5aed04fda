# Constants for unit conversions and measurements
EMU_PER_PT = 914400.0 / 72.0  # 1 PT = 914400 EMU / 72
DEFAULT_DPI = 96  # Standard screen DPI
DEFAULT_FONT_SIZE_PT = 12.0  # Default font size in points
BATCH_SIZE = 450  # Maximum number of requests per batch update
DEFAULT_FONT_FAMILY = "Arial"


# Helper functions for unit conversions
def emu_to_pt(emu: float) -> float:
    """Convert EMU (English Metric Unit) to points."""
    return emu / EMU_PER_PT


def pt_to_emu(pt: float) -> float:
    """Convert points to EMU (English Metric Unit)."""
    return pt * EMU_PER_PT


def pt_to_px(pt: float, dpi: int = DEFAULT_DPI) -> float:
    """Convert points to pixels using the specified DPI."""
    return pt * dpi / 72.0


def px_to_pt(px: float, dpi: int = DEFAULT_DPI) -> float:
    """Convert pixels to points using the specified DPI."""
    return px * 72.0 / dpi


def emu_to_px(emu: float, dpi: int = DEFAULT_DPI) -> float:
    """Convert EMU to pixels using the specified DPI."""
    return pt_to_px(emu_to_pt(emu), dpi)


def px_to_emu(px: float, dpi: int = DEFAULT_DPI) -> float:
    """Convert pixels to EMU using the specified DPI."""
    return pt_to_emu(px_to_pt(px, dpi))
