import logging
from datetime import datetime

from celery import Celery
from celery.result import AsyncResult
from django.db.models import Prefetch
from django.utils import timezone
from server.celery import app as celery_app

from .models import Campaign, Content, ContentGroup, ContentVariation, Playbook
from .shared_definitions.protobuf.gen.action_define_pb2 import (
    ActionStatusStats,
    GenerationStatus,
    GenerationStatusType,
)


class ContentGenStatusUpdater:
    @staticmethod
    def mark_content_gen_not_started(content, is_save=True):
        content.content_status["gen_status"] = {
            "status": "NOT_STARTED",
            "update_time": timezone.now().isoformat(),
        }
        if is_save:
            Content.objects.filter(pk=content.id).update(
                content_status=content.content_status
            )

    @staticmethod
    def mark_content_gen_finished(content, is_save=True):
        content.content_status["gen_status"].update(
            {
                "status": "FINISHED",
                "update_time": timezone.now().isoformat(),
            }
        )
        if is_save:
            Content.objects.filter(pk=content.id).update(
                content_status=content.content_status
            )

    @staticmethod
    def mark_content_gen_error(content, error_message, is_save=True):
        content.content_status["gen_status"].update(
            {
                "status": "ERROR",
                "error": error_message,
                "update_time": timezone.now().isoformat(),
            }
        )
        if is_save:
            Content.objects.filter(pk=content.id).update(
                content_status=content.content_status
            )

    @staticmethod
    def batch_mark_content_gen_error(content_ids, error_message):
        contents = Content.objects.filter(pk__in=content_ids).filter(
            Q(content_status__gen_status__status="QUEUED")
            | Q(content_status__gen_status__status="IN_PROGRESS")
        )
        for content in contents:
            ContentGenStatusUpdater.mark_content_gen_error(
                content, error_message, is_save=False
            )
        Content.objects.bulk_update(contents, ["content_status"])

    @staticmethod
    def mark_content_gen_queued(content, task_id, is_save=True):
        content.content_status["gen_status"].update(
            {
                "status": "QUEUED",
                "task_id": task_id,
                "update_time": timezone.now().isoformat(),
            }
        )
        if is_save:
            Content.objects.filter(pk=content.id).update(
                content_status=content.content_status
            )

    @staticmethod
    def batch_mark_content_gen_queued(contents, task_id):
        for content in contents:
            ContentGenStatusUpdater.mark_content_gen_queued(
                content, task_id, is_save=False
            )
        Content.objects.bulk_update(contents, ["content_status"])

    @staticmethod
    def mark_content_gen_in_progress(content, task_id, is_save=True):
        content.content_status["gen_status"].update(
            {
                "status": "IN_PROGRESS",
                "task_id": task_id,
                "update_time": timezone.now().isoformat(),
            }
        )
        if is_save:
            Content.objects.filter(pk=content.id).update(
                content_status=content.content_status
            )

    @staticmethod
    def mark_content_gen_terminated(content, is_save=True):
        content.content_status["gen_status"].update(
            {
                "status": "NOT_STARTED",
                "comment": "Terminated by user",
                "update_time": timezone.now().isoformat(),
            }
        )
        if is_save:
            Content.objects.filter(pk=content.id).update(
                content_status=content.content_status
            )


class GenStatusUpdater:
    TIMEOUT_THRESHOLD_RUNNING = 1800  # 30 minutes
    TIMEOUT_THRESHOLD_WAITING = 3600 * 5  # 5 hours

    def __init__(self) -> None:
        self.kill_cache = set()

    def get_content_gen_status(self, content):
        content_status = content.content_status.get(
            "gen_status", {"status": "NOT_STARTED"}
        )

        if "status" not in content_status:
            logging.error(
                f'content {content.id} does not have status with gen_status {content.content_status.get("gen_status")}'
            )
            content_status["status"] = "NOT_STARTED"

        # check task_status first
        if (
            content_status["status"] in ("QUEUED", "IN_PROGRESS")
            and "task_id" in content_status
        ):
            task_id = content_status["task_id"]
            result = AsyncResult(task_id)
            if result.status == "FAILURE":
                content_status["status"] = "ERROR"
                content_status["error"] = (
                    f"Content {content.id} failed to generate due to task {task_id} failure: {result.result}"
                )
            elif result.status == "SUCCESS":
                # re-fetch the status since there could be race conditions
                # reload content from database
                content = Content.objects.get(pk=content.id)
                if content.content_status["gen_status"]["status"] in (
                    "QUEUED",
                    "IN_PROGRESS",
                ):
                    # should be an exception but not that severe
                    logging.error(
                        f'Error: content {content.id} has status {content_status["status"]} but task {task_id} is SUCCESS'
                    )
                    content_status["status"] = "ERROR"
                    content_status["error"] = (
                        f"Content {content.id} failed to generate due to task {task_id} but task success"
                    )
            elif result.status == "REVOKED":
                content_status["status"] = "NOT_STARTED"
                content_status["comment"] = (
                    f"Content {content.id} is terminated since task {task_id} is revoked"
                )

        if (
            content_status["status"] in ("QUEUED", "IN_PROGRESS")
            and "update_time" in content_status
        ):
            current_time = timezone.now()
            time_delta = (
                current_time - datetime.fromisoformat(content_status["update_time"])
            ).total_seconds()
            if (
                content_status["status"] == "IN_PROGRESS"
                and time_delta >= GenStatusUpdater.TIMEOUT_THRESHOLD_RUNNING
                or content_status["status"] == "QUEUED"
                and time_delta >= GenStatusUpdater.TIMEOUT_THRESHOLD_WAITING
            ):
                task_id = None
                if (
                    "task_id" in content_status
                    and content_status["task_id"] not in self.kill_cache
                ):
                    task_id = content_status["task_id"]
                    try:
                        celery_app.control.revoke(task_id, terminate=True)
                    except Exception as e:
                        logging.error(f"Failed to revoke task {task_id}: {e}")
                    else:
                        logging.info(f"Revoked task {task_id}")
                        self.kill_cache.add(task_id)

                status = content_status["status"]
                error_msg = f"Timeoout error: Content {content.id} has been {status} for {time_delta // 60} minutes. Task {task_id} is revoked."
                # logging purpose, since IN_PROGRESS is the one running
                if content_status["status"] == "IN_PROGRESS":
                    logging.error(error_msg)

                content_status = {
                    "status": "ERROR",
                    "error": error_msg,
                    "update_time": timezone.now().isoformat(),
                }

                content.content_status["gen_status"] = content_status
                Content.objects.filter(pk=content.id).update(
                    content_status=content.content_status
                )
        return content_status

    def reset_content_gen_status(self, content):
        content.content_status["gen_status"] = {
            "status": "NOT_STARTED",
        }
        Content.objects.filter(pk=content.id).update(
            content_status=content.content_status
        )

    def get_content_group_gen_status(self, content_group):
        contents = Content.objects.filter(content_group=content_group)
        if not contents:  # no content in the group
            if not content_group.content_group_status:
                content_group.content_group_status = {}
            content_group.content_group_status["gen_status"] = {
                "status": "NOT_STARTED",
                "contents": {},
            }
            ContentGroup.objects.filter(pk=content_group.id).update(
                content_group_status=content_group.content_group_status
            )
            return content_group.content_group_status["gen_status"]

        prev_status = content_group.content_group_status.get("gen_status", {}).get(
            "status", None
        )

        content_group_status_data = {"contents": {}}

        all_content_finished = True
        any_content_in_process = False
        has_error = False
        errors = []

        for content in contents:
            content_status = self.get_content_gen_status(content)
            content_group_status_data["contents"][content.id] = content_status

            if content_status["status"] != "FINISHED":
                all_content_finished = False

            if content_status["status"] in ("IN_PROGRESS", "QUEUED"):
                any_content_in_process = True
            elif content_status["status"] == "ERROR":
                has_error = True
                errors.append(
                    f'Content {content.id} has error: {content_status["error"]}'
                )

        if any_content_in_process:
            content_group_status_data["status"] = "IN_PROGRESS"
        elif all_content_finished:
            content_group_status_data["status"] = "FINISHED"
        elif has_error:
            content_group_status_data["status"] = "ERROR"
        else:
            content_group_status_data["status"] = "NOT_STARTED"
        if errors:
            content_group_status_data["error"] = errors

        if content_group is not None and content_group.content_group_status is None:
            logging.warning(
                f"content group {content_group.id} does not have content_group_status"
            )
            content_group.content_group_status = {}
        content_group.content_group_status["gen_status"] = content_group_status_data
        ContentGroup.objects.filter(pk=content_group.id).update(
            content_group_status=content_group.content_group_status
        )

        return content_group_status_data

    def get_content_group_gen_status_v3(self, content_group):
        cnts_succ = 0
        cnts_fail = 0
        cnts_running = 0
        cnts_not_started = 0

        contents = Content.objects.filter(content_group=content_group)
        content_group_gen_status = {}

        for content in contents:
            content_status = self.get_content_gen_status(content)
            targets = content.content_params.get("targets", {})
            if len(targets) > 1:
                logging.error(
                    f"content {content.id} has {len(targets)} targets and expected 0 or 1"
                )
                continue
            if not targets:
                target_name = content_group.content_group_name
            else:
                target_key, target_value = next(iter(targets.items()))
                target_name = f"{target_key}:{target_value}"

            # Map Django status to GenerationStatusType
            status_type = {
                "FINISHED": GenerationStatusType.GENERATION_STATUS_TYPE_COMPLETE,
                "ERROR": GenerationStatusType.GENERATION_STATUS_TYPE_FAILED,
                "IN_PROGRESS": GenerationStatusType.GENERATION_STATUS_TYPE_IN_PROGRESS,
                "QUEUED": GenerationStatusType.GENERATION_STATUS_TYPE_IN_PROGRESS,
                "NOT_STARTED": GenerationStatusType.GENERATION_STATUS_TYPE_UNSPECIFIED,
            }.get(
                content_status["status"],
                GenerationStatusType.GENERATION_STATUS_TYPE_UNSPECIFIED,
            )

            content_group_gen_status[content.id] = GenerationStatus(
                status_type=status_type, target_name=target_name
            )
            if "error" in content_status and content_status["error"]:
                content_group_gen_status[content.id].error_message = content_status[
                    "error"
                ]

            # Update counters
            if content_status["status"] == "FINISHED":
                cnts_succ += 1
            elif content_status["status"] == "ERROR":
                cnts_fail += 1
            elif content_status["status"] in ("IN_PROGRESS", "QUEUED"):
                cnts_running += 1
            elif content_status["status"] == "NOT_STARTED":
                cnts_not_started += 1

        return {
            "stats": ActionStatusStats(
                cnts_not_started=cnts_not_started,
                cnts_succ=cnts_succ,
                cnts_fail=cnts_fail,
                cnts_running=cnts_running,
            ),
            "content_group_gen_status": content_group_gen_status,
        }

    def reset_content_group_gen_status(self, content_group):
        if not content_group.content_set.all():  # no content in the group
            if not content_group.content_group_status:
                content_group.content_group_status = {}
            content_group.content_group_status["gen_status"] = {
                "status": "NOT_STARTED",
                "contents": {},
            }
            ContentGroup.objects.filter(pk=content_group.id).update(
                content_group_status=content_group.content_group_status
            )

        for content in content_group.content_set.all():  # already prefetched
            self.reset_content_gen_status(content)

    # always fetch
    def get_campaign_gen_status(self, campaign):
        # default value
        status_data = {
            "status": "NOT_STARTED",
            "content_groups": {},
        }

        content_groups = ContentGroup.objects.filter(campaign=campaign)
        if not content_groups:
            if (
                "gen_status" in campaign.campaign_status
                and campaign.campaign_status["gen_status"] == status_data
            ):
                return status_data
            campaign.campaign_status["gen_status"] = status_data
            Campaign.objects.filter(pk=campaign.id).update(
                campaign_status=campaign.campaign_status
            )
            return status_data

        all_finished = True
        any_in_process = False
        any_has_error = False
        errors = []

        for content_group in content_groups:
            content_group_status_data = self.get_content_group_gen_status(content_group)
            status_data["content_groups"][content_group.id] = content_group_status_data

            if content_group_status_data["status"] == "ERROR":
                any_has_error = True
                content_group_error = content_group_status_data.get(
                    "error", "unknown error"
                )
                errors.append(
                    f"Content group {content_group.id} has error: {content_group_error}"
                )
            if content_group_status_data["status"] != "FINISHED":
                all_finished = False
            if content_group_status_data["status"] == "IN_PROGRESS":
                any_in_process = True

        if any_in_process:
            status_data["status"] = "IN_PROGRESS"
        elif any_has_error:
            status_data["status"] = "ERROR"
        elif all_finished:
            status_data["status"] = "FINISHED"
        else:
            status_data["status"] = "NOT_STARTED"
        if errors:
            status_data["error"] = errors

        campaign.campaign_status["gen_status"] = status_data
        Campaign.objects.filter(pk=campaign.id).update(
            campaign_status=campaign.campaign_status
        )
        return status_data
