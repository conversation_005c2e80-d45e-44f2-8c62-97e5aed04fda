import logging
import random
import traceback
from datetime import datetime, timedelta

from django.db.models import Count, F, <PERSON>loat<PERSON>ield, Q, Sum, Value
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import Cast, Coalesce
from django.utils import timezone

from .logger import tofu_axiom_logger
from .models import Content, OffsiteEventLogs
from .paragon_wrapper import ParagonWrapper
from .shared_types import ContentType


class ContentGroupMetricPuller:
    def __init__(self, content_group_instance):
        self._content_group_instance = content_group_instance
        self._paragon_wrapper = None

    @property
    def paragon_wrapper(self):
        if not self._paragon_wrapper:
            self._paragon_wrapper = ParagonWrapper(
                self._content_group_instance.campaign.playbook
            )
        return self._paragon_wrapper

    def _pull_hubspot_email(self, export_email_id):
        metrics = {}
        try:
            metrics = {
                "export_email_id": export_email_id,
                "sync_from": "hubspot",
                "stats": self.paragon_wrapper.get_hubspot_email_stats(export_email_id),
            }
            tofu_axiom_logger.log_axiom(
                event_type="metric_tiles",
                success="success",
                source="hubspot",
                campaign_id=self._content_group_instance.campaign.id,
                content_group_id=self._content_group_instance.id,
                email_id=export_email_id,
                metrics=metrics["stats"],
            )
        except Exception as e:
            logging.error(
                f"Error getting stats from Paragon for hubspot email {export_email_id}: {str(e)}"
            )
            tofu_axiom_logger.log_axiom(
                event_type="metric_tiles",
                success="failure",
                source="hubspot",
                campaign_id=self._content_group_instance.campaign.id,
                content_group_id=self._content_group_instance.id,
                email_id=export_email_id,
                error=str(e),
            )
        return metrics

    def _pull_hubspot_landing_page(self, export_landing_page_id):
        metrics = {}
        try:
            metrics = {
                "export_landing_page_id": export_landing_page_id,
                "sync_from": "hubspot",
                "stats": self.paragon_wrapper.get_hubspot_landing_page_stats(
                    export_landing_page_id
                ),
            }
            now = datetime.now()
            start_1_day = (now - timedelta(days=1)).strftime("%Y%m%d")
            start_7_day = (now - timedelta(days=7)).strftime("%Y%m%d")
            start_30_day = (now - timedelta(days=30)).strftime("%Y%m%d")

            try:
                stats_1_day = self.paragon_wrapper.get_hubspot_landing_page_stats(
                    export_landing_page_id,
                    start_from=start_1_day,
                )
                stats_7_day = self.paragon_wrapper.get_hubspot_landing_page_stats(
                    export_landing_page_id,
                    start_from=start_7_day,
                )
                stats_30_day = self.paragon_wrapper.get_hubspot_landing_page_stats(
                    export_landing_page_id,
                    start_from=start_30_day,
                )
                metrics["stats_1_day"] = stats_1_day.get("stats", {})
                metrics["stats_7_day"] = stats_7_day.get("stats", {})
                metrics["stats_30_day"] = stats_30_day.get("stats", {})
            except Exception as e:
                logging.error(
                    f"Error getting stats from Paragon for hubspot landing page {export_landing_page_id} for 1, 7, 30 days: {str(e)}\n{traceback.format_exc()}"
                )

            tofu_axiom_logger.log_axiom(
                event_type="metric_tiles",
                success="success",
                source="hubspot",
                campaign_id=self._content_group_instance.campaign.id,
                content_group_id=self._content_group_instance.id,
                landing_page_id=export_landing_page_id,
                metrics=metrics["stats"],
            )
        except PermissionError as e:
            logging.error(
                f"Permission error getting stats from Paragon for hubspot landing page {export_landing_page_id}: {str(e)}"
            )
            tofu_axiom_logger.log_axiom(
                event_type="metric_tiles",
                success="failure",
                source="hubspot",
                campaign_id=self._content_group_instance.campaign.id,
                content_group_id=self._content_group_instance.id,
                landing_page_id=export_landing_page_id,
                error=str(e),
            )
        except Exception as e:
            logging.error(
                f"Error getting stats from Paragon for hubspot landing page {export_landing_page_id}: {str(e)}\n{traceback.format_exc()}"
            )
            tofu_axiom_logger.log_axiom(
                event_type="metric_tiles",
                success="failure",
                source="hubspot",
                campaign_id=self._content_group_instance.campaign.id,
                content_group_id=self._content_group_instance.id,
                landing_page_id=export_landing_page_id,
                error=str(e),
            )
        return metrics

    def _pull_marketo_email(self, export_email_id):
        metrics = {}
        try:
            (
                total_stats,
                stats_1_day,
                stats_7_day,
                stats_30_day,
            ) = self.paragon_wrapper.get_marketo_email_stats(export_email_id)
            metrics = {
                "export_email_id": export_email_id,
                "sync_from": "marketo",
                "stats": total_stats,
                "stats_1_day": stats_1_day,
                "stats_7_day": stats_7_day,
                "stats_30_day": stats_30_day,
            }
            tofu_axiom_logger.log_axiom(
                event_type="metric_tiles",
                success="success",
                source="marketo",
                campaign_id=self._content_group_instance.campaign.id,
                content_group_id=self._content_group_instance.id,
                email_id=export_email_id,
                metrics=metrics["stats"],
            )
        except Exception as e:
            logging.error(
                f"Error getting stats from Paragon for marketo email {export_email_id}: {str(e)} with traceback: {traceback.format_exc()}"
            )
            tofu_axiom_logger.log_axiom(
                event_type="metric_tiles",
                success="failure",
                source="marketo",
                campaign_id=self._content_group_instance.campaign.id,
                content_group_id=self._content_group_instance.id,
                email_id=export_email_id,
                error=str(e),
            )
        return metrics

    def _pull_tofujs_landing_page(self):
        content_group_page_views = OffsiteEventLogs.objects.filter(
            campaign_id=self._content_group_instance.campaign.id,
            event_type="page_view",
        ).filter(
            # Match either content_id from the content group OR direct content_group_id reference
            (
                Q(
                    content_id__in=Content.objects.filter(
                        content_group=self._content_group_instance
                    ).values_list("id", flat=True)
                )
                | Q(content_id=None, content_group_id=self._content_group_instance.id)
            )
        )

        page_views = content_group_page_views.count()

        if not page_views:  # page_views exist
            return {}

        content_group_page_views = content_group_page_views.annotate(
            time_spent=Coalesce(
                Cast(
                    KeyTextTransform("time_spent", "payload"), output_field=FloatField()
                ),
                Value(1.0),
                output_field=FloatField(),
            )
        )

        total_time_spent = content_group_page_views.aggregate(
            total_time_spent=Sum("time_spent")
        )["total_time_spent"]

        per_target_views = (
            content_group_page_views.values("tofu_slug")
            .annotate(count=Count("*"), time_spent=Sum("time_spent"))
            .order_by(F("tofu_slug").asc(nulls_first=True))
        )

        # Current time
        now = timezone.now()

        # Time deltas for 1 day, 7 days, and 30 days
        one_day_ago = now - timedelta(days=1)
        seven_days_ago = now - timedelta(days=7)
        thirty_days_ago = now - timedelta(days=30)

        # Count for past 1 day
        count_last_1_day = content_group_page_views.filter(created_at__gte=one_day_ago)
        count_last_1_day_stats = count_last_1_day.aggregate(
            count=Count("session_id"), time_spent=Sum("time_spent")
        )

        # Count for past 7 days
        count_last_7_days = content_group_page_views.filter(
            created_at__gte=seven_days_ago
        )
        count_last_7_days_stats = count_last_7_days.aggregate(
            count=Count("session_id"), time_spent=Sum("time_spent")
        )

        # Count for past 30 days
        count_last_30_days = content_group_page_views.filter(
            created_at__gte=thirty_days_ago
        )
        count_last_30_days_stats = count_last_30_days.aggregate(
            count=Count("session_id"), time_spent=Sum("time_spent")
        )

        count_last_1_day_per_target = (
            count_last_1_day.values("tofu_slug")
            .annotate(count=Count("*"), time_spent=Sum("time_spent"))
            .order_by(F("tofu_slug").asc(nulls_first=True))
        )
        count_last_7_days_per_target = (
            count_last_7_days.values("tofu_slug")
            .annotate(count=Count("*"), time_spent=Sum("time_spent"))
            .order_by(F("tofu_slug").asc(nulls_first=True))
        )
        count_last_30_days_per_target = (
            count_last_30_days.values("tofu_slug")
            .annotate(count=Count("*"), time_spent=Sum("time_spent"))
            .order_by(F("tofu_slug").asc(nulls_first=True))
        )

        metrics = {
            "sync_from": "tofujs",
            "stats": {
                "rawViews": page_views,
                "totalTimeSpent": total_time_spent,
                "perTargetViews": per_target_views,
            },
            "stats_1_day": {
                "rawViews": count_last_1_day_stats["count"],
                "totalTimeSpent": count_last_1_day_stats["time_spent"],
                "perTargetViews": count_last_1_day_per_target,
            },
            "stats_7_day": {
                "rawViews": count_last_7_days_stats["count"],
                "totalTimeSpent": count_last_7_days_stats["time_spent"],
                "perTargetViews": count_last_7_days_per_target,
            },
            "stats_30_day": {
                "rawViews": count_last_30_days_stats["count"],
                "totalTimeSpent": count_last_30_days_stats["time_spent"],
                "perTargetViews": count_last_30_days_per_target,
            },
        }
        return metrics

    def pull(self):
        metric_tiles = {}
        if not self._content_group_instance.content_group_params:  # legacy data
            return metric_tiles

        export_response = self._content_group_instance.content_group_params.get(
            "export_response", {}
        )
        content_type = self._content_group_instance.content_group_params.get(
            "content_type", ""
        )
        if content_type in (ContentType.EmailMarketing, ContentType.EmailSDR):
            # email metrics
            if "hubspot" in export_response:
                export_email_id = export_response["hubspot"].get("id", "")
                if export_email_id:
                    return self._pull_hubspot_email(export_email_id)
            if "marketo" in export_response:
                export_email_id = export_response["marketo"].get("id", "")
                if export_email_id:
                    return self._pull_marketo_email(export_email_id)
        elif content_type == ContentType.LandingPage:
            # landing page metrics
            tofujs_metrics = self._pull_tofujs_landing_page()
            if tofujs_metrics:
                return tofujs_metrics

            if "hubspot" in export_response:
                export_landing_page_id = export_response["hubspot"].get("id", "")
                if export_landing_page_id:
                    return self._pull_hubspot_landing_page(export_landing_page_id)

        return metric_tiles

    def gen_fake_metrics(self, target_size):
        if not self._content_group_instance.content_group_params:
            return {}

        content_type = self._content_group_instance.content_group_params.get(
            "content_type", ""
        )
        if content_type in (ContentType.EmailMarketing, ContentType.EmailSDR):

            def gen_fake_email_stats():
                cnt_sent = target_size
                cnt_delivered = target_size
                cnt_opened = int(target_size * random.uniform(0.25, 0.6))
                cnt_clicked = int(cnt_opened * random.uniform(0.3, 0.5))
                cnt_unsubscribed = int(cnt_opened * random.uniform(0.00, 0.05))
                return {
                    "counters": {
                        "sent": cnt_sent,
                        "delivered": cnt_delivered,
                        "opened": cnt_opened,
                        "clicked": cnt_clicked,
                        "unsubscribed": cnt_unsubscribed,
                    },
                    "ratios": {
                        "openratio": (
                            round(cnt_opened / cnt_sent * 100, 2) if cnt_sent > 0 else 0
                        ),
                        "clickratio": (
                            round(cnt_clicked / cnt_delivered * 100, 2)
                            if cnt_delivered > 0
                            else 0
                        ),
                        "clickthroughratio": (
                            round(cnt_clicked / cnt_opened * 100, 2)
                            if cnt_opened > 0
                            else 0
                        ),
                        "deliveredratio": (
                            round(cnt_delivered / cnt_sent * 100, 2)
                            if cnt_sent > 0
                            else 0
                        ),
                        "unsubscribedratio": (
                            round(cnt_unsubscribed / cnt_sent * 100, 2)
                            if cnt_sent > 0
                            else 0
                        ),
                    },
                }

            return {
                "sync_from": "fake",
                "stats": gen_fake_email_stats(),
                "stats_1_day": gen_fake_email_stats(),
                "stats_7_day": gen_fake_email_stats(),
                "stats_30_day": gen_fake_email_stats(),
            }
        elif content_type == ContentType.LandingPage:

            def gen_fake_landing_page_stats():
                page_views = int(target_size * random.uniform(0.3, 0.8))
                target_page_views = [
                    {
                        "tofu_slug": f"target-{i}",
                        "count": int(page_views * random.uniform(0.1, 0.5)),
                    }
                    for i in range(1, 6)
                ]
                return {
                    "rawViews": page_views,
                    "perTargetViews": target_page_views,
                }

            return {
                "sync_from": "fake",
                "stats": gen_fake_landing_page_stats(),
                "stats_1_day": gen_fake_landing_page_stats(),
                "stats_7_day": gen_fake_landing_page_stats(),
                "stats_30_day": gen_fake_landing_page_stats(),
            }

        return {}
