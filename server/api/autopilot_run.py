import datetime
import logging
from dataclasses import asdict, dataclass
from datetime import <PERSON><PERSON><PERSON>
from enum import Enum
from typing import List

from django.db.models import Q
from django.utils import timezone

from .models import AutopilotRun, Campaign, Content


class AutopilotStatus(Enum):
    DONE = "Done"
    IN_PROGRESS = "In progress"
    ERROR = "Error"
    UNKNOWN = "Unknown"

    def __str__(self):
        return self.value


@dataclass
class AutopilotStep:
    id: str
    status: str
    detail: str


@dataclass
class AutopilotTask:
    id: str
    name: str
    status: str
    steps: List[AutopilotStep]


@dataclass
class AutopilotRunObject:
    id: str
    timestamp: datetime
    status: str
    message: str
    tasks: List[AutopilotTask]

    def to_dict(self):
        # Convert datetime to string and nested objects to dicts
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "status": self.status,
            "message": self.message,
            "tasks": [asdict(task) for task in self.tasks],
        }


class AutopilotSessionProcessor:
    def __init__(self, session_runs: List):
        self.session_runs = session_runs
        self.timestamp = None
        self.status = None
        self.message = None
        self.target_info_group_name = None
        self.tasks = []
        self.autopilot_visibility = {}

    def process(self):
        for run in self.session_runs:
            self._process_run(run)
        self.status, self.message = self.build_status_message()
        if self.status == "Unknown":
            return
        self.tasks = self.build_tasks()

    def _process_run(self, run: AutopilotRun) -> None:
        """Process a single autopilot run based on its action type."""
        action_processors = {
            AutopilotRun.AutopilotActionType.CRM_SYNC: self._process_crm_sync,
            AutopilotRun.AutopilotActionType.TARGET_CREATION: self._process_target_creation,
            AutopilotRun.AutopilotActionType.ALL_TARGETS_CREATION: self._process_all_targets_creation,
            AutopilotRun.AutopilotActionType.CONTENT_GENERATION: self._process_content_generation,
            AutopilotRun.AutopilotActionType.EXPORT: self._process_export,
        }

        processor = action_processors.get(run.autopilot_action_type)
        if processor:
            processor(run)

    def _process_crm_sync(self, run: AutopilotRun) -> None:
        """Process CRM sync action type."""
        self.timestamp = run.created_at
        self.target_info_group_name = run.target_info_group.target_info_group_key

    def _process_target_creation(self, run: AutopilotRun) -> None:
        """Process target creation action type."""
        if not self.timestamp:
            # case where crm sync is not run.
            self.timestamp = run.created_at
        if not self.target_info_group_name:
            self.target_info_group_name = run.target_info_group.target_info_group_key
        if "target_creation" not in self.autopilot_visibility:
            self.autopilot_visibility["target_creation"] = {}

        done_targets = run.status.get("Done", [])
        in_progress_targets = run.status.get("In Progress", [])
        error_record_data = run.status.get("Error", [])

        # remove any targets present in both done_targets and in_progress_targets from in_progress_targets.
        in_progress_targets = [
            target for target in in_progress_targets if target not in done_targets
        ]
        self.autopilot_visibility["target_creation"]["Done"] = done_targets
        self.autopilot_visibility["target_creation"][
            "In Progress"
        ] = in_progress_targets
        self.autopilot_visibility["target_creation"]["Error"] = error_record_data

    def _process_all_targets_creation(self, run: AutopilotRun) -> None:
        """Process all targets creation action type."""
        if not self.timestamp:
            # case where crm sync is not run.
            self.timestamp = run.created_at
        if not self.target_info_group_name:
            self.target_info_group_name = run.target_info_group.target_info_group_key
        if "all_targets_creation" not in self.autopilot_visibility:
            self.autopilot_visibility["all_targets_creation"] = {}

        done_targets = run.status.get("Done", [])
        in_progress_targets = run.status.get("In Progress", [])
        error_record_data = run.status.get("Error", [])

        # remove any targets present in both done_targets and in_progress_targets from in_progress_targets.
        in_progress_targets = [
            target for target in in_progress_targets if target not in done_targets
        ]
        self.autopilot_visibility["all_targets_creation"]["Done"] = done_targets
        self.autopilot_visibility["all_targets_creation"][
            "In Progress"
        ] = in_progress_targets
        self.autopilot_visibility["all_targets_creation"]["Error"] = error_record_data

    def _process_content_generation(self, run: AutopilotRun) -> None:
        """Process content generation action type."""
        if not self.timestamp:
            # case where crm sync is not run.
            self.timestamp = run.created_at
        if not self.target_info_group_name:
            self.target_info_group_name = run.target_info_group.target_info_group_key
        if "content_generation" not in self.autopilot_visibility:
            self.autopilot_visibility["content_generation"] = {}
        done_contents = run.status.get("Done", [])
        if not done_contents:
            done_contents = []
        in_progress_contents = run.status.get("In Progress", [])
        if not in_progress_contents:
            in_progress_contents = []
        error_contents = run.status.get("Error", [])
        if not error_contents:
            error_contents = []

        # filter done contents from in progress.
        in_progress_contents = [
            content for content in in_progress_contents if content not in done_contents
        ]
        all_content_ids = done_contents + in_progress_contents + error_contents
        if not all_content_ids:
            return
        content_objects = {
            c.id: c
            for c in Content.objects.filter(id__in=all_content_ids).select_related(
                "content_group"
            )
        }
        # for each content in done_contents, use its content_group_id as the key.
        for content in done_contents:
            content_obj = content_objects.get(content)
            if not content_obj:
                continue
            content_group_name = content_obj.content_group.content_group_name
            if (
                content_group_name
                not in self.autopilot_visibility["content_generation"]
            ):
                self.autopilot_visibility["content_generation"][content_group_name] = {
                    "Done": [],
                    "In Progress": [],
                    "Error": [],
                }
            self.autopilot_visibility["content_generation"][content_group_name][
                "Done"
            ].append(content)

        # do the same for in_progress_contents.
        for content in in_progress_contents:
            content_obj = content_objects.get(content)
            if not content_obj:
                continue
            content_group_name = content_obj.content_group.content_group_name
            if (
                content_group_name
                not in self.autopilot_visibility["content_generation"]
            ):
                self.autopilot_visibility["content_generation"][content_group_name] = {
                    "Done": [],
                    "In Progress": [],
                    "Error": [],
                }
            self.autopilot_visibility["content_generation"][content_group_name][
                "In Progress"
            ].append(content)

        # do the same for error_contents.
        for content in error_contents:
            content_obj = content_objects.get(content)
            if not content_obj:
                continue
            content_group_name = content_obj.content_group.content_group_name
            if (
                content_group_name
                not in self.autopilot_visibility["content_generation"]
            ):
                self.autopilot_visibility["content_generation"][content_group_name] = {
                    "Done": [],
                    "In Progress": [],
                    "Error": [],
                }
            self.autopilot_visibility["content_generation"][content_group_name][
                "Error"
            ].append(content)

    def _process_export(self, run: AutopilotRun) -> None:
        """Process export action type."""
        if not self.timestamp:
            # case where crm sync is not run.
            self.timestamp = run.created_at
        if not self.target_info_group_name:
            self.target_info_group_name = run.target_info_group.target_info_group_key
        if "export" not in self.autopilot_visibility:
            self.autopilot_visibility["export"] = {}
        done_contents = run.status.get("Done", [])
        if not done_contents:
            done_contents = []
        error_contents = run.status.get("Error", [])
        if not error_contents:
            error_contents = []
        all_content_ids = done_contents + error_contents
        if not all_content_ids:
            return
        content_objects = {
            c.id: c
            for c in Content.objects.filter(id__in=all_content_ids).select_related(
                "content_group"
            )
        }

        # for each content in done_contents, use its content_group_id as the key.
        for content in done_contents:
            content_obj = content_objects.get(content)
            if not content_obj:
                continue
            content_group_name = content_obj.content_group.content_group_name
            if content_group_name not in self.autopilot_visibility["export"]:
                self.autopilot_visibility["export"][content_group_name] = {
                    "Done": [],
                    "In Progress": [],
                    "Error": [],
                }
            self.autopilot_visibility["export"][content_group_name]["Done"].append(
                content
            )

        # do the same for error_contents.
        for content in error_contents:
            content_obj = content_objects.get(content)
            if not content_obj:
                continue
            content_group_name = content_obj.content_group.content_group_name
            if content_group_name not in self.autopilot_visibility["export"]:
                self.autopilot_visibility["export"][content_group_name] = {
                    "Done": [],
                    "In Progress": [],
                    "Error": [],
                }
            self.autopilot_visibility["export"][content_group_name]["Error"].append(
                content
            )

    def build_status_message(self):
        status = "Unknown"
        message = "Unknown status"
        status_cnts = self.check_status_counts()
        if status_cnts["Error"] > 0:
            status = "Error"
            message = f"{status_cnts['Error']} tasks failed"
        elif status_cnts["In Progress"] > 0:
            status = "In Progress"
            message = f"{status_cnts['In Progress']} tasks pending"
        elif status_cnts["Done"] > 0:
            status = "Success"
            message = "All tasks completed"
        else:
            status = "Unknown"
            message = "Unknown status"
        return status, message

    def check_status_counts(self):
        status_cnts = {
            "Error": 0,
            "In Progress": 0,
            "Done": 0,
        }

        target_task_keys = [
            "all_targets_creation",
            "target_creation",
        ]
        content_task_keys = [
            "content_generation",
            "export",
        ]
        for task_key in target_task_keys:
            if task_key in self.autopilot_visibility:
                status_cnts["Done"] += len(
                    self.autopilot_visibility[task_key].get("Done", [])
                )
                status_cnts["In Progress"] += len(
                    self.autopilot_visibility[task_key].get("In Progress", [])
                )
                status_cnts["Error"] += len(
                    self.autopilot_visibility[task_key].get("Error", [])
                )

        for task_key in content_task_keys:
            if task_key in self.autopilot_visibility:
                for content_group_id in self.autopilot_visibility[task_key]:
                    status_cnts["Done"] += len(
                        self.autopilot_visibility[task_key][content_group_id].get(
                            "Done", []
                        )
                    )
                    status_cnts["In Progress"] += len(
                        self.autopilot_visibility[task_key][content_group_id].get(
                            "In Progress", []
                        )
                    )
                    status_cnts["Error"] += len(
                        self.autopilot_visibility[task_key][content_group_id].get(
                            "Error", []
                        )
                    )
        return status_cnts

    def build_tasks(self):
        """
        tasks: [
              {
                id: 'task-1',
                name: '15 new targets added to TOFU | Website form',
                status: 'Done',
                steps: [],
              },
              {
                id: 'task-2',
                name: '10 new targets added to campaign from TOFU | Website form',
                status: 'Done',
                steps: [],
              },
              {
                id: 'task-3',
                name: 'Landing page demo',
                status: 'Error',
                steps: [
                  { id: 'step-1', status: 'Not Run', detail: '10 exported' },
                  { id: 'step-2', status: 'Error', detail: '10 added and generated' },
                ],
              },
            ],
        tasks: [
          {
            id: 'task-1',
            name: 'Email - SDR outreach 3 final',
            status: 'Done',
            steps: [
              { id: 'step-1', status: 'Done', detail: '10 exported' },
              { id: 'step-2', status: 'Done', detail: '10 added and generated' },
            ],
          },
          {
            id: 'task-2',
            name: 'Email - SDR outreach 2',
            status: 'Done',
            steps: [
              { id: 'step-1', status: 'Done', detail: '10 exported' },
              { id: 'step-2', status: 'Done', detail: '10 added and generated' },
            ],
          },
          {
            id: 'task-3',
            name: 'Email - SDR outreach 1',
            status: 'Done',
            steps: [
              { id: 'step-1', status: 'Done', detail: '10 exported' },
              { id: 'step-2', status: 'Done', detail: '10 added and generated' },
            ],
          },
        ],
        """
        tasks = []
        target_info_group_name = self.target_info_group_name
        if "all_targets_creation" in self.autopilot_visibility:
            all_targets_creation_run = self.autopilot_visibility["all_targets_creation"]
            num_targets_created_done = len(all_targets_creation_run.get("Done", []))
            num_targets_created_in_progress = len(
                all_targets_creation_run.get("In Progress", [])
            )
            num_targets_created_error = len(all_targets_creation_run.get("Error", []))
            all_targets_creation_name = ""
            if num_targets_created_error > 0:
                all_targets_creation_status = "Error"
                all_targets_creation_name += (
                    f"{num_targets_created_error} targets failed to create. "
                )
            elif num_targets_created_in_progress > 0:
                all_targets_creation_status = "In Progress"
                all_targets_creation_name += (
                    f"{num_targets_created_in_progress} targets in progress. "
                )
            elif num_targets_created_done > 0:
                all_targets_creation_status = "Done"
                all_targets_creation_name += (
                    f"{num_targets_created_done} new targets identified and fetched "
                )
            else:
                all_targets_creation_status = "Unknown"
                all_targets_creation_name += "Unknown status."
            all_targets_creation_task = AutopilotTask(
                id="task-1",
                name=all_targets_creation_name,
                status=all_targets_creation_status,
                steps=[],
            )
            tasks.append(all_targets_creation_task)
        if "target_creation" in self.autopilot_visibility:
            # do the same for target_creation.
            target_creation_run = self.autopilot_visibility["target_creation"]
            num_targets_created_done = len(target_creation_run.get("Done", []))
            num_targets_created_in_progress = len(
                target_creation_run.get("In Progress", [])
            )
            num_targets_created_error = len(target_creation_run.get("Error", []))
            target_creation_name = ""
            if num_targets_created_error > 0:
                target_creation_status = "Error"
                target_creation_name += (
                    f"{num_targets_created_error} targets failed to create. "
                )
            elif num_targets_created_in_progress > 0:
                target_creation_status = "In Progress"
                target_creation_name += (
                    f"{num_targets_created_in_progress} targets in progress. "
                )
            elif num_targets_created_done > 0:
                target_creation_status = "Done"
                target_creation_name += f"{num_targets_created_done} new targets added to campaign: {target_info_group_name}."
            else:
                target_creation_status = "Unknown"
                target_creation_name += "Unknown status."
            target_creation_task = AutopilotTask(
                id="task-2",
                name=target_creation_name,
                status=target_creation_status,
                steps=[],
            )
            tasks.append(target_creation_task)
        # we need to create one task for each content group.
        n = 3
        content_groups_processed = set()

        if "content_generation" in self.autopilot_visibility:
            content_generation_run = self.autopilot_visibility["content_generation"]
            for content_group_name in content_generation_run:
                content_group_run = content_generation_run[content_group_name]
                export_run = {}
                if "export" in self.autopilot_visibility:
                    if content_group_name in self.autopilot_visibility["export"]:
                        export_run = self.autopilot_visibility["export"][
                            content_group_name
                        ]
                content_status, content_steps = self.build_content_status_steps(
                    content_group_run, export_run
                )
                content_task = AutopilotTask(
                    id=f"task-{n}",
                    name=content_group_name,
                    status=content_status,
                    steps=content_steps,
                )
                tasks.append(content_task)
                content_groups_processed.add(content_group_name)
                n += 1

        # Handle case where export exists but content generation doesn't
        if "export" in self.autopilot_visibility:
            for content_group_name in self.autopilot_visibility["export"]:
                if content_group_name not in content_groups_processed:
                    export_run = self.autopilot_visibility["export"][content_group_name]
                    content_status, content_steps = self.build_content_status_steps(
                        {}, export_run
                    )
                    content_task = AutopilotTask(
                        id=f"task-{n}",
                        name=content_group_name,
                        status=content_status,
                        steps=content_steps,
                    )
                    tasks.append(content_task)
                    n += 1
        return tasks

    def build_content_status_steps(
        self, content_gen_status_object, export_status_object
    ):
        status = "Unknown"
        steps = []

        if not content_gen_status_object and not export_status_object:
            status = "Not Run"
            return status, steps

        total_contents_generated = 0
        if not content_gen_status_object:
            content_gen_step = AutopilotStep(
                id="step-1", status="Not Run", detail="Content generation not run"
            )
            steps.append(content_gen_step)

        else:
            content_gen_step_detail = ""
            done_contents = len(content_gen_status_object.get("Done", []))
            if done_contents > 0:
                content_gen_step_detail += f"{done_contents} contents generated. "
                total_contents_generated += done_contents
                content_gen_step_status = "Done"
            in_progress_contents = len(content_gen_status_object.get("In Progress", []))
            if in_progress_contents > 0:
                content_gen_step_detail += (
                    f"{in_progress_contents} contents in progress. "
                )
                total_contents_generated += in_progress_contents
                content_gen_step_status = "In Progress"
            error_contents = len(content_gen_status_object.get("Error", []))
            if error_contents > 0:
                content_gen_step_detail += (
                    f"{error_contents} contents failed to generate. "
                )
                total_contents_generated += error_contents
                content_gen_step_status = "Error"
            if total_contents_generated == 0:
                content_gen_step_detail = "Content generation not run"
                content_gen_step_status = "Not Run"

            content_gen_step = AutopilotStep(
                id="step-1",
                status=content_gen_step_status,
                detail=content_gen_step_detail,
            )
            status = (
                "Not Run"
                if content_gen_step_status == "Not Run"
                else content_gen_step_status
            )
            steps.append(content_gen_step)

        if not export_status_object:
            export_step = AutopilotStep(
                id="step-2", status="Not Run", detail="Export not run"
            )
            steps.append(export_step)
            status = "In Progress"
            return status, steps

        total_content_export = 0
        export_step_detail = ""
        done_contents = len(export_status_object.get("Done", []))
        if done_contents > 0:
            export_step_detail += f"{done_contents} contents exported. "
            total_content_export += done_contents
            export_step_status = "Done"
        in_progress_contents = len(export_status_object.get("In Progress", []))
        if in_progress_contents > 0:
            export_step_detail += f"{in_progress_contents} contents in progress. "
            total_content_export += in_progress_contents
            export_step_status = "In Progress"
        error_contents = len(export_status_object.get("Error", []))
        if error_contents > 0:
            export_step_detail += f"{error_contents} contents failed to export. "
            total_content_export += error_contents
            export_step_status = "Error"

        if total_content_export < total_contents_generated:
            export_step_status = "In Progress"
            export_step_detail += (
                f"{total_contents_generated - total_content_export} contents pending."
            )

        if total_content_export == 0:
            export_step_detail = "Export not run"

        export_step = AutopilotStep(
            id="step-2", status=export_step_status, detail=export_step_detail
        )
        steps.append(export_step)
        status = export_step_status
        return status, steps


class AutopilotObjectBuilder:
    def __init__(self, campaign_id):
        self.campaign_id = campaign_id
        self.campaign_instance = None

    def _get_base_runs(self) -> dict:
        """Get initial autopilot runs for the campaign."""

        autopilot_runs = {}
        last_30_days_timestamp = timezone.now() - timedelta(days=30)
        try:
            self.campaign_instance = Campaign.objects.get(id=self.campaign_id)
        except Campaign.DoesNotExist:
            logging.error(f"Campaign with id {self.campaign_id} not found")
            return autopilot_runs

        # First get all session_ids from runs with this campaign
        campaign_runs = AutopilotRun.objects.filter(
            campaign=self.campaign_instance,
            created_at__gte=last_30_days_timestamp,
        ).exclude(
            (Q(status__Done__isnull=True) | Q(status__Done=[]))
            & (Q(status__Error__isnull=True) | Q(status__Error=[]))
        )
        session_ids = campaign_runs.values_list("session_id", flat=True).distinct()

        # also get the autopilot runs associated with the campaign's target info group.
        targets = self.campaign_instance.campaign_params.get("targets", [])
        # Get all unique keys from all target dictionaries
        target_info_group_keys = (
            list(set().union(*[target.keys() for target in targets if target]))
            if targets
            else []
        )
        # we assume there is only one but there could be more.
        target_info_group_session_runs = AutopilotRun.objects.filter(
            target_info_group__target_info_group_key__in=target_info_group_keys,
            playbook=self.campaign_instance.playbook,
            created_at__gte=last_30_days_timestamp,
        )
        target_info_group_session_ids = target_info_group_session_runs.values_list(
            "session_id", flat=True
        ).distinct()

        # combine the session ids.
        session_ids = list(set(session_ids) | set(target_info_group_session_ids))

        # Then get ALL runs for these sessions, including those with null campaign
        all_session_runs = (
            AutopilotRun.objects.filter(session_id__in=session_ids)
            .filter(Q(campaign=self.campaign_instance) | Q(campaign__isnull=True))
            .order_by("session_id")
        )

        # Group by session_id in memory
        for run in all_session_runs:
            autopilot_runs.setdefault(run.session_id, []).append(run)

        return autopilot_runs

    def build(self):
        self.base_runs = self._get_base_runs()
        # logging.info(f"{len(self.base_runs)} sessions found")
        runs = []
        for session_id in self.base_runs:
            session_runs = self.base_runs[session_id]
            autopilot_session_processor = AutopilotSessionProcessor(session_runs)
            autopilot_session_processor.process()
            timestamp = autopilot_session_processor.timestamp
            status = autopilot_session_processor.status
            if status == "Unknown":  # skip for invalid status.
                continue
            message = autopilot_session_processor.message
            tasks = autopilot_session_processor.tasks

            run = AutopilotRunObject(session_id, timestamp, status, message, tasks)
            runs.append(run)
        return [run.to_dict() for run in runs]  # Convert all runs to dictionaries
