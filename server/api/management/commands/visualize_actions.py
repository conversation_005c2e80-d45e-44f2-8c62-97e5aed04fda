import textwrap

import matplotlib.pyplot as plt
import networkx as nx
from api.models import Action, ActionEdge
from api.serializers import ActionSerializer
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Visualize the action flow diagram"

    def add_arguments(self, parser):
        parser.add_argument(
            "--campaign", type=int, help="Campaign ID to filter actions", required=False
        )

    def create_action_graph(self, actions_data):
        G = nx.DiGraph()

        # Add nodes with attributes and layer info
        category_ranks = {
            "ACTION_CATEGORY_USER_INPUT": 0,
            "ACTION_CATEGORY_REPURPOSE": 1,
            "ACTION_CATEGORY_PERSONALIZE": 2,
            "ACTION_CATEGORY_EXPORT": 3,
        }

        # Add nodes with attributes
        for action in actions_data:
            action_name = action["action_name"]
            category = action["action_category"]
            label = f"({category.replace('ACTION_CATEGORY_', '')})\nAction #{action['id']} - {action_name}"

            G.add_node(
                action["id"],
                label=label,
                category=category,
                layer=category_ranks[category],
            )

        # Add edges with attributes
        for action in actions_data:
            for incoming in action["incoming_actions_data"]:
                edge = incoming["edge"]
                edge_label = f"edge_id: {edge['id']}\n"
                if edge["config"]:
                    config_str = str(edge["config"])
                    config_str = "\n".join(textwrap.wrap(config_str, width=20))
                    edge_label += f"config: {config_str}"

                G.add_edge(edge["from_action"], action["id"], label=edge_label)

        return G

    def visualize_action_graph(self, G):
        plt.figure(figsize=(15, 10))

        # Use multipartite layout
        pos = nx.multipartite_layout(G, subset_key="layer", scale=2.0)

        # Define colors for different categories
        category_colors = {
            "ACTION_CATEGORY_UNSPECIFIED": "#D3D3D3",  # Light gray
            "ACTION_CATEGORY_USER_INPUT": "#F0E68C",  # Khaki
            "ACTION_CATEGORY_REPURPOSE": "#87CEEB",  # Sky blue
            "ACTION_CATEGORY_PERSONALIZE": "#FFB6C1",  # Light pink
            "ACTION_CATEGORY_EXPORT": "#98FB98",  # Pale green
        }

        # Draw edges
        nx.draw_networkx_edges(
            G, pos, edge_color="black", arrows=True, arrowsize=20, width=1.0
        )

        # Draw nodes
        for node, (x, y) in pos.items():
            label = nx.get_node_attributes(G, "label")[node]
            category = nx.get_node_attributes(G, "category")[node]
            color = category_colors.get(category, "#D3D3D3")

            # Create rectangle for node
            width = 0.4
            height = 0.15
            rect = plt.Rectangle(
                (x - width / 2, y - height / 2),
                width,
                height,
                facecolor=color,
                edgecolor="gray",
                alpha=1.0,
                zorder=2,
            )
            plt.gca().add_patch(rect)

            # Add node label
            plt.text(
                x,
                y,
                label,
                horizontalalignment="center",
                verticalalignment="center",
                fontsize=9,
                fontweight="bold",
                bbox=dict(facecolor="white", alpha=0.7, edgecolor="none", pad=0.5),
                zorder=3,
            )

        # Draw edge labels
        edge_labels = nx.get_edge_attributes(G, "label")
        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)

        plt.title("Action Flow Diagram", pad=20, fontsize=16, fontweight="bold")
        plt.axis("off")
        plt.tight_layout()

        return plt

    def handle(self, *args, **options):
        # Filter actions based on campaign if provided
        actions = Action.objects.all()

        if options["campaign"]:
            actions = actions.filter(campaign=options["campaign"])
            self.stdout.write(f"Filtering actions for campaign {options['campaign']}")

        # Serialize the actions
        serializer = ActionSerializer(actions, many=True)
        actions_data = serializer.data
        if not actions_data:
            self.stdout.write(
                self.style.WARNING("No actions found with the given filters")
            )
            return

        # Create and visualize graph
        G = self.create_action_graph(actions_data)
        plt = self.visualize_action_graph(G)

        self.stdout.write(self.style.SUCCESS("Displaying action flow diagram..."))
        plt.show()
