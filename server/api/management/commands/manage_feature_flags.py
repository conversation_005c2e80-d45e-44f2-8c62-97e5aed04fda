from api.models import TofuUser
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction


class Command(BaseCommand):
    help = "Manage boolean feature flags for users"

    SUPPORTED_FLAGS = [
        "campaignV3Enabled",
        "inboundLandingPageEnabled",
        "internalFeatures",
    ]

    def add_arguments(self, parser):
        parser.add_argument(
            "--flag",
            type=str,
            required=True,
            choices=self.SUPPORTED_FLAGS,
            help=f'Feature flag to manage. Supported flags: {", ".join(self.SUPPORTED_FLAGS)}',
        )
        parser.add_argument(
            "--enable",
            action="store_true",
            help="Enable the feature flag (default)",
        )
        parser.add_argument(
            "--disable",
            action="store_true",
            help="Disable the feature flag",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes",
        )
        parser.add_argument(
            "--list-only",
            action="store_true",
            help="Only list users and their flag status without making changes",
        )
        parser.add_argument(
            "--username",
            type=str,
            help="Manage feature flag for a specific user by username",
        )
        parser.add_argument(
            "--admin-only",
            action="store_true",
            default=True,
            help='Only target admin users (usernames starting with "tofuadmin") (default)',
        )
        parser.add_argument(
            "--all-users",
            action="store_true",
            help="Target all users, not just admin users",
        )
        parser.add_argument(
            "--limit",
            type=int,
            help="Limit the number of users to process",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of users to update in each batch (default: 100)",
        )
        parser.add_argument(
            "--no-bulk",
            action="store_true",
            help="Disable bulk operations and update users one by one",
        )

    def handle(self, *args, **options):
        flag_name = options.get("flag")
        enable_flag = options.get("enable", True)
        disable_flag = options.get("disable", False)
        dry_run = options.get("dry_run", False)
        list_only = options.get("list_only", False)
        specific_username = options.get("username")
        admin_only = options.get("admin_only", True)
        all_users = options.get("all_users", False)
        limit = options.get("limit")
        batch_size = options.get("batch_size", 100)
        no_bulk = options.get("no_bulk", False)

        # If --all-users is specified, override admin_only
        if all_users:
            admin_only = False

        # Check for conflicting options
        if enable_flag and disable_flag:
            raise CommandError("Cannot specify both --enable and --disable")

        # Determine the target value for the flag
        target_value = not disable_flag

        # Get users based on options
        if specific_username:
            # Get a specific user
            try:
                users = TofuUser.objects.filter(username=specific_username)
                if not users.exists():
                    self.stdout.write(
                        self.style.ERROR(f"User not found: {specific_username}")
                    )
                    return
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error finding user {specific_username}: {str(e)}"
                    )
                )
                return
        elif admin_only:
            # Get all admin users
            users = TofuUser.objects.filter(username__startswith="tofuadmin")
        else:
            # Get all users
            users = TofuUser.objects.all()

        # Apply limit if specified
        if limit is not None and limit > 0:
            original_count = users.count()
            users = users[:limit]
            if not list_only:
                self.stdout.write(
                    self.style.NOTICE(
                        f"Limiting to {limit} users out of {original_count} total matching users"
                    )
                )

        if list_only:
            self.stdout.write(
                self.style.NOTICE(f"Listing users with {flag_name} status:")
            )
            for user in users:
                flag_status = (
                    user.context.get(flag_name, False) if user.context else False
                )
                status_str = "ENABLED" if flag_status else "DISABLED"
                self.stdout.write(f"{user.username} - {flag_name}: {status_str}")
            self.stdout.write(self.style.SUCCESS(f"Total users: {users.count()}"))
            return

        action_str = "enable" if target_value else "disable"

        # If dry run or no bulk operations requested, use the individual update approach
        if dry_run or no_bulk:
            count = self._update_users_individually(
                users, flag_name, target_value, dry_run
            )
        else:
            count = self._update_users_in_bulk(
                users, flag_name, target_value, batch_size
            )

        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Dry run completed. Would {action_str} {flag_name} for {count} users"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully {action_str}d {flag_name} for {count} users"
                )
            )

    def _update_users_individually(self, users, flag_name, target_value, dry_run):
        """Update users one by one, showing detailed progress."""
        self.stdout.write(f"Processing {users.count()} users individually...")
        count = 0
        for user in users:
            # Check current status
            current_status = (
                user.context.get(flag_name, False) if user.context else False
            )

            # Skip if already in desired state
            if current_status == target_value:
                self.stdout.write(
                    f"Skipping {user.username} - {flag_name} already {'enabled' if target_value else 'disabled'}"
                )
                continue

            # Initialize context if it doesn't exist
            if user.context is None:
                user.context = {}

            # Set flag to target value
            if not dry_run:
                user.context[flag_name] = target_value
                user.save()
                self.stdout.write(
                    f"{'Enabled' if target_value else 'Disabled'} {flag_name} for user: {user.username}"
                )
            else:
                self.stdout.write(
                    f"Would {'enable' if target_value else 'disable'} {flag_name} for user: {user.username} (dry run)"
                )

            count += 1

        return count

    def _update_users_in_bulk(self, users, flag_name, target_value, batch_size):
        """Update users in bulk for better performance."""
        total_count = 0
        total_users = users.count()
        self.stdout.write(
            f"Processing {total_users} users in batches of {batch_size}..."
        )

        # Process in batches to avoid memory issues with large datasets
        for i in range(0, total_users, batch_size):
            batch_end = min(i + batch_size, total_users)
            self.stdout.write(f"Processing batch {i+1}-{batch_end} of {total_users}...")

            batch_users = users[i:batch_end]
            to_update = []

            # First pass: identify users that need updating
            for user in batch_users:
                current_status = (
                    user.context.get(flag_name, False) if user.context else False
                )

                # Skip if already in desired state
                if current_status == target_value:
                    continue

                # Initialize context if it doesn't exist
                if user.context is None:
                    user.context = {}

                # Set flag to target value
                user.context[flag_name] = target_value
                to_update.append(user)

            # Second pass: bulk update the identified users
            if to_update:
                with transaction.atomic():
                    for user in to_update:
                        user.save(update_fields=["context"])

                batch_count = len(to_update)
                total_count += batch_count
                self.stdout.write(
                    self.style.SUCCESS(f"Updated {batch_count} users in this batch")
                )
            else:
                self.stdout.write("No users needed updating in this batch")

        return total_count
