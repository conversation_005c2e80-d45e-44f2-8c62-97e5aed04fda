# Django Management Commands

This directory contains custom Django management commands for the Tofu application.

## Available Commands

### `manage_feature_flags.py`

A command for managing boolean feature flags for users in the system.

#### Supported Feature Flags

- `campaignV3Enabled` - Enables the Campaign V3 features
- `inboundLandingPageEnabled` - Enables the inbound landing page features
- `internalFeatures` - Enables internal/development features
- `richTextV2Enabled` - Enables Rich text v2 experience

#### Usage

```bash
python manage.py manage_feature_flags [options]
```

#### Options

| Option | Description |
|--------|-------------|
| `--flag=FLAG_NAME` | **Required**. Feature flag to manage. Must be one of the supported flags. |
| `--enable` | Enable the feature flag (default behavior) |
| `--disable` | Disable the feature flag |
| `--dry-run` | Show what would be done without making changes |
| `--list-only` | Only list users and their flag status without making changes |
| `--username=USERNAME` | Manage feature flag for a specific user by username |
| `--admin-only` | Only target admin users (usernames starting with "tofuadmin") |
| `--limit=NUMBER` | Limit the number of users to process |
| `--batch-size=NUMBER` | Number of users to update in each batch (default: 100) |
| `--no-bulk` | Disable bulk operations and update users one by one |

#### Examples

List all users with their `campaignV3Enabled` status:
```bash
python manage.py manage_feature_flags --flag=campaignV3Enabled --list-only
```

Enable `campaignV3Enabled` for a specific user:
```bash
python manage.py manage_feature_flags --flag=campaignV3Enabled --username=tofuadmin-zaicheng
```

Disable `inboundLandingPageEnabled` for all admin users:
```bash
python manage.py manage_feature_flags --flag=inboundLandingPageEnabled --disable --admin-only
```

Perform a dry run to see what would happen when enabling a flag:
```bash
python manage.py manage_feature_flags --flag=internalFeatures --dry-run
```

Enable a flag for a limited number of users:
```bash
python manage.py manage_feature_flags --flag=campaignV3Enabled --limit=50
```

Use a larger batch size for better performance with many users:
```bash
python manage.py manage_feature_flags --flag=internalFeatures --batch-size=500
```

### `visualize_actions.py`

A command for visualizing user actions in the system.

## Adding New Commands

To add a new management command:

1. Create a new Python file in this directory
2. Define a class that inherits from `django.core.management.base.BaseCommand`
3. Implement the required methods (`add_arguments` and `handle`)
4. Make sure the file is importable

For more information, see the [Django documentation on custom management commands](https://docs.djangoproject.com/en/stable/howto/custom-management-commands/). 