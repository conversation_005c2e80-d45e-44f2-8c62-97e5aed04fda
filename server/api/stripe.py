import logging
import os
import traceback

import stripe
from api.integrations.slack import send_slack_message
from django.db import transaction

from .models import TofuUser
from .tofu_lite import reset_credits_for_tofu_lite
from .tofu_pages_utils import get_tier_from_price_id

tofu_lite_product_ids = {
    "prod_RXsnaNmDJ4fwEk": "Tofu Pages 300",
    "prod_RXsnSlhHc8nYKE": "Tofu Pages 100",
}


def handle_stripe_event(event):
    try:
        stripe.api_key = os.environ.get("STRIPE_API_KEY", "")
        if not stripe.api_key:
            logging.error("STRIPE_API_KEY is not configured")
            return
        # Validate event
        if not event or not event.type:
            raise ValueError("Invalid event structure")

        # Log event
        # logging.info(f"Processing Stripe event: {event.type}")

        # Handle specific event types
        if event.type == "payment_intent.succeeded":
            handle_payment_success(event.data.object)
        elif event.type == "payment_intent.payment_failed":
            handle_payment_failure(event.data.object)
        else:
            logging.warning(f"Unhandled Stripe event type: {event.type}")
        # Add more event types as needed
    except Exception as e:
        logging.error(
            f"Error processing Stripe event: {str(e)}\n{traceback.format_exc()}"
        )
        raise


def handle_payment_success(payment_intent):
    # logging.info(f"Payment intent succeeded: {payment_intent}")

    # First check if the payment intent is for a subscription, ignore otherwise.
    invoice_id = getattr(payment_intent, "invoice", None)
    if not invoice_id:
        raise ValueError(f"No invoice id found for payment intent {payment_intent.id}")
    try:
        invoice = stripe.Invoice.retrieve(invoice_id)
    except Exception as e:
        logging.error(f"Error retrieving invoice {invoice_id}: {str(e)}")
        return
    subscription_id = getattr(invoice, "subscription", None)
    if not subscription_id:
        logging.warning(f"No subscription id found for invoice {invoice_id}")
        return
    try:
        subscription = stripe.Subscription.retrieve(subscription_id)
    except Exception as e:
        logging.error(f"Error retrieving subscription {subscription_id}: {str(e)}")
        return

    has_tofu_lite_price = any(
        item.get("price", {}).get("product") in tofu_lite_product_ids
        for item in subscription.get("items", {}).get("data", [])
    )

    if not has_tofu_lite_price:
        logging.warning(f"No tofu lite price found for subscription {subscription_id}")
        return

    stripe_customer_id = getattr(payment_intent, "customer", None)
    tofu_user = None
    tofu_user_id = None
    if stripe_customer_id:
        # search for tofu user with stripe customer id
        tofu_user = TofuUser.objects.filter(
            stripe_customer_id=stripe_customer_id
        ).first()
        if tofu_user:
            tofu_user_id = tofu_user.id
    else:
        logging.error(
            f"No stripe customer id found for payment intent {payment_intent}"
        )
    if not tofu_user:
        logging.warning(
            f"No tofu user found with stripe customer id {stripe_customer_id}"
        )
        # try to get tofu user from checkout session
        checkout_session_id = get_checkout_session_id_from_payment_intent(
            payment_intent
        )
        if not checkout_session_id:
            payment_intent_id = getattr(payment_intent, "id", None)
            if not payment_intent_id:
                error_message = (
                    f"No checkout session id found for payment intent {payment_intent}"
                )
            else:
                error_message = f"No checkout session id found for payment intent {payment_intent_id}"
            logging.error(error_message)
            raise Exception(error_message)
        tofu_user_id = get_checkout_session_tofu_user_id(checkout_session_id)
        if not tofu_user_id:
            error_msg = (
                f"No tofu user found with checkout session id {checkout_session_id}"
            )
            logging.error(error_msg)
            raise Exception(error_msg)
    reset_credits_for_tofu_lite(tofu_user_id, payment_info=payment_intent)
    # Send Slack notification about successful payment
    # only on prod.
    if os.environ.get("TOFU_ENV", default="unknown") == "production":
        try:
            amount = getattr(payment_intent, "amount", 0)
            message = (
                f":white_check_mark: Stripe Payment Success\n"
                f"User ID: `{tofu_user_id}`\n"
                f"Amount: `{amount}`"
            )
            send_slack_message("#customer-success-tofu-lite", message)
        except Exception as e:
            logging.error(f"Error sending Slack notification: {str(e)}")


def get_checkout_session_tofu_user_id(checkout_session_id):
    tofu_user = TofuUser.objects.filter(
        stripe_checkout_session_id=checkout_session_id
    ).first()
    if not tofu_user:
        return None
    try:
        link_tofu_user_checkout_session_customer_id(tofu_user.id)
    except Exception as e:
        logging.error(f"Error getting tofu user checkout session customer id: {str(e)}")
        return None
    return tofu_user.id


def get_checkout_session_id_from_payment_intent(payment_intent):
    checkout_sessions = stripe.checkout.Session.list(
        payment_intent=payment_intent.id, limit=1
    )
    if not checkout_sessions or not checkout_sessions.data:
        logging.error(
            f"No checkout session found for payment intent {payment_intent.id}"
        )
        raise Exception(
            f"No checkout session found for payment intent {payment_intent.id}"
        )
    # logging.info(f"Checkout session: {checkout_sessions}")
    return checkout_sessions.data[0].id


def handle_payment_failure(payment_intent):
    # First check if the payment intent is for a subscription, ignore otherwise.
    invoice_id = getattr(payment_intent, "invoice", None)
    if not invoice_id:
        logging.info(
            f"Ignoring payment failure for non-subscription payment (no invoice ID)"
        )
        return
    try:
        invoice = stripe.Invoice.retrieve(invoice_id)
        subscription_id = getattr(invoice, "subscription", None)
    except Exception as e:
        logging.error(f"Error retrieving invoice {invoice_id}: {str(e)}")
        return
    if not subscription_id:
        logging.warning(f"No subscription id found for invoice {invoice_id}")
        return
    # otherwise log the failure
    logging.error(f"Payment intent failed: {payment_intent}")


def link_tofu_user_to_checkout_session(tofu_user_id, checkout_session_id):
    logging.info(
        f"Linking tofu user {tofu_user_id} to checkout session {checkout_session_id}"
    )
    if not tofu_user_id:
        logging.error(f"No tofu user id found")
        return False
    if not checkout_session_id:
        logging.error(f"No checkout session id found")
        return False
    try:
        with transaction.atomic():
            user = TofuUser.objects.select_for_update().get(id=tofu_user_id)
            user.stripe_checkout_session_id = checkout_session_id
            user.save()
            return True
    except TofuUser.DoesNotExist:
        logging.error(f"No tofu user found with id {tofu_user_id}")
        return False
    except Exception as e:
        logging.error(f"Error linking tofu user to checkout session: {str(e)}")
        return False


def link_tofu_user_checkout_session_customer_id(tofu_user_id):
    try:
        with transaction.atomic():
            user = TofuUser.objects.select_for_update().get(id=tofu_user_id)
            checkout_session_id = user.stripe_checkout_session_id
            if not checkout_session_id:
                logging.error(
                    f"No checkout session id found for tofu user {tofu_user_id}"
                )
                return None

            try:
                checkout_session = stripe.checkout.Session.retrieve(checkout_session_id)
                customer_id = checkout_session.customer
                if not customer_id:
                    raise ValueError(
                        f"No customer ID in checkout session {checkout_session_id}"
                    )
                user.stripe_customer_id = customer_id
                user.save()
                logging.info(f"User stripe customer id: {user.stripe_customer_id}")
                logging.info(
                    f"Linked tofu user {tofu_user_id} to checkout session {checkout_session_id} with stripe customer id {customer_id}"
                )
                return customer_id
            except Exception as e:
                logging.error(
                    f"Error retrieving checkout session {checkout_session_id}: {str(e)}"
                )
                return None

    except TofuUser.DoesNotExist:
        logging.error(f"No tofu user found with id {tofu_user_id}")
        return None


def _get_active_subscriptions(tofu_user_id):
    """Helper function to get all active subscriptions for a user"""
    try:
        tofu_user = TofuUser.objects.get(id=tofu_user_id)
    except TofuUser.DoesNotExist:
        raise ValueError(f"No tofu user found with id {tofu_user_id}")

    stripe_customer_id = tofu_user.stripe_customer_id
    if not stripe_customer_id:
        raise ValueError(f"No stripe customer id found for tofu user {tofu_user_id}")

    try:
        subscriptions = stripe.Subscription.list(customer=stripe_customer_id)
    except Exception as e:
        raise ValueError(f"Error fetching stripe subscriptions: {str(e)}")

    if not subscriptions.data:
        raise ValueError(f"No stripe subscription found for tofu user {tofu_user_id}")

    return tofu_user, subscriptions.data


def has_subscription(tofu_user_id):
    if not tofu_user_id:
        raise ValueError("tofu_user_id is required")

    try:
        _get_active_subscriptions(tofu_user_id)
        return True
    except ValueError as e:
        # Expected errors (no user, no customer, no subscription)
        logging.info(str(e))
        return False
    except Exception as e:
        # Unexpected errors (DB errors, Stripe API errors, etc.)
        logging.error(f"Unexpected error checking subscription: {str(e)}")
        raise


def cancel_subscription(tofu_user_id):
    if not tofu_user_id:
        raise ValueError("tofu_user_id is required")
    try:
        tofu_user, subscriptions = _get_active_subscriptions(tofu_user_id)
        with transaction.atomic():
            for subscription in subscriptions:
                try:
                    stripe.Subscription.cancel(subscription.id)
                    logging.info(f"Stripe subscription {subscription.id} cancelled")
                except Exception as e:
                    raise ValueError(
                        f"Error cancelling subscription {subscription.id}: {str(e)}"
                    )

            tofu_user.tofu_lite_subscription_tier = (
                TofuUser.TofuLiteSubscriptionTier.FREE_TIER
            )
            tofu_user.save()
    except Exception as e:
        raise Exception(f"Error cancelling stripe subscription: {str(e)}") from e


def change_subscription(tofu_user_id, price_id):
    if not tofu_user_id:
        raise ValueError("tofu_user_id is required")
    if not price_id:
        raise ValueError("price_id is required")

    try:
        tofu_user, subscriptions = _get_active_subscriptions(tofu_user_id)
        if len(subscriptions) > 1:
            message = f"Expected exactly one active subscription, got {subscriptions}"
            logging.error(message)
            raise ValueError(message)
        subscription = subscriptions[0]
        items = stripe.SubscriptionItem.list(subscription=subscription.id).data
        if len(items) > 1:
            message = f"Expected exactly one item in subscription, got {items}"
            logging.error(message)
            raise ValueError(message)
        sub_item_id = items[0].id
        stripe.Subscription.modify(
            subscription.id, items=[{"id": sub_item_id, "price": price_id}]
        )
        old_tier = tofu_user.tofu_lite_subscription_tier
        new_tier = get_tier_from_price_id(price_id)
        tofu_user.tofu_lite_subscription_tier = new_tier
        tofu_user.save()
        if (
            old_tier == TofuUser.TofuLiteSubscriptionTier.TIER_1
            and new_tier == TofuUser.TofuLiteSubscriptionTier.TIER_2
        ):
            # reset the subscription to charge from today
            stripe.Subscription.modify(
                subscription.id,
                proration_behavior="always_invoice",
                billing_cycle_anchor="now",
            )
    except Exception as e:
        message = (
            f"Error changing stripe subscription: {str(e)}\n{traceback.format_exc()}"
        )
        logging.error(message)
        raise Exception(message) from e
