import copy
import datetime
import logging

import google.auth
import googleapiclient.discovery
import googleapiclient.errors
from django.forms.models import model_to_dict
from django.utils import timezone
from langsmith import traceable

from .actions.action_copier import ActionCopier
from .campaign import CampaignHandler
from .content import ContentGenerator
from .content_group import ContentGroupHandler
from .eval_utils import (
    campaign_has_any_custom_instructions,
    fetch_asset,
    get_asset_names_from_content,
    get_repurpose_asset_params_from_content,
)
from .integrations.langsmith_utils import fetch_langsmith_links
from .langsmith_integration import BaseTracableClass
from .models import (
    Campaign,
    Content,
    ContentGroup,
    ContentVariation,
    Playbook,
    TofuUser,
)
from .playbook import PlaybookHandler
from .playbook_build.doc_loader import get_template


class BenchmarkResultSaver:
    @staticmethod
    def save_to_google_drive(creds, spreadsheet_id):
        drive_service = googleapiclient.discovery.build(
            "drive", "v3", credentials=creds
        )

        # hardcoded
        GOOGLE_DRIVE_ID_FOR_BENCHMARK = "190RlobXLLXlCHpAACkSW157UFLcuTNNT"
        res = (
            drive_service.files()
            .update(
                fileId=spreadsheet_id,
                body={},
                addParents=GOOGLE_DRIVE_ID_FOR_BENCHMARK,
                removeParents="root",
                supportsAllDrives=True,
            )
            .execute()
        )
        logging.info(f"saving drive result: {res}")
        return res

    @staticmethod
    def get_wrap_requests_width(fields):
        extended_fields = [
            "preceding",
            "succeeding",
            "custom_instructions",
            "original_component",
            "generated_component",
            "meta",
            "custom_instruction_generated_component",
        ]
        wrap_widths = []
        for i, field in enumerate(fields):
            if field in extended_fields:
                wrap_widths.append(
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": 0,
                                "dimension": "COLUMNS",
                                "startIndex": i,
                                "endIndex": i + 1,
                            },
                            "properties": {"pixelSize": 300},
                            "fields": "pixelSize",
                        }
                    }
                )
        return wrap_widths

    @staticmethod
    def get_wrap_requests_width_repurpose(fields):
        huge_fields = ["generated_component"]
        extended_fields = [
            "custom_instructions",
            "link_to_review",
            "link_to_anchor",
            "repurpose_template",
        ]
        wrap_widths = []
        for i, field in enumerate(fields):
            if field in huge_fields:
                wrap_widths.append(
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": 0,
                                "dimension": "COLUMNS",
                                "startIndex": i,
                                "endIndex": i + 1,
                            },
                            "properties": {"pixelSize": 1200},
                            "fields": "pixelSize",
                        }
                    }
                )
            elif field in extended_fields:
                wrap_widths.append(
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": 0,
                                "dimension": "COLUMNS",
                                "startIndex": i,
                                "endIndex": i + 1,
                            },
                            "properties": {"pixelSize": 300},
                            "fields": "pixelSize",
                        }
                    }
                )
        return wrap_widths

    @staticmethod
    def create_gsheet(creds, title, fields, data, repurpose=False):
        service = googleapiclient.discovery.build("sheets", "v4", credentials=creds)
        spreadsheet = (
            service.spreadsheets()
            .create(
                body={"properties": {"title": title}},
                fields="spreadsheetId",
            )
            .execute()
        )
        spreadsheet_id = spreadsheet.get("spreadsheetId")

        BenchmarkResultSaver.save_to_google_drive(creds, spreadsheet_id)

        range = "Sheet1"
        value_range_body = {"values": data}
        request = (
            service.spreadsheets()
            .values()
            .update(
                spreadsheetId=spreadsheet_id,
                range=range,
                valueInputOption="RAW",
                body=value_range_body,
            )
        )
        response = request.execute()

        wrap_requests = [
            {
                "repeatCell": {
                    "range": {
                        "sheetId": 0,  # Assuming the first sheet has ID 0, change this if necessary
                    },
                    "cell": {
                        "userEnteredFormat": {
                            "wrapStrategy": "WRAP",
                        },
                    },
                    "fields": "userEnteredFormat.wrapStrategy",
                },
            }
        ]
        if repurpose:
            wrap_requests += BenchmarkResultSaver.get_wrap_requests_width_repurpose(
                fields
            )
        else:
            wrap_requests += BenchmarkResultSaver.get_wrap_requests_width(fields)

        # Execute the requests to set text wrap
        batch_update_request_body = {"requests": wrap_requests}
        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id, body=batch_update_request_body
        ).execute()

        return response, spreadsheet_id

    @staticmethod
    def create_and_save_gsheet(title, fields, data, repurpose=False):
        try:
            SCOPES = [
                "https://www.googleapis.com/auth/spreadsheets",
                "https://www.googleapis.com/auth/drive",
            ]
            creds = google.oauth2.service_account.Credentials.from_service_account_file(
                ".credentials/google_service_account_key.json", scopes=SCOPES
            )

            _response, spreadsheet_id = BenchmarkResultSaver.create_gsheet(
                creds, title, fields, data, repurpose
            )
            return {
                "google_sheet_url": f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}/edit"
            }
        except googleapiclient.errors.HttpError as error:
            print(f"Failed to generate google sheet due to: {error}")
            return error


class Benchmark(BaseTracableClass):
    REVIEW_USER_ID = 129
    REVIEW_PLAYBOOK_ID = 125

    @staticmethod
    def get_benchmark_instance(test_only=False, session_id=None, session_tag=None):
        return Benchmark(
            Benchmark.REVIEW_USER_ID,
            Benchmark.REVIEW_PLAYBOOK_ID,
            test_only=test_only,
            session_id=session_id,
            session_tag=session_tag,
        )

    def __init__(
        self,
        creator_id,
        playbook_id,
        test_only=True,
        session_id=None,
        session_tag=None,
    ) -> None:
        self.review_user = TofuUser.objects.get(pk=creator_id)
        self.review_playbook = Playbook.objects.get(pk=playbook_id)
        self.test_only = test_only
        self.session_id = session_id
        self.session_tag = session_tag

        logging.info(
            f"Using review user: {self.review_user}, type of user: {type(self.review_user)}"
        )

    @traceable
    def gen_review(
        self,
        prefix,
        campaign_ids,
        content_ids,
        model_name,
        num_of_variations,
        enable_custom,
        generate_all_targets,
        joint_generation=False,
        template_generation=False,
    ):
        if template_generation:
            fields = self.gen_review_template_keys()
        else:
            fields = self.gen_review_keys()
        results = [fields]

        orig_contents = Content.objects.filter(pk__in=content_ids)
        # content generation
        for orig_content in orig_contents:
            logging.info("Content id is: " + str(orig_content.id))
            # copy first
            copied_content = self.copy_content(orig_content, None, False, prefix)
            # generate
            self.gen_review_result(
                copied_content,
                model_name,
                num_of_variations,
                enable_custom,
                joint_generation=joint_generation,
                template_generation=template_generation,
            )
            if not self.test_only:
                # copy to review account
                review_content = self.copy_content(copied_content, None, True)
                logging.info(
                    f"review_content is with id {review_content.id}, and data is {review_content}"
                )
                results += self.get_content_review_data(
                    review_content,
                    None,
                    model_name,
                    num_of_variations,
                    enable_custom=enable_custom,
                    orig_content_id=orig_content.id,
                    orig_campaign=orig_content.content_group.campaign,
                )
            # delete the intermediate content. The name check is only for safety of the code
            if copied_content.content_name.startswith(prefix):
                copied_content.delete()
            else:
                raise Exception(
                    f"Content {copied_content.id} is not with prefix {prefix}"
                )

        orig_campaigns = Campaign.objects.filter(pk__in=campaign_ids)
        # campaign generation
        for orig_campaign in orig_campaigns:
            logging.info("Campaign id is: " + str(orig_campaign.id))
            one_target_only = orig_campaign.id not in generate_all_targets
            # copy first
            copied_campaign = self.copy_campaign(
                orig_campaign, False, prefix, one_target_only
            )
            # no custom
            self.gen_campaign_review_result(
                copied_campaign,
                model_name,
                num_of_variations,
                enable_custom=False,
                joint_generation=joint_generation,
                template_generation=template_generation,
            )

            # with custom
            if enable_custom and campaign_has_any_custom_instructions(orig_campaign):
                copied_campaign_for_custom = self.copy_campaign(
                    orig_campaign, False, prefix, one_target_only
                )
                self.gen_campaign_review_result(
                    copied_campaign_for_custom,
                    model_name,
                    num_of_variations,
                    enable_custom=True,
                    joint_generation=joint_generation,
                    template_generation=template_generation,
                )
                if not self.test_only:
                    review_campaign_for_custom = self.copy_campaign(
                        copied_campaign_for_custom, True
                    )

            else:
                copied_campaign_for_custom = None
                review_campaign_for_custom = None

            if not self.test_only:
                # copy to review account
                review_campaign = self.copy_campaign(copied_campaign, True)
                logging.info(
                    f"review_campaign is with id {review_campaign.id}, and data is {review_campaign}"
                )
                results.extend(
                    self.get_campaign_review_data(
                        review_campaign,
                        model_name,
                        num_of_variations,
                        repurpose=False,
                        orig_playbook_instance=orig_campaign.playbook,
                        enable_custom=enable_custom,
                        template_generation=template_generation,
                        orig_campaign=orig_campaign,
                        campaign_with_custom_instruct=review_campaign_for_custom,
                    )
                )

            # delete the intermediate campaign. The name check is only for safety of the code
            if copied_campaign.campaign_name.startswith(prefix):
                # logging.error(f'copied campaign to delete is: {copied_campaign.id}')
                copied_campaign.delete()
            else:
                raise Exception(
                    f"Campaign {copied_campaign.id} is not with prefix {prefix}"
                )
            if copied_campaign_for_custom:
                if copied_campaign_for_custom.campaign_name.startswith(prefix):
                    copied_campaign_for_custom.delete()
                else:
                    raise Exception(
                        f"Campaign {copied_campaign_for_custom.id} is not with prefix {prefix}"
                    )

        if not self.test_only:
            gsheet_filename = self.gen_auto_review_filename(prefix)
            result_data = BenchmarkResultSaver.create_and_save_gsheet(
                gsheet_filename, fields, results
            )
            return result_data

    @traceable
    def gen_review_repurpose(
        self,
        prefix,
        campaign_ids,
        content_ids,
        model_name,
        num_of_variations,
        enable_custom,
        generate_all_targets,
        joint_generation=True,
    ):
        fields = self.gen_review_keys_repurpose()
        results = [fields]

        orig_contents = Content.objects.filter(pk__in=content_ids)
        # content generation
        for orig_content in orig_contents:
            # copy first
            copied_content = self.copy_content(orig_content, None, False, prefix)
            # generate
            self.gen_review_result(
                copied_content,
                model_name,
                num_of_variations,
                enable_custom,
                joint_generation=joint_generation,
            )
            # copy to review account
            review_content = self.copy_content(copied_content, None, True)
            logging.info(
                f"review_content is with id {review_content.id}, and data is {review_content}"
            )
            if not self.test_only:
                results += self.get_content_review_data(
                    review_content,
                    None,
                    model_name,
                    num_of_variations,
                    repurpose=True,
                    orig_playbook_instance=orig_content.playbook,
                    enable_custom=enable_custom,
                    orig_content_id=orig_content.id,
                    orig_campaign=orig_content.content_group.campaign,
                )
            # delete the intermediate content. The name check is only for safety of the code
            if copied_content.content_name.startswith(prefix):
                copied_content.delete()
            else:
                raise Exception(
                    f"Content {copied_content.id} is not with prefix {prefix}"
                )

        orig_campaigns = Campaign.objects.filter(pk__in=campaign_ids)
        # campaign generation
        for orig_campaign in orig_campaigns:
            one_target_only = orig_campaign.id not in generate_all_targets
            # copy first
            copied_campaign = self.copy_campaign(
                orig_campaign, False, prefix, one_target_only
            )
            # generate
            self.gen_campaign_review_result(
                copied_campaign,
                model_name,
                num_of_variations,
                enable_custom,
                joint_generation=joint_generation,
            )
            if not self.test_only:

                # copy to review account
                review_campaign = self.copy_campaign(copied_campaign, True)
                logging.info(
                    f"review_campaign is with id {review_campaign.id}, and data is {review_campaign}"
                )
                results.extend(
                    self.get_campaign_review_data(
                        review_campaign,
                        model_name,
                        num_of_variations,
                        repurpose=True,
                        orig_playbook_instance=orig_campaign.playbook,
                        enable_custom=enable_custom,
                        orig_campaign=orig_campaign,
                    )
                )

            # delete the intermediate campaign. The name check is only for safety of the code
            if copied_campaign.campaign_name.startswith(prefix):
                # logging.error(f'copied campaign to delete is: {copied_campaign.id}')
                copied_campaign.delete()
            else:
                raise Exception(
                    f"Campaign {copied_campaign.id} is not with prefix {prefix}"
                )

        if not self.test_only:
            gsheet_filename = self.gen_auto_review_filename(prefix)
            result_data = BenchmarkResultSaver.create_and_save_gsheet(
                gsheet_filename, fields, results, repurpose=True
            )
            return result_data
        else:
            return {}

    def copy_campaign(
        self, orig_campaign, is_copy_for_review, prefix="", one_target_only=False
    ):
        # reject copy if the owner does not start with tofuadmin
        creator = orig_campaign.creator.username
        if "tofuadmin" not in creator:
            raise Exception(
                f"Campaign {orig_campaign.id} is owned by non-tofuadmin account {creator}"
            )
        orig_data = model_to_dict(orig_campaign)

        fields_to_remove = ["id", "created_at", "updated_at"]
        for field in fields_to_remove:
            orig_data.pop(field, None)

        # To remove certain fields
        copied_data = copy.deepcopy(orig_data)
        if not isinstance(self.review_user, TofuUser):
            raise Exception(f"Invalid review user: {self.review_user}")
        if is_copy_for_review:
            copied_data["creator"] = self.review_user
            copied_data["playbook"] = self.review_playbook
        else:
            copied_data["creator"] = TofuUser.objects.get(pk=orig_data["creator"])
            copied_data["playbook"] = Playbook.objects.get(pk=orig_data["playbook"])
        if one_target_only and copied_data["campaign_params"]["targets"]:
            copied_data["campaign_params"]["targets"] = (
                Benchmark.copy_one_target_for_campaign(
                    copied_data["campaign_params"]["targets"]
                )
            )

        copied_data["campaign_name"] = (
            f'{prefix}-copy({orig_campaign.id})-{orig_data["campaign_name"]}'
            if prefix
            else orig_data["campaign_name"]
        )
        # add orig_campaign
        copied_data["campaign_params"]["orig_campaign_id"] = orig_campaign.id
        # TODO: check campaign/content_group?
        copied_campaign = Campaign.objects.create(**copied_data)

        is_campaign_v3 = orig_campaign.campaign_params.get("is_campaign_v3", False)
        # campaign v3 case
        if is_campaign_v3 and not is_copy_for_review:
            action_copier = ActionCopier()
            action_copier.copy_actions_for_campaign(
                orig_campaign, copied_campaign, copy_components=True
            )

            copied_campaign.campaign_status["clone_status"] = {
                "status": "FINISHED",
                "update_time": timezone.now().isoformat(),
            }
            copied_campaign.save(update_fields=["campaign_status"])
        else:
            orig_content_groups = ContentGroup.objects.filter(
                campaign=orig_campaign
            ).order_by("id")
            for orig_content_group in orig_content_groups:
                # check if hideFromCampaign in content group params is true.
                if not orig_content_group.content_group_params.get(
                    "hideFromCampaign", False
                ):
                    self.copy_content_group(
                        orig_content_group, copied_campaign, is_copy_for_review, prefix
                    )

        copied_campaign_handler = CampaignHandler(copied_campaign)
        if not is_copy_for_review:
            copied_campaign_handler.update_content_collection_params()

        return copied_campaign

    def copy_content_group(
        self, orig_content_group, copied_campaign, is_copy_for_review, prefix=""
    ):
        # reject copy if the owner does not start with tofuadmin
        creator = orig_content_group.creator.username
        if "tofuadmin" not in creator:
            raise Exception(
                f"Content {orig_content_group.id} is owned by non-tofuadmin account {creator}"
            )
        orig_data = model_to_dict(orig_content_group)

        fields_to_remove = ["id", "created_at", "updated_at"]
        for field in fields_to_remove:
            orig_data.pop(field, None)

        # To remove certain fields
        copied_data = copy.deepcopy(orig_data)
        if not isinstance(self.review_user, TofuUser):
            raise Exception(f"Invalid review user: {self.review_user}")
        if is_copy_for_review:
            copied_data["creator"] = self.review_user
        else:
            copied_data["creator"] = TofuUser.objects.get(pk=orig_data["creator"])
        # make sure we have consistent targets if we manipulate the targets
        copied_data["content_group_params"]["targets"] = copy.deepcopy(
            copied_campaign.campaign_params["targets"]
        )
        copied_data["content_group_params"][
            "orig_content_group_id"
        ] = orig_content_group.id
        copied_data["campaign"] = copied_campaign

        copied_data["content_group_name"] = orig_data["content_group_name"]
        # TODO: check campaign/content_group?

        # Remove actions from copied_data
        copied_data.pop("action", None)
        # Create content group without actions
        copied_content_group = ContentGroup.objects.create(**copied_data)

        if not is_copy_for_review:
            content_group_handler = ContentGroupHandler(copied_content_group)
            content_group_handler.bulk_create_content()
        else:
            orig_contents = Content.objects.filter(
                content_group=orig_content_group
            ).order_by("id")
            for orig_content in orig_contents:
                self.copy_content(
                    orig_content, copied_content_group, is_copy_for_review, prefix
                )

        return copied_content_group

    def copy_content(
        self,
        orig_content,
        copied_content_group=None,
        is_copy_for_review=False,
        prefix="",
    ):
        # reject copy if the owner does not start with tofuadmin
        creator = orig_content.creator.username
        if "tofuadmin" not in creator:
            raise Exception(
                f"Content {orig_content.id} is owned by non-tofuadmin account {creator}"
            )

        orig_data = model_to_dict(orig_content)
        fields_to_remove = ["id", "created_at", "updated_at"]
        for field in fields_to_remove:
            orig_data.pop(field, None)

        # To remove certain fields
        copied_data = copy.deepcopy(orig_data)
        if not isinstance(self.review_user, TofuUser):
            raise Exception(f"Invalid review user: {self.review_user}")
        if is_copy_for_review:
            copied_data["creator"] = self.review_user
            copied_data["playbook"] = self.review_playbook
        else:
            copied_data["creator"] = TofuUser.objects.get(pk=orig_data["creator"])
            copied_data["playbook"] = Playbook.objects.get(pk=orig_data["playbook"])
        if copied_content_group:
            copied_data["content_group"] = copied_content_group
        elif "content_group" in orig_data:
            copied_data["content_group"] = ContentGroup.objects.get(
                pk=orig_data["content_group"]
            )
        if "orig_content_id" not in copied_data["content_params"]:
            copied_data["content_params"]["orig_content_id"] = orig_content.id
        copied_data["content_name"] = (
            f'{prefix}-copy({orig_content.id})-{orig_data["content_name"]}'
            if prefix
            else orig_data["content_name"]
        )
        # TODO: check campaign/content_group?
        copied_content = Content.objects.create(**copied_data)

        # Copy related ContentVariation objects only for review
        if is_copy_for_review:
            orig_variations = ContentVariation.objects.filter(content=orig_content)

            for variation in orig_variations:
                variation_data = model_to_dict(variation)
                for field in fields_to_remove:
                    variation_data.pop(field, None)
                variation_data["content"] = copied_content  # Link to new Content
                ContentVariation.objects.create(**variation_data)
        return copied_content

    def gen_auto_filename(self, content_ids, campaign_ids):
        current_time = datetime.datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d-%H-%M-%S")
        ids = "-".join([str(id) for id in content_ids + campaign_ids])
        return f"{formatted_time}_content-id-{ids}"

    def gen_auto_review_filename(self, prefix):
        current_time = datetime.datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d-%H-%M-%S")
        return f"{prefix}-{formatted_time}"

    # have to be consistent with gen_result
    def gen_review_keys(self):
        return [
            "content_id",
            "orig_content_id",
            "orig_campaign_id",
            "content_name",
            "content_type",
            "content_source_format",
            "target",
            "variation_number",
            "assets",
            "model",
            "original_component",
            "generated_component",
            "link_to_review",
            "link_to_anchor",
            "custom_instructions",
            "custom_instruction_generated_component",
            "link_to_review_custom_instruct",
            "link_to_langsmith",
        ]

    def gen_review_keys_repurpose(self):
        return [
            "content_id",
            "content_name",
            "content_type",
            "content_source_format",
            "variation_number",
            "component_id",
            "custom_instructions",
            "assets",
            "model",
            "repurpose_template",
            "generated_component",
            "link_to_review",
            "link_to_anchor",
            "link_to_langsmith",
        ]

    def gen_review_template_keys(self):
        return [
            "content_id",
            "orig_content_id",
            "orig_campaign_id",
            "content_name",
            "content_type",
            "content_source_format",
            "variation_number",
            "component_id",
            "model",
            "generated_component",
            "link_to_review",
            "link_to_anchor",
            "link_to_langsmith",
        ]

    def gen_review_result(
        self,
        content,
        model_name,
        num_of_variations,
        enable_custom,
        joint_generation=False,
        template_generation=False,
    ):
        playbook = content.playbook

        playbook_handler = PlaybookHandler.load_from_db_instance(playbook)
        content_generator = ContentGenerator(playbook_handler, content)
        content_generator.set_settings(
            foundation_model=model_name,
            num_of_variations=num_of_variations,
            enable_custom=enable_custom,
            joint_generation=joint_generation,
            template_generation=template_generation,
            session_id=self.session_id,
            session_tag=self.session_tag,
            save_variations=True,
        )

        content_variants = content_generator.gen()
        if not content_variants:
            raise Exception("Fail to generate variants")

    def gen_campaign_review_result(
        self,
        campaign,
        model_name,
        num_of_variations,
        enable_custom,
        joint_generation=False,
        template_generation=False,
    ):
        campaign_handler = CampaignHandler(campaign)
        collection_ids = campaign_handler.get_all_content_collection_ids()
        campaign_handler.gen(
            content_group_ids=[],
            content_ids=[],
            cache_key="",
            model_name=model_name,
            num_of_variations=num_of_variations,
            enable_custom=enable_custom,
            joint_generation=joint_generation,
            template_generation=template_generation,
            collection_ids=collection_ids,
            session_id=self.session_id,
            session_tag=self.session_tag,
            use_all_contents=True,
        )

    def get_campaign_review_data(
        self,
        campaign,
        model_name,
        num_of_variations,
        repurpose=False,
        orig_playbook_instance=None,
        enable_custom=False,
        template_generation=False,
        orig_campaign=None,
        campaign_with_custom_instruct=None,
    ):
        results = []
        for content_group in ContentGroup.objects.filter(campaign=campaign):
            for content in Content.objects.filter(content_group=content_group):
                # Get orig_content id
                if not orig_campaign:
                    orig_content_id = ""
                else:
                    orig_content_id = Benchmark.get_orig_content_id(
                        content, orig_campaign
                    )

                if campaign_with_custom_instruct:
                    content_group_with_custom_instruct = ContentGroup.objects.filter(
                        campaign=campaign_with_custom_instruct
                    ).first()
                    content_source_copy = content.content_params.get(
                        "content_source_copy", ""
                    )
                    content_with_custom_instruct = Content.objects.filter(
                        content_group=content_group_with_custom_instruct,
                        content_params__content_source_copy=content_source_copy,
                    ).first()
                else:
                    content_with_custom_instruct = None

                results += self.get_content_review_data(
                    content,
                    campaign,
                    model_name,
                    num_of_variations,
                    repurpose,
                    orig_playbook_instance,
                    enable_custom=enable_custom,
                    template_generation=template_generation,
                    orig_content_id=orig_content_id,
                    orig_campaign=orig_campaign,
                    content_with_custom_instruct=content_with_custom_instruct,
                )
        return results

    def get_content_review_data(
        self,
        content,
        campaign,
        model_name,
        num_of_variations,
        repurpose=False,
        orig_playbook_instance=None,
        enable_custom=False,
        template_generation=False,
        orig_content_id=None,
        orig_campaign=None,
        content_with_custom_instruct=None,
    ):
        content_page_link = (
            f"https://app.tofuhq.com/factory/content/{content.id}?stage=content"
        )
        results = []

        content_name = content.content_name
        components = content.content_group.components
        content_params = content.content_params
        content_variants = ContentVariation.objects.filter(content=content)
        logging.info(
            f"search result of varaitions is {content_variants} and content id {content.id}"
        )

        content_type = content_params["content_type"]
        content_source_format = content_params.get("content_source_format", "")

        prompt_params = content_params.get("custom_instructions", [])
        content_group = content.content_group
        prompt_params.extend(
            content_group.content_group_params.get("custom_instructions", [])
        )
        prompt_params.extend(campaign.campaign_params.get("custom_instructions", []))

        # repurpose template
        repurpose_template_content_source_copy = (
            content.content_group.content_group_params.get(
                "repurpose_template_content_source_copy", None
            )
        )
        if repurpose_template_content_source_copy:
            repurpose_template = get_template(repurpose_template_content_source_copy)
        else:
            repurpose_template = {}

        orig_campaign_id = orig_campaign.id if orig_campaign else ""

        if content_with_custom_instruct:
            custom_instruct_content_page_link = f"https://app.tofuhq.com/factory/content/{content_with_custom_instruct.id}?stage=content"
            custom_content_variant = ContentVariation.objects.filter(
                content=content_with_custom_instruct
            ).first()
        else:
            custom_instruct_content_page_link = ""
            custom_content_variant = None

        for content_variant in content_variants:
            logging.info(f"content_variant is {content_variant}")
            target_params = content_variant.params["targets"]
            for order_of_variation in range(num_of_variations):
                keys = list(components.keys())

                # sort component keys by time_added and isEmailSubject and order
                keys.sort(
                    key=lambda x: (
                        not components[x]
                        .get("meta", {})
                        .get("isEmailSubject", False),  # False < True
                        components[x].get("meta", {}).get("order", 0),
                        components[x].get("meta", {}).get("time_added", 0),
                    )
                )

                for component_id in keys:
                    component_prompt_params = copy.deepcopy(prompt_params)
                    component = components[component_id]
                    metadata = component.get("meta", {})
                    component_prompt_params.extend(
                        metadata.get("component_params", {}).get(
                            "custom_instructions", []
                        )
                    )
                    # for each prompt param, if it is a dict get 'instruction', else keep the string value.
                    if enable_custom:
                        component_prompt_params = [
                            (
                                param.get("instruction", "")
                                if isinstance(param, dict)
                                else param
                            )
                            for param in component_prompt_params
                        ]
                    else:
                        component_prompt_params = []
                    component_prompt_params = [p for p in component_prompt_params if p]
                    component_variation = content_variant.variations[component_id]
                    generated_variations = component_variation["meta"]["variations"][
                        order_of_variation
                    ]
                    variation_text = generated_variations.get("text", "")
                    component_type = component.get("meta", {}).get(
                        "component_type", "message"
                    )
                    if component_type != "message" and component_type != "unspecified":
                        variation_text = f"{component_type}: {variation_text}"
                    request_id = generated_variations.get("meta", {}).get("request_id")
                    if request_id:
                        langsmith_link = fetch_langsmith_links(request_id)
                    else:
                        langsmith_link = ""
                    if custom_content_variant:
                        custom_content_component_variation = (
                            custom_content_variant.variations[component_id]
                        )
                        custom_instuction_generated_variations = (
                            custom_content_component_variation["meta"]["variations"][
                                order_of_variation
                            ]
                        )
                    else:
                        custom_instuction_generated_variations = {}
                    anchor_content_link = ""
                    asset_names = get_asset_names_from_content(content)
                    if repurpose:
                        repurpose_asset_params = (
                            get_repurpose_asset_params_from_content(content)
                        )
                        for asset_param in repurpose_asset_params:
                            for p in asset_param["assets"]:
                                for key, value in p.items():
                                    if key not in asset_names:
                                        asset_names[key] = []
                                    asset_names[key].append(value)
                    for key1, key2 in asset_names.items():
                        # key2 is list of asset names.
                        if not isinstance(key2, list):
                            raise ValueError(f"key2 is not a list: {key2}")
                        for l2_key in key2:
                            asset_link = fetch_asset(
                                orig_playbook_instance,
                                key1,
                                l2_key,
                            )
                            if asset_link:
                                anchor_content_link += asset_link + "\n"
                    if repurpose:

                        if component_type == "email subject":
                            component_repurpose_template = repurpose_template.get(
                                "subjectLine"
                            )
                        elif component_type == "email body":
                            component_repurpose_template = repurpose_template.get(
                                "body"
                            )
                        else:
                            component_repurpose_template = repurpose_template.get(
                                "text"
                            )
                        results.append(
                            [
                                content.id,
                                content_name,
                                self.get_content_type(
                                    content_type, content_source_format
                                ),
                                content_source_format,
                                f"variation-{order_of_variation+1}",
                                component_id,
                                str(component_prompt_params),
                                str(asset_names),
                                model_name,
                                str(component_repurpose_template),
                                variation_text,
                                content_page_link,
                                anchor_content_link,
                                langsmith_link,
                            ]
                        )
                    elif template_generation:
                        results.append(
                            [
                                content.id,
                                orig_content_id,
                                orig_campaign_id,
                                content_name,
                                self.get_content_type(
                                    content_type, content_source_format
                                ),
                                content_source_format,
                                f"variation-{order_of_variation+1}",
                                component_id,
                                model_name,
                                variation_text,
                                content_page_link,
                                anchor_content_link,
                                langsmith_link,
                            ]
                        )
                    else:
                        results.append(
                            [
                                content.id,
                                orig_content_id,
                                orig_campaign_id,
                                content_name,
                                self.get_content_type(
                                    content_type, content_source_format
                                ),
                                content_source_format,
                                str(target_params),
                                f"variation-{order_of_variation+1}",
                                str(asset_names),
                                model_name,
                                component.get("text", ""),
                                variation_text,
                                content_page_link,
                                anchor_content_link,
                                str(component_prompt_params),
                                custom_instuction_generated_variations.get("text", ""),
                                custom_instruct_content_page_link,
                                langsmith_link,
                            ]
                        )
        return results

    # helper
    @staticmethod
    def copy_one_target_for_campaign(targets):
        res = {}
        for k, v in targets[0].items():
            res[k] = [v[0]] if isinstance(v, list) else [v]
        logging.info(f"copy targets from {targets} to {res}")
        return [res]

    def get_content_type(self, content_type, content_source_format):
        if content_source_format == "PDF":
            return "PDF"
        elif "Email" in content_type:
            return "Email"
        else:
            return content_type

    # helper
    @staticmethod
    def get_orig_content_id(content, orig_campaign=None):
        orig_content_id = content.content_params.get("orig_content_id", "")
        if not orig_content_id and orig_campaign:
            orig_content_group = ContentGroup.objects.filter(
                campaign=orig_campaign, components=content.content_group.components
            ).first()
            orig_content = Content.objects.filter(
                content_group=orig_content_group,
                components=content.content_group.components,
            ).first()
            if orig_content:
                orig_content_id = orig_content.id
        if not orig_content_id:
            logging.error(f"Fail to find orig content for content {content.id}")
        return orig_content_id


benchmark_review_personalization_campaigns_small_set = [
    # PDFs (Brochure, Whitepaper, Case Study, eBooks)
    527,  # 1195, # Igloo - Sales Deck Core Test - 1:Few ABM by industry
    # Short Form Text (Emails, Social Media Post)
    610,  # 1777, # Stampli SDR Email - 1:1 ABM for  Vet Clinics
    # Long Form Text (Blog post, longer landing page)
    518,  # 1204, # Igloo - Personalize blog post meant for manufacturing industry for three other industries (1:Few ABM)
    # Contact-level email gen
    4480,
    # Levitate email
    5529,
    # 50Pros LP
    10697,
    # converted campaign v3
    177859,  # copy of 527
    178264,  # copy of 518
    178265,  # copy of 4480
    178266,  # copy of 10697
]

benchmark_review_template_campaigns = [
    # Email - SDR
    8363,  # Userlane - Boeing
    8364,  # 1Password - Away
    8365,  # Levitate - Perry & Neblett
    8366,  # Avanan - Primed
    8367,  # Globality - FedEx
]

benchmark_review_repurpose_campaigns = [
    # Tofu AMA Sterling Snow Transcript
    2738,  # 16801 LinkedIn, 16798 Blog Post
    # Bluecore Blog
    2740,  # 16808 Social post
    # Golioth Reference Design
    2741,  # 168945 Landing Page
    # Wunderkind Guide
    2745,  # 16946 Email-Marketing 16947 Email-SDR 16948 Blog Post
    # AMA Blog posts
    3969,
    4024,
    4025,
    4050,
    # Orb eBook
    11144,
    # Creating an InboundType Campaign from a Whitepaper
    # Creating multiple Blog Posts from a Whitepaper
    96090,
    # Creating an InboundType Campaign from a Audio or Video Recording
    96133,
    # Creating a Case Study from an Audio or Video Recording
    13744,
    # Creating a Blog Post from 3rd Party Content
    # Creating a Blog Post from 3rd Party Podcast
    92586,
    75652,
    74360,
    # converted campaign v3
    # 177925,  # copy of 2738
    # 178263,  # copy of 2745
    # 178267,  # copy of 11144
    # 178268,  # copy of 2741
    203466,  # Email template test
    203482,  # Blog post template test
]


benchmark_review_personalization_campaigns = [
    # Landing Page - change a few sections for personalization
    520,  # 1783, # Stampli Vet Clinic Landing Page - 1:1 ABM
    643,  # Group 6 GPT4 Med Facilities Landing Page (vJW Test)
    649,  # Call Analytics LP
    # PDFs (Brochure, Whitepaper, Case Study, eBooks)
    527,  # 1195, # Igloo - Sales Deck Core Test - 1:Few ABM by industry
    # Short Form Text (Emails, Social Media Post)
    610,  # 1777, # Stampli SDR Email - 1:1 ABM for  Vet Clinics
    # Long Form Text (Blog post, longer landing page)
    518,  # 1204, # Igloo - Personalize blog post meant for manufacturing industry for three other industries (1:Few ABM)
    # Contact-level email gen
    4480,
    # Levitate email
    5529,
    # Target name capitalization
    6599,
    # Wunderkind custom instruction test
    8682,
    8685,
    # 8686, # NEL
    # 9180, # NEL
    # 9183, # NEL
    # 9186, # NEL
    # Vividly LP
    9661,
    # 50Pros LP
    # 10697, # NEL
    # Avanan SDR Email
    10722,
    # Seomonitor LP
    # 11143, NEL
    76103,
    # converted campaign v3
    # 177859,  # copy of 527
    # 178264,  # copy of 518
    # 178265,  # copy of 4480
    # 178266,  # copy of 10697
]

benchmark_review_template_campaigns_small_set = [
    # Email - SDR
    8363,  # Userlane - Boeing
]

benchmark_review_repurpose_campaigns_small_set = [
    # Tofu AMA Sterling Snow Transcript
    2738,  # 16801 LinkedIn, 16798 Blog Post
    # Bluecore Blog
    2740,  # 16808 Social post
    # Golioth Reference Design
    2741,  # 168945 Landing Page
    # Wunderkind Guide
    2745,  # 16946 Email-Marketing 16947 Email-SDR 16948 Blog Post
    # AMA Blog posts
    3969,
    # Orb eBook
    11144,
    # converted campaign v3
    177925,  # copy of 2738
    178263,  # copy of 2745
    178267,  # copy of 11144
    178268,  # copy of 2741
    203466,  # Email template test
    # 211860,  # Sales deck test
]

benchmark_review_repurpose_campaigns_small_set_llm_tests = [
    # Tofu AMA Sterling Snow Transcript
    2738,  # 16801 LinkedIn, 16798 Blog Post
    # Bluecore Blog
    2740,  # 16808 Social post
    # Golioth Reference Design
    2741,  # 168945 Landing Page
    # Wunderkind Guide
    2745,  # 16946 Email-Marketing 16947 Email-SDR 16948 Blog Post
    # AMA Blog posts
    3969,
    # Orb eBook
    11144,
    # converted campaign v3
    177925,  # copy of 2738
    178263,  # copy of 2745
    178267,  # copy of 11144
    178268,  # copy of 2741
    203466,  # Email template test
    # 211860,  # Sales deck test
]
