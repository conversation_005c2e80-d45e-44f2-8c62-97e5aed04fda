import copy
import logging
from typing import Dict, Iterable, Optional

from ...models import Content, ContentGroup, TargetInfo
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ...sync.content_sync.content_group_hubspot_sync import ContentGroupHubspotSyncer
from ...sync.utils.export_settings_wrapper import ExportSettingsWrapper
from ...utils import CloudWatchMetrics
from ..integration_sync.hubspot_integration import HubspotIntegration
from .export_handler import BaseExportHandler, EmailTemplateInput
from .html_generator.html_utils import are_html_equivalent
from .html_generator.hubspot_html_helpers import HubspotHtmlGenerator


class HubspotEmailExportHandler(BaseExportHandler):
    def __init__(self, playbook_id: int, source_platform: PlatformType) -> None:
        super().__init__(playbook_id, source_platform)
        self.integration = HubspotIntegration(
            self.paragon_wrapper.get_hubspot_agent(), None, None
        )

    def create_or_update_export_settings(
        self, content_group: ContentGroup, save_to_db: bool = True, **settings
    ):
        """
        Preferred entry point for creating or updating export settings for a content group.
        This will always use the merging logic to ensure settings are initialized and updated as needed.
        Args:
            content_group: The ContentGroup instance to operate on.
            save_to_db: Whether to persist the changes to the database (default: True).
            **settings: Any settings to merge into the export settings (e.g., componentsSetting, targetsSetting, advancedSetting).
        Returns:
            The updated export settings dictionary.
        """
        # 1. Always create a new export settings
        new_export_settings = self.create_export_settings(
            content_group, save_to_db=False, **settings
        )
        # 2. Merge update_settings into export_settings if needed
        export_settings_wrapper = ExportSettingsWrapper(content_group)
        current_export_settings = export_settings_wrapper.export_settings
        if current_export_settings:
            new_export_settings = self.update_export_settings(
                content_group, new_export_settings, save_to_db=False, **settings
            )
        # 3. Save if needed
        if save_to_db:
            content_group.content_group_params["export_settings"] = new_export_settings
            content_group.save()
            content_group.refresh_from_db()
        return new_export_settings

    # This is to update the export settings, asssume there is already an initial
    # version of export settings, and user would like to change some settings
    def update_export_settings(
        self,
        content_group: ContentGroup,
        new_export_settings: dict,
        save_to_db: bool = True,
        **settings,
    ):
        export_type = settings.get("export_type", "dynamic")
        update_settings = (
            new_export_settings.get("hubspot", {}).get("email", {}).get(export_type, {})
        )
        if not update_settings:
            logging.error(
                f"No update settings found for content group {content_group.id}, this is not an update"
            )
            raise ValueError(
                f"No update settings found for content group {content_group.id}, this is not an update"
            )
        export_settings_wrapper = ExportSettingsWrapper(content_group)
        export_settings = export_settings_wrapper.export_settings
        updated_export_settings = copy.deepcopy(export_settings)
        current_targets_setting = copy.deepcopy(export_settings_wrapper.target_settings)
        current_components_setting = copy.deepcopy(
            export_settings_wrapper.components_setting
        )
        current_advanced_setting = copy.deepcopy(
            export_settings_wrapper.advanced_setting
        )

        if not current_targets_setting:
            logging.error(
                f"No targets setting found for content group {content_group.id}, this is not an update"
            )
            raise ValueError(
                f"No targets setting found for content group {content_group.id}, this is not an update"
            )
        if not current_components_setting:
            logging.error(
                f"No components setting found for content group {content_group.id}, this is not an update"
            )
            raise ValueError(
                f"No components setting found for content group {content_group.id}, this is not an update"
            )
        if not current_advanced_setting:
            logging.error(
                f"No advanced setting found for content group {content_group.id} this is not an update"
            )
            raise ValueError(
                f"No advanced setting found for content group {content_group.id} this is not an update"
            )
        update_components_setting = update_settings.get("componentsSetting", {})
        update_targets_setting = update_settings.get("targetsSetting", [])
        update_advanced_setting = update_settings.get("advancedSetting", {})
        if update_components_setting:
            logging.info(
                f"Updating components setting for content group {content_group.id}"
            )
            # we only allow update the token of the component
            for update_cid, update_token in update_components_setting.items():
                if update_cid in current_components_setting:
                    if update_token == "skip":
                        current_components_setting[update_cid] = "skip"
                    else:
                        current_components_setting[update_cid] = update_token
            updated_export_settings["componentsSetting"] = current_components_setting
        if update_targets_setting:
            logging.info(
                f"Updating targets setting for content group {content_group.id}"
            )
            # Use the new merge function for efficient update/add
            current_targets_setting = self._merge_targets_setting(
                current_targets_setting, update_targets_setting
            )
            updated_export_settings["targetsSetting"] = current_targets_setting
        if update_advanced_setting:
            logging.info(
                f"Updating advanced setting for content group {content_group.id}"
            )
            # we only allow update the email type and email footer
            current_advanced_setting["emailType"] = update_advanced_setting.get(
                "emailType", current_advanced_setting["emailType"]
            )
            current_advanced_setting["emailFooter"] = update_advanced_setting.get(
                "emailFooter", current_advanced_setting["emailFooter"]
            )
            updated_export_settings["advancedSetting"] = current_advanced_setting
        if save_to_db:
            content_group.content_group_params["export_settings"] = (
                updated_export_settings
            )
            content_group.save()
            content_group.refresh_from_db()
            logging.info(
                f"Updated export settings for content group {content_group.id}"
            )
        return {
            export_settings_wrapper.export_destination: {
                "email": {export_settings_wrapper.export_type: updated_export_settings}
            },
            "exportType": export_settings_wrapper.export_type,
            "exportDestination": export_settings_wrapper.export_destination,
        }

    # ToDo: we need to evaluate if this logic in action.update_input or action.execute()
    # This is to create a brand new export settings, as the first version of export settings
    def create_export_settings(
        self, content_group: ContentGroup, save_to_db: bool = True, **init_settings
    ):
        """
        Auto-populate export settings for a new HubSpot dynamic email export using only content_group.
        """
        export_type = init_settings.get("export_type", "dynamic")
        init_advanced_setting = init_settings.get(
            "advanced_setting",
            {
                "emailType": "automated",
                "emailFooter": True,
            },
        )
        # 1. Build componentsSetting from content_group.components (dynamic export)
        components_setting = {}
        components = content_group.components or {}
        # we only support dynamic export for now
        destination = "hubspot"
        content_group_id = content_group.id
        # Sort components by time_added if present
        sorted_component_ids = sorted(
            components.keys(),
            key=lambda cid: components[cid].get("meta", {}).get("time_added", 0),
        )
        for idx, component_id in enumerate(sorted_component_ids):
            component = components[component_id]
            # For HubSpot, use tofu_email_{contentGroup.id}_{idx+1}
            token = f"tofu_email_{content_group_id}_{idx+1}"
            # If the component is a link, skip token assignment (use 'skip')
            if component.get("link"):
                components_setting[component_id] = "skip"
            else:
                components_setting[component_id] = token

        # 2. Build targetsSetting from all Content in the group
        targets_setting = []
        contents = Content.objects.filter(content_group=content_group)
        # Build lookup dict
        target_info_lookup = self._build_target_info_lookup(
            self.playbook_instance.id, contents
        )
        for content in contents:
            content_id = content.id
            content_params = content.content_params or {}
            targets = content_params.get("targets", {})
            if not targets:
                continue
            # Sort targets by key for deterministic order
            target_entries = sorted(targets.items(), key=lambda x: x[0])
            targetLabels = []
            targetNames = []
            targetListNames = []
            emailNames = []
            platform_ids = {}
            for l1_key, l2_key in target_entries:
                targetLabels.append(f"{l1_key}: {l2_key}")
                targetNames.append(l2_key)
                targetListNames.append(l1_key)
                # Get platform IDs from TargetInfo
                target_info = target_info_lookup.get((l1_key, l2_key))
                meta = target_info.meta if target_info and target_info.meta else {}
                if not meta:
                    logging.error(f"No meta found for target {l1_key}:{l2_key}")
                    continue
                # HubSpot
                hubspot_record_id = meta.get("hubspot_record_id", "")
                hubspot_object_type = meta.get("hubspot_object_type", "")
                if hubspot_record_id and hubspot_object_type:
                    platform_ids["hubIds"] = {
                        "hubspot_record_id": hubspot_record_id,
                        "hubspot_object_type": hubspot_object_type,
                    }
            # Email name logic
            if export_type == "static":
                emailNames = targetNames.copy()
            else:
                if not emailNames:
                    emailNames = [content_group.content_group_name or "Tofu Email"]
            # isExportTarget logic for dynamic export: must have hubIds
            is_export_target = bool(platform_ids.get("hubIds"))
            targets_setting.append(
                {
                    "contentId": content_id,
                    "targetLabels": targetLabels,
                    "targetNames": targetNames,
                    "targetListNames": targetListNames,
                    "isExportTarget": is_export_target,
                    "emailName": " | ".join(emailNames),
                    "exportStatus": "Not Started",
                    **platform_ids,
                }
            )

        # 3. Advanced setting (default or from params)
        advanced_setting = init_advanced_setting

        # 4. Assemble export settings dict
        export_settings = {
            "hubspot": {
                "email": {
                    export_type: {
                        "exportType": export_type,
                        "destination": destination,
                        "targetsSetting": targets_setting,
                        "componentsSetting": components_setting,
                        "advancedSetting": advanced_setting,
                    }
                }
            },
            "exportType": export_type,
            "exportDestination": destination,
        }

        if save_to_db:
            content_group.content_group_params["export_settings"] = export_settings
            content_group.save()
            content_group.refresh_from_db()
            logging.info(
                f"Created export settings for content group {content_group.id}"
            )
        return export_settings

    def _update_draft_url(
        self, content_group: ContentGroup, export_response: Optional[Dict]
    ) -> Optional[str]:
        """
        Update the draft URL for each target in the content group's export settings.

        Args:
            content_group: The ContentGroup object to update
            export_response: The response from the HubSpot email creation API

        This function gets the draft URL from the export response and updates it in the
        content group's export settings for each target, then saves the content group.
        """
        try:
            if not export_response:
                logging.error(
                    f"Failed to update draft URL for content group {content_group.id}: no export response"
                )
                return

            # Get draft URL from the export response
            try:
                draft_url = self.integration.get_hubspot_email_draft_url(
                    export_response
                )
                if not draft_url:
                    logging.warning(
                        f"No draft URL found in export response for content group {content_group.id}"
                    )
                    return
            except Exception as e:
                logging.exception(f"Error getting draft URL from export response: {e}")
                return

            # Get targets setting from content group params
            try:
                # Access nested dictionary in a more readable way
                export_settings_wrapper = ExportSettingsWrapper(content_group)
                targets_setting = export_settings_wrapper.target_settings

                # Update each target with the draft URL
                for target in targets_setting:
                    target["draftURL"] = draft_url
                    target["hasJustBeenExported"] = True

                # Save changes to the content group
                content_group.save()
                content_group.refresh_from_db()
                logging.info(
                    f"Updated draft URL for content group {content_group.id}: {draft_url}"
                )
                return draft_url
            except Exception as e:
                logging.exception(
                    f"Error updating draft URL in content group {content_group.id}: {e}"
                )
        except Exception as e:
            logging.exception(
                f"Unexpected error updating draft URL for content group {content_group.id}: {e}"
            )

    def _extract_and_validate_export_settings(self, content_group):
        export_settings_wrapper = ExportSettingsWrapper(content_group)
        components_setting = export_settings_wrapper.components_setting
        if not components_setting:
            logging.error(
                f"Error: components setting not found for content group: {content_group.id}"
            )
            return None, None, None, None
        targets_setting = export_settings_wrapper.target_settings
        if not targets_setting:
            logging.error(
                f"Error: targetsSetting is empty for content group: {content_group.id}"
            )
            return None, None, None, None
        advanced_setting = export_settings_wrapper.advanced_setting
        unique_object_types = set(
            t["hubIds"]["hubspot_object_type"]
            for t in targets_setting
            # if t.get('isExportTarget') and
            if t.get("hubIds", {}).get("hubspot_object_type")
        )
        if not unique_object_types:
            logging.error("No selected target is connected with Hubspot.")
            raise ValueError("No selected target is connected with Hubspot.")
        if len(unique_object_types) > 1:
            logging.error("Only one HubSpot object type supported at a time.")
            raise ValueError("Only one HubSpot object type supported at a time.")
        hub_object_type = list(unique_object_types)[0]
        return components_setting, targets_setting, advanced_setting, hub_object_type

    def _create_crm_group_and_properties(self, hub_object_type, components_setting):
        try:
            self.integration.agent._create_crm_group(hub_object_type, "tofu", "tofu")
            for p13n_token in components_setting.values():
                self.integration.agent.create_crm_field(
                    hub_object_type, p13n_token, p13n_token
                )
            logging.info(
                f"Created CRM group and properties for object type {hub_object_type}"
            )
        except Exception as e:
            logging.exception(f"Failed to create CRM group/properties: {e}")
            raise

    def _compare_email_template_input(
        self,
        fe_input: Optional[EmailTemplateInput],
        be_html: str,
        be_file_name: str,
        be_subject: str,
        be_type: str,
    ):
        try:
            if not fe_input:
                logging.info(
                    "No FE email_template_input provided; using BE-generated values."
                )
                return {
                    "html_content": be_html,
                    "email_file_name": be_file_name,
                    "email_subject": be_subject,
                    "email_type": be_type,
                    "source": "BE",
                }
            # Compare and log differences
            diffs = {}
            if not are_html_equivalent(fe_input["html_content"], be_html):
                diffs["html_content"] = {"fe": fe_input["html_content"], "be": be_html}
            if fe_input["email_file_name"] != be_file_name:
                diffs["email_file_name"] = {
                    "fe": fe_input["email_file_name"],
                    "be": be_file_name,
                }
            if fe_input["email_subject"] != be_subject:
                diffs["email_subject"] = {
                    "fe": fe_input["email_subject"],
                    "be": be_subject,
                }
            if fe_input["email_type"] != be_type:
                diffs["email_type"] = {"fe": fe_input["email_type"], "be": be_type}
            if diffs:
                logging.error(f"FE/BE email template input mismatch: {diffs}")
                CloudWatchMetrics.put_metric(
                    "HubspotExportEmailTemplateInputMismatch",
                    1,
                    dimensions=[
                        {
                            "Name": "playbook_id",
                            "Value": str(self.playbook_instance.id),
                        },
                        {
                            "Name": "source_platform",
                            "Value": str(self.source_platform),
                        },
                    ],
                )
            else:
                logging.info("FE and BE email template input match.")
            # Always use FE value for now
            return {
                "html_content": fe_input["html_content"],
                "email_file_name": fe_input["email_file_name"],
                "email_subject": fe_input["email_subject"],
                "email_type": fe_input["email_type"],
                "source": "FE",
            }
        except Exception as e:
            logging.exception(
                f"Exception in _compare_email_template_input: {e}. Falling back to BE-generated values."
            )
            return {
                "html_content": be_html,
                "email_file_name": be_file_name,
                "email_subject": be_subject,
                "email_type": be_type,
                "source": "BE",
            }

    def do_export_new(
        self,
        content_group: ContentGroup,
        email_template_input: Optional[EmailTemplateInput] = None,
    ):
        """
        Export a new content group to HubSpot as an email.

        Args:
            content_group: The ContentGroup to export
            email_template_input: Optional input from the frontend for email template
        """
        try:
            # Step 1: Extract and validate export settings
            components_setting, targets_setting, advanced_setting, hub_object_type = (
                self._extract_and_validate_export_settings(content_group)
            )
            if components_setting is None:
                logging.error(
                    f"Failed to extract valid export settings for content group {content_group.id}"
                )
                return

            # Step 2: Create CRM group and properties (skip components with links)
            logging.info("Creating CRM group and properties in HubSpot...")
            filtered_components_setting = {
                cid: token
                for cid, token in components_setting.items()
                if not (content_group.components.get(cid, {}).get("link"))
            }
            self._create_crm_group_and_properties(
                hub_object_type, filtered_components_setting
            )

            # Step 3: Generate HTML and subject, create template/email
            logging.info(
                "Generating HTML and subject, and creating HubSpot template/email..."
            )
            html_generator = HubspotHtmlGenerator(
                content_group, filtered_components_setting, hub_object_type
            )
            html_content, email_subject = html_generator.generate_html_and_subject()

            # Generate email_file_name in the new style with "Tofu" prefix
            email_file_name = html_generator.generate_email_file_name()
            email_type = advanced_setting.get("emailType", "automated")

            # Compare FE and BE values, always use FE for now
            template_input = self._compare_email_template_input(
                email_template_input,
                html_content,
                email_file_name,
                email_subject,
                email_type,
            )

            # Create template and marketing email
            export_response = self.integration.create_marketing_email(
                template_input["html_content"],
                template_input["email_file_name"],
                template_input["email_subject"],
                template_input["email_type"],
            )
            logging.info(
                f"Successfully created HubSpot marketing email: {export_response}"
            )

            # Step 4: Per-target CRM property update (delegate to ContentGroupHubspotSyncer)
            logging.info(
                "Updating CRM properties for each target via ContentGroupHubspotSyncer..."
            )
            self.do_export_existing(content_group)

            # Step 5: Update draft URL for each target
            draft_url = self._update_draft_url(content_group, export_response)
            logging.info(
                f"Successfully updated draft URL for content group {content_group.id}: {draft_url}"
            )

            return export_response
        except Exception as e:
            logging.exception(
                f"Failed to export content group {content_group.id} to HubSpot: {e}"
            )
            raise

    def do_export_existing(self, content_group: ContentGroup):

        components_setting, _, _, hub_object_type = (
            self._extract_and_validate_export_settings(content_group)
        )
        if components_setting is None:
            logging.error(
                f"Failed to extract and validate export settings for content group {content_group.id}"
            )
            return
        # Use ContentGroupHubspotSyncer to update CRM properties for each target
        logging.info(
            "Updating CRM properties for each target via ContentGroupHubspotSyncer..."
        )
        syncer = ContentGroupHubspotSyncer(
            content_group,
            record_type=hub_object_type,
            record_type_plural=hub_object_type,
        )
        unexported_contents = syncer.get_unexported_contents()
        if not unexported_contents:
            logging.info(
                f"No unexported contents for content group {content_group.id}."
            )
            return
        logging.info(
            f"Updating CRM properties for {len(unexported_contents)} unexported contents..."
        )
        self._mark_export_status_in_progress(content_group, unexported_contents)
        syncer.export(contents=unexported_contents)
        logging.info(
            f"Successfully updated CRM properties for all unexported contents for content group {content_group.id}."
        )

    def _build_target_info_lookup(self, playbook_id, contents):
        """
        Given a playbook_id and a queryset of Content, extract all (l1_key, l2_key) pairs from their targets,
        fetch all relevant TargetInfo objects, and return a dict mapping (l1_key, l2_key) -> TargetInfo.
        This is to prevent N+1 queries when fetching TargetInfo objects in the do_export_existing method.
        """
        target_info_keys = set()
        for content in contents:
            content_params = content.content_params or {}
            targets = content_params.get("targets", {})
            for l1_key, l2_key in targets.items():
                target_info_keys.add((l1_key, l2_key))
        if not target_info_keys:
            return {}
        target_infos = TargetInfo.objects.filter(
            target_info_group__playbook_id=playbook_id,
            target_info_group__target_info_group_key__in=[
                k[0] for k in target_info_keys
            ],
            target_key__in=[k[1] for k in target_info_keys],
        )
        return {
            (ti.target_info_group.target_info_group_key, ti.target_key): ti
            for ti in target_infos
        }

    def _merge_targets_setting(self, current_targets_setting, update_targets_setting):
        """
        Efficiently update the current_targets_setting list of dicts with update_targets_setting.
        Updates existing targets by contentId and adds new ones if not present.
        """
        # Build a mapping from contentId to update dict
        update_targets_map = {
            t["contentId"]: t for t in update_targets_setting if "contentId" in t
        }
        # Build a mapping from contentId to current target dict
        current_targets_map = {
            t.get("contentId"): t for t in current_targets_setting if "contentId" in t
        }

        # Prepare dicts for updates and adds
        targets_to_update = {}
        targets_to_add = {}

        for cid, update_dict in update_targets_map.items():
            if cid in current_targets_map:
                targets_to_update[cid] = update_dict
            else:
                targets_to_add[cid] = update_dict

        # Update existing targets
        for cid, update_dict in targets_to_update.items():
            target = current_targets_map[cid]
            if "emailName" in update_dict and update_dict["emailName"] is not None:
                target["emailName"] = update_dict["emailName"]
            if (
                "isExportTarget" in update_dict
                and update_dict["isExportTarget"] is not None
            ):
                target["isExportTarget"] = update_dict["isExportTarget"]

        # Add new targets
        for cid, add_dict in targets_to_add.items():
            current_targets_setting.append(add_dict)

        return current_targets_setting
