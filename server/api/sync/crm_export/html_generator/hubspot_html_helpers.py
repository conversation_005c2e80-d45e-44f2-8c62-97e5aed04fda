import logging
import re
from datetime import datetime
from datetime import timezone as dt_timezone
from typing import Any, Dict, Optional, Tuple

from bs4 import BeautifulSoup, NavigableString, Tag

from ....models import Campaign
from ...utils.export_settings_wrapper import ExportSettingsWrapper
from .html_utils import (
    clean_html_attributes,
    fetch_clean_content_source_text,
    get_hubl_for_text_module,
    get_linked_url_token,
)


class HubspotHtmlGenerator:

    EMAIL_FOOTER_HTML = """
<p style='font-family: Geneva, Verdana, Arial, Helvetica, sans-serif; font-size: 12px; line-height: 1.34em; display: block; text-align: left;'>
  <br>  
  <span style='color: #cccccc;'>
    {{ site_settings.company_name }} | {{ site_settings.company_street_address_1 }}, {{ site_settings.company_city }}, {{ site_settings.company_state }} 
  </span>
  <br>
  <span style='color: #cccccc;'>
    Update your <a target='_blank' href='{{ unsubscribe_link }}' style='text-decoration: underline; color: #cccccc;' data-unsubscribe='true' rel='noopener'>email preferences</a>
  </span>
</p>
    """
    TEXT_EMAIL_FOOTER_MODULE = """{% module "preview_text",\n    path="@hubspot/rich_text",\n    html="<div id=\'preview_text\' style=\'display:none;font-size:1px;color:{{background_color}};line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;\'>{% text \'preview_text\' label=\'Preview Text\', value=\'\', no_wrapper=True %}</div>" %}"""

    def __init__(
        self,
        content_group: Any,
        components_setting: Dict,
        hub_object_type: str,
        content_source_format: str = "Text",
    ):
        self.content_group = content_group
        self.components_setting = components_setting
        self.hub_object_type = hub_object_type
        self.content_source_format = content_source_format
        self.export_settings_wrapper = ExportSettingsWrapper(content_group)
        if not content_group.components:
            raise ValueError(f"Content group {content_group.id} has no components")
        self.content_group_components = content_group.components
        if not content_group.campaign:
            raise ValueError(f"Content group {content_group.id} has no campaign")
        self.campaign: Campaign = content_group.campaign

    def generate_email_file_name(self) -> str:
        """
        Generate a standardized email file name with timestamp.
        Ensures consistent timestamp format that matches the module name in the template.

        Args:
            content_group_name: The name of the content group

        Returns:
            String with formatted email file name
        """
        # Using UTC time to match frontend behavior
        now = datetime.now(dt_timezone.utc)

        # Format timestamp exactly as frontend does - use 'Z' suffix for UTC time
        # Make sure the timestamp has millisecond precision
        iso_time = now.isoformat(timespec="milliseconds").replace("+00:00", "Z")

        # Format the complete filename to match frontend pattern
        return f"Tofu {self.content_group.content_group_name} ({iso_time})"

    def generate_html_and_subject(self) -> Tuple[str, str]:
        """
        Generate HTML content and subject line for HubSpot email from content group.
        Matches frontend logic in useExportHubspotEmail.ts.

        Returns:
            Tuple of (html_content, email_subject)
        """
        # Step 1: Fetch and prepare HTML content and subject
        html_element, subject_html_element, email_footer = (
            self._fetch_html_content_and_subject()
        )
        if html_element is None:
            return "", ""

        # Step 2: Process components and replace with tokens
        html_element, email_subject_html_element = (
            self._replace_tofu_components_with_p13n_tokens(
                html_element,
                subject_html_element,
            )
        )

        logging.info(
            f"Generated HTML and subject for content_group {self.content_group.id}"
        )

        # Step 3: Finalize the HTML and subject
        base_target = self.export_settings_wrapper.target_settings[0]
        advanced_setting = self.export_settings_wrapper.advanced_setting
        export_response, html_content, email_subject = (
            self._finalize_hubspot_email_export(
                html_element,
                email_subject_html_element,
                base_target,
                self.content_source_format,
                advanced_setting,
            )
        )
        return str(html_content), str(email_subject)

    def _fetch_html_content_and_subject(
        self,
    ) -> Tuple[BeautifulSoup, BeautifulSoup, str]:
        content_params = getattr(self.content_group, "content_group_params", {}) or {}
        content_source_copy = content_params.get("content_source_copy")
        subject_line_only_content_source_copy = content_params.get(
            "subject_line_only_content_source_copy"
        )
        content_source_format = content_params.get("content_source_format", "Text")
        email_footer = self.export_settings_wrapper.advanced_setting.get(
            "emailFooter", True
        )

        if content_source_format != "Text":
            logging.error(
                f"Only Text format is supported for content_group {self.content_group.id}"
            )
            return None, None, email_footer

        if not content_source_copy:
            logging.warning(
                f"No content_source_copy found for content_group {self.content_group.id}"
            )
            return None, None, email_footer

        # Get base HTML elements - using same logic as frontend's fetchCleanContentSourceText
        result = fetch_clean_content_source_text(content_source_copy)
        html_element = result.get("txt")

        # Get subject line HTML element
        subject_html_element = None
        if subject_line_only_content_source_copy:
            subject_result = fetch_clean_content_source_text(
                subject_line_only_content_source_copy
            )
            subject_html_element = subject_result.get("emailSubjectTxt")
        elif result.get("emailSubjectTxt"):
            subject_html_element = result.get("emailSubjectTxt")

        if not html_element:
            logging.warning(
                f"Failed to parse HTML content for content_group {self.content_group.id}"
            )
            return None, None, email_footer

        return html_element, subject_html_element, email_footer

    def _replace_tofu_components_with_p13n_tokens(
        self,
        html_element: Tag,
        email_subject_html_element: Optional[Tag],
        is_hubspot_email_id_flow: bool = False,
    ) -> Tuple[Tag, Optional[Tag]]:
        for component_id, p13n_token in self.components_setting.items():
            attribute = f'[data-tofu-id="{component_id}"]'
            element = html_element.select_one(attribute) if html_element else None
            email_subject_element = (
                email_subject_html_element.select_one(attribute)
                if email_subject_html_element
                else None
            )

            if element and not is_hubspot_email_id_flow:
                if element.name.lower() == "a":
                    link_component = self.content_group_components.get(
                        component_id, {}
                    ).get("link", {})
                    linked_content_group_id = link_component.get("contentGroupId")
                    link_type = link_component.get("type")
                    if link_type == "tofu_link":
                        linked_url_token_dict = (
                            get_linked_url_token(
                                linked_content_group_id, self.campaign, "hubspot"
                            )
                            or {}
                        )
                        linked_url_token = linked_url_token_dict.get("token")
                        if not linked_url_token:
                            raise ValueError(
                                "Failed to get the link of the landing page."
                            )
                        token = f"{{{{{self.hub_object_type}.{linked_url_token}}}}}"
                        element["href"] = token
                    else:
                        element["href"] = link_component.get("path", "")
                else:
                    original_text = self.content_group_components[component_id]["text"]
                    if "'" in original_text or '"' in original_text:
                        p13n_content = f"{{{{{self.hub_object_type}.{p13n_token}}}}}"
                    else:
                        safe_text = (
                            original_text.replace("\n", "<br>")
                            .replace("'", "\\'")
                            .replace('"', '\\"')
                            .strip()
                        )
                        p13n_content = f"{{{{ personalization_token('{self.hub_object_type}.{p13n_token}', '{safe_text}') }}}}"
                    if self.content_source_format == "Text":
                        element.clear()
                        element.append(NavigableString(p13n_content))
                    else:
                        # For HTML, wrap with HubL module
                        hubl_module = get_hubl_for_text_module(
                            component_id, p13n_content
                        )
                        element.clear()
                        # Insert as raw HTML (not escaped)
                        hubl_soup = BeautifulSoup(hubl_module, "html.parser")
                        for child in hubl_soup.contents:
                            element.append(child)

            elif email_subject_element:
                original_text = self.content_group_components[component_id]["text"]
                if "'" in original_text or '"' in original_text:
                    p13n_content = f"{{{{{self.hub_object_type}.{p13n_token}}}}}"
                else:
                    safe_text = (
                        original_text.replace("\n", "<br>")
                        .replace("'", "\\'")
                        .replace('"', '\\"')
                        .strip()
                    )
                    p13n_content = f"{{{{ personalization_token('{self.hub_object_type}.{p13n_token}', '{safe_text}') }}}}"
                email_subject_element.clear()
                email_subject_element.append(NavigableString(p13n_content))

        return html_element, email_subject_html_element

    def _finalize_hubspot_email_export(
        self,
        html_element: Tag,
        email_subject_html_element: Tag,
        base_target: dict,
        content_source_format: str = "Text",
        advanced_setting: dict = None,
        is_hubspot_email_id_flow: bool = False,
        update_email: callable = None,
        cloned_email_id: str = None,
    ):
        """
        Finalizes the subject and body HTML for HubSpot email export.

        Returns:
            (export_response, html_content, email_subject)
        """
        if advanced_setting is None:
            advanced_setting = {}
        # 1. Extract subject line text
        email_subject = ""
        if email_subject_html_element is not None:
            # If using BeautifulSoup, get text; if string, just use it
            if hasattr(email_subject_html_element, "get_text"):
                email_subject = email_subject_html_element.get_text(strip=True)
            else:
                email_subject = str(email_subject_html_element).strip()

        export_response = None
        html_content = str(html_element)

        if is_hubspot_email_id_flow:
            if email_subject:
                # Only push emailSubject to hubspot
                export_response = update_email(
                    email_id=cloned_email_id,
                    updated_email_details={"subject": email_subject},
                )
        else:
            email_file_name = f"{base_target.get('emailName', '')} (Python Export)"
            if content_source_format == "Text":
                # Clean attributes
                clean_html_attributes(html_element)
                # Convert to HubL module
                html_content = self._add_hubspot_includes_for_text_email(
                    email_file_name,
                    str(html_element),
                    advanced_setting.get("emailFooter"),
                )
            elif content_source_format == "Html":
                html_content = self._add_hubspot_includes_for_html_email(
                    str(html_element), advanced_setting.get("emailFooter")
                )
        return export_response, html_content, email_subject

    @staticmethod
    def _add_hubspot_includes_for_text_email(
        email_file_name: str, html: str, email_footer: bool
    ) -> str:
        # Make sure all href attributes use single quotes to match frontend
        html = re.sub(r'href="([^"]*)"', r"href='\1'", html)

        # Escape quotes in HTML
        html_escaped = html.replace('"', '\\"')
        preview_module = HubspotHtmlGenerator.TEXT_EMAIL_FOOTER_MODULE
        main_module = (
            '{% module "'
            + email_file_name
            + '",\n    path="@hubspot/rich_text",\n    label='
            + email_file_name
            + ',\n    html="'
            + html_escaped
            + '"\n  %}'
        )

        result = preview_module + "\n    " + main_module

        if email_footer:
            footer_html = HubspotHtmlGenerator.EMAIL_FOOTER_HTML
            footer_html_escaped = footer_html.replace('"', '\\"')
            footer_module = (
                '{% module "email_can_spam", label="Email Footer", path="@hubspot/email_can_spam", html="'
                + footer_html_escaped
                + '" %}'
            )
            result += " \n    " + footer_module
        return result

    @staticmethod
    def _add_hubspot_includes_for_html_email(
        html_content: str, footer: bool = True
    ) -> str:
        preview_tag = '<div style="display:none!important">{% if subject %}{{ subject }}{% endif %}</div>'
        footer_module = '{% module "email_can_spam" label="Email Footer" path="@hubspot/email_can_spam" %}'

        soup = BeautifulSoup(html_content, "html.parser")
        body = soup.body

        if body:
            body.insert(0, BeautifulSoup(preview_tag, "html.parser"))

        for style_elem in soup.find_all("style"):
            content = str(style_elem)
            raw_wrapped = f"{{% raw %}}{content}{{% endraw %}}"
            style_elem.replace_with(BeautifulSoup(raw_wrapped, "html.parser"))

        if footer:
            existing_footer = soup.select_one(".hse-footer")
            if existing_footer:
                existing_footer.replace_with(
                    BeautifulSoup(footer_module, "html.parser")
                )
            else:
                last_section = soup.select_one(".hse-section:last-child")
                if last_section:
                    last_section.insert_after(
                        BeautifulSoup(footer_module, "html.parser")
                    )
                elif body:
                    body.append(BeautifulSoup(footer_module, "html.parser"))

        return str(soup)
