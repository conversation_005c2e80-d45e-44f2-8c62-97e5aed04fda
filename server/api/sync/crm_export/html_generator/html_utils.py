import json
import logging
import re
from datetime import datetime
from datetime import timezone as dt_timezone
from typing import Any, Dict, Optional, Tuple
from urllib.parse import urlparse

from bs4 import BeautifulSoup, NavigableString, Tag

from ....models import Campaign, ContentGroup
from ....utils import get_s3_file, is_valid_url
from ...utils.export_settings_wrapper import ExportSettingsWrapper


def clean_html_attributes(html_element: Tag):
    """
    Clean HTML attributes from an element, preserving only href for links.
    This matches frontend functionality in factoryHelpers.ts.

    Args:
        html_element: BeautifulSoup Tag to clean
    """
    # Process the current element
    attrs_to_remove = []
    for attr in html_element.attrs:
        # Preserve href attribute for links
        if html_element.name == "a" and attr == "href":
            continue
        attrs_to_remove.append(attr)

    # Remove the attributes
    for attr in attrs_to_remove:
        del html_element[attr]

    # Process all child elements recursively
    for child in html_element.find_all(True, recursive=False):
        clean_html_attributes(child)


def clean_html(html_element: Tag, select_decorator: str) -> Tag:
    """
    Remove a specific class from all HTML elements that have it.
    Direct port of the frontend cleanHtml function.

    Args:
        html_element: BeautifulSoup Tag to clean
        select_decorator: Class name to remove

    Returns:
        Cleaned BeautifulSoup Tag
    """
    # Find all elements with the specified class
    selected_elements = html_element.select(f".{select_decorator}")

    # Remove the class from each element
    for element in selected_elements:
        if "class" in element.attrs:
            element["class"] = [c for c in element["class"] if c != select_decorator]

    return html_element


def slate_to_html(node: dict) -> str:
    """
    Recursively converts a Slate node to HTML, matching the JS slateToHtml logic.
    """
    if node is None:
        return ""

    # Handle leaf text node
    if "text" in node:
        formatted_text = node["text"].replace("\n", "<br/>")
        return formatted_text

    children = node.get("children", [])
    children_html = "".join(slate_to_html(child) for child in children)

    node_type = node.get("type", "")

    if node_type == "selected-component":
        return f'<span data-tofu-id="{node.get("id", "")}">{children_html}</span>'
    elif node_type == "link-component":
        return f'<a data-tofu-id="{node.get("id", "")}" href="">{children_html}</a>'
    elif node_type == "paragraph":
        return f"<div>{children_html}</div><br>"
    else:
        return children_html


def transform_slate_to_html_for_export(data: list) -> dict:
    """
    Transforms a list of Slate nodes into subject and body HTML, matching the JS transformSlateToHtmlForExport.
    """
    subject_line_html = ""
    body_html = ""

    for entry in data:
        entry_type = entry.get("type")
        if entry_type in ("subject-line", "subject-line-only"):
            subject_line_html = slate_to_html(entry)
        elif entry_type in ("body", "text"):
            body_html = slate_to_html(entry)

    return {
        "emailSubjectText": subject_line_html or None,
        "text": f"{body_html}",
    }


def fetch_clean_content_source_text(content_source_copy: str) -> Dict[str, Any]:
    """
    Fetch and clean content source text, similar to fetchCleanContentSourceText in frontend.

    Args:
        content_source_copy: S3 path to content source

    Returns:
        Dictionary with txt and emailSubjectTxt (if available)
    """
    if not content_source_copy:
        return {"txt": None, "emailSubjectTxt": None}

    # Get content from S3
    content_data = get_s3_file(content_source_copy)

    try:
        # Try to parse as JSON (for slate format)
        content = json.loads(content_data)

        if isinstance(content, list):
            # Handle slate array format by using transform_slate_to_html_for_export
            transformed = transform_slate_to_html_for_export(content)

            # Create BeautifulSoup objects with html.parser - maintain HTML structure
            txt = BeautifulSoup(f"<div>{transformed['text']}</div>", "html.parser")
            email_subject_txt = (
                BeautifulSoup(
                    f"<div>{transformed['emailSubjectText']}</div>", "html.parser"
                )
                if transformed["emailSubjectText"]
                else None
            )

            return {"txt": txt, "emailSubjectTxt": email_subject_txt}
        else:
            # Handle JSON object format
            text = content.get("text", "")
            email_subject_text = content.get("emailSubjectText", "")

            # Use <br> instead of <br/> to match frontend behavior
            txt = BeautifulSoup(
                f"<div>{text.replace(chr(10), '<br>')}</div>", "html.parser"
            )
            email_subject_txt = (
                BeautifulSoup(f"<div>{email_subject_text}</div>", "html.parser")
                if email_subject_text
                else None
            )

            # Remove highlight styles as in frontend
            txt = clean_html(txt, "highlight")
            txt = clean_html(txt, "bg-red-200")

            return {"txt": txt, "emailSubjectTxt": email_subject_txt}
    except json.JSONDecodeError:
        # If not JSON, treat as plain text
        txt = BeautifulSoup(
            f"<div>{content_data.replace(chr(10), '<br>')}</div>", "html.parser"
        )
        return {"txt": txt, "emailSubjectTxt": None}


def fetch_clean_content_source_html(content_source_copy: str) -> BeautifulSoup:
    """
    Fetch and clean content source HTML, similar to fetchCleanContentSourceHtml in frontend.

    Args:
        content_source_copy: S3 path to content source

    Returns:
        BeautifulSoup object with clean HTML
    """
    if not content_source_copy:
        return None

    # Get content from S3
    html_string = get_s3_file(content_source_copy)

    # Parse HTML using html.parser
    html_doc = BeautifulSoup(html_string, "html.parser")

    # Clean using the same approach as frontend
    html_doc = clean_html(html_doc, "tofu-hovered-element")
    html_doc = clean_html(html_doc, "tofu-selected-element")
    html_doc = clean_html(html_doc, "tofu-hide-element")

    return html_doc


def get_hubl_for_text_module(component_id: str, content: str) -> str:
    """
    Wraps the given content in a HubSpot tofu_text_module block.

    Args:
        component_id: The tofu component ID.
        content: The content to wrap (usually a p13n token).

    Returns:
        A string containing the HubL module block.
    """
    return f'{{% tofu_text_module id="{component_id}" %}}{content}{{% endtofu_text_module %}}'


def get_linked_url_token(
    linked_content_group_id: str,
    campaign: Campaign,
    platform: str,
) -> Optional[Dict[str, str]]:
    """
    Python equivalent of the getLinkedUrlToken TypeScript function.

    Args:
        linked_content_group_id: The ID of the linked content group.
        campaign: The campaign object (dict).
        platform: The platform string ('hubspot', 'marketo', etc).

    Returns:
        Dict with 'token' and 'fallbackURL' if found, else None.
    """
    # Find the linked content group
    content_groups = ContentGroup.objects.filter(campaign=campaign)

    try:
        linked_id_int = int(linked_content_group_id)
    except (TypeError, ValueError):
        return None

    linked_content_group = next(
        (cg for cg in content_groups if cg.id == linked_id_int), None
    )
    if not linked_content_group:
        return None
    export_setting_wrapper = ExportSettingsWrapper(linked_content_group)
    linked_export_settings = export_setting_wrapper.export_settings

    if not linked_export_settings:
        return None

    destination = linked_export_settings.get("exportDestination")
    export_type = linked_export_settings.get("exportType")
    linked_current_type_export_settings = (
        linked_export_settings.get(destination, {}).get("page", {}).get(export_type)
    )

    if not linked_current_type_export_settings:
        return None

    fallback_url = "#"
    if platform == "hubspot":
        fallback_url = (
            get_personalized_url(linked_current_type_export_settings, {})
            or "default url"
        )
        if export_type == "static":
            idx = fallback_url.rfind("/")
            fallback_url = fallback_url[:idx] if idx != -1 else fallback_url
        else:
            idx = fallback_url.find("?")
            fallback_url = fallback_url[:idx] if idx != -1 else fallback_url
    elif platform == "marketo":
        fallback_url = (
            linked_current_type_export_settings.get("urlSetting", {}).get("domain")
            or "default url"
        )

    key = f"{platform}URLToken"
    token = linked_current_type_export_settings.get("urlSetting", {}).get(key)

    if token:
        return {"token": token, "fallbackURL": fallback_url}

    return None


def get_personalized_url(export_setting: dict, target: dict) -> str:
    export_type = export_setting.get("exportType")
    url_setting = export_setting.get("urlSetting", {})
    domain = url_setting.get("domain", "").rstrip("/")
    group_slug = url_setting.get("groupSlug")
    additional_url_params = url_setting.get("additionalURLParams", "")
    tofu_content_id = export_setting.get("embedSetting", {}).get("tofu_content_id")
    destination = export_setting.get("destination")
    full_url = ""
    target_url = ""
    url_suffix = ""
    if destination == "marketo":
        url_suffix = ".html"

    if export_type == "static":
        target_url = target.get("pageSlug")
        if group_slug and export_type != "embed" and destination != "download":
            full_url = f"{domain}/{group_slug}/{target_url or ''}{url_suffix}"
        else:
            full_url = f"{domain}/{target_url or ''}{url_suffix}"
    else:
        target_url = (
            f"tofu_content_id={tofu_content_id}&tofu_slug={target.get('pageSlug')}"
        )
        if group_slug and export_type != "embed" and destination != "download":
            full_url = f"{domain}/{group_slug}{url_suffix}?{target_url}"
        else:
            full_url = f"{domain}?{target_url}"

    # Add https:// if missing
    if not full_url.startswith("http"):
        full_url = f"https://{full_url}"

    # Add additional URL params if present and URL is valid
    if additional_url_params and is_valid_url(full_url):
        parsed = urlparse(full_url)
        if parsed.query:
            full_url += f"&{additional_url_params}"
        else:
            full_url += f"?{additional_url_params}"

    return full_url


def normalize_html(html_str):
    soup = BeautifulSoup(html_str, "html.parser")
    # Optionally, prettify or strip whitespace
    return soup.prettify()


def are_html_equivalent(html1, html2):
    return normalize_html(html1) == normalize_html(html2)
