"""
Factory for creating ExportWorkflow instances with the correct integration and handler.
"""

import logging

from ...models import ContentGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ..utils.export_settings_wrapper import ExportSettingsWrapper
from .crm_export_hubspot import HubspotEmailExportHandler
from .export_handler import BaseExportHandler


def get_export_handler(
    platform_type: PlatformType, content_group: ContentGroup
) -> BaseExportHandler:
    """
    Factory function to create an ExportWorkflow with the correct integration and handler.

    Args:
        platform_type (PlatformType): The CRM platform.
        content_group (ContentGroup): The content group to export.

    Returns:
        BaseExportHandler: An instance with the correct integration and handler.
    """

    export_settings_wrapper = ExportSettingsWrapper(content_group)

    campaign = content_group.campaign
    if not campaign:
        raise ValueError(
            f"Campaign not found, This content group {content_group.id} is not eligible for export"
        )

    playbook = campaign.playbook
    if not playbook:
        raise ValueError(
            f"Playbook not found, This content group {content_group.id} is not eligible for export"
        )

    # Platform integration selection
    if platform_type == PlatformType.PLATFORM_TYPE_HUBSPOT:
        if export_settings_wrapper.is_email():
            handler = HubspotEmailExportHandler(
                playbook_id=playbook.id, source_platform=platform_type
            )
        elif export_settings_wrapper.is_page():
            raise NotImplementedError("Landing page export is not implemented yet")
        else:
            raise ValueError(f"Unsupported platform type: {platform_type}")
    else:
        raise ValueError(f"Unsupported platform type: {platform_type}")

    return handler
