import logging
from abc import ABC, abstractmethod
from typing import Iterable, Optional, TypedDict

from django.core.cache import cache

from ...models import Content, ContentGroup, Playbook
from ...paragon_wrapper import ParagonWrapper
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ...sync.utils.export_settings_wrapper import ExportSettingsWrapper


class EmailTemplateInput(TypedDict):
    html_content: str
    email_file_name: str
    email_subject: str
    email_type: str


class BaseExportHandler(ABC):

    def __init__(self, playbook_id: int, source_platform: PlatformType) -> None:
        try:
            self.playbook_instance = Playbook.objects.get(id=playbook_id)
        except Playbook.DoesNotExist:
            raise ValueError(f"Playbook with id {playbook_id} does not exist")
        self.source_platform = source_platform
        self.paragon_wrapper = ParagonWrapper(self.playbook_instance)

    def export_new(
        self,
        content_group: ContentGroup,  # ToDo: remove this optional argument after verifying the new export workflow
        email_template_input: Optional[EmailTemplateInput] = None,
    ):
        """Do the export for a content group."""
        if self.is_another_export_in_progress(content_group):
            logging.warning(
                f"Another export is in progress for content group {content_group.id}, skipping"
            )
            return
        try:
            self.set_export_in_progress(content_group)
            self.do_export_new(content_group, email_template_input)
        except Exception as e:
            logging.error(f"Error exporting content group {content_group.id}: {e}")
            raise e
        finally:
            self.set_export_completed(content_group)

    def export_existing(self, content_group: ContentGroup):
        """Do the export for an existing content group."""
        if self.is_another_export_in_progress(content_group):
            logging.warning(
                f"Another export is in progress for content group {content_group.id}, skipping"
            )
            return
        try:
            self.set_export_in_progress(content_group)
            self.do_export_existing(content_group)
        except Exception as e:
            logging.error(f"Error exporting content group {content_group.id}: {e}")
            raise e
        finally:
            self.set_export_completed(content_group)

    """
    Abstract base class for platform+category specific export handlers.
    Each handler is responsible for both the export category (email, landing page)
    and the platform (HubSpot, Marketo, Salesforce, etc.).
    """

    @abstractmethod
    def do_export_new(
        self,
        content_group: ContentGroup,
        # ToDo: remove this optional argument after verifying the new export workflow
        email_template_input: Optional[EmailTemplateInput] = None,
    ):
        """Export a new entity (content group, campaign, etc.)."""
        pass

    @abstractmethod
    def do_export_existing(self, content_group: ContentGroup):
        """Export an existing entity (content group, campaign, etc.)."""
        pass

    @abstractmethod
    def create_export_settings(
        self, content_group: ContentGroup, save_to_db: bool = True, **kwargs
    ):
        """Create export settings for a new export (content group, campaign, etc.)."""
        pass

    @abstractmethod
    def update_export_settings(
        self, content_group: ContentGroup, save_to_db: bool = True, **kwargs
    ):
        """Update export settings for an existing export (content group, campaign, etc.)."""
        pass

    @abstractmethod
    def create_or_update_export_settings(
        self, content_group: ContentGroup, save_to_db: bool = True, **kwargs
    ):
        """Create or update export settings for a content group."""
        pass

    @abstractmethod
    def _extract_and_validate_export_settings(self, content_group: ContentGroup):
        """Extract and validate export settings for a content group."""
        pass

    def is_another_export_in_progress(self, content_group: ContentGroup):
        """Check if another export is in progress for a content group."""
        try:
            return cache.get(f"export_in_progress_{content_group.id}")
        except Exception as e:
            logging.error(
                f"Error checking if another export is in progress for content group {content_group.id}: {e}"
            )
            return False

    def set_export_in_progress(self, content_group: ContentGroup):
        """Set the export in progress for a content group."""
        cache.set(f"export_in_progress_{content_group.id}", True, timeout=60 * 60 * 24)

    def set_export_completed(self, content_group: ContentGroup):
        """Set the export completed for a content group."""
        cache.delete(f"export_in_progress_{content_group.id}")

    def _mark_export_status_in_progress(
        self, content_group: ContentGroup, unexported_contents: Iterable[Content]
    ):
        """
        Mark the export status of the contents as in progress.
        """
        export_settings_wrapper = ExportSettingsWrapper(content_group)
        content_ids = [content.id for content in unexported_contents]
        targets_settings_to_update = [
            {
                "contentId": content_id,
                "exportStatus": "In Progress",
            }
            for content_id in content_ids
        ]
        for target_setting in targets_settings_to_update:
            export_settings_wrapper.update_or_append_target_settings(target_setting)
        content_group.save(update_fields=["content_group_params"])
        content_group.refresh_from_db()
