import logging

from ...shared_types import ContentType

# "export_settings":{
#     "hubspot":{
#         "email":{
#         "dynamic":{
#             "exportType":"dynamic",
#             "destination":"hubspot",
#             "targetsSetting":[
#                 {
#                     "hubIds":{
#                     "hubspot_record_id":"36485251",
#                     "hubspot_object_type":"contact"
#                     },
#                     "contentId":42796999,
#                     "emailName":"Tofu Email - SDR 4",
#                     "targetNames":[
#                     "Spring"
#                     ],
#                     "exportStatus":"Completed",
#                     "lastExportAt":1738007214987,
#                     "targetLabels":[
#                     "HV Contacts - Send Cookies via Sendoso: Spring"
#                     ],
#                     "isExportTarget":false,
#                     "targetListNames":[
#                     "HV Contacts - Send Cookies via Sendoso"
#                     ]
#                 }
#             ],
#             "advancedSetting":{
#                 "emailType":"automated",
#                 "emailFooter":true
#             },
#             "componentsSetting":{
#                 "IRkAycyJsbWuA-bQ":"tofu_email_235083_1",
#                 "kekaXGdeJGMwh136":"tofu_email_235083_2",
#                 "msf58XCL2pStgDgb":"tofu_email_235083_3"
#             }
#         }
#         }
#     },
#     "exportType":"dynamic",
#     "exportDestination":"hubspot"
# }


class ExportSettingsWrapper:
    def __init__(self, content_group) -> None:
        self._content_group_instance = content_group
        content_group_params = content_group.content_group_params
        export_settings = content_group_params.get("export_settings", {}) or {}
        content_type = content_group_params.get("content_type", None)

        self._export_type = export_settings.get("exportType", None)
        self._export_destination = export_settings.get("exportDestination", None)
        self._export_category = (
            "email"
            if (
                content_type == ContentType.EmailMarketing
                or content_type == ContentType.EmailSDR
            )
            else "page"
        )

        self._export_settings = {}
        if self._export_type is not None and self._export_destination is not None:
            self._export_settings = (
                export_settings.get(self._export_destination, {})
                .get(self._export_category, {})
                .get(self._export_type, {})
                or {}
            )

        self._target_settings = self._export_settings.get("targetsSetting", [])
        self._advanced_setting = self._export_settings.get("advancedSetting", {})
        self._components_setting = self._export_settings.get("componentsSetting", {})

    @property
    def export_type(self):
        return self._export_type

    @property
    def export_destination(self):
        return self._export_destination

    @property
    def export_settings(self):
        return self._export_settings

    @property
    def advanced_setting(self):
        return dict(self._advanced_setting)

    @property
    def components_setting(self):
        return dict(self._components_setting)

    @property
    def target_settings(self):
        return self._target_settings

    @property
    def url_settings(self):
        return self._export_settings.get("urlSetting", {})

    @property
    def embed_settings(self):
        return self._export_settings.get("embedSetting", {})

    def has_settings(self) -> bool:
        return bool(self._export_settings)

    def has_completed_export(self):
        if not self._export_settings:
            return False
        targets_settings = self._export_settings.get("targetsSetting", [])
        return any(
            target.get("exportStatus") == "Completed" for target in targets_settings
        )

    def is_email(self):
        return self._export_category == "email"

    def is_page(self):
        return self._export_category == "page"

    def update_or_append_target_settings(self, new_export_setting):
        """
        Update an existing target in targetsSetting if a target with the same contentId exists,
        otherwise append the new target. The match is a full match on contentId.
        """
        try:
            targets_setting = self._content_group_instance.content_group_params[
                "export_settings"
            ][self.export_destination][self._export_category][self.export_type][
                "targetsSetting"
            ]
            new_content_id = new_export_setting.get("contentId")
            if not new_content_id:
                raise ValueError("No contentId found in new export setting")
            # Step 1: Find the target to update
            target_to_update = None
            for idx, target in enumerate(targets_setting):
                if target.get("contentId") == new_content_id:
                    target_to_update = idx
                    break

            # Step 2: Update if found, otherwise insert
            if target_to_update is not None:
                targets_setting[target_to_update].update(new_export_setting)
            else:
                targets_setting.append(new_export_setting)
        except Exception as e:
            logging.error(
                f"Failed to update or append target settings {new_export_setting} due to {str(e)}"
            )
