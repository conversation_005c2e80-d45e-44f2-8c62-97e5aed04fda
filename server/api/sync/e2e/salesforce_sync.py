import logging
import traceback
import uuid

from django.core.cache import cache

from ...models import TargetInfo
from ...paragon_wrapper import ParagonWrapper
from ...playbook import PlaybookHandler
from ..content_sync.content_group_salesforce_sync import ContentGroupSalesforceSyncer
from ..integration_sync.salesforce_integration import SalesforceIntegration
from .base_sync import BasePlaybookSyncer


class PlaybookSalesforceSyncer(BasePlaybookSyncer):
    def __init__(self, playbook_instance, target_info_group, campaigns, list_id):
        super().__init__(playbook_instance, target_info_group, campaigns, list_id)

    @property
    def record_type(self):
        return self.integration.record_type

    @property
    def record_type_plural(self):
        return self.integration.record_type_plural

    def _init_integrations(self):
        self.salesforce_agent = ParagonWrapper(
            self._playbook_instance
        ).get_salesforce_agent()

        self.integration = SalesforceIntegration(
            self.salesforce_agent, self.target_info_group, self.list_id
        )

    def _get_content_group_syncer(self, content_group):
        return ContentGroupSalesforceSyncer(
            content_group,
            record_type=self.record_type,
            record_type_plural=self.record_type_plural,
        )

    def _get_record_id_from_record_data(self, record_data):
        return self.integration._get_record_id_from_record_data(record_data)

    def export_tofu_insights(self, task_id, insights_to_crm_field_mapping):
        """
        Export target Tofu insights (summary and value prop) to Salesforce custom fields.

        Args:
            task_id (str): The ID of the Celery task
            insights_to_crm_field_mapping (dict): Mapping of insights to Salesforce custom fields
            {
                "summary": "summary_field_name",
                "value_prop": "value_prop_field_name"
            }
        Returns:
            dict: Export statistics (total and processed count)
        """

        try:
            # Get Salesforce agent via Paragon
            if not hasattr(self, "salesforce_agent") or not self.salesforce_agent:
                self._init_integrations()

            if not self.salesforce_agent:
                logging.error(
                    f"Salesforce integration is not available for this playbook {self._playbook_instance.id}"
                )
                raise ValueError(
                    "Salesforce integration is not available for this account"
                )

            # Set up Tofu insight fields in Salesforce
            # get all field names and add __c to the end if not present
            # update insights_to_crm_field_mapping with the new field names
            insights_to_crm_field_mapping.update(
                {
                    k: v + "__c" if not v.endswith("__c") else v
                    for k, v in insights_to_crm_field_mapping.items()
                }
            )
            self.salesforce_agent.create_salesforce_html_fields(
                object_type=self.record_type,
                field_names=insights_to_crm_field_mapping.values(),
            )

            playbook_handler = PlaybookHandler(self._playbook_instance)
            targets = TargetInfo.objects.filter(
                target_info_group=self.target_info_group
            )
            total_targets = targets.count()
            processed = 0

            # Initialize progress in cache
            cache.set(
                task_id,
                {"task_return": {"total": total_targets, "processed": 0}},
                timeout=60 * 60 * 24,
            )

            for target in targets:
                insights_data = playbook_handler.get_target_insights_data(target)
                data_to_update = {}
                for insight_name, field_name in insights_to_crm_field_mapping.items():
                    data_to_update[field_name] = insights_data.get(insight_name, "")

                # Get Salesforce IDs from target.meta
                salesforce_id = target.meta.get("salesforce", {}).get("Id", None)
                object_type = target.meta.get("salesforce", {}).get(
                    "object_type", self.record_type
                )

                if not salesforce_id:
                    # Skip targets that are not synced from Salesforce
                    logging.warning(f"Target {target.id} does not have a Salesforce ID")
                    continue

                salesforce_ids = {"object_type": object_type, "Id": salesforce_id}

                self.salesforce_agent.update_object_fields(
                    salesforce_ids=salesforce_ids, data_to_push=data_to_update
                )
                processed += 1

                # Update progress in cache
                cache.set(
                    task_id,
                    {"task_return": {"total": total_targets, "processed": processed}},
                    timeout=60 * 60 * 24,
                )

            return {"total": total_targets, "processed": processed}

        except Exception as e:
            logging.exception(
                f"Error in PlaybookSalesforceSyncer.export_tofu_insights: {e}"
            )
            raise
