import logging
import os
import traceback
import uuid
from abc import ABC, abstractmethod

from ...async_tasks import wait_for_all_tasks
from ...campaign import CampaignHandler
from ...campaign_gen_wrapper import CampaignGenWrapper
from ...content_group import ContentGroupHandler
from ...logger import log_autosync_update
from ...models import AutopilotRun, ContentGroup, TargetInfo
from ...utils import cache
from ..worker.record_fetcher import SyncList<PERSON><PERSON><PERSON><PERSON>etcher
from ..worker.target_creator import SyncTargetCreator


# TODO: make it a non ABC class, make it as a workflow with dependency on Integration only
class BasePlaybookSyncer(ABC):
    def __init__(self, playbook_instance, target_info_group, campaign_list, list_id):
        self._playbook_instance = playbook_instance
        self.target_info_group = target_info_group
        self.campaign_list = campaign_list
        self.list_id = list_id
        self._init_integrations()
        self.session_id = None
        self.record_fetcher = SyncListRecordFetcher(self.integration)
        self.target_creator = SyncTargetCreator(self.integration)

    @property
    def record_type(self):
        # move the logic to the subclass to avoid potential issues because every platform is different
        raise NotImplementedError

    @property
    def record_type_plural(self):
        # move the logic to the subclass to avoid potential issues because every platform is different
        raise NotImplementedError

    @property
    def _info_prefix(self):
        playbook = self._playbook_instance
        user = None
        try:
            # Get the owner or first user associated with the playbook
            user = playbook.get_owner_or_first_user()
        except Exception as e:
            logging.error(f"Error fetching user for playbook: {e}")

        user_info = (
            f"User: {user.username} ({user.full_name})" if user else "Unknown User"
        )
        playbook_info = f"Playbook: {playbook.id} ({playbook.company_domain or 'N/A'})"
        target_group_info = f"Target list: {self.target_info_group.target_info_group_key} ({self.target_info_group.id})"

        return f"E2E sync: {user_info}, {playbook_info}, {target_group_info}:"

    @property
    def _error_prefix(self):
        return f"Error in {self._info_prefix}"

    @abstractmethod
    def _init_integrations(self):
        raise NotImplementedError

    @abstractmethod
    def _get_record_id_from_record_data(self, record_data):
        raise NotImplementedError

    def _is_record_id_skipped(self, record_id):
        cache_key = f"e2e_sync_retry_record_id:{self.target_info_group.id}:{record_id}"
        return cache.get(cache_key)

    def _get_new_records(self):
        # Creates autopilot run record for crm sync
        autopilot_run_record = self.autopilot_run_record_crm_sync_start()
        try:
            new_records = self.record_fetcher.get_new_record_ids(
                skip_func=self._is_record_id_skipped
            )
        except Exception as e:
            logging.exception(
                f"{self._error_prefix} Error getting new records: {e}\n{traceback.format_exc()}"
            )
            self._update_autopilot_run_record_status(autopilot_run_record, "Error")
            return []
        self._update_autopilot_run_record_status(
            autopilot_run_record, "Done", new_records
        )
        self._check_or_delete_autopilot_run_record(autopilot_run_record)
        return new_records

    def _batch_create_and_build_targets(self, batch_record_data):

        # Creates autopilot run record for target creation
        autopilot_run_record = self.autopilot_run_record_target_creation()

        def cache_key(record_id):
            return f"e2e_sync_retry_record_id:{self.target_info_group.id}:{record_id}"

        # skip target creation if the record_id is in the cache
        def skip_target_creation_if_cached(record_id):
            return cache.get(cache_key(record_id))

        # create target data
        new_target_keys, new_target_objects_data, failed_record_data = (
            self.target_creator.create_target_data(
                batch_record_data, skip_func=skip_target_creation_if_cached
            )
        )

        for record_data, _ in failed_record_data:
            record_id = self._get_record_id_from_record_data(record_data)
            # mark this record in cache and don't retry until 7 days later
            cache.set(cache_key(record_id), True, timeout=60 * 60 * 24 * 7)
            self._update_autopilot_run_record_status(
                autopilot_run_record, "Error", record_data
            )

        self._update_autopilot_run_record_status(
            autopilot_run_record,
            "In progress",
            new_target_keys,
        )

        # bulk create targets
        target_objects = self.target_creator.bulk_create_targets(
            new_target_objects_data
        )

        # update autopilot run record status
        self._update_autopilot_run_record_status(
            autopilot_run_record,
            "Done",
            new_target_keys,
        )
        self._check_or_delete_autopilot_run_record(autopilot_run_record)
        return target_objects

    def sync(self):
        # CHECK ENV VAR
        if os.getenv("E2E_SYNC_DISABLED") == "True":
            return
        # start crm sync
        self.session_id = str(uuid.uuid4())
        new_record_ids = self._get_new_records()

        if new_record_ids:
            logging.info(f"{self._info_prefix} new_record_ids: {new_record_ids}")
            log_autosync_update(
                f"{self._info_prefix} {len(new_record_ids)} new records found."
            )

            try:
                batch_record_data = self.record_fetcher.get_batch_record_data(
                    new_record_ids
                )
                new_target_objects = self._batch_create_and_build_targets(
                    batch_record_data
                )
                log_autosync_update(
                    f"{self._info_prefix} {len(new_target_objects)} new targets created."
                )
            except Exception as e:
                logging.error(
                    f"{self._error_prefix} Error creating and building objects: {e}\n{traceback.format_exc()}"
                )
                return
            if new_target_objects:
                logging.info(
                    f"{self._info_prefix} new_target_objects are created: {new_target_objects}"
                )

        task_ids = {}
        for campaign in self.campaign_list:
            try:
                task_ids_for_campaign = self._update_campaign(campaign)
                if task_ids_for_campaign:
                    if isinstance(task_ids_for_campaign, dict):
                        task_ids.update(task_ids_for_campaign)
                    else:
                        logging.error(
                            f"Unexpected task_ids_for_campaign type: {type(task_ids_for_campaign)}"
                        )
            except Exception as e:
                logging.error(
                    f"{self._error_prefix} Error updating campaign {campaign}: {e}\n{traceback.format_exc()}"
                )
                continue

        task_results = wait_for_all_tasks(task_ids, timeout=60 * 60 * 6)

        for campaign in self.campaign_list:
            try:
                self._export_campaign(campaign)
            except Exception as e:
                logging.error(
                    f"{self._error_prefix} Error updating campaign {campaign}: {e}\n{traceback.format_exc()}"
                )
                continue

    # This interface does 4 things
    # - check the gaps between the campaign's target to the target_info_group
    # - add new target_objects to campaign
    # - create content in content_group
    # - generate content for the new target_objects
    def _update_campaign(self, campaign):
        # update campaign params["targets"] with new_target_objects
        # start campaign content generation
        # Creates autopilot run record for content generation
        autopilot_run_record = self.autopilot_run_record_target_creation(campaign)
        new_target_objects = []
        all_target_infos = TargetInfo.objects.filter(
            target_info_group=self.target_info_group
        )
        for target_dict in campaign.campaign_params["targets"]:
            if self.target_info_group.target_info_group_key in target_dict:
                current_target_names = target_dict[
                    self.target_info_group.target_info_group_key
                ]
                new_target_objects.extend(
                    [
                        x
                        for x in all_target_infos
                        if x.target_key not in current_target_names
                    ]
                )
        new_target_keys = []
        if new_target_objects:
            new_target_keys = [x.target_key for x in new_target_objects]
            self._update_autopilot_run_record_status(
                autopilot_run_record,
                "In progress",
                new_target_keys,
            )

            updated_targets = []
            for target_dict in campaign.campaign_params["targets"]:
                if self.target_info_group.target_info_group_key in target_dict:
                    target_dict[self.target_info_group.target_info_group_key].extend(
                        [x.target_key for x in new_target_objects]
                    )
                updated_targets.append(target_dict)
            log_autosync_update(
                f"{self._info_prefix} {len(new_target_objects)} new targets added to campaign {campaign.id} ({campaign.campaign_name})."
            )
            campaign.campaign_params["targets"] = updated_targets
            campaign.save()
            campaign.refresh_from_db()

        campaign_handler = CampaignHandler(campaign)
        task_ids = {}
        for content_group in ContentGroup.objects.filter(campaign=campaign):
            try:
                task_id = self._update_content_group(campaign_handler, content_group)
                if task_id:
                    task_ids[task_id] = {
                        "content_group_id": content_group.id,
                    }
            except Exception as e:
                logging.exception(
                    f"{self._error_prefix} Error updating content_group {content_group}: {e}\n{traceback.format_exc()}"
                )
                self._update_autopilot_run_record_status(
                    autopilot_run_record, "Error", new_target_keys
                )
                continue
        self._update_autopilot_run_record_status(
            autopilot_run_record,
            "Done",
            new_target_keys,
        )
        self._check_or_delete_autopilot_run_record(autopilot_run_record)
        return task_ids

    # This interface does 2 things
    # - create content in the the content_group
    # - generate content for the content_group
    def _update_content_group(self, campaign_handler, content_group):
        content_group_handler = ContentGroupHandler(content_group)
        try:
            # for safety we check if it's rep
            if not content_group_handler.is_eligible_for_autopilot():
                return

            contents = content_group_handler.bulk_create_content()
            autopilot_run_record = self.autopilot_run_record_content_generation(
                content_group.campaign
            )
            content_ids = [x.id for x in contents]
            self._update_autopilot_run_record_status(
                autopilot_run_record,
                "In progress",
                content_ids,
            )
        except Exception as e:
            logging.error(f"Error creating content for {content_group}: {e}")
            return None
        job_id = str(uuid.uuid4())
        logging.info(
            f"{self._info_prefix} job_id: {job_id} to generate contents for content_group {content_group} for contents: {contents}"
        )
        try:
            # inside autopilot we just use campaign creator as the user
            user = content_group.campaign.creator
            if not user:
                logging.error(f"No user found for campaign {content_group.campaign.id}")
                return None
            campaign_gen_wrapper = CampaignGenWrapper(content_group.campaign)
            task_id = campaign_gen_wrapper.submit_job(
                user=user,
                content_group_ids=[content_group.id],
                content_ids=[],
                collection_ids=[],
                continue_gen=True,
                joint_generation=True,
                use_all_contents=False,
            )
            content_ids = [x.id for x in contents]
            self._update_autopilot_run_record_status(
                autopilot_run_record,
                "Done",
                content_ids,
            )
            self._check_or_delete_autopilot_run_record(autopilot_run_record)
            return task_id
        except Exception as e:
            logging.error(
                f"{self._error_prefix} Error generating content for {content_group}: {e}"
            )
            self._update_autopilot_run_record_status(
                autopilot_run_record,
                "Error",
                content_ids,
            )
            return None

    def _export_campaign(self, campaign):
        exported_content_ids = []
        # start campaign export
        autopilot_run_record = self.autopilot_run_record_export(campaign)
        for content_group in ContentGroup.objects.filter(campaign=campaign):
            try:
                content_ids = self._export_content_group(content_group)
                if content_ids:
                    exported_content_ids.extend(content_ids)
                    logging.info(
                        f"{self._info_prefix} {len(content_ids)} contents exported for {content_group}: {content_ids}"
                    )
            except Exception as e:
                logging.error(
                    f"{self._error_prefix} Error updating content_group {content_group}: {e}\n{traceback.format_exc()}"
                )
                self._update_autopilot_run_record_status(
                    autopilot_run_record,
                    "Error",
                    exported_content_ids,
                )
                continue
        self._update_autopilot_run_record_status(
            autopilot_run_record,
            "Done",
            exported_content_ids,
        )
        self._check_or_delete_autopilot_run_record(autopilot_run_record)

    def _export_content_group(self, content_group):
        content_group_syncer = self._get_content_group_syncer(content_group)

        if not content_group_syncer.is_eligible_auto_sync():
            return

        try:
            unexported_contents = content_group_syncer.get_unexported_contents()
            if not unexported_contents:
                return
            content_group_syncer.export(
                contents=unexported_contents,
            )
            log_autosync_update(
                f"{self._info_prefix} {len(unexported_contents)} contents exported for content_group {content_group.id} ({content_group.content_group_name})."
            )
            logging.info(
                f"{self._info_prefix} {len(unexported_contents)} contents are exported for {content_group}: {unexported_contents}"
            )
            return [x.id for x in unexported_contents]
        except Exception as e:
            logging.error(
                f"{self._error_prefix} Error exporting content for {content_group}: {e}\n{traceback.format_exc()}"
            )
            return

    def _update_autopilot_run_record_status(
        self, autopilot_run_record, status, extra_info=None
    ):
        if not extra_info:
            return
        if not autopilot_run_record.status:
            autopilot_run_record.status = {}
        autopilot_run_record.status[status] = extra_info
        autopilot_run_record.save()

    def _check_or_delete_autopilot_run_record(self, autopilot_run_record):
        # delete autopilot run record if it has no status or all status are empty.
        if not autopilot_run_record.status and autopilot_run_record.id:
            autopilot_run_record.delete()
            return
        for key, value in autopilot_run_record.status.items():
            if value:
                autopilot_run_record.save()
                return
        if autopilot_run_record.id:
            autopilot_run_record.delete()

    def autopilot_run_record_crm_sync_start(self):
        autopilot_run_record = AutopilotRun(
            playbook=self._playbook_instance,
            target_info_group=self.target_info_group,
            campaign=None,
            autopilot_action_type=AutopilotRun.AutopilotActionType.CRM_SYNC,
            status={},
            session_id=self.session_id,
        )
        return autopilot_run_record

    def autopilot_run_record_target_creation(self, campaign=None):
        if campaign:
            autopilot_run_record = AutopilotRun(
                playbook=self._playbook_instance,
                target_info_group=self.target_info_group,
                campaign=campaign,
                autopilot_action_type=AutopilotRun.AutopilotActionType.TARGET_CREATION,
                status={},
                session_id=self.session_id,
            )
            return autopilot_run_record
        else:
            autopilot_run_record = AutopilotRun(
                playbook=self._playbook_instance,
                target_info_group=self.target_info_group,
                campaign=campaign,
                autopilot_action_type=AutopilotRun.AutopilotActionType.ALL_TARGETS_CREATION,
                status={},
                session_id=self.session_id,
            )
            return autopilot_run_record

    def autopilot_run_record_content_generation(self, campaign):
        autopilot_run_record = AutopilotRun(
            playbook=self._playbook_instance,
            target_info_group=self.target_info_group,
            campaign=campaign,
            autopilot_action_type=AutopilotRun.AutopilotActionType.CONTENT_GENERATION,
            status={},
            session_id=self.session_id,
        )
        return autopilot_run_record

    def autopilot_run_record_export(self, campaign):
        autopilot_run_record = AutopilotRun(
            playbook=self._playbook_instance,
            target_info_group=self.target_info_group,
            campaign=campaign,
            autopilot_action_type=AutopilotRun.AutopilotActionType.EXPORT,
            status={},
            session_id=self.session_id,
        )
        return autopilot_run_record
