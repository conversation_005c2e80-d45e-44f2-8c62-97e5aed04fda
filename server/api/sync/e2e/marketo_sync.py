import logging
import math
import time
import traceback
import uuid

from django.core.cache import cache

from ...models import (
    TargetInfo,
)
from ...paragon_wrapper import ParagonWrapper
from ...playbook_build.target_info_group_wrapper import TargetInfoGroupWrapper
from ..content_sync.content_group_marketo_sync import ContentGroupMarketoSyncer
from ..integration_sync.marketo_integration import MarketoIntegration
from .base_sync import BasePlaybookSyncer


class PlaybookMarketoSyncer(BasePlaybookSyncer):
    def __init__(self, playbook_instance, target_info_group, campaigns, list_id):
        super().__init__(playbook_instance, target_info_group, campaigns, list_id)

        self._record_ids_to_update = {}

    @property
    def record_type(self):
        return self.integration.record_type

    @property
    def record_type_plural(self):
        return self.integration.record_type_plural

    @property
    def table_data(self):
        return self.integration.table_data

    def _get_record_id_from_record_data(self, record_data):
        return self.integration._get_record_id_from_record_data(record_data)

    def _init_integrations(self):
        self.marketo_agent = ParagonWrapper(self._playbook_instance).get_marketo_agent()
        self.integration = MarketoIntegration(
            self.marketo_agent, self.target_info_group, self.list_id
        )

    def _get_content_group_syncer(self, content_group):
        return ContentGroupMarketoSyncer(
            content_group,
            record_type=self.record_type,
            record_type_plural=self.record_type_plural,
        )

    # This is an override of the base_sync.py method
    def _batch_create_and_build_targets(self, batch_record_data):
        # if it's a contact, use super()._batch_create_and_build_targets
        if self.record_type == "contact":
            return super()._batch_create_and_build_targets(batch_record_data)

        new_target_objects_data = {}  # key is company key
        target_infos_to_update = {}
        for record_data in batch_record_data:
            record_id = self.integration._get_record_id_from_record_data(record_data)
            cache_key = (
                f"e2e_sync_retry_record_id:{self.target_info_group.id}:{record_id}"
            )
            try:
                target_key, meta, target_data = self.integration._create_target_data(
                    record_data=record_data,
                    global_position=len(new_target_objects_data),
                )
                if not target_key:
                    continue
                target_key = target_key.strip()
            except Exception as e:
                logging.error(
                    f"{self._error_prefix} Error creating target data: {e}\n{traceback.format_exc()}"
                )
                # mark this record in cache and don't retry until 7 days later
                cache.set(cache_key, True, timeout=60 * 60 * 24 * 7)
                continue
            # if target_key already exists, skip
            if TargetInfo.objects.filter(
                target_info_group=self.target_info_group, target_key=target_key
            ).exists():  # append to lead_ids
                # update meta for lead_ids
                target_info = TargetInfo.objects.get(
                    target_info_group=self.target_info_group, target_key=target_key
                )
                target_info.meta["marketo"]["lead_ids"].append(
                    meta["marketo"]["lead_id"]
                )
                if target_info.id not in target_infos_to_update:
                    target_infos_to_update[target_info.id] = target_info
                if target_info.id not in self._record_ids_to_update:
                    self._record_ids_to_update[target_info.id] = []

                record_id = record_data.get("id")
                if record_id:
                    self._record_ids_to_update[target_info.id].append(record_id)
                else:
                    logging.error(
                        f"Record id not found in {record_data} for target {target_info}"
                    )
            elif target_key in new_target_objects_data:
                new_target_objects_data[target_key]["meta"]["marketo"][
                    "lead_ids"
                ].append(meta["marketo"]["lead_id"])
            else:
                new_target_objects_data[target_key] = {
                    "target_key": target_key,
                    "meta": meta,
                    "docs": target_data,
                }
        if target_infos_to_update:
            TargetInfo.objects.bulk_update(target_infos_to_update.values(), ["meta"])
        if not new_target_objects_data:
            logging.warning(f"{self._error_prefix} No new target objects are created")
            return []

        # TODO: the following logic is duplicated with base_sync.py
        target_objects = TargetInfoGroupWrapper(self.target_info_group).bulk_create(
            new_target_objects_data
        )
        if target_objects:
            if not self.target_info_group.meta.get("importListSettings"):
                logging.error(
                    f"Unexpected behavior: importListSettings is not set for {self.target_info_group}"
                )
                self.target_info_group.meta["importListSettings"] = {}
            self.target_info_group.meta["importListSettings"]["lastSync"] = int(
                time.time() * 1000
            )
            self.target_info_group.save(update_fields=["meta"])
        return target_objects
