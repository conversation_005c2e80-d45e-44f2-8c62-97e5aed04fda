import logging

from django.core.cache import cache

from ...models import (
    TargetInfo,
)
from ...paragon_wrapper import ParagonWrapper
from ...playbook import PlaybookHandler
from ..content_sync.content_group_hubspot_sync import ContentGroupHubspotSyncer
from ..integration_sync.hubspot_integration import HubspotIntegration
from .base_sync import BasePlaybookSyncer


class PlaybookHubspotSyncer(BasePlaybookSyncer):

    def __init__(self, playbook_instance, target_info_group, campaigns, list_id):
        super().__init__(playbook_instance, target_info_group, campaigns, list_id)

    @property
    def record_type(self):
        return self.integration.record_type

    @property
    def record_type_plural(self):
        return self.integration.record_type_plural

    def _init_integrations(self):
        self.hubspot_agent = ParagonWrapper(self._playbook_instance).get_hubspot_agent()
        self.integration = HubspotIntegration(
            self.hubspot_agent, self.target_info_group, self.list_id
        )

    def _get_content_group_syncer(self, content_group):
        return ContentGroupHubspotSyncer(
            content_group,
            record_type=self.record_type,
            record_type_plural=self.record_type_plural,
        )

    # TODO: move this to integration_sync
    def _get_record_id_from_record_data(self, record_data):
        return self.integration._get_record_id_from_record_data(record_data)

    def export_tofu_insights(self, task_id, insights_to_crm_field_mapping):
        """
        Export target Tofu insights (summary and value prop) to HubSpot custom fields.

        Args:
            insights_to_crm_field_mapping (dict): Mapping of insights to HubSpot custom fields
            {
                "summary": "summary_field_name",
                "value_prop": "value_prop_field_name"
            }
        Returns:
            dict: Export statistics (total and processed count)
        """

        try:
            # Get HubSpot agent via Paragon
            if not self.hubspot_agent:
                logging.error(
                    f"HubSpot integration is not available for this playbook {self._playbook_instance.id}"
                )
                raise ValueError(
                    "HubSpot integration is not available for this account"
                )

            # Set up Tofu insight fields in HubSpot
            for insight_name, field_name in insights_to_crm_field_mapping.items():
                self.hubspot_agent.create_crm_field(
                    object_type=self.record_type,
                    name=field_name,
                    label=field_name,
                    type="string",
                    field_type="html",
                )

            playbook_handler = PlaybookHandler(self._playbook_instance)
            targets = TargetInfo.objects.filter(
                target_info_group=self.target_info_group
            )
            total_targets = targets.count()
            processed = 0

            # Initialize progress in cache
            cache.set(
                task_id,
                {"task_return": {"total": total_targets, "processed": 0}},
                timeout=60 * 60 * 24,
            )

            for target in targets:
                insights_data = playbook_handler.get_target_insights_data(target)
                data_to_update = {}
                for insight_name, field_name in insights_to_crm_field_mapping.items():
                    data_to_update[field_name] = insights_data.get(insight_name, "")

                hubspot_id = target.meta.get("hubspot_record_id", None)
                if not hubspot_id:
                    # in some cases, the user could manually add targets that are not synced from HubSpot,
                    # so instead of throwing errors, we are just skipping those targets
                    logging.warning(f"Target {target.id} does not have a hubspot id")
                    continue

                self.hubspot_agent.update_crm_property(
                    record_type_plural=self.record_type_plural,
                    hubspot_id=hubspot_id,
                    properties=data_to_update,
                )
                processed += 1

                # Update progress in cache
                cache.set(
                    task_id,
                    {"task_return": {"total": total_targets, "processed": processed}},
                    timeout=60 * 60 * 24,
                )
            return {"total": total_targets, "processed": processed}

        except Exception as e:
            logging.exception(
                f"Error in PlaybookHubspotSyncer.export_tofu_insights: {e}"
            )
            raise
