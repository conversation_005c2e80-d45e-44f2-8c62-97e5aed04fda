import logging
import time
import traceback
import uuid
from typing import List

from celery import shared_task

from ...models import TargetInfo, TargetInfoGroup
from ...playbook_build.async_object_tasks import parallel_build_docs
from ...playbook_build.target_info_group_wrapper import TargetInfoGroupWrapper
from ...status import <PERSON>Handler
from ..integration_sync.base_integration import BaseIntegration


class SyncTargetCreator:
    """
    Helper class to prepare and bulk create targets for sync operations.
    This class is side-effect free: it does not update run records, cache, or perform any DB writes except for the actual creation step.
    """

    def __init__(self, integration: BaseIntegration):
        """
        Args:
            integration: The integration instance.
        """
        self.integration = integration
        self.target_info_group = integration.target_info_group

    def create_target_data_for_new_records(
        self, new_records_data, build_targets=True
    ) -> List[TargetInfo]:
        """
        Create target data for new records.
        Args:
            new_records_data (list[dict]): List of record data dicts to process.
        Returns:
            list: List of created model instances.
        """

        # step 1: create target data for creation
        new_target_keys, new_target_objects_data, failed_record_data = (
            self.create_target_data(new_records_data)
        )
        logging.info(f"New target keys: {len(new_target_keys)}")
        logging.info(f"New target objects data: {len(new_target_objects_data)}")
        logging.info(f"Failed record data: {len(failed_record_data)}")
        for record_data, exception in failed_record_data:
            logging.error(
                f"Failed to create target data for record: {record_data}, error: {exception}"
            )

        # step 2: create targets in DB
        if new_target_objects_data:
            created_targets = self.bulk_create_targets(
                new_target_objects_data, build_targets=build_targets
            )
            logging.info(
                f"Targets created for {self.target_info_group.target_info_group_key}: {len(created_targets)}"
            )
            return created_targets
        else:
            logging.info(
                f"No targets created for {self.target_info_group.target_info_group_key}"
            )
            return []

    def create_target_data(self, batch_record_data, skip_func=None):
        """
        Prepares target data for bulk creation.
        Args:
            batch_record_data (list[dict]): List of record data dicts to process.
            skip_func (callable): Function to determine if a record should be skipped.
        Returns:
            (successes, failures):
                successes: list of new target object data dicts
                failures: list of (record_data, exception) tuples
        """
        new_target_objects_data = []
        failed_record_data = []

        for record_data in batch_record_data:
            record_id = self.integration._get_record_id_from_record_data(record_data)
            if skip_func and skip_func(record_id):
                continue
            try:
                target_key, meta, target_data = self.integration._create_target_data(
                    record_data,
                    global_position=len(new_target_objects_data),
                )
                if not target_key:
                    continue
                target_key = target_key.strip()
            except Exception as e:
                logging.error(
                    f"Error creating target data: {e}\n{traceback.format_exc()}"
                )
                failed_record_data.append((record_data, e))
                continue
            # if target_key already exists, skip
            if (
                self.target_info_group
                and TargetInfo.objects.filter(
                    target_info_group=self.target_info_group, target_key=target_key
                ).exists()
            ):
                target_key = f"{target_key} ({self.integration._get_record_id_from_record_data(record_data)})"
            new_target_objects_data.append(
                {"target_key": target_key, "meta": meta, "docs": target_data}
            )
        if not new_target_objects_data:
            logging.warning(f"No new target objects are created")
            return [], [], failed_record_data
        new_target_keys = [x["target_key"] for x in new_target_objects_data]
        return new_target_keys, new_target_objects_data, failed_record_data

    def bulk_create_targets(self, new_target_objects_data, build_targets=True):
        """
        Bulk creates targets using the provided model class.
        Args:
            new_target_objects_data (list[dict]): List of target object data dicts to create.
        Returns:
            list: List of created model instances.
        """
        if not new_target_objects_data:
            return []
        target_objects = TargetInfoGroupWrapper(self.target_info_group).bulk_create(
            new_target_objects_data, build=build_targets
        )
        if target_objects:
            if not self.target_info_group.meta.get("importListSettings"):
                logging.error(
                    f"Unexpected behavior: importListSettings is not set for {self.target_info_group}"
                )
                self.target_info_group.meta["importListSettings"] = {}
            self.target_info_group.meta["importListSettings"]["lastSync"] = int(
                time.time() * 1000
            )
            self.target_info_group.save(update_fields=["meta"])
        return target_objects

    def submit_build_targets_task(self, target_info_ids: List[int]) -> str:
        task_id = f"build_targets:target_info_group:{self.target_info_group.id}:{uuid.uuid4()}"
        async_build_targets.apply_async(
            args=(self.target_info_group.id, target_info_ids), task_id=task_id
        )
        return task_id


@shared_task
def async_build_targets(target_info_group_id: str, target_info_ids: List[int]):
    target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)

    if not target_info_ids:
        logging.error(
            f"No targets to build for target info group {target_info_group_id}"
        )
        return

    targets_to_build = TargetInfo.objects.filter(
        id__in=target_info_ids, target_info_group=target_info_group
    )
    if not targets_to_build:
        logging.error(
            f"No targets to build for target info group {target_info_group_id}"
        )
        return

    playbook = target_info_group.playbook
    if not playbook:
        logging.error(
            f"Playbook not found for target info group {target_info_group_id}"
        )
        return

    # start to build targets
    StatusHandler.set_playbook_status(playbook, "info_expansion", "in progress")

    logging.info(
        f"Building {len(targets_to_build)} targets for target info group {target_info_group_id}"
    )
    try:
        parallel_build_docs(targets_to_build, rebuild=True, check_and_rebuild=False)
        logging.info(
            f"Built {len(targets_to_build)} targets for target info group {target_info_group_id}"
        )
        StatusHandler.set_playbook_status(playbook, "info_expansion", "success")
    except Exception as e:
        logging.exception(
            f"Error building targets for target info group {target_info_group_id}: {e}"
        )
        StatusHandler.set_playbook_status(playbook, "info_expansion", "error")
        raise e
