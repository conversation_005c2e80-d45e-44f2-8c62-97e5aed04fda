import logging

from ..integration_sync.base_integration import BaseIntegration


class SyncListRecordFetcher:
    """
    Helper class for fetching new records for sync operations.
    Requires the syncer to implement _get_platform_records and _get_playbook_records.
    This class is side-effect free: it does not update run records, cache, or perform any DB writes.

    Example usage:
        integration = BaseIntegration(...)
        record_fetcher = SyncListRecordFetcher(integration)
        new_records = record_fetcher.get_new_records(skip_func=integration._is_record_id_skipped)
        batch_data = record_fetcher.get_batch_record_data(new_records)
    """

    def __init__(self, integration: BaseIntegration):
        self.integration = integration

    def get_new_records_data(self, skip_func=None):
        # step 1: get new records ids
        new_record_ids = self.get_new_record_ids(skip_func)
        logging.info(
            f"New records discovered for {self.integration.list_id}: {len(new_record_ids)}"
        )
        if not new_record_ids:
            logging.info(f"No new records discovered for {self.integration.list_id}")
            return []

        # step 2: get new records data
        new_records_data = self.get_batch_record_data(new_record_ids)
        logging.info(f"New records data: {len(new_records_data)}")
        if not new_records_data:
            logging.error(
                f"No new records data discovered for {self.integration.list_id}"
            )
            return []

        return new_records_data

    def get_new_record_ids(self, skip_func=None):
        """
        Returns a list of new records to be imported, excluding those already present or skipped.
        Args:
            imported_record_ids (list): List of record IDs already present in the playbook.
            skip_func (callable): Function that takes a record ID and returns True if it should be skipped.
        Returns:
            list: New records to be imported.
        """
        latest_records = self.integration._get_platform_records()
        current_records = self.integration._get_playbook_records()
        if (
            self.integration.target_info_group
            and self.integration.target_info_group.meta
        ):
            for record_id in self.integration.target_info_group.meta.get(
                "imported_record_ids", []
            ):
                current_records.append(record_id)
        current_records_set = set(current_records)
        latest_records_set = set(latest_records)
        new_records = [
            x
            for x in latest_records_set
            if x not in current_records_set and (not skip_func or not skip_func(x))
        ]
        return list(new_records)

    def get_batch_record_data(self, record_ids):
        """
        Returns a list of record data for the given record IDs.

        Args:
            record_ids (list): List of record IDs.

        Returns:
            list: List of record data.
        """
        return self.integration._get_batch_record_data(record_ids)
