import logging
import uuid

from ...models import TargetInfo
from .base_integration import BaseIntegration


class SalesforceIntegration(BaseIntegration):
    def __init__(self, salesforce_agent, target_info_group, list_id):
        super().__init__(salesforce_agent, target_info_group, list_id)
        self._salesforce_records = None

    @property
    def record_type(self):
        if self._record_type is not None:
            return self._record_type
        # note that this is reading from importListSettings, not meta.type
        record_type = self.target_info_group.meta.get("importListSettings", {}).get(
            "type"
        )
        if record_type == "Lead":
            return "Lead"
        elif record_type == "Contact":
            return "Contact"
        elif record_type == "Account":
            return "Account"
        else:
            raise ValueError(f"Unknown record type: {record_type}")

    @property
    def record_type_plural(self):
        record_type = self.record_type
        if record_type == "Lead":
            return "Leads"
        elif record_type == "Contact":
            return "Contacts"
        elif record_type == "Account":
            return "Accounts"
        else:
            raise ValueError(f"Unknown record type: {record_type}")

    def _get_platform_records(self):
        if self.target_info_group is None:
            raise ValueError("target_info_group is None")
        import_list_settings = self.target_info_group.meta.get("importListSettings", {})
        record_type = import_list_settings.get("type")
        list_type = import_list_settings.get("listType")
        if not record_type or not list_type:
            raise ValueError(
                f"record_type or list_type not found in {import_list_settings}"
            )

        # TODO: refactor this since it's duplicated
        object_identifier = next(
            (
                item["columnName"]
                for item in import_list_settings.get("tableData", [])
                if item["dataType"] == "salesforce_identifier"
            ),
        )
        if not object_identifier:
            raise ValueError(f"object_identifier not found in {import_list_settings}")

        table_data = import_list_settings.get("tableData", [])
        properties = [item["columnName"] for item in table_data if "columnName" in item]

        self._salesforce_records = self.agent.get_list_records(
            self.list_id,
            record_type=record_type,
            list_type=list_type,
            properties=properties,
        )
        salesforce_record_ids = [
            str(x[object_identifier])
            for x in self._salesforce_records
            if object_identifier in x and x[object_identifier]
        ]
        return salesforce_record_ids

    def _get_playbook_records(self):
        records = []
        for target_info in TargetInfo.objects.filter(
            target_info_group=self.target_info_group
        ):
            salesforce_data = target_info.meta.get("salesforce", {})
            object_identifier = salesforce_data.get("object_identifier")
            if not object_identifier:
                logging.error(f"object_identifier not found in {salesforce_data}")
                continue

            record_id = salesforce_data.get(object_identifier)
            if not record_id:
                logging.error(
                    f"Record {target_info} does not have salesforce.Id in {target_info.meta}"
                )
                continue
            records.append(str(record_id))
        return records

    def _get_batch_record_data(self, record_ids):
        if not self._salesforce_records:
            raise ValueError(f"salesforce_records not processed")

        import_list_settings = self.target_info_group.meta.get("importListSettings", {})
        table_data = import_list_settings.get("tableData", [])
        if not table_data:
            raise ValueError(f"tableData not found in {self.target_info_group.meta}")

        object_identifier = next(
            (
                item["columnName"]
                for item in import_list_settings.get("tableData", [])
                if item["dataType"] == "salesforce_identifier"
            ),
        )
        if not object_identifier:
            raise ValueError(f"object_identifier not found in {import_list_settings}")

        column_names = [x["columnName"] for x in table_data]
        records = [
            {k: x.get(k) for k in column_names}
            for x in self._salesforce_records
            if x.get(object_identifier) in record_ids
        ]
        return records

    def _create_target_data(self, record_data, global_position):
        target_data = {}
        target_key = None

        # TODO: double check the object_identifier logic
        meta = {
            "position": global_position,
            "synced_from": "salesforce",
            "salesforce": {"object_type": self.record_type},
        }

        doc_position = 0
        for filed_define in self.table_data:
            field_name, field_type = (
                filed_define["columnName"],
                filed_define["dataType"],
            )
            if field_type == "subtarget_name":
                target_key = record_data.get(field_name, None)
                meta["field_name"] = field_name
                continue
            elif field_type == "salesforce_identifier":
                meta["salesforce"]["object_identifier"] = field_name
                meta["salesforce"][field_name] = record_data.get(field_name, "")
                continue

            if field_type not in ("text", "url", "file", "web_search"):
                logging.error(f"Unknown field type: {field_type}")
                continue
            if not record_data.get(field_name):  # skip empty values
                continue
            doc_id = str(uuid.uuid4())
            target_data[doc_id] = {
                "id": doc_id,
                "meta": {"field_name": field_name},
                "type": field_type,
                "value": record_data.get(field_name, ""),
                "position": doc_position,
            }
            doc_position += 1

        if not target_key:
            logging.warning(f"subtarget_name not found in {record_data}")
            return None, None, None

        if "object_identifier" not in meta["salesforce"]:
            logging.error(f"object_identifier not found in {meta}")
            raise ValueError(f"object_identifier not found in {meta}")
        return target_key, meta, target_data

    def _get_record_id_from_record_data(self, record_data):
        return record_data.get("Id", None)
