import logging
import math
import uuid

from ...models import TargetInfo
from .base_integration import BaseIntegration


class MarketoIntegration(BaseIntegration):
    def __init__(self, marketo_agent, target_info_group, list_id):
        super().__init__(marketo_agent, target_info_group, list_id)
        self.marketo_records = None

    @property
    def record_type(self):
        if self._record_type is not None:
            return self._record_type.lower()
        record_type = self.target_info_group.meta.get("type")
        if record_type == "Contact":
            return "contact"
        elif record_type == "Company":
            return "company"
        else:
            raise ValueError(f"Unknown record type: {record_type}")

    @property
    def record_type_plural(self):
        record_type = self.record_type
        if record_type == "contact":
            return "contacts"
        elif record_type == "company":
            return "companies"
        else:
            raise ValueError(f"Unknown record type: {record_type}")

    def _get_platform_records(self):
        if self.target_info_group is None:
            raise ValueError("target_info_group is None")
        list_type = self.target_info_group.meta.get("importListSettings", {}).get(
            "type"
        )
        if not list_type:
            raise ValueError(f"type not found in {self.target_info_group.meta}")
        all_columns = [x["columnName"] for x in self.table_data]
        if "id" not in all_columns:
            all_columns.append("id")
        self.marketo_records = self.agent.get_list_records(
            self.list_id, list_type, all_columns
        )

        marketo_ids = set()
        for record in self.marketo_records:
            marketo_id = record.get("id")
            if not marketo_id:
                logging.error(f"Marketo CRM identifier not found for target: {record}")
                continue
            if marketo_id == "nan" or math.isnan(marketo_id):
                logging.error(f"Marketo CRM identifier is nan for target: {record}")
                continue
            marketo_ids.add(str(marketo_id))
        return list(marketo_ids)

    def _get_playbook_records(self):
        records = []

        for target_info in TargetInfo.objects.filter(
            target_info_group=self.target_info_group
        ):
            marketo_record = target_info.meta.get("marketo", {})
            if not marketo_record:
                logging.error(
                    f"Record {target_info} does not have marketo in {target_info.meta}"
                )
                continue
            marketo_id = marketo_record.get("lead_id") or marketo_record.get("lead_ids")
            if not marketo_id:
                logging.error(
                    f"Record {target_info} does not have lead_id or lead_ids in {target_info.meta}"
                )
                continue
            if isinstance(marketo_id, list):
                for id in marketo_id:
                    records.append(str(id))
            else:
                records.append(str(marketo_id))
        return records

    def _get_batch_record_data(self, record_ids):
        return [
            next(
                (
                    x
                    for x in self.marketo_records
                    if str(x.get(self.marketo_identifier_field)) == str(record_id)
                ),
                None,
            )
            for record_id in record_ids
        ]

    def _create_target_data(self, record_data, global_position):
        target_data = {}
        target_key = None

        if self.record_type == "contact":
            meta = {
                "position": global_position,
                "synced_from": "marketo",
                "marketo": {
                    "object_type": self.record_type,
                    "object_identifier": "lead_id",
                    "lead_id": record_data.get("id"),
                },
            }
        else:
            meta = {
                "position": global_position,
                "synced_from": "marketo",
                "marketo": {
                    "object_type": self.record_type,
                    "object_identifier": "lead_ids",
                    "lead_ids": [record_data.get("id")],
                },
            }

        doc_position = 0
        for filed_define in self.table_data:
            field_name, field_type = (
                filed_define["columnName"],
                filed_define["dataType"],
            )
            if field_type == "subtarget_name":
                target_key = record_data.get(field_name, None)
                continue
            elif field_type == "marketo_identifier":
                meta["marketo"]["lead_id"] = record_data.get(field_name, "")
                continue

            if field_type not in ("text", "url", "file", "web_search"):
                logging.error(f"Unknown field type: {field_type}")
                continue

            # marketo sometimes return NaN
            record_value = record_data.get(field_name)

            def is_nan(value):
                return isinstance(value, float) and math.isnan(value)

            if not record_value or is_nan(record_value):
                continue

            # TODO: the field name is not correct since we shall save the field label
            doc_id = str(uuid.uuid4())
            target_data[doc_id] = {
                "id": doc_id,
                "meta": {"field_name": field_name},
                "type": field_type,
                "value": record_value,
                "position": doc_position,
            }
            doc_position += 1

        if not target_key:
            logging.warning(f"subtarget_name not found in {record_data}")
            return None, None, None

        if "lead_id" not in meta["marketo"] or not meta["marketo"]["lead_id"]:
            logging.error(f"lead_id not found in {record_data} with meta {meta}")
            raise ValueError(f"lead_id not found in {record_data} with meta {meta}")
        return target_key, meta, target_data

    def _get_record_id_from_record_data(self, record_data):
        marketo_id = record_data.get("id")
        if not marketo_id:
            logging.error(f"Marketo CRM identifier not found for target: {record_data}")
            return None
        return str(marketo_id)
