import logging
import uuid
from typing import Any, Dict, List, Optional

from ...models import TargetInfo
from .base_integration import BaseIntegration


class HubspotIntegration(BaseIntegration):
    def __init__(
        self,
        hubspot_agent,
        target_info_group,
        list_id,
        record_type: Optional[str] = None,
        table_data: Optional[List[Dict[str, Any]]] = None,
    ):
        super().__init__(
            hubspot_agent, target_info_group, list_id, record_type, table_data
        )

    @property
    def record_type(self):
        if self._record_type is not None:
            return self._record_type.lower()
        # if record_type is not provided, we need to get it from the target info group
        record_type = self.target_info_group.meta.get("type")
        if record_type == "Company":
            return "company"
        elif record_type == "Contact":
            return "contact"
        else:
            raise ValueError(f"Unknown record type: {record_type}")

    @property
    def record_type_plural(self):
        record_type = self.record_type
        if record_type == "company":
            return "companies"
        elif record_type == "contact":
            return "contacts"
        else:
            raise ValueError(f"Unknown record type: {record_type}")

    def _get_platform_records(self):
        hubsport_records = self.agent.get_list_records(self.list_id)
        hubsport_records = [str(x["recordId"]) for x in hubsport_records]
        return hubsport_records

    def _get_playbook_records(self):
        records = []
        if self.target_info_group is None:
            return records
        for target_info in TargetInfo.objects.filter(
            target_info_group=self.target_info_group
        ):
            record_id = target_info.meta.get("hubspot_record_id", None)
            if not record_id:
                logging.error(
                    f"Record {target_info} does not have hubspot_record_id in {target_info.meta}"
                )
                continue
            records.append(str(record_id))
        return records

    def _get_batch_record_data(self, record_ids):
        table_data = self.table_data
        if not table_data:
            raise ValueError(f"tableData not found in {self.list_id}")
        column_names = [x["columnName"] for x in table_data]
        return self.agent.get_batch_records(
            record_ids, self.record_type_plural, column_names
        )

    def _create_target_data(self, record_data, global_position):
        target_data = {}
        target_key = None

        meta = {
            "position": global_position,
            "synced_from": "hubspot",
            "hubspot_object_type": self.record_type,
            "hubspot_object_identifier": "hubspot_record_id",
        }

        doc_position = 0
        for filed_define in self.table_data:
            field_name, field_type = (
                filed_define["columnName"],
                filed_define["dataType"],
            )
            if field_type == "subtarget_name":
                target_key = record_data.get(field_name, None)
                continue
            elif field_type == "hubspot_identifier":
                meta["hubspot_record_id"] = record_data.get(field_name, "")
                continue

            if field_type not in ("text", "url", "file", "web_search"):
                logging.error(f"Unknown field type: {field_type}")
                continue
            if not record_data.get(field_name):  # skip empty values
                continue
            doc_id = str(uuid.uuid4())
            target_data[doc_id] = {
                "id": doc_id,
                "meta": {"field_name": field_name},
                "type": field_type,
                "value": record_data.get(field_name, ""),
                "position": doc_position,
            }
            doc_position += 1

        if not target_key:
            logging.warning(f"subtarget_name not found in {record_data}")
            return None, None, None

        if "hubspot_record_id" not in meta or not meta["hubspot_record_id"]:
            logging.error(
                f"hubspot_identifier not found in {record_data} with meta {meta}"
            )
            raise ValueError(
                f"hubspot_identifier not found in {record_data} with meta {meta}"
            )
        return target_key, meta, target_data

    def _get_record_id_from_record_data(self, record_data):
        return record_data.get("hs_object_id", None)

    def create_marketing_email(
        self,
        html_content: str,
        file_name: str,
        email_subject: str,
        email_type: str = "automated",
    ):
        template_path = f"tofu/emails/{file_name}.html"
        try:
            # Step 1: Create the template
            self.agent.create_content_template(
                source=html_content,
                template_path=template_path,
                template_type=2,
                folder="tofu",
                category_id=2,
                is_available_for_new_content=False,
            )
        except Exception as e:
            logging.exception(
                f"Failed to create HubSpot content template for {template_path}: {e}"
            )
            raise
        try:
            # Step 2: Create the marketing email
            result = self.agent.create_marketing_email(
                fileName=file_name,
                template_path=template_path,
                subject=email_subject,
                email_type=email_type,
                authorName="Tofu",
                subcategory="batch",
            )
            logging.info(
                f"Successfully created HubSpot marketing email for {file_name} using template {template_path}"
            )
            return result
        except Exception as e:
            logging.exception(
                f"Failed to create HubSpot marketing email for {file_name}: {e}"
            )
            raise

    def get_hubspot_email_draft_url(self, export_response: Dict) -> Optional[str]:
        id = export_response.get("id", "")
        if not id:
            logging.error(
                f"Failed to get HubSpot email draft URL for {export_response}: no id in export response"
            )
            return None
        account_details = self.agent.get_account_details()
        portal_id = account_details.get("portalId", "")
        if not portal_id:
            logging.error(
                f"Failed to get HubSpot portal ID for {account_details}: no portalId in account details"
            )
            return None
        return f"https://app.hubspot.com/email/{portal_id}/edit/{id}/content"
