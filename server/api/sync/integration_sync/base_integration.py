# This is the base class for all integration syncs.

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from api.models import TargetInfoGroup
from api.paragon_wrapper import BaseParagonAgent


class BaseIntegration(ABC):
    def __init__(
        self,
        agent: BaseParagonAgent,
        target_info_group: Optional[TargetInfoGroup],
        list_id: str,
        record_type: Optional[str] = None,
        table_data: Optional[List[Dict[str, Any]]] = None,
    ):
        self.agent = agent
        self.target_info_group = target_info_group
        self.list_id = list_id
        self._record_type = record_type
        self._table_data = table_data

    # Properties
    @property
    @abstractmethod
    def record_type(self):
        pass

    @property
    @abstractmethod
    def record_type_plural(self):
        pass

    @property
    def table_data(self):
        if self._table_data is not None:
            return self._table_data
        if self.target_info_group is not None:
            return self.target_info_group.meta.get("importListSettings", {}).get(
                "tableData", []
            )
        return []

    # for record fetching
    @abstractmethod
    def _get_platform_records(self):
        pass

    @abstractmethod
    def _get_playbook_records(self):
        pass

    @abstractmethod
    def _get_batch_record_data(self, record_ids):
        pass

    # for target creation
    @abstractmethod
    def _create_target_data(self, record_data, global_position):
        pass

    @abstractmethod
    def _get_record_id_from_record_data(self, record_data):
        pass

    def create_marketing_email(
        self,
        html_content: str,
        file_name: str,
        email_subject: str,
        email_type: str = "automated",
    ):
        logging.error("create_marketing_email is not implemented for this integration")
