import copy
import logging
import time
import traceback

from ...models import Content
from ...paragon_wrapper import ParagonWrapper
from ...shared_types import ContentType
from .content_marketo_sync import ContentMarketoSyncer


class ContentGroupMarketoSyncer:
    def __init__(
        self,
        content_group,
        record_type,
        record_type_plural,
        marketo_records=[],
    ) -> None:
        self.content_group = content_group
        self.record_type = record_type
        self.record_type_plural = record_type_plural
        self.marketo_records = marketo_records
        self.marketo_agent = None
        self.lead_ids_to_content_id = {}

    @property
    def export_settings(self):
        if not self.content_group.content_group_params:
            return {}
        return self.content_group.content_group_params.get("export_settings", {})

    # TODO: move this to base class
    @property
    def export_destination(self):
        return self.export_settings.get("exportDestination", "")

    @property
    def export_type(self):
        return self.export_settings.get("exportType", "")

    @property
    def content_group_export_type(self):
        content_type = self.content_group.content_params.get("content_type", "")
        if content_type in [ContentType.EMAIL, ContentType.LANDING_PAGE]:
            return "email"
        elif content_type == ContentType.LANDING_PAGE:
            return "page"
        else:
            return ""

    @property
    def targets_settings(self):
        return (
            self.export_settings.get(self.export_destination, {})
            .get(self.content_group_export_type, {})
            .get(self.export_type, {})
            .get("targetsSetting", [])
        )

    @property
    def components_setting(self):
        return (
            self.export_settings.get(self.export_destination, {})
            .get(self.content_group_export_type, {})
            .get(self.export_type, {})
            .get("componentsSetting", {})
        )

    def get_paragon_agent(self):
        if not self.marketo_agent:
            self.marketo_agent = ParagonWrapper(
                self.content_group.campaign.playbook
            ).get_marketo_agent()
        return self.marketo_agent

    def _update_targets_setting_without_saving(self, targets_setting_to_update):
        self.content_group.content_group_params["export_settings"][
            self.export_destination
        ][self.content_group_export_type][self.export_type][
            "targetsSetting"
        ] = targets_setting_to_update

    def is_eligible_auto_sync(self):
        # TODO: consolidate the logic between marketo and hubspot
        def is_eligible_targets_settings(targets_settings):
            return any(
                target_setting.get("exportStatus") == "Completed"
                for target_setting in targets_settings
            )

        targets_settings = self.targets_settings
        if not targets_settings:
            return False
        return is_eligible_targets_settings(targets_settings)

    def _get_content_specific_data_to_update(self, content_id):
        content = Content.objects.get(id=content_id)
        content_syncer = ContentMarketoSyncer(content)
        l1_key, l2_key = content_syncer.get_target_keys()
        return {
            "emailName": content.name,
            "contentId": content_id,
            "targetLabels": [f"{l1_key}: {l2_key}"],
        }

    def _update_status_based_on_result(self, data_to_update, update_result):
        content_data_to_update = {}
        for update_single_result in update_result:
            lead_id = update_single_result.get("id")
            if not lead_id:
                logging.error(
                    f"debug: Error exporting to Marketo: lead id not found for {update_single_result}"
                )
                continue

            content_id = self.lead_ids_to_content_id.get(lead_id)
            if not content_id:
                logging.error(
                    f"debug: Error exporting to Marketo: content id not found for {lead_id}"
                )
                continue

            status = update_single_result.get("status")
            reason = update_single_result.get("reason")

            if content_id not in content_data_to_update:
                if self.record_type == "contact":
                    content_data_to_update[content_id] = {
                        "marketoIds": {
                            "lead_id": lead_id,
                            "object_type": self.record_type,
                            "object_identifier": "lead_id",
                        },
                        "contentId": content_id,
                        "exportStatus": "Completed",
                        "lastExportAt": int(time.time() * 1000),
                        "isExportTarget": False,
                    }
                else:
                    content_data_to_update[content_id] = {
                        "marketoIds": {
                            "lead_ids": [lead_id],
                            "object_type": self.record_type,
                            "object_identifier": "lead_id",
                        },
                        "contentId": content_id,
                        "exportStatus": "Completed",
                        "lastExportAt": int(time.time() * 1000),
                        "isExportTarget": False,
                    }
                content_data_to_update[content_id].update(
                    self._get_content_specific_data_to_update(content_id)
                )
            else:
                content_data_to_update[content_id]["marketoIds"]["lead_ids"].append(
                    lead_id
                )

    def export(self):
        export_settings = self.export_settings
        if not export_settings:
            logging.error(
                f"Error exporting to Marketo: export settings not found for content group: {self.content_group.id}"
            )
            return
        targets_settings = self.targets_settings
        if not targets_settings:
            logging.error(
                f"Error exporting to Marketo: targets setting not found for content group: {self.content_group.id}"
            )
            return

        # check for every content now
        all_contents = Content.objects.filter(content_group=self.content_group)
        num_of_contents_exported = 0
        all_data_to_update = {}

        for content in all_contents:

            content_syncer = ContentMarketoSyncer(content)
            content_data_to_update = content_syncer.export(
                targets_settings=targets_settings, export_settings=export_settings
            )
            if content_data_to_update:
                num_of_contents_exported += 1
                all_data_to_update.update(content_data_to_update)
                for lead_id in content_data_to_update.keys():
                    self.lead_ids_to_content_id[lead_id] = content.id

        if all_data_to_update:
            update_result = self.get_paragon_agent().batch_update_crm_property(
                update_data=list(all_data_to_update.values())
            )
            self._update_status_based_on_result(all_data_to_update, update_result)
        return num_of_contents_exported
