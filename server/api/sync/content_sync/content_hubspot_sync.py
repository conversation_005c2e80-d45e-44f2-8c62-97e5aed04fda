import logging
import re
import time
import traceback

from ...models import ContentVariation, PublicContent, TargetInfo
from ...thread_locals import get_current_user


class ContentHubspotSyncer:
    def __init__(self, content_instance) -> None:
        self._content_instance = content_instance
        self._target_info = None

    def get_target_info(self):
        if self._target_info:
            return self._target_info

        target_params = self._content_instance.content_params.get("targets", {})
        if len(target_params) != 1:
            logging.error(
                f"Invalid target params: {target_params} for func get_target_info"
            )
            return None

        l1_key = list(target_params.keys())[0]
        l2_key = target_params[l1_key]

        target_info = TargetInfo.objects.filter(
            target_info_group__playbook_id=self._content_instance.content_group.campaign.playbook.id,
            target_info_group__target_info_group_key=l1_key,
            target_key=l2_key,
        )
        self._target_info = target_info.first()
        return self._target_info

    def get_hubspot_id(self):
        target_info = self.get_target_info()
        if not target_info:
            return None
        return target_info.meta.get("hubspot_record_id")

    def get_keys(self):  # return l1_key, l2_key
        target_info = self.get_target_info()
        if not target_info:
            return None, None
        return (
            target_info.target_info_group.target_info_group_key,
            target_info.target_key,
        )

    def update_crm_hubspot_dynamic_email(
        self, hubspot_agent, components_setting, record_type_plural
    ):
        hubspot_id = self.get_hubspot_id()
        if not hubspot_id:
            logging.error(
                f"HubSpot CRM ID not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"HubSpot CRM ID not found for target: {self._content_instance.content_params.get('targets', {})}"
            )

        content_var = ContentVariation.objects.get(content=self._content_instance)
        if not content_var:
            logging.error(
                f"Content result not found for content: {self._content_instance.id}"
            )
            raise Exception(
                f"Content result not found for content: {self._content_instance.id}"
            )

        variations = content_var.variations

        mapped_generations = {}
        for k, v in components_setting.items():
            if v == "skip":
                continue
            if k not in variations:
                logging.error(
                    f"Component {k} not found in content variations: {variations}"
                )
                continue
            generated_content = (
                variations[k].get("meta", {}).get("current_version", {}).get("text")
            )
            # fix new line for email
            generated_content = re.sub(r"\n", "<br>", generated_content)
            if not generated_content:
                logging.error(
                    f"Generated content not found for component {k}: {variations[k]}"
                )
                continue
            mapped_generations[v] = generated_content

        if not mapped_generations:
            logging.error(
                f"Debug: generated content not found for components: {components_setting}"
            )
            raise Exception(
                f"Debug: generated content not found for components: {components_setting}"
            )

        try:
            hubspot_agent.update_crm_property(
                record_type_plural, hubspot_id, mapped_generations
            )
        except Exception as e:
            logging.error(
                f"Error updating HubSpot CRM property: {e}\n{traceback.format_exc()}"
            )
            raise Exception(f"Error updating HubSpot CRM property: {e}")

    def _publish_content(self, tofu_content_id, tofu_slug):
        content_variation = ContentVariation.objects.get(content=self._content_instance)
        # ToDo: add shadow testing with context manager
        user = (
            self._content_instance.creator
            if self._content_instance and hasattr(self._content_instance, "creator")
            else None
        )
        if not user:
            logging.error("No user found for content hubspot sync, use current user")
            user = get_current_user()

        public_content, created = PublicContent.objects.update_or_create(
            tofu_content_id=tofu_content_id,
            tofu_slug=tofu_slug,
            defaults={
                "creator": user,
                "source_content_variation": content_variation,
                "variations": content_variation.variations,
            },
        )
        if not created:
            logging.error(
                f"Public content already exists for tofu_content_id: {tofu_content_id}, tofu_slug: {tofu_slug}"
            )

    @staticmethod
    def get_full_url_preview(
        export_type, destination, target_slug, group_slug, domain, tofu_content_id
    ):
        url_suffix = ""
        if destination == "marketo":
            url_suffix = ".html"

        if export_type == "static":
            target_url = target_slug
            if group_slug and export_type != "embed" and destination != "download":
                full_url = f"{domain}/{group_slug}/{target_url}{url_suffix}"
            else:
                full_url = f"{domain}/{target_url}{url_suffix}"
        else:
            target_url = f"tofu_content_id={tofu_content_id}&tofu_slug={target_slug}"
            if group_slug and export_type != "embed" and destination != "download":
                full_url = f"{domain}/{group_slug}{url_suffix}?{target_url}"
            else:
                full_url = f"{domain}?{target_url}"

        if not full_url:
            raise Exception(
                f"Full URL not found for export type: {export_type}, destination: {destination}, target_slug: {target_slug}, group_slug: {group_slug}, domain: {domain}"
            )

        if not full_url.startswith("http"):
            full_url = f"https://{full_url}"
        return full_url

    def update_page_hubspot(
        self,
        hubspot_agent,
        record_type_plural,
        record_type,
        export_settings_wrapper,
        need_to_export_url=False,
    ):
        def check_settings_and_logging_error(settings, key):
            if not settings.get(key):
                error_msg = f"Error: {key} has no value in settings: {settings}"
                logging.error(error_msg)
                return None
            return settings.get(key)

        url_setting = export_settings_wrapper.url_settings
        if not url_setting:
            return

        domain = check_settings_and_logging_error(url_setting, "domain")
        if not domain:
            return
        domain = domain.rstrip("/")

        group_slug = url_setting.get("groupSlug", "")

        embed_settings = export_settings_wrapper.embed_settings
        if not embed_settings:
            return

        tofu_content_id = check_settings_and_logging_error(
            embed_settings, "tofu_content_id"
        )
        if not tofu_content_id:
            return

        property_name = check_settings_and_logging_error(url_setting, "hubspotURLToken")
        if not property_name:
            return

        destination = export_settings_wrapper.export_destination
        if not destination:
            return

        # TODO: wrap this
        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            return

        def format_slug(s):
            """
            Convert a string to a slug format.
            """
            # Lowercase the string
            s = s.lower()
            # Replace specified characters with '_'
            s = re.sub(r"[.,/#!$%^&*;:{}=`~()+'\"\ ]+", "_", s)
            # Collapse consecutive underscores into one
            s = re.sub(r"_+", "_", s)
            return s

        tofu_slug = format_slug(target_info.target_key)
        hubspot_id = check_settings_and_logging_error(
            target_info.meta, "hubspot_record_id"
        )
        if not hubspot_id:
            return

        self._publish_content(tofu_content_id=tofu_content_id, tofu_slug=tofu_slug)

        page_export_type = export_settings_wrapper.export_type
        if need_to_export_url:
            url = ContentHubspotSyncer.get_full_url_preview(
                page_export_type,
                destination,
                tofu_slug,
                group_slug,
                domain,
                tofu_content_id,
            )
            hubspot_agent.update_crm_property(
                record_type_plural, hubspot_id, {property_name: url}
            )

        page_title = ""
        if page_export_type == "dynamic":
            targets_setting = export_settings_wrapper.target_settings
            if targets_setting:
                first_target = targets_setting[0]
                page_title = first_target.get("pageTitle", "")

        target_data = {
            "hubIds": {
                "hubspot_record_id": hubspot_id,
                "hubspot_object_type": record_type,
            },
            "pageSlug": tofu_slug,
            "contentId": self._content_instance.id,
            "pageTitle": page_title,
            "isCloneForm": False,  # TODO
            "exportStatus": "Completed",
            "lastExportAt": int(time.time() * 1000),
            "targetLabels": [
                f"{target_info.target_info_group.target_info_group_key}: {target_info.target_key}"
            ],
            "isExportTarget": False,
        }
        if need_to_export_url:
            target_data["urlExportStatus"] = "Completed"

        return target_data
