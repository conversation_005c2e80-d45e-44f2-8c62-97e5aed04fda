import copy
import logging
import traceback

from ...models import ContentVariation, PublicContent, TargetInfo
from ...thread_locals import get_current_user


class ContentMarketoSyncer:
    def __init__(self, content_instance, marketo_reports_to_update=[]) -> None:
        self._content_instance = content_instance
        self.marketo_reports_to_update = marketo_reports_to_update

    def get_target_info(self):
        target_params = self._content_instance.content_params.get("targets", {})
        if len(target_params) != 1:
            logging.error(
                f"Invalid target params: {target_params} for func get_target_info"
            )
            return None

        l1_key = list(target_params.keys())[0]
        l2_key = target_params[l1_key]

        target_info = TargetInfo.objects.filter(
            target_info_group__playbook_id=self._content_instance.content_group.campaign.playbook.id,
            target_info_group__target_info_group_key=l1_key,
            target_key=l2_key,
        )
        return target_info.first()

    def get_target_key(self):
        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            return None
        return target_info.target_key

    def get_target_keys(self):
        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            return None, None
        return (
            target_info.target_info_group.target_info_group_key,
            target_info.target_key,
        )

    def _publish_content(self, tofu_content_id, tofu_slug):
        content_variation = ContentVariation.objects.get(content=self._content_instance)

        user = (
            self._content_instance.creator
            if self._content_instance and hasattr(self._content_instance, "creator")
            else None
        )
        if not user:
            logging.error("No user found for content marketo sync, use current user")
            user = get_current_user()

        public_content, created = PublicContent.objects.update_or_create(
            tofu_content_id=tofu_content_id,
            tofu_slug=tofu_slug,
            defaults={
                "creator": user,
                "source_content_variation": content_variation,
                "variations": content_variation.variations,
            },
        )
        if not created:
            logging.error(
                f"Public content already exists for tofu_content_id: {tofu_content_id}, tofu_slug: {tofu_slug}"
            )

    # TODO: move this to the right class
    @staticmethod
    def get_full_url_preview(
        export_type, destination, target_slug, group_slug, domain, tofu_content_id
    ):
        url_suffix = ""
        if destination == "marketo":
            url_suffix = ".html"

        if export_type == "static":
            target_url = target_slug
            if group_slug and export_type != "embed" and destination != "download":
                full_url = f"{domain}/{group_slug}/{target_url}{url_suffix}"
            else:
                full_url = f"{domain}/{target_url}{url_suffix}"
        else:
            target_url = f"tofu_content_id={tofu_content_id}&tofu_slug={target_slug}"
            if group_slug and export_type != "embed" and destination != "download":
                full_url = f"{domain}/{group_slug}{url_suffix}?{target_url}"
            else:
                full_url = f"{domain}?{target_url}"

        if not full_url:
            raise Exception(
                f"Full URL not found for export type: {export_type}, destination: {destination}, target_slug: {target_slug}, group_slug: {group_slug}, domain: {domain}"
            )

        if not full_url.startswith("http"):
            full_url = f"https://{full_url}"
        return full_url

    def update_crm_marketo_dynamic_email(
        self, marketo_agent, components_setting, record_type_plural
    ):
        content_var = ContentVariation.objects.get(content=self._content_instance)
        if not content_var:
            logging.error(
                f"Content result not found for content: {self._content_instance.id}"
            )
            raise Exception(
                f"Content result not found for content: {self._content_instance.id}"
            )

        variations = content_var.variations

        mapped_generations = {}
        for k, v in components_setting.items():
            if k not in variations:
                logging.error(
                    f"Component {k} not found in content variations: {variations}"
                )
                continue
            generated_content = (
                variations[k].get("meta", {}).get("current_version", {}).get("text")
            )
            if not generated_content:
                logging.error(
                    f"Generated content not found for component {k}: {variations[k]}"
                )
                continue
            mapped_generations[v] = generated_content

        if not mapped_generations:
            logging.error(
                f"Debug: generated content not found for components: {components_setting}"
            )
            raise Exception(
                f"Debug: generated content not found for components: {components_setting}"
            )

        # TODO: wrap this
        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )

        marketo_id = target_info.meta.get("marketo", {}).get("lead_id")
        if not marketo_id:
            logging.error(
                f"Marketo CRM ID not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"Marketo CRM ID not found for target: {self._content_instance.content_params.get('targets', {})}"
            )

        try:
            marketo_agent.update_crm_property(marketo_id, mapped_generations)
        except Exception as e:
            logging.error(
                f"Error updating Marketo CRM property: {e}\n{traceback.format_exc()}"
            )
            raise Exception(
                f"Error updating Marketo CRM property: {e}\n{traceback.format_exc()}"
            )
        return (
            marketo_id,
            target_info.target_info_group.target_info_group_key,
            target_info.target_key,
        )

    def get_marketo_identifier(self):
        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )

        marketo_identifier_field = target_info.meta.get("marketo", {}).get(
            "object_identifier"
        )
        if not marketo_identifier_field:
            logging.error(
                f"Marketo CRM identifier field not found for target: {target_info.meta}"
            )
            logging.error(
                f"Marketo CRM identifier field not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"Marketo CRM identifier field not found for target: {self._content_instance.content_params.get('targets', {})}"
            )

        marketo_identifier = target_info.meta.get("marketo", {}).get(
            marketo_identifier_field
        )
        if not marketo_identifier:
            logging.error(
                f"Marketo CRM identifier not found for target in CG: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"Marketo CRM identifier not found for target in CG: {self._content_instance.content_params.get('targets', {})}"
            )
        return marketo_identifier, marketo_identifier_field

    def get_crm_data_marketo_dyanmic_email(self, components_setting):
        content_var = ContentVariation.objects.get(content=self._content_instance)
        if not content_var:
            logging.error(
                f"Content result not found for content: {self._content_instance.id}"
            )
            raise Exception(
                f"Content result not found for content: {self._content_instance.id}"
            )

        variations = content_var.variations

        mapped_generations = {}
        for k, v in components_setting.items():
            if k not in variations:
                logging.error(
                    f"Component {k} not found in content variations: {variations}"
                )
                continue
            generated_content = (
                variations[k].get("meta", {}).get("current_version", {}).get("text")
            )
            if not generated_content:
                logging.error(
                    f"Generated content not found for component {k}: {variations[k]}"
                )
                continue
            mapped_generations[v] = generated_content

        marketo_id, marketo_identifier_field = self.get_marketo_identifier()
        update_data = mapped_generations
        target_info = self.get_target_info()

        return (
            marketo_id,
            marketo_identifier_field,
            update_data,
            target_info.target_info_group.target_info_group_key,
            target_info.target_key,
        )

    def get_incremental_lead_ids_to_export(self, target_settings):
        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            return False
        if not target_info.meta:
            logging.error(f"Target info meta not found for target: {target_info.id}")
            return False

        content_target_settings = None
        for k, v in target_settings.items():
            if v.get("contentId") == self._content_instance.id:
                content_target_settings = v
                break

        exported_lead_ids = []
        if (
            content_target_settings
            and content_target_settings.get("exportStatus") == "Completed"
        ):
            # exported lead_ids
            exported_lead_ids = content_target_settings.get("marketoIds", {}).get(
                "lead_ids", []
            )

        # get the target info for the definition
        lead_ids = target_info.meta.get("marketo", {}).get("lead_ids", [])

        # incremental diff
        lead_ids_to_export = list(set(lead_ids) - set(exported_lead_ids))
        return lead_ids_to_export

    def get_crm_data_to_update(self, export_settings):

        components_setting = (
            export_settings.get("marketo", {})
            .get("email", {})
            .get("dynamic", {})
            .get("componentsSetting", {})
        )
        if not components_setting:
            logging.error(
                f"Error exporting to Marketo: components setting not found for content group: {self._content_instance.content_group.id}"
            )
            return None

        # find all ids
        (
            marketo_id,
            marketo_identifier_field,
            batch_update_data,
            l1_key,
            l2_key,
        ) = self.get_crm_data_marketo_dyanmic_email(
            components_setting=components_setting,
        )
        return batch_update_data

    def export(self, target_settings, export_settings):
        lead_ids_to_export = self.get_incremental_lead_ids_to_export(target_settings)
        if not lead_ids_to_export:
            return []

        data_to_update = self.get_crm_data_to_update(export_settings)
        if not data_to_update:
            return []

        batch_data_to_update = []
        for lead_id in lead_ids_to_export:
            lead_data_to_update = copy.deepcopy(data_to_update)
            lead_data_to_update["id"] = lead_id
            batch_data_to_update.append(lead_data_to_update)

        return batch_data_to_update
