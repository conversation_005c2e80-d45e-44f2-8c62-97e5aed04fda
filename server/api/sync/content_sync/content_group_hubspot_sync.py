import logging
import time
import traceback

from django.db.models import Exists, OuterRef

from ...models import Content, ContentVariation
from ...paragon_wrapper import ParagonWrapper
from ..utils.export_settings_wrapper import ExportSettingsWrapper
from .content_hubspot_sync import ContentHubspotSyncer


class ContentGroupHubspotSyncer:
    def __init__(self, content_group, record_type, record_type_plural) -> None:
        self.content_group = content_group
        self.record_type = record_type
        self.record_type_plural = record_type_plural

        self.export_settings_wrapper = ExportSettingsWrapper(content_group)

    def is_eligible_auto_sync(self):
        return self.export_settings_wrapper.has_completed_export()

    def export(self, contents=None):
        if not contents:
            raise ValueError("Not implemented now")
        else:
            return self._incremental_export_to_hubspot(contents=contents)

    def _incremental_export_to_hubspot(self, contents):
        if not self.export_settings_wrapper.export_settings:
            return

        if self.export_settings_wrapper.is_email():
            if self.export_settings_wrapper.export_type == "dynamic":
                self._incremental_export_dynamic_email_to_hubspot(contents)
        elif self.export_settings_wrapper.is_page():
            return self._incremental_export_single_setting_to_hubspot(contents)
        else:
            pass

    def _incremental_export_dynamic_email_to_hubspot(self, contents):
        hubspot_agent = ParagonWrapper(
            self.content_group.campaign.playbook
        ).get_hubspot_agent()

        export_settings = self.export_settings_wrapper.export_settings
        components_setting = export_settings.get("componentsSetting", {})
        if not components_setting:
            logging.error(
                f"Error exporting to Hubspot: components setting not found for content group: {self.content_group.id}"
            )
            return
        targets_setting = export_settings.get("targetsSetting", [])
        if not targets_setting:
            logging.error(
                f"Error exporting to Hubspot: targetsSetting is empty for content group: {self.content_group.id}"
            )
            return
        base_target = targets_setting[0] if targets_setting else {}
        email_name = base_target.get("emailName", "New Email")

        for content in contents:
            try:
                content_syncer = ContentHubspotSyncer(content)
                hubspot_id = content_syncer.get_hubspot_id()
                if not hubspot_id:
                    logging.error(
                        f"HubSpot CRM ID not found for target: {content.content_params.get('targets', {})}"
                    )
                    raise Exception(
                        f"HubSpot CRM ID not found for target: {content.content_params.get('targets', {})}"
                    )
                l1_key, l2_key = content_syncer.get_keys()

                new_export_setting = {
                    "hubIds": {
                        "hubspot_record_id": hubspot_id,
                        "hubspot_object_type": self.record_type,
                    },
                    "contentId": content.id,
                    "emailName": email_name,
                    "exportStatus": "Completed",
                    "lastExportAt": int(time.time() * 1000),
                    "targetLabels": [f"{l1_key}: {l2_key}"],
                    "isExportTarget": False,
                }
            except Exception as e:
                logging.error(
                    f"Error exporting content {content} to Hubspot: {e}\n{traceback.format_exc()}"
                )
                continue

            try:
                content_syncer.update_crm_hubspot_dynamic_email(
                    hubspot_agent=hubspot_agent,
                    components_setting=components_setting,
                    record_type_plural=self.record_type_plural,
                )
            except Exception as e:
                logging.error(
                    f"Error exporting content {content} to Hubspot: {e}\n{traceback.format_exc()}"
                )
                new_export_setting["errorMessage"] = str(e)

            # update the exported result to content_group_data
            self.export_settings_wrapper.update_or_append_target_settings(
                new_export_setting=new_export_setting
            )

        self.content_group.save()

    def _incremental_export_single_setting_to_hubspot(
        self,
        contents,
    ):
        hubspot_agent = ParagonWrapper(
            self.content_group.campaign.playbook
        ).get_hubspot_agent()

        need_to_export_url = False

        # Check if any target in export settings has urlExportStatus of "Completed"
        export_settings = self.export_settings_wrapper.export_settings
        if export_settings and "targetsSetting" in export_settings:
            for target in export_settings["targetsSetting"]:
                if target.get("urlExportStatus") == "Completed":
                    need_to_export_url = True
                    break

        for content in contents:
            try:
                content_data = ContentHubspotSyncer(content).update_page_hubspot(
                    hubspot_agent=hubspot_agent,
                    record_type_plural=self.record_type_plural,
                    record_type=self.record_type,
                    export_settings_wrapper=self.export_settings_wrapper,
                    need_to_export_url=need_to_export_url,
                )
                if not content_data:
                    logging.error(f"Fail exporting content {content} to Hubspot")
                    continue
                # TODO: this is for near future to check export
                # will delete after the function is tested well in production
                logging.info(f"Exported content {content} to Hubspot: {content_data}")
                self.export_settings_wrapper.update_or_append_target_settings(
                    content_data
                )

            except Exception as e:
                logging.error(
                    f"Error exporting content {content} to Hubspot: {e}\n{traceback.format_exc()}"
                )
                continue
        self.content_group.save()

    # for incremental export
    def get_unexported_contents(self):
        target_settings = self.export_settings_wrapper.target_settings
        if not target_settings:
            # no previous export and current the function is used for incremental export
            return []

        # x is not dict for several dev data. would clean up later
        exported_ids = [
            x["contentId"]
            for x in target_settings
            if isinstance(x, dict) and x.get("exportStatus") == "Completed"
        ]

        # Define a subquery for ContentVariation that must exist
        content_variation_subquery = ContentVariation.objects.filter(
            content=OuterRef("pk")
        )

        unexported_contents = (
            Content.objects.filter(content_group=self.content_group)
            .exclude(id__in=exported_ids)
            .filter(
                # Content has at least one ContentVariation
                Exists(content_variation_subquery),
                # Content gen is marked as FINISHED
                content_status__gen_status__status="FINISHED",
            )
        )

        logging.info(f"Contents to export: {unexported_contents}")
        return unexported_contents
