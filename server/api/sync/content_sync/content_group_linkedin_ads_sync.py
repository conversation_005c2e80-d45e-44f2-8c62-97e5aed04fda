import logging
import traceback
from datetime import datetime

from rest_framework import status

from ...paragon_wrapper import ParagonWrapper


class ContentGroupLinkedinAdsSyncer:
    def __init__(self, content_group) -> None:
        self.content_group = content_group

    def export_to_linkedin_ads(self):
        wrapper = ParagonWrapper(self.content_group.campaign.playbook)

        # TODO: address where we load the ad_campaign_id
        ad_campaign_id = self.content_group.content_group_params.get("ad_campaign_id")

        if not ad_campaign_id:
            ad_campaign_id = 292429503
        if not ad_campaign_id:
            logging.error(
                f"Ad Campaign ID not found for content group: {self.content_group.id}"
            )
            return status.HTTP_400_BAD_REQUEST, {
                "error": "Ad Campaign ID not specified"
            }

        # TODO: address where we load the contents_to_export
        image_path = (
            "/api/web/storage/s3-presigned-url?file={filename}&fileType={file_type}&directory={s3_bucket}".format(
                filename="084a273115fa472958b8b49637a14522.jpg",
                file_type="image/jpeg",
                s3_bucket="tmp-jian-images",
            ),
        )
        contents_to_export = {
            # "image_path": image_path,
            "dest_url": "https://www.google.com",
            "introductory_text": "Introductory Text from code",
            "headline": "Headline from code",
            "description": "Description from code",
            "call_to_action": "SUBSCRIBE",
            "ad_name": f"tmp ad name - {datetime.now().date().strftime('%Y-%m-%d')}",  # TODO: check what name we shall put here, this field is optional and not rendered in ad but edit
        }

        try:
            export_code, export_response = wrapper.export_linkedin_ads(
                ad_campaign_id, contents_to_export
            )
        except Exception as e:
            logging.error(
                f"Error exporting to LinkedIn Ads: {e}\n{traceback.format_exc()}"
            )
            return status.HTTP_500_INTERNAL_SERVER_ERROR, {
                "error": f"Error exporting to LinkedIn Ads: {e}"
            }
        return export_code, export_response
