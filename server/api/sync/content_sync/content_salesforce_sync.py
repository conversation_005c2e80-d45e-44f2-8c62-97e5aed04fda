import logging
import re
import time

from ...models import ContentVariation, PublicContent, TargetInfo
from ...thread_locals import get_current_user


class ContentSalesforceSyncer:
    def __init__(self, content_instance) -> None:
        self._content_instance = content_instance

    def fetch_salesforce_id(self):
        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            return None

        return target_info.meta.get("salesforce", {}).get("Id")

    def get_target_keys(self):
        return self._content_instance.content_params.get("targets", {})

    def get_target_info(self):
        target_params = self._content_instance.content_params.get("targets", {})
        if len(target_params) != 1:
            logging.error(
                f"Invalid target params: {target_params} for func get_target_info"
            )
            return None

        l1_key = list(target_params.keys())[0]
        l2_key = target_params[l1_key]

        target_info = TargetInfo.objects.filter(
            target_info_group__playbook_id=self._content_instance.content_group.campaign.playbook.id,
            target_info_group__target_info_group_key=l1_key,
            target_key=l2_key,
        )
        return target_info.first()

    def update_crm_salesforce_dynamic_email(
        self, salesforce_agent, components_setting, record_type_plural
    ):
        content_var = ContentVariation.objects.get(content=self._content_instance)
        if not content_var:
            logging.error(
                f"Content result not found for content: {self._content_instance.id}"
            )
            raise Exception(
                f"Content result not found for content: {self._content_instance.id}"
            )

        variations = content_var.variations

        mapped_generations = {}
        for k, v in components_setting.items():
            if v == "skip":
                continue
            if k not in variations:
                logging.error(
                    f"Component {k} not found in content variations: {variations}"
                )
                continue
            generated_content = (
                variations[k].get("meta", {}).get("current_version", {}).get("text")
            )
            # fix new line for email
            generated_content = re.sub(r"\n", "<br>", generated_content)
            if not generated_content:
                logging.error(
                    f"Generated content not found for component {k}: {variations[k]}"
                )
                continue
            mapped_generations[v] = generated_content

        if not mapped_generations:
            logging.error(
                f"Debug: generated content not found for components: {components_setting}"
            )
            raise Exception(
                f"Debug: generated content not found for components: {components_setting}"
            )

        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )

        salesforce_id = target_info.meta.get("salesforce", {}).get("Id")
        if not salesforce_id:
            logging.error(
                f"Salesforce CRM ID not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            raise Exception(
                f"Salesforce CRM ID not found for target: {self._content_instance.content_params.get('targets', {})}"
            )

        try:
            salesforce_agent.update_crm_property(
                record_type_plural, salesforce_id, mapped_generations
            )
        except Exception as e:
            logging.exception(f"Error updating Salesforce CRM property: {e}")
            raise Exception(f"Error updating Salesforce CRM property: {e}")
        return (
            salesforce_id,
            target_info.target_info_group.target_info_group_key,
            target_info.target_key,
        )

    def fetch_components_results(self, components):
        content_var = ContentVariation.objects.get(content=self._content_instance)
        if not content_var:
            logging.error(
                f"Content result not found for content: {self._content_instance.id}"
            )
            raise Exception(
                f"Content result not found for content: {self._content_instance.id}"
            )

        variations = content_var.variations
        results = {}

        for component_id in components:
            if component_id not in variations:
                logging.error(
                    f"Component {component_id} not found in content variations: {variations}"
                )
                continue
            generated_content = (
                variations[component_id]
                .get("meta", {})
                .get("current_version", {})
                .get("text")
            )
            if not generated_content:
                logging.error(
                    f"Generated content not found for component {component_id}: {variations[component_id]}"
                )
                continue
            results[component_id] = {"text": generated_content}

        return results

    def _publish_content(self, tofu_content_id, tofu_slug):
        content_variation = ContentVariation.objects.get(content=self._content_instance)

        user = (
            self._content_instance.creator
            if self._content_instance and hasattr(self._content_instance, "creator")
            else None
        )
        if not user:
            logging.warning(
                "No user found for content salesforce sync, use current user"
            )
            user = get_current_user()

        public_content, created = PublicContent.objects.update_or_create(
            tofu_content_id=tofu_content_id,
            tofu_slug=tofu_slug,
            defaults={
                "creator": user,
                "source_content_variation": content_variation,
                "variations": content_variation.variations,
            },
        )
        if not created:
            logging.error(
                f"Public content already exists for tofu_content_id: {tofu_content_id}, tofu_slug: {tofu_slug}"
            )

    @staticmethod
    # TODO: move to utils
    def get_full_url_preview(
        export_type, destination, target_slug, group_slug, domain, tofu_content_id
    ):
        url_suffix = ""
        if destination == "marketo":
            url_suffix = ".html"

        if export_type == "static":
            target_url = target_slug
            if group_slug and export_type != "embed" and destination != "download":
                full_url = f"{domain}/{group_slug}/{target_url}{url_suffix}"
            else:
                full_url = f"{domain}/{target_url}{url_suffix}"
        elif export_type == "dynamic":
            target_url = f"tofu_content_id={tofu_content_id}&tofu_slug={target_slug}"
            if group_slug and export_type != "embed" and destination != "download":
                full_url = f"{domain}/{group_slug}{url_suffix}?{target_url}"
            else:
                full_url = f"{domain}?{target_url}"
        else:
            target_url = f"tofu_content_id={tofu_content_id}&tofu_slug={target_slug}"
            full_url = f"{domain}?{target_url}"

        if not full_url:
            raise Exception(
                f"Full URL not found for export type: {export_type}, destination: {destination}, target_slug: {target_slug}, group_slug: {group_slug}, domain: {domain}"
            )

        if not full_url.startswith("http"):
            full_url = f"https://{full_url}"
        return full_url

    def update_page_salesforce(
        self,
        salesforce_agent,
        record_type_plural,
        record_type,
        page_export_settings,
        page_export_type,
        salesforce_object_type,
        need_to_export_url=False,
    ):
        def check_settings_and_logging_error(settings, key):
            if not settings.get(key):
                error_msg = f"Error: {key} has no value in settings: {settings}"
                logging.error(error_msg)
                return None
            return settings.get(key)

        url_setting = check_settings_and_logging_error(
            page_export_settings, "urlSetting"
        )
        if not url_setting:
            logging.error(
                f"Error: urlSetting has no value in settings: {page_export_settings}"
            )
            return

        domain = check_settings_and_logging_error(url_setting, "domain")
        if not domain:
            return
        domain = domain.rstrip("/")

        group_slug = url_setting.get("groupSlug", "")

        embed_settings = check_settings_and_logging_error(
            page_export_settings, "embedSetting"
        )
        if not embed_settings:
            return

        tofu_content_id = check_settings_and_logging_error(
            embed_settings, "tofu_content_id"
        )
        if not tofu_content_id:
            return

        property_name = check_settings_and_logging_error(
            url_setting, "salesforceURLToken"
        )
        if not property_name:
            return

        destination = check_settings_and_logging_error(
            page_export_settings, "destination"
        )
        if not destination:
            return

        target_info = self.get_target_info()
        if not target_info:
            logging.error(
                f"Target info not found for target: {self._content_instance.content_params.get('targets', {})}"
            )
            return

        def format_slug(s):
            s = s.lower()
            s = re.sub(r"[.,/#!$%^&*;:{}=`~()+'\"\ ]+", "_", s)
            s = re.sub(r"_+", "_", s)
            return s

        tofu_slug = format_slug(target_info.target_key)
        salesforce_id = check_settings_and_logging_error(
            target_info.meta.get("salesforce", {}), "Id"
        )
        if not salesforce_id:
            logging.error(
                f"Salesforce CRM ID not found for target: {target_info.target_key}"
            )
            return

        self._publish_content(tofu_content_id=tofu_content_id, tofu_slug=tofu_slug)

        if need_to_export_url:
            url = ContentSalesforceSyncer.get_full_url_preview(
                page_export_type,
                destination,
                tofu_slug,
                group_slug,
                domain,
                tofu_content_id,
            )
            # remove https:// or http:// if it's present
            # the reason is because when the salesforce token is synced to other platforms like outreach and salesloft,
            # those platforms will add https:// or http:// to the token when it's used as a hyperlink
            url = re.sub(r"^(https?://)", "", url)

            data_to_push = {}
            data_to_push[property_name] = url

            salesforce_id_data = {
                "Id": salesforce_id,
                "object_type": salesforce_object_type,
                "object_identifier": "Id",
            }
            salesforce_agent.update_object_fields(salesforce_id_data, data_to_push)

        page_title = ""
        if page_export_type == "dynamic":
            targets_setting = page_export_settings.get("targetsSetting", [])
            if targets_setting:
                first_target = targets_setting[0]
                page_title = first_target.get("pageTitle", "")

        l1_key = target_info.target_info_group.target_info_group_key
        l2_key = target_info.target_key

        return {
            "pageSlug": tofu_slug,
            "contentId": self._content_instance.id,
            "pageTitle": page_title,
            "isCloneForm": False,
            "targetNames": [l2_key],
            "exportStatus": "Completed",
            "lastExportAt": int(time.time() * 1000),
            "targetLabels": [f"{l1_key}: {l2_key}"],
            "salesforceIds": {
                "Id": salesforce_id,
                "object_type": record_type,
                "object_identifier": "Id",
            },
            "isExportTarget": False,
            "targetListNames": [l1_key],
            "urlExportStatus": "Updatable",
            "hasJustBeenExported": True,
        }
