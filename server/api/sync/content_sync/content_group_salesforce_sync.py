import logging
import time
import traceback
from typing import Any, Dict, List

from django.db.models import Exists, OuterRef

from ...models import Content, ContentVariation
from ...paragon_wrapper import ParagonWrapper
from ..utils.export_settings_wrapper import ExportSettingsWrapper
from .content_salesforce_sync import ContentSalesforceSyncer


def has_completed_export(settings):
    if not settings:
        return False
    targets_settings = settings.get("targetsSetting", [])
    return any(target.get("exportStatus") == "Completed" for target in targets_settings)


class ContentGroupSalesforceSyncer:
    def __init__(self, content_group, record_type, record_type_plural) -> None:
        self.content_group = content_group
        self.record_type = record_type
        self.record_type_plural = record_type_plural
        self.salesforce_agent = ParagonWrapper(
            self.content_group.campaign.playbook
        ).get_salesforce_agent()

        self.export_settings_wrapper = ExportSettingsWrapper(content_group)

    def _is_eligible_auto_sync_email(self):
        email_settings = (
            self.content_group.content_group_params.get("export_settings", {})
            .get("salesforce", {})
            .get("email", {})
            .get("dynamic", {})
        )
        return email_settings and has_completed_export(email_settings)

    def _is_eligible_auto_sync_page(self):
        page_settings = (
            self.content_group.content_group_params.get("export_settings", {})
            .get("other", {})
            .get("page", {})
            .get("embed", {})
        )
        if page_settings:
            return has_completed_export(page_settings)
        return False

    def is_eligible_auto_sync(self):
        return self._is_eligible_auto_sync_email() or self._is_eligible_auto_sync_page()

    def export(self, contents=None):
        if not contents:
            raise ValueError("Not implemented now")
        else:
            return self._incremental_export_to_salesforce(contents=contents)

    def _incremental_export_to_salesforce(self, contents):
        export_settings = self.content_group.content_group_params.get(
            "export_settings", {}
        )
        is_email = export_settings.get("salesforce", {}).get("email", {})
        if is_email:
            return self._incremental_export_email_to_salesforce(contents)

        is_landing_page = export_settings.get("other", {}).get("page", {})
        if is_landing_page:
            return self._incremental_export_landing_page_to_salesforce(contents)

        return

    def _incremental_export_email_to_salesforce(self, contents) -> None:
        dynamic_settings = (
            self.content_group.content_group_params.get("export_settings", {})
            .get("salesforce", {})
            .get("email", {})
            .get("dynamic", {})
        )
        if dynamic_settings:
            self._incremental_export_dynamic_email_to_salesforce(
                contents, dynamic_settings
            )

    def _incremental_export_dynamic_email_to_salesforce(
        self, contents, dynamic_settings
    ) -> None:
        self.export_salesforce_email_as_dynamic(
            export_settings=dynamic_settings, contents=contents
        )

    def export_salesforce_email_as_dynamic(
        self, export_settings: Dict[str, Any], contents: List[Content]
    ) -> None:
        try:
            targets_setting = export_settings.get("targetsSetting", [])
            components_setting = export_settings.get("componentsSetting", {})

            unique_salesforce_object_types = set()
            for target in targets_setting:
                if target.get("salesforceIds", {}).get("object_type"):
                    unique_salesforce_object_types.add(
                        target["salesforceIds"]["object_type"]
                    )

            if len(unique_salesforce_object_types) == 0:
                raise Exception(
                    "No selected target is connected with Salesforce. In dynamic mode, only targets that are imported from Salesforce can be exported."
                )

            if len(unique_salesforce_object_types) > 1:
                raise Exception(
                    "We currently only support exporting to targets that have the same Salesforce object type (Account, Lead or Contact). Please select targets with the same Salesforce object type."
                )

            salesforce_object_type = list(unique_salesforce_object_types)[0]
            if salesforce_object_type not in ["Account", "Lead", "Contact"]:
                raise Exception(
                    "We currently only support exporting to targets that have the Salesforce object type (Account, Lead or Contact). Please select targets with the Salesforce object type."
                )

            # Create Salesforce HTML fields
            p13n_tokens = list(components_setting.values())
            self.salesforce_agent.create_salesforce_html_fields(
                salesforce_object_type, p13n_tokens
            )

            try:
                components_to_fetch = {
                    component_id: component
                    for component_id, component in self.content_group.components.items()
                    if components_setting.get(str(component_id)) != "skip"
                }
            except Exception as e:
                logging.exception(
                    f"Error fetching components and will fallback to all components: {e}"
                )
                components_to_fetch = self.content_group.components

            # Export the tofu data for each target to CRM, 10 targets at a time
            for content in contents:
                try:

                    content_syncer = ContentSalesforceSyncer(content)
                    salesforce_id = content_syncer.fetch_salesforce_id()
                    target_keys = content_syncer.get_target_keys()

                    if not salesforce_id:
                        raise Exception("Content not connected with Salesforce")

                    tofu_components = content_syncer.fetch_components_results(
                        components_to_fetch
                    )

                    data_to_push = {}
                    for component_id, value in tofu_components.items():
                        p13n_token = components_setting.get(component_id)
                        if not p13n_token:
                            continue

                        tofu_data = value.get("text", "")
                        data_to_push[p13n_token] = tofu_data

                    salesforce_id_data = {
                        "Id": salesforce_id,
                        "object_type": salesforce_object_type,
                        "object_identifier": "Id",
                    }
                    self.salesforce_agent.update_object_fields(
                        salesforce_id_data, data_to_push
                    )

                    # update export status for this target to 'Completed'

                    self.content_group.content_group_params["export_settings"][
                        "salesforce"
                    ]["email"]["dynamic"]["targetsSetting"].append(
                        {
                            "contentId": content.id,
                            "emailName": content.content_name,
                            "targetNames": list(target_keys.values()),
                            "exportStatus": "Completed",
                            "lastExportAt": int(
                                time.time() * 1000
                            ),  # Current timestamp in milliseconds
                            "targetLabels": [
                                f"{l1_key}: {l2_key}"
                                for l1_key, l2_key in target_keys.items()
                            ],
                            "salesforceIds": {
                                "Id": salesforce_id,
                                "object_type": salesforce_object_type,
                                "object_identifier": "Id",
                            },
                            "isExportTarget": False,
                            "targetListNames": list(target_keys.keys()),
                            "hasJustBeenExported": True,
                        }
                    )
                except Exception as error:
                    logging.exception(
                        f"Error exporting content {content.id}: {str(error)}"
                    )
                    # TODO: set export status to 'Failed'

                    self.content_group.content_group_params["export_settings"][
                        "salesforce"
                    ]["email"]["dynamic"]["targetsSetting"].append(
                        {
                            "contentId": content.id,
                            "emailName": content.content_name,
                            "targetNames": list(target_keys.values()),
                            "exportStatus": "Error",
                            "lastExportAt": int(
                                time.time() * 1000
                            ),  # Current timestamp in milliseconds
                            "targetLabels": [
                                f"{l1_key}: {l2_key}"
                                for l1_key, l2_key in target_keys.items()
                            ],
                            "salesforceIds": {
                                "Id": salesforce_id,
                                "object_type": salesforce_object_type,
                                "object_identifier": "Id",
                            },
                            "isExportTarget": False,
                            "targetListNames": list(target_keys.keys()),
                            "hasJustBeenExported": True,
                            "errorMessage": str(error),
                        }
                    )
            self.content_group.save(update_fields=["content_group_params"])
        except Exception as error:
            logging.exception(
                f"Error in export_salesforce_email_as_dynamic: {str(error)}"
            )
            raise

    def _incremental_export_landing_page_to_salesforce(self, contents):
        page_export_settings_dynamic = (
            self.content_group.content_group_params.get("export_settings", {})
            .get("salesforce", {})
            .get("page", {})
            .get("dynamic", {})
        )
        if page_export_settings_dynamic:
            self._incremental_export_single_setting_to_salesforce(
                contents=contents,
                page_export_type="dynamic",
                page_export_settings=page_export_settings_dynamic,
            )
        page_export_setting_embed = (
            self.content_group.content_group_params.get("export_settings", {})
            .get("other", {})
            .get("page", {})
            .get("embed", {})
        )
        if page_export_setting_embed:
            self._incremental_export_single_setting_to_salesforce(
                contents=contents,
                page_export_type="embed",
                page_export_settings=page_export_setting_embed,
            )

    def _incremental_export_single_setting_to_salesforce(
        self,
        contents,
        page_export_type,
        page_export_settings,
    ):

        salesforce_agent = ParagonWrapper(
            self.content_group.campaign.playbook
        ).get_salesforce_agent()

        # first publish the data
        # secondly update on salesforce
        updated_target_setting = page_export_settings.get("targetsSetting", [])

        unique_salesforce_object_types = set()
        for target in updated_target_setting:
            if target.get("salesforceIds", {}).get("object_type"):
                unique_salesforce_object_types.add(
                    target["salesforceIds"]["object_type"]
                )

        if len(unique_salesforce_object_types) == 0:
            raise Exception(
                "No selected target is connected with Salesforce. In dynamic mode, only targets that are imported from Salesforce can be exported."
            )
        if len(unique_salesforce_object_types) > 1:
            raise Exception(
                "We currently only support exporting to targets that have the same Salesforce object type (Account, Lead or Contact). Please select targets with the same Salesforce object type."
            )

        salesforce_object_type = list(unique_salesforce_object_types)[0]
        if salesforce_object_type not in ["Account", "Lead", "Contact"]:
            raise Exception(
                "We currently only support exporting to targets that have the Salesforce object type (Account, Lead or Contact). Please select targets with the Salesforce object type."
            )

        need_to_export_url = False

        # Check if any target in export settings has urlExportStatus of "Completed"
        export_settings = self.export_settings_wrapper.export_settings
        if export_settings and "targetsSetting" in export_settings:
            for target in export_settings["targetsSetting"]:
                if target.get("urlExportStatus") == "Completed":
                    need_to_export_url = True
                    break

        for content in contents:
            try:
                content_data = ContentSalesforceSyncer(content).update_page_salesforce(
                    salesforce_agent=salesforce_agent,
                    record_type_plural=self.record_type_plural,
                    record_type=self.record_type,
                    page_export_type=page_export_type,
                    page_export_settings=page_export_settings,
                    salesforce_object_type=salesforce_object_type,
                    need_to_export_url=need_to_export_url,
                )
                if not content_data:
                    logging.error(f"Fail exporting content {content} to Salesforce")
                    continue
                # TODO: this is for near future to check export
                # will delete after the function is tested well in production
                logging.info(
                    f"Exported content {content} to Salesforce: {content_data}"
                )
                updated_target_setting.append(content_data)

            except Exception as e:
                logging.exception(
                    f"Error exporting content {content} to Salesforce: {e}"
                )
                continue
        if page_export_type == "dynamic":
            self.content_group.content_group_params["export_settings"]["salesforce"][
                "page"
            ][page_export_type]["targetsSetting"] = updated_target_setting
        elif page_export_type == "embed":
            self.content_group.content_group_params["export_settings"]["other"]["page"][
                "embed"
            ]["targetsSetting"] = updated_target_setting
        else:
            logging.error(f"Invalid page export type: {page_export_type}")
        self.content_group.save(update_fields=["content_group_params"])

    def get_unexported_contents(self):
        export_settings = self.content_group.content_group_params.get(
            "export_settings", {}
        )
        target_settings = (
            export_settings.get("salesforce", {})
            .get("email", {})
            .get("dynamic", {})
            .get("targetsSetting", [])
        ) or (
            export_settings.get("other", {})
            .get("page", {})
            .get("embed", {})
            .get("targetsSetting", [])
        )

        if not target_settings:
            # no previous export and current the function is used for incremental export
            return []

        # x is not dict for several dev data. would clean up later
        exported_ids = [
            x["contentId"]
            for x in target_settings
            if isinstance(x, dict) and x.get("exportStatus") == "Completed"
        ]

        # Define a subquery for ContentVariation that must exist
        content_variation_subquery = ContentVariation.objects.filter(
            content=OuterRef("pk")
        )

        unexported_contents = (
            Content.objects.filter(content_group=self.content_group)
            .exclude(id__in=exported_ids)
            .filter(
                # Content has at least one ContentVariation
                Exists(content_variation_subquery),
                # Content gen is marked as FINISHED
                content_status__gen_status__status="FINISHED",
            )
        )

        logging.info(f"Contents to export: {unexported_contents}")
        return unexported_contents
