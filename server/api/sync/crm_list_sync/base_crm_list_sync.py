from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from ...models import Playbook, TargetInfoGroup
from ...paragon_wrapper import ParagonWrapper
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ..integration_sync.base_integration import BaseIntegration


class RecordType(str, Enum):
    CONTACT = "Contact"
    COMPANY = "Company"
    LEAD = "Lead"


class BaseCRMListSync(ABC):
    def __init__(self, playbook_id: int, source_platform: PlatformType):
        try:
            self.playbook_instance = Playbook.objects.get(id=playbook_id)
        except Playbook.DoesNotExist:
            raise ValueError(f"Playbook with id {playbook_id} does not exist")

        self.source_platform = source_platform
        self.paragon_wrapper = ParagonWrapper(self.playbook_instance)

    @property
    @abstractmethod
    def _platform_name(self) -> str:
        """Get the platform name string (e.g., 'hubspot', 'marketo')"""
        pass

    def _get_platform_settings_key(self) -> str:
        """Get the platform-specific settings key for field mappings"""
        platform_name = self._platform_name
        return f"{platform_name}ImportSettings"

    def _get_field_mapping_from_settings(
        self, record_type: str
    ) -> Optional[List[Dict[str, Any]]]:
        """Get field mapping from playbook settings for the current platform and record type"""
        if not self.playbook_instance.settings:
            return None

        platform_key = self._get_platform_settings_key()
        platform_name = self._platform_name
        field_mappings_key = f"{platform_name}FieldMappings"

        return (
            self.playbook_instance.settings.get(platform_key, {})
            .get(field_mappings_key, {})
            .get(record_type, None)
        )

    def _save_field_mapping_to_settings(
        self, record_type: str, table_data: List[Dict[str, Any]]
    ) -> None:
        """Save field mapping to playbook settings for the current platform and record type"""
        settings = self.playbook_instance.settings or {}
        platform_key = self._get_platform_settings_key()
        platform_name = self._platform_name
        field_mappings_key = f"{platform_name}FieldMappings"

        platform_settings = settings.setdefault(platform_key, {})
        field_mappings = platform_settings.setdefault(field_mappings_key, {})

        # Only update if different to avoid unnecessary saves
        if field_mappings.get(record_type) != table_data:
            field_mappings[record_type] = table_data
            self.playbook_instance.settings = settings
            self.playbook_instance.save(update_fields=["settings"])

    def _get_effective_field_mapping(
        self,
        record_type: str,
        provided_table_data: Optional[List[Dict[str, Any]]],
        default_mapping: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """
        Get the effective field mapping using priority: provided > settings > default
        """
        if provided_table_data is not None:
            return provided_table_data

        # Try to get from settings
        settings_mapping = self._get_field_mapping_from_settings(record_type)
        if settings_mapping:
            return settings_mapping

        # Fall back to default
        return default_mapping

    @abstractmethod
    def _create_target_info_group_from_list(
        self,
        list_id: str,
        record_type: RecordType,
        table_data: Optional[List[Dict[str, Any]]] = None,
    ) -> Tuple[TargetInfoGroup, str, dict]:
        pass

    @abstractmethod
    def _create_integration(
        self,
        target_info_group: TargetInfoGroup,
    ) -> BaseIntegration:
        pass

    @abstractmethod
    def _create_integration_without_target_info_group(
        self,
        list_id: str,
        record_type: RecordType,
        table_data: Optional[List[Dict[str, Any]]] = None,
    ) -> BaseIntegration:
        pass

    def _get_target_info_group_name(self, list_name: str) -> Optional[str]:
        if not list_name:
            raise ValueError("List name is required")
        # Gather all existing group keys that start with list_name
        existing_groups = TargetInfoGroup.objects.filter(
            playbook=self.playbook_instance,
            target_info_group_key__startswith=list_name,
        ).values_list("target_info_group_key", flat=True)
        existing_titles = set(existing_groups)

        unique_name = list_name
        counter = 2
        while unique_name in existing_titles:
            unique_name = f"{list_name} {counter}"
            counter += 1
        return unique_name
