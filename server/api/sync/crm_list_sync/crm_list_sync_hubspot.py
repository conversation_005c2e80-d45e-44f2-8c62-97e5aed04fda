import logging
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from ...models import TargetInfoGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ..integration_sync.hubspot_integration import HubspotIntegration
from .base_crm_list_sync import BaseCRMListSync, RecordType


class ObjectTypeId(str, Enum):
    CONTACT = "0-1"
    COMPANY = "0-2"


class CRMListSyncHubspot(BaseCRMListSync):

    DEFAULT_TABLE_VALUES_MAP = {
        "Contact": [
            {"columnName": "email", "dataType": "subtarget_name"},
            {"columnName": "hs_object_id", "dataType": "hubspot_identifier"},
            {"columnName": "firstname", "dataType": "text"},
            {"columnName": "lastname", "dataType": "text"},
            {"columnName": "company", "dataType": "text"},
            {"columnName": "website", "dataType": "url"},
        ],
        "Company": [
            {"columnName": "name", "dataType": "subtarget_name"},
            {"columnName": "hs_object_id", "dataType": "hubspot_identifier"},
            {"columnName": "website", "dataType": "url"},
        ],
    }

    def __init__(self, playbook_id: int):
        super().__init__(playbook_id, PlatformType.PLATFORM_TYPE_HUBSPOT)

    @property
    def _platform_name(self) -> str:
        """Get the platform name string"""
        return "hubspot"

    def _create_target_info_group_from_list(
        self,
        list_id: str,
        record_type: RecordType,
        table_data: Optional[List[Dict[str, Any]]] = None,
        create_target_info_group: bool = True,
    ) -> Tuple[TargetInfoGroup, str, dict]:
        if self.source_platform != PlatformType.PLATFORM_TYPE_HUBSPOT:
            raise ValueError(
                f"Source platform {self.source_platform} is not supported for import list as target info group task"
            )

        # step 0: get the list detail and handle the case of create company list from contact
        hubspot_agent = self.paragon_wrapper.get_hubspot_agent()
        try:
            list_detail = hubspot_agent.get_list_by_id(list_id)
        except Exception as e:
            raise ValueError(f"Failed to get list detail from hubspot: {e}")

        object_type_id = list_detail.get("objectTypeId", None)
        if not object_type_id:
            raise ValueError(f"Object type id not found for list {list_id}")

        list_name = list_detail.get("name", None)
        if not list_name:
            raise ValueError(f"List name not found for list {list_id}")

        if object_type_id == ObjectTypeId.CONTACT and record_type == RecordType.COMPANY:
            logging.info(
                f"Creating company list from contact: {list_id} with name {list_name}"
            )
            result = hubspot_agent.create_company_list_from_contact(
                list_name, ObjectTypeId.COMPANY, "DYNAMIC", list_id
            )
            list_id = result.get("listId", None)
            if not list_id:
                raise ValueError(
                    f"Failed to create company list from contact: {result}"
                )
            list_name = result.get("name", None)
            if not list_name:
                raise ValueError(
                    f"Failed to create company list from contact: {result}"
                )
            object_type_id = result.get("objectTypeId", None)
            if not object_type_id:
                raise ValueError(f"Object type id not found for list {list_id}")
            logging.info(
                f"Created company list from contact: {list_id} with name {list_name}"
            )

        # step 1: fill into import setting metadata
        import_setting_metadata = {
            "syncFrom": "hubspot",
            "id": list_id,
            "listId": list_id,
            "targetsImportLevel": record_type,
        }

        metadata = {
            "type": record_type,
            "isNewImportedList": True,
            "importListSettings": import_setting_metadata,
        }

        import_setting_metadata["objectTypeId"] = object_type_id
        import_setting_metadata["listName"] = list_name

        # populate table data using the common field mapping logic
        effective_table_data = self._get_effective_field_mapping(
            record_type, table_data, self.DEFAULT_TABLE_VALUES_MAP[record_type]
        )
        import_setting_metadata["tableData"] = effective_table_data

        # Always save the effective field mapping to settings
        self._save_field_mapping_to_settings(record_type, effective_table_data)

        # step 2: we will always create a new target info group
        target_info_group_name = self._get_target_info_group_name(list_name)
        logging.info(f"Creating target info group with name {target_info_group_name}")

        # step 3: create the target info group
        if create_target_info_group:
            target_info_group = TargetInfoGroup.objects.create(
                playbook=self.playbook_instance,
                target_info_group_key=target_info_group_name,
                meta=metadata,
            )
        else:
            target_info_group = None

        return target_info_group, target_info_group_name, metadata

    def _create_integration(
        self,
        target_info_group: TargetInfoGroup,
    ) -> HubspotIntegration:

        list_id = target_info_group.meta.get("importListSettings", {}).get(
            "listId", None
        )
        if list_id is None:
            raise ValueError(
                f"List id not found for target info group {target_info_group.target_info_group_key}"
            )

        if self.source_platform == PlatformType.PLATFORM_TYPE_HUBSPOT:
            return HubspotIntegration(
                self.paragon_wrapper.get_hubspot_agent(),
                target_info_group,
                list_id,
            )
        else:
            raise ValueError(
                f"Source platform {self.source_platform} is not supported for integration sync"
            )

    def _create_integration_without_target_info_group(
        self,
        list_id: str,
        record_type: RecordType,
        table_data: Optional[List[Dict[str, Any]]] = None,
    ) -> HubspotIntegration:
        effective_table_data = self._get_effective_field_mapping(
            record_type, table_data, self.DEFAULT_TABLE_VALUES_MAP[record_type]
        )

        return HubspotIntegration(
            self.paragon_wrapper.get_hubspot_agent(),
            None,
            list_id,
            record_type,
            effective_table_data,
        )
