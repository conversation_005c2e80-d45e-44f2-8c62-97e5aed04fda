import logging
import time
from typing import Any, Dict, List, Optional, TypedDict

from ...models import Playbook, TargetInfoGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ...utils import CloudWatchMetrics
from ..integration_sync.base_integration import BaseIntegration
from ..worker.record_fetcher import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>cher
from ..worker.target_creator import Sync<PERSON><PERSON><PERSON><PERSON><PERSON>
from .base_crm_list_sync import BaseCRMListSync, RecordType


class ImportListAsTargetInfoGroupTaskInput(TypedDict):
    playbook_id: int
    list_id: str
    source: PlatformType
    record_type: RecordType
    table_data: Optional[List[Dict[str, Any]]] = None
    task_submit_time: float
    create_targets: bool


class CRMListSyncWorkflow:

    def __init__(self, playbook_id: int, crm_list_sync: BaseCRMListSync):
        try:
            self.playbook_instance = Playbook.objects.get(id=playbook_id)
        except Playbook.DoesNotExist:
            raise ValueError(f"Playbook with id {playbook_id} does not exist")
        self.crm_list_sync = crm_list_sync

    def fetch_list_data(
        self,
        list_id: str,
        record_type: RecordType,
        table_data: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        logging.info(f"Fetching list data: {list_id}, {record_type}")
        start_time = time.time()
        # step 0: get the target info group related information
        _, target_info_group_name, target_info_group_meta = (
            self.crm_list_sync._create_target_info_group_from_list(
                list_id, record_type, table_data, create_target_info_group=False
            )
        )
        # step 1: create integration without target info group
        integration = self.crm_list_sync._create_integration_without_target_info_group(
            list_id, record_type, table_data
        )
        record_fetcher = self._create_record_fetcher(integration)
        target_creator = self._create_target_creator(integration)
        # step 2: fetch list data from platform by calling record_fetcher
        new_records_data = record_fetcher.get_new_records_data()
        if not new_records_data:
            logging.info(f"No new records data discovered for {list_id}, {record_type}")
            return {
                "target_info_group_name": target_info_group_name,
                "target_info_group_meta": target_info_group_meta,
                "new_target_objects_data": [],
            }
        # step 3: generate target info data from the fetched list data, by using target_creator
        new_target_keys, new_target_objects_data, failed_record_data = (
            target_creator.create_target_data(new_records_data)
        )
        logging.info(
            f"Created {len(new_target_keys)} targets data for {list_id}, {record_type}"
        )
        # step 4: return the target info data
        end_time = time.time()
        CloudWatchMetrics.put_metric(
            "CRMListSyncWorkflow_FetchListData",
            end_time - start_time,
            [
                {"Name": "playbook_id", "Value": str(self.playbook_instance.id)},
                {"Name": "list_id", "Value": str(list_id)},
                {"Name": "record_type", "Value": str(record_type)},
                {"Name": "platform", "Value": str(self.crm_list_sync.source_platform)},
                {
                    "Name": "new_records_count",
                    "Value": str(len(new_records_data)),
                },
            ],
            unit="Seconds",
        )
        return {
            "target_info_group_name": target_info_group_name,
            "target_info_group_meta": target_info_group_meta,
            "new_target_objects_data": new_target_objects_data,
        }

    def sync_import_list_as_target_info_group(
        self,
        list_id: str,
        record_type: RecordType,
        table_data: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        logging.info(
            f"Syncing import list as target info group: {list_id}, {record_type}, {table_data}"
        )
        start_time = time.time()
        # step 1: create the target info group
        target_info_group, _, _ = (
            self.crm_list_sync._create_target_info_group_from_list(
                list_id, record_type, table_data
            )
        )
        logging.info(f"Created target info group: {target_info_group}")
        CloudWatchMetrics.put_metric(
            "CRMListSyncWorkflow_ImportList_CreateTargetInfoGroup",
            time.time() - start_time,
            [
                {"Name": "playbook_id", "Value": str(self.playbook_instance.id)},
                {"Name": "list_id", "Value": str(list_id)},
                {"Name": "target_info_group_id", "Value": str(target_info_group.id)},
            ],
            unit="Seconds",
        )
        # step 2: sync the list to the target info group
        try:
            sync_result = self.sync_list_to_target_info_group(target_info_group.id)
        except Exception as e:
            logging.exception(
                f"Error syncing list to target info group while importing list: {e}"
            )
            raise e
        end_time = time.time()
        CloudWatchMetrics.put_metric(
            "CRMListSyncWorkflow_ImportList",
            end_time - start_time,
            [
                {"Name": "playbook_id", "Value": str(self.playbook_instance.id)},
                {"Name": "list_id", "Value": str(list_id)},
                {"Name": "target_info_group_id", "Value": str(target_info_group.id)},
                {"Name": "platform", "Value": str(self.crm_list_sync.source_platform)},
            ],
            unit="Seconds",
        )
        return {"target_info_group_id": target_info_group.id, **sync_result}

    def sync_list_to_target_info_group(
        self, target_info_group_id: str
    ) -> Dict[str, Any]:
        start_time = time.time()
        # step 1: verify the target info group exists
        try:
            target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)
        except TargetInfoGroup.DoesNotExist:
            raise ValueError(
                f"Target info group with id {target_info_group_id} does not exist"
            )
        if not self._is_target_info_group_supported(target_info_group):
            raise ValueError(
                f"Target info group with id {target_info_group_id} is not supported"
            )

        # step 2: get the integration and syncer
        integration = self.crm_list_sync._create_integration(target_info_group)
        record_fetcher = self._create_record_fetcher(integration)
        target_creator = self._create_target_creator(integration)

        # step 3: get new records data
        time_before_get_new_records_data = time.time()
        new_records_data = record_fetcher.get_new_records_data()
        if not new_records_data:
            logging.info(
                f"No new records data discovered for {target_info_group.target_info_group_key}"
            )
            return {}
        CloudWatchMetrics.put_metric(
            "CRMListSyncWorkflow_SyncList_GetNewRecordsData",
            time.time() - time_before_get_new_records_data,
            [
                {"Name": "playbook_id", "Value": str(self.playbook_instance.id)},
                {"Name": "target_info_group_id", "Value": str(target_info_group_id)},
                {"Name": "platform", "Value": str(self.crm_list_sync.source_platform)},
            ],
            unit="Seconds",
        )
        # step 4: create targets data
        time_before_create_targets_data = time.time()
        created_targets = target_creator.create_target_data_for_new_records(
            new_records_data, build_targets=False
        )
        CloudWatchMetrics.put_metric(
            "CRMListSyncWorkflow_SyncList_CreateTargets",
            time.time() - time_before_create_targets_data,
            [
                {"Name": "playbook_id", "Value": str(self.playbook_instance.id)},
                {"Name": "target_info_group_id", "Value": str(target_info_group_id)},
                {"Name": "platform", "Value": str(self.crm_list_sync.source_platform)},
            ],
            unit="Seconds",
        )
        logging.info(
            f"Created {len(created_targets)} targets data for {target_info_group.target_info_group_key}"
        )
        # step 5: build targets in async
        created_target_ids = [target.id for target in created_targets]
        task_id = target_creator.submit_build_targets_task(created_target_ids)
        logging.info(
            f"Submitted build targets task for {target_info_group.target_info_group_key}: {task_id}"
        )
        CloudWatchMetrics.put_metric(
            "CRMListSyncWorkflow_SyncList",
            time.time() - start_time,
            [
                {"Name": "playbook_id", "Value": str(self.playbook_instance.id)},
                {"Name": "target_info_group_id", "Value": str(target_info_group_id)},
                {"Name": "platform", "Value": str(self.crm_list_sync.source_platform)},
                {
                    "Name": "created_targets_count",
                    "Value": str(len(created_target_ids)),
                },
            ],
        )
        return {"targets_build_task_id": task_id}

    def _is_target_info_group_supported(
        self, target_info_group: TargetInfoGroup
    ) -> bool:
        list_id = self._get_list_id(target_info_group)
        if not list_id:
            logging.error(f"listId not found in {target_info_group.meta}")
            return False
        source = target_info_group.meta.get("importListSettings", {}).get(
            "syncFrom", ""
        )
        if not source:
            logging.error(f"syncFrom not found in {target_info_group.meta}")
            return False
        return True

    def _create_record_fetcher(
        self, integration: BaseIntegration
    ) -> SyncListRecordFetcher:
        return SyncListRecordFetcher(integration)

    def _create_target_creator(self, integration: BaseIntegration) -> SyncTargetCreator:
        return SyncTargetCreator(integration)

    @staticmethod
    def _get_list_id(target_info_group: TargetInfoGroup) -> str:
        """
        Return the list identifier, checking 'listId' first then 'id'.
        """
        return target_info_group.meta.get("importListSettings", {}).get(
            "listId", ""
        ) or target_info_group.meta.get("importListSettings", {}).get("id", "")
