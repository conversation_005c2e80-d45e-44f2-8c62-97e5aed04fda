import logging
import time

from celery import shared_task
from django.core.cache import cache

from ...models import TargetInfoGroup
from ...shared_definitions.protobuf.gen.action_define_pb2 import PlatformType
from ...utils import CloudWatchMetrics
from .crm_list_sync_hubspot import CRMList<PERSON>yncHubspot
from .crm_list_sync_workflow import (
    CRMListSyncWorkflow,
    ImportListAsTargetInfoGroupTaskInput,
)


@shared_task
def import_list_as_target_info_group_task(
    task_input: ImportListAsTargetInfoGroupTaskInput,
    task_id: str,
):
    source = task_input["source"]
    task_submit_time = task_input.get("task_submit_time", None)
    if task_submit_time is not None:
        current_time = time.time()
        CloudWatchMetrics.put_metric(
            "CRMListSyncWorkflow_ImportList_Task_WaitingTime",
            current_time - task_submit_time,
            [
                {"Name": "playbook_id", "Value": str(task_input["playbook_id"])},
                {"Name": "source", "Value": str(source)},
                {"Name": "task_id", "Value": str(task_id)},
            ],
            unit="Seconds",
        )
    workflow = get_crm_list_sync_workflow(source, task_input["playbook_id"])

    try:
        if task_input["create_targets"]:
            result = workflow.sync_import_list_as_target_info_group(
                task_input["list_id"],
                task_input["record_type"],
                task_input["table_data"],
            )
        else:
            # we don't need to create targets, so we can fetch the list data directly
            # This is to make the whole process the same as the existing FE logic
            # TODO: revisit the logic after all platforms support fetching list data
            result = workflow.fetch_list_data(
                task_input["list_id"],
                task_input["record_type"],
                task_input["table_data"],
            )
        cache.set(
            task_id,
            {"task_return": result},
            timeout=60 * 60 * 24 * 1,
        )
        return result
    except Exception as e:
        logging.exception(f"Error syncing list to target info group: {e}")
        raise e


@shared_task
def sync_list_to_target_info_group_task(target_info_group_id):
    # step 1: get the target info group
    try:
        target_info_group = TargetInfoGroup.objects.get(id=target_info_group_id)
    except TargetInfoGroup.DoesNotExist:
        logging.error(
            f"Target info group with id {target_info_group_id} does not exist"
        )
        raise ValueError(
            f"Target info group with id {target_info_group_id} does not exist"
        )
    # step 2: get source platform from target info group
    source = target_info_group.meta.get("importListSettings", {}).get("syncFrom", "")
    if not source:
        logging.error(
            f"Source not found in target info group with id {target_info_group_id}"
        )
        raise ValueError(
            f"Source not found in target info group with id {target_info_group_id}"
        )
    platform_type_key = f"PLATFORM_TYPE_{source.upper()}"
    platform_type = PlatformType.Value(platform_type_key)
    # step 3: get the workflow
    workflow = get_crm_list_sync_workflow(platform_type, target_info_group.playbook_id)
    # step 4: sync the list to the target info group
    try:
        workflow.sync_list_to_target_info_group(target_info_group_id)
    except Exception as e:
        logging.exception(f"Error syncing list to target info group: {e}")
        raise e


def get_crm_list_sync_workflow(
    source: PlatformType, playbook_id: int
) -> CRMListSyncWorkflow:
    crm_list_sync = None
    if source == PlatformType.PLATFORM_TYPE_HUBSPOT:
        crm_list_sync = CRMListSyncHubspot(playbook_id)
    else:
        raise ValueError(
            f"{source} for import list as target info group task is not supported yet"
        )
    return CRMListSyncWorkflow(playbook_id, crm_list_sync)
