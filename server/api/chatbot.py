import base64
import copy
import json
import logging
import os
import re
import threading
import time
import traceback
import uuid
from typing import List, Optional, Union

from django.core.cache import cache
from langchain.schema import AIMessage, HumanMessage, SystemMessage
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from server.celery import app as celery_app

from .content_gen.default_content_gen_postprocessor import (
    DefaultContentGenPostprocessor,
)
from .langsmith_integration import dynamic_traceable
from .model_caller import ModelCaller
from .model_config import ModelConfigResolver
from .models import AssetInfo, AssetInfoGroup, ChatHistory, Playbook
from .playground.chatbot_status import PlaygroundChatbotStatus, TaskState
from .playground.langgraph.langgraph_chat_generator import LangGraphAgentChatGenerator
from .playground.playground_chat_generator import (
    BasePlaygroundChatGenerator,
    GenerateChatRequest,
)
from .playground.playground_utils import crawl_urls, serialize_message_history
from .prompt.prompt_library.playground import (
    brand_guidelines_message,
    company_context_message,
    image_gen_system_message,
    system_init_message,
)
from .s3_utils import get_s3_image_base64
from .task_registry import GenerationGoal
from .utils import (
    convert_messages_to_prompt_for_claude,
    create_presigned_url,
    get_token_count,
)


def terminate_gen(task_id):
    celery_app.control.revoke(task_id, terminate=True)
    terminated_status = PlaygroundChatbotStatus(
        status=TaskState.TERMINATED,
        display_message="Task terminated",
        current_step=None,
        step_output=None,
        steps=[],
    )
    cache.set(
        task_id,
        terminated_status,
        timeout=3600 * 24,
    )
    return terminated_status


def is_image_file(filetype):
    return (
        filetype == "image/jpeg" or filetype == "image/png" or filetype == "image/jpg"
    )


class PlaygroundChatGenerator(BasePlaygroundChatGenerator):
    def __init__(self, user, chat_history, thread_id):
        super().__init__(user, chat_history, thread_id)

    @dynamic_traceable(name="playground_chat")
    def generate_chat_response(
        self,
        generate_chat_request: GenerateChatRequest,
    ):
        try:
            cache.set(
                generate_chat_request.task_id,
                PlaygroundChatbotStatus(
                    status=TaskState.RUNNING,
                    display_message="Thinking...",
                    current_step=None,
                    step_output=None,
                    steps=[],
                ),
                timeout=3600 * 24,
            )

            if generate_chat_request.image_gen:
                image_gen_model_config = ModelConfigResolver.resolve(
                    GenerationGoal.IMAGE_GENERATION
                )
                self.image_gen_model_caller = ModelCaller(image_gen_model_config)
                model_config = ModelConfigResolver.resolve(
                    GenerationGoal.CHATBOT,
                    foundation_model=generate_chat_request.model,
                )
            else:
                model_config = ModelConfigResolver.resolve(
                    GenerationGoal.CHATBOT, foundation_model=generate_chat_request.model
                )
            self.model_caller = ModelCaller(model_config)
            self.model_budget = model_config.model_budget
            asset_budget = 0
            target_budget = 0
            # if we have targets and assets, each should get 0.15, else give 0.3 to the one we have.
            if generate_chat_request.targets and generate_chat_request.assets:
                target_budget = int(0.15 * self.model_budget)
                asset_budget = int(0.15 * self.model_budget)
            elif generate_chat_request.targets:
                target_budget = int(0.3 * self.model_budget)
            elif generate_chat_request.assets:
                asset_budget = int(0.3 * self.model_budget)

            system_context = self.get_system_context()
            if generate_chat_request.assets:
                cache.set(
                    generate_chat_request.task_id,
                    PlaygroundChatbotStatus(
                        status=TaskState.RUNNING,
                        display_message="Reading Assets...",
                        current_step="get_assets_message",
                        step_output=f"Reading {len(generate_chat_request.assets)} assets",
                        steps=[],
                    ),
                    timeout=3600 * 24,
                )
                assets_message = self.get_assets_message(
                    generate_chat_request.assets,
                    generate_chat_request.new_message.content,
                    asset_budget=asset_budget,
                )
                generate_chat_request.previous_messages.append(
                    AIMessage(
                        content="What are the reference contents you want my writing to be base upon?",
                        additional_kwargs={"keep_hidden": True},
                    ),
                )
                generate_chat_request.previous_messages.append(assets_message)

            if generate_chat_request.targets:
                cache.set(
                    generate_chat_request.task_id,
                    PlaygroundChatbotStatus(
                        status=TaskState.RUNNING,
                        display_message="Reading Targets...",
                        current_step="get_targets_message",
                        step_output=f"Reading {len(generate_chat_request.targets)} targets",
                        steps=[],
                    ),
                    timeout=3600 * 24,
                )
                targets_message = self.get_targets_message(
                    generate_chat_request.targets, target_budget=target_budget
                )
                generate_chat_request.previous_messages.append(
                    AIMessage(
                        content="Tell me more about the target audience",
                        additional_kwargs={"keep_hidden": True},
                    ),
                )
                generate_chat_request.previous_messages.append(targets_message)

            url_message = get_url_message(
                generate_chat_request.new_message.content, generate_chat_request.task_id
            )
            if url_message:
                generate_chat_request.previous_messages.append(url_message)

            response_message = self.get_chat_response(
                generate_chat_request.model,
                generate_chat_request.previous_messages,
                generate_chat_request.new_message,
                system_context,
                generate_chat_request.task_id,
                use_company_info=generate_chat_request.use_company_info,
                use_brand_guidelines=generate_chat_request.use_brand_guidelines,
                image_generation=generate_chat_request.image_gen,
            )
            messages = generate_chat_request.previous_messages + [
                generate_chat_request.new_message,
                response_message,
            ]

            # attach uuid to the messages if they don't have it.
            for message in messages:
                if message.additional_kwargs is None:
                    message.additional_kwargs = {}
                if "id" not in message.additional_kwargs:
                    message.additional_kwargs["id"] = str(uuid.uuid4())
            # Save to db
            self.chat_history.json["previous_messages"] = serialize_message_history(
                messages
            )
            self.chat_history.model = generate_chat_request.model
            self.chat_history.save()
            image_urls = (
                response_message.additional_kwargs.get("image_urls", [])
                if response_message.additional_kwargs
                else []
            )
            image_url = image_urls[0] if image_urls else None
            text_content = response_message.content

            cache.set(
                generate_chat_request.task_id,
                PlaygroundChatbotStatus(
                    status=TaskState.COMPLETED,
                    message=text_content,
                    image_url=image_url,
                    current_step=None,
                    step_output=None,
                    steps=[],
                ),
                timeout=3600 * 24,
            )

            return response_message.content
        except Exception as e:
            logging.error(
                f"async chat failed: {e} for task {generate_chat_request.task_id}\n{traceback.format_exc()}"
            )
            cache.set(
                generate_chat_request.task_id,
                PlaygroundChatbotStatus(
                    status=TaskState.FAILED,
                    display_message=str(e),
                    current_step=None,
                    step_output=None,
                    steps=[],
                ),
                timeout=3600 * 24,
            )

    def generate_image_gen_prompt(self, messages):
        try:
            response = self.model_caller.get_results_with_fallback(messages)
            if not isinstance(response, list):
                raise Exception("Response is not a list")
            if not response:
                raise Exception("Response is empty")
            text_response = response[0].text
            if not text_response:
                raise Exception("Generated text is empty")
            return text_response
        except Exception as e:
            logging.error(
                f"Error generating image gen prompt: {e}\n{traceback.format_exc()}"
            )
            return "Generate an image based on our conversation."  # Fallback prompt

    def get_chat_response(
        self,
        model,
        previous_messages,
        new_message,
        system_context,
        task_id,
        use_company_info=True,
        use_brand_guidelines=True,
        image_generation=False,
    ):
        # Update cache status at the start
        cache.set(
            task_id,
            PlaygroundChatbotStatus(
                status=TaskState.RUNNING,
                display_message="Thinking...",
                current_step=None,
                step_output=None,
                steps=[],
            ),
            timeout=3600 * 24,
        )
        if image_generation:
            sys_msg = image_gen_system_message.format(**system_context)
        else:
            sys_msg = system_init_message.format(**system_context)
        sys_msg_full = sys_msg
        if use_company_info:
            sys_msg_full += company_context_message.format(**system_context)
        if use_brand_guidelines:
            sys_msg_full += brand_guidelines_message.format(**system_context)

        all_messages = [
            SystemMessage(content=sys_msg_full),
            *previous_messages,
        ]

        total_tokens = sum([get_token_count(m.content) for m in all_messages])

        while total_tokens > self.model_budget:
            # assumptions: Alternating human and ai messages.
            # trim the first two turns
            if len(all_messages) < 3:
                break
            previous_messages = previous_messages[2:]
            all_messages = [SystemMessage(content=sys_msg_full), *previous_messages]
            total_tokens = sum([get_token_count(m.content) for m in all_messages])

        # Add some buffer to the budget
        if total_tokens > self.model_budget * 0.9:
            logging.error(f"Chat message has reached limit: {new_message.content}")
            raise Exception(
                "The chat message is too long and has reached the limit. Please start a new thread."
            )

        messages = [
            SystemMessage(content=sys_msg_full),
            *previous_messages,
        ]

        messages = messages + [new_message]
        # for all models, convert images to base 64.
        invoke_messages = convert_image_urls_to_base64(messages)

        # convert messages for claude
        if "claude" in model:
            invoke_messages = convert_messages_to_prompt_for_claude(invoke_messages)
        if image_generation:
            cache.set(
                task_id,
                PlaygroundChatbotStatus(
                    status=TaskState.RUNNING,
                    display_message="Generating image prompt...",
                    current_step=None,
                    step_output=None,
                    steps=[],
                ),
                timeout=3600 * 24,
            )
            new_image_gen_message = HumanMessage(
                content="Generate a prompt for an image generation model given the above context. Be specific about the image you want to generate.",
                additional_kwargs={"keep_hidden": True},
            )
            invoke_messages = invoke_messages + [new_image_gen_message]
            # logging.info(f"invoke_messages: {invoke_messages}")
            limit = 2
            prev_images = get_prev_images(invoke_messages, limit)
            logging.info(f"prev_images length: {len(prev_images)}")
            image_gen_prompt = self.generate_image_gen_prompt(invoke_messages)
            if not image_gen_prompt:
                raise Exception("Image gen prompt is empty")

            cache.set(
                task_id,
                PlaygroundChatbotStatus(
                    status=TaskState.RUNNING,
                    display_message="Generating image...",
                    current_step=None,
                    step_output=None,
                    steps=[],
                ),
                timeout=3600 * 24,
            )
            response_img_url = (
                self.image_gen_model_caller.get_image_generation_with_fallback(
                    image_gen_prompt,
                    playbook=self.gen_env._data_wrapper.playbook_instance,
                    prev_images=prev_images,
                )
            )
            return AIMessage(
                content="",
                additional_kwargs={"image_urls": [response_img_url]},
            )
        else:
            response = self.model_caller.get_results_with_fallback(invoke_messages)
            if not isinstance(response, list):
                raise Exception("Response is not a list")
            if not response:
                raise Exception("Response is empty")
            text_response = response[0].text
            if not text_response:
                raise Exception("Generated text is empty")

        processed_response = postprocess_response(text_response)

        MIN_WORDS_FOR_REWRITING = 50  # Move to configuration
        if (
            use_brand_guidelines
            and len(processed_response.split()) > MIN_WORDS_FOR_REWRITING
        ):
            cache.set(
                task_id,
                PlaygroundChatbotStatus(
                    status=TaskState.RUNNING,
                    display_message="Checking Style Guidelines...",
                    current_step="brand_guideline_rewriting",
                    step_output="rewritten response",
                    steps=[],
                ),
                timeout=3600 * 24,
            )
            # do brand guideline rewriting only if the response is longer than 50 words.
            processed_response = self.brand_guideline_rewriting(
                processed_response, system_context["brand_guidelines"]
            )

        return AIMessage(content=processed_response)

    def brand_guideline_rewriting(self, response: str, brand_guidelines: str) -> str:
        default_content_gen_postprocessor = DefaultContentGenPostprocessor(
            gen_env=self.gen_env, model_caller=self.model_caller
        )
        return default_content_gen_postprocessor.rewrite_with_brand_guideline(
            [response], brand_guidelines
        )[0]


def convert_image_urls_to_base64(
    messages: List[Union[AIMessage, HumanMessage, SystemMessage]], max_images: int = 5
) -> List[Union[AIMessage, HumanMessage, SystemMessage]]:
    """
    Convert image URLs in messages to base64 encoded data.

    Args:
        messages: List of message objects (AIMessage, HumanMessage, or SystemMessage)
        max_images: Maximum number of images to convert (default: 5)

    Returns:
        List of processed messages with base64 encoded images

    Raises:
        ValueError: If messages is not a list or max_images is not a positive integer

    Note:
        - Images are processed in reverse order to prioritize recent images
        - Each image is limited to 10MB after base64 encoding
        - Failed image conversions are logged but don't stop processing
    """
    if not isinstance(messages, list):
        raise ValueError("messages must be a list")
    if not isinstance(max_images, int) or max_images <= 0:
        raise ValueError("max_images must be a positive integer")

    image_messages = []
    converted_images = 0

    def _convert_image_url(image_url, max_size_bytes=10 * 1024 * 1024):  # 10MB limit
        try:
            base64_data = get_base64_image(image_url)
            if len(base64_data) > max_size_bytes:
                logging.warning(
                    "Image %s exceeds size limit of %d bytes", image_url, max_size_bytes
                )
                return None
            return base64_data
        except Exception as e:
            logging.error("Failed to fetch image %s: %s", image_url, e)
            return None

    def _process_human_message(message):
        nonlocal converted_images
        if not (
            message.additional_kwargs and message.additional_kwargs.get("image_urls")
        ):
            return message

        new_message = copy.deepcopy(message)
        content = message.content
        if isinstance(content, (dict, list)):
            new_message.content = [{"type": "text", "text": json.dumps(content)}]
        else:
            new_message.content = [{"type": "text", "text": str(content)}]

        for image_url in message.additional_kwargs["image_urls"]:
            if converted_images >= max_images:
                break

            image_base64 = _convert_image_url(image_url)
            if image_base64:
                new_message.content.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}",
                            "detail": "auto",
                        },
                    }
                )
                converted_images += 1
        return new_message

    def _process_ai_message(message):
        nonlocal converted_images
        if not (
            message.additional_kwargs
            and message.additional_kwargs.get("image_urls")
            and converted_images < max_images
        ):
            return [message]

        # Create a new human message with the images
        new_human_message = HumanMessage(
            content=[{"type": "text", "text": "Here is the image."}]
        )

        for image_url in message.additional_kwargs["image_urls"]:
            if converted_images >= max_images:
                break

            image_base64 = _convert_image_url(image_url)
            if image_base64:
                new_human_message.content.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                    }
                )
                converted_images += 1

        # Return both the human message with images and the AI message with text
        new_ai_message = copy.deepcopy(message)
        new_ai_message.content = message.content  # keep original kwargs & ids
        return [new_human_message, new_ai_message]

    # we want to do this backwards, so we only convert the last max_images images.
    for message in reversed(messages):
        if isinstance(message, SystemMessage):
            image_messages.insert(0, message)
        elif isinstance(message, HumanMessage):
            processed_message = _process_human_message(message)
            image_messages.insert(0, processed_message)
        else:
            processed_messages = _process_ai_message(message)
            for msg in reversed(processed_messages):
                image_messages.insert(0, msg)

    logging.info(f"converted_images: {converted_images}")
    return image_messages


def get_prev_images(messages, limit=2):
    # step backwards through the messages, getting the first image_url from message.content
    prev_images = []
    for message in reversed(messages):
        if message.content and isinstance(message.content, list):
            for content in message.content:
                if content.get("type") == "image_url":
                    image_str_data = content.get("image_url").get("url")
                    # Parse the data URL to get MIME type and base64 data
                    if image_str_data.startswith("data:"):
                        # Split 'data:image/jpeg;base64,ABC123' into parts
                        mime_type = image_str_data.split(";")[0].split(":")[1]
                        base64_data = image_str_data.split(",")[1]
                        image_bytes = base64.b64decode(base64_data)
                        # Return as tuple (filename, bytes, mime_type) which OpenAI's API accepts
                        filename = str(uuid.uuid4())
                        prev_images.append((filename, image_bytes, mime_type))
                        if len(prev_images) >= limit:
                            return prev_images
    return prev_images


def get_base64_image(image_url):
    image_bucket = None
    # First check if the image url is a valid s3 tofu image url.
    if "tofu-generated-images.s3" in image_url:
        image_bucket = "tofu-generated-images"
    elif "tofu-uploaded-files.s3" in image_url:
        image_bucket = "tofu-uploaded-files"
    else:
        logging.error(f"Invalid image url: {image_url}")
        return None
    base64_image = get_s3_image_base64(image_bucket, image_url)
    return base64_image


def get_file_assets(s3_files, playbook, thread_id):
    # files is a list of s3 urls.
    asset_group_key = "[TOFU Internal] Playground Files"
    file_assets = {asset_group_key: []}
    # first check if we have the files in cache.
    for s3_file in s3_files:
        # Validate s3_file structure
        if not isinstance(s3_file, dict):
            logging.warning(f"Invalid s3_file format: {s3_file}")
            continue

        required_fields = [
            "mime_file_type",
            "s3_presigned_path",
            "s3_bucket",
            "s3_filename",
        ]
        if not all(s3_file.get(field) for field in required_fields):
            logging.warning(f"Missing required fields in s3_file: {s3_file}")
            continue
        # ignore the files that are images.
        if is_image_file(s3_file.get("mime_file_type")):
            continue
        try:
            asset_info = create_asset_info_from_file(
                s3_file,
                playbook=playbook,
                asset_group_key=asset_group_key,
                thread_id=thread_id,
            )
            file_assets[asset_group_key].append(asset_info.asset_key)

        except Exception as e:
            logging.exception(f"Error creating asset info from file: {e}")
            continue
    if file_assets.get(asset_group_key):
        return file_assets
    return None


def create_asset_info_from_file(
    s3_file,
    playbook,
    asset_group_key="[TOFU Internal] Playground Files",
    thread_id=None,
):
    """
    Create an AssetInfo object from an S3 file.

    Args:
        s3_file: Dictionary containing S3 file information
        playbook: Playbook object to associate the asset with
        asset_group_key: Key for the asset group
        thread_id: Thread ID to associate the asset with

    Returns:
        AssetInfo object

    Raises:
        ValueError: If required fields are missing
    """
    # Validate required fields
    required_fields = [
        "s3_bucket",
        "s3_filename",
        "mime_file_type",
        "original_filename",
        "s3_presigned_path",
    ]
    missing_fields = [field for field in required_fields if not s3_file.get(field)]
    if missing_fields:
        raise ValueError(f"Missing required fields: {missing_fields}")

    s3_bucket = s3_file.get("s3_bucket")
    s3_filename = s3_file.get("s3_filename")
    mime_file_type = s3_file.get("mime_file_type")
    original_filename = s3_file.get("original_filename")
    s3_presigned_path = s3_file.get("s3_presigned_path")

    asset_key = str(uuid.uuid4())
    asset_value = {
        "s3_bucket": s3_bucket,
        "s3_filename": s3_filename,
        "mime_file_type": mime_file_type,
        "original_filename": original_filename,
        "s3_presigned_path": s3_presigned_path,
    }
    docs = {
        asset_key: {
            "id": asset_key,
            "type": "file",
            "value": asset_value,
            "position": 0,
        }
    }
    try:
        asset_info_group = AssetInfoGroup.objects.filter(
            asset_info_group_key=asset_group_key,
            playbook=playbook,
        ).first()
        if not asset_info_group:
            asset_info_group = AssetInfoGroup.objects.create(
                asset_info_group_key=asset_group_key,
                playbook=playbook,
                meta={"position": 0},
            )
        meta = {"position": 0}
        if thread_id:
            meta["thread_id"] = thread_id
        asset_info = AssetInfo.objects.create(
            asset_info_group=asset_info_group,
            asset_key=asset_key,
            docs=docs,
            meta=meta,
        )
        return asset_info
    except Exception as e:
        logging.exception(f"Error creating asset info from file: {e}")
        return None


def async_chat(
    task_id,
    model,
    user,
    previous_messages,
    new_message,
    chat_history,
    use_company_info,
    use_brand_guidelines,
    targets=None,
    assets=None,
    is_deepresearch=False,
    thread_id=None,
    s3_files=None,
    image_gen=False,
):

    # get assets from files.
    if s3_files:
        playbook = Playbook.objects.filter(users=user).first()
        if not playbook:
            raise Exception("No Playbook associated with the user.")
        file_assets = get_file_assets(s3_files, playbook, thread_id)
        # add the key value pair of file_assets to the assets dictionary.
        if file_assets:
            if assets is None:
                assets = file_assets
            else:
                assets = {**assets, **file_assets}
            if not new_message.additional_kwargs:
                new_message.additional_kwargs = {}
            new_message.additional_kwargs["thread_asset_ids"] = get_asset_ids(
                file_assets
            )
    generate_chat_request = GenerateChatRequest(
        task_id=task_id,
        model=model,
        previous_messages=previous_messages,
        new_message=new_message,
        use_company_info=use_company_info,
        use_brand_guidelines=use_brand_guidelines,
        targets=targets,
        assets=assets,
        image_gen=image_gen,
    )
    if is_deepresearch:
        generator = LangGraphAgentChatGenerator(
            user=user,
            chat_history=chat_history,
            thread_id=thread_id,
            chat_request=generate_chat_request,
        )
    else:
        generator = PlaygroundChatGenerator(
            user=user, chat_history=chat_history, thread_id=thread_id
        )

    return generator.generate_chat_response(generate_chat_request)


def playground_chat(
    user,
    model,
    new_message_text,
    key,
    use_company_info=True,
    use_brand_guidelines=True,
    regenerate=False,
    targets=None,
    assets=None,
    is_deepresearch=False,
    s3_files=None,
    image_gen=False,
):
    # Fetch previous messages from the database
    chat_history = ChatHistory.objects.filter(creator=user)

    if chat_history:
        chat_history = chat_history.filter(key=key).first()
    else:
        raise Exception("Chat history not found.")

    previous_messages = deserialize_chat_history(
        chat_history.json.get("previous_messages", [])
    )
    if regenerate:
        # Remove the last two turns of the conversation.
        if len(previous_messages) >= 2:
            previous_messages = previous_messages[:-2]

    task_uuid = uuid.uuid4()  # Generate a unique task ID
    task_id = f"playground_chat_task_{task_uuid}"

    cache.set(
        task_id,
        PlaygroundChatbotStatus(
            status=TaskState.SUBMITTED,
            display_message=None,
            current_step=None,
            step_output=None,
            steps=[],
        ),
        timeout=3600 * 24,
    )
    additional_kwargs = {}
    if s3_files:
        additional_kwargs["s3_files"] = s3_files
        # check if any of the files are images, if so, add the image_url to the additional_kwargs.
        image_urls = get_image_urls_from_s3_files(s3_files)
        if image_urls:
            additional_kwargs["image_urls"] = image_urls

    new_message = HumanMessage(
        content=new_message_text,
        additional_kwargs=additional_kwargs,
    )

    job_thread = threading.Thread(
        target=async_chat,
        args=(
            task_id,
            model,
            user,
            previous_messages,
            new_message,
            chat_history,
            use_company_info,
            use_brand_guidelines,
            targets,
            assets,
            is_deepresearch,
            key,
            s3_files,
            image_gen,
        ),
    )
    job_thread.start()
    return task_id


def poll_playground_chat_response(task_id):
    try:
        response = cache.get(task_id)
    except Exception as e:
        logging.exception(
            f"debug: Failed to get playground chat response for {task_id}: {e}"
        )
        response = None
    if not response:
        raise Exception(f"Playground chat task {task_id} not found")
    return response


def reset_playground_chat(user, model):
    # clean up old empty chat histories.
    # ChatHistory.objects.filter(creator=user, json__previous_messages=[]).delete()

    key = str(uuid.uuid4())
    chat_history = ChatHistory.objects.create(
        creator=user, key=key, model=model, json={"previous_messages": []}
    )
    return chat_history


def extract_result_from_claude(input_text):
    # Extract all text within <result> tags
    result_texts = re.findall(r"<result>(.*?)</result>", input_text, re.DOTALL)

    # Get the last result text if there are multiple
    last_result_text = result_texts[-1] if result_texts else ""

    # Remove all tagged content to get the free form text
    free_form_text = re.sub(r"<[^>]+>.*?</[^>]+>", "", input_text, flags=re.DOTALL)

    # Remove any remaining tags from the free form text
    free_form_text = re.sub(r"<[^>]+>", "", free_form_text).strip()

    # Combine the last result text and free form text
    combined_text = last_result_text + ("\n" + free_form_text if free_form_text else "")

    return combined_text.strip()


def postprocess_response(response: str) -> str:
    # Replace circle dot with regular dash.
    return response.replace("•", "-")


def deserialize_chat_history(messages):
    return [convert_dict_to_message(m) for m in messages]


def convert_dict_to_message(message_dict):
    role = message_dict.get("role")
    name = message_dict.get("name")
    id_ = message_dict.get("id")
    content = message_dict.get("content")
    # get any other kwargs
    kwargs = {
        k: v
        for k, v in message_dict.items()
        if k not in ["role", "name", "id", "content"]
    }
    if role == "user":
        return HumanMessage(
            content=content, id=id_, name=name, additional_kwargs=kwargs
        )
    elif role == "assistant":
        return AIMessage(content=content, id=id_, name=name, additional_kwargs=kwargs)
    elif role == "system":
        return SystemMessage(
            content=content, id=id_, name=name, additional_kwargs=kwargs
        )
    else:
        raise Exception(f"Unknown role: {role}")


def get_url_message(message_text, task_id):
    # extract all urls from the message text
    # Then crawl and return the content of the urls.

    urls = get_urls_from_text(message_text)
    if not urls:
        return None

    # crawl the urls
    cache.set(
        task_id,
        PlaygroundChatbotStatus(
            status=TaskState.RUNNING,
            display_message="Reading URLs...",
            current_step="get_url_message",
            step_output=f"Reading url {urls}",
            steps=[],
        ),
        timeout=3600 * 24,
    )

    url_contents = crawl_urls(urls)

    # return the contents of the urls wrapped as <urlContent url="{url}">{content}</urlContent>
    if not url_contents:
        return None

    url_contents_str = ""
    for url, content in url_contents.items():
        url_contents_str += f'<urlContent url="{url}">\n{content}\n</urlContent>'
    return HumanMessage(
        content=url_contents_str, additional_kwargs={"keep_hidden": True}
    )


def get_urls_from_text(message_text):
    # First remove all content between **@ and **
    message_text = re.sub(r"\*\*@.*?\*\*", "", message_text)

    # Extract all URLs from the message text, including bare domains with common TLDs
    url_pattern = re.compile(
        r"(?:https?:\/\/)?"  # Optional http:// or https://
        r"(?:www\.)?"  # Optional www.
        r"[A-Za-z0-9-]+"  # Domain name part (before the first dot)
        r"(?:\.[A-Za-z0-9-]+)*"  # Optional subdomains, e.g. .co, .co.uk, etc.
        r"\.(?:com|org|net|edu|gov|io|co|ai|app|dev)"  # Restrict TLD
        r"(?:\/[^\s]*)*"  # Optional path/query string
    )
    urls = url_pattern.findall(message_text)

    # Strip leading and trailing punctuation from each URL
    stripped_urls = [url.strip(".,!?;:") for url in urls]

    return stripped_urls


def get_image_urls_from_s3_files(s3_files):
    """
    Extract image URLs from S3 files.

    Args:
        s3_files: List of dictionaries containing S3 file information

    Returns:
        List of presigned URLs for image files
    """
    image_urls = []
    if not s3_files:
        return image_urls

    for s3_file in s3_files:
        if not isinstance(s3_file, dict):
            logging.warning(f"Invalid s3_file format: {s3_file}")
            continue

        if not all(
            key in s3_file for key in ["mime_file_type", "s3_bucket", "s3_filename"]
        ):
            logging.warning(f"Missing required fields in s3_file: {s3_file}")
            continue

        content_type = s3_file.get("mime_file_type")
        if is_image_file(content_type):
            try:
                image_urls.append(
                    create_presigned_url(
                        s3_file.get("s3_bucket"),
                        s3_file.get("s3_filename"),
                    )
                )
            except Exception as e:
                logging.error(f"Error creating presigned URL: {e}")
                continue

    return image_urls


def get_asset_ids(file_assets):
    asset_ids = []
    asset_info_group_keys = list(file_assets.keys())
    for asset_info_group_key in asset_info_group_keys:
        asset_info_group = AssetInfoGroup.objects.filter(
            asset_info_group_key=asset_info_group_key
        ).first()
        if asset_info_group:
            asset_info_keys = file_assets.get(asset_info_group_key)
            # assert is list.
            assert isinstance(asset_info_keys, list)
            for asset_info_key in asset_info_keys:
                asset_info = AssetInfo.objects.filter(
                    asset_info_group=asset_info_group, asset_key=asset_info_key
                ).first()
                if asset_info:
                    asset_ids.append(asset_info.id)
    return asset_ids
