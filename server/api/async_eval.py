# async tasks using celery
# restart the celery worker if there's any code change to the tasks

import logging

from api.eval import Benchmark
from server.celery import app

from .models import TofuUser
from .thread_locals import set_current_user


@app.task(bind=True, acks_late=True)
def async_eval_gen_review(
    self,
    user_id,
    prefix,
    campaign_ids,
    content_ids,
    model_name,
    num_of_variations,
    enable_custom,
    generate_all_targets,
    joint_generation=True,
    template_generation=False,
    test_only=False,
):
    try:
        logging.info(
            f"Running async eval gen review for campaigns {campaign_ids} with {model_name}"
        )

        user = TofuUser.objects.filter(id=user_id)
        set_current_user(user[0] if user else None)

        benchmark = Benchmark.get_benchmark_instance(test_only=test_only)
        result_data = benchmark.gen_review(
            prefix,
            campaign_ids,
            content_ids,
            model_name,
            num_of_variations,
            enable_custom,
            generate_all_targets,
            joint_generation=joint_generation,
            template_generation=template_generation,
        )
        print(f"Saved results to {result_data}")
    except Exception as e:
        logging.error(f"Fail to run eval gen review: {e}")
        raise e


@app.task(bind=True, acks_late=True)
def async_eval_gen_review_repurpose(
    self,
    user_id,
    prefix,
    campaign_ids,
    content_ids,
    model_name,
    num_of_variations,
    enable_custom,
    generate_all_targets,
    joint_generation=True,
    test_only=False,
):
    try:
        logging.info(
            f"Running async eval gen review repurpose for campaigns {campaign_ids} with {model_name}"
        )

        user = TofuUser.objects.filter(id=user_id)
        set_current_user(user[0] if user else None)

        benchmark = Benchmark.get_benchmark_instance(test_only=test_only)
        result_data = benchmark.gen_review_repurpose(
            prefix,
            campaign_ids,
            content_ids,
            model_name,
            num_of_variations,
            enable_custom,
            generate_all_targets,
            joint_generation=joint_generation,
        )
        print(f"Saved results to {result_data}")
    except Exception as e:
        logging.error(f"Fail to run eval gen review repurpose: {e}")
        raise e


@app.task(bind=True, acks_late=True)
def async_eval_gen_review_template(
    self,
    user_id,
    prefix,
    campaign_ids,
    content_ids,
    model_name,
    num_of_variations,
    enable_custom,
    generate_all_targets,
    test_only=False,
):
    try:
        logging.info(
            f"Running async eval gen template for campaigns {campaign_ids} with {model_name}"
        )

        user = TofuUser.objects.filter(id=user_id)
        set_current_user(user[0] if user else None)

        benchmark = Benchmark.get_benchmark_instance(test_only=test_only)
        result_data = benchmark.gen_review(
            prefix,
            campaign_ids,
            content_ids,
            model_name,
            num_of_variations,
            enable_custom,
            generate_all_targets,
            joint_generation=True,
            template_generation=True,
        )
        print(f"Saved results to {result_data}")
    except Exception as e:
        logging.error(f"Fail to run eval gen template: {e}")
        raise e
