import logging


class ObjectInfoWrapper:
    def __init__(self, object) -> None:
        self.object = object

    def get_summary(self):
        if self.object.summary:
            return self.object.summary
        else:
            if "brief" not in self.object.meta:
                logging.error(
                    f"brief not in meta: {self.object.meta} for {self.object.__class__.__name__} {self.object.id}"
                )
            brief_summary = self.object.meta["brief"]
            return brief_summary

    def is_built(self):
        # TODO perhaps leverage the status
        return self.object.docs == self.object.docs_last_build


class TargetInfoWrapper(ObjectInfoWrapper):
    pass
